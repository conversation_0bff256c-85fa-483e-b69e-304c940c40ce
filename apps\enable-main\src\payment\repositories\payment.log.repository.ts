import { Injectable } from '@nestjs/common';
import { GenericRepository } from '@app/shared-stuff';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { PaymentLog, PaymentLogDocument } from '../models/payment.log.model';
import { PaymentLogRepositoryInterface } from './interfaces/payment.log.repository.interface';

@Injectable()
export class PaymentLogRepository
  extends GenericRepository<PaymentLogDocument, PaymentLog>
  implements PaymentLogRepositoryInterface
{
  constructor(
    @InjectModel(PaymentLog.name)
    private paymentLogModel: Model<PaymentLogDocument, PaymentLog>,
  ) {
    super(paymentLogModel);
  }
}
