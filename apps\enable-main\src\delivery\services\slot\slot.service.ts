import { OrderDeliveryType, OrderDocument } from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { isValidObjectId, Model, Types } from 'mongoose';
import { HelperService } from '../../../shared/services/helper/helper.service';
import { SlotToCreate, SlotToIndex, SlotToUpdate } from '../../dto/slot.dto';
import { SlotDocument } from '../../models/slot.model';

@Injectable()
export class SlotService {
  days = [
    'Saturday',
    'Sunday',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
  ];
  constructor(
    @InjectModel('Slot') private SlotModel: Model<SlotDocument>,
    private helperService: HelperService,
  ) {}

  async index(slotIndex: SlotToIndex) {
    let slots = this.SlotModel.find();

    if ((slotIndex.offset || slotIndex.offset == 0) && slotIndex.limit) {
      slots = slots
        .skip(slotIndex.offset * slotIndex.limit)
        .limit(slotIndex.limit);
    }
    if (slotIndex.owner && slotIndex.owner != 'all') {
      slots = slots.find({ owner: slotIndex.owner });
    }
    if (slotIndex.company && slotIndex.owner == 'company') {
      slots = slots.find({ company: new Types.ObjectId(slotIndex.company) });
    }
    slots = slots.find({ deletedAt: { $eq: null } });
    let slotDate = slotIndex.day || moment();
    slotDate = slotDate == 'undefined' ? moment() : slotDate;
    const slotDay = moment(slotDate).format('dddd');
    const slotDateString = moment(slotDate)
      .format('YYYY-MM-DD')
      .toString() as any;
    const slotsFinal = await slots.lean().exec();
    for (let i = 0; i < slotsFinal.length; i++) {
      const currentSlot = slotsFinal[i];
      if (!currentSlot['orders_per_day']) {
        currentSlot['orders_per_day'] = {};
      }
      if (!currentSlot['urgent_orders_per_day']) {
        currentSlot['urgent_orders_per_day'] = {};
      }
      if (
        (!currentSlot.orders_per_day[slotDateString] ||
          currentSlot.orders_per_day[slotDateString] <
            currentSlot.maximum_number_of_orders) &&
        slotIndex.delivery_type != OrderDeliveryType.urgent
      ) {
        currentSlot['available'] = true;
      } else if (
        (!currentSlot.urgent_orders_per_day[slotDateString] ||
          currentSlot.urgent_orders_per_day[slotDateString] <
            currentSlot.maximum_number_of_urgent_orders) &&
        slotIndex.delivery_type == OrderDeliveryType.urgent
      ) {
        currentSlot['available'] = true;
      } else {
        currentSlot['available'] = true;
      }

      if (currentSlot.days && !currentSlot.days.includes(slotDay)) {
        currentSlot['available'] = false;
      }
    }

    return slotsFinal;
  }

  async create(slotToCreate: SlotToCreate) {
    slotToCreate['company'] = new Types.ObjectId(
      slotToCreate['company'],
    ) as any;
    const createdSlot = new this.SlotModel(slotToCreate);
    createdSlot.orders_per_day = {};
    createdSlot.urgent_orders_per_day = {};
    createdSlot.createdBy = slotToCreate.current_user;
    await createdSlot.save();
  }

  async update(slotToUpdate: SlotToUpdate) {
    slotToUpdate['company'] = new Types.ObjectId(
      slotToUpdate['company'],
    ) as any;
    const updatedSlot = await this.SlotModel.findOneAndUpdate(
      { _id: slotToUpdate._id },
      slotToUpdate as any,
    );
    updatedSlot.updatedBy = slotToUpdate.current_user;
    await updatedSlot.save();
    return updatedSlot;
  }

  async remove(id: string, current: any) {
    const removedSlot = await this.SlotModel.findOne({ _id: id });
    removedSlot.deletedAt = moment().utc().toDate();
    removedSlot.deletedBy = current;
    await removedSlot.save();
    return removedSlot;
  }

  async get_details(id: string) {
    const filterOBJ = { $or: [] } as any;
    if (isValidObjectId(id)) {
      filterOBJ.$or.push({ _id: new Types.ObjectId(id) });
    }
    const selectedSlot = await this.SlotModel.findOne(filterOBJ);
    return selectedSlot;
  }

  async reserveSlotToOrder(order: OrderDocument, delivery_date, slot_id) {
    const slot = await this.SlotModel.findOne({ _id: slot_id });
    const date = moment(delivery_date).format('YYYY-MM-DD').toString();
    if (!slot['orders_per_day']) {
      slot.orders_per_day = {};
    }
    if (!slot.urgent_orders_per_day) {
      slot.urgent_orders_per_day = {};
    }
    if (!slot['orders_per_day'][date]) {
      slot['orders_per_day'][date] = 0;
    }
    if (!slot['urgent_orders_per_day'][date]) {
      slot['urgent_orders_per_day'][date] = 0;
    }
    if (order.delivery_type == OrderDeliveryType.urgent) {
      slot['urgent_orders_per_day'][date] =
        slot['urgent_orders_per_day'][date] + 1;
    } else {
      slot['orders_per_day'][date] = slot['orders_per_day'][date] + 1;
    }
    order['delivery_slot_from'] = slot.from;
    order['delivery_slot_to'] = slot.to;
    order.delivery_slot = slot._id;
    order.delivery_date = moment
      .utc(date + ' ' + slot.to, 'YYYY-MM-DD HH:mm')
      .toDate() as any;
    await order.save();
    await slot.save();
  }
}
