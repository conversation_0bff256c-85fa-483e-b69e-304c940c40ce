// [EBL-5680] Using External Storage provider instead of saving static files on the machine
// Check the coverage of image migration, ideally all these numbers should be 0

console.log({
  orderItemImages: db.orderitems.countDocuments({
    'images.0': { $exists: true },
    'images.url': null,
  }),
  passConfigBufferPassTemplateLogo: db.pass_configs.countDocuments({
    'passTemplate.logo.name': { $exists: true },
    'passTemplate.logo.url': null,
  }),
  passConfigBufferPassTemplateIcon: db.pass_configs.countDocuments({
    'passTemplate.icon.name': { $exists: true },
    'passTemplate.icon.url': null,
  }),
  passConfigStampsConfigEmptyStamp: db.pass_configs.countDocuments({
    'stripImageConfig.stampsConfig.emptyStamp.name': { $exists: true },
    'stripImageConfig.stampsConfig.emptyStamp.url': null,
  }),
  passConfigStampsConfigFilledStamp: db.pass_configs.countDocuments({
    'stripImageConfig.stampsConfig.filledStamp.name': { $exists: true },
    'stripImageConfig.stampsConfig.filledStamp.url': null,
  }),
  passConfigStaticStrip: db.pass_configs.countDocuments({
    'stripImageConfig.staticStripImage.name': { $exists: true },
    'stripImageConfig.staticStripImage.url': null,
  }),
  passConfigBackgroundImage: db.pass_configs.countDocuments({
    'stripImageConfig.backgroundImage.name': { $exists: true },
    'stripImageConfig.backgroundImage.url': null,
  }),
  branchImage: db.branches.countDocuments({
    'image.name': { $exists: true },
    'image.url': null,
  }),
  brandImage: db.brands.countDocuments({
    'image.name': { $exists: true },
    'image.url': null,
  }),
  brandPassPreviewImage: db.brands.countDocuments({
    'passPreviewImage.name': { $exists: true },
    'passPreviewImage.url': null,
  }),
  companyImage: db.companies.countDocuments({
    'image.name': { $exists: true },
    'image.url': null,
  }),
  tierBarDisplay: db.loyaltytiers.countDocuments({
    'barDisplay.name': { $exists: true },
    'barDisplay.url': null,
  }),
  tierBackgroundImage: db.loyaltytiers.countDocuments({
    'stripBackgroundImage.name': { $exists: true },
    'stripBackgroundImage.url': null,
  }),
  tierBorderDisplayEmptyStamp: db.loyaltytiers.countDocuments({
    'borderDisplay.emptyStamp.name': { $exists: true },
    'borderDisplay.emptyStamp.url': null,
  }),
  tierBorderDisplayFilledStamp: db.loyaltytiers.countDocuments({
    'borderDisplay.filledStamp.name': { $exists: true },
    'borderDisplay.filledStamp.url': null,
  }),
  tierBorderDisplayImage: db.loyaltytiers.countDocuments({
    'borderDisplay.image.name': { $exists: true },
    'borderDisplay.image.url': null,
  }),
  menuCategoryImage: db.menucategories.countDocuments({
    'image.name': { $exists: true },
    'image.url': null,
  }),
  menuItemImages: db.menucategories.countDocuments({
    'images.name': { $exists: true },
    'images.url': null,
  }),
  menuItemImages: db.menucategories.countDocuments({
    'images.name': { $exists: true },
    'images.url': null,
  }),
  punchCardEmptyStamp: db.punchcards.countDocuments({
    'emptyStamp.name': { $exists: true },
    'emptyStamp.url': null,
  }),
  punchCardFilledStamp: db.punchcards.countDocuments({
    'filledStamp.name': { $exists: true },
    'filledStamp.url': null,
  }),
  punchCardAchievementsStampOnEarnEmptyStamp: db.punchcards.countDocuments({
    'achievementStampOnEarn.emptyStamp.name': { $exists: true },
    'achievementStampOnEarn.emptyStamp.url': null,
  }),
  punchCardAchievementsStampOnEarnfilledStamp: db.punchcards.countDocuments({
    'achievementStampOnEarn.filledStamp.name': { $exists: true },
    'achievementStampOnEarn.filledStamp.url': null,
  }),
  punchCardAchievementsStampOnEarnimage: db.punchcards.countDocuments({
    'achievementStampOnEarn.image.name': { $exists: true },
    'achievementStampOnEarn.image.url': null,
  }),
  punchCardAchievementsStampOnRedeemEmptyStamp: db.punchcards.countDocuments({
    'achievementStampOnRedeem.emptyStamp.name': { $exists: true },
    'achievementStampOnRedeem.emptyStamp.url': null,
  }),
  punchCardAchievementsStampOnRedeemfilledStamp: db.punchcards.countDocuments({
    'achievementStampOnRedeem.filledStamp.name': { $exists: true },
    'achievementStampOnRedeem.filledStamp.url': null,
  }),
  punchCardAchievementsStampOnRedeemimage: db.punchcards.countDocuments({
    'achievementStampOnRedeem.image.name': { $exists: true },
    'achievementStampOnRedeem.image.url': null,
  }),
  punchCardAchievementStampOnEarnImage: db.punchcards.countDocuments({
    'achievements.stampOnEarn.image.name': { $exists: true },
    'achievements.stampOnEarn.image.url': null,
  }),
  punchCardAchievementStampOnEarnemptyStamp: db.punchcards.countDocuments({
    'achievements.stampOnEarn.emptyStamp.name': { $exists: true },
    'achievements.stampOnEarn.emptyStamp.url': null,
  }),
  punchCardAchievementStampOnEarnfilledStamp: db.punchcards.countDocuments({
    'achievements.stampOnEarn.filledStamp.name': { $exists: true },
    'achievements.stampOnEarn.filledStamp.url': null,
  }),
  punchCardAchievementStampOnRedeemImage: db.punchcards.countDocuments({
    'achievements.stampOnRedeem.image.name': { $exists: true },
    'achievements.stampOnRedeem.image.url': null,
  }),
  punchCardAchievementStampOnRedeememptyStamp: db.punchcards.countDocuments({
    'achievements.stampOnRedeem.emptyStamp.name': { $exists: true },
    'achievements.stampOnRedeem.emptyStamp.url': null,
  }),
  punchCardAchievementStampOnRedeemfilledStamp: db.punchcards.countDocuments({
    'achievements.stampOnRedeem.filledStamp.name': { $exists: true },
    'achievements.stampOnRedeem.filledStamp.url': null,
  }),
  punchCardAchievementCounterMenuItem: db.punchcards.countDocuments({
    'achievements.requirement.menuItem.images.name': { $exists: true },
    'achievements.requirement.menuItem.images.url': null,
  }),
  punchCardAchievementRewardMenuItem: db.punchcards.countDocuments({
    'achievements.reward.menuItem.images.name': { $exists: true },
    'achievements.reward.menuItem.images.url': null,
  }),
});
