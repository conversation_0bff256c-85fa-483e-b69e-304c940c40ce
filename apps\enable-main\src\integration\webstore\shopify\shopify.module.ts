import {
  CollectionName,
  CustomCacheModule,
  ShopifyRequestSchema,
} from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { CouponModule } from '../../../coupon/coupon.module';
import { CustomerReadModule } from '../../../customer/modules/customer-read/customer-read.module';
import { CustomerWriteModule } from '../../../customer/modules/customer-write/customer-write.module';
import { RestaurantModule } from '../../../restaurant/restaurant.module';
import { StoreModule } from '../../../store/store.module';
import { ShopifyRequestRepository } from './repositories/shopify-request.repository';
import { ShopifyService } from './services/shopify.service';

@Module({
  imports: [
    RestaurantModule,
    CustomerReadModule,
    CustomerWriteModule,
    StoreModule,
    CouponModule,
    CustomCacheModule,
    MongooseModule.forFeature([
      { name: CollectionName.SHOPIFY_REQUEST, schema: ShopifyRequestSchema },
    ]),
  ],
  controllers: [],
  providers: [ShopifyService, ShopifyRequestRepository],
  exports: [ShopifyService],
})
export class ShopifyModule {}
