import {
  MenuGroupDocument,
  MenuGroupSearchMapping,
  MenuGroupSortMapping,
  MenuGroupToCreate,
  MenuGroupToIndex,
  MenuGroupToRemove,
  MenuGroupToUpdate,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import {
  FilterQuery,
  isObjectIdOrHexString,
  isValidObjectId,
  Model,
  Types,
} from 'mongoose';
import * as randomString from 'randomstring';

@Injectable()
export class MenuGroupService {
  constructor(
    @InjectModel('MenuGroup') private menuGroup: Model<MenuGroupDocument>,
  ) {}

  async index(menuGroupToIndex: MenuGroupToIndex) {
    const aggregation = [];

    if (menuGroupToIndex.month) {
      aggregation.push({
        $match: { month: menuGroupToIndex.month },
      });
    }

    if (menuGroupToIndex.sort_type) {
      aggregation.push({
        $sort: MenuGroupSortMapping[menuGroupToIndex.sort_type],
      });
    }

    if (menuGroupToIndex.ids) {
      aggregation.push({
        $match: { _id: { $in: menuGroupToIndex.ids } },
      });
    }

    if (menuGroupToIndex.search_key) {
      if (menuGroupToIndex.search_type == 'all') {
        aggregation.push(
          {
            $addFields: {
              matched: {
                $or: [
                  {
                    $regexMatch: {
                      input: '$name',
                      options: 'i',
                      regex: new RegExp(`.*${menuGroupToIndex.search_key}.*`),
                    },
                  },
                  {
                    $regexMatch: {
                      input: '$type',
                      options: 'i',
                      regex: new RegExp(`.*${menuGroupToIndex.search_key}.*`),
                    },
                  },
                ],
              },
            },
          },
          { $match: { matched: true } },
        );
      } else {
        aggregation.push(
          {
            $addFields: {
              matched: {
                $regexMatch: MenuGroupSearchMapping(
                  menuGroupToIndex.search_type || 'all',
                  menuGroupToIndex.search_key,
                ),
              },
            },
          },
          { $match: { matched: true } },
        );
      }
    }

    aggregation.push({ $match: { deletedAt: { $eq: null } } });

    if (
      (menuGroupToIndex.offset || menuGroupToIndex.offset == 0) &&
      menuGroupToIndex.limit
    ) {
      aggregation.push({
        $skip: menuGroupToIndex.offset * menuGroupToIndex.limit,
      });
      aggregation.push({
        $limit: menuGroupToIndex.limit,
      });
    }

    const menuGroups = await this.menuGroup.aggregate(aggregation);
    return menuGroups;
  }

  async findByIdIn(ids: Types.ObjectId[]): Promise<MenuGroupDocument[]> {
    return await this.menuGroup.find({ _id: { $in: ids } });
  }

  async getDetails(id: string) {
    const filters = { $or: [{ deliverectId: id }] } as any;
    if (isValidObjectId(id)) {
      filters.$or.push({ _id: id });
    }
    const menuGroup = await this.menuGroup.findOne(filters);

    return menuGroup;
  }

  async findByRefOrPlu(ref: string, plu: string) {
    if (!ref && !plu) return null;

    const filters: FilterQuery<MenuGroupDocument> = {
      $or: [],
    };

    if (ref) filters.$or.push({ reference: ref });

    if (isObjectIdOrHexString(ref))
      filters.$or.push({ _id: new Types.ObjectId(ref) });

    if (plu) filters.$or.push({ plu });

    return await this.menuGroup.findOne(filters);
  }

  async getTotalNumberOfGroups(menuGroupToIndex: MenuGroupToIndex) {
    delete menuGroupToIndex.offset;
    delete menuGroupToIndex.limit;
    return (await this.index(menuGroupToIndex)).length;
  }

  async create(menuGroupToCreate: MenuGroupToCreate) {
    if (!menuGroupToCreate.plu) {
      menuGroupToCreate.plu = randomString.generate(10);
    }
    const menuGroup = new this.menuGroup(menuGroupToCreate);

    if (menuGroupToCreate.items) {
      let totalPrice = 0;
      for (let i = 0; i < menuGroupToCreate.items.length; i++) {
        if (!menuGroupToCreate.items[i].plu) {
          menuGroup.items[i].plu = randomString.generate(10);
        }
        totalPrice += menuGroupToCreate.items[i].price;
      }
      menuGroup.totalPrice = totalPrice;
    }

    await menuGroup.save();
    return menuGroup;
  }

  async update(menuGroupUpdate: MenuGroupToUpdate) {
    const menuGroup = await this.menuGroup.findOne({
      _id: menuGroupUpdate._id,
    });
    if (!menuGroupUpdate.plu) {
      menuGroupUpdate.plu = randomString.generate(10);
    }
    await this.menuGroup.updateOne({ _id: menuGroup._id }, menuGroupUpdate);
    return menuGroup;
  }

  async remove(menuGroupToRemove: MenuGroupToRemove) {
    const menuGroup = await this.menuGroup.findOne({
      _id: menuGroupToRemove._id,
    });
    menuGroup.deletedBy = menuGroupToRemove.deletedBy;
    menuGroup.deletedAt = moment.utc().toDate();
    await menuGroup.save();
    return menuGroup;
  }

  async removeMany(ids: Types.ObjectId[]) {
    await this.menuGroup.deleteMany({ _id: { $in: ids } });
  }

  async updateGroupPlu(groupId: Types.ObjectId, plu: string) {
    await this.menuGroup.updateOne({ _id: groupId }, { $set: { plu } });
  }

  async updateGroupItemPlu(
    groupId: Types.ObjectId,
    itemName: string,
    plu: string,
  ) {
    const group = await this.menuGroup.findOne({ _id: groupId });
    group.items.map((item) => {
      if (item.name == itemName) {
        item.plu = plu;
      }
    });

    await this.menuGroup.updateOne({ _id: groupId }, group);
  }
}
