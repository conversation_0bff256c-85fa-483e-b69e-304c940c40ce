import { CompanyDocument, CurrentUser } from '@app/shared-stuff';
import { Request } from 'express';
import { Types } from 'mongoose';

// This interface represents the additional properties added to the express
// request instance by the authenticate-user middleware.
export interface HydratedRequest extends Request {
  current: CurrentUser;
  company?: CompanyDocument;
  company_id?: Types.ObjectId;
  companies?: Types.ObjectId[];
  branches?: Types.ObjectId[];
}
