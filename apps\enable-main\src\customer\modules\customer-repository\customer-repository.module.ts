import { CollectionName, CustomerSchema } from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CustomerRepository } from './customer.repository';
import { CustomerRepositoryInterface } from './customer.repository.interface';

@Module({
  providers: [
    {
      provide: CustomerRepositoryInterface,
      useClass: CustomerRepository,
    },
  ],
  imports: [
    MongooseModule.forFeature([
      { name: CollectionName.CUSTOMER, schema: CustomerSchema },
    ]),
  ],
  exports: [CustomerRepositoryInterface],
})
export class CustomerRepositoryModule {}
