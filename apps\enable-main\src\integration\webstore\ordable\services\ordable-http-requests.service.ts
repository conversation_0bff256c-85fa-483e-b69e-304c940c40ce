import { LoggerService } from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import { Inject, Injectable } from '@nestjs/common';

import { AxiosError } from 'axios';
import { HelperService } from '../../../../shared/services/helper/helper.service';
import { IntegrationLogRepositoryInterface } from '../../../integration-log/repositories/interfaces/integration-log.repository.interface';
import { OrdableHttpResponseDto } from '../dtos/ordable-http-response.dto';
import { OrdableHttpRequestsServiceInterface } from './ordable-http-requests.service.interface';

@Injectable()
export class OrdableHttpRequestsService
  implements OrdableHttpRequestsServiceInterface
{
  private readonly loggerService = new LoggerService(
    OrdableHttpRequestsService.name,
  );
  CONTENT_TYPE = 'application/json';
  constructor(
    private httpService: HttpService,
    private helperService: HelperService,
    @Inject('IntegrationLogRepositoryInterface')
    private readonly integrationLogRepository: IntegrationLogRepositoryInterface,
  ) {}

  async createOrdablePostRequest(
    URL: string,
    API_KEY: string,
    createOrdableDto: any,
  ): Promise<OrdableHttpResponseDto> {
    this.loggerService.log('Store API key', API_KEY);
    return new Promise(async (resolve) => {
      this.httpService
        .post(URL, createOrdableDto, {
          headers: {
            Authorization: `${API_KEY}`,
            Accept: this.CONTENT_TYPE,
            'Content-Type': this.CONTENT_TYPE,
          },
        })
        .subscribe({
          next: (data) => {
            resolve(data.data);
          },
          error: (err: AxiosError) => {
            this.loggerService.error(
              `${err.message} trying to POST ${URL}`,
              err.stack,
              {
                responseData: err.response?.data,
                responseMessage: err.response?.statusText,
                createOrdableDto,
              },
            );
            resolve(this.helperService.transformError(err));
          },
        });
    });
  }
  async createOrdablePatchRequest(
    URL: string,
    API_KEY: string,
    updateOrdableDto: any,
  ): Promise<OrdableHttpResponseDto> {
    return new Promise(async (resolve) => {
      this.httpService
        .patch(URL, updateOrdableDto, {
          headers: {
            Authorization: `${API_KEY}`,
            Accept: this.CONTENT_TYPE,
            'Content-Type': this.CONTENT_TYPE,
          },
        })
        .subscribe({
          next: (data) => {
            resolve(data.data);
          },
          error: (err) => {
            this.loggerService.error(
              err.message + ` Trying to PATCH ${URL}`,
              err.stacktrace,
              {
                responseData: err.response?.data,
                responseMessage: err.response?.statusText,
                updateOrdableDto,
              },
            );
            resolve(this.helperService.transformError(err));
          },
        });
    });
  }

  async createOrdableGetRequest(
    URL: string,
    API_KEY: string,
  ): Promise<OrdableHttpResponseDto> {
    return new Promise(async (resolve) => {
      this.httpService
        .get(URL, {
          headers: {
            Authorization: `${API_KEY}`,
            Accept: this.CONTENT_TYPE,
            'Content-Type': this.CONTENT_TYPE,
          },
        })
        .subscribe({
          next: (data) => {
            resolve(data.data);
          },
          error: (err) => {
            this.loggerService.error(
              err.message + ` Trying to GET ${URL}`,
              err.stacktrace,
              {
                responseData: err.response?.data,
                responseMessage: err.response?.statusText,
              },
            );
            resolve(this.helperService.transformError(err));
          },
        });
    });
  }

  async createOrdableDeleteRequest(
    URL: string,
    API_KEY: string,
  ): Promise<OrdableHttpResponseDto> {
    return new Promise(async (resolve) => {
      this.httpService
        .delete(URL, {
          headers: {
            Authorization: `${API_KEY}`,
            Accept: this.CONTENT_TYPE,
            'Content-Type': this.CONTENT_TYPE,
          },
        })
        .subscribe({
          next: (data) => {
            resolve(data.data);
          },
          error: (err) => {
            this.loggerService.error(
              `Trying to DELETE ${URL}` + err.message,
              err.stack,
              {
                responseData: err.response?.data,
                responseMessage: err.response?.statusText,
              },
            );
            resolve(this.helperService.transformError(err));
          },
        });
    });
  }

  async logOrdableResponse(
    action: string,
    requestBody: any,
    responseBody: any,
  ): Promise<void> {
    if (!responseBody.success)
      return await this.integrationLogRepository.logError(
        action,
        requestBody,
        responseBody,
      );

    await this.integrationLogRepository.logSuccess(
      action,
      requestBody,
      responseBody,
    );
  }
}
