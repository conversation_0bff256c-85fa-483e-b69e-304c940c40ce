import { CustomCacheModule, SharedStuffModule } from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { EventEmitter2, EventEmitterModule } from '@nestjs/event-emitter';
import { MongooseModule } from '@nestjs/mongoose';
import { StoreEvents } from 'apps/enable-main/src/store/enumerations/store-events.enum';
import { BrandModule } from '../brand/brand.module';
import { UserModule } from '../user/user.module';
import { StoreController } from './controllers/store.controller';
import { StoreListener } from './listeners/store.listener';
import { Store, StoreDocument, StoreSchema } from './models/store.model';
import { StoreRepository } from './repositories/store.repository';
import { StoreService } from './services/store.service';

@Module({
  controllers: [StoreController],
  providers: [
    {
      provide: 'StoreServiceInterface',
      useClass: StoreService,
    },
    {
      provide: 'StoreRepositoryInterface',
      useClass: StoreRepository,
    },
    StoreListener,
  ],
  imports: [
    EventEmitterModule,
    UserModule,
    SharedStuffModule,
    CustomCacheModule,
    BrandModule,
    MongooseModule.forFeatureAsync([
      {
        name: Store.name,
        useFactory: (eventEmitter: EventEmitter2) => {
          const storeSchema = StoreSchema;
          storeSchema.post<StoreDocument>(
            'save',
            async function (store: StoreDocument, next: any) {
              eventEmitter.emit(StoreEvents.STORE_UPDATED, store);
              next();
            },
          );

          return storeSchema;
        },
        inject: [EventEmitter2],
      },
    ]),
  ],

  exports: ['StoreServiceInterface'],
})
export class StoreModule {}
