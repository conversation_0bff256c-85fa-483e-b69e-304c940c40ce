import {
  IsNotEmpty,
  IsBoolean,
  IsString,
  IsUrl,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { DibsyAmount } from './dibsy-amount.dto';

export class DibsyCreatePaymentLinkDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @ValidateNested()
  @Type(() => DibsyAmount)
  amount: DibsyAmount;

  @IsString()
  @IsNotEmpty()
  description: string;

  @IsUrl()
  redirectUrl: string;

  @IsUrl()
  webhookUrl: string;

  @IsBoolean()
  reusable: boolean;
}
