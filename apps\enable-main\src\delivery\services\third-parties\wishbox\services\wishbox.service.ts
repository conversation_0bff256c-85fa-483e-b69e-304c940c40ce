import {
  LoggerService,
  OrderDeliveryAction,
  OrderDocument,
  OrderPaymentMethod,
} from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { IntegrationLogRepositoryInterface } from '../../../../../integration/integration-log/repositories/interfaces/integration-log.repository.interface';
import { LocationType } from '../../../../../shared/enums/location-type.enum';
import { HelperService } from '../../../../../shared/services/helper/helper.service';
import { ThirdPartyTaskCreationDto } from '../../../../dto/third-party-task-creation.dto';
import { ThirdPartiesServiceInterface } from '../../third-parties.service.interface';
import { ThirdPartySharedService } from '../../third-party-shared.service';
import { CreateWishboxOrder } from '../dtos/create-wishbox-order.dto';
import { UpdateWishboxDeliveryStatusDto } from '../dtos/update-wishbox-delivery-status.dto';
import { WishboxPaymentType } from '../enums/wishbox-payment-type.enum';
import { WishboxServiceInterface } from './wishbox.service.interface';

@Injectable()
export class WishboxService
  implements WishboxServiceInterface, ThirdPartiesServiceInterface
{
  vehicleTypes: string[] = [] as const;
  defaultVehicleType: string = '';
  private readonly loggerService = new LoggerService(WishboxService.name);

  constructor(
    private configService: ConfigService,
    private httpService: HttpService,
    private helperService: HelperService,
    @Inject('IntegrationLogRepositoryInterface')
    private readonly integrationLogRepository: IntegrationLogRepositoryInterface,
    private thirdPartySharedService: ThirdPartySharedService,
    private eventEmitter: EventEmitter2,
  ) {}

  async createTask(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
  ): Promise<any> {
    const createWishboxOrderDto = await this.constructCreateWishboxOrderDto(
      thirdPartyTaskCreationDto,
    );
    try {
      const response = await this.create(createWishboxOrderDto);
      await this.integrationLogRepository.logSuccess(
        '[Wishbox] Place Order',
        createWishboxOrderDto,
        response,
      );
      return { status: 'success', response: response };
    } catch (error) {
      const errorMessage = error.response?.data?.message ?? error.message;
      this.loggerService.error(
        errorMessage,
        error.stacktrace,
        createWishboxOrderDto,
      );
      await this.integrationLogRepository.logError(
        '[Wishbox] Place Order',
        createWishboxOrderDto,
        error,
      );
      return { status: 'error', message: errorMessage };
    }
  }

  async applyPostFunction(
    taskCreationResponse: any,
    order: OrderDocument,
  ): Promise<void> {
    await this.updateOrder(taskCreationResponse, order);
  }

  async updateWishboxDeliveryStatus(
    updateWishboxDeliveryStatusDto: UpdateWishboxDeliveryStatusDto,
  ): Promise<void> {
    this.eventEmitter.emit('wishbox.updated', updateWishboxDeliveryStatusDto);
  }

  async create(createWishboxOrder: CreateWishboxOrder): Promise<unknown> {
    const apiKey = this.configService.get('WISHBOX_API_KEY');
    const URL = this.configService.get('WISHBOX_PLACE_ORDER_URL');
    const headers = this.constructRequestHeaders(apiKey);

    return new Promise(async (resolve) => {
      this.httpService
        .post(URL, createWishboxOrder, {
          headers: headers,
        })
        .subscribe({
          next: (data) => {
            resolve(data.data ?? {});
          },
          error: (error) => {
            this.loggerService.error(
              'Error while creating wishbox order' + error.message,
              error.stacktrace,
              createWishboxOrder,
            );
            resolve({ status: 'error', message: JSON.stringify(error) });
          },
        });
    });
  }

  private async updateOrder(response: any, order: OrderDocument) {
    order.assigned_driver_name = 'Wishbox Driver';
    order.driver = undefined;
    order.deliveryTaskCreated = true;
    order.wishboxOrderId = response?.data?.orderId;
    await order.save();
  }

  private async constructCreateWishboxOrderDto(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
  ): Promise<CreateWishboxOrder> {
    const order = thirdPartyTaskCreationDto.order;
    const isPickupOrder =
      order.delivery_action === OrderDeliveryAction.IN_STORE_PICKUP;
    const deliveryAddress = isPickupOrder
      ? 'Pickup'
      : this.helperService.convertLocationToString(
          order.deliveryLocation,
          LocationType.DELIVERY,
        );

    const shopLocation = await this.thirdPartySharedService.getPickupLocation(
      thirdPartyTaskCreationDto,
    );

    return {
      fullName: order.customer_name,
      phoneNumber: order.customer_phone,
      address: deliveryAddress,
      landMark: order.deliveryLocation.nearestLandmark,
      orderAmount: order.total_amount.toString(),
      suggestion:
        order.order_remarks + ', Enable Order ID: ' + order._id.toHexString(),
      latitude: order.deliveryLocation.latitude,
      longitude: order.deliveryLocation.longitude,
      paymentType: this.mapPaymentMethodToWishboxPaymentType(
        order.payment_method,
      ),
      shopAddress: this.helperService.convertLocationToString(
        shopLocation,
        LocationType.PICKUP,
      ),
      shopLatitude: shopLocation.latitude,
      shopLongitude: shopLocation.longitude,
      shopId:
        typeof order.branch !== 'string'
          ? order?.branch?.wishboxShopId
          : undefined,
      shopName:
        typeof order.branch !== 'string' ? order?.branch?.name : undefined,
    };
  }

  private mapPaymentMethodToWishboxPaymentType(
    paymentMethod: OrderPaymentMethod,
  ): WishboxPaymentType {
    if (paymentMethod == OrderPaymentMethod.online)
      return WishboxPaymentType.PAID_ONLINE;
    else return WishboxPaymentType.COD;
    //as a default value ( business requirement)
  }

  private constructRequestHeaders(apiKey: string) {
    const CONTENT_TYPE = 'application/json';
    return {
      apiKey: apiKey,
      Accept: CONTENT_TYPE,
      'Content-Type': CONTENT_TYPE,
    };
  }
}
