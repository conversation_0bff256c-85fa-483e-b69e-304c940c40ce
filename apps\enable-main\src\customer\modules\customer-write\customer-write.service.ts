import {
  Create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  CurrentUser,
  Customer,
  CustomerDocument,
  CustomerEarnedBenefit,
  DeviceData,
  EarnedReward,
  MessageCustomerDto,
  omit,
  responseCode,
  SavedLocationDocument,
  SavedLocationToCreate,
  UpdateCustomerDto,
  UsedReward,
} from '@app/shared-stuff';
import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { plainToInstance } from 'class-transformer';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { BranchService } from '../../../branch/services/branch/branch.service';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { SavedLocationService } from '../../../location/services/saved-location/saved-location.service';
import { CustomerRepositoryInterface } from '../customer-repository/customer.repository.interface';
import { CustomerWriteServiceInterface } from './customer-write.service.interface';

@Injectable()
export class CustomerWriteService implements CustomerWriteServiceInterface {
  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly savedLocationService: SavedLocationService,
    private readonly branchService: BranchService,
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandServiceInterface,
    @Inject(CustomerRepositoryInterface)
    private readonly customerRepository: CustomerRepositoryInterface,
  ) {}

  async create(
    createCustomerDto: CreateCustomerDto,
  ): Promise<CustomerDocument> {
    const existedCustomer =
      await this.customerRepository.findByPhoneAndCompanyId(
        createCustomerDto.phone,
        createCustomerDto.company,
      );
    if (existedCustomer) {
      throw new BadRequestException(
        'customer is already exist',
        responseCode.DUPLICATED_ENTRY.toString(),
      );
    }

    if (!createCustomerDto.first_name) {
      createCustomerDto.first_name = 'anonymous';
      createCustomerDto.last_name = 'customer';
    }

    createCustomerDto.full_name = createCustomerDto.last_name
      ? createCustomerDto.first_name + ' ' + createCustomerDto.last_name
      : createCustomerDto.first_name;
    const customer = plainToInstance(Customer, createCustomerDto);
    customer.company = new Types.ObjectId(createCustomerDto.company);
    if (createCustomerDto.firstBrandOrderdId)
      await this.addFirstBrandOrdered(
        createCustomerDto.firstBrandOrderdId,
        customer,
      );
    if (createCustomerDto.firstBranchOrderdId)
      await this.addFirstBranchOrdered(
        createCustomerDto.firstBranchOrderdId,
        customer,
      );
    const createdCustomer = await this.customerRepository.create(customer);
    if (createCustomerDto.location) {
      const createdLocation = await this.createInitialLocation(
        createCustomerDto.location,
        createdCustomer,
      );
      await this.addLocationToCustomer(createdCustomer, createdLocation._id);
    }
    return createdCustomer;
  }

  private async addFirstBrandOrdered(
    firstBrandOrderedId: Types.ObjectId,
    customer: Customer,
  ): Promise<void> {
    const brand = await this.brandService.findById(firstBrandOrderedId);
    customer.firstBrandOrderd = {
      _id: brand.id,
      name: brand.name,
      phoneNumber: brand.phoneNumber,
      senderId: brand.senderId,
      emailSenderId: brand.emailSenderId,
      image: brand.image,
    };
  }

  private async addFirstBranchOrdered(
    firstBranchOrderdId: Types.ObjectId,
    customer: Customer,
  ): Promise<void> {
    const branch = await this.branchService.get_details(
      firstBranchOrderdId.toString(),
    );
    customer.firstBranchOrderd = {
      _id: branch._id,
      name: branch.name,
    };
  }

  private async createInitialLocation(
    location: SavedLocationToCreate,
    createdCustomer: CustomerDocument,
  ): Promise<SavedLocationDocument> {
    location.customerId = createdCustomer._id;
    return await this.savedLocationService.create(location);
  }

  async addLocationToCustomer(
    createdCustomer: CustomerDocument,
    createdLocationId: Types.ObjectId,
  ): Promise<void> {
    createdCustomer.savedLocations.push(createdLocationId);
    await this.customerRepository.findOneAndUpdate(
      { _id: createdCustomer._id },
      createdCustomer,
    );
  }

  async updateOrCreate(
    createCustomerDto: CreateCustomerDto & { loyaltyTier?: unknown },
    currentUser: CurrentUser,
  ): Promise<CustomerDocument> {
    const customer = await this.customerRepository.findByPhoneAndCompanyId(
      createCustomerDto.phone,
      createCustomerDto.company,
    );
    if (customer) {
      createCustomerDto.first_name =
        createCustomerDto.first_name || customer.first_name;
      createCustomerDto.last_name =
        createCustomerDto.last_name || customer.last_name;
      createCustomerDto.full_name = createCustomerDto.last_name
        ? createCustomerDto.first_name + ' ' + createCustomerDto.last_name
        : createCustomerDto.first_name;
      createCustomerDto['updatedBy'] = currentUser;
      createCustomerDto['_id'] = customer._id;
      const updateCustomerDto = omit(createCustomerDto, [
        'phone',
        'contact_channel',
        'createdBy',
        'company',
        'loyaltyTier',
      ]);
      await customer.updateOne(updateCustomerDto);
      this.eventEmitter.emit(
        'customer.updated',
        customer.set(updateCustomerDto),
      );
      return customer.set(updateCustomerDto);
    }
    return await this.create(createCustomerDto);
  }

  async findOrCreate(
    createCustomerDto: CreateCustomerDto,
    savedLocationId?: Types.ObjectId,
  ): Promise<CustomerDocument> {
    let customer = await this.customerRepository.findByPhoneAndCompanyId(
      createCustomerDto.phone,
      new Types.ObjectId(createCustomerDto.company),
    );
    if (!customer) customer = await this.create(createCustomerDto);

    if (savedLocationId && !customer.savedLocations.includes(savedLocationId))
      await this.addLocationToCustomer(customer, savedLocationId);
    return customer;
  }

  async removeLatestPayment(id: Types.ObjectId): Promise<void> {
    await this.customerRepository.removeLatestPayment(id);
  }

  async update(
    updateCustomerDto: UpdateCustomerDto,
    currentUser: CurrentUser,
    companyId: string,
  ): Promise<Types.ObjectId> {
    const customer = await this.customerRepository.findById(
      new Types.ObjectId(updateCustomerDto._id),
    );
    this.validateCustomer(companyId, 'Update', customer);
    updateCustomerDto.full_name =
      (updateCustomerDto.first_name || customer.first_name) +
      ' ' +
      (updateCustomerDto.last_name || customer.last_name);
    updateCustomerDto.updatedBy = currentUser;

    const updatesToApply = omit(updateCustomerDto, ['loyaltyTier']);
    this.eventEmitter.emit('customer.updated.pre', customer, updateCustomerDto);
    await customer.updateOne(updatesToApply);
    this.eventEmitter.emit('customer.updated', customer.set(updatesToApply));

    return customer._id;
  }

  private async validateCustomer(
    companyId: string,
    operation: string,
    existedCustomer: Customer,
  ): Promise<void> {
    if (!existedCustomer)
      throw new NotFoundException(
        'Customer is not exist',
        responseCode.ENTITY_NOT_FOUND.toString(),
      );

    if (!this.isAccessibleCompany(companyId, existedCustomer))
      throw new UnprocessableEntityException(
        "You don't have access to this company Customer",
        responseCode.MISSING_DATA.toString(),
      );

    if (existedCustomer.deletedBy)
      throw new BadRequestException(
        "Can't " + operation + ' a Deleted Customer',
        responseCode.ENTITY_DELETED.toString(),
      );
  }

  private isAccessibleCompany(
    companyId: string,
    existedCustomer: Customer,
  ): boolean {
    return companyId && existedCustomer.company.toHexString() != companyId
      ? false
      : true;
  }

  async remove(
    id: string,
    currentUser: CurrentUser,
    companyId: string,
  ): Promise<string> {
    const existedCustomer = await this.customerRepository.findById(
      new Types.ObjectId(id),
    );
    this.validateCustomer(companyId, 'Delete', existedCustomer);
    await this.checkActiveOrders(existedCustomer);
    return await this.performCustomerDeletion(existedCustomer, currentUser);
  }

  async performCustomerDeletion(
    existedCustomer: CustomerDocument,
    currentUser: CurrentUser,
  ): Promise<string> {
    existedCustomer.deletedBy = currentUser;
    existedCustomer.deletedAt = moment.utc().toDate();
    await existedCustomer.save();
    this.eventEmitter.emit('customer.deleted', existedCustomer);
    return (
      'Customer With Id ' + existedCustomer._id + ' was Deleted Successfully'
    );
  }

  async checkActiveOrders(existedCustomer: CustomerDocument): Promise<void> {
    let activeOrdersCheckComplete = false;

    const timeoutPromise = new Promise<void>((_, reject) => {
      setTimeout(() => {
        if (!activeOrdersCheckComplete) {
          reject(
            new BadRequestException(
              'Timeout: Unable to complete the active orders check',
              responseCode.FAIL_TO_DELETE.toString(),
            ),
          );
        }
      }, 10000); // 10 seconds timeout
    });

    await Promise.race([
      this.eventEmitter.emitAsync(
        'customer.check.active.orders',
        existedCustomer,
      ),
      timeoutPromise,
    ]);

    activeOrdersCheckComplete = true;
  }

  async setDeviceData(
    customerId: Types.ObjectId,
    deviceData: DeviceData,
  ): Promise<void> {
    await this.customerRepository.updateOne(
      { _id: customerId },
      { deviceData },
    );
  }

  async updateRewards(
    customer: CustomerDocument,
    rewards: EarnedReward[] | UsedReward[],
  ): Promise<void> {
    customer = await this.customerRepository.findOne({ _id: customer._id });
    const areUsedRewards = 'redeemedAt' in rewards[0];
    const rewardField = areUsedRewards ? 'usedRewards' : 'rewards';

    const newRewards = [
      ...(customer[rewardField]
        ? customer[rewardField].filter(
            (reward) =>
              !rewards.some((tReward) => reward._id.equals(tReward._id)),
          )
        : []),
      ...rewards,
    ];

    await this.customerRepository.updateRewards(customer._id, newRewards);
  }

  async updateBenefits(
    customer: CustomerDocument,
    benefits: CustomerEarnedBenefit[],
  ): Promise<void> {
    await this.customerRepository.updateBenefits(
      customer._id,
      benefits,
      'earnedBenefits',
    );
  }

  async messageCustomer(messageCustomerDto: MessageCustomerDto): Promise<void> {
    const customer =
      await this.customerRepository.messageCustomer(messageCustomerDto);
    this.eventEmitter.emit('customer.messages.updated', customer);
  }
}
