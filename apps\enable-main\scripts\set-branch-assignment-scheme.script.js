// This script regarding EBL-6006 [BranchAssignmentScheme] to set branchAssignmentScheme and remove the old props
db.companies.updateMany({}, [
  {
    $set: {
      'branchAssignmentConfig.branchAssignmentScheme': {
        $cond: {
          if: { $eq: ['$usingBranchAutoAssignment', true] },
          then: 'nearest',
          else: 'manual',
        },
      },
    },
  },
  {
    $unset: ['usingBranchAutoAssignment', 'usingAutoAssignment'],
  },
]);
