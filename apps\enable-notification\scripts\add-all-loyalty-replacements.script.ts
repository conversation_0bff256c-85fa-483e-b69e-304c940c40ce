// EBL-3714 [Loyalty Program] Enable All Loyalty Content Replacements For All Loyalty Related Triggers
// Add all loyalty replacements to customer, order creation and order completion triggers
db.triggers.updateMany(
  {
    $or: [
      { module: 'CUSTOMER' },
      {
        module: 'ORDER',
        action: {
          $in: [
            'ON_ORDER_CREATED',
            'THREE_HOURS_AFTER_ORDER_COMPLETION',
            'ON_WEBSTORE_ORDER_CREATED',
            'ON_ORDER_COMPLETED',
            'ON_ORDER_COMPLETED_DELIVERECT',
            'ON_DINEIN_ORDER',
            'ON_WALK_IN_ORDER',
            'ON_AGGREGATOR_ORDER',
            'ON_AGGREGATOR_ORDER_NON_LOYALTY_MEMBERS',
            'THREE_HOURS_AFTER_AGGREGATOR_ORDER',
            'THREE_HOURS_AFTER_AGGREGATOR_ORDER_NON_LOYALTY_MEMBERS',
          ],
        },
      },
    ],
  },
  {
    $addToSet: {
      replacement: {
        $each: [
          'firstName',
          'fullName',
          'loyaltyPointBalance',
          'loyaltyTier',
          'remainingOrdersCurrentTier',
          'remainingOrdersUpperTier',
          'upperTierDiscountValue',
          'tierDiscountValue',
          'tierDiscountOrderValueThreshold',
          'loyaltyRegistrationPageLink',
          'walletPassAccessPageLink',
          'walletPassLink',
          'ordableLink',
        ],
      },
    },
  },
);

db.templates.updateMany(
  {
    $or: [
      { 'trigger.module': 'CUSTOMER' },
      {
        'trigger.module': 'ORDER',
        'trigger.action': {
          $in: [
            'ON_ORDER_CREATED',
            'THREE_HOURS_AFTER_ORDER_COMPLETION',
            'ON_WEBSTORE_ORDER_CREATED',
            'ON_ORDER_COMPLETED',
            'ON_ORDER_COMPLETED_DELIVERECT',
            'ON_DINEIN_ORDER',
            'ON_WALK_IN_ORDER',
            'ON_AGGREGATOR_ORDER',
            'ON_AGGREGATOR_ORDER_NON_LOYALTY_MEMBERS',
            'THREE_HOURS_AFTER_AGGREGATOR_ORDER',
            'THREE_HOURS_AFTER_AGGREGATOR_ORDER_NON_LOYALTY_MEMBERS',
          ],
        },
      },
    ],
  },
  {
    $addToSet: {
      'trigger.replacement': {
        $each: [
          'firstName',
          'fullName',
          'loyaltyPointBalance',
          'loyaltyTier',
          'remainingOrdersCurrentTier',
          'remainingOrdersUpperTier',
          'upperTierDiscountValue',
          'tierDiscountValue',
          'tierDiscountOrderValueThreshold',
          'loyaltyRegistrationPageLink',
          'walletPassAccessPageLink',
          'walletPassLink',
          'ordableLink',
        ],
      },
    },
  },
);
