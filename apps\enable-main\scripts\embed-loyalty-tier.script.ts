// EBL-4227 Introduce new automation journeys customer filters / conditions
// Changed customer loyaltyTier from ObjectId to embedded object

const embeddedTierFields = {
  _id: 1,
  nameEn: 1,
  nameAr: 1,
  orderRateThreshold: 1,
  amountSpentThreshold: 1,
  pointsRateThreshold: 1,
  percentDiscount: 1,
  freeDelivery: 1,
  isHighestTier: 1,
  isVipTier: 1,
  minimumCartValueForDiscount: 1,
  microsDiscountId: 1,
  percentDiscountMaximumNumberOfUsesType: 1,
  percentDiscountMaximumNumberOfUses: 1,
  percentDiscountPreserveUsage: 1,
  freeDeliveryMaximumNumberOfUsesType: 1,
  freeDeliveryMaximumNumberOfUses: 1,
  freeDeliveryPreserveUsage: 1,
  loyaltyCardForegroundColor: 1,
  loyaltyCardBackgroundColor: 1,
};

db.customers.bulkWrite(
  db.customers
    .find({ 'loyaltyTier._id': { $exists: true } }, { loyaltyTier: 1 })
    .map((customer) => {
      const loyaltyTier = db.loyaltytiers.findOne(
        { _id: customer.loyaltyTier._id },
        embeddedTierFields,
      );
      if (!loyaltyTier) console.log(customer._id);
      return {
        updateOne: {
          filter: { _id: customer._id },
          update: {
            $set: {
              loyaltyTier: {
                ...customer.loyaltyTier,
                ...loyaltyTier,
              },
            },
          },
        },
      };
    })
    .toArray(),
  { ordered: false },
);

db.customers.updateMany(
  {
    'loyaltyTier.percentDiscountMaximumNumberOfUsesType': 'limited_usage',
    'loyaltyTier.percentDiscountRemainingNumberOfUses': null,
  },
  [
    {
      $set: {
        'loyaltyTier.percentDiscountRemainingNumberOfUses':
          '$loyaltyTier.percentDiscountMaximumNumberOfUses',
      },
    },
  ],
);

db.customers.updateMany(
  {
    'loyaltyTier.freeDeliveryMaximumNumberOfUsesType': 'limited_usage',
    'loyaltyTier.freeDeliveryRemainingNumberOfUses': null,
  },
  [
    {
      $set: {
        'loyaltyTier.freeDeliveryRemainingNumberOfUses':
          '$loyaltyTier.freeDeliveryMaximumNumberOfUses',
      },
    },
  ],
);
