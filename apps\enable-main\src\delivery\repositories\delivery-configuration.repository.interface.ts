import { IGenericRepository } from '@app/shared-stuff';
import { Types } from 'mongoose';
import {
  DeliveryConfiguration,
  DeliveryConfigurationDocument,
} from '../models/delivery-configuration.model';

export interface DeliveryConfigurationRepositoryInterface
  extends IGenericRepository<
    DeliveryConfigurationDocument,
    DeliveryConfiguration
  > {
  deleteDefaultDeliveryMethod(_id: Types.ObjectId): Promise<void>;
  deleteThirdPartyConfiguration(_id: Types.ObjectId): Promise<void>;
  findByBranchId(
    branchId: Types.ObjectId,
  ): Promise<DeliveryConfigurationDocument>;
  getByBrandIdAndBranchId(
    branchId: Types.ObjectId,
    brandId: Types.ObjectId,
  ): Promise<DeliveryConfigurationDocument>;
  getByBrandId(brandId: Types.ObjectId): Promise<DeliveryConfigurationDocument>;
  getByBranchId(
    branchId: Types.ObjectId,
  ): Promise<DeliveryConfigurationDocument>;
  getByCompanyId(
    companyId: Types.ObjectId,
  ): Promise<DeliveryConfigurationDocument>;
}
export const DeliveryConfigurationRepositoryInterface = Symbol(
  'DeliveryConfigurationRepositoryInterface',
);
