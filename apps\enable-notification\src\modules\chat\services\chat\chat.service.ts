import {
  Chat<PERSON><PERSON><PERSON>g<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  HelperSharedServiceInterface,
  IHelperSharedService,
  LoggerService,
  SuccessResult,
  TemplateDocument,
  TriggerUserDto,
  WhatsAppButtonType,
} from '@app/shared-stuff';
import { HttpStatus, Inject, Injectable } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';
import { ConfigurationDocument } from '../../../configuration/models/configuration.model';
import { ChatRepositoryInterface } from '../../repositories/chat.repository.interface';
import { ChatSendDto } from '../../types/dtos/chat-send.dto';
import { SendChatUsingTemplateDto } from '../../types/dtos/send-chat-using-template.dto';
import { ChatIntegrationsServiceInterface } from '../integrations/chat-intergrations.service.interface';
import { EbChatService } from '../integrations/eb-chat/eb-chat.service';
import { ChatServiceInterface } from './chat.service.interface';

@Injectable()
export class ChatService implements ChatServiceInterface {
  private readonly loggerService = new LoggerService(ChatService.name);

  private chatMap: Map<ChatProvider, ChatIntegrationsServiceInterface>;

  constructor(
    @Inject(ChatRepositoryInterface)
    private readonly chatRepository: ChatRepositoryInterface,
    private ebChatService: EbChatService,
    @Inject(IHelperSharedService)
    private helperSharedService: HelperSharedServiceInterface,
  ) {
    this.initChatMap();
  }

  async send(chatSendDto: ChatSendDto, configuration: ConfigurationDocument) {
    await this.validateProviderExists(chatSendDto, configuration);
    chatSendDto = {
      ...chatSendDto,
      text: this.removeTabsAndNewLines(chatSendDto.text),
    };
    try {
      const chatResponse = await this.chatMap
        .get(configuration.chatConfig.provider)
        .send(chatSendDto, configuration.chatConfig);
      await this.createChatLog(chatResponse, chatSendDto);
      this.loggerService.log(
        'create EbChat Request successfully',
        chatSendDto,
        chatResponse,
      );
    } catch (error: any) {
      this.loggerService.error(
        'An error occurred while sending chat with message ' + error.message,
        error.stacktrace,
        chatSendDto,
      );
    }
  }

  async getTemplates(chatConfigDto: ChatConfigDto) {
    return await this.chatMap
      .get(chatConfigDto.provider)
      .getTemplates(chatConfigDto);
  }

  async sendUsingTemplate(
    user: TriggerUserDto,
    template: TemplateDocument,
    configuration: ConfigurationDocument,
    replacement: Record<string, any>,
  ) {
    const chatSendDto = this.constructSendChatTemplateDto(
      user,
      template,
      replacement,
    );
    try {
      const chatResponse = await this.chatMap
        .get(configuration.chatConfig.provider)
        .sendUsingTemplate(chatSendDto, configuration.chatConfig);
      this.loggerService.log(
        'Chat Success Response: ',
        chatSendDto,
        chatResponse,
      );
      await this.createChatLog(chatResponse, chatSendDto);
    } catch (error) {
      this.loggerService.error(
        'Chat Error Response: ' + error.message,
        error.stacktrace,
        chatSendDto,
      );
      await this.createChatLog(
        this.helperSharedService.transformError(error),
        chatSendDto,
      );
    }
  }

  getTemplateComponents(
    template: TemplateDocument,
    replacement: Record<string, any>,
  ) {
    const components = [];

    if (
      template.ebChatTemplate.replacementMapping &&
      template.ebChatTemplate.replacementMapping.length > 0
    ) {
      const bodyParameters = template.ebChatTemplate.replacementMapping.map(
        (mapping) => {
          return {
            type: 'text',
            text:
              replacement[mapping] && replacement[mapping] !== 0
                ? replacement[mapping].toString()
                : 'N/A',
          };
        },
      );
      components.push({
        type: 'body',
        parameters: bodyParameters,
      });
    }

    // Requirement from EBChat team to handle OTPs as special case
    if (
      template.ebChatTemplate.buttonsReference?.some((references) =>
        references?.some((button) => button.type === WhatsAppButtonType.OTP),
      )
    ) {
      components.push({
        type: 'BUTTON',
        sub_type: 'url',
        index: '0',
        parameters: [
          {
            type: 'text',
            text: replacement?.otp?.toString(),
          },
        ],
      });
    }

    return components;
  }

  getTemplateLanguage(
    templateLanguages: string[],
    userPreferredLanguage: string,
  ): string {
    let templateLanguage;
    if (templateLanguages.length > 0 && userPreferredLanguage)
      templateLanguage = templateLanguages.find((lang: string) =>
        lang.toLowerCase().startsWith(userPreferredLanguage.toLowerCase()),
      );
    return templateLanguage ?? templateLanguages[0];
  }

  private constructSendChatTemplateDto(
    user: TriggerUserDto,
    template: TemplateDocument,
    replacement: Record<string, any>,
  ): SendChatUsingTemplateDto {
    const templateLanguage = this.getTemplateLanguage(
      template.languages,
      user.preferredLanguage,
    );

    const metadata = this.getChatMetaData(template, replacement);

    const components = this.getTemplateComponents(template, replacement);

    return {
      messaging_product: 'whatsapp',
      recipient_type: 'individual',
      to: user.countryCode?.replace('+', '') + user.phone,
      type: 'template',
      template: {
        name: template.ebChatTemplate.externalReference,
        language: {
          code: templateLanguage,
          policy: 'deterministic',
        },
        components: components,
      },
      metadata,
    };
  }

  private async validateProviderExists(
    chatSendDto: ChatSendDto,
    configuration: ConfigurationDocument,
  ) {
    if (
      !configuration.chatConfig?.provider ||
      configuration.chatConfig.provider === ChatProvider.NO_PROVIDER
    ) {
      this.loggerService.error('No chat provider configured. Chat not sent.', {
        chatSendDto: chatSendDto,
        configuration: configuration,
      });

      await this.createChatLog(
        {
          success: false,
          status: 'error',
          message: 'No chat provider configured. Chat not sent',
          stackOrResponse: { configuration },
        },
        chatSendDto,
      );

      throw new RpcException({
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'No chat provider configured. Chat not sent',
        stackOrResponse: { configuration },
        data: { chatSendDto },
      });
    }
  }

  private removeTabsAndNewLines(text: any): string {
    // Sending text with new lines or tabs to EB chat causes an error that says
    // "cannot have new-line/tab characters or more than 4 consecutive spaces"
    // Global flag required when calling replaceAll with regex
    // /g to perform a global replace on a string so we don't need replaceAll
    const spacesRegex = /\s{2,}/g;
    return text
      .replaceAll('\n', ' ')
      .replaceAll('\t', ' ')
      .replace(spacesRegex, ' ');
  }

  private async createChatLog(
    chatResponse: SuccessResult | ErrorResult | any,
    chatSendDto: ChatSendDto | SendChatUsingTemplateDto,
  ) {
    if ('template' in chatSendDto) {
      await this.chatRepository.create({
        name: chatSendDto.template?.name,
        phone: chatSendDto.to,
        templateComponents: chatSendDto.template?.components,
        rawResponse: chatResponse?.response?.data || chatResponse?.response,
        responseData: JSON.stringify(chatResponse),
        metadata: chatSendDto.metadata,
      });
    } else
      await this.chatRepository.create({
        name: chatSendDto.name,
        phone: chatSendDto.phone,
        text: chatSendDto.text,
        rawResponse: chatResponse.success
          ? (chatResponse as SuccessResult).data
          : (chatResponse as ErrorResult).stackOrResponse,
        metadata: chatSendDto.metadata,
      });
  }

  private initChatMap() {
    this.chatMap = new Map<ChatProvider, ChatIntegrationsServiceInterface>([
      [ChatProvider.EB_CHAT, this.ebChatService],
    ]);
  }

  private getChatMetaData(
    template: TemplateDocument,
    replacement: Record<string, any>,
  ) {
    const metaData: Record<string, any> = {};
    if (replacement['orderCode'] && template.ebChatTemplate?.ebChatTrigger) {
      metaData['data'] = { enable_orderId: replacement['orderCode'] };
      metaData['trigger'] = template.ebChatTemplate?.ebChatTrigger;
    }
    return metaData;
  }
}
