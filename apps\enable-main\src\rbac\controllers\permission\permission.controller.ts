import { PermissionToIndx } from '../../../rbac/dto/permission.dto';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  SetMetadata,
} from '@nestjs/common';
import { PermissionService } from '../../services/permission/permission.service';
import { responseCode } from '@app/shared-stuff';
import { Request, Response } from 'express';

import {
  PermissionToCreate,
  PermissionToUpdate,
} from '../../dto/permission.dto';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { HelperService } from '../../../shared/services/helper/helper.service';

@ApiTags('Permission')
@Controller('permission')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@SetMetadata('module', 'permission')
export class PermissionController {
  constructor(
    private permissionService: PermissionService,
    private helperService: HelperService,
  ) {}

  @Get()
  @SetMetadata('action', 'get_all')
  async findAll(
    @Query() dataIndex: PermissionToIndx,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      dataIndex.user_data = req['user'];
      const selectedPermissions = await this.permissionService.index(dataIndex);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success for getting permission details',
        selectedPermissions,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
  @Get(':id')
  @SetMetadata('action', 'get_details')
  async findDetails(@Param('id') id: string, @Res() res: Response) {
    try {
      const selectedPerrmison = await this.permissionService.get_details(id);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'success for getting permmison by id',
        selectedPerrmison,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() permissionToCreate: PermissionToCreate,
    @Res() res: Response,
  ) {
    try {
      const createdRole =
        await this.permissionService.create(permissionToCreate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success for creating role',
        createdRole,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
  @Put(':id')
  @SetMetadata('action', 'update')
  async update(
    @Body() permissionToUpdate: PermissionToUpdate,
    @Res() res: Response,
  ) {
    try {
      const updatedPermission =
        await this.permissionService.update(permissionToUpdate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'success for update permission',
        updatedPermission,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Delete(':id')
  @SetMetadata('action', 'remove')
  async remove(@Param('id') id: string, @Res() res: Response) {
    try {
      const removedPermission = await this.permissionService.remove('id');
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_REMOVE,
        'success for remove permission',
        removedPermission,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get('export/excel')
  @SetMetadata('action', 'export_excel')
  async export_excel(@Res() res: Response) {
    try {
      const filePath = await this.permissionService.export_excel();
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'Success to get all as excel file',
        filePath,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
}
