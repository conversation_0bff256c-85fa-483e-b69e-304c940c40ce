import {
  CouponDocument,
  CustomerEarnedBenefit,
  EarnedReward,
  LoyaltyTierDocument,
  PromotionOrdableInfo,
} from '@app/shared-stuff';
import { StoreDocument } from '../../../../../store/models/store.model';

export interface OrdablePromotionsServiceInterface {
  createCouponDiscount(coupon: CouponDocument): Promise<CouponDocument>;
  deleteCouponDiscount(coupon: CouponDocument): Promise<CouponDocument>;
  syncPromotionUpdate(
    enablePromotion: LoyaltyTierDocument | CouponDocument,
  ): Promise<void>;
  addCustomerToPromotion(
    store: StoreDocument,
    customerOrdableId: number,
    enablePromotion: LoyaltyTierDocument | CouponDocument,
  ): Promise<PromotionOrdableInfo>;
  removeCustomerFromPromotion(
    store: StoreDocument,
    customerOrdableId: number,
    enablePromotion: LoyaltyTierDocument | CouponDocument,
  ): Promise<PromotionOrdableInfo>;
  createOrdablePromotion(
    store: StoreDocument,
    customerOrdableIds: number[],
    enablePromotion:
      | LoyaltyTierDocument
      | CouponDocument
      | EarnedReward
      | CustomerEarnedBenefit,
  ): Promise<PromotionOrdableInfo>;
  deleteOrdablePromotion(
    store: StoreDocument,
    promotionOrdableId: number,
  ): Promise<PromotionOrdableInfo>;
}

export const OrdablePromotionsServiceInterface = Symbol(
  'OrdablePromotionsServiceInterface',
);
