name: 'Enable-Backend: Development'

on:
  push:
    branches:
      - development
  pull_request:
    types:
      - closed
    branches:
      - development
  workflow_dispatch:

jobs:
  CacheDependencies:
    runs-on: ubuntu-latest
    outputs:
      cache-hit: ${{ steps.cache-deps.outputs.cache-hit }}
    steps:
      - name: Cache Node Modules
        id: cache-deps
        uses: actions/cache@v2
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

  PullChanges:
    runs-on: ubuntu-latest
    needs: CacheDependencies
    steps:
      - name: Check Out Repository
        uses: actions/checkout@v3
      - name: Pull Changes
        uses: appleboy/ssh-action@v0.1.4
        with:
        # ... your SSH connection details ...

  InstallDependency:
    runs-on: ubuntu-latest
    needs: PullChanges  # This job will not run if PullChanges fails
    steps:
      - name: Check Out Repository
        uses: actions/checkout@v3
      - name: Install Dependencies
        if: steps.cache-deps.outputs.cache-hit != 'true'
        run: npm ci

  BuildAndReload:
    needs: InstallDependency  # This job will not run if InstallDependency fails
    environment: Development
    runs-on: ubuntu-latest
    strategy:
      matrix:
        components: [ 'enable-main', 'enable-notification', 'enable-reporting' ]
    steps:
      - name: Check Out Repository
        uses: actions/checkout@v3
      - name: Build ${{ matrix.components }}
        run: nest build ${{ matrix.components }}
      - name: Reload Service
        if: ${{ needs.InstallDependency.outputs.changed }}
        uses: appleboy/ssh-action@v0.1.4
        with:
        # ... SSH details ...

  Notify:
    runs-on: ubuntu-latest
    needs: BuildAndReload  # This job will not run if BuildAndReload fails
    if: always()  # This ensures the notification is sent whether the previous job succeeded or failed
    steps:
      - name: Send Notification
        uses: some-notification-action@v1
        with:
        # ... your notification configuration ...

  Cleanup:
    runs-on: ubuntu-latest
    needs: [ BuildAndReload, Notify ]
    if: always()  # Cleanup should always run
    steps:
      - name: Cleanup Workspace
        run: # Your cleanup commands here
