import { Inject, Injectable, Logger } from '@nestjs/common';

import {
  BenefitAppliedTo,
  CouponDocument,
  CustomerDocument,
  CustomerEarnedBenefit,
  CustomerUsedBenefit,
  EarnedReward,
  EmbeddedTierDto,
  forEachAsync,
  LoyaltyStatus,
  LoyaltyTierDocument,
  mapAsync,
  PromotionOrdableInfo,
  PunchCardBenefit,
  UsedReward,
} from '@app/shared-stuff';

import { CouponServiceInterface } from '../../../../../coupon/services/coupon.service.interface';
import { CustomerWriteServiceInterface } from '../../../../../customer/modules/customer-write/customer-write.service.interface';
import { LoyaltyTierReadServiceInterface } from '../../../../../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.service.interface';
import { StoreDocument } from '../../../../../store/models/store.model';
import { OrdableFreeDeliveryServiceInterface } from '../free-delivery/ordable-free-delivery.service.interface';
import { OrdablePromotionsServiceInterface } from '../promotions/ordable-promotions.service.interface';
import { OrdableStoresServiceInterface } from '../stores/ordable-stores.service.interface';
import { OrdablePromotionsSyncServiceInterface } from './ordable-promotions-sync.service.interface';

@Injectable()
export class OrdablePromotionsSyncService
  implements OrdablePromotionsSyncServiceInterface
{
  private logger = new Logger(OrdablePromotionsSyncService.name);

  constructor(
    @Inject(OrdableStoresServiceInterface)
    private readonly ordableStoresService: OrdableStoresServiceInterface,
    @Inject(LoyaltyTierReadServiceInterface)
    private readonly loyaltyTierService: LoyaltyTierReadServiceInterface,
    @Inject(CouponServiceInterface)
    private readonly couponService: CouponServiceInterface,
    @Inject(OrdableFreeDeliveryServiceInterface)
    private readonly ordableFreeDeliveryService: OrdableFreeDeliveryServiceInterface,
    @Inject(OrdablePromotionsServiceInterface)
    private readonly ordablePromotionsService: OrdablePromotionsServiceInterface,
    @Inject(CustomerWriteServiceInterface)
    private readonly customerWriteService: CustomerWriteServiceInterface,
  ) {}

  async initPromotions(
    store: StoreDocument,
    customers: CustomerDocument[],
  ): Promise<void> {
    const loyaltyTiers = await this.loyaltyTierService.findByCompanyId(
      store.companyId,
    );

    const hasOrdableId = (customer: CustomerDocument) =>
      customer.ordableStores?.[store._id.toHexString()]?.ordableId;
    const toOrdableId = hasOrdableId;
    const ordableCustomers = customers.filter(hasOrdableId);

    await forEachAsync(loyaltyTiers, async (loyaltyTier) => {
      const hasTier = (customer: CustomerDocument) =>
        customer.loyaltyTier?._id.equals(loyaltyTier._id);
      await this.ordablePromotionsService.createOrdablePromotion(
        store,
        ordableCustomers.filter(hasTier).map(toOrdableId),
        loyaltyTier,
      );
    });

    const coupons = await this.couponService.index({
      companyId: store.companyId,
    });

    await forEachAsync(coupons, async (coupon) => {
      const isEligible = (customer: CustomerDocument) =>
        coupon.loyaltyPointCost <= customer.loyaltyPoints;
      await this.ordablePromotionsService.createOrdablePromotion(
        store,
        ordableCustomers.filter(isEligible).map(toOrdableId),
        coupon,
      );
    });
  }

  async syncCustomerTier(
    customer: CustomerDocument,
    previousEmbeddedTier?: EmbeddedTierDto,
  ): Promise<void> {
    if (!this.isSyncable(customer)) return;

    const loyaltyTier = customer.loyaltyTier
      ? await this.loyaltyTierService.findById(customer.loyaltyTier?._id)
      : null;

    if (previousEmbeddedTier) {
      const previousTier = await this.loyaltyTierService.findById(
        previousEmbeddedTier._id,
      );

      await this.removeFromPreviousTier(
        customer,
        previousTier,
        loyaltyTier?.freeDelivery,
      );
    }

    if (loyaltyTier) await this.addToNewTier(customer, loyaltyTier);
  }

  private async removeFromPreviousTier(
    customer: CustomerDocument,
    loyaltyTier: LoyaltyTierDocument,
    keepFreeDelivery: boolean,
  ) {
    await this.ordableStoresService.forEachStore(
      loyaltyTier,
      async (store) =>
        await this.ordablePromotionsService.removeCustomerFromPromotion(
          store,
          customer.ordableStores?.[store._id.toHexString()]?.ordableId,
          loyaltyTier,
        ),
    );

    if (!keepFreeDelivery && loyaltyTier.freeDelivery)
      await this.ordableFreeDeliveryService.removeFreeDelivery(customer);
  }

  private async addToNewTier(
    customer: CustomerDocument,
    loyaltyTier: LoyaltyTierDocument,
  ) {
    await this.ordableStoresService.forEachStore(
      loyaltyTier,
      async (store) =>
        await this.ordablePromotionsService.addCustomerToPromotion(
          store,
          customer.ordableStores?.[store._id.toHexString()]?.ordableId,
          loyaltyTier,
        ),
    );

    if (loyaltyTier.freeDelivery)
      await this.ordableFreeDeliveryService.addFreeDelivery(customer);
  }

  async syncCustomerCoupons(customer: CustomerDocument): Promise<void> {
    if (!this.isSyncable(customer)) return;

    const coupons = await this.couponService.index({
      companyId: customer.company,
    });

    await this.unassignCoupons(coupons, customer);
    await this.assignCoupons(coupons, customer);
  }

  private async unassignCoupons(
    coupons: CouponDocument[],
    customer: CustomerDocument,
  ) {
    const couponToUnassign = coupons.filter(
      (coupon) => coupon.loyaltyPointCost > customer.loyaltyPoints,
    );

    const unassignCoupon = async (coupon: CouponDocument) =>
      await this.ordableStoresService.forEachStore(
        coupon,
        async (store) =>
          await this.ordablePromotionsService.removeCustomerFromPromotion(
            store,
            customer.ordableStores?.[store._id.toHexString()]?.ordableId,
            coupon,
          ),
      );

    if (couponToUnassign && couponToUnassign.length > 0) {
      await Promise.all(couponToUnassign.map(unassignCoupon));
    }
  }

  private isSyncable(customer: CustomerDocument): boolean {
    if (customer.loyaltyStatus !== LoyaltyStatus.MEMBER) return false;
    if (!customer.ordableStores) {
      this.logger.warn(
        `Must register customer ${customer._id} before syncing promotions [or brand has no Ordable stores]`,
      );
      return false;
    }
    return true;
  }

  private async assignCoupons(
    coupons: CouponDocument[],
    customer: CustomerDocument,
  ) {
    const couponToAssign = coupons.filter(
      (coupon) => coupon.loyaltyPointCost <= customer.loyaltyPoints,
    );

    const assignCoupon = async (coupon: CouponDocument) =>
      await this.ordableStoresService.forEachStore(
        coupon,
        async (store) =>
          await this.ordablePromotionsService.addCustomerToPromotion(
            store,
            customer.ordableStores?.[store._id.toHexString()]?.ordableId,
            coupon,
          ),
      );

    if (couponToAssign && couponToAssign.length > 0) {
      await Promise.all(couponToAssign.map(assignCoupon));
    }
  }

  async syncCustomerBenefits(
    customer: CustomerDocument,
  ): Promise<CustomerDocument> {
    if (!customer.earnedBenefits || customer.earnedBenefits.length === 0)
      return;

    const benefitsToCreate = customer.earnedBenefits
      .filter((benefit) => !benefit.ordableStores)
      .filter((benefit) => benefit.appliedTo !== BenefitAppliedTo.MENU_ITEM);

    if (benefitsToCreate.length > 0) {
      customer = await this.ordableStoresService.forEachStore(
        customer,
        (store) => this.createBenefits(store, customer, benefitsToCreate),
      );
    }

    if (!customer.usedBenefits || customer.usedBenefits.length === 0) return;

    const benefitsToDelete = customer.usedBenefits.filter(
      (benefit) => benefit.ordableStores,
    );
    if (benefitsToDelete.length > 0)
      customer = await this.ordableStoresService.forEachStore(
        customer,
        (store) => this.deleteBenefits(store, customer, benefitsToDelete),
      );

    return customer;
  }

  async syncCustomerRewards(
    customer: CustomerDocument,
  ): Promise<CustomerDocument> {
    if (!customer?.rewards || customer.rewards.length === 0) return customer;

    const rewardsToCreate = customer.rewards
      .filter((reward) => !reward.ordableStores)
      .filter((reward) => reward.benefit !== PunchCardBenefit.MENU_ITEM); // Waiting on ordable to fix bugs withh menu item promotions
    if (rewardsToCreate.length > 0) {
      customer = await this.ordableStoresService.forEachStore(
        customer,
        (store) => this.createRewards(store, customer, rewardsToCreate),
      );
    }

    if (!customer.usedRewards || customer.usedRewards.length === 0)
      return customer;
    const rewardsToDelete = customer.usedRewards.filter(
      (reward) => reward.ordableStores,
    );
    if (rewardsToDelete.length > 0)
      customer = await this.ordableStoresService.forEachStore(
        customer,
        (store) => this.deleteRewards(store, customer, rewardsToDelete),
      );

    return customer;
  }

  private async createRewards(
    store: StoreDocument,
    customer: CustomerDocument,
    rewardsToCreate: EarnedReward[],
  ): Promise<void> {
    const ordableId =
      customer.ordableStores?.[store._id.toHexString()]?.ordableId;
    if (!ordableId)
      return this.logger.warn(
        `Cannot create rewards for customer ${customer._id} without ordableId`,
      );

    const createReward = async (
      reward: EarnedReward,
    ): Promise<EarnedReward> => {
      const createdPromotionInfo: PromotionOrdableInfo =
        await this.ordablePromotionsService.createOrdablePromotion(
          store,
          [ordableId],
          reward,
        );
      return {
        ...reward,
        ordableStores: {
          ...(reward.ordableStores || {}),
          [store._id.toHexString()]: {
            ordableId: createdPromotionInfo.ordableId,
          },
        },
      };
    };

    const createdRewards = await mapAsync(rewardsToCreate, createReward);
    await this.customerWriteService.updateRewards(customer, createdRewards);
  }

  private async deleteRewards(
    store: StoreDocument,
    customer: CustomerDocument,
    rewardsToDelete: UsedReward[],
  ): Promise<void> {
    const deleteReward = async (reward: EarnedReward) => {
      await this.ordablePromotionsService.deleteOrdablePromotion(
        store,
        reward.ordableStores?.[store._id.toHexString()]?.ordableId,
      );
      return {
        ...reward,
        ordableStores: {
          ...(reward.ordableStores || {}),
          [store._id.toHexString()]: { ordableId: null },
        },
      };
    };

    const deletedRewards = await mapAsync(rewardsToDelete, deleteReward);
    await this.customerWriteService.updateRewards(customer, deletedRewards);
  }

  private async createBenefits(
    store: StoreDocument,
    customer: CustomerDocument,
    benefitsToCreate: CustomerEarnedBenefit[],
  ): Promise<void> {
    const ordableId =
      customer.ordableStores?.[store._id.toHexString()]?.ordableId;
    if (!ordableId)
      return this.logger.warn(
        `Cannot create benefits for customer ${customer._id} without ordableId`,
      );

    const createBenefit = async (benefit: CustomerEarnedBenefit) => {
      const createdPromotionInfo: PromotionOrdableInfo =
        await this.ordablePromotionsService.createOrdablePromotion(
          store,
          [ordableId],
          benefit,
        );
      return {
        ...benefit,
        ordableStores: {
          ...(benefit.ordableStores || {}),
          [store._id.toHexString()]: {
            ordableId: createdPromotionInfo?.ordableId,
          },
        },
      };
    };

    const createdBenefits = await mapAsync(benefitsToCreate, createBenefit);

    const newCustomerBenefits = customer.earnedBenefits.map(
      (benefit) =>
        createdBenefits.find((created) => created._id.equals(benefit._id)) ||
        benefit,
    );

    await this.customerWriteService.updateBenefits(
      customer,
      newCustomerBenefits,
    );
  }

  private async deleteBenefits(
    store: StoreDocument,
    customer: CustomerDocument,
    benefitsToDelete: CustomerUsedBenefit[],
  ): Promise<void> {
    const deleteBenefit = async (benefit: CustomerUsedBenefit) => {
      await this.ordablePromotionsService.deleteOrdablePromotion(
        store,
        benefit.ordableStores?.[store._id.toHexString()]?.ordableId,
      );
      return {
        ...benefit,
        ordableStores: {
          ...(benefit.ordableStores || {}),
          [store._id.toHexString()]: { ordableId: null },
        },
      };
    };

    await mapAsync(benefitsToDelete, deleteBenefit);
  }
}
