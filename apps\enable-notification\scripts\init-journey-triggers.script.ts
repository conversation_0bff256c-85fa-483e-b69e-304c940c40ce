// EBL-4107 [CRM] Journey CRUD
// add canBeEnrollmentTrigger and canBeReferenceTrigger to existing triggers
// See: https://e-butler.atlassian.net/wiki/spaces/EPW/pages/215252993/TD+ECRM-331+Engagement+Automations#Triggers%2C-Variables%2C-Values-and-References-Requirements

db.triggers.updateMany(
  {
    name: {
      $in: [
        '[CUSTOMER] ON_LOYALTY_PROGRAM_REGISTRATION',
        '[ORDER] ON_AGGREGATOR_ORDER',
        '[ORDER] ON_AGGREGATOR_ORDER_NON_LOYALTY_MEMBERS',
      ],
    },
  },
  { $set: { canBeEnrollmentTrigger: true } },
);

db.triggers.updateMany(
  {
    name: {
      $in: [
        '[CUSTOMER] ON_LOYALTY_PROGRAM_REGISTRATION',
        '[ORDER] ON_ORDER_CREATED',
        '[ORDE<PERSON>] ON_ORDER_COMPLETED',
        '[ORDER] ON_AGGREGATOR_ORDER',
        '[ORDER] ON_AGGREGATOR_ORDER_NON_LOYALTY_MEMBERS',
      ],
    },
  },
  { $set: { canBeReferenceTrigger: true } },
);
