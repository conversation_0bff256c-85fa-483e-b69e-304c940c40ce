import {
  CurrentUser,
  OrderDocument,
  OrderLogActionEnum,
  OrderStatusEnum,
  OrderTransitionTrigger,
  responseCode,
} from '@app/shared-stuff';
import { BadRequestException, Inject } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { OrderLogServiceInterface } from '../../interfaces/order-log.service.interface';
import { OrderNotificationService } from '../../order-notification/order-notification.service';
import { OrderReversionService } from '../../order-reversion/order-reversion.service';
import { OrderStatusServiceInterface } from '../order-status.interface';

export class CanceledOrderService implements OrderStatusServiceInterface {
  transitionalStatuses = [
    OrderStatusEnum.UNASSIGNED,
    OrderStatusEnum.PENDING,
    OrderStatusEnum.SCHEDULED,
    OrderStatusEnum.PREPARING,
    OrderStatusEnum.PENDING_PICKUP,
    OrderStatusEnum.IN_ROUTE,
    OrderStatusEnum.COMPLETED,
  ];
  transitionalTrigger = [
    OrderTransitionTrigger.CANCEL,
    OrderTransitionTrigger.DELIVERECT_STATUS_UPDATE,
  ];

  constructor(
    @Inject('OrderLogServiceInterface')
    private readonly orderLogService: OrderLogServiceInterface,
    private orderNotificationService: OrderNotificationService,
    private eventEmitter: EventEmitter2,
    private readonly orderReversionService: OrderReversionService,
  ) {}

  validateStatus(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
  ): boolean {
    return (
      this.ValidateTransition(order, orderTransitionTrigger) &&
      this.validatePreCondition(order)
    );
  }

  async applyPostFunction(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
    oldStatus: OrderStatusEnum,
    user: CurrentUser,
  ) {
    this.eventEmitter.emit('order.cancelled', order);
    await this.orderNotificationService.onOrderCancelled(order);
    order.delayTag = undefined;
    await order.save();

    await this.orderReversionService.cancelCustomerOrder(order);

    await this.orderLogService.saveOrderLog(
      order,
      { oldStatus: oldStatus },
      { newStatus: OrderStatusEnum.CANCELED, trigger: orderTransitionTrigger },
      OrderLogActionEnum.ON_ORDER_CANCELED,
      user,
    );
  }

  private ValidateTransition(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
  ): boolean {
    if (
      this.transitionalTrigger.includes(orderTransitionTrigger) &&
      this.transitionalStatuses.includes(order.status)
    )
      return true;
    throw new BadRequestException(
      "Can't Change From " + order.status + ' To Canceled',
      responseCode.STATUS_NOT_VALID.toString(),
    );
  }

  private validatePreCondition(order: OrderDocument): boolean {
    return true;
  }
}
