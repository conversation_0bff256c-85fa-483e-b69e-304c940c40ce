import { OrderStatusEnum } from '@app/shared-stuff';
import { deliverectOrderStatus } from './enums/deliverect-order-status.enum';

export const menuTypeMapping = {
  0: 'pickupAndDelivery',
  1: 'delivery',
  2: 'pickup',
  3: 'eatIn',
};

export const deliverectOrderStatusMapping: Map<
  deliverectOrderStatus,
  OrderStatusEnum
> = new Map([
  [deliverectOrderStatus.RECEIVED, OrderStatusEnum.UNASSIGNED],
  [deliverectOrderStatus.SCHEDULED, OrderStatusEnum.SCHEDULED],
  [deliverectOrderStatus.PREPARING, OrderStatusEnum.PREPARING],
  [deliverectOrderStatus.READY_FOR_PICKUP, OrderStatusEnum.PENDING_PICKUP],
  [deliverectOrderStatus.IN_DELIVERY, OrderStatusEnum.IN_ROUTE],
  [deliverectOrderStatus.FINALIZED, OrderStatusEnum.COMPLETED],
  [deliverectOrderStatus.CANCELED, OrderStatusEnum.CANCELED],
]);
