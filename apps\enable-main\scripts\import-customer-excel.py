# Configure the variables below, then run:
# python import-customer-excel.py KuludCustomers.csv
# (You may need to `python -m pip install pandas` first)

# BASE_URL = "http://localhost:3004"
BASE_URL = "https://backend.enable.tech"
API_KEY = ""

import pandas as pd
import requests
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed

file_path = sys.argv[1]
customer_api = f"{BASE_URL}/customer"
headers = {
    "Content-Type": "application/json",
    "saasapikey": API_KEY,
}


def post_data_to_api(row):
    customer_payload = {
        "first_name": row["Customer Name"],
        "phone": row["Phone Number"],
        "country_code": "+974",
    }

    customer_response = requests.post(
        customer_api,
        json=customer_payload,
        headers=headers,
    )

    if customer_response.status_code != 200:
        print(
            f"Failed to create customer: [{row['Phone Number']}] {row['Customer Name']}"
        )
        print(customer_response.json())
    # else:
    #     print(f"Created customer for phone: {row['Phone Number']}")


df = pd.read_csv(file_path, encoding="utf-8", encoding_errors="ignore")

with ThreadPoolExecutor(max_workers=3) as executor:
    futures = [executor.submit(post_data_to_api, row) for _, row in df.iterrows()]

    for future in as_completed(futures):
        try:
            future.result()
        except Exception as e:
            print(e)
