import { Module } from '@nestjs/common';
import { BigCommerceController } from './controllers/big-commerce.controller';
import { BigCommerceService } from './services/big-commerce.service';
import { OrderModule } from '../../order/order.module';
import { BigCommerceServiceInterface } from './services/big-commerce.service.interface';
import { BigCommerceListener } from './listeners/big-commerce.listener';
import { SharedStuffModule } from '@app/shared-stuff';
import { IntegrationLogModule } from '../integration-log/integration-log.module';

@Module({
  imports: [OrderModule, SharedStuffModule, IntegrationLogModule],
  controllers: [BigCommerceController],
  providers: [
    BigCommerceListener,
    {
      provide: BigCommerceServiceInterface,
      useClass: BigCommerceService,
    },
  ],
})
export class BigCommerceModule {}
