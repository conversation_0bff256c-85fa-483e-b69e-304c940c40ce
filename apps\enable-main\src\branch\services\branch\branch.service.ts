import {
  Branch,
  BranchDocument,
  BranchToCreate,
  BranchToIndex,
  BranchWithId,
  Brand,
  CollectionName,
  CompanyDocument,
  CreateCompanyDto,
  CreateDeliveryConfigurationDto,
  CurrentUser,
  DeliveryMethod,
  EmbeddedBrandDto,
  ImageToCreate,
  LogError,
  responseCode,
  SavedLocationAddressType,
  SavedLocationType,
  UpdateBranchDto,
  UpdateBusyBranchDto,
  UpdateDeliveryConfigurationDto,
  UserBranchesToAssign,
} from '@app/shared-stuff';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import {
  FilterQuery,
  isObjectIdOrHexString,
  Model,
  PipelineStage,
  Types,
} from 'mongoose';
import * as randomstring from 'randomstring';
import { CompanyService } from '../../../company/services/company/company.service';
import { DeliveryConfigurationDocument } from '../../../delivery/models/delivery-configuration.model';
import { DeliveryConfigurationServiceInterface } from '../../../delivery/services/delivery-configuration/delivery-configuration.service.interface';
import { SavedLocationService } from '../../../location/services/saved-location/saved-location.service';
import { HelperService } from '../../../shared/services/helper/helper.service';
import { UsersToAssignToBranch } from '../../../user/dto/user.dto';
import { UserService } from '../../../user/services/user/user.service';
import { ImageService } from './../../../shared/services/image/image.service';
import { UserDocument } from './../../../user/models/user.model';

@Injectable()
export class BranchService {
  sort_types = {
    name_desc: { name: -1 },
    name_asc: { name: 1 },
    revenue_asc: { total_revenue: 1 },
    revenue_desc: { total_revenue: -1 },
    company_name_asc: { company_name: 1 },
    company_name_desc: { company_name: -1 },
    date_created: { createdAt: 1 },
    name: { name: 1 },
    number_of_users: { number_of_users: -1 },
  };

  availableFields = [
    'status',
    'name',
    'acronym',
    'email',
    'phone',
    'company_name',
    'area',
    'city',
    'brands',
    'radius',
  ];

  constructor(
    @InjectModel(CollectionName.BRANCH)
    private branchModel: Model<BranchDocument>,
    private companyService: CompanyService,
    private usersService: UserService,
    private imageService: ImageService,
    private savedLocationService: SavedLocationService,
    private eventEmitter: EventEmitter2,
    @Inject(DeliveryConfigurationServiceInterface)
    private readonly deliveryConfigurationService: DeliveryConfigurationServiceInterface,
    private helperService: HelperService,
  ) {}

  async index(branchToIndex: BranchToIndex): Promise<BranchDocument[]> {
    const match: unknown = {};

    if (Array.isArray(branchToIndex.branches)) {
      // the user is not assigned to any branch
      if (branchToIndex.branches.length === 0) {
        return [];
      } else if (
        // the user is assigned to branches but,
        // searching for a specific branch that doesn't exist in those branches
        branchToIndex.branch &&
        !branchToIndex.branches.find((x) =>
          x.equals(new Types.ObjectId(branchToIndex.branch)),
        )
      ) {
        return [];
        // the user is assigned to branches and
        // is searching for a branch that is included in those branches
      } else if (
        branchToIndex.branch &&
        branchToIndex.branches.find((x) =>
          x.equals(new Types.ObjectId(branchToIndex.branch)),
        )
      ) {
        match['_id'] = new Types.ObjectId(branchToIndex.branch);
        // the user is assigned to branches
        // but is not searching for a specific branch using branch id
      } else if (!branchToIndex.branch) {
        match['_id'] = {
          $in: branchToIndex.branches.map((id) => new Types.ObjectId(id)),
        };
      }
    }
    if (branchToIndex.company) {
      const company = await this.companyService.get_details(
        branchToIndex.company,
      );
      if (!company)
        throw {
          code: responseCode.MISSING_DATA,
          statusCode: 422,
          message: 'company not found!',
        };
      match['company'] = new Types.ObjectId(branchToIndex.company) as any;
    }
    if (branchToIndex.brandId) {
      branchToIndex.brandId = new Types.ObjectId(branchToIndex.brandId);
      match['brands'] = {
        $elemMatch: { _id: branchToIndex.brandId },
      };
    }

    if (branchToIndex.brandBranchPhoneNumber) {
      match['brands'] = {
        $elemMatch: { phoneNumber: branchToIndex.brandBranchPhoneNumber },
      };
    }

    if (branchToIndex.month) {
      match['month'] = branchToIndex.month;
    }

    if (branchToIndex.deliveryArea) {
      match['deliveryAreas'] = {
        $elemMatch: { $eq: branchToIndex.deliveryArea },
      };
    }

    /**
     * Available search fields:
     *    [ "status", "name", "acronym",
     *        "email", "phone", "company_name",
     *        "area", "city" ]
     * special search key is "all",
     *
     */
    if (branchToIndex.search_type && branchToIndex.search_key) {
      if (
        this.availableFields.includes(branchToIndex.search_type) &&
        branchToIndex.search_key
      ) {
        match[branchToIndex.search_type] = {
          $regex: branchToIndex.search_key,
          $options: 'i',
        };
      } else if (branchToIndex.search_type === 'all') {
        match['$or'] = this.availableFields.map((field) => {
          return {
            [field]: {
              $regex: branchToIndex.search_key,
              $options: 'i',
            },
          };
        });
      }
    }

    match['deletedAt'] = { $eq: null };
    const pipeline: PipelineStage[] = [
      { $match: match },
      ...this.createSortStage(branchToIndex),
      ...(Number(branchToIndex.offset) || Number(branchToIndex.offset) == 0
        ? [
            {
              $skip: Number(branchToIndex.offset) * Number(branchToIndex.limit),
            },
          ]
        : []),
      ...(Number(branchToIndex.limit)
        ? [
            {
              $limit: Number(branchToIndex.limit),
            },
          ]
        : []),
      {
        $lookup: {
          from: 'saved_locations',
          let: { locationId: '$locationId' },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ['$_id', '$$locationId'] },
              },
            },
          ],
          as: 'location',
        },
      },
      {
        $unwind: { path: '$location', preserveNullAndEmptyArrays: true },
      },
    ];

    const branches = await this.branchModel.aggregate(pipeline);

    return branches;
  }

  async findById(id: Types.ObjectId): Promise<BranchDocument> {
    return this.branchModel.findById(id);
  }

  async create(branchToCreate: BranchToCreate) {
    this.helperService.checkDeliveryOptionExist(
      branchToCreate.deliveryConfiguration.usingBranchDrivers,
      branchToCreate.deliveryConfiguration.usingCompanyDrivers,
      branchToCreate.deliveryConfiguration.usingEbDelivery,
      branchToCreate.deliveryConfiguration.usingThirdParty,
    );

    if (branchToCreate.image) {
      branchToCreate.image.for = 'branch';
      branchToCreate.image = (await this.imageService.uploadImage(
        branchToCreate.image,
      )) as any;
    }

    if (branchToCreate.location) {
      const location = await this.savedLocationService.create(
        branchToCreate.location,
      );
      branchToCreate['locationId'] = location._id;
      branchToCreate['area'] = location.area;
      branchToCreate['city'] = location.city;
    }

    if (branchToCreate.brands) {
      this.checkPhoneNumberAvailability(branchToCreate.brands);
    }
    // TODO Delete this line after investigation
    this.convertBrandIdsToObjectIds(branchToCreate.brands);

    // creating a branch instance
    const createdBranch = new this.branchModel(branchToCreate);
    if (branchToCreate.khuludBranchId) {
      createdBranch.integrationInfo = {
        ...(createdBranch.integrationInfo ?? {}),
        khuludBranchId: branchToCreate.khuludBranchId,
      };
    }
    //TODO: if we have array of branches in brands -> add branch to brand function

    const company = await this.companyService.add_branch_to_company(
      createdBranch._id,
      createdBranch.company,
    );

    createdBranch.company_name = company.name;

    createdBranch.number_of_users = 0;
    createdBranch.callCenterConfig
      ? (createdBranch.callCenterConfig.webStoreLink =
          createdBranch.externalLinks?.webStoreLink)
      : undefined;

    //Adding users to branch
    createdBranch.users = [] as any;
    const usersSet = new Set(branchToCreate.users);
    const usersArray = Array.from(usersSet);

    for (let i = 0; i < usersArray.length; i++) {
      const selectedUSer = await this.usersService.get_details(usersArray[i]);
      // adding the new branch id into user's branches
      selectedUSer.branches.push(createdBranch._id);
      // removing duplicates
      const branchesSet = new Set(selectedUSer.branches);
      // converting branches set to an array
      selectedUSer.branches = Array.from(branchesSet);

      selectedUSer.company = new Types.ObjectId(createdBranch.company);
      //TODO: send notification to this user that he has been assign to branch
      await selectedUSer.save();

      createdBranch.users.push(selectedUSer._id);
      createdBranch.number_of_users += 1;
    }
    // in the model is a regular object
    createdBranch.createdBy = branchToCreate.current_user;
    await createdBranch.save();
    this.eventEmitter.emit('branch.created', createdBranch);
    branchToCreate.deliveryConfiguration.branchId = createdBranch._id;
    if (branchToCreate.isFollowingCompanyDeliveryConfiguration == false)
      await this.createDeliveryConfiguration(
        branchToCreate.deliveryConfiguration,
      );
    return createdBranch;
  }

  async update(updateBranchDto: UpdateBranchDto, company_id: string) {
    const allowedDirectUpdates = [
      'name',
      'acronym',
      'email',
      'phone',
      'status',
      'image',
      'senderId',
      'internalName',
      'callCenterConfig',
      'externalLinks',
      'deliveryAreas',
      'adlerOutletId',
      'mrDeliveryVendorId',
      'locationId',
      'area',
      'city',
      'website',
      'brands',
      'radius',
      'integratorChannelConfig',
    ];
    const existedBranch = await this.branchModel.findById({
      _id: updateBranchDto._id,
    });

    // checking if the branch exists
    if (!existedBranch) {
      throw {
        code: responseCode.BAD_REQUEST,
        statusCode: 400,
        message: 'You need to provide a valid and existing branch id',
      };
    }

    // checking if the company of current user, is the same of the branch to update
    if (
      company_id &&
      existedBranch.company.toString() !== company_id.toString()
    ) {
      throw {
        code: responseCode.BAD_REQUEST,
        statusCode: 400,
        message: "You don't have access to this company branches",
      };
    }

    if (updateBranchDto.image) {
      const imageToCreate: ImageToCreate = {
        name: updateBranchDto.name || randomstring.generate(10),
        alt: updateBranchDto.image.alt,
        description: updateBranchDto.image.description,
        for: 'branch',
        base64: updateBranchDto.image.base64,
      };
      updateBranchDto['image'] = (await this.imageService.uploadImage(
        imageToCreate,
      )) as any;
    }

    if (updateBranchDto.location) {
      updateBranchDto.location['_id'] = new Types.ObjectId(
        updateBranchDto.location['_id'],
      );
      const location = await this.savedLocationService.update(
        updateBranchDto.location,
      );
      updateBranchDto['locationId'] = location._id;
      updateBranchDto['area'] = location.area;
      updateBranchDto['city'] = location.city;
    } else if (updateBranchDto.locationToCreate) {
      const location = await this.savedLocationService.create(
        updateBranchDto.locationToCreate,
      );
      updateBranchDto['locationId'] = location._id;
      updateBranchDto['area'] = location.area;
      updateBranchDto['city'] = location.city;
    }

    /* applying basic direct updates */
    for (let i = 0; i < allowedDirectUpdates.length; i++) {
      if (updateBranchDto[allowedDirectUpdates[i]]) {
        existedBranch[allowedDirectUpdates[i]] =
          updateBranchDto[allowedDirectUpdates[i]];
      } else {
      }
    }
    if (updateBranchDto.khuludBranchId) {
      existedBranch.integrationInfo = {
        ...(existedBranch.integrationInfo ?? {}),
        khuludBranchId: updateBranchDto.khuludBranchId,
      };
    }

    // assigning current user as the last updater to the branch
    existedBranch.updatedBy = updateBranchDto.current_user;

    /* updating user list */
    // creating a set from the newly assigned users
    // to ensure unique values
    const usersSet = new Set(updateBranchDto.users);
    // assigning an array form of the set into the updatedBranch document
    existedBranch.users = Array.from(usersSet).map(
      (userId) => new Types.ObjectId(userId),
    );

    // updating the number of the users in the branch
    existedBranch.number_of_users = existedBranch.users.length;

    // removing this branch from all previous users
    await this.removeBranchFromAllUsers(existedBranch);

    // looping over each user and assigning the updated branch to him
    for (let i = 0; i < existedBranch.users.length; i++) {
      // fetching each user details
      const selectedUSer = await this.usersService.get_details(
        existedBranch.users[i],
      );
      // checking if the branch is already assigned or not
      if (selectedUSer.branches.indexOf(existedBranch._id) == -1) {
        // if not, assign it
        selectedUSer.branches.push(existedBranch._id);
      }
      // assigning the company id into the user's document
      selectedUSer.company = new Types.ObjectId(existedBranch.company);
      //TODO: send notification to this user that he has been assign to branch
      await selectedUSer.save();
    }

    existedBranch.callCenterConfig
      ? (existedBranch.callCenterConfig.webStoreLink =
          existedBranch.externalLinks?.webStoreLink)
      : undefined;
    await this.handleDeliveryConfigurationLogic(
      updateBranchDto.deliveryConfiguration,
      updateBranchDto.isFollowingCompanyDeliveryConfiguration,
      new Types.ObjectId(updateBranchDto._id),
    );
    existedBranch.isFollowingCompanyDeliveryConfiguration =
      updateBranchDto.isFollowingCompanyDeliveryConfiguration;
    await existedBranch.save();
    this.eventEmitter.emit('branch.updated', existedBranch);
    return existedBranch;
  }

  async delete(id: string, currentUser: CurrentUser) {
    const deletedBranch = await this.branchModel.findOne({
      _id: id,
    });

    if (!deletedBranch) {
      throw {
        code: responseCode.UNAUTHORIZED,
        statusCode: 401,
        message: "You don't have access to this branch",
      };
    }
    if (await this.isDefaultBranch(deletedBranch._id))
      throw new BadRequestException(
        'Default branch cannot be deleted. Set a new default first.',
        responseCode.FAIL_TO_DELETE.toString(),
      );

    deletedBranch.deletedBy = currentUser;
    deletedBranch.deletedAt = moment().utc().toDate();

    await this.usersService.deleteBranchFromUsers(deletedBranch._id);
    await this.companyService.deleteBranchFromCompany(deletedBranch._id);

    await deletedBranch.save();
    return deletedBranch;
  }

  private async isDefaultBranch(_id: Types.ObjectId): Promise<boolean> {
    return !!(await this.companyService.findByDefaultBranchId(_id));
  }

  private getBranchFilters(
    id: string | Types.ObjectId,
    companyId?: Types.ObjectId,
  ) {
    const filters = {
      ...(companyId ? { company: companyId } : {}),
      $or: [
        { deliverectLocationId: id },
        { adlerOutletId: id },
        { phone: id },
        { 'integrationInfo.khuludBranchId': id.toString() },
      ],
    } as any;

    if (isObjectIdOrHexString(id)) {
      filters.$or.push({ _id: id });
    }

    return filters;
  }

  async get_details(
    id: string | Types.ObjectId,
    companyId?: Types.ObjectId,
  ): Promise<BranchWithId> {
    const selectedBranch = await this.branchModel
      .findOne(this.getBranchFilters(id, companyId))
      .populate('company', '_id name phone email')
      .populate('location')
      .populate({
        path: 'users',
        select: 'name _id email phone roles',
        populate: { path: 'roles', select: 'name _id' },
      });

    let deliveryConfiguration: DeliveryConfigurationDocument;
    if (selectedBranch) {
      deliveryConfiguration =
        await this.getExistedDeliveryConfigurationByBranchId(
          new Types.ObjectId(selectedBranch._id),
        );
      const jsonSelectedBranch: any = selectedBranch.toJSON();

      jsonSelectedBranch.deliveryConfiguration = deliveryConfiguration;
      return jsonSelectedBranch;
    }
  }

  async findByPhoneNumber(phoneNumber: string): Promise<BranchDocument[]> {
    return await this.branchModel.find({
      $or: [
        { phone: { $eq: phoneNumber } },
        {
          brands: {
            $elemMatch: {
              phoneNumber: { $eq: phoneNumber },
            },
          },
        },
      ],
    });
  }

  async get_total_branches(branchToIndex: BranchToIndex) {
    delete branchToIndex.offset;
    delete branchToIndex.limit;
    const branches = await this.index(branchToIndex);
    return branches.length;
  }

  // assigns a group of users into a branch
  async assignUsersIntoABranch(
    usersBranchToAssign: UsersToAssignToBranch,
    company_id: string,
  ) {
    const branch = await this.branchModel.findOne({
      _id: usersBranchToAssign.branch_id,
      company: company_id,
    });

    if (!branch) {
      throw {
        code: responseCode.MISSING_DATA,
        statusCode: 404,
        message: "you don't have access to this branch",
      };
    }

    /* updating user list */
    const usersSet = new Set(usersBranchToAssign.users);
    branch.users = Array.from(usersSet).map(
      (userId) => new Types.ObjectId(userId),
    );
    branch.number_of_users = branch.users.length;

    await this.removeBranchFromAllUsers(branch);
    for (let i = 0; i < branch.users.length; i++) {
      const currentUser = await this.usersService.get_details(branch.users[i]);
      if (currentUser.branches.indexOf(branch._id) == -1) {
        currentUser.branches.push(branch._id);
      }
      currentUser.company = new Types.ObjectId(branch.company);
      await currentUser.save();
    }

    await branch.save();
    return branch;
  }

  // assign multiple branches to a user
  async assignBranchesToUser(userBranchesToAssign: UserBranchesToAssign) {
    if (!userBranchesToAssign.user) {
      throw {
        code: responseCode.BAD_REQUEST,
        statusCode: 400,
        message: 'You need to provide a user',
      };
    }

    const user = await this.usersService.get_details(
      userBranchesToAssign.user.toString(),
    );
    await this.removeUserFromAllBranches(user);

    const uniqueBranchesIds = [...new Set(userBranchesToAssign.branches)].map(
      (id) => new Types.ObjectId(id),
    );

    user.branches = uniqueBranchesIds;
    await this.addUserToBranches(user._id, uniqueBranchesIds);
    await this.usersService.updateBranches(user._id, user.branches);
    return user;
  }

  async addUserToBranches(userId: Types.ObjectId, branchIds: Types.ObjectId[]) {
    return this.branchModel.updateMany(
      { _id: { $in: branchIds } },
      { $addToSet: { users: userId } },
    );
  }

  @OnEvent('company.createDefaultBranch')
  @LogError()
  async createDefaultBranch(data: any) {
    const company = data['company'] as CompanyDocument,
      user = data['user'] as UserDocument,
      companyToCreate = data['companyToCreate'] as CreateCompanyDto;

    const defaultBranch = await this.create({
      name: company.name,
      acronym: company.acronym,
      users: [user._id],
      senderId: company['senderId'],
      internalName: company.name,
      image: companyToCreate.image,
      deliveryAreas: ['Doha'],
      adlerOutletId: undefined,
      brands: [],
      location: {
        addressType: SavedLocationAddressType.BRANCH,
        area: 'Doha',
        city: 'Doha',
        country: 'Qatar',
        additionalInfo: '',
        buildingName: 'Doha',
        buildingNumber: 66,
        zoneNumber: 6,
        streetNumber: 110,
        customerId: undefined,
        latitude: 11111,
        longitude: 11111,
        nickname: '',
        floorNumber: 0,
        nearestLandmark: '',
        pinLink: '',
        streetName: '',
        type: SavedLocationType.PIN_LOCATION,
        unitNumber: 10,
      },
      company: company._id,
      current_user: company.createdBy,
      email: company.email,
      phone: company.phone,
      company_name: company.name,
      radius: 0,
      callCenterConfig: {
        deliveryFee: 0,
        deliveryTiming: '10:00 AM',
        currentlyDeliveringIn: 60,
        freeDeliveryAbove: 200,
        messages: [],
        minimumForDelivery: 100,
        openTiming: '02:00 AM',
        allowCallerToReceivePromotion: false,
        webStoreLink: company.website,
      },
      externalLinks: {
        feedbackLink: '',
        webStoreLink: '',
      },
      mrDeliveryVendorId: '',
      isFollowingCompanyDeliveryConfiguration: true,
      deliveryConfiguration: {
        usingBranchDrivers: true,
        defaultDeliveryMethod: DeliveryMethod.BRANCH_DRIVERS,
      },
    });
    this.eventEmitter.emit(
      'company.createDefaultBrand',
      company,
      defaultBranch,
    );
  }

  async getNearestBranch(
    lat: number,
    lng: number,
    company: Types.ObjectId,
    branchIds: Types.ObjectId[],
    brandId = '',
  ): Promise<BranchDocument> {
    const branches = await this.branchModel
      .find({
        company: company as any,
        deletedAt: null,
        isRestaurantBusy: false,
        ...(brandId
          ? { brands: { $elemMatch: { _id: new Types.ObjectId(brandId) } } }
          : {}),
      })
      .populate('location');

    let distance = 9999999999;
    let branch = undefined;

    for (let i = 0; i < branches.length; i++) {
      const currentBranch = branches[i];
      const branchExist = branchIds.find((x) => x.equals(currentBranch._id));

      if (
        (currentBranch.location &&
          currentBranch.location.latitude &&
          currentBranch.location.longitude &&
          !branchIds.length) ||
        branchExist
      ) {
        const currentDistance = this.calculateDistance(
          lat,
          lng,
          currentBranch.location ? currentBranch.location.latitude : 0,
          currentBranch.location ? currentBranch.location.longitude : 0,
        );
        if (currentDistance < distance) {
          distance = currentDistance;
          branch = branches[i];
        }
      }
    }

    return branch ? branch : branches[0];
  }

  async updateDeliverectChannelInfo(
    branchId: Types.ObjectId,
    channelName: string,
    channelId: string,
    channelLocationId: string,
  ) {
    return await this.branchModel.updateOne(
      { _id: branchId },
      {
        $set: {
          deliverectChannelId: channelId,
          deliverectChannelName: channelName,
          deliverectLocationId: new Types.ObjectId(channelLocationId),
        },
      },
    );
  }

  async decrementNumberOfPayments(id: string | Types.ObjectId) {
    return await this.branchModel.updateOne(this.getBranchFilters(id), {
      $inc: { number_of_payments: -1 },
    });
  }

  async setIsRestaurantBusy(
    id: string | Types.ObjectId,
    isRestaurantBusy: boolean,
  ) {
    return await this.branchModel.updateOne(this.getBranchFilters(id), {
      $set: { isRestaurantBusy },
    });
  }

  async setDeliverectLocationId(
    id: string | Types.ObjectId,
    deliverectLocationId: Types.ObjectId,
  ) {
    return await this.branchModel.updateOne(this.getBranchFilters(id), {
      $set: { deliverectLocationId },
    });
  }

  public async findByLocationRef(
    company: Types.ObjectId,
    locationRef: string,
  ): Promise<BranchDocument> {
    const filter: FilterQuery<Branch> = {
      company,
      $or: [
        {
          'integratorChannelConfig.restaurantManager.locationRef': locationRef,
        },
        ...(isObjectIdOrHexString(locationRef)
          ? [{ _id: new Types.ObjectId(locationRef) }]
          : []),
      ],
    };

    return await this.branchModel.findOne(filter);
  }

  @OnEvent('brands-updated')
  @LogError()
  async updateBrandsOnBranch(defaultBranch: BranchDocument) {
    await this.branchModel.updateOne(
      { _id: defaultBranch._id },
      { $set: { brands: defaultBranch.brands } },
    );
  }

  @OnEvent('on-Brand-Update')
  @LogError()
  async updateBrandOnBranch(updatedBrand: Brand, brandId: Types.ObjectId) {
    const branchToIndex = this.constructBranchToIndex(brandId);
    const branches = await this.index(branchToIndex);
    for (let i = 0; i < branches.length; i++) {
      for (let j = 0; j < branches[i].brands.length; j++) {
        if (branches[i].brands[j]._id == brandId) {
          branches[i].brands[j] = {
            ...branches[i].brands[j],
            image: updatedBrand.image,
            name: updatedBrand.name,
          };
        }
      }
      await this.branchModel.updateOne(
        { _id: branches[i]._id },
        { $set: { brands: branches[i].brands } },
      );
    }
  }

  async updateIsBusyBranch(
    updateBusyBranchDto: UpdateBusyBranchDto,
  ): Promise<void> {
    await this.branchModel.updateOne(
      {
        _id: new Types.ObjectId(updateBusyBranchDto.branchId),
      },
      { $set: { isRestaurantBusy: updateBusyBranchDto.isRestaurantBusy } },
    );
  }

  async findAllByIds(ids: Types.ObjectId[]): Promise<BranchDocument[]> {
    return this.branchModel.find({ _id: { $in: ids } });
  }

  private createSortStage(branchToIndex: BranchToIndex): PipelineStage[] {
    if (branchToIndex.sort_type && this.sort_types[branchToIndex.sort_type]) {
      return [{ $sort: this.sort_types[branchToIndex.sort_type] }];
    } else if (branchToIndex.sortBy) {
      const [key, order] = branchToIndex.sortBy.split(':');

      if (key !== 'name') {
        return [{ $sort: { [key]: order === 'desc' ? -1 : 1 } }];
      }

      // When sorting by name, we need to use internalName if it exists
      return [
        { $addFields: { sortBy: { $ifNull: ['$internalName', '$name'] } } },
        { $sort: { sortBy: order === 'desc' ? -1 : 1 } },
        { $project: { sortBy: 0 } },
      ];
    }

    return [{ $sort: { createdAt: -1 } }];
  }

  private async createDeliveryConfiguration(
    deliveryConfiguration: CreateDeliveryConfigurationDto,
  ) {
    await this.deliveryConfigurationService.create(deliveryConfiguration);
  }

  private convertBrandIdsToObjectIds(
    brands: EmbeddedBrandDto[],
  ): EmbeddedBrandDto[] {
    for (let i = 0; i < brands.length; i++) {
      brands[i]._id = new Types.ObjectId(brands[i]._id);
    }
    return brands;
  }

  private checkPhoneNumberAvailability(brands: EmbeddedBrandDto[]) {
    for (let i = 0; i < brands.length; i++) {
      if (!brands[i].phoneNumber)
        throw new BadRequestException(
          'Please Provide Phone Number for All Brands',
          responseCode.MISSING_DATA.toString(),
        );
    }
  }

  private async handleDeliveryConfigurationLogic(
    deliveryConfiguration: CreateDeliveryConfigurationDto,
    isFollowingCompanyDeliveryConfiguration: boolean,
    branchId: Types.ObjectId,
  ) {
    if (isFollowingCompanyDeliveryConfiguration == true)
      await this.deliveryConfigurationService.deleteByBranchId(branchId);
    else {
      await this.handleNotFollowingCompanyDeliveryConfigurationLogic(
        deliveryConfiguration,
        branchId,
      );
    }
  }

  private async handleNotFollowingCompanyDeliveryConfigurationLogic(
    deliveryConfiguration: CreateDeliveryConfigurationDto,
    branchId: Types.ObjectId,
  ) {
    const existedDeliveryConfiguration =
      await this.getExistedDeliveryConfigurationByBranchId(branchId);

    if (!existedDeliveryConfiguration) {
      deliveryConfiguration.branchId = branchId;
      await this.createDeliveryConfiguration(deliveryConfiguration);
    } else
      this.helperService.checkDeliveryOptionExist(
        deliveryConfiguration.usingBranchDrivers,
        deliveryConfiguration.usingCompanyDrivers,
        deliveryConfiguration.usingEbDelivery,
        deliveryConfiguration.usingThirdParty,
      );
    await this.updateDeliveryConfiguration({
      _id: existedDeliveryConfiguration._id,
      usingBranchDrivers: deliveryConfiguration.usingBranchDrivers,
      usingCompanyDrivers: deliveryConfiguration.usingCompanyDrivers,
      usingThirdParty: deliveryConfiguration.usingThirdParty,
      usingEbDelivery: deliveryConfiguration.usingEbDelivery,
      defaultDeliveryMethod: deliveryConfiguration.defaultDeliveryMethod,
      thirdPartyConfiguration: deliveryConfiguration.thirdPartyConfiguration,
      companyId: deliveryConfiguration.companyId,
      brandId: deliveryConfiguration.brandId,
      branchId: deliveryConfiguration.branchId,
    });
  }

  private async getExistedDeliveryConfigurationByBranchId(
    branchId: Types.ObjectId,
  ) {
    return await this.deliveryConfigurationService.findByBranchId(branchId);
  }

  private async updateDeliveryConfiguration(
    updateDeliveryConfigurationDto: UpdateDeliveryConfigurationDto,
  ) {
    await this.deliveryConfigurationService.update(
      updateDeliveryConfigurationDto,
    );
  }

  // deletes a branch from all users.
  private async removeBranchFromAllUsers(branch: BranchDocument) {
    // getting users who were assigned to this branch
    const users = await this.usersService.index({
      branch: branch._id,
    } as any);
    // deleting branch id from users' documents
    for (let i = 0; i < users.length; i++) {
      const index = users[i].branches.indexOf(branch._id);
      const branches = users[i].branches.slice();
      branches.splice(index, 1);
      users[i].branches = branches;
      await users[i].save();
    }
  }

  private async removeUserFromAllBranches(user: UserDocument) {
    await this.branchModel.updateMany({}, { $pull: { users: user._id } });
    await this.usersService.updateBranches(user._id, []);
  }

  private calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371; // km
    //has a problem with the .toRad() method below.
    const x1 = lat2 - lat1;
    const dLat = (x1 * Math.PI) / 180;
    const x2 = lng2 - lng1;
    const dLon = (x2 * Math.PI) / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos((lat1 * Math.PI) / 180) *
        Math.cos((lat2 * Math.PI) / 180) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const d = R * c;

    return d;
  }

  private constructBranchToIndex(brandId: Types.ObjectId) {
    return {
      offset: 0,
      limit: 1000000,
      currentUser: {},
      search_key: '',
      month: '',
      sort_type: '',
      sortBy: '',
      search_type: '',
      branch: null,
      company: '',
      branches: null,
      brandId: brandId,
      deliveryArea: '',
      brandBranchPhoneNumber: '',
    };
  }
}
