# Environment Variables for enable-main microservice
#GENERAL CONFIGURATION
KAFKA_BROKERS=
KAFKA_USERNAME=
KAFKA_PASSWORD=
KAFKA_CLIENT_ID=
KAFKA_SSL_ENABLED=
KAFKA_SSL_KEY_FILE=
KAFKA_SSL_CERT_FILE=
KAFKA_SASSL_ENABLED=
#AWS CREDENTIial
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_QUEUE_ENDPOINT=
activeMicroserviceProtocol='nats'
FRONTEND_BASE_URL='http://localhost:4200'


#NATS
NATS_SERVERS=nats://localhost:4222

#HS DELIVERY
HS_DELIVERY_TEAM_ID=

# PUBLIC PATH
PUBLIC_PATH=
# Pusher Configurations
PUSHER_APP_ID=
PUSHER_KEY=
PUSHER_SECRET=
PUSHER_CLUSTER=
PUSHER_TLS=
# Ebutler Base URL
EBUTLER_BASE=
EButlerAPIKey=
# ENVIROMENT
ENVIRONMENT=
#LOCATION PAGES
LOCATION_PAGE=
EXTERNAL_LOCATION_URL=
#DELIVERY TRACKING LINK
DELIVERY_TRACKING_LINK='https://public.testing.enable.tech/track/map/order'
ORDER_TRACKING_BASE=
# MENU ITEM PAGE configration
MENU_ITEM_PROMOTION_LINK=
# FCM SETUP
FIREBASE_PROJECT_ID=
FIREBASE_PRIVATE_KEY=
FIREBASE_CLIENT_EMAIL=
# EMAIL SETUP
EMAIL_HOST=
EMAIL_PORT=
EMAIL_USER=
EMAIL_PASSWORD=
#Payment Page Configurations
PAY_PAGE=
SUCCESSFULL_RECEIPT_PAGE=
UNSUCCESSFULL_RECEIPT_PAGE=
EXTERNAL_PAYMENT_URL=
#VISTA MONEY CONFIGURATION
GATEWAY_URL=
GATEWAY_URL_TESTING=
VISTAMONEY_CURRENCY_CODE=
VISTAMONEY_COUNTRY_CODE=
VISTAMONEY_PURCHASE_TRANSACTION_CODE=
VISTAMONEY_TERMINAL_ID=
VISTAMONEY_PASSWORD=
#CbPay Payment VARS
CBPAY_BASE_URL=
CBPAY_CLIENT_ID=
CBPAY_MERCHANT_ID=
CBPAY_SECRET_KEY=
#SKIP CASH PAYMENT VARS
SKIP_CASH_BASE_URL=
SKIP_CASH_KEY_ID=
SKIP_CASH_KEY_SECRET=
SKIP_CASH_CLIENT_ID=
SKIP_CASH_WEBHOOK_KEY=

#Payment security
SECURE_PAYMENT_ROUTE=
#QPAY CONFIGURATION
QPAY_URL=
#QPAY_URL=
QPAY_SECRET_KEY=
QPAY_BANK_ID=
QPAY_MERCHANT_ID=
#DIBSY CONFIGURATIUON
DIBSY_BASE_URL=
DIBSY_SECRET_KEY=
#Secure Acceptance Configuratio
SECURE_ACCEPTANCE_BASE_URL=
SECURE_ACCEPTANCE_SECRET_KEY=
SECURE_ACCEPTANCE_ACCESS_KEY=
SECURE_ACCEPTANCE_PROFILE_ID=
SECURE_ACCEPTANCE_SINGED_FILED=
SECURE_ACCEPTANCE_LOCALE=
#STRIPE PAYMENT INTEGRATION
STRIP_API_KEY_SECRET=
STRIP_PUBLISHABLE_KEY=
STRIPE_SiGNATURE=
#TESS PAYMENT INTEGRATION
TESS_BASE_URL=
TESS_MERCHANT_KEY=
TESS_MERCHANT_PASS=
#Deliverict
DELIVERECT_STAGING=
DELIVERECT_PRODUCTION=
DELIVERECT_CLIENT_ID=
DELIVERECT_CLIENT_SECRET=
DELIVERECT_CURRENT_HOST=
DELIVERECT_POS_CLIENT_ID=
DELIVERECT_POS_CLIENT_SECRET=
# Adler Integration Base URL
ADLER_BASE_URL=
#BALDIA API URL
BALADIA_API_URL=
#TOOKAN
TOOKAN_API_KEY=
TOOKAN_WEBHOOK_SHARED_SECRET=
TOOKAN_BASE_URL=
# BEE DELVIERY
BEE_BASEURL=
BEE_TOKEN=
# MrDelivery Configuration
MR_DELIVERY_BASE=
MR_DELIVERY_API_KEY=
MR_DELIVERY_MARKETPLACE_USER_ID=
MR_DELIVERY_VENDOR_ID=
# Clicks Integration
CLICKS_BASE_URL=
CLICKS_API_KEY=
#DeliveryHub INTEGRATION
DELIVERY_HUB_TEAM_ID=
# CALL-CENTER CONFIG
MAQSAM_BASE_URL=
MAQSAM_API_KEY=
MAQSAM_API_SECRET=
# SHORTEN URL PATH
SHORTEN_BASE_URL=
#ENABLE MAIN SERVICE CONFIGURATION
# JSON web token (JWT) secret: this keeps our app's user authentication secure
MAIN_JWT_SECRET=
# MONGODB CONNECTION STRING
MONGODB_URI=
ENABLE_TEST_USER_EMAIL=
ENABLE_TEST_USER_PASSWORD=
ENABLE_TEST_DEFAULT_USER_PASSWORD=
HARD_DELETE_CODE=
# HTTP Port
MAIN_PORT=
#SWAGGER URL
MAIN_SWAGGER_UI_URL=
HOST_URL=
MY_FATOORAH_QATAR_BASE_URL='https://api-qa.myfatoorah.com/'
MY_FATOORAH_BASE_URL='https://api.myfatoorah.com/'
MY_FATOORAH_SECRET_KEY=
#BIGCOMMERCE
BIGCOMMERCE_CLIENT_ID=
BIGCOMMERCE_SECRET=
BIGCOMMERCE_ACCESS_TOKEN=
BIGCOMMERCE_STORE_HASH=
# LOYALTY
APPLE_WALLET_PASSPHRASE=
LOYALTY_PAGE_BASE_URL= "https://enable-mfs.testing.enable.tech/loyalty/"
WALLET_PASSES_API_KEY=
DEFAULT_PASS_TYPE_IDENTIFIER="pass.tech.enable"
#Wishbox
WISHBOX_PLACE_ORDER_URL=
WISHBOX_API_KEY=

#Falcon Flex
FALCON_FLEX_BASE_URL=
FALCON_FLEX_TOKEN=

# Google Wallet
GOOGLE_WALLET_BASE_URL="https://walletobjects.googleapis.com/walletobjects/v1"
GOOGLE_WALLET_CREDENTIALS=
GOOGLE_WALLET_ISSUER_ID=

#Google cloud
GOOGLE_CLOUD_CREDENTIALS=
GOOGLE_CLOUD_PROJECT_ID=

#Winston Logger
WINSTON_LOGGING_DEVELOPMENT_LEVEL='info'
WINSTON_LOGGING_TESTING_LEVEL='debug'
WINSTON_LOGGING_STAGING_LEVEL='debug'
WINSTON_LOGGING_PRODUCTION_LEVEL='debug'

ENABLE_RIDERS_TEAM_ID=742307

#Tracking
MIXPANEL_TOKEN=
AMPLITUDE_API_KEY=

#Pass Delivery
PASS_DELIVERY_BASE_URL=
PASS_DELIVERY_TOKEN=

#Metabase
METABASE_SITE_URL=
METABASE_SECRET_KEY=

# Google Cloud Storage
GOOGLE_CLOUD_STORAGE_BUCKET="enable-main-standard-dev"
GOOGLE_CLOUD_STORAGE_PROJECT_ID=
GOOGLE_CLOUD_STORAGE_CREDENTIALS=

MICROSERVICE_NAME=enable-main
# Redis
REDIS_URL=

# PWA
DEFAULT_ENABLE_WALLET_LINK="https://testing-resturant.staging.enable.tech/"

# Number of instances
NUMBER_OF_INSTANCES=3

#CloudFlare
CF_PWA_ZONE_ID=
CF_PWA_API_TOKEN=
CF_PWA_WORKER_NAME=
CF_PWA_CONFIG_BASE=