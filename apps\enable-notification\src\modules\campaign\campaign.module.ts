import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Campaign, CampaignSchema } from '@app/shared-stuff';
import { CampaignController } from './controllers/campaign.controller';
import { CampaignRepository } from './repositories/campaign.repository';
import { CampaignRepositoryInterface } from './repositories/campaign.repository.interface';
import { CampaignService } from './services/campaign/campaign.service';
import { CampaignServiceInterface } from './services/campaign/campaign.service.interface';
import { TriggerModule } from '../trigger/trigger.module';
import { TemplateModule } from '../template/template.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { schema: CampaignSchema, name: Campaign.name },
    ]),
    TriggerModule,
    TemplateModule,
  ],
  controllers: [CampaignController],
  providers: [
    { provide: CampaignServiceInterface, useClass: CampaignService },
    { provide: CampaignRepositoryInterface, useClass: CampaignRepository },
  ],
})
export class CampaignModule {}
