import {
  Brand,
  CollectionName,
  GenericRepository,
  Payment,
  PaymentDocument,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import {
  FilterQuery,
  isValidObjectId,
  Model,
  Types,
  UpdateWriteOpResult,
} from 'mongoose';
import { PaymentReceiptDocument } from '../types/payment-receipt-document.type';
import { PaymentRepositoryInterface } from './interfaces/payment.repository.interface';

@Injectable()
export class PaymentRepository
  extends GenericRepository<PaymentDocument, Payment>
  implements PaymentRepositoryInterface
{
  constructor(
    @InjectModel(CollectionName.PAYMENT)
    private paymentModel: Model<PaymentDocument, Payment>,
  ) {
    super(paymentModel);
  }

  async findAll(): Promise<PaymentDocument[]> {
    return await this.repository.find({ deletedAt: null });
  }

  async findById(id: Types.ObjectId): Promise<PaymentDocument> {
    return await this.repository.findOne({ deletedAt: null, _id: id });
  }

  async findByIdWithCustomer(id: Types.ObjectId): Promise<PaymentDocument> {
    return this.paymentModel
      .findOne({ _id: id, deletedAt: null })
      .populate('customer')
      .exec();
  }

  async count(entityFilterQuery: FilterQuery<Payment> = {}): Promise<number> {
    return await this.paymentModel.countDocuments({
      deletedAt: null,
      ...entityFilterQuery,
    });
  }

  async findOne(
    entityFilterQuery: FilterQuery<Payment>,
  ): Promise<PaymentDocument | null> {
    return await this.paymentModel.findOne({
      deletedAt: null,
      ...entityFilterQuery,
    });
  }

  async findByCode(code: string): Promise<PaymentDocument | null> {
    return await this.paymentModel.findOne({ deletedAt: null, code: code });
  }

  async findByCodeWithPaymentLinkDetails(
    code: string,
  ): Promise<PaymentDocument | null> {
    return await this.paymentModel
      .findOne({ deletedAt: null, code: code })
      .populate('customer', 'full_name email phone _id')
      .populate('company', 'name email phone _id')
      .populate('branch', '_id name acronym senderId');
  }

  async findByCodeOrIdWithDetails(
    codeOrId: string | Types.ObjectId,
  ): Promise<PaymentDocument | null> {
    const filter: FilterQuery<PaymentDocument> = {
      deletedAt: null,
      $or: [{ code: codeOrId }],
    };

    // Sometimes this function returns true even if not ObjectId
    if (isValidObjectId(codeOrId)) filter.$or.push({ _id: codeOrId });

    return await this.paymentModel
      .findOne(filter)
      .populate('customer', 'full_name email phone _id')
      .populate('company', 'name email phone _id')
      .populate({
        path: 'branch',
        model: CollectionName.BRANCH,
        select: '_id name acronym senderId',
      });
  }

  async findByCodeOrIdWithFullDetails(
    codeOrId: string,
  ): Promise<PaymentDocument | null> {
    const filter: FilterQuery<PaymentDocument> = {
      deletedAt: null,
      $or: [{ code: codeOrId }],
    };

    // Sometimes this function returns true even if not ObjectId
    if (isValidObjectId(codeOrId)) filter.$or.push({ _id: codeOrId });

    return await this.paymentModel
      .findOne(filter)
      .select(
        'code customer_name payment_method payment_method_used amount _id payment_method ' +
          'order_code status order customer company_name language country_code customer_name ' +
          'customer_phone transaction_date branch transaction_id comments stripeClientSecret ' +
          'brand myFatoorahData localization integrationInfo',
      )
      .populate('customer', 'full_name email phone')
      .populate(
        'company',
        'name image _id followBranchMechanism usingApplePay localization',
      )
      .populate('branch', '_id name acronym image');
  }

  async findByCodeWithReceipt(
    code: string,
  ): Promise<PaymentReceiptDocument | null> {
    return await this.paymentModel.findOne(
      { deletedAt: null, code: code },
      'status transaction_id transaction_date customer_name company_name amount',
    );
  }

  async findByCBPayCode(
    transaction_code: any,
  ): Promise<PaymentDocument | null> {
    return await this.paymentModel
      .findOne({
        $or: [
          { transaction_id: transaction_code },
          {
            cbPayTransactionIds: {
              $elemMatch: { $eq: transaction_code },
            },
          },
        ],
      })
      .populate('customer')
      .populate('branch');
  }

  async findByExternalId(externalId: any): Promise<PaymentDocument | null> {
    return await this.paymentModel
      .findOne({
        externalId: externalId,
      })
      .populate('branch');
  }

  async findByIdWithBranch(
    id: Types.ObjectId,
  ): Promise<PaymentDocument | null> {
    return await this.paymentModel
      .findOne({
        _id: id,
      })
      .populate('branch');
  }

  async findByCodeWithBranch(code: string): Promise<PaymentDocument | null> {
    return await this.paymentModel
      .findOne({
        code: code,
      })
      .populate('branch');
  }

  async updateManyByBrand(
    startDate: moment.Moment,
    endDate: moment.Moment,
    brandId: Types.ObjectId,
    brand: Partial<Brand>,
  ): Promise<UpdateWriteOpResult> {
    const filter = {
      'brand._id': brandId,
      createdAt: {
        $gte: startDate.format('YYYY-MM-DD'),
        $lte: endDate.format('YYYY-MM-DD'),
      },
    };

    const update = {
      $set: {
        brand: {
          name: brand.name,
          phoneNumber: brand.phoneNumber,
          image: brand.image,
          _id: new Types.ObjectId(brandId),
        },
      },
    };

    return await this.paymentModel.updateMany(filter, update).exec();
  }
}
