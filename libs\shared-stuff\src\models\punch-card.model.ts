import { Schema, SchemaFactory } from '@nestjs/mongoose';
import { ValidateNested } from 'class-validator';
import { HydratedDocument, Types } from 'mongoose';
import { Field } from '../decorators/field';
import { RefField } from '../decorators/ref-field';
import { Image } from '../dtos/image/image.dto';
import { Achievement } from '../dtos/punch-card/achievement.dto';
import { PunchCardCounter } from '../dtos/punch-card/punch-card-counter.dto';
import { CollectionName } from '../enums/general/collection-name.enum';

export type PunchCardDocument = HydratedDocument<PunchCard>;

@Schema({ timestamps: true })
export class PunchCard {
  @RefField({
    ref: CollectionName.COMPANY,
    required: true,
  })
  companyId: Types.ObjectId;

  @Field({ type: String, required: true })
  nameEn: string;

  @Field({ type: String, required: true })
  nameAr: string;

  @Field({ type: PunchCardCounter, required: true })
  counter: PunchCardCounter;

  @Field({
    type: Achievement,
    required: false,
    default: [],
    isArray: true,
    additionalDecorators: [ValidateNested()],
  })
  achievements: Achievement[];

  @Field({ type: Boolean, required: true })
  hasStampOnEarn: boolean;

  @Field({ type: Boolean, required: true })
  hasStampOnRedeem: boolean;

  @Field({ type: Image, required: false })
  emptyStamp?: Image;

  @Field({ type: Image, required: false })
  filledStamp?: Image;

  @Field({ type: Number, required: true })
  track: number;
}

export const PunchCardSchema = SchemaFactory.createForClass(PunchCard);
PunchCardSchema.index({ companyId: 1 });
