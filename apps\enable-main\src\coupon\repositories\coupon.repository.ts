import {
  CollectionName,
  Coupon,
  CouponDocument,
  CouponIndexDto,
  CouponSearchType,
  CustomerDocument,
  GenericRepository,
} from '@app/shared-stuff';
import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model, Types } from 'mongoose';
import { CouponRepositoryInterface } from './coupon.repository.interface';

@Injectable()
export class CouponRepository
  extends GenericRepository<CouponDocument, Coupon>
  implements CouponRepositoryInterface
{
  private readonly logger = new Logger(CouponRepository.name);

  constructor(
    @InjectModel(CollectionName.COUPON)
    private couponModel: Model<CouponDocument, Coupon>,
  ) {
    super(couponModel);
  }

  async index(couponIndexDto: CouponIndexDto): Promise<CouponDocument[]> {
    const match: FilterQuery<Coupon> = {
      ...this.parseSearchTypes(couponIndexDto),
      companyId: couponIndexDto.companyId,
      deletedAt: null,
    };

    if (couponIndexDto.benefitType)
      match['benefits.type'] = couponIndexDto.benefitType;

    return this.couponModel.find(match);
  }

  private parseSearchTypes({
    searchTypes,
    searchValues,
  }: CouponIndexDto): FilterQuery<CouponDocument> {
    if (!searchTypes || searchTypes.length === 0) return {};
    if (!searchValues || searchValues.length === 0) return {};

    const searchTypeMapping = this.getSearchTypeMapping();

    const queries = searchTypes
      .filter((type) => type in searchTypeMapping)
      .map((type, index) => [type, searchValues[index]])
      .map(([type, value]) => searchTypeMapping[type](value));

    return queries.length > 1 ? { $and: queries } : queries[0] || {};
  }

  private getSearchTypeMapping(): Record<
    CouponSearchType,
    (value: string) => FilterQuery<CouponDocument>
  > {
    return {
      [CouponSearchType.COST_GTE]: (value) => ({
        loyaltyPointCost: { $gte: Number(value) },
      }),
      [CouponSearchType.COST_LTE]: (value) => ({
        loyaltyPointCost: { $lte: Number(value) },
      }),
    };
  }

  async findByCompanyId(companyId: Types.ObjectId): Promise<CouponDocument[]> {
    return await this.couponModel
      .find({
        companyId,
        deletedAt: null,
      })
      .sort({ loyaltyPointCost: 1 });
  }

  async findByOrdableId(ordableId: number): Promise<CouponDocument> {
    return await this.couponModel.findOne({
      ordableId,
      deletedAt: null,
    });
  }

  async findByShopifyDiscountCodeForCustomer(
    code: string,
    customer: CustomerDocument,
  ): Promise<CouponDocument | null> {
    return this.couponModel.findOne({
      companyId: customer.company,
      shopifyDiscountCode: code,
      loyaltyPointCost: { $lte: customer.loyaltyPoints },
      deletedAt: null,
    });
  }

  async findByCost(
    companyId: Types.ObjectId,
    lowerBound: number,
    upperBound: number,
  ): Promise<CouponDocument[]> {
    return await this.couponModel.find({
      companyId,
      loyaltyPointCost: { $gt: lowerBound, $lte: upperBound },
      deletedAt: null,
    });
  }

  async findHighestCoupon(
    companyId: Types.ObjectId,
    upperBound: number,
  ): Promise<CouponDocument> {
    return await this.couponModel
      .findOne({
        companyId,
        loyaltyPointCost: { $lte: upperBound },
        deletedAt: null,
      })
      .sort({ loyaltyPointCost: 'desc' });
  }
}
