import {
  GenericExceptionFilter,
  TransformInterceptor,
} from '@app/shared-stuff';
import { paymentGatewayCredentialMapping } from '@app/shared-stuff/constants/payment/payment-gateway-credential-mapping.constant';
import {
  Body,
  Controller,
  Get,
  Inject,
  Param,
  Post,
  Put,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { Types } from 'mongoose';
import { CreatePaymentConfigurationDto } from '../../../../../../libs/shared-stuff/src/dtos/payment-configuration/create-payment-configuration.dto';
import { UpdatePaymentConfigurationDto } from '../../../../../../libs/shared-stuff/src/dtos/payment-configuration/update-payment-configuration.dto';
import { PaymentConfigurationServiceInterface } from '../../services/payment-configuration/payment-configuration.service.interface';

@Controller('payment/configuration')
@ApiTags('Payment Configuration')
@SetMetadata('module', 'payment-configuration')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
export class PaymentConfigurationController {
  constructor(
    @Inject(PaymentConfigurationServiceInterface)
    private paymentConfigurationService: PaymentConfigurationServiceInterface,
  ) {}

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body()
    createPaymentConfigurationDto: CreatePaymentConfigurationDto,
  ) {
    return await this.paymentConfigurationService.create(
      createPaymentConfigurationDto,
    );
  }

  @Put()
  @SetMetadata('action', 'update')
  async update(
    @Body()
    updatePaymentConfigurationDto: UpdatePaymentConfigurationDto,
  ) {
    return await this.paymentConfigurationService.update(
      updatePaymentConfigurationDto,
    );
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  @ApiQuery({
    name: 'id',
    type: String,
    description:
      'Payment configuration ID, or company ID, or brand ID, or branch ID',
  })
  async findByIdentifier(@Param('id') identifier: string) {
    // TODO: before returning, make sure current user has access to this
    // company's payment configs
    return await this.paymentConfigurationService.findPaymentConfigByIdentifier(
      new Types.ObjectId(identifier),
    );
  }

  @Get('/public/:id')
  @SetMetadata('public', 'true')
  @ApiQuery({
    name: 'id',
    type: String,
    description:
      'Payment configuration ID, or company ID, or brand ID, or branch ID',
  })
  async findByIdentifierPublic(@Param('id') identifier: string) {
    return await this.paymentConfigurationService.findPublicPaymentConfigByIdentifier(
      new Types.ObjectId(identifier),
    );
  }

  @Get('gateway/credential')
  @SetMetadata('action', 'get_details')
  getGatewayCredential() {
    return paymentGatewayCredentialMapping;
  }
}
