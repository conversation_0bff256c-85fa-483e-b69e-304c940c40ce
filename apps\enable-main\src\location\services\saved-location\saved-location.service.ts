import {
  CollectionName,
  Customer,
  CustomerDocument,
  responseCode,
  SavedAddressToSetDefault,
  SavedLocationDocument,
  SavedLocationSearchType,
  SavedLocationToCreate,
  SavedLocationToIndex,
  SavedLocationToUpdate,
  SavedLocationType,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { HelperService } from './../../../shared/services/helper/helper.service';

@Injectable()
export class SavedLocationService {
  constructor(
    @InjectModel(CollectionName.SAVED_LOCATION)
    private savedLocationModel: Model<SavedLocationDocument>,
    private helperService: HelperService,
    @InjectModel(CollectionName.CUSTOMER)
    private customerModel: Model<Customer>,
    private eventEmitter: EventEmitter2,
  ) {}

  public async findAllByIds(locationIds: Types.ObjectId[]) {
    return this.savedLocationModel.find({
      _id: { $in: locationIds },
      deletedAt: null,
    });
  }

  async index(savedLocationToIndex: SavedLocationToIndex) {
    const aggregation = [] as any;
    let match = { deletedAt: null } as any;
    const sort = { createdAt: -1 };

    if (savedLocationToIndex.customer) {
      const customer = await this.customerModel.findOne({
        $or: [
          { phone: savedLocationToIndex.customer },
          {
            _id: Types.ObjectId.isValid(savedLocationToIndex.customer)
              ? new Types.ObjectId(savedLocationToIndex.customer)
              : new Types.ObjectId(),
          },
        ],
        company: new Types.ObjectId(savedLocationToIndex.companyId),
        deletedAt: null,
      });
      match['_id'] = {
        $in: customer && customer.savedLocations ? customer.savedLocations : [],
      };
    }

    if (savedLocationToIndex.searchType && savedLocationToIndex.search_key) {
      if (savedLocationToIndex.searchType == SavedLocationSearchType.all) {
        const availableFields = Object.keys(SavedLocationSearchType).filter(
          (x) => x != SavedLocationSearchType.all,
        );
        match = {
          ...match,
          $or: availableFields.map((field) => {
            return {
              [field]: {
                $regex: savedLocationToIndex.search_key,
                $options: 'i',
              },
            };
          }),
        };
      } else {
        match[savedLocationToIndex.searchType] = {
          $regex: savedLocationToIndex.search_key,
          $options: 'i',
        };
      }
    }

    // Building The Pipeline
    aggregation.push(
      { $match: match },
      { $sort: sort },
      {
        $facet: {
          paginatedResult: [
            ...(Number(savedLocationToIndex.offset) ||
            Number(savedLocationToIndex.offset) == 0
              ? [
                  {
                    $skip:
                      Number(savedLocationToIndex.offset) *
                      Number(savedLocationToIndex.limit),
                  },
                ]
              : [
                  {
                    $skip: 0,
                  },
                ]),
            ...(Number(savedLocationToIndex.limit)
              ? [
                  {
                    $limit: Number(savedLocationToIndex.limit),
                  },
                ]
              : []),
          ],
          totalCount: [
            {
              $count: 'createdAt',
            },
          ],
        },
      },
    );

    const savedLocations = await this.savedLocationModel.aggregate(aggregation);
    return savedLocations;
  }

  async update(savedLocationToUpdate: SavedLocationToUpdate) {
    let savedLocation = await this.getDetails(
      savedLocationToUpdate._id.toHexString(),
    );

    // Re-Validate the saved location
    const customer = await this.validateSavedLocation(savedLocationToUpdate);

    savedLocation = await this.savedLocationModel.findOneAndUpdate(
      { _id: savedLocation._id },
      savedLocationToUpdate,
      { new: true },
    );

    //Firing the saved location updated Event
    this.eventEmitter.emit('savedLocation.updated', savedLocation);
    return savedLocation;
  }

  async create(
    savedLocationToCreate: SavedLocationToCreate,
  ): Promise<SavedLocationDocument> {
    // validate the saved location to create and fetching the customer info
    const customer = await this.validateSavedLocation(savedLocationToCreate);

    // creating the saved location
    const savedLocation = new this.savedLocationModel(savedLocationToCreate);

    await savedLocation.save();

    if (customer) {
      await this.customerModel.updateOne(
        { _id: customer._id },
        { $push: { savedLocations: savedLocation._id } },
      );
    }

    return savedLocation;
  }

  private async validateSavedLocation(
    savedLocationTo: SavedLocationToCreate | SavedLocationToUpdate,
  ) {
    // Saved Location Type Validation
    if (savedLocationTo.type == SavedLocationType.NATIONAL_ADDRESS) {
      if (
        !savedLocationTo.zoneNumber ||
        !savedLocationTo.buildingNumber ||
        !savedLocationTo.streetNumber
      ) {
        throw {
          code: responseCode.BAD_REQUEST,
          statusCode: 400,
          message:
            'You need to provide the (zoneNumber, buildingNumber, streetNumber) since the address type is NATIONAL_ADDRESS',
        };
      }
      const nationalAddress =
        await this.helperService.convertQatarNationalAddressToMapsCoordinate(
          savedLocationTo.zoneNumber,
          savedLocationTo.streetNumber,
          savedLocationTo.buildingNumber,
        );
      savedLocationTo.latitude = nationalAddress['lat'];
      savedLocationTo.longitude = nationalAddress['lng'];
      savedLocationTo.pinLink = nationalAddress['google_link'];
    } else if (savedLocationTo.type == SavedLocationType.PIN_LOCATION) {
      if (!savedLocationTo.latitude || !savedLocationTo.longitude) {
        throw {
          code: responseCode.BAD_REQUEST,
          statusCode: 400,
          message:
            'You need to provide the (latitude, longitude) since the address type is PIN_LOCATION',
        };
      }
      if (!savedLocationTo.buildingName) {
        throw {
          code: responseCode.BAD_REQUEST,
          statusCode: 400,
          message:
            'You need to provide the ( buildingName ) since the address type is PIN_LOCATION',
        };
      }
      savedLocationTo.pinLink = `https://maps.google.com/?q=${savedLocationTo.latitude},${savedLocationTo.longitude}`;
    }

    // Customer Validation (LAST STEP)
    if (savedLocationTo['customerId']) {
      const customer = await this.customerModel.findOne({
        _id: new Types.ObjectId(savedLocationTo['customerId']),
      });
      if (!customer) {
        throw {
          code: responseCode.BAD_REQUEST,
          statusCode: 400,
          message:
            'customerId provided but it is not correct, Please provide a correct customer id',
        };
      }
      return customer;
    } else {
      return undefined;
    }
  }

  async findOrCreate() {}

  async getDetails(id: string) {
    const savedLocation = await this.savedLocationModel.findOne({ _id: id });

    return savedLocation;
  }

  async remove(id: string) {
    const savedLocation = await this.getDetails(id);

    if (!savedLocation) {
      throw {
        statusCode: 400,
        code: 400,
        message: 'Please Include A Correct Saved Location ID',
      };
    }

    savedLocation.deletedAt = new Date();
    await savedLocation.save();

    return savedLocation;
  }

  async setDefaultCustomerSavedLocation(
    defaultAddressToSet: SavedAddressToSetDefault,
  ) {
    const customer = await this.customerModel.findOne({
      _id: defaultAddressToSet.customerId,
    });

    const locations = await this.savedLocationModel.find({
      _id: { $in: customer.savedLocations },
    });

    for (let i = 0; i < locations.length; i++) {
      if (locations[i]._id == defaultAddressToSet.locationId) {
        locations[i].isDefault = true;
      } else {
        locations[i].isDefault = false;
      }
      await locations[i].save();
    }

    return 'DONE';
  }

  async getCachedNationalAddress(
    zone: number,
    building: number,
    street: number,
  ) {
    const location = await this.savedLocationModel.findOne({
      zoneNumber: zone,
      streetNumber: street,
      buildingNumber: building,
    });
    return location;
  }

  async insertMany(data: any) {
    const locations = await this.savedLocationModel.insertMany(data, {
      ordered: false,
      rawResult: true,
    });
    return locations;
  }

  async fetchNationalAddressAndUpdate(locationId: Types.ObjectId) {
    const location = await this.savedLocationModel.findOne({ _id: locationId });
    if (
      !location ||
      !location.zoneNumber ||
      !location.buildingNumber ||
      !location.streetNumber
    ) {
      return;
    }

    const nationalAddress =
      await this.helperService.convertQatarNationalAddressToMapsCoordinate(
        location.zoneNumber,
        location.streetNumber,
        location.buildingNumber,
      );

    location.latitude = nationalAddress['lat'];
    location.longitude = nationalAddress['lng'];
    location.pinLink = nationalAddress['google_link'];
    await location.save();

    return location;
  }

  async getPreferredSavedLocation(
    customer: CustomerDocument,
  ): Promise<SavedLocationDocument | null> {
    if (!customer.savedLocations || customer.savedLocations.length === 0) {
      return null;
    }

    const defaultLocation = await this.savedLocationModel.findOne({
      _id: { $in: customer.savedLocations },
      isDefault: true,
    });
    if (defaultLocation) return defaultLocation;

    for (let i = customer.savedLocations.length - 1; i >= 0; i--) {
      const location = await this.savedLocationModel.findById(
        customer.savedLocations[i],
      );
      if (location) return location;
    }

    return null;
  }
}
