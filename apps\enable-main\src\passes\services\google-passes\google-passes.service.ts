import {
  BarcodeType<PERSON><PERSON>,
  BrandDocument,
  CompanyDocument,
  CustomerDocument,
  forEachAsync,
  GoogleWalletWebhookPayload,
  GoogleWalletWebhookType,
  IMAGE_URLS,
  LabelledField,
  LoyaltyClass,
  LoyaltyObject,
  PassConfig,
  PassField,
  PassFieldContext,
  PassFieldName,
  PassImage,
  PassObjectId,
  RegisteredPass,
  ReviewStatusEnum,
  StateEnum,
  TextModuleData,
  TierLevellingUpMethod,
  WalletApp,
} from '@app/shared-stuff';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { CompanyService } from '../../../company/services/company/company.service';
import { CustomerCodeServiceInterface } from '../../../customer/modules/customer-code/customer-code.service.interface';
import { CustomerPassServiceInterface } from '../../../customer/modules/customer-pass/customer-pass.service.interface';
import { CustomerReadServiceInterface } from '../../../customer/modules/customer-read/customer-read.service.interface';
import { GeneratePassDto } from '../../dto/generate-pass.dto';
import { GooglePassesHttpService } from '../google-passes-http/google-passes-http.service';
import { PassConfigService } from '../pass-config/pass-config.service';
import { PassesFieldServiceInterface } from '../passes-field/passes-field.service.interface';
import { PassesImageServiceInterface } from '../passes-image/passes-image.service.interface';
import { PassesService } from '../passes.service';
import { GooglePassesServiceInterface } from './google-passes.service.interface';

@Injectable()
export class GooglePassesService implements GooglePassesServiceInterface {
  constructor(
    private readonly passesService: PassesService,
    @Inject(PassesImageServiceInterface)
    private readonly passesImageService: PassesImageServiceInterface,
    @Inject(PassesFieldServiceInterface)
    private readonly passesFieldService: PassesFieldServiceInterface,
    @Inject(CustomerReadServiceInterface)
    private readonly customerReadService: CustomerReadServiceInterface,
    @Inject(CustomerPassServiceInterface)
    private readonly customerPassService: CustomerPassServiceInterface,
    @Inject(CustomerCodeServiceInterface)
    private readonly customerCodeService: CustomerCodeServiceInterface,
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandServiceInterface,
    private readonly companyService: CompanyService,
    private readonly configService: ConfigService,
    private readonly googlePassesHttpService: GooglePassesHttpService,
    private readonly passConfigService: PassConfigService,
  ) {}
  private readonly logger = new Logger(GooglePassesService.name);

  async handleGoogleWalletWebhook(
    payload: GoogleWalletWebhookPayload,
  ): Promise<void> {
    if (payload.eventType === GoogleWalletWebhookType.REGISTER)
      await this.registerGoogleWalletPass(payload);
    else if (payload.eventType === GoogleWalletWebhookType.UNREGISTER)
      await this.unregisterGoogleWalletPass(payload);
    else this.logger.error(`Unknown event type ${payload.eventType}`);
  }

  async registerGoogleWalletPass({
    classId,
    objectId,
    nonce,
  }: GoogleWalletWebhookPayload): Promise<void> {
    const customerId = objectId.getCustomerId();
    const customer = await this.customerReadService.findOne(customerId);

    const exists = (customer.registeredPasses || []).some(
      (existingPass: RegisteredPass) =>
        existingPass.serialNumber === nonce && !existingPass.deletedAt,
    );
    if (exists) return;

    const newPass: RegisteredPass = {
      passTypeIdentifier: classId,
      deviceLibraryIdentifier: objectId.toString(),
      serialNumber: nonce,
      walletApp: WalletApp.GOOGLE_WALLET,
      deviceData: customer.deviceData,
      createdAt: moment.utc().toDate(),
      updatedAt: moment.utc().toDate(),
    };
    await this.passesService.saveNewPass(customer, newPass);
  }

  async unregisterGoogleWalletPass({
    objectId,
  }: GoogleWalletWebhookPayload): Promise<void> {
    const customerId = objectId.getCustomerId();
    const customer = await this.customerReadService.findOne(customerId);

    const foundPass = (customer.registeredPasses || []).find(
      (existingPass: RegisteredPass) =>
        existingPass.deviceLibraryIdentifier === objectId.toString(),
    );
    if (!foundPass) {
      this.logger.warn('Tried to delete pass that does not exist.', {
        customer,
        objectId: objectId.toString(),
      });
    } else {
      await this.passesService.deletePass(customer, foundPass);
    }
  }

  async generatePassWithoutAuth({
    customerId,
    brandId,
    forceUpdate,
  }: GeneratePassDto): Promise<string> {
    const customer = await this.customerReadService.findOne(
      customerId.toHexString(),
    );

    return await this.generatePass(customer, brandId, forceUpdate);
  }

  async generatePass(
    customer: CustomerDocument,
    brandId: Types.ObjectId,
    forceUpdate = false,
  ): Promise<string> {
    const generatedLink = customer.googlePassLinks?.[brandId.toString()];
    if (generatedLink && !forceUpdate) return generatedLink;

    const hostUrl = this.configService.get<string>('HOST_URL');
    const isLocalEnv =
      !hostUrl ||
      hostUrl.includes('http://') ||
      hostUrl.includes('://localhost') ||
      hostUrl.includes('://192.168.');
    if (isLocalEnv) return 'No google pass generation for local env';

    const brand = await this.brandService.findById(brandId);
    const company = await this.companyService.findById(brand.companyId._id);
    const passConfig = await this.passConfigService.getPassConfig(
      company._id,
      brand._id,
      customer.loyaltyTier?._id,
    );

    if (!company.hasLoyaltyProgram) return '';

    const loyaltyClass = this.buildLoyaltyClass(passConfig, brand);
    await this.googlePassesHttpService.updateOrCreateClass(loyaltyClass);

    const passFieldContext = await this.passesService.getPassFieldContext(
      customer,
      brand,
    );

    const loyaltyObject = await this.buildLoyaltyObject(
      customer,
      passConfig,
      brand,
      company,
      passFieldContext,
    );
    await this.googlePassesHttpService.updateOrCreateObject(loyaltyObject);
    const passLink = await this.googlePassesHttpService.createPassLink(
      loyaltyObject.id,
    );

    await this.customerPassService.savePassLink(
      customer,
      brand._id.toString(),
      passLink,
    );
    return passLink;
  }

  private buildLoyaltyClass(
    passConfig: PassConfig,
    brand: BrandDocument,
  ): LoyaltyClass {
    const baseUrl = this.configService.get<string>('HOST_URL');
    const webhookUrl = `${baseUrl}/v1/passes/googleWalletWebhook`;
    const programLogo = this.getProgramLogo(passConfig, brand);
    return {
      id: this.googlePassesHttpService.getLoyaltyClassId(brand),
      issuerName: 'Enable.tech',
      reviewStatus: ReviewStatusEnum.UNDER_REVIEW,
      callbackOptions: { url: webhookUrl },
      programName: passConfig.googleWalletConfig?.programName ?? brand.name,
      programLogo: programLogo,
      wideProgramLogo: passConfig.googleWalletConfig?.useNarrowProgramLogo
        ? null
        : programLogo,
      hexBackgroundColor:
        this.rgbToHex(passConfig.passTemplate?.backgroundColor) || '#ffffff',
      classTemplateInfo: {
        cardTemplateOverride: {
          cardRowTemplateInfos: [
            {
              oneItem: {
                item: {
                  firstValue: {
                    fields: [
                      { fieldPath: "object.imageModulesData['STRIP_IMAGE']" },
                    ],
                  },
                },
              },
            },
            {
              threeItems: {
                startItem: {
                  firstValue: {
                    fields: [{ fieldPath: 'object.textModulesData[0]' }],
                  },
                },
                middleItem: {
                  firstValue: {
                    fields: [{ fieldPath: 'object.textModulesData[1]' }],
                  },
                },
                endItem: {
                  firstValue: {
                    fields: [{ fieldPath: 'object.textModulesData[2]' }],
                  },
                },
              },
            },
            {
              threeItems: {
                startItem: {
                  firstValue: {
                    fields: [{ fieldPath: 'object.textModulesData[3]' }],
                  },
                },
                middleItem: {
                  firstValue: {
                    fields: [{ fieldPath: 'object.textModulesData[4]' }],
                  },
                },
                endItem: {
                  firstValue: {
                    fields: [{ fieldPath: 'object.textModulesData[5]' }],
                  },
                },
              },
            },
          ],
        },
      },
    };
  }

  private getProgramLogo(
    passConfig: PassConfig,
    brand: BrandDocument,
  ): PassImage {
    return {
      sourceUri: { uri: this.getProgramLogoUri(passConfig, brand) },
      contentDescription: {
        defaultValue: { language: 'en-US', value: `${brand.name} logo` },
      },
    };
  }

  private getProgramLogoUri(
    passConfig: PassConfig,
    brand: BrandDocument,
  ): string {
    if (passConfig.googleWalletConfig?.programLogo)
      return passConfig.googleWalletConfig.programLogo.url;

    if (brand.image) return brand.image.url;

    return IMAGE_URLS.ENABLE_LOGO;
  }

  private async buildLoyaltyObject(
    customer: CustomerDocument,
    passConfig: PassConfig,
    brand: BrandDocument,
    company: CompanyDocument,
    passFieldContext: PassFieldContext,
  ): Promise<LoyaltyObject> {
    return {
      id: this.googlePassesHttpService
        .getLoyaltyObjectId(brand, customer)
        .toString(),
      classId: this.googlePassesHttpService.getLoyaltyClassId(brand),
      state: StateEnum.ACTIVE,
      heroImage: null,
      linksModuleData: this.passesFieldService.getLinksModuleData(brand),
      textModulesData: await this.getTextModulesData(
        customer,
        passConfig,
        brand,
        company,
        passFieldContext,
      ),
      imageModulesData: [
        { mainImage: this.getStripImage(customer, brand), id: 'STRIP_IMAGE' },
      ],
      barcode: {
        type: BarcodeTypeEnum.QR_CODE,
        value: await this.passesService.generateQRCodePayload(
          customer,
          passConfig,
        ),
        alternateText: '',
      },
      accountId:
        await this.customerCodeService.getOrGenerateCustomerShortCode(customer),
      accountName: customer.full_name,
      loyaltyPoints: {
        label: company.loyaltyProgramConfig.pointsBalanceTitleEn,
        balance: {
          int: Math.floor(customer.loyaltyPoints),
        },
      },
      secondaryLoyaltyPoints: {
        label: 'Tier',
        balance: {
          string:
            customer.loyaltyTier?.nameEn ??
            company.loyaltyProgramConfig?.noTierTitleEn ??
            'No Tier',
        },
      },
      ...(brand.image && {
        wideProgramLogo: {
          sourceUri: {
            uri: brand.image.url,
          },
        },
      }),
    };
  }

  private async getTextModulesData(
    customer: CustomerDocument,
    passConfig: PassConfig,
    brand: BrandDocument,
    company: CompanyDocument,
    passFieldContext: PassFieldContext,
  ): Promise<TextModuleData[]> {
    const fields = this.getFrontFields(company, passConfig);

    const frontFields = await this.passesFieldService.getFields(
      fields,
      passConfig?.fieldConfig,
      passFieldContext,
    );

    const textModuleData: TextModuleData[] = frontFields.map((field) =>
      field.convertForGooglePass(),
    );

    const push = (field: PassField) => {
      const googlePassField = field?.convertForGooglePass();
      if (googlePassField) textModuleData.push(googlePassField);
    };

    // Add empty fields to push the following fields to the back of the pass.
    while (textModuleData.length < 6) {
      textModuleData.push({});
    }

    push(this.passesFieldService.getMessagesField(customer));

    push(
      this.passesFieldService.getPassDescriptionField(
        passConfig.fieldConfig,
        brand,
      ),
    );

    if (company?.loyaltyProgramConfig?.hasLoyaltyTiers)
      push(
        await this.passesFieldService.getTierDescriptionField(
          customer,
          brand,
          passFieldContext.nextTier,
          passFieldContext.loyaltyTierProgramProgress,
        ),
      );

    if (
      company?.loyaltyProgramConfig?.hasLoyaltyPoints &&
      !passFieldContext.isPointsWithoutCoupons
    )
      push(
        this.passesFieldService.getPointsDescriptionField(
          customer,
          brand,
          company,
          passFieldContext.availableCoupons,
        ),
      );

    if (company?.loyaltyProgramConfig?.hasLoyaltyPunchCards)
      push(
        await this.passesFieldService.getRewardsDescriptionField(
          customer,
          brand,
        ),
      );

    push(this.passesFieldService.getCustomerBenefitField(customer));

    const hasSocialLinks =
      company.facebook || company.instgram || company.twitter;
    if (hasSocialLinks) {
      push(this.passesFieldService.getSocialLinksField(brand, company));
    }

    push(this.passesFieldService.getPoweredByField());

    return textModuleData;
  }

  private getFrontFields(
    company: CompanyDocument,
    passConfig: PassConfig,
  ): LabelledField[] {
    if (passConfig?.fieldConfig?.googleWalletFields)
      return passConfig.fieldConfig.googleWalletFields;

    const fieldNames: LabelledField[] = [
      { fieldName: PassFieldName.CUSTOMER_FIRST_NAME },
    ];

    const isTiersAndPointsAndPunchCard =
      company?.loyaltyProgramConfig?.hasLoyaltyPoints &&
      company?.loyaltyProgramConfig?.hasLoyaltyTiers &&
      company?.loyaltyProgramConfig?.hasLoyaltyPunchCards;

    if (isTiersAndPointsAndPunchCard) {
      fieldNames.push(
        { fieldName: PassFieldName.CUSTOMER_AVAILABLE_REWARDS },
        { fieldName: PassFieldName.PUNCH_CARD_PROGRESS },
        { fieldName: PassFieldName.POINTS_BALANCE },
        { fieldName: PassFieldName.CUSTOMER_AVAILABLE_COUPONS },
      );
      if (!passConfig.fieldConfig?.hideTierSecondaryField)
        fieldNames.push({ fieldName: PassFieldName.TIER });

      return fieldNames;
    }

    if (
      company?.loyaltyProgramConfig?.hasLoyaltyTiers &&
      !passConfig.fieldConfig?.hideTierSecondaryField
    )
      fieldNames.push({ fieldName: PassFieldName.TIER });

    const isPointsWithoutCoupons =
      company.loyaltyProgramConfig.hasLoyaltyTiers &&
      company.loyaltyProgramConfig.hasLoyaltyPoints &&
      company.loyaltyProgramConfig.tierLevellingUpMethod ===
        TierLevellingUpMethod.POINTS_RATE &&
      !company.loyaltyProgramConfig.hasCoupons;

    if (
      company?.loyaltyProgramConfig?.hasLoyaltyPoints &&
      !isPointsWithoutCoupons
    )
      fieldNames.push({ fieldName: PassFieldName.POINTS_BALANCE });

    if (
      company?.loyaltyProgramConfig?.hasLoyaltyPoints &&
      isPointsWithoutCoupons
    )
      fieldNames.push({ fieldName: PassFieldName.POINTS_PROGRESS });

    if (company?.loyaltyProgramConfig?.hasLoyaltyTiers)
      fieldNames.push(
        { fieldName: PassFieldName.TIER_VALID_TILL },
        { fieldName: PassFieldName.NEXT_TIER },
      );

    if (
      company?.loyaltyProgramConfig?.hasLoyaltyPoints &&
      !isPointsWithoutCoupons
    )
      fieldNames.push(
        { fieldName: PassFieldName.NEXT_COUPON },
        { fieldName: PassFieldName.CUSTOMER_AVAILABLE_COUPONS },
      );

    if (company?.loyaltyProgramConfig?.hasLoyaltyPunchCards)
      fieldNames.push(
        { fieldName: PassFieldName.CUSTOMER_AVAILABLE_REWARDS },
        { fieldName: PassFieldName.PUNCH_CARD_PROGRESS },
      );

    return fieldNames;
  }

  private getStripImage(
    customer: CustomerDocument,
    brand: BrandDocument,
  ): PassImage {
    return {
      sourceUri: {
        uri: this.passesImageService.generateImageUrl(customer, brand),
      },
      contentDescription: {
        defaultValue: {
          language: 'en',
          value: 'Stamp card representing loyalty progress',
        },
      },
    };
  }

  private rgbToHex(rgbString: string): string {
    // matches 'rgb(255, 255, 255)'
    const isRGB = /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/.test(rgbString);
    if (!rgbString || !isRGB) return rgbString;

    // ' 255' => 255
    const extractRgbComponent = (component: string): number =>
      parseInt(component.trim());

    // 255 => 'ff'
    const convertToHexComponent = (rgbComponent: number): string =>
      (rgbComponent < 16 ? '0' : '') + rgbComponent.toString(16);

    const hexValues = rgbString
      .substring(4, rgbString.length - 1)
      .split(',')
      .map(extractRgbComponent)
      .map(convertToHexComponent);

    const hexString = `#${hexValues.join('')}`;
    return hexString;
  }

  public async updatePasses(googlePasses: RegisteredPass[]): Promise<void> {
    const uniqueObjectIds = this.getUniqueObjectIds(googlePasses);
    await forEachAsync(uniqueObjectIds, async (objectId) => {
      const customerId = new Types.ObjectId(objectId.getCustomerId());
      const brandId = new Types.ObjectId(objectId.getBrandId());
      await this.generatePassWithoutAuth({
        customerId,
        brandId,
        forceUpdate: true,
      });
    });
  }

  private getUniqueObjectIds(googlePasses: RegisteredPass[]): PassObjectId[] {
    const ids = googlePasses.map((pass) => pass.deviceLibraryIdentifier);
    const uniqueIds = Array.from(new Set(ids));
    return uniqueIds.map((id) => new PassObjectId(id));
  }
}
