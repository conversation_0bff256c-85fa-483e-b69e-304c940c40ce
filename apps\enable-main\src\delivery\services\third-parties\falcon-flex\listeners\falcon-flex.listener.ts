import { OrderDocument } from '@app/shared-stuff';
import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { FalconFlexService } from '../services/falcon-flex.service';

@Injectable()
export class FalconFlexListener {
  private readonly logger = new Logger(FalconFlexListener.name);

  constructor(private readonly falconFlexService: FalconFlexService) {}

  @OnEvent('order.cancelled')
  async onOrderCancelled(order: OrderDocument) {
    if (!order.falconFlexTaskId) return;
    this.logger.log('order.cancelled Event');
    this.logger.log('Order Code', order.code);
    await this.falconFlexService.cancel(order);
  }

  @OnEvent('order.deleted')
  async onOrderDeleted(order: OrderDocument) {
    if (!order.falconFlexTaskId) return;
    this.logger.log('order.deleted Event');
    this.logger.log('Order Code', order.code);
    await this.falconFlexService.cancel(order);
  }
}
