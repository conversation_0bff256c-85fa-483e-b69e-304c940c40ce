import {
  CampaignDocument,
  CreateCampaignDtoWithAuth,
  IndexCampaignDto,
  LaunchCampaignDto,
} from '@app/shared-stuff';

export interface CampaignServiceInterface {
  index(indexCampaignDto: IndexCampaignDto): Promise<CampaignDocument[]>;
  create(
    createCampaignDto: CreateCampaignDtoWithAuth,
  ): Promise<CampaignDocument>;
  launch(launchCampaignDto: LaunchCampaignDto): Promise<void>;
}

export const CampaignServiceInterface = Symbol('CampaignServiceInterface');
