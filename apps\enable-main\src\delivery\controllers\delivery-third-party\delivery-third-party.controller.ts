import {
  GenericExceptionFilter,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Get,
  Inject,
  Put,
  Query,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';
import { IndexDeliveryThirdPartyDto } from '../../dto/delivery-third-party/index-delivery-third-party.dto';
import { UpdateDeliveryThirdPartyDto } from '../../dto/delivery-third-party/update-delivery-third-party.dto';
import { DeliveryThirdParty } from '../../models/delivery-third-party.model';
import { DeliveryThirdPartyServiceInterface } from '../../services/delivery-third-party/delivery-third-party.service.interface';

@Controller('delivery/thirdParty')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags(DeliveryThirdParty.name)
@SetMetadata('module', 'thirdPartyConfiguration')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class DeliveryThirdPartyController {
  constructor(
    @Inject(DeliveryThirdPartyServiceInterface)
    private readonly deliveryThirdPartyService: DeliveryThirdPartyServiceInterface,
  ) {}

  @Get()
  @ApiOkResponse({ type: DeliveryThirdParty, isArray: true })
  @SetMetadata('action', 'index')
  async index(
    @Query() indexThirdPartyConfigurationDto: IndexDeliveryThirdPartyDto,
  ) {
    return await this.deliveryThirdPartyService.index(
      indexThirdPartyConfigurationDto,
    );
  }

  @Put()
  @ApiOkResponse({ type: DeliveryThirdParty })
  @SetMetadata('action', 'update')
  async update(
    @Body() updateThirdPartyConfigurationDto: UpdateDeliveryThirdPartyDto,
  ) {
    return await this.deliveryThirdPartyService.update(
      updateThirdPartyConfigurationDto,
    );
  }
}
