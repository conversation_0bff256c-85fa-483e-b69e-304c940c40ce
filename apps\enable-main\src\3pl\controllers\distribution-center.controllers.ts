import {
  CreateDistributionCenterDto,
  GenericExceptionFilter,
  GetAllDistributionCenterDto,
  SingleIdDto,
  TransformInterceptor,
  UpdateDistributionCenterDto,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Get,
  Inject,
  Param,
  Patch,
  Post,
  Query,
  Req,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { Types } from 'mongoose';
import { DistributionCenterServiceInterface } from '../services/distribution-center/distribution-center-service.interface';

@Controller('distribution-center')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags('Third Party Logistics')
@SetMetadata('module', 'distribution-center')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class DistributionCenterController {
  constructor(
    @Inject(DistributionCenterServiceInterface)
    private distributionCenterService: DistributionCenterServiceInterface,
  ) {}

  @Get()
  @SetMetadata('action', 'index')
  async findAll(
    @Query() getAllDistributionCenterDto: GetAllDistributionCenterDto,
    @Req() req: Request,
  ) {
    getAllDistributionCenterDto.companyId = req['company_id']
      ? req['company_id']
      : getAllDistributionCenterDto.companyId;

    const result = await this.distributionCenterService.findAll(
      getAllDistributionCenterDto,
    );
    return {
      distributionCenters: result[0].paginatedResult,
      totalDistributionCenters: result[0].totalCount[0].createdAt,
    };
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  async findOne(@Param() { id }: SingleIdDto) {
    return this.distributionCenterService.findOne(id);
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() createDistributionCenterDto: CreateDistributionCenterDto,
    @Req() req: Request,
  ) {
    createDistributionCenterDto.companyId = req['company_id']
      ? req['company_id']
      : createDistributionCenterDto.companyId;
    return this.distributionCenterService.create(createDistributionCenterDto);
  }

  @Patch(':id')
  @SetMetadata('action', 'update')
  async update(
    @Param() { id }: SingleIdDto,
    @Body() updateDistributionCenterDto: UpdateDistributionCenterDto,
    @Req() req: Request,
  ) {
    updateDistributionCenterDto._id = new Types.ObjectId(id);
    updateDistributionCenterDto.companyId = req['company_id']
      ? req['company_id']
      : updateDistributionCenterDto.companyId;
    return this.distributionCenterService.update(updateDistributionCenterDto);
  }
}
