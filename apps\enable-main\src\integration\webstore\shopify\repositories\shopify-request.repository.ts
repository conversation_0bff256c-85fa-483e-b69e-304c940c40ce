import {
  CollectionName,
  GenericRepository,
  ShopifyRequest,
  ShopifyRequestDocument,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@Injectable()
export class ShopifyRequestRepository extends GenericRepository<
  ShopifyRequestDocument,
  ShopifyRequest
> {
  constructor(
    @InjectModel(CollectionName.SHOPIFY_REQUEST)
    private shopifyRequestModel: Model<ShopifyRequestDocument, ShopifyRequest>,
  ) {
    super(shopifyRequestModel);
  }
}
