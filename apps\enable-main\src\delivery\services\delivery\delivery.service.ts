import { Injectable } from '@nestjs/common';
import { NationalAddressToGet } from '../../../delivery/dto/delivery.dto';
import { HelperService } from './../../../shared/services/helper/helper.service';
@Injectable()
export class DeliveryService {
  constructor(private helperService: HelperService) {}

  async get_national_address(nationalAddressToGet: NationalAddressToGet) {
    const nationalRequest =
      this.helperService.convertQatarNationalAddressToMapsCoordinate(
        nationalAddressToGet.zone_number,
        nationalAddressToGet.street_number,
        nationalAddressToGet.building_number,
      );

    return nationalRequest;
  }
}
