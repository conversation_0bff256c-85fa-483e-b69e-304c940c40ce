import { DataIndex } from '@app/shared-stuff';
import { ApiProperty } from '@nestjs/swagger';
import { StoreProvider } from '../enumerations/store-provider.enum';
import { Types } from 'mongoose';
import { StoreSortType } from '../enumerations/sort-type.enum';

export class IndexStoreDto extends DataIndex {
  @ApiProperty({
    required: false,
    type: String,
    enum: Object.keys(StoreSortType),
  })
  sortType: StoreSortType;

  @ApiProperty({
    required: false,
    type: String,
  })
  companyId: Types.ObjectId;

  @ApiProperty({
    required: false,
    type: String,
  })
  brandId: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: false,
    enum: Object.keys(StoreProvider),
  })
  provider: StoreProvider;
}
