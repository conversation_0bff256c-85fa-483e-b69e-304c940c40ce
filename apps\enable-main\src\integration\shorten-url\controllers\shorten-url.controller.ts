import {
  CreateShortenUrlDto,
  GenericExceptionFilter,
  ShortenUrlCodeDto,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Get,
  Inject,
  Param,
  Post,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBasi<PERSON><PERSON>uth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { ShortenUrlServiceInterface } from '../services/shorten-url.service.interface';

@Controller('shorten-url')
@ApiTags('Shorten URL')
@SetMetadata('module', 'shorten-url')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
export class ShortenUrlController {
  constructor(
    @Inject(ShortenUrlServiceInterface)
    private shortenService: ShortenUrlServiceInterface,
  ) {}

  @Post()
  @SetMetadata('action', 'create')
  async create(@Body() createShortenUrlDto: CreateShortenUrlDto) {
    return this.shortenService.create(createShortenUrlDto);
  }

  @Get(':code')
  @SetMetadata('action', 'get-details')
  async getDetails(@Param() shortenUrlCodeDto: ShortenUrlCodeDto) {
    return this.shortenService.getDetails(shortenUrlCodeDto);
  }
}
