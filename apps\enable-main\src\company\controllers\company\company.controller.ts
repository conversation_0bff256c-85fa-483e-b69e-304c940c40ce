import {
  CompanyDeliveryTeamToCreate,
  CompanyDeliveryTeamToEdit,
  CompanyStatusToUpdate,
  CompanyToIndex,
  CompanyToUpdate,
  CreateCompanyDto,
  responseCode,
  RestaurantBusyToBeSet,
  SingleIdDto,
  TransformInterceptor,
  UpdateCompanyLocalizationDto,
  UpdateLoyaltyProgramConfigDto,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  SetMetadata,
} from '@nestjs/common';
import { Patch, UseInterceptors } from '@nestjs/common/decorators';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { Request, Response } from 'express';
import { Types } from 'mongoose';
import { DeliveryConfigurationServiceInterface } from '../../../delivery/services/delivery-configuration/delivery-configuration.service.interface';
import { LoyaltyTierReadServiceInterface } from '../../../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.service.interface';
import { CurrentUserService } from '../../../shared/services/current-user/current-user.service';
import { HelperService } from '../../../shared/services/helper/helper.service';
import { CompanyService } from '../../services/company/company.service';

@Controller('company')
@ApiTags('Company')
@SetMetadata('module', 'company')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class CompanyController {
  constructor(
    private companyService: CompanyService,
    private helperTools: HelperService,
    @Inject(DeliveryConfigurationServiceInterface)
    private readonly deliveryConfigurationService: DeliveryConfigurationServiceInterface,
    @Inject(CurrentUserService)
    private readonly currentUserService: CurrentUserService,
    @Inject(LoyaltyTierReadServiceInterface)
    private readonly loyaltyTierService: LoyaltyTierReadServiceInterface,
  ) {}

  @Get()
  @SetMetadata('action', 'get_all')
  async index(
    @Query() comapnyToIndex: CompanyToIndex,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const selectedCompanies = await this.companyService.index(
        comapnyToIndex,
        req['companies'],
        req['company_id'],
      );
      return this.helperTools.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to get companies',
        { companies: selectedCompanies },
        res,
      );
    } catch (err) {
      return this.helperTools.handelError(err, res);
    }
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() companyToCreate: CreateCompanyDto,
    @Res() res: Response,
  ) {
    try {
      const createdCompany = await this.companyService.create(companyToCreate);
      return this.helperTools.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success to create companies',
        createdCompany,
        res,
      );
    } catch (err) {
      return this.helperTools.handelError(err, res);
    }
  }

  @Post('team')
  @SetMetadata('action', 'create_team')
  async create_team(
    @Body() companyDeliveryTeamToCreate: CompanyDeliveryTeamToCreate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      companyDeliveryTeamToCreate.company = req['company_id']
        ? req['company_id']
        : '';
      const createdDeliveryCompanyteam =
        await this.companyService.createTookanTeam(companyDeliveryTeamToCreate);
      return this.helperTools.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to create company delivery team',
        createdDeliveryCompanyteam,
        res,
      );
    } catch (err) {
      return this.helperTools.handelError(err, res);
    }
  }

  @Put('team/:id')
  @SetMetadata('action', 'update_team')
  async update_team(
    @Body() companyTeamToEdit: CompanyDeliveryTeamToEdit,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      companyTeamToEdit.company = req['company_id'] ? req['company_id'] : '';
      const editedDeliveryTeam =
        await this.companyService.editTookanTeam(companyTeamToEdit);
      return this.helperTools.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to update company delivery team',
        editedDeliveryTeam,
        res,
      );
    } catch (err) {
      return this.helperTools.handelError(err, res);
    }
  }

  @Put(':id')
  @SetMetadata('action', 'update')
  async update(@Body() companyToUpdate: CompanyToUpdate, @Res() res: Response) {
    try {
      const updatedCompany = await this.companyService.update(companyToUpdate);
      return this.helperTools.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'success to update all companies',
        updatedCompany,
        res,
      );
    } catch (err) {
      return this.helperTools.handelError(err, res);
    }
  }

  @Put('localization/update')
  @SetMetadata('action', 'updateCompanyLocalization')
  async updateCompanyLocalization(
    @Body() updateCompanyLocalizationDto: UpdateCompanyLocalizationDto,
    @Res() res: Response,
  ) {
    try {
      const company = await this.companyService.updateCompanyLocalization(
        updateCompanyLocalizationDto,
      );
      return this.helperTools.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'success to update company localization',
        company,
        res,
      );
    } catch (err) {
      return this.helperTools.handelError(err, res);
    }
  }

  @Patch(':id/loyalty-program-config')
  @SetMetadata('action', 'updateLoyaltyProgramConfig')
  async updateLoyaltyProgramConfig(
    @Param() { id }: SingleIdDto,
    @Body() updateLoyaltyProgramConfigDto: UpdateLoyaltyProgramConfigDto,
    @Res() res: Response,
  ) {
    try {
      this.currentUserService.validateAccessToCompany(id);
      const updatedCompany =
        await this.companyService.updateLoyaltyProgramConfig(
          id,
          updateLoyaltyProgramConfigDto,
        );
      return this.helperTools.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'success to update loyaltyProgramConfig',
        updatedCompany,
        res,
      );
    } catch (err) {
      return this.helperTools.handelError(err, res);
    }
  }

  @Post('status')
  @SetMetadata('action', 'update_status')
  async update_status(
    @Body() compantStatusToUpdate: CompanyStatusToUpdate,
    @Res() res: Response,
  ) {
    try {
      const updatedCompany = await this.companyService.change_status(
        compantStatusToUpdate,
      );
      return this.helperTools.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'Success to update company status',
        updatedCompany,
        res,
      );
    } catch (err) {
      return this.helperTools.handelError(err, res);
    }
  }

  @Delete(':id')
  @SetMetadata('action', 'delete')
  async remove(
    @Param('id') id: string,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const removedCompany = await this.companyService.remove(
        id,
        req['current'],
      );
      return this.helperTools.handelSuccessResponse(
        responseCode.SUCCESS_TO_REMOVE,
        'Success to remove company',
        removedCompany,
        res,
      );
    } catch (err) {
      return this.helperTools.handelError(err, res);
    }
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  async get_details(@Param('id') id: string, @Res() res: Response) {
    try {
      const companyDetails = await this.companyService.get_details(id);
      const companyAsJson = companyDetails.toJSON();
      const deliveryConfiguration =
        await this.deliveryConfigurationService.findByCompanyId(
          new Types.ObjectId(id),
        );
      companyAsJson['deliveryConfiguration'] = deliveryConfiguration;
      if (companyDetails.loyaltyProgramConfig?.baseTierEnrollmentCode) {
        companyAsJson['loyaltyProgramConfig']['baseTier'] =
          await this.loyaltyTierService.findByEnrollmentCode(
            companyDetails.loyaltyProgramConfig.baseTierEnrollmentCode,
          );
      }
      return this.helperTools.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'Success to get company details',
        companyAsJson,
        res,
      );
    } catch (err) {
      return this.helperTools.handelError(err, res);
    }
  }

  @Post('busy')
  @SetMetadata('action', 'setCompanyBusy')
  async setCompanyBusy(
    @Body() restaurantBusyToBeSet: RestaurantBusyToBeSet,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      restaurantBusyToBeSet.companyId = req['company_id'];
      restaurantBusyToBeSet.currentUser = req['current'];
      restaurantBusyToBeSet.branches = req['branches'];
      await this.companyService.setCompanyBusy(restaurantBusyToBeSet);
      return this.helperTools.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'Success To Change The Company Busy',
        {},
        res,
      );
    } catch (err) {
      return this.helperTools.handelError(err, res);
    }
  }

  @Get('getAll/currencies')
  @SetMetadata('action', 'get all currencies')
  async findAllCurrencies(@Res() res: Response) {
    try {
      const currencies = this.companyService.findAllCurrencies();
      return this.helperTools.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'Success to get all currencies',
        currencies,
        res,
      );
    } catch (err) {
      return this.helperTools.handelError(err, res);
    }
  }

  @Get('metabase/iframe-url')
  @SetMetadata('action', 'get-metabase-iframe-url')
  @UseInterceptors(TransformInterceptor)
  async getMetabaseIframeUrl(@Req() req: Request) {
    return await this.companyService.getMetabaseIframeUrl(req['company_id']);
  }
}
