// EBL-5388
// Dibsy | Payment Links

// <PERSON>ript to rename DibsySecretApiKey to dibsySecretApiKey
// Script to add integrationType with embedded as a default value
db.paymentconfigurations.find().forEach((doc) => {
  let updated = false;

  // Iterate over each configuration type (credit_cards, debit_cards, apple_pay)
  Object.keys(doc.configuration).forEach((key) => {
    const config = doc.configuration[key].configuration;

    // Check if DibsySecretApiKey exists and rename it to dibsySecretApiKey
    if (config.DibsySecretApiKey) {
      config.dibsySecretApiKey = config.DibsySecretApiKey;
      delete config.DibsySecretApiKey;
      updated = true;
    }

    // Check if dibsySecretApiKey exists and integrationType doesn't
    if (config.dibsySecretApiKey && !config.integrationType) {
      config.integrationType = 'embedded';
      updated = true;
    }
  });

  // Update the document if changes were made
  if (updated) {
    db.paymentconfigurations.updateOne({ _id: doc._id }, { $set: doc });
  }
});
