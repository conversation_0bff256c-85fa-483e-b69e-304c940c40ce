import { CollectionName, LoggerService, responseCode } from '@app/shared-stuff';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { FilterQuery, Model, Types } from 'mongoose';
import * as uuidApiKey from 'uuid-apikey';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { ApiKeyToCreate, ApiKeyToUpdate } from '../../../user/dto/apikey.dto';
import { ApiKeyDocument } from '../../../user/models/apikey.model';
import { SettingsService } from './../../../shared/services/settings/settings.service';
import { ApiKeyToIndex } from './../../dto/apikey.dto';

@Injectable()
export class ApikeyService {
  readonly logger = new LoggerService(ApikeyService.name);
  constructor(
    @InjectModel('ApiKey') private apiKeyModel: Model<ApiKeyDocument>,
    @Inject(forwardRef(() => 'BrandServiceInterface'))
    private brandService: BrandServiceInterface,
    private settingService: SettingsService,
  ) {}

  async index(filterData: ApiKeyToIndex) {
    const filter: FilterQuery<ApiKeyDocument> = { deletedAt: null };

    if (filterData.role_id) filter['roles._id'] = filterData.role_id;

    if (filterData.role_name) {
      const roles = filterData.role_name.split(',');
      filter['roles.name'] = { $in: roles };
    }

    if (filterData.brandId) filter['brand._id'] = filterData.brandId;

    if (filterData.search_key)
      filter.name = { $regex: `.*${filterData.search_key}.*` };

    if (filterData.status) filter.status = filterData.status;

    if (filterData.company)
      filter.company = new Types.ObjectId(filterData.company);

    if (filterData.branch) filter.branch = filterData.branch;

    if (filterData.id) filter.id = filterData.id;

    let query = this.apiKeyModel.find(filter);

    if (filterData.limit) {
      query = query
        .skip(filterData.limit * (filterData.offset ?? 0))
        .limit(filterData.limit);
    }

    return query.exec();
  }

  async create(keyToCreate: ApiKeyToCreate) {
    if (keyToCreate.brand) {
      const brand = await this.brandService.findById(keyToCreate.brand._id);
      if (!brand) {
        throw {
          code: responseCode.MISSING_DATA,
          statusCode: 422,
          message: 'please provide correct brand',
        };
      }

      keyToCreate.brand = {
        ...brand,
        _id: new Types.ObjectId(brand._id),
      };
    }

    keyToCreate.code = (uuidApiKey as any).create().apiKey;
    keyToCreate.secret = (uuidApiKey as any).create().uuid;

    keyToCreate.expire_date = moment(
      keyToCreate.expire_date,
      'YYYY-MM-DD hh:mm',
    ).toDate() as any;

    keyToCreate['company'] = new Types.ObjectId(keyToCreate.company) as any;
    keyToCreate['roles'] = ['5f172ce60564f853cc1eca86'];

    keyToCreate.roles.map((x) => {
      return new Types.ObjectId(x);
    });

    const createdKey = new this.apiKeyModel(keyToCreate);
    await createdKey.save();
    return createdKey;
  }

  async update(keyToUpdate: ApiKeyToUpdate) {
    const brand = await this.brandService.findById(keyToUpdate.brand._id);
    if (!brand) {
      throw {
        code: responseCode.MISSING_DATA,
        statusCode: 422,
        message: 'please provide correct brand',
      };
    }

    keyToUpdate.brand = {
      ...brand,
      _id: new Types.ObjectId(brand._id),
    };

    keyToUpdate['company'] = new Types.ObjectId(keyToUpdate['company']) as any;

    keyToUpdate.roles.map((x) => {
      return new Types.ObjectId(x);
    });

    keyToUpdate.expire_date = moment(
      keyToUpdate.expire_date,
      'YYYY-MM-DD hh:mm',
    ).toDate() as any;

    const apiKey = await this.apiKeyModel.findOne({ _id: keyToUpdate._id });

    await this.apiKeyModel.updateOne({ _id: apiKey._id }, keyToUpdate);

    return apiKey;
  }

  async get_details(id) {
    const selectedApiKey = await this.apiKeyModel
      .findById(id)
      .populate('roles');
    return selectedApiKey;
  }

  async remove(id: string) {
    const deletedApiKey = await this.apiKeyModel.findOne({ _id: id });
    deletedApiKey.deletedAt = moment().utc().toDate();
    await deletedApiKey.save();
    return deletedApiKey;
  }

  async get_me(code) {
    this.logger.log(`Request Created With ApiKey: ${code}`);
    const selectedApiKey = await this.apiKeyModel
      .findOne({ code: code })
      .populate({
        path: 'roles',
        populate: { path: 'permissions', model: CollectionName.PERMISSIONS },
        model: CollectionName.ROLES,
      })
      .populate({
        path: 'company',
        select:
          '-skipCashKeyId -skipCashKeySecret -skipCashKeyClientId -skipCashWebHookKey -terminal_id -terminal_password -branches -savedAddress',
      })
      .populate('branch')
      .exec();
    if (!selectedApiKey) {
      throw {
        code: responseCode.UN_AUTHENTICATED,
        message: 'apikey is not valid',
        statusCode: 401,
      };
    }
    if (selectedApiKey.status == 'not_active') {
      //TODO: throw an error
    }
    // Logger.log(moment(selectedApiKey.expire_date).toString());
    // Logger.log(moment().toString());
    // if (moment(selectedApiKey.expire_date).isBefore(moment())) {
    //     throw { code: responseCode.APIKEY_EXPIRED, message: "expired Api key", statusCode: 401 }
    // }
    return selectedApiKey;
  }

  async validateApiKey(apikey: string, domain: string) {
    const selectedApiKey = await this.apiKeyModel.findOne({ code: apikey });
    if (!selectedApiKey) {
      throw {
        code: responseCode.UN_AUTHENTICATED,
        message: 'apikey is not valid',
        statusCode: 401,
      };
    }
    const setting = await this.settingService.getSettings();
    const jsonData = selectedApiKey.toJSON();
    jsonData['enableDeliveryPluginVersion'] = setting.deliveryPluginVersion;
    jsonData['enablePaymentPluginVersion'] = setting.paymentPluginVersion;
    //TODO: Validate the coming domain is in the domain list or not
    //TODO: check the expire date for the apikey
    return jsonData;
  }
}
