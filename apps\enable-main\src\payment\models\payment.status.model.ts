import {
  CollectionName,
  CurrentUser,
  PaymentStatusEnum,
} from '@app/shared-stuff';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type PaymentStatusDocument = PaymentStatus & Document;

@Schema({ timestamps: true })
export class PaymentStatus {
  @Prop({
    enum: PaymentStatusEnum,
    type: String,
    required: true,
  })
  old_status: PaymentStatusEnum;

  @Prop({
    enum: PaymentStatusEnum,
    type: String,
    required: true,
  })
  new_status: PaymentStatusEnum;

  @Prop({
    type: Types.ObjectId,
    ref: CollectionName.PAYMENT,
    required: true,
  })
  payment: string;

  @Prop({
    type: CurrentUser,
    required: false,
  })
  updatedBy: CurrentUser;

  @Prop({
    type: CurrentUser,
    required: false,
  })
  createdBy: CurrentUser;

  @Prop({
    type: CurrentUser,
    required: false,
  })
  assignedTo: CurrentUser;

  @Prop({
    type: CurrentUser,
    required: false,
  })
  deletedBy: CurrentUser;

  @Prop({
    type: Date,
    required: false,
  })
  deletedAt: Date;
}

export const PaymentStatusSchema = SchemaFactory.createForClass(PaymentStatus);
