// Add minimumCartValueForDiscount to embedded customer loyalty tiers

db.customers.bulkWrite(
  db.customers
    .find(
      {
        loyaltyTier: { $ne: null },
        'loyaltyTier.minimumCartValueForDiscount': { $exists: false },
      },
      { _id: 1, loyaltyTier: 1 },
    )
    .toArray()
    .map(({ _id, loyaltyTier }) => {
      const tier = db.loyaltytiers.findOne(
        { _id: loyaltyTier._id },
        { minimumCartValueForDiscount: 1 },
      );
      return {
        updateOne: {
          filter: { _id },
          update: {
            $set: {
              'loyaltyTier.minimumCartValueForDiscount':
                tier.minimumCartValueForDiscount,
            },
          },
        },
      };
    }),
  { ordered: false },
);
