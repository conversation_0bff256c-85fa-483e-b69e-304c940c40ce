import { CountryDialCode } from '@app/shared-stuff';
import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { Types } from 'mongoose';

export class GetCustomerIntegrationDetailsDto {
  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  uniqueIdentifier?: string;

  @ApiProperty({
    type: String,
    enum: CountryDialCode,
    required: false,
  })
  @IsOptional()
  @IsString()
  countryCode?: CountryDialCode;

  @ApiHideProperty()
  companyId: Types.ObjectId;
}
