import {
  GenericExceptionFilter,
  GetTriggerNamesDto,
  GetTriggerNamesResponseDto,
  IndexTriggerDto,
  MicroserviceCommunicationService,
  TransformInterceptor,
  TriggerClient,
} from '@app/shared-stuff';
import {
  Controller,
  Get,
  Inject,
  OnModuleDestroy,
  OnModuleInit,
  Query,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';

@Controller('trigger')
@ApiTags('Notification-Trigger')
@SetMetadata('module', 'triggers')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
export class TriggerController implements OnModuleInit, OnModuleDestroy {
  constructor(
    @Inject('enable-main-notification-producer') private client: ClientProxy,
    private microserviceCommunicationService: MicroserviceCommunicationService,
  ) {}

  async onModuleDestroy() {
    await this.microserviceCommunicationService.disconnect(this.client);
  }

  async onModuleInit() {
    await this.microserviceCommunicationService.connect(this.client);
  }

  @Get()
  @SetMetadata('action', 'index')
  public async index(@Query() indexTriggerDto: IndexTriggerDto) {
    indexTriggerDto.client = TriggerClient.ENABLE_MAIN;
    return this.microserviceCommunicationService.produceAndWaitForResponse<
      IndexTriggerDto,
      any
    >(this.client, 'index.trigger.request', indexTriggerDto);
  }

  @Get('names')
  @ApiResponse({ type: GetTriggerNamesResponseDto, isArray: true })
  @SetMetadata('action', 'names')
  public async getTriggerNames(
    @Query() getTriggerNamesDto: GetTriggerNamesDto,
  ) {
    return this.microserviceCommunicationService.produceAndWaitForResponse<
      GetTriggerNamesDto,
      GetTriggerNamesResponseDto[]
    >(this.client, 'names.trigger.request', getTriggerNamesDto);
  }
}
