import {
  CreateLocationItemDto,
  EmbeddedLocationItemDto,
  GetAllLocationItemDto,
  HelperSharedServiceInterface,
  IHelperSharedService,
  IndexResultDto,
  LocationItem,
  LocationItemDocument,
  LocationItemDocumentToEmbedded,
  LocationItemType,
  LoggerService,
  omit,
  UpdateLocationItemDto,
} from '@app/shared-stuff';
import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { plainToInstance } from 'class-transformer';
import * as H3 from 'h3-js';
import { FilterQuery, PipelineStage, Types } from 'mongoose';
import { LocationItemRepositoryInterface } from '../../repositories/location-item/location-item-repository.interface';
import { LocationItemServiceInterface } from './location-item-service.interface';

@Injectable()
export class LocationItemService implements LocationItemServiceInterface {
  private readonly loggerService = new LoggerService(LocationItemService.name);

  constructor(
    @Inject(LocationItemRepositoryInterface)
    private locationItemRepository: LocationItemRepositoryInterface,
    @Inject(IHelperSharedService)
    private helperSharedService: HelperSharedServiceInterface,
    private eventEmitter: EventEmitter2,
  ) {}

  async findAll(
    getAllLocationItemDto: GetAllLocationItemDto,
  ): Promise<IndexResultDto<LocationItem>[]> {
    const pipeline: PipelineStage[] = [];

    this.addMatchStage(pipeline, getAllLocationItemDto);
    this.addSortStage(pipeline);

    this.addPaginationStage(pipeline, getAllLocationItemDto);
    return (await this.locationItemRepository.aggregate(pipeline)) as [
      IndexResultDto<LocationItem>,
    ];
  }

  async create(
    createLocationItemDto: CreateLocationItemDto,
  ): Promise<LocationItemDocument> {
    const embeddedParent: EmbeddedLocationItemDto =
      await this.getEmbeddedParent(createLocationItemDto.parentId);

    const locationItem = plainToInstance(LocationItem, {
      ...createLocationItemDto,
      parent: embeddedParent,
      ancestors: await this.getAncestors(embeddedParent?._id),
    });

    if (
      createLocationItemDto.type === LocationItemType.AREA &&
      locationItem.parent.type === LocationItemType.COUNTRY
    ) {
      await this.locationItemRepository.findOneAndUpdate(
        {
          _id: locationItem.parent._id,
        },
        { isEditable: false },
      );
    }
    const createdLocationItem =
      await this.locationItemRepository.create(locationItem);

    await this.updateChildren(
      createLocationItemDto.childrenIds,
      createdLocationItem,
    );

    return createdLocationItem;
  }

  async update(
    updateLocationItemDto: UpdateLocationItemDto,
  ): Promise<LocationItemDocument> {
    let locationItem = await this.findOne(updateLocationItemDto._id);
    const embeddedParent: EmbeddedLocationItemDto =
      await this.getEmbeddedParent(updateLocationItemDto.parentId);

    const parent = embeddedParent ? embeddedParent : locationItem.parent;

    const locationItemDto = plainToInstance(LocationItem, {
      ...locationItem.toJSON(),
      ...updateLocationItemDto,
      parent,
      ancestors: await this.getAncestors(parent?._id),
    });

    locationItem = await this.locationItemRepository.findOneAndUpdate(
      { _id: locationItem.id },
      omit(locationItemDto, ['_id' as any]),
    );

    if (
      updateLocationItemDto.childrenIds &&
      updateLocationItemDto.childrenIds.length
    ) {
      await this.locationItemRepository.updateMany(
        { 'parent._id': locationItem._id },
        { $unset: { parent: 1 } },
      );
      await this.updateChildren(
        updateLocationItemDto.childrenIds,
        locationItem,
      );
    } else {
      await this.locationItemRepository.updateMany(
        { 'parent._id': locationItem._id },
        { $set: { parent: LocationItemDocumentToEmbedded([locationItem])[0] } },
      );
    }

    this.eventEmitter.emit('locationItem.updated', locationItem);
    return locationItem;
  }

  async findOne(id: Types.ObjectId): Promise<LocationItemDocument> {
    const locationItem = await this.locationItemRepository.findById(id);
    if (!locationItem) {
      throw new NotFoundException(`Location item with id ${id} not found`);
    }
    return locationItem;
  }

  async findOneByNameAndType(name: string, type: LocationItemType) {
    return this.locationItemRepository.findOne({
      name: { $regex: name, $options: 'i' },
      type,
    });
  }

  async delete(id: Types.ObjectId): Promise<void> {
    const locationItem = await this.findOne(id);
    await this.locationItemRepository.remove(locationItem.id);
    await this.locationItemRepository.updateMany(
      { 'parent._id': locationItem._id },
      { $unset: { parent: 1 }, $pull: { ancestors: locationItem._id } },
    );
    if (locationItem.type === LocationItemType.AREA) {
      await this.handleCountryEnableModification(locationItem);
    }
  }

  private async handleCountryEnableModification(
    locationItem: LocationItemDocument,
  ) {
    let parentItem = await this.findOne(locationItem?.parent?._id);

    if (parentItem.type === LocationItemType.CITY) {
      parentItem = await this.findOne(parentItem?.parent?._id);
    }
    if (parentItem.type === LocationItemType.COUNTRY) {
      const itemChildren = await this.findAll({
        includeChildren: true,
        parentId: parentItem._id,
      });

      const isCountryHasArea = itemChildren[0].paginatedResult.some(
        (childItem) => childItem.type === LocationItemType.AREA,
      );

      if (!isCountryHasArea) {
        parentItem.isEditable = true;
        await parentItem.save();
      }
    }
  }

  private async getEmbeddedParent(
    parentId?: Types.ObjectId,
  ): Promise<EmbeddedLocationItemDto | undefined> {
    if (!parentId) {
      return undefined;
    }

    const parentItem = await this.findOne(parentId);
    return LocationItemDocumentToEmbedded([parentItem])[0];
  }

  private async updateChildren(
    childrenIds: Types.ObjectId[] | undefined,
    parentItem: LocationItemDocument,
  ): Promise<void> {
    if (!childrenIds || childrenIds.length === 0) {
      return;
    }

    const embeddedParent = LocationItemDocumentToEmbedded([parentItem])[0];
    await this.locationItemRepository.updateMany(
      { _id: { $in: childrenIds } },
      { $set: { parent: embeddedParent } },
    );
  }

  private addMatchStage(
    pipeline: PipelineStage[],
    getAllLocationItemDto: GetAllLocationItemDto,
  ) {
    const match: FilterQuery<LocationItem> = {};
    if (getAllLocationItemDto.type) match.type = getAllLocationItemDto.type;
    if (getAllLocationItemDto.parentId) {
      if (!getAllLocationItemDto.ancestorsSearch)
        match['parent._id'] = getAllLocationItemDto.parentId;
      else match['ancestors'] = getAllLocationItemDto.parentId;
    }
    if (
      getAllLocationItemDto.lat &&
      getAllLocationItemDto.lng &&
      getAllLocationItemDto.h3Resolution
    ) {
      match['h3Indexes'] = H3.latLngToCell(
        Number(getAllLocationItemDto.lat),
        Number(getAllLocationItemDto.lng),
        Number(getAllLocationItemDto.h3Resolution),
      );
    }
    if (getAllLocationItemDto.search_key) {
      match['name'] = {
        $regex: getAllLocationItemDto.search_key,
        $options: 'i',
      };
    }

    pipeline.push({ $match: match });
  }

  private addSortStage(pipeline: PipelineStage[]) {
    pipeline.push({ $sort: { createdAt: -1 } });
  }

  private addPaginationStage(
    pipeline: PipelineStage[],
    getAllLocationItemDto: GetAllLocationItemDto,
  ) {
    const extraPaginatedResultPipeline: PipelineStage.FacetPipelineStage[] = [];

    if (getAllLocationItemDto.includeChildren) {
      // add the children list as a normal staging in a facet
      extraPaginatedResultPipeline.push({
        $lookup: {
          from: 'LocationItems',
          localField: '_id',
          foreignField: 'parent._id',
          as: 'children',
        },
      });
    }

    pipeline.push(
      this.helperSharedService.createPaginationStage(
        getAllLocationItemDto.offset,
        getAllLocationItemDto.limit,
        extraPaginatedResultPipeline,
      ),
    );
  }

  private async getAncestors(
    parentId: Types.ObjectId,
  ): Promise<Types.ObjectId[]> {
    if (!parentId) return [];
    const parent = await this.findOne(parentId);
    return [...parent.ancestors, parentId];
  }
}
