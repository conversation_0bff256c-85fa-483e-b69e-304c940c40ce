import {
  CompanyDocument,
  CreateCustomerDto,
  GenericTriggerModel,
  Language,
  LanguageCode,
  MenuItemDocument,
  MenuItemToPromote,
  TriggerModule,
} from '@app/shared-stuff';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CompanyService } from '../../../company/services/company/company.service';
import { CustomerReadServiceInterface } from '../../../customer/modules/customer-read/customer-read.service.interface';
import { CustomerWriteServiceInterface } from '../../../customer/modules/customer-write/customer-write.service.interface';
import { MenuCategoryService } from '../menu-category/menu-category.service';

@Injectable()
export class MenuItemNotificationService {
  constructor(
    private menuCategoryService: MenuCategoryService,
    private configService: ConfigService,
    private companyService: CompanyService,
    @Inject(CustomerReadServiceInterface)
    private customerReadService: CustomerReadServiceInterface,
    @Inject(CustomerWriteServiceInterface)
    private customerWriteService: CustomerWriteServiceInterface,
  ) {}

  async populateGenericTriggerModel(
    menuItem: MenuItemDocument,
    menuItemToPromote: MenuItemToPromote,
  ): Promise<GenericTriggerModel> {
    const replacements = await this.getAllPaymentReplacements(menuItem);
    const company = await this.companyService.get_details(
      menuItem.company._id ? menuItem.company._id.toHexString() : undefined,
    );
    if (!company) {
      throw new BadRequestException(
        'Please Provide Menu Item assigned to company ID',
      );
    }
    const customer = await this.getCustomer(menuItemToPromote, company);

    return {
      companyId: company._id.toString(),
      branchId: '',
      brandId: menuItemToPromote.brandId.toHexString(),
      customerId: customer._id,
      countryCode: menuItemToPromote.customerCountryCode,
      createdBy: menuItem.createdBy,
      giftRecipientUser: undefined,
      isGift: false,
      senderId: company.senderId ? company.senderId : undefined,
      emailSenderId: company.email,
      triggerModule: TriggerModule.MENU_ITEM,
      replacements: replacements,
      language:
        customer.language === Language.english
          ? LanguageCode.en
          : LanguageCode.ar,
      context: {
        customer: {
          ...customer.toJSON(),
          _id: customer._id,
        },
      },
    };
  }

  private async getCustomer(
    menuItemToPromote: MenuItemToPromote,
    company: CompanyDocument,
  ) {
    const customer = await this.customerReadService.findOne(
      menuItemToPromote.customerPhone,
      company._id,
    );
    if (!customer) {
      const createCustomerDto: CreateCustomerDto = {
        full_name: 'anonymous customer',
        first_name: 'anonymous ',
        last_name: 'customer',
        phone: menuItemToPromote.customerPhone,
        country_code: menuItemToPromote.customerCountryCode,
        company: company._id,
        email: '',
        contact_channel: undefined,
        location: undefined,
        createdBy: undefined,
        firstBrandOrderdId: null,
        firstBranchOrderdId: null,
      };
      return await this.customerWriteService.create(
        createCustomerDto,
        menuItemToPromote.currentUser,
      );
    } else {
      return customer;
    }
  }

  private async getAllPaymentReplacements(menuItem: MenuItemDocument) {
    const menuCategory = await this.menuCategoryService.getDetails(
      menuItem.menuCategory.toString(),
    );
    const replacements = {
      menuItemName: menuItem.nameEn,
      menuItemPromotionLink:
        this.configService.get('MENU_ITEM_PROMOTION_LINK') +
        '/' +
        menuItem._id.toString(),
      menuCategory: menuCategory ? menuCategory.name : '',
    };
    return replacements;
  }
}
