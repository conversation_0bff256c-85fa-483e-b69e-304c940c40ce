import { PrivilegeToIndx } from '../../../rbac/dto/privilege.dto';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  SetMetadata,
} from '@nestjs/common';
import { PrivilegesService } from '../../services/privileges/privileges.service';
import { responseCode } from '@app/shared-stuff';
import { Request, Response } from 'express';
import { PrivilegeToCreate, PrivilegeToUpdate } from '../../dto/privilege.dto';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { HelperService } from '../../../shared/services/helper/helper.service';

@ApiTags('Privilege')
@Controller('privileges')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@SetMetadata('module', 'privilege')
export class PrivilegesController {
  constructor(
    private privilegesService: PrivilegesService,
    private helperService: HelperService,
  ) {}
  @Get()
  @SetMetadata('action', 'get_all')
  async findAll(
    @Query() dataIndex: PrivilegeToIndx,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      dataIndex.user_data = req['user'];
      const selectedPrivileges = await this.privilegesService.index(dataIndex);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success for getting privilege details',
        selectedPrivileges,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
  @Get(':id')
  @SetMetadata('action', 'get_details')
  async findDetails(@Param('id') id: string, @Res() res: Response) {
    try {
      const selectedPrivilege = await this.privilegesService.get_details(id);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'success for getting privilege by id',
        selectedPrivilege,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() privilegeToCreate: PrivilegeToCreate,
    @Res() res: Response,
  ) {
    try {
      const createdPrivilege =
        await this.privilegesService.create(privilegeToCreate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success for creating privilege',
        createdPrivilege,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
  @Put(':id')
  @SetMetadata('action', 'update')
  async update(
    @Body() privilegeToUpdate: PrivilegeToUpdate,
    @Res() res: Response,
  ) {
    try {
      const updatedPrivilege =
        await this.privilegesService.update(privilegeToUpdate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'success for update privilege',
        updatedPrivilege,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Delete(':id')
  @SetMetadata('action', 'remove')
  async remove(@Param('id') id: string, @Res() res: Response) {
    try {
      const removedPrivilege = await this.privilegesService.remove('id');
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_REMOVE,
        'success for remove privilege',
        removedPrivilege,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
}
