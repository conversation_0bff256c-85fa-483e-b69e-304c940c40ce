import {
  CurrentUser,
  DeliveryMethod,
  OrderDeliveryAction,
  OrderDocument,
  OrderLogActionEnum,
  OrderPaymentStatus,
  OrderStatusEnum,
  OrderTransitionTrigger,
  responseCode,
} from '@app/shared-stuff';
import { BadRequestException, Inject } from '@nestjs/common';
import { CompanyService } from '../../../../company/services/company/company.service';
import { CustomerOrderServiceInterface } from '../../../../customer/modules/customer-order/customer-order.service.interface';
import { OrderLogServiceInterface } from '../../interfaces/order-log.service.interface';
import { OrderNotificationService } from '../../order-notification/order-notification.service';
import { OrderStatusServiceInterface } from '../order-status.interface';

export class CompletedOrderService implements OrderStatusServiceInterface {
  transitionalStatuses = [
    OrderStatusEnum.PREPARING,
    OrderStatusEnum.PENDING_PICKUP,
    OrderStatusEnum.IN_ROUTE,
  ];
  transitionalTrigger = [
    OrderTransitionTrigger.FIRST_PARTY_DELIVERED,
    OrderTransitionTrigger.MANUAL,
    OrderTransitionTrigger.THIRD_PARTY_DELIVERED,
    OrderTransitionTrigger.DELIVERECT_STATUS_UPDATE,
    OrderTransitionTrigger.MICROS_CLOSED_CHECK,
  ];

  constructor(
    @Inject('OrderLogServiceInterface')
    private readonly orderLogService: OrderLogServiceInterface,
    @Inject(CustomerOrderServiceInterface)
    private customerOrderService: CustomerOrderServiceInterface,
    private orderNotificationService: OrderNotificationService,
    private companyService: CompanyService,
  ) {}

  validateStatus(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
  ): boolean {
    return (
      this.ValidateTransition(order, orderTransitionTrigger) ||
      this.validatePreCondition(order)
    );
  }

  private ValidateTransition(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
  ): boolean {
    if (
      this.transitionalTrigger.includes(orderTransitionTrigger) &&
      this.transitionalStatuses.includes(order.status)
    )
      return true;

    throw new BadRequestException(
      "Can't Change From " + order.status + ' To completed',
      responseCode.STATUS_NOT_VALID.toString(),
    );
  }

  private validatePreCondition(order: OrderDocument): boolean {
    if (this.checkPreConditions(order)) return true;

    throw new BadRequestException(
      "Can't change From " +
        order.status +
        ' to  completed' +
        'The order with ID ' +
        order._id,
      responseCode.STATUS_NOT_VALID.toString(),
    );
  }

  private checkPreConditions(order: OrderDocument): boolean {
    if (
      order.status === OrderStatusEnum.PENDING_PICKUP &&
      (order.delivery_action === OrderDeliveryAction.IN_STORE_PICKUP ||
        order.delivery_action == OrderDeliveryAction.DELIVERY_LOCATION)
    )
      return true;
    if (
      order.status === OrderStatusEnum.PREPARING &&
      order.delivery_action === OrderDeliveryAction.IN_STORE_PICKUP
    )
      return true;

    if (
      (order.status === OrderStatusEnum.PREPARING &&
        (order.deliveryMethod === DeliveryMethod.BRANCH_DRIVERS ||
          order.deliveryMethod === DeliveryMethod.COMPANY_DRIVERS)) ||
      order.deliveryMethod === DeliveryMethod.THIRD_PARTY
    )
      return true;
  }

  async applyPostFunction(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
    oldStatus: OrderStatusEnum,
    user: CurrentUser,
  ) {
    order.payment_status = OrderPaymentStatus.COMPLETED;
    order.delayTag = undefined;
    await order.save();
    await this.customerOrderService.completeCustomerOrder(order);
    if (order.is_gift === true) {
      await this.orderNotificationService.onGiftOrderCompleted(order);
      await this.orderLogService.saveOrderLog(
        order,
        { oldStatus: oldStatus },
        {
          newStatus: OrderStatusEnum.COMPLETED,
          trigger: orderTransitionTrigger,
        },
        OrderLogActionEnum.ON_GIFT_ORDER_COMPLETED,
        user,
      );
    } else {
      await this.orderNotificationService.onOrderCompleted(order);
      await this.orderLogService.saveOrderLog(
        order,
        { oldStatus: oldStatus },
        {
          newStatus: OrderStatusEnum.COMPLETED,
          trigger: orderTransitionTrigger,
        },
        OrderLogActionEnum.ON_ORDER_COMPLETED,
        user,
      );
    }
  }
}
