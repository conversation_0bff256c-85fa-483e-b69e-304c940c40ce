import { SharedStuffModule } from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { BrandModule } from '../../../brand/brand.module';
import { CompanyModule } from '../../../company/company.module';
import { PunchCardReadModule } from '../../../punch-card/modules/punch-card-read/punch-card-read.module';
import { PunchCardModule } from '../../../punch-card/punch-card.module';
import { StorageModule } from '../../../storage/storage.module';
import { CustomerIndexModule } from '../customer-index/customer-index.module';
import { CustomerLoyaltyMemberModule } from '../customer-loyalty-member/customer-loyalty-member.module';
import { CustomerLoyaltyModule } from '../customer-loyalty/customer-loyalty.module';
import { CustomerPunchCardModule } from '../customer-punch-card/customer-punch-card.module';
import { CustomerRepositoryModule } from '../customer-repository/customer-repository.module';
import { CustomerIOService } from './customer-io.service';
import { CustomerIOServiceInterface } from './customer-io.service.interface';

@Module({
  providers: [
    { provide: CustomerIOServiceInterface, useClass: CustomerIOService },
  ],
  imports: [
    ConfigModule,
    CompanyModule,
    CustomerRepositoryModule,
    CustomerIndexModule,
    BrandModule,
    PunchCardModule,
    CustomerLoyaltyModule,
    CustomerLoyaltyMemberModule,
    CustomerPunchCardModule,
    SharedStuffModule,
    PunchCardReadModule,
    StorageModule,
  ],
  exports: [CustomerIOServiceInterface],
})
export class CustomerIoModule {}
