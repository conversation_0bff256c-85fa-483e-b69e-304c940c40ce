// EBL-3960 Ordable Link Per Store
// Migrate old flat structure to new `ordableStores` field

const getTierUpdate = (store, tier) => [
  {
    $set: {
      ordableStores: {
        [store._id.toHexString()]: {
          ordableId: tier.ordableId,
          ordableCustomerIds: db.customers.distinct('ordableId', {
            ordableTierId: tier.ordableId,
          }),
        },
      },
    },
  },
  { $unset: ['ordableId'] },
];

const getCouponUpdate = (store, coupon) => [
  {
    $set: {
      ordableStores: {
        [store._id.toHexString()]: {
          ordableId: coupon.ordableId,
          ordableCustomerIds: db.customers.distinct('ordableId', {
            ordableCouponIds: coupon.ordableId,
          }),
        },
      },
    },
  },
  { $unset: ['ordableId'] },
];

const getCustomerUpdate = (store) => [
  {
    $set: {
      ordableStores: {
        [store._id.toHexString()]: {
          ordableId: '$ordableId',
          ordableLink: '$ordableLink',
          ordableBrands: store.brands,
        },
      },
    },
  },
  {
    $unset: [
      'ordableId',
      'ordableLink',
      'ordableCouponIds',
      'ordableBrandIds',
      'ordableTierId',
      'ordablePromotions',
    ],
  },
];

db.customers
  .distinct('company', {
    ordableId: { $exists: true },
  })
  .forEach((companyId) => {
    const store = db.stores.findOne({ companyId });
    if (!store) console.log(`Store not found for company ${companyId}`);

    db.loyaltytiers
      .find({ companyId, ordableId: { $exists: true } })
      .forEach((tier) =>
        db.loyaltytiers.updateOne(
          { _id: tier._id },
          getTierUpdate(store, tier),
        ),
      );

    db.coupons
      .find({ companyId, ordableId: { $exists: true } })
      .forEach((coupon) =>
        db.coupons.updateOne(
          { _id: coupon._id },
          getCouponUpdate(store, coupon),
        ),
      );

    db.customers.updateMany(
      { company: companyId, ordableId: { $exists: true } },
      getCustomerUpdate(store),
    );
  });
