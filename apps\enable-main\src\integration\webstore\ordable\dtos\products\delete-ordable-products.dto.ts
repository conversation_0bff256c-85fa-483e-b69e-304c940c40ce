import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';
import { OrdableProductsReferenceBy } from '../../enums/ordable-products-reference-by.enum';

export class DeleteOrdableProductsDto {
  @ApiProperty({
    type: String,
    enum: OrdableProductsReferenceBy,
    default: OrdableProductsReferenceBy.product_id,
    required: false,
  })
  reference_by: OrdableProductsReferenceBy;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  id: string | number;

  @ApiProperty({
    type: String,
    required: false,
    description:
      ' If specified, the product will be deleted from a specific branch. Otherwise, it will be deleted from the master list',
  })
  @IsString()
  branch_id: string;
}
