import {
  CompanyDocument,
  LoggerService,
  OrderDocument,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { MicrosService } from '../services/micros.service';

import { Types } from 'mongoose';
import { CompanyService } from '../../../../company/services/company/company.service';

@Injectable()
export class MicrosListener {
  private readonly logger = new LoggerService(MicrosListener.name);

  constructor(
    private readonly microsService: MicrosService,
    private readonly companyService: CompanyService,
  ) {}

  @OnEvent('order.created')
  async onOrderCreated(order: OrderDocument, company: CompanyDocument) {
    this.logger.log(
      `[Micros] order created event fired with order code : ${order.code}`,
    );
    if (
      company &&
      company.hasMicros === true &&
      company.integratorChannelConfig.micros &&
      company.integratorChannelConfig.micros.authBaseUrl &&
      company.integratorChannelConfig.micros.checkBaseUrl &&
      typeof order.branch !== 'string' &&
      order.branch?.integratorChannelConfig.micros
    )
      await this.microsService.createCheck(
        order,
        company.integratorChannelConfig.micros,
        typeof order.branch !== 'string'
          ? order.branch?.integratorChannelConfig.micros
          : undefined,
      );
  }

  @OnEvent('payment.completed')
  async onPaymentCompleted(order: OrderDocument) {
    this.logger.log(
      `[Micros] payment completed event fired with micros check ref ${order?.microsOrderId} and order code ${order.code} `,
    );
    const companyId = order.company['_id']
      ? order.company['_id']
      : new Types.ObjectId(order.company);
    const company = await this.companyService.findById(companyId);
    if (
      order.microsOrderId &&
      company.hasMicros &&
      company?.integratorChannelConfig.micros &&
      company?.integratorChannelConfig.micros.authBaseUrl &&
      company?.integratorChannelConfig.micros.checkBaseUrl &&
      typeof order.branch !== 'string' &&
      order?.branch?.integratorChannelConfig.micros
    )
      await this.microsService.updateCheck(
        order,
        company.integratorChannelConfig.micros,
        order?.branch?.integratorChannelConfig.micros,
      );
  }
}
