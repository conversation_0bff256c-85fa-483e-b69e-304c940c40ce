import {
  ActionGroup,
  CreateActionGroupDto,
  UpdateActionGroupDto,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface ActionGroupServiceInterface {
  create(createActionGroupDto: CreateActionGroupDto): Promise<ActionGroup>;
  update(updateActionGroupDto: UpdateActionGroupDto): Promise<ActionGroup>;
  delete(groupId: Types.ObjectId): Promise<number>;
}

export const ActionGroupServiceInterface = Symbol(
  'ActionGroupServiceInterface',
);
