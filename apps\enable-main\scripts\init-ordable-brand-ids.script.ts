// EBL-3928 Wallet Pass Page Updates - Expose Ordable Brand ID
// Initialize ordableBrandIds for existing customers.

db.companies.find({ hasLoyaltyProgram: true }).forEach((company) => {
  const stores = db.stores.find({ companyId: company._id }).toArray();
  if (!stores || stores.length === 0) return;

  const brandIds = stores[0].brands.map((brand) => brand._id);

  db.customers.updateMany(
    { company: company._id, ordableId: { $exists: true } },
    { $set: { ordableBrandIds: brandIds } },
  );
});
