import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';

import { CompletedPunchCardModule } from '../../../punch-card/modules/completed-punch-card/completed-punch-card.module';
import { PunchCardReadModule } from '../../../punch-card/modules/punch-card-read/punch-card-read.module';
import { CustomerReadModule } from '../customer-read/customer-read.module';
import { CustomerRepositoryModule } from '../customer-repository/customer-repository.module';
import { CustomerPunchCardService } from './customer-punch-card.service';
import { CustomerPunchCardServiceInterface } from './customer-punch-card.service.interface';

@Module({
  providers: [
    {
      provide: CustomerPunchCardServiceInterface,
      useClass: CustomerPunchCardService,
    },
  ],
  imports: [
    EventEmitterModule,
    CustomerReadModule,
    CustomerRepositoryModule,
    PunchCardReadModule,
    CompletedPunchCardModule,
  ],
  exports: [CustomerPunchCardServiceInterface],
})
export class CustomerPunchCardModule {}
