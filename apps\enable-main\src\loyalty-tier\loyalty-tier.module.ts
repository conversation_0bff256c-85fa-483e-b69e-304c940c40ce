import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { SharedModule } from '../shared/shared.module';
import { LoyaltyTierController } from './controllers/loyalty-tier.controller';
import { LoyaltyTierListener } from './listeners/loyalty-tier.listener';
import { LoyaltyTierDeletionModule } from './modules/loyalty-tier-deletion/loyalty-tier-deletion.module';
import { LoyaltyTierIndexModule } from './modules/loyalty-tier-index/loyalty-tier-index.module';
import { LoyaltyTierReadModule } from './modules/loyalty-tier-read/loyalty-tier-read.module';
import { LoyaltyTierRepositoryModule } from './modules/loyalty-tier-repository/loyalty-tier-repository.module';
import { LoyaltyTierWriteModule } from './modules/loyalty-tier-write/loyalty-tier-write.module';

@Module({
  controllers: [LoyaltyTierController],
  providers: [LoyaltyTierListener],
  imports: [
    SharedModule,
    EventEmitterModule,
    LoyaltyTierIndexModule,
    LoyaltyTierReadModule,
    LoyaltyTierWriteModule,
    LoyaltyTierDeletionModule,
    LoyaltyTierRepositoryModule,
  ],
})
export class LoyaltyTierModule {}
