import {
  CustomerDocument,
  CustomerReplacementOptions,
  LoyaltyCustomerReplacementsDto,
} from '@app/shared-stuff';

export interface CustomerReplacementsServiceInterface {
  getCustomerReplacements(
    customer: CustomerDocument,
    customerReplacementOptions?: CustomerReplacementOptions,
  ): Promise<LoyaltyCustomerReplacementsDto>;
}

export const CustomerReplacementsServiceInterface = Symbol(
  'CustomerReplacementsServiceInterface',
);
