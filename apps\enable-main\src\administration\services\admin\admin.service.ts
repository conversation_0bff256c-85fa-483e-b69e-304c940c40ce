import {
  BranchDocument,
  Brand,
  CollectionName,
  CompanyDocument,
  CompanyConfigDocument,
  CreateCompanyDto,
  Customer,
  OrderDocument,
  OrderItem,
  responseCode,
  SavedLocation,
  UpdateCompanyConfigDto,
} from '@app/shared-stuff';
import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { compare } from 'bcrypt';
import { sign } from 'jsonwebtoken';
import { Model, mongo, Types } from 'mongoose';
import { CompanyService } from '../../../company/services/company/company.service';
import { OrderLogDocument } from '../../../order/models/order.log.model';
import { RoleDocument } from '../../../rbac/models/role.model';
import { UserDocument } from '../../../user/models/user.model';
import { AdminToLogin, Feedback } from './../../dto/admin.dto';

@Injectable()
export class AdminService {
  feedbacks: Feedback[] = [];

  constructor(
    @InjectModel('User') private userModel: Model<UserDocument>,
    @InjectModel(CollectionName.COMPANY)
    private companyModel: Model<CompanyDocument>,
    @InjectModel(CollectionName.BRAND) private brandModel: Model<Brand>,
    @InjectModel(CollectionName.BRANCH)
    private branchModel: Model<BranchDocument>,
    @InjectModel(CollectionName.CUSTOMER)
    private customerModel: Model<Customer>,
    @InjectModel(CollectionName.ORDER) private orderModel: Model<OrderDocument>,
    @InjectModel('OrderItem') private orderItemModel: Model<OrderItem>,
    @InjectModel(CollectionName.ROLES) private roleModel: Model<RoleDocument>,
    @InjectModel('OrderLog') private orderLogModel: Model<OrderLogDocument>,
    @InjectModel(CollectionName.SAVED_LOCATION)
    private savedLocationModel: Model<SavedLocation>,
    @InjectModel(CollectionName.COMPANY_CONFIG)
    private companyConfigModel: Model<CompanyConfigDocument>,
    private companyService: CompanyService,
    private configService: ConfigService,
  ) {}

  async login(adminToLogin: AdminToLogin) {
    if (!adminToLogin.email) {
      throw {
        code: responseCode.MISSING_DATA,
        statusCode: 422,
        message: 'please provide the user email or phone to login',
      };
    }

    const logiedUser = await this.userModel
      .findOne({
        email: adminToLogin.email,
      })
      .exec();
    if (!logiedUser) {
      throw {
        code: responseCode.WRONG_CREDENTIAL,
        statusCode: 401,
        message: 'wrong credentials',
      };
    }
    if (logiedUser.company) {
      throw {
        code: responseCode.WRONG_CREDENTIAL,
        statusCode: 401,
        message: 'You cannot access the admin panel',
      };
    }

    if (logiedUser.status != 'active') {
      throw {
        code: responseCode.WRONG_CREDENTIAL,
        statusCode: 400,
        message: 'You account is disabled right now',
      };
    }

    const passwordChecked = await compare(
      adminToLogin.password,
      logiedUser.password,
    );
    if (!passwordChecked) {
      throw {
        code: responseCode.WRONG_CREDENTIAL,
        statusCode: 401,
        message: 'wrong credentials',
      };
    }
    const token = sign(
      { id: logiedUser.id },
      this.configService.get<string>('MAIN_JWT_SECRET'),
      { expiresIn: '30d' },
    );
    logiedUser.auth_token = token;
    await logiedUser.save();
    return token;
  }

  async createCompany(companyToCreate: CreateCompanyDto) {
    return await this.companyService.create(companyToCreate);
  }

  async updateCompanyConfig(companyConfigToUpdate: UpdateCompanyConfigDto) {
    return await this.companyService.updateCompanyConfig(companyConfigToUpdate);
  }

  async createFeedback(feedback: Feedback) {
    this.feedbacks.push(feedback);
    return 'DONE';
  }

  async getFeedback() {
    return this.feedbacks;
  }

  async hardDelete(id: string, code: string): Promise<mongo.DeleteResult[]> {
    if (code !== this.configService.get<string>('HARD_DELETE_CODE')) {
      throw new BadRequestException('This route is not for general use');
    }

    const companyId = new Types.ObjectId(id);

    const ordersToDelete = await this.orderModel.find(
      { company: companyId },
      { code: 1 },
    );
    const orderCodes = ordersToDelete.map((order) => order.code);

    const customersToDelete = await this.customerModel.find(
      { company: companyId },
      { savedLocations: 1 },
    );
    const savedLocations = customersToDelete
      .map((customer) => customer.savedLocations)
      .flat();

    return await Promise.all([
      this.companyModel.deleteOne({ _id: companyId }).exec(),
      this.companyConfigModel.deleteOne({ company: companyId }).exec(),
      this.roleModel.deleteMany({ company: companyId }).exec(),
      this.branchModel.deleteMany({ company: companyId }).exec(),
      this.brandModel.deleteMany({ companyId: companyId }).exec(),
      this.customerModel.deleteMany({ company: companyId }).exec(),
      this.savedLocationModel
        .deleteMany({ _id: { $in: savedLocations } })
        .exec(),
      this.orderLogModel.deleteMany({ orderCode: { $in: orderCodes } }).exec(),
      this.orderModel.deleteMany({ company: companyId }).exec(),
      this.orderItemModel.deleteMany({ company: companyId }).exec(),
      this.userModel.deleteMany({ company: companyId }).exec(),
    ]);
  }
}
