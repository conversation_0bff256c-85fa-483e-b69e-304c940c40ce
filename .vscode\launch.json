{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Enable Main",
      "skipFiles": ["<node_internals>/**"],
      "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/nest",
      "args": [
        "start",
        "enable-main",
        "--watch",
        "--debug",
        "0.0.0.0:9232"
      ],
      "console": "integratedTerminal",
      "restart": true,
      "autoAttachChildProcesses": true,
      "sourceMaps": true,
      "env": {
        "NODE_ENV": "development"
      }
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Enable Notification",
      "skipFiles": ["<node_internals>/**"],
      "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/nest",
      "args": [
        "start",
        "enable-notification",
        "--watch",
        "--debug",
        "0.0.0.0:9230"
      ],
      "console": "integratedTerminal",
      "restart": true,
      "autoAttachChildProcesses": true,
      "sourceMaps": true,
      "env": {
        "NODE_ENV": "development"
      }
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Enable Shorten",
      "skipFiles": ["<node_internals>/**"],
      "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/nest",
      "args": [
        "start",
        "enable-shorten",
        "--watch",
        "--debug",
        "0.0.0.0:9231"
      ],
      "console": "integratedTerminal",
      "restart": true,
      "autoAttachChildProcesses": true,
      "sourceMaps": true,
      "env": {
        "NODE_ENV": "development"
      }
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Enable Reporting",
      "skipFiles": ["<node_internals>/**"],
      "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/nest",
      "args": [
        "start",
        "enable-reporting",
        "--watch",
        "--debug",
        "0.0.0.0:9233"
      ],
      "console": "integratedTerminal",
      "restart": true,
      "autoAttachChildProcesses": true,
      "sourceMaps": true,
      "env": {
        "NODE_ENV": "development"
      }
    }
  ],
  "compounds": [
    {
      "name": "Debug All Enable Services",
      "configurations": [
        "Debug Enable Main",
        "Debug Enable Notification",
        "Debug Enable Shorten",
        "Debug Enable Reporting"
      ]
    }
  ]
}
