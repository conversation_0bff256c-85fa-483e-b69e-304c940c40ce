import {
  CouponDocument,
  CouponIndexDto,
  Create<PERSON><PERSON>ponDto,
  CurrentUser,
  CustomerDocument,
  responseCode,
  UpdateCouponDto,
  Coupon,
} from '@app/shared-stuff';
import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Types } from 'mongoose';
import { CompanyService } from '../../company/services/company/company.service';
import { CouponRepositoryInterface } from '../repositories/coupon.repository.interface';
import { CouponServiceInterface } from './coupon.service.interface';
import { plainToInstance } from 'class-transformer';
import { BenefitUtilityService } from '../../benefit/benefit-utility/benefit-utility.service';

@Injectable()
export class CouponService implements CouponServiceInterface {
  constructor(
    @Inject(CouponRepositoryInterface)
    private readonly couponRepository: CouponRepositoryInterface,
    private readonly companyService: CompanyService,
    private readonly eventEmitter: EventEmitter2,
    private readonly benefitUtilityService: BenefitUtilityService,
  ) {}

  async index(couponIndexDto: CouponIndexDto): Promise<CouponDocument[]> {
    return await this.couponRepository.index(couponIndexDto);
  }

  async findById(couponId: Types.ObjectId): Promise<CouponDocument> {
    const coupon = await this.couponRepository.findById(couponId);
    if (!coupon || coupon.deletedAt) {
      throw new NotFoundException(
        `Coupon with id ${couponId.toHexString()} not found`,
      );
    }

    return coupon;
  }

  async findCustomerRedeemableCoupons(
    customer: CustomerDocument,
  ): Promise<CouponDocument[]> {
    if (!customer) return [];
    const coupons = await this.couponRepository.findByCompanyId(
      customer.company,
    );

    return coupons.filter(
      (coupon) => coupon.loyaltyPointCost <= customer.loyaltyPoints,
    );
  }

  async findCustomerHighestRedeemableCoupon(
    customer: CustomerDocument,
  ): Promise<CouponDocument> {
    if (!customer) return null;
    return await this.couponRepository.findHighestCoupon(
      customer.company,
      customer.loyaltyPoints || 0,
    );
  }

  async findByCompanyId(
    companyId: Types.ObjectId,
    withException?: boolean,
  ): Promise<CouponDocument[]> {
    const coupons = await this.couponRepository.findByCompanyId(companyId);

    if (coupons.length === 0 && withException) {
      const errorMessage = `There are no coupons attached to this company with id: ${companyId.toHexString()}`;
      throw new BadRequestException(
        errorMessage,
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    }

    return coupons;
  }

  async findCustomerNextCoupon(
    customer: CustomerDocument,
  ): Promise<CouponDocument | null> {
    if (!customer) return null;
    const coupons = await this.couponRepository.findByCompanyId(
      customer.company,
    );
    const nextCoupon = coupons
      .sort(
        (firstCoupon, secondCoupon) =>
          firstCoupon.loyaltyPointCost - secondCoupon.loyaltyPointCost,
      )
      .find((coupon) => coupon.loyaltyPointCost > customer.loyaltyPoints);

    return nextCoupon;
  }

  async findByOrdableId(ordableId: number): Promise<CouponDocument> {
    return await this.couponRepository.findByOrdableId(ordableId);
  }

  async findByShopifyDiscountCodeForCustomer(
    code: string,
    customer: CustomerDocument,
  ): Promise<CouponDocument | null> {
    return this.couponRepository.findByShopifyDiscountCodeForCustomer(
      code,
      customer,
    );
  }

  async findByCost(
    companyId: Types.ObjectId,
    lowerBound: number,
    upperBound: number,
  ): Promise<CouponDocument[]> {
    return await this.couponRepository.findByCost(
      companyId,
      lowerBound,
      upperBound,
    );
  }

  async create(createCouponDto: CreateCouponDto): Promise<CouponDocument> {
    const company = await this.companyService.findById(
      createCouponDto.companyId,
    );

    const newCoupon = plainToInstance(Coupon, createCouponDto);
    newCoupon.benefits = await this.benefitUtilityService.upsertEmbedded(
      company,
      createCouponDto.benefits,
    );

    const coupon = await this.couponRepository.create(newCoupon);

    this.eventEmitter.emit('coupon.created', coupon);
    return coupon;
  }

  async update({
    couponId,
    companyId,
    ...updateCouponDto
  }: UpdateCouponDto): Promise<CouponDocument> {
    const company = await this.companyService.findById(companyId);

    const coupon = await this.couponRepository.findOneAndUpdate(
      { _id: couponId, deletedAt: null },
      updateCouponDto,
    );

    if (!coupon) {
      throw new NotFoundException(
        `Coupon with id ${couponId.toHexString()} not found`,
      );
    }

    if (updateCouponDto.benefits) {
      coupon.benefits = await this.benefitUtilityService.upsertEmbedded(
        company,
        updateCouponDto.benefits,
      );
      await coupon.save();
    }

    this.eventEmitter.emit('coupon.updated', coupon);
    return coupon;
  }

  async delete(
    couponId: Types.ObjectId,
    companyId: Types.ObjectId,
    currentUser: CurrentUser,
  ): Promise<string> {
    const deletedDocument = await this.couponRepository.findOneAndUpdate(
      { _id: couponId, companyId, deletedAt: null },
      { deletedAt: new Date(), deletedBy: currentUser },
    );

    if (!deletedDocument)
      throw new NotFoundException(
        `Coupon with ID ${couponId.toHexString()} not found`,
      );

    this.eventEmitter.emit('coupon.deleted', deletedDocument);
    return `Successfully Deleted Coupon with ID ${couponId.toHexString()}`;
  }
}
