import {
  CreateShipmentDto,
  GenericExceptionFilter,
  GetAllShipmentDto,
  GetShipmentTrackingResponseDto,
  SingleIdDto,
  TransformInterceptor,
  UpdateShipmentDto,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Get,
  Inject,
  Param,
  Patch,
  Post,
  Query,
  Req,
  Res,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { ShipmentServiceInterface } from '../services/shipment/shipment-service.interface';

@Controller('shipment')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags('Third Party Logistics')
@SetMetadata('module', 'shipment')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class ShipmentController {
  constructor(
    @Inject(ShipmentServiceInterface)
    private shipmentService: ShipmentServiceInterface,
  ) {}

  @Get()
  @SetMetadata('action', 'index')
  async findAll(
    @Query() getAllShipmentDto: GetAllShipmentDto,
    @Req() req: Request,
  ) {
    getAllShipmentDto.companyId = req['company_id']
      ? req['company_id']
      : getAllShipmentDto.companyId;

    const result = await this.shipmentService.findAll(getAllShipmentDto);
    return {
      shipments: result[0].paginatedResult,
      totalShipments: result[0]?.totalCount[0]?.createdAt,
    };
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  async findOne(@Param() { id }: SingleIdDto) {
    return this.shipmentService.findOne(id);
  }

  @Get('tracking-number/:trackingNumber')
  @SetMetadata('action', 'get-by-tracking-number')
  async findOneByTrackingNumber(
    @Param('trackingNumber') trackingNumber: string,
  ) {
    return this.shipmentService.findByTrackingNumber(trackingNumber);
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() createShipmentDto: CreateShipmentDto,
    @Req() req: Request,
  ) {
    createShipmentDto.companyId = req['company_id']
      ? req['company_id']
      : createShipmentDto.companyId;
    createShipmentDto.eblBrandId = req['brand']
      ? req['brand']._id
      : createShipmentDto.eblBrandId;
    return this.shipmentService.create(createShipmentDto);
  }

  @Patch()
  @SetMetadata('action', 'update')
  async update(
    @Body() updateShipmentDto: UpdateShipmentDto,
    @Req() req: Request,
  ) {
    updateShipmentDto.companyId = req['company_id']
      ? req['company_id']
      : updateShipmentDto.companyId;
    return this.shipmentService.update(updateShipmentDto);
  }

  @Get('public/way-bill/:id')
  @SetMetadata('public', 'true')
  @SetMetadata('exclude-from-transformation', true)
  async getWayBill(
    @Param('id') trackingNumberOrId: string,
    @Res() res: Response,
  ) {
    res.set({
      'Cache-Control': 'no-cache',
      'Last-Modified': new Date().toUTCString(),
      'Content-TYPE': 'application/pdf',
      'Content-Transfer-Encoding': 'binary',
      'Content-Disposition': 'inline; filename=way-bill.pdf',
    });
    const waybill =
      await this.shipmentService.generateWayBill(trackingNumberOrId);
    return res.send(waybill);
  }

  @Get('public/tracking/:id')
  @SetMetadata('public', 'true')
  async getTracking(
    @Param() { id }: SingleIdDto,
  ): Promise<GetShipmentTrackingResponseDto> {
    return this.shipmentService.getTrackingDetails(id);
  }
}
