import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsOptional } from 'class-validator';
import { DibsyAmount } from './dibsy-amount.dto';

export class DibsyPaymentToCreate {
  @ApiProperty({
    type: () => DibsyAmount,
    required: true,
  })
  @Type(() => DibsyAmount)
  @IsNotEmpty()
  amount: DibsyAmount;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  redirectUrl: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  webhookUrl?: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  method: string;

  @ApiProperty({
    type: Object,
    required: false,
  })
  @IsOptional()
  metadata?: any;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  cardToken?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  applePayToken?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  customerId?: string;
}
