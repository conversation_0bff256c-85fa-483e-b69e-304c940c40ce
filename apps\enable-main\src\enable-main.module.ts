import {
  LoggerService,
  MicroserviceCommunicationModule,
  MongooseModuleRegistered,
} from '@app/shared-stuff';
import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { ConditionalModule, ConfigModule, ConfigService } from '@nestjs/config';
import { APP_GUARD, Reflector } from '@nestjs/core';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { WinstonModule } from 'nest-winston';
import { ThirdPartyLogisticModule } from './3pl/third-party-logistic.module';
import { AdministrationModule } from './administration/administration.module';
import { BackdoorModule } from './backdoor/backdoor.module';
import { BenefitModule } from './benefit/benefit.module';
import { BranchModule } from './branch/branch.module';
import { BrandModule } from './brand/brand.module';
import { CallCenterModule } from './call-center/call-center.module';
import { CampaignModule } from './campaign/campaign.module';
import { CompanyModule } from './company/company.module';
import { CouponModule } from './coupon/coupon.module';
import { CustomerModule } from './customer/customer.module';
import { DeliveryModule } from './delivery/delivery.module';
import { IntegrationModule } from './integration/integration.module';
import { JourneyModule } from './journey/journey.module';
import { LocationModule } from './location/location.module';
import { LoyaltyPointLogModule } from './loyalty-point-log/loyalty-point-log.module';
import { LoyaltyTierLogModule } from './loyalty-tier-log/loyalty-tier-log.module';
import { LoyaltyTierModule } from './loyalty-tier/loyalty-tier.module';
import { LoyaltyTransactionModule } from './loyalty-transaction/loyalty-transaction.module';
import { MainModule } from './main/main.module';
import { MobileModule } from './mobile/mobile.module';
import { NotificationModule } from './notification/notification.module';
import { OrderModule } from './order/order.module';
import { OtpModule } from './otp/otp.module';
import { PassesModule } from './passes/passes.module';
import { PaymentModule } from './payment/payment.module';
import { PunchCardModule } from './punch-card/punch-card.module';
import { RbacModule } from './rbac/rbac.module';
import { ReportModule } from './report/report.module';
import { RestaurantModule } from './restaurant/restaurant.module';
import { AuthorizeUserGuard } from './shared/guards/authorize-user.guard';
import { AuthenticateAdminMiddleware } from './shared/middlewares/authenticate-admin.middleware';
import { AuthenticateUserMiddleware } from './shared/middlewares/authenticate-user.middleware';
import { SharedModule } from './shared/shared.module';
import { StorageModule } from './storage/storage.module';
import { StoreModule } from './store/store.module';
import { UserModule } from './user/user.module';
import { UtilityModule } from './utility/utility.module';
import { WebsocketGatewayModule } from './websocket-gateway/websocket-gateway.module';
import { PwaConfigModule } from './pwa-config/pwa-config.module';

@Module({
  imports: [
    WinstonModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) =>
        LoggerService.generateWinstonLogger(configService),
    }),
    // Third Party Module
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['./apps/enable-main/.env', '.env'],
    }),
    MongooseModuleRegistered,
    ScheduleModule.forRoot(),
    EventEmitterModule.forRoot({
      verboseMemoryLeak: true,
    }),
    ConditionalModule.registerWhen(
      BackdoorModule,
      (env: NodeJS.ProcessEnv) =>
        env.ENVIRONMENT && !env.ENVIRONMENT.toLowerCase().includes('prod'),
    ),
    // Static Modules
    PassesModule,
    SharedModule,
    OrderModule,
    UserModule,
    BranchModule,
    CompanyModule,
    StoreModule,
    DeliveryModule,
    LocationModule,
    NotificationModule,
    PaymentModule,
    RbacModule,
    CustomerModule,
    RestaurantModule,
    AdministrationModule,
    CallCenterModule,
    MainModule,
    ReportModule,
    WebsocketGatewayModule,
    BrandModule,
    IntegrationModule,
    CouponModule,
    LoyaltyTierModule,
    PassesModule,
    OtpModule,
    MicroserviceCommunicationModule,
    CampaignModule,
    JourneyModule,
    PunchCardModule,
    ThirdPartyLogisticModule,
    LoyaltyTierLogModule,
    LoyaltyPointLogModule,
    LoyaltyTransactionModule,
    MobileModule,
    UtilityModule,
    BenefitModule,
    StorageModule,
    PwaConfigModule,
  ],
  controllers: [],
  providers: [
    {
      provide: APP_GUARD,
      useClass: AuthorizeUserGuard,
    },
  ],
})
export class EnableMainModule {
  constructor(private reflector: Reflector) {}

  // Applay Authenticate Middleware
  configure(consumer: MiddlewareConsumer) {
    // consumer.apply(RawBodyMiddleware).forRoutes({
    //   path : 'payment/action/stripe/after_done',
    //   method : RequestMethod.POST
    // }).apply(JsonBodyMiddleware).forRoutes('*')

    consumer
      .apply(AuthenticateUserMiddleware)
      .exclude(
        { path: 'auth/login', method: RequestMethod.POST },
        { path: 'auth/register', method: RequestMethod.POST },
        { path: 'auth/logout', method: RequestMethod.POST },
        { path: 'auth/me', method: RequestMethod.GET },
        { path: 'apikey/validate/:code', method: RequestMethod.GET },
        { path: 'auth/password/compare', method: RequestMethod.POST },
        { path: 'payment/clone', method: RequestMethod.POST },
        {
          path: '{*beforeSplat}public{*afterSplat}',
          method: RequestMethod.ALL,
        },
        { path: 'v1/payment/process', method: RequestMethod.POST },
        { path: 'payment/tess/session', method: RequestMethod.POST },
        {
          path: 'payment/tess/callback/:paymentCode',
          method: RequestMethod.GET,
        },
        { path: 'payment/action/after_done', method: RequestMethod.GET },
        {
          path: 'payment/action/dibsy/merchant_validation',
          method: RequestMethod.POST,
        },
        {
          path: 'payment/action/stripe/after_done',
          method: RequestMethod.POST,
        },
        {
          path: 'payment/fatoorah/callback/:paymentCode',
          method: RequestMethod.GET,
        },
        {
          path: 'payment/fatoorah/webhook',
          method: RequestMethod.POST,
        },
        {
          path: 'payment/fatoorah/execute/:sessionId',
          method: RequestMethod.GET,
        },
        {
          path: 'payment/action/dibsy/after_done/:paymentId',
          method: RequestMethod.ALL,
        },
        {
          path: 'payment/action/skipcash/returned',
          method: RequestMethod.GET,
        },
        {
          path: 'payment/action/skipcash/after_done',
          method: RequestMethod.POST,
        },
        {
          path: 'payment/action/cybersource/after_done',
          method: RequestMethod.POST,
        },
        {
          path: 'payment/action/cybersource/customer_response',
          method: RequestMethod.POST,
        },
        {
          path: 'payment/action/tap/after_done/:paymentId',
          method: RequestMethod.GET,
        },
        {
          path: 'payment/action/tap/update_status/:paymentId',
          method: RequestMethod.POST,
        },
        { path: 'user/password/request', method: RequestMethod.POST },
        { path: 'user/password/change', method: RequestMethod.POST },
        { path: 'user/password/validate', method: RequestMethod.POST },
        { path: 'order/woocommerce/create', method: RequestMethod.GET },
        { path: 'order/magento/create', method: RequestMethod.GET },
        { path: 'registration-request', method: RequestMethod.POST },
        { path: 'delivery/national/address', method: RequestMethod.POST },
        { path: 'order/tookan/status', method: RequestMethod.POST },
        { path: 'order/tookan/status', method: RequestMethod.GET },
        { path: 'order/tracking/:uniqueIdentifier', method: RequestMethod.GET },

        { path: 'deliverect/:branchId/menuPush', method: RequestMethod.POST },
        {
          path: 'deliverect/:branchId/SnoozeOrUnSnooze',
          method: RequestMethod.POST,
        },
        { path: 'deliverect/ChannelStatus', method: RequestMethod.POST },
        {
          path: 'deliverect/:branchId/restaurantBusy',
          method: RequestMethod.POST,
        },
        { path: 'deliverect/orderStatusUpdate', method: RequestMethod.POST },
        {
          path: 'deliverect/pos/tables/:companyId/:locationId',
          method: RequestMethod.GET,
        },
        {
          path: 'deliverect/pos/floors/:companyId/:locationId',
          method: RequestMethod.GET,
        },
        {
          path: 'deliverect/pos/register',
          method: RequestMethod.POST,
        },
        { path: 'deliverect/pos/order/:companyId', method: RequestMethod.POST },
        {
          path: 'deliverect/pos/product/:companyId/:locationId',
          method: RequestMethod.GET,
        },

        { path: 'payment/cbpay/requestToken', method: RequestMethod.POST },
        { path: 'bee/status/updated', method: RequestMethod.POST },
        { path: 'mr-delivery/status/updated', method: RequestMethod.POST },
        { path: 'call-center/maqsam/webhook', method: RequestMethod.POST },
        { path: 'call-center/ziwo/webhook', method: RequestMethod.POST },
        { path: 'v1/log', method: RequestMethod.POST },
        { path: 'v1/wallet', method: RequestMethod.POST },
        { path: 'v1/devices/{*splat}', method: RequestMethod.ALL },
        { path: 'v1/passes/{*splat}', method: RequestMethod.ALL },
        { path: 'v1/stamps.png', method: RequestMethod.GET },
        { path: 'admin/{*splat}', method: RequestMethod.ALL },
        { path: 'customer/me', method: RequestMethod.GET },
        { path: 'otp/{*splat}', method: RequestMethod.POST },
        { path: 'loyalty-tier', method: RequestMethod.GET },
        { path: 'mobile/version', method: RequestMethod.GET },
        { path: 'mobile/version', method: RequestMethod.POST },
        {
          path: 'notification/configuration/:ownerId/hasWhatsappKey',
          method: RequestMethod.GET,
        },
        { path: 'coupon', method: RequestMethod.GET },
        { path: 'v2/coupon', method: RequestMethod.GET },
        { path: 'coupon/:couponId', method: RequestMethod.GET },
        { path: 'sms/mitto/webhook', method: RequestMethod.GET },
        { path: 'customer/loyaltyRegistration', method: RequestMethod.POST },
        { path: 'customer/deviceData', method: RequestMethod.POST },
        {
          path: 'integration/store/:storeId/shopify/order/create',
          method: RequestMethod.POST,
        },
        {
          path: 'integration/store/:storeId/shopify/order/capture',
          method: RequestMethod.POST,
        },
        {
          path: 'integration/store/:storeId/shopify/menu-item/create',
          method: RequestMethod.POST,
        },
        {
          path: 'integration/store/:storeId/shopify/menu-item/update',
          method: RequestMethod.POST,
        },
        { path: 'images/:folder/:name', method: RequestMethod.GET },
        { path: 'documents/:name', method: RequestMethod.GET },
        { path: 'backdoor/{*splat}', method: RequestMethod.ALL },
        { path: 'utility/cache/clearAll', method: RequestMethod.POST },
        { path: 'public/tracking/:orderCode', method: RequestMethod.GET },
        { path: 'order/tookan/delivery/success', method: RequestMethod.POST },
        { path: 'pass-config', method: RequestMethod.GET },
        { path: 'pass-config/:passConfigId', method: RequestMethod.GET },
        { path: 'notification/fcm/subscribe', method: RequestMethod.POST },
        { path: 'notification/fcm/unsubscribe', method: RequestMethod.POST },
        { path: 'notification/configuration', method: RequestMethod.GET },
        { path: 'health', method: RequestMethod.GET },
      )
      .forRoutes({
        path: '{*splat}',
        method: RequestMethod.ALL,
      });

    // Apply Admin Authentication Middleware
    consumer
      .apply(AuthenticateAdminMiddleware)
      .exclude(
        { path: 'admin/login', method: RequestMethod.POST },
        { path: 'admin/:companyId/hardDelete', method: RequestMethod.DELETE },
      )
      .forRoutes(
        { path: 'admin/*', method: RequestMethod.ALL },
        { path: 'notification/configuration', method: RequestMethod.GET },
      );

    // // Apply zipKin middleWare
    // consumer.apply(ZipkinMiddleware).forRoutes({
    //   path: '*',
    //   method: RequestMethod.ALL,
    // });
  }
}
