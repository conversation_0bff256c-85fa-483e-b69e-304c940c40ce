import { GenericRepository } from '@app/shared-stuff';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model, Types } from 'mongoose';
import {
  PaymentConfiguration,
  PaymentConfigurationDocument,
} from '../../../../../libs/shared-stuff/src/models/payment.configuration.model';
import { PaymentConfigurationRepositoryInterface } from './interfaces/payment.configuration.repository.interface';

export class PaymentConfigurationRepository
  extends GenericRepository<PaymentConfigurationDocument, PaymentConfiguration>
  implements PaymentConfigurationRepositoryInterface
{
  constructor(
    @InjectModel(PaymentConfiguration.name)
    private PaymentConfigurationModel: Model<
      PaymentConfigurationDocument,
      PaymentConfiguration
    >,
  ) {
    super(PaymentConfigurationModel);
  }

  async findByBranchId(
    branchId: Types.ObjectId,
  ): Promise<PaymentConfigurationDocument | null> {
    return await this.PaymentConfigurationModel.findOne({
      branchId,
    });
  }

  async findByBrandId(
    brandId: Types.ObjectId,
  ): Promise<PaymentConfigurationDocument | null> {
    return await this.PaymentConfigurationModel.findOne({
      brandId,
    });
  }

  async findByCompanyId(
    companyId: Types.ObjectId,
  ): Promise<PaymentConfigurationDocument | null> {
    return await this.PaymentConfigurationModel.findOne({
      companyId: companyId,
    });
  }

  async findByIdentifier(
    identifier: Types.ObjectId,
  ): Promise<PaymentConfigurationDocument> {
    return await this.PaymentConfigurationModel.findOne({
      $or: [
        { _id: identifier },
        { branchId: identifier },
        { brandId: identifier },
        { companyId: identifier },
      ],
    });
  }

  async findOneAndReplace(
    filter: FilterQuery<PaymentConfigurationDocument>,
    paymentConfiguration: PaymentConfiguration,
  ): Promise<PaymentConfigurationDocument> {
    return await this.PaymentConfigurationModel.findOneAndReplace(
      filter,
      paymentConfiguration,
      { rawResult: false },
    );
  }
}
