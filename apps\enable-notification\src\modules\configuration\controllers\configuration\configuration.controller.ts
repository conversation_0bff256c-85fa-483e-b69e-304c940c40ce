import {
  DocumentTransformerInterceptor,
  GetConfigRequestDto,
  OwnerIdDto,
  TemplateOwner,
  UpdateConfigurationByOwnerDto,
} from '@app/shared-stuff';
import {
  Controller,
  Inject,
  SetMetadata,
  UseInterceptors,
} from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { plainToInstance } from 'class-transformer';

import { ConfigurationServiceInterface } from '../../services/configuration/configuration.service.interface';

@Controller('configuration')
@SetMetadata('module', 'configuration')
@UseInterceptors(DocumentTransformerInterceptor)
export class ConfigurationController {
  constructor(
    @Inject('ConfigurationServiceInterface')
    private configurationService: ConfigurationServiceInterface,
  ) {}

  @MessagePattern('get.configuration.request')
  async getConfiguration(@Payload() getConfigRequestDto: GetConfigRequestDto) {
    const owner = plainToInstance(TemplateOwner, getConfigRequestDto.owner);
    const config =
      await this.configurationService.findOrCreateConfiguration(owner);

    return { config };
  }

  @MessagePattern('hasWhatsappKey.configuration.request')
  async hasWhatsappKey(@Payload() ownerIdDto: OwnerIdDto) {
    return await this.configurationService.hasWhatsappKey(ownerIdDto.ownerId);
  }

  @MessagePattern('update.configuration.request')
  async updateConfiguration(
    @Payload() updateConfigurationByOwnerDto: UpdateConfigurationByOwnerDto,
  ) {
    return await this.configurationService.updateConfig(
      updateConfigurationByOwnerDto,
    );
  }
}
