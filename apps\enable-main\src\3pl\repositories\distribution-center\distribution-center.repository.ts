import { Injectable } from '@nestjs/common';
import {
  CollectionName,
  DistributionCenter,
  DistributionCenterDocument,
  GenericRepository,
} from '@app/shared-stuff';
import { DistributionCenterRepositoryInterface } from './distribution-center-repository.interface';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@Injectable()
export class DistributionCenterRepository
  extends GenericRepository<DistributionCenterDocument, DistributionCenter>
  implements DistributionCenterRepositoryInterface
{
  constructor(
    @InjectModel(CollectionName.DISTRIBUTION_CENTER)
    private distributionCenterModel: Model<
      DistributionCenterDocument,
      DistributionCenter
    >,
  ) {
    super(distributionCenterModel);
  }
}
