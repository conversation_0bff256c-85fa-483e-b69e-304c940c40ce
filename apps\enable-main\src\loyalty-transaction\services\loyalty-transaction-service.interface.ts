import {
  CompanyDocument,
  CreateManualLoyaltyTransactionDto,
  CustomerDocument,
  CustomerEarnedBenefit,
  LoyaltyTransactionDocument,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface LoyaltyTransactionServiceInterface {
  recordEarnedBenefits(
    customer: CustomerDocument,
    earnedBenefits: CustomerEarnedBenefit[],
  ): Promise<LoyaltyTransactionDocument[]>;
  recordRedeemedBenefit(
    customer: CustomerDocument,
    benefit: CustomerEarnedBenefit,
  ): Promise<LoyaltyTransactionDocument>;
  createManual(
    createLoyaltyTransactionDto: CreateManualLoyaltyTransactionDto,
  ): Promise<LoyaltyTransactionDocument>;
  recordRefundedBenefits(
    customer: CustomerDocument,
    benefit: CustomerEarnedBenefit[],
  ): Promise<LoyaltyTransactionDocument[]>;

  getRedeemedBenefitCount(
    customer: CustomerDocument,
    company: CompanyDocument,
    benefitId: Types.ObjectId,
  ): Promise<number>;
}
export const LoyaltyTransactionServiceInterface = Symbol(
  'LoyaltyTransactionServiceInterface',
);
