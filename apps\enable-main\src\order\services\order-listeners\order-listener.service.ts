import { LogError, LoggerService, OrderDocument } from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { CustomerOrderServiceInterface } from '../../../customer/modules/customer-order/customer-order.service.interface';
import { CustomerReadServiceInterface } from '../../../customer/modules/customer-read/customer-read.service.interface';
import { OrderDeliveryService } from '../order-delivery/order-delivery.service';

@Injectable()
export class OrderListener {
  private readonly loggerService = new LoggerService(OrderListener.name);

  constructor(
    private readonly orderDeliveryService: OrderDeliveryService,
    @Inject(CustomerOrderServiceInterface)
    private readonly customerOrderService: CustomerOrderServiceInterface,
    @Inject(CustomerReadServiceInterface)
    private readonly customerReadService: CustomerReadServiceInterface,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  @OnEvent('order.cancelled')
  @LogError()
  async onOrderCancelled(order: OrderDocument) {
    if (!order.pickupJobId && !order.deliveryJobId) return;
    this.loggerService.log('[Tookan] order.cancelled Event is fired');
    this.loggerService.log('[Tookan] Order Code', order.code);
    const customer = await this.customerReadService.findById(order.customer);
    this.eventEmitter.emit('customer.points.updated', customer);
    await this.orderDeliveryService.cancelTookanTask(order);
  }
}
