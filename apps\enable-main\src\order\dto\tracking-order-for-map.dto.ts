import {
  CountryDialCode,
  OrderStatusEnum,
  TrackingPageSelectedLogo,
} from '@app/shared-stuff';
import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class TrackingOrderForMapDto {
  @ApiProperty({
    description: 'Order unique code or identifier',
    example: 'TEST-O-00001',
  })
  orderCode: string;

  @ApiProperty({
    enum: OrderStatusEnum,
    description: 'Current status of the order',
    example: OrderStatusEnum.PENDING,
  })
  orderStatus: OrderStatusEnum;

  @ApiProperty({
    description: 'Tookan tracking url',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  tookanTrackingUrl: string;

  @ApiProperty({
    description: 'Driver name (if assigned)',
    example: 'John Doe',
    required: false,
  })
  driverName?: string;

  @ApiProperty({
    description: 'Driver phone number (if assigned)',
    example: '34567890',
    required: false,
  })
  driverPhoneNumber?: string;

  @IsOptional()
  @IsEnum(CountryDialCode)
  countryCode?: CountryDialCode;

  @ApiProperty({
    description: 'Is The Overlay Enabled',
    example: 'false',
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  enableOverlay?: boolean;

  @ApiProperty({
    description: 'Branding color',
    example: '#000000',
    required: false,
  })
  @IsOptional()
  @IsString()
  brandingColor?: string;

  @IsNotEmpty()
  @IsEnum(TrackingPageSelectedLogo)
  selectedLogo: TrackingPageSelectedLogo;

  @ApiProperty({
    description: 'The brand logo',
    example: 'https://example.com/logo.png',
    required: false,
  })
  @IsString()
  @IsOptional()
  brandLogo?: string;

  @ApiProperty({
    description: 'Delivery location latitude',
    example: 25.286106,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  deliveryLatitude?: number;

  @ApiProperty({
    description: 'Delivery location longitude',
    example: 51.534817,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  deliveryLongitude?: number;

  @ApiProperty({
    description: 'Tookan driver ID (if assigned)',
    required: false,
  })
  @IsOptional()
  @IsString()
  tookanDriverId?: string;
}
