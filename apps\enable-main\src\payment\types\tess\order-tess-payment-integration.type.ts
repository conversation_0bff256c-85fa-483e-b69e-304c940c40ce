import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  Matches,
  MaxLength,
  MinLength,
} from 'class-validator';

export class OrderTESSPaymentIntegration {
  @ApiProperty({
    type: String,
    required: true,
    maxLength: 255,
    description: 'Order ID',
  })
  @IsNotEmpty()
  @Matches('a-zA-Z0-9')
  @MaxLength(255)
  number: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Product price. Format: XX.XX.',
  })
  @IsString()
  amount: string;

  @ApiProperty({
    type: String,
    required: true,
    maxLength: 3,
  })
  @IsNotEmpty()
  @Matches('A-Z')
  @MaxLength(3)
  currency: string;

  @ApiProperty({
    type: String,
    required: true,
    minLength: 2,
    maxLength: 1024,
    description: 'Product name',
  })
  @IsNotEmpty()
  @Matches('a-zA-Z0-9!"#$%&\'()*+,./:;&@')
  @MaxLength(1024)
  @MinLength(2)
  description: string;
}
