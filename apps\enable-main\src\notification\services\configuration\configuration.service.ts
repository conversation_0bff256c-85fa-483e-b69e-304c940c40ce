import {
  GetConfigRequestDto,
  GetConfigurationDto,
  MicroserviceCommunicationService,
  TemplateOwner,
  UpdateConfigurationByOwnerDto,
  UpdateConfigurationDto,
} from '@app/shared-stuff';
import {
  Inject,
  Injectable,
  OnModuleDestroy,
  OnModuleInit,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { Types } from 'mongoose';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { CompanyService } from '../../../company/services/company/company.service';

@Injectable()
export class ConfigurationService implements OnModuleInit, OnModuleDestroy {
  constructor(
    @Inject('enable-main-notification-producer')
    private client: ClientProxy,
    private companyService: CompanyService,
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandServiceInterface,
    private microserviceCommunicationService: MicroserviceCommunicationService,
  ) {}

  async onModuleDestroy() {
    await this.microserviceCommunicationService.disconnect(this.client);
  }

  async onModuleInit() {
    await this.microserviceCommunicationService.connect(this.client);
  }

  async getConfiguration({ ownerId, name }: GetConfigurationDto) {
    const owner: TemplateOwner = {
      _id: ownerId,
      name,
    };
    return this.microserviceCommunicationService.produceAndWaitForResponse<
      GetConfigRequestDto,
      any
    >(this.client, 'get.configuration.request', { owner });
  }

  async updateConfiguration({
    companyId,
    brandId,
    ...updateConfigurationDto
  }: UpdateConfigurationDto) {
    let owner: TemplateOwner;
    if (companyId) {
      const company = await this.companyService.findById(companyId);
      owner = {
        _id: companyId,
        name: company.name,
        emailSenderId: company.name,
        smsSenderId: company.senderId,
      };
    } else {
      const brand = await this.brandService.findById(brandId);
      owner = {
        _id: brandId,
        name: brand.name,
      };
    }

    const updateConfigurationByOwnerDto: UpdateConfigurationByOwnerDto = {
      ...updateConfigurationDto,
      owner,
    };
    return this.microserviceCommunicationService.produceAndWaitForResponse<
      UpdateConfigurationByOwnerDto,
      any
    >(
      this.client,
      'update.configuration.request',
      updateConfigurationByOwnerDto,
    );
  }

  async hasWhatsappKey(ownerId: Types.ObjectId): Promise<boolean> {
    return this.microserviceCommunicationService.produceAndWaitForResponse(
      this.client,
      'hasWhatsappKey.configuration.request',
      { ownerId },
    );
  }
}
