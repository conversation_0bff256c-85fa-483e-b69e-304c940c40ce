import {
  IGenericRepository,
  LoyaltyTransaction,
  LoyaltyTransactionDocument,
} from '@app/shared-stuff';

export interface LoyaltyTransactionRepositoryInterface
  extends IGenericRepository<LoyaltyTransactionDocument, LoyaltyTransaction> {
  createMany(
    transactions: LoyaltyTransaction[],
  ): Promise<LoyaltyTransactionDocument[]>;
}

export const LoyaltyTransactionRepositoryInterface = Symbol(
  'LoyaltyTransactionRepositoryInterface',
);
