import { CollectionName, ExternalOrder } from '@app/shared-stuff';
import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model } from 'mongoose';

@Injectable()
export class ExternalOrderGuard implements CanActivate {
  constructor(
    @InjectModel(CollectionName.EXTERNAL_ORDER)
    private readonly externalOrderModel: Model<ExternalOrder>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const externalOrderId = request.headers['x-external-order-id'];

    if (!externalOrderId) return true;

    try {
      await this.externalOrderModel.create({
        externalOrderId,
        createdAt: moment.utc().toDate(),
      });
    } catch (error) {
      if (error && 'code' in error && error.code === 11000) return false;
    }

    return true;
  }
}
