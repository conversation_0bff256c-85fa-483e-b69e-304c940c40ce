import { Module } from '@nestjs/common';
import { CustomerRepositoryModule } from '../customer-repository/customer-repository.module';
import { CustomerCodeService } from './customer-code.service';
import { CustomerCodeServiceInterface } from './customer-code.service.interface';

@Module({
  providers: [
    { provide: CustomerCodeServiceInterface, useClass: CustomerCodeService },
  ],
  imports: [CustomerRepositoryModule],
  exports: [CustomerCodeServiceInterface],
})
export class CustomerCodeModule {}
