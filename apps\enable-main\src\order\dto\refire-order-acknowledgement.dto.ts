import { ObjectIdTransform } from '@app/shared-stuff';
import { ApiProperty } from '@nestjs/swagger';
import { Types } from 'mongoose';

export class RefireOrderAcknowledgementDto {
  @ApiProperty({
    type: String,
    required: true,
  })
  @ObjectIdTransform()
  userId: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: false,
  })
  @ObjectIdTransform({ optional: true })
  orderId?: Types.ObjectId;
}
