import { Inject, Injectable } from '@nestjs/common';

import { Store } from '../../../../../store/models/store.model';
import { CreateOrdableOptionsDto } from '../../dtos/options/create-ordable-options.dto';
import { UpdateOrdableOptionsDto } from '../../dtos/options/update-ordable-options.dto';
import { OrdableHttpRequestsServiceInterface } from '../ordable-http-requests.service.interface';
import { OrdableOptionsServiceInterface } from './ordable-options.service.interface';

@Injectable()
export class OrdableOptionsService implements OrdableOptionsServiceInterface {
  OPTIONS_URI = '/api/options/';
  constructor(
    @Inject('OrdableHttpRequestsServiceInterface')
    private readonly ordableHttpRequestsService: OrdableHttpRequestsServiceInterface,
  ) {}

  create(
    createOrdableOptionsDto: CreateOrdableOptionsDto,
    store: Store,
  ): Promise<any> {
    const URL = store.apiBaseUrl + this.OPTIONS_URI;
    const API_KEY = store.apiKey;
    return this.ordableHttpRequestsService.createOrdablePostRequest(
      URL,
      API_KEY,
      createOrdableOptionsDto,
    );
  }

  update(
    updateOrdableOptionsDto: UpdateOrdableOptionsDto,
    store: Store,
  ): Promise<any> {
    const URL = store.apiBaseUrl + this.OPTIONS_URI;
    const API_KEY = store.apiKey;
    return this.ordableHttpRequestsService.createOrdablePatchRequest(
      URL,
      API_KEY,
      updateOrdableOptionsDto,
    );
  }

  findAll(store: Store): Promise<any> {
    const URL = store.apiBaseUrl + this.OPTIONS_URI;
    const API_KEY = store.apiKey;
    return this.ordableHttpRequestsService.createOrdableGetRequest(
      URL,
      API_KEY,
    );
  }
}
