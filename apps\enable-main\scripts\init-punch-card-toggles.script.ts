// EBL-4696 Punch card v1.3 - Rewarding Scheme
// Init has StampOnEarn and hasStampOnRedeem
// Also rename achievementBorderDisplay to achievementStampOnEarn

db.punchcards.updateMany(
  { hasStampOnEarn: { $exists: false } },
  { $set: { hasStampOnEarn: true } },
);

db.customers.updateMany(
  {
    'punchCardProgress.punchCard': { $exists: true },
    'punchCardProgress.punchCard.hasStampOnEarn': { $exists: false },
  },
  { $set: { 'punchCardProgress.$[].punchCard.hasStampOnEarn': true } },
);

db.punchcards.updateMany(
  { hasStampOnRedeem: { $exists: false } },
  { $set: { hasStampOnRedeem: false } },
);

db.punchcards.updateMany(
  { achievementBorderDisplay: { $exists: true } },
  { $rename: { achievementBorderDisplay: 'achievementStampOnEarn' } },
);

db.customers
  .find(
    {
      'punchCardProgress.punchCard.achievementBorderDisplay': { $exists: true },
    },
    { punchCardProgress: 1 },
  )
  .forEach(({ _id, punchCardProgress }) => {
    const newPunchCardProgress = punchCardProgress.map(
      ({
        punchCard: { achievementBorderDisplay, ...punchCard },
        ...progress
      }) => ({
        ...progress,
        punchCard: {
          ...punchCard,
          achievementStampOnEarn: achievementBorderDisplay,
        },
      }),
    );
    db.customers.updateOne(
      { _id },
      { $set: { punchCardProgress: newPunchCardProgress } },
    );
  });
