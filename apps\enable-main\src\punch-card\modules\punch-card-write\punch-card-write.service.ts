import {
  CompanyDocument,
  CounterMenuItem,
  CounterMenuItemSource,
  CreatePunchCardDto,
  Image,
  IMAGE_URLS,
  MAX_PUNCH_CARDS,
  PunchCardCounter,
  PunchCardDocument,
  RequirementType,
  UpdatePunchCardDto,
} from '@app/shared-stuff';
import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { plainToInstance } from 'class-transformer';
import { Types } from 'mongoose';
import { CompanyService } from '../../../company/services/company/company.service';
import { PassConfigService } from '../../../passes/services/pass-config/pass-config.service';
import { MenuItemService } from '../../../restaurant/services/menu-item/menu-item.service';
import { ImageService } from '../../../shared/services/image/image.service';
import { PunchCardReadService } from '../punch-card-read/punch-card-read.service';
import { PunchCardRepository } from '../punch-card-repository/punch-card.repository';

@Injectable()
export class PunchCardWriteService {
  private readonly logger = new Logger(PunchCardWriteService.name);
  constructor(
    private readonly punchCardRepository: PunchCardRepository,
    private readonly punchCardReadService: PunchCardReadService,
    private readonly eventEmitter: EventEmitter2,
    private readonly menuItemService: MenuItemService,
    private readonly imageService: ImageService,
    private readonly companyService: CompanyService,
    private readonly passConfigService: PassConfigService,
    private readonly configService: ConfigService,
  ) {}

  public async create({
    companyId,
    counterType,
    menuItemSources,
    nameEn,
    ...rest
  }: CreatePunchCardDto): Promise<PunchCardDocument> {
    const company = await this.companyService.findById(companyId);
    if (!company.loyaltyProgramConfig?.hasLoyaltyPunchCards)
      throw new BadRequestException(
        `Company ${companyId} does not have loyalty punch cards feature toggle.`,
      );

    const punchCards =
      await this.punchCardRepository.countByCompanyId(companyId);
    if (punchCards >= MAX_PUNCH_CARDS) {
      throw new BadRequestException(
        `Company ${companyId} already has the maximum number of punch cards (${MAX_PUNCH_CARDS}).`,
      );
    }

    const punchCardId = new Types.ObjectId();
    const counter = await this.createCounter(
      company,
      punchCardId,
      nameEn,
      counterType,
      menuItemSources,
    );
    const punchCard = await this.punchCardRepository.create({
      ...rest,
      _id: punchCardId,
      nameEn,
      companyId,
      counter,
      achievements: [],
      track: punchCards,
    });
    return punchCard;
  }

  async createCounterMenuItem(
    company: CompanyDocument,
    punchCardId: Types.ObjectId,
    punchCardNameEn: string,
    menuItemSources: CounterMenuItemSource[],
  ): Promise<CounterMenuItem> {
    if (!menuItemSources || menuItemSources.length === 0)
      throw new BadRequestException(
        'Cannot create menu item counter without menu item sources.',
      );

    const fallbackImage: Image = {
      for: 'cake',
      name: 'cake_filled.png',
      url: IMAGE_URLS.FILLED_STAMP,
      alt: '',
      description: '',
    };

    if (menuItemSources.length === 1) {
      const masterMenuItemId = menuItemSources[0].masterMenuItemId;
      const menuItem = await this.menuItemService.getDetails(
        masterMenuItemId.toString(),
      );

      if (!menuItem)
        throw new BadRequestException(
          `Menu item with ID ${masterMenuItemId} not found.`,
        );

      return {
        masterMenuItemId,
        nameEn: menuItem.nameEn,
        image: this.menuItemService.getImageUrl(menuItem) || fallbackImage.url,
        images: menuItem.images,
        externalImage: menuItem.externalImage || fallbackImage.url,
        eligibleMenuItemIds: (menuItem.similarMenuItems || []).concat(
          menuItem._id,
        ),
      };
    }

    const passConfig = await this.passConfigService.getPassConfig(company._id);
    const stamp =
      passConfig?.stripImageConfig?.stampsConfig?.filledStamp || fallbackImage;
    return {
      masterMenuItemId: punchCardId,
      nameEn: punchCardNameEn,
      image: stamp.url,
      images: [stamp],
      externalImage: '',
      eligibleMenuItemIds: await this.getEligibleMenuItemIds(
        menuItemSources,
        punchCardId,
      ),
    };
  }

  private async getEligibleMenuItemIds(
    menuItemSources: CounterMenuItemSource[],
    punchCardId: Types.ObjectId,
  ): Promise<Types.ObjectId[]> {
    const menuItemIds = menuItemSources.map((item) => item.masterMenuItemId);
    const menuItems = await this.menuItemService.findManyById(menuItemIds);

    return menuItems
      .flatMap((menuItem) => menuItem.similarMenuItems || [])
      .concat(menuItemIds)
      .concat(punchCardId);
  }

  private async createCounter(
    company: CompanyDocument,
    punchCardId: Types.ObjectId,
    punchCardNameEn: string,
    counterType: RequirementType,
    menuItemSources?: CounterMenuItemSource[],
  ): Promise<PunchCardCounter> {
    if (
      counterType === RequirementType.NUMBER_OF_ORDERS ||
      counterType === RequirementType.NUMBER_OF_LOYALTY_ORDERS
    )
      return plainToInstance(PunchCardCounter, { type: counterType });

    if (counterType !== RequirementType.NUMBER_OF_MENU_ITEMS)
      throw new BadRequestException(
        `Counter type ${counterType} is not supported.`,
      );

    if (!menuItemSources || menuItemSources.length === 0)
      throw new BadRequestException(
        `Since counterType is ${counterType}, menuItemSources are required.`,
      );

    return plainToInstance(PunchCardCounter, {
      type: counterType,
      menuItem: await this.createCounterMenuItem(
        company,
        punchCardId,
        punchCardNameEn,
        menuItemSources,
      ),
      menuItemSources,
    });
  }

  public async update(
    punchCardId: Types.ObjectId,
    { counterType, menuItemSources, ...otherUpdates }: UpdatePunchCardDto,
  ): Promise<PunchCardDocument> {
    const punchCard = await this.punchCardReadService.findById(punchCardId);

    if (otherUpdates) punchCard.set(otherUpdates);
    await punchCard.save();

    const hasCounterTypeChanged =
      counterType && punchCard.counter.type === counterType;
    if (!hasCounterTypeChanged && !menuItemSources) {
      this.eventEmitter.emit('punchcard.updated', punchCard);
      return punchCard;
    }

    const company = await this.companyService.findById(punchCard.companyId);
    const newCounter = await this.createCounter(
      company,
      punchCard._id,
      otherUpdates?.nameEn ?? punchCard.nameEn,
      counterType ?? punchCard.counter.type,
      menuItemSources ?? punchCard.counter.menuItemSources,
    );
    const updatedPunchCard = await this.punchCardRepository.updateCounter(
      punchCardId,
      newCounter,
    );
    this.eventEmitter.emit('punchcard.updated', updatedPunchCard);
    return updatedPunchCard;
  }
}
