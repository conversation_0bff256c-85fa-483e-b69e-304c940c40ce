import {
  LoggerService,
  OrderDeliveryAction,
  OrderDocument,
  OrderItem,
  OrderPaymentMethod,
} from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { CustomerReadServiceInterface } from '../../../../customer/modules/customer-read/customer-read.service.interface';
import { LocationType } from '../../../../shared/enums/location-type.enum';
import { HelperService } from '../../../../shared/services/helper/helper.service';
import { CreateSolutionErpOrderDto } from '../dtos/create-solution-erp-order.dto';
import { SolutionERPOrderType } from '../enums/solution-erp-order-type.enum';
import { SolutionERPCustomer } from '../types/solution-erp-customer.type';
import { SolutionERPItem } from '../types/solution-erp-item.type';
import { SolutionERPServiceInterface } from './solution-erp.service.interface';

@Injectable()
export class SolutionERPService implements SolutionERPServiceInterface {
  private readonly loggerService = new LoggerService(SolutionERPService.name);

  constructor(
    private configService: ConfigService,
    private httpService: HttpService,
    @Inject(CustomerReadServiceInterface)
    private customerReadService: CustomerReadServiceInterface,
    private helperService: HelperService,
  ) {}

  async constructCreateSolutionErpOrderDto(
    order: OrderDocument,
  ): Promise<CreateSolutionErpOrderDto> {
    const isPickupOrder =
      order.delivery_action === OrderDeliveryAction.IN_STORE_PICKUP;
    const deliveryAddress = isPickupOrder
      ? 'Pickup'
      : this.helperService.convertLocationToString(
          order.deliveryLocation,
          LocationType.DELIVERY,
        );
    const deliveryDate = isPickupOrder
      ? order.pickup_date
      : order.delivery_date;

    let solutionERPCustomer: SolutionERPCustomer;
    let solutionERPItems: SolutionERPItem[];
    if (order.customer)
      solutionERPCustomer = await this.constructSolutionERPCustomer(
        order.customer,
        order.company,
        deliveryAddress,
        order.payment_method,
      );
    if (order.items)
      solutionERPItems = this.constructSolutionERPItems(order.items);

    return {
      customer: solutionERPCustomer,
      orderDate: deliveryDate,
      deliveryName: order.customer_name,
      deliveryAddress: deliveryAddress,
      deliveryDate: deliveryDate,
      deliveryTime: moment(deliveryDate).format('HH:mm:ss'),
      priority: 0,
      comments: order.order_remarks,
      items: solutionERPItems,
      shippingCharge: order.delivery_amount,
      orderTotal: order.total_amount,
      orderType: isPickupOrder
        ? SolutionERPOrderType.TAKE_AWAY
        : SolutionERPOrderType.DELIVERY,
      paymentMethod: this.getMappedPaymentMethod(order.payment_method),
      discount: order.total_discount.toString(),
      contact: order.customer_name,
    };
  }

  private getMappedPaymentMethod(paymentMethod: OrderPaymentMethod): string {
    if (paymentMethod === OrderPaymentMethod.online) return 'Skip Cash';
    else if (paymentMethod == OrderPaymentMethod.card_machine)
      return 'Card Machine';
    else return 'cash'; //as a default value ( business requirement)
  }

  private constructSolutionERPItems(
    orderItems: OrderItem[],
  ): SolutionERPItem[] {
    return orderItems.map((orderItem: OrderItem) => ({
      stock_id: orderItem.solutionErpId,
      description: orderItem.name.toString(),
      uom: 'each',
      quantity: orderItem.quantity.toString(),
      price: (orderItem.totalAmount / orderItem.quantity).toString(), // basePrice + modifiers
      discount: '0',
      total: orderItem.totalAmount.toString(),
      notes: '',
    }));
  }

  private async constructSolutionERPCustomer(
    customerId: Types.ObjectId,
    companyId: Types.ObjectId,
    deliveryAddress: string,
    orderPaymentMethod: OrderPaymentMethod,
  ): Promise<SolutionERPCustomer> {
    const customer = await this.customerReadService.findOne(
      customerId.toHexString(),
      companyId,
    );
    return {
      customer_code: customer._id,
      name: this.getMappedPaymentMethod(orderPaymentMethod),
      address: deliveryAddress,
      phone: customer.phone,
      phone2: '',
      fax: '',
      email: customer.email,
      notes: '',
    };
  }

  async create(
    createSolutionErpOrderDto: CreateSolutionErpOrderDto,
    apiKey: string,
  ): Promise<unknown> {
    const URL = this.configService.get('SALES_ORDER_URI');
    const headers = this.constructRequestHeaders(apiKey);
    return new Promise(async (resolve) => {
      this.httpService
        .post(URL, createSolutionErpOrderDto, {
          headers: headers,
        })
        .subscribe({
          next: (data) => {
            resolve(data.data ?? {});
          },
          error: (err) => {
            this.loggerService.error(
              'Error ocurred with message : ' + err.message,
              err.stacktrace,
              createSolutionErpOrderDto,
            );
            resolve({ status: 'error', message: JSON.stringify(err) });
          },
        });
    });
  }

  private constructRequestHeaders(apiKey: string) {
    const CONTENT_TYPE = 'application/json';
    return {
      Authorization: `bearer ${apiKey}`,
      Accept: CONTENT_TYPE,
      'Content-Type': CONTENT_TYPE,
    };
  }
}
