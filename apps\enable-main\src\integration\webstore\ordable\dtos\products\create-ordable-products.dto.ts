import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsString,
} from 'class-validator';

export class CreateOrdableProductsDto {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  ar_name: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNumber()
  price: number;

  @ApiProperty({
    type: Number,
    required: true,
    description: 'Obtain from the categories API',
  })
  @IsInt()
  category_id: number;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Full URL',
  })
  @IsString()
  photo: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsString()
  sku: string;

  @ApiProperty({
    type: Boolean,
    required: false,
  })
  @IsBoolean()
  is_active: boolean;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsString()
  description: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsString()
  ar_description: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsString()
  short_description: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsString()
  ar_short_description: string;

  @ApiProperty({
    type: String,
    required: false,
    description:
      'name or id of a branch to automatically add the product to that branch',
  })
  @IsString()
  branch_id: string;
}
