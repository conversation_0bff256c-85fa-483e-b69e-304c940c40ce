import {
  areObjectIdsEqual,
  CreatePassConfigDto,
  omit,
  PassConfig,
  PassConfigDocument,
  PassOwnerType,
  PassTypeIdentifierConfig,
  UpdatePassConfigDto,
  UploadPassConfigImageDto,
  UploadPassConfigImageResponseDto,
} from '@app/shared-stuff';
import { BadRequestException, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { plainToInstance } from 'class-transformer';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { generate } from 'randomstring';
import { GoogleCloudStorageService } from '../../../storage/google-cloud-storage.service';
import { PassConfigRepository } from '../../repositories/pass-config.repository';
import { PassTypeIdentifierConfigRepository } from '../../repositories/pass-type-identifier-config.repository';

@Injectable()
export class PassConfigService {
  constructor(
    private readonly passConfigRepository: PassConfigRepository,
    private readonly passTypeIdentifierConfigRepository: PassTypeIdentifierConfigRepository,
    private readonly eventEmitter: EventEmitter2,
    private readonly googleCloudStorageService: GoogleCloudStorageService,
  ) {}

  async uploadImage(
    uploadImageDto: UploadPassConfigImageDto,
  ): Promise<UploadPassConfigImageResponseDto> {
    return this.googleCloudStorageService.getSignedUrl(
      this.buildImagePath(uploadImageDto),
    );
  }

  private buildImagePath(uploadImageDto: UploadPassConfigImageDto): string {
    const ownerId = uploadImageDto.ownerId ?? uploadImageDto.companyId;
    const folder = uploadImageDto.for ?? 'unknown';
    return `images/${uploadImageDto.companyId}/passImages/${ownerId}/${folder}/${moment.utc().toISOString()} ${generate(10)}`;
  }

  public async create(
    createPassConfigDto: CreatePassConfigDto,
  ): Promise<PassConfig> {
    await this.validatePassConfigCreation(createPassConfigDto);

    const passConfig = await this.parseCreatePassConfigDto(createPassConfigDto);

    const passConfigDocument =
      await this.passConfigRepository.create(passConfig);

    return passConfigDocument;
  }

  public async update(
    passConfigId: Types.ObjectId,
    updatePassConfigDto: UpdatePassConfigDto,
  ): Promise<PassConfig> {
    const existingPassConfig = await this.getPassConfigById(passConfigId);

    await this.validatePassConfigUpdate(
      existingPassConfig,
      updatePassConfigDto,
    );

    const updates = await this.parseUpdatePassConfigDto(
      existingPassConfig,
      updatePassConfigDto,
    );

    const updatedPassConfig = await this.passConfigRepository.findOneAndUpdate(
      { _id: passConfigId },
      updates,
    );

    if (updatePassConfigDto.refresh)
      this.eventEmitter.emit('passconfig.updated', updatedPassConfig);

    return updatedPassConfig;
  }

  async parseUpdatePassConfigDto(
    existingPassConfig: PassConfigDocument,
    updatePassConfigDto: UpdatePassConfigDto,
  ): Promise<Partial<PassConfig>> {
    const updates: Partial<PassConfig> = omit(updatePassConfigDto, [
      'refresh',
      'allowOwnerUpdate',
      'passTypeIdentifier',
    ]);

    if (updatePassConfigDto.passTypeIdentifier)
      updates.passTypeIdentifierConfig =
        await this.passTypeIdentifierConfigRepository.findByPassTypeIdentifier(
          updatePassConfigDto.passTypeIdentifier,
        );

    return this.mergeConfigs(existingPassConfig, updatePassConfigDto);
  }

  async validatePassConfigUpdate(
    existingPassConfig: PassConfig,
    updatePassConfigDto: UpdatePassConfigDto,
  ) {
    const isOwnerChanged =
      updatePassConfigDto.owner &&
      !areObjectIdsEqual(
        updatePassConfigDto.owner._id,
        existingPassConfig.owner._id,
      );
    if (isOwnerChanged && !updatePassConfigDto.allowOwnerUpdate)
      throw new BadRequestException(
        'This config belongs to a different owner. Either send the passConfigId for the correct owner, or send \'"allowOwnerUpdate": true\' in the payload.',
      );
  }

  private async parseCreatePassConfigDto(
    createPassConfigDto: CreatePassConfigDto,
  ): Promise<PassConfig> {
    const passConfig: PassConfig = plainToInstance(PassConfig, {
      ...omit(createPassConfigDto, ['passTypeIdentifier']),
      passTypeIdentifierConfig:
        await this.getPassTypeIdentifierConfig(createPassConfigDto),
    });

    return passConfig;
  }

  private async getPassTypeIdentifierConfig(
    createPassConfigDto: CreatePassConfigDto,
  ): Promise<PassTypeIdentifierConfig | undefined> {
    const hasIdentifier = !!createPassConfigDto.passTypeIdentifier;
    const needsIdentifier =
      createPassConfigDto.owner.type === PassOwnerType.COMPANY;
    if (!hasIdentifier && !needsIdentifier) return undefined;

    const passTypeIdentifierConfig =
      await this.passTypeIdentifierConfigRepository.findByPassTypeIdentifier(
        createPassConfigDto.passTypeIdentifier,
      );
    return passTypeIdentifierConfig.toObject();
  }

  private async validatePassConfigCreation(
    createPassConfigDto: CreatePassConfigDto,
  ) {
    const isExistingOwnerId = await this.passConfigRepository.isExistingOwnerId(
      createPassConfigDto.owner._id,
    );
    if (isExistingOwnerId)
      throw new BadRequestException(
        `Pass Config already exists for owner ID "${createPassConfigDto.owner._id}"`,
      );
  }

  public getPassConfigById(
    passConfigId: Types.ObjectId,
  ): Promise<PassConfigDocument> {
    return this.passConfigRepository.findById(passConfigId);
  }

  public async getPassConfig(
    companyId: Types.ObjectId,
    brandId?: Types.ObjectId,
    loyaltyTierId?: Types.ObjectId,
  ): Promise<PassConfig> {
    const companyConfig =
      await this.passConfigRepository.findByOwnerId(companyId);

    if (!brandId && !loyaltyTierId) return companyConfig;

    const brandConfig = await this.passConfigRepository.findByOwnerId(brandId);

    if (!brandConfig && !loyaltyTierId) return companyConfig;
    if (!companyConfig && !loyaltyTierId) return brandConfig;

    const loyaltyTierConfig =
      await this.passConfigRepository.findByOwnerId(loyaltyTierId);

    if (!loyaltyTierConfig && !companyConfig) return brandConfig;
    if (!loyaltyTierConfig && !brandConfig) return companyConfig;
    if (!companyConfig && !brandConfig) return loyaltyTierConfig;

    return this.mergeConfigs(
      this.mergeConfigs(companyConfig, brandConfig),
      loyaltyTierConfig,
    );
  }

  private mergeConfigs(
    base: PassConfig,
    override: Partial<PassConfig>,
  ): PassConfig {
    return {
      passTypeIdentifierConfig:
        override?.passTypeIdentifierConfig ?? base?.passTypeIdentifierConfig,
      passTemplate: {
        ...base?.passTemplate,
        ...override?.passTemplate,
      },
      stripImageConfig: {
        ...base?.stripImageConfig,
        ...override?.stripImageConfig,
        barConfig: {
          ...base?.stripImageConfig?.barConfig,
          ...override?.stripImageConfig?.barConfig,
        },
        minibarConfig: {
          ...base?.stripImageConfig?.minibarConfig,
          ...override?.stripImageConfig?.minibarConfig,
        },
        stampsConfig: {
          ...base?.stripImageConfig?.stampsConfig,
          ...override?.stripImageConfig?.stampsConfig,
        },
      },
      fieldConfig: {
        ...base?.fieldConfig,
        ...override?.fieldConfig,
      },
      geofencingConfig: {
        ...base?.geofencingConfig,
        ...override?.geofencingConfig,
      },
      barcodePayload: override?.barcodePayload ?? base?.barcodePayload,
      owner: override?.owner ?? base?.owner,
    };
  }
}
