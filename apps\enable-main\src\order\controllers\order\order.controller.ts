import {
  AggregatorOrderToCreate,
  GenericExceptionFilter,
  OrderCancellationReason,
  OrderPosToCreate,
  responseCode,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  SetMetadata,
  UseFilters,
  UseInterceptors,
  Version,
} from '@nestjs/common';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { Request, Response } from 'express';
import { Types } from 'mongoose';
import {
  OrderIndex,
  OrderStatusToUpdate,
  OrderToAssign,
  OrderToCancel,
  OrderToReject,
} from '../../../order/dto/order.dto';
import { itemToRemove, ItemToUpdate } from '../../../order/dto/orderItem.dto';
import { AggregatorOrderService } from '../../../order/services/aggregatorOrder/aggregatorOrder.service';
import { OrderItemService } from '../../../order/services/order-item/order-item.service';
import { OrderService } from '../../../order/services/order/order.service';
import { HelperService } from '../../../shared/services/helper/helper.service';
import { ChangeOrderPaymentStatusDto } from '../../dto/change-order-payment-status.dto';
import { ReadyOrderDto } from '../../dto/ready-order.dto';
import { RefireOrderAcknowledgementDto } from '../../dto/refire-order-acknowledgement.dto';
import { OrderNotificationService } from '../../services/order-notification/order-notification.service';
import { TookanTaskWebhookService } from '../../services/order-tookan/tookan-task-webhook-service';
import { OrderDetailsEditService } from '../../services/order/order-details-edit.service';
import {
  AcknowledgeOrderDto,
  OrderDeliveryAddressToUpdate,
  OrderDeliverySmsToSend,
  OrderDetailsToEdit,
  OrderDispatchToEdit,
  OrderDriverToChange,
  OrderPaymentMethodToChange,
} from './../../dto/order.dto';
import { OrderItemToAdd, OrderItemToIndex } from './../../dto/orderItem.dto';
import { OrderPosService } from './../../services/order-pos/order-pos.service';

@ApiTags('Order')
@SetMetadata('module', 'order')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@Controller('order')
export class OrderController {
  constructor(
    private orderService: OrderService,
    private helperService: HelperService,
    private orderItemService: OrderItemService,
    private orderPosService: OrderPosService,
    private aggregatorOrderService: AggregatorOrderService,
    private orderNotification: OrderNotificationService,
    private readonly orderDetailsEditService: OrderDetailsEditService,
    private readonly tookanTaskWebhookService: TookanTaskWebhookService,
  ) {}

  @Get()
  @SetMetadata('action', 'get_all')
  async index(
    @Query() orderIndex: OrderIndex,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      orderIndex.company = req['company_id']
        ? req['company_id']
        : orderIndex.company;

      orderIndex.branch = req['branch_id']
        ? req['branch_id']
        : orderIndex.branch;

      orderIndex.filter_branches = req['branches']
        ? req['branches']
        : orderIndex.filter_branches;

      orderIndex.companyIds = req['companies']
        ? req['companies'].toString()
        : '';

      const selectedOrders = await this.orderService.index(orderIndex);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'Success to get all orders',
        {
          orders: selectedOrders[0]['paginatedResults'],
          totalOrders: selectedOrders[0]['totalCount'][0]
            ? selectedOrders[0]['totalCount'][0]['createdAt']
            : 0,
          counts: [],
          orderUpdates: 0,
        },
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() orderToCreate: OrderPosToCreate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      orderToCreate.company = req['company_id']
        ? req['company_id']
        : orderToCreate.company;
      orderToCreate.current_user = req['current'];

      orderToCreate.brandId =
        !orderToCreate.brandId && !orderToCreate.brand
          ? req['brand']
            ? req['brand']['_id']
            : undefined
          : orderToCreate.brandId;

      const createOrder = await this.orderPosService.create(orderToCreate);

      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'Success to create order',
        createOrder,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('create/pos')
  @SetMetadata('action', 'createPosOrder')
  async createPosOrder(
    @Body() orderPosToCreate: OrderPosToCreate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      orderPosToCreate.company = req['company_id']
        ? (new Types.ObjectId(req['company_id']) as any)
        : new Types.ObjectId(orderPosToCreate.company);
      orderPosToCreate.current_user = req['current'];

      orderPosToCreate.brandId =
        !orderPosToCreate.brandId && !orderPosToCreate.brand
          ? req['brand']
            ? req['brand']['_id']
            : undefined
          : orderPosToCreate.brandId;

      const order = await this.orderPosService.create(orderPosToCreate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'Success to create order from POS',
        order,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post(['create/aggregator', 'capture'])
  @SetMetadata('action', 'createAggregatorOrder')
  async createAggregatorOrder(
    @Body() aggregatorOrderToCreate: AggregatorOrderToCreate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      aggregatorOrderToCreate.companyId = req['company_id']
        ? (new Types.ObjectId(req['company_id']) as any)
        : new Types.ObjectId(aggregatorOrderToCreate.companyId);
      aggregatorOrderToCreate.currentUser = req['current'];

      const order = await this.aggregatorOrderService.create(
        aggregatorOrderToCreate,
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'Success To Capture Order',
        order,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Delete(':id')
  @SetMetadata('action', 'remove')
  async remove(
    @Param('id') orderId: string,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const removedOrder = await this.orderService.remove(
        new Types.ObjectId(orderId),
        req['current'],
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_REMOVE,
        'Success To remove the order',
        removedOrder,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('status')
  @SetMetadata('action', 'update_status')
  async update_status(
    @Body() statusToUpdate: OrderStatusToUpdate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      statusToUpdate.branch = req['branch_id'] ? req['branch_id'] : '';
      const updatedOrder = await this.orderService.changeStatus(
        statusToUpdate._id,
        statusToUpdate.status,
        req['current'],
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'Success to change order status',
        updatedOrder,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get('list/status')
  @SetMetadata('action', 'getStatus')
  getOrderStatus(@Res() res: Response) {
    try {
      const statues = this.orderService.getOrderStatusAsList();

      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'Success to Get all order status',
        statues,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  async get_details(
    @Param('id') id: string,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const selectedOrder = await this.orderService.getDetailsForFront(
        id,
        req['company_id'],
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'Success to get the order details',
        selectedOrder,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get('public/:id')
  @SetMetadata('public', 'true')
  async get_public_details(
    @Param('id') id: string,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      const selectedOrder = await this.orderService.getDetailsForFront(
        id,
        null,
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'Success to get the order details',
        selectedOrder,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Put('public/delivery')
  @SetMetadata('public', 'true')
  async update_delivery_public_address(
    @Body() orderDeliveryAddressToUpdate: OrderDeliveryAddressToUpdate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      orderDeliveryAddressToUpdate.send_sms = true;
      orderDeliveryAddressToUpdate.current_user = req['current'];

      const updatedOrder = await this.orderService.update_delivery_location(
        orderDeliveryAddressToUpdate,
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'Success To Update the order delivery address',
        updatedOrder,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Put('details/:id')
  @SetMetadata('action', 'update_order_details')
  async updateOrderDetails(
    @Body() orderDetailsToEdit: OrderDetailsToEdit,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      orderDetailsToEdit.current_user = req['current'];

      const order = await this.orderDetailsEditService.editOrderDetails(
        orderDetailsToEdit,
        req['company_id'],
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'update order details',
        order,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Put('dispatch/:id')
  @SetMetadata('action', 'update_order_dispatch')
  async updateOrderDispatch(
    @Body() orderDispatchToEdit: OrderDispatchToEdit,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      orderDispatchToEdit.current_user = req['current'];

      const order = await this.orderService.editOrderDispatch(
        orderDispatchToEdit,
        req['company_id'],
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'update order dispatch details',
        order,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Put('delivery')
  @SetMetadata('action', 'update_delivery_address')
  async update_delivery_address(
    @Body() orderDeliveryAddressToUpdate: OrderDeliveryAddressToUpdate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      orderDeliveryAddressToUpdate.send_sms = false;
      orderDeliveryAddressToUpdate.current_user = req['current'];

      const updatedOrder = await this.orderService.update_delivery_location(
        orderDeliveryAddressToUpdate,
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'Success To Update the order delivery address',
        updatedOrder,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('delivery/sms')
  @SetMetadata('action', 'resend_delivery_address')
  async resend_delivery_address(
    @Body() deliveryAddressToSend: OrderDeliverySmsToSend,
    @Res() res: Response,
  ) {
    try {
      const order = await this.orderService.get_details(
        deliveryAddressToSend.order,
      );
      const updatedOrder =
        await this.orderNotification.handleDeliveryAndPaymentNotification(
          order,
        );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'Success To resend the delivery address',
        updatedOrder,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get('export/excel')
  @SetMetadata('action', 'export_excel')
  async export_excel(
    @Res() res: Response,
    @Req() req: Request,
    @Query() orderToIndex: OrderIndex,
  ) {
    try {
      orderToIndex.company = req['company_id']
        ? req['company_id']
        : orderToIndex.company;
      orderToIndex.branch = req['branch_id']
        ? req['branch_id']
        : orderToIndex.branch;
      orderToIndex.filter_branches = req['branches']
        ? req['branches']
        : orderToIndex.filter_branches;

      const fileName = await this.orderService.export_excel(orderToIndex);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'Success to get all as excel file',
        `/documents/${fileName}`,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('branch/assign')
  @SetMetadata('action', 'assign_branch')
  async assignOrderToBranch(
    @Body() orderToAssign: OrderToAssign,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      //TODO: Make sure that you will need to check that the user has  a branch or not

      const assignORders = await this.orderService.assignOrderToBranch(
        orderToAssign,
        req['current'],
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'Success to assign order to branch',
        assignORders,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('branch/reject')
  @SetMetadata('action', 'reject_order')
  async rejectOrderFromBranch(
    @Body() orderToReject: OrderToReject,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      if (req['branch_id']) orderToReject.branch = req['branch_id'];
      orderToReject.currentUser = req['current'];
      const rejectedOrder =
        await this.orderService.rejectOrderFromBranch(orderToReject);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'Success to reject order from branch',
        rejectedOrder,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('ready')
  @SetMetadata('action', 'order_is_ready')
  async onOrderReady(
    @Body() orderToReady: ReadyOrderDto,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      const order = await this.orderService.onOrderReady(
        orderToReady,
        req['current'],
      );

      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'Success to make order ready',
        order,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('driver/update')
  @SetMetadata('action', 'updateOrderDriver')
  async updateDriverASsignment(
    @Body() orderDriverToChange: OrderDriverToChange,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const driver = await this.orderService.changeDeliveryMethod(
        orderDriverToChange,
        req['current'],
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'Success to assign the driver to the order',
        driver,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('cancel')
  @SetMetadata('action', 'cancel_order')
  async cancelOrder(
    @Body() orderToCancel: OrderToCancel,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      orderToCancel.currentUser = req['current'];
      const updatedOrder = await this.orderService.cancelOrder(orderToCancel);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'Success to cancel order',
        updatedOrder,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get('cancel/reasons')
  @SetMetadata('action', 'order-cancellation-reasons')
  getCancellationReasons(@Res() res: Response) {
    try {
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GET_CANCELLATION_REASONS,
        'Success to get the order cancellation reasons',
        Object.values(OrderCancellationReason),
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('item/update')
  @SetMetadata('action', 'update_item')
  async update_order_item(
    @Body() itemToUpdate: ItemToUpdate,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      const updatedOrderItem = await this.orderItemService.update(itemToUpdate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'Success to update order item',
        updatedOrderItem,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('item/remove')
  @SetMetadata('action', 'remove_item')
  async remove_order_item(
    @Body() itemToRemove: itemToRemove,
    @Res() res: Response,
  ) {
    try {
      const removedOrderItem = await this.orderItemService.remove(itemToRemove);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'Success to remove order item',
        removedOrderItem,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get('item/all')
  @SetMetadata('action', 'index_items')
  async getAllItems(
    @Query() orderItemToIndex: OrderItemToIndex,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      const items = await this.orderItemService.index(orderItemToIndex);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'Success to get all  order items',
        items,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('item/create')
  @SetMetadata('action', 'create_item')
  async create_order_item(
    @Body() orderItemToAdd: OrderItemToAdd,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      const item = await this.orderItemService.addToOrder(orderItemToAdd);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'Success to add item to order',
        item,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('tookan/status')
  @SetMetadata('public', 'true')
  async tookan_task_updated(
    @Body() data,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      const updateData =
        await this.tookanTaskWebhookService.handleTookanTaskUpdated(data);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'Success To Update Tookan Task',
        updateData,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get('tookan/status')
  @SetMetadata('public', 'true')
  async tookan_task_updated_get(
    @Query() data,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      const updateData =
        await this.tookanTaskWebhookService.handleTookanTaskUpdated(data);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'Success To Update Tookan Task',
        updateData,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('payment_method')
  @SetMetadata('action', 'changeOrderPaymentMethod')
  async changeOrderPaymentMethod(
    @Body() orderPaymentMethodToChange: OrderPaymentMethodToChange,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      orderPaymentMethodToChange.companyId = req['company_id'];

      orderPaymentMethodToChange.currentUser = req['current'];
      const order = await this.orderService.changePaymentMethod(
        orderPaymentMethodToChange,
      );
      return this.helperService.handelSuccessResponse(
        HttpStatus.OK,
        'Success To Change Order Payment Method',
        { order },
        res,
      );
    } catch (error) {
      return this.helperService.handelError(error, res);
    }
  }

  @Post('notification/acknowledge')
  @SetMetadata('action', 'orderNotificationAcknowledge')
  async orderNotificationAcknowledge(
    @Body() acknowledgeOrderDto: AcknowledgeOrderDto,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      acknowledgeOrderDto.currentUser = req['current'];
      const order =
        await this.orderService.acknowledgeOrder(acknowledgeOrderDto);
      return this.helperService.handelSuccessResponse(
        HttpStatus.OK,
        'Success To Acknowledge the order',
        { order },
        res,
      );
    } catch (error) {
      return this.helperService.handelError(error, res);
    }
  }

  @Post('acknowledge/refire')
  @SetMetadata('action', 'orderAcknowledgeRefire')
  async refireOrderAcknowledgement(
    @Body() refireOrderAcknowledgementDto: RefireOrderAcknowledgementDto,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      refireOrderAcknowledgementDto.userId = req['current']['id'];
      const order = await this.orderService.refireOrderAcknowledgement(
        refireOrderAcknowledgementDto,
      );
      return this.helperService.handelSuccessResponse(
        HttpStatus.OK,
        'Success To re-fire the acknowledge',
        { order },
        res,
      );
    } catch (error) {
      return this.helperService.handelError(error, res);
    }
  }

  @Get('tracking/:uniqueIdentifier')
  @SetMetadata('public', 'true')
  async findTrackingOrder(
    @Param('uniqueIdentifier') uniqueIdentifier: string,
    @Res() res: Response,
  ) {
    try {
      const result =
        await this.orderService.findTrackingOrder(uniqueIdentifier);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GET_ONE,
        'Success To Get Tracking Order',
        result,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get('public/tracking/:orderCode')
  @Version('2')
  @SetMetadata('public', 'true')
  @ApiOperation({
    summary: 'Get tracking order for map',
    description: 'Get tracking order for map',
  })
  async findTrackingOrderForMap(
    @Param('orderCode') orderCode: string,
    @Res() res: Response,
  ) {
    try {
      const result = await this.orderService.findTrackingOrderForMap(orderCode);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GET_ONE,
        'Success To Get Tracking Order with Map Details',
        result,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('change/paymentStatus')
  @UseInterceptors(TransformInterceptor)
  @UseFilters(GenericExceptionFilter)
  @SetMetadata('action', 'changeOrderPaymentStatus')
  async changeOrderPaymentStatus(
    @Body() changeOrderPaymentStatusDto: ChangeOrderPaymentStatusDto,
  ) {
    return await this.orderService.changeOrderPaymentStatus(
      changeOrderPaymentStatusDto,
    );
  }
}
