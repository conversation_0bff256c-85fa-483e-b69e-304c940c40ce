import { CustomerDocument, RegisteredPass } from '@app/shared-stuff';

export interface CustomerPassServiceInterface {
  applyPassGenerationPreFunctions(customer: CustomerDocument): Promise<void>;

  applyPassUpdatePostFunctions(registeredPass: RegisteredPass): Promise<void>;

  savePassLink(
    customer: CustomerDocument,
    brandId: string,
    passLink: string,
  ): Promise<void>;
}

export const CustomerPassServiceInterface = Symbol(
  'CustomerPassServiceInterface',
);
