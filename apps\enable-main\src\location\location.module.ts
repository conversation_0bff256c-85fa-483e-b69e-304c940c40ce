import {
  CollectionName,
  CustomerSchema,
  LocationItemSchema,
  SavedLocationSchema,
  SharedStuffModule,
} from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { MongooseModule } from '@nestjs/mongoose';
import { SharedModule } from './../shared/shared.module';
import { AreaController } from './controllers/area/area.controller';
import { CityController } from './controllers/city/city.controller';
import { CountryController } from './controllers/country/country.controller';
import { LocationItemController } from './controllers/location-item/location-item.controller';
import { SavedLocationController } from './controllers/saved-location/saved-location.controller';
import { LocationItemRepositoryInterface } from './repositories/location-item/location-item-repository.interface';
import { LocationItemRepository } from './repositories/location-item/location-item.repository';
import { AreaService } from './services/area/area.service';
import { LocationItemServiceInterface } from './services/location-item/location-item-service.interface';
import { LocationItemService } from './services/location-item/location-item.service';
import { SavedLocationService } from './services/saved-location/saved-location.service';

@Module({
  controllers: [
    CityController,
    AreaController,
    CountryController,
    SavedLocationController,
    LocationItemController,
  ],
  providers: [
    SavedLocationService,
    AreaService,
    {
      provide: LocationItemRepositoryInterface,
      useClass: LocationItemRepository,
    },
    {
      provide: LocationItemServiceInterface,
      useClass: LocationItemService,
    },
  ],
  imports: [
    SharedModule,
    SharedStuffModule,
    EventEmitterModule,
    MongooseModule.forFeature([
      { name: CollectionName.LOCATION_ITEM, schema: LocationItemSchema },
      { name: CollectionName.SAVED_LOCATION, schema: SavedLocationSchema },
      { name: CollectionName.CUSTOMER, schema: CustomerSchema },
    ]),
  ],
  exports: [SavedLocationService, LocationItemServiceInterface],
})
export class LocationModule {}
