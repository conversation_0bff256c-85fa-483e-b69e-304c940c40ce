import { CollectionName, responseCode } from '@app/shared-stuff';
import { Injectable, NestMiddleware } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { plainToInstance } from 'class-transformer';
import { NextFunction, Request, Response } from 'express';
import { JwtPayload, verify } from 'jsonwebtoken';
import { RoleService } from '../../rbac/services/role/role.service';
import { AuthType } from '../../user/enums/token-type.enum';
import { ApikeyService } from '../../user/services/apikey/apikey.service';
import { AuthService } from '../../user/services/auth/auth.service';
import { CustomerTokenPayload } from '../dto/customer-token-payload.dto';
import { HelperService } from '../services/helper/helper.service';

@Injectable()
export class AuthenticateUserMiddleware implements NestMiddleware {
  constructor(
    private authService: AuthService,
    private apikeyService: ApikeyService,
    private configService: ConfigService,
    private helperService: HelperService,
    private roleService: RoleService,
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    try {
      const bearerToken = req.headers.authorization;
      const apikey =
        req.headers.saasapikey ||
        req.headers.API_KEY ||
        req.headers.api_key ||
        req.query['API_KEY'];
      if (!bearerToken && !apikey) {
        return res.status(400).send({
          code: responseCode.TOKEN_NOT_FOUND,
          message: 'Please Provide token or api key to procceed',
        });
      }
      if (bearerToken) {
        const token = bearerToken.split(' ')[1];
        const tokenExpiredCheck =
          await this.authService.checkIfTokenInExpired(token);

        if (tokenExpiredCheck) {
          return res.status(400).send({
            message: 'Your token is expired',
            code: responseCode.TOKEN_EXPIRED,
          });
        }

        const tokenPayload = this.authService.verifyToken(token);

        if (this.isCustomerToken(tokenPayload)) {
          await this.populateCustomerRequest(req, tokenPayload);
        } else {
          await this.populateUserRequest(req, token);
          this.validateStatus(req['user'], res);
        }
      } else if (apikey) {
        await this.populateApikeyRequest(req, res, apikey as string);
        this.validateStatus(req['apikey'], res);
      }
      next();
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  private async populateApikeyRequest(
    req: Request,
    res: Response,
    apikey: string,
  ) {
    const selectedApikey = await this.apikeyService.get_me(apikey);
    req['apikey'] = selectedApikey;
    if (selectedApikey['company']) {
      req['company_id'] = selectedApikey.company['_id'];
      req['company'] = selectedApikey.company;
    } else if (
      selectedApikey.secret != req.headers['saassecret'] ||
      !selectedApikey.secret
    ) {
      return res.status(400).send({
        message: 'You Dont have access to this api',
        code: responseCode.USER_NOT_ACTIVE,
      });
    }
    if (selectedApikey.branches && selectedApikey.branches.length) {
      req['branches'] = selectedApikey.branches.map((x) => {
        return x['_id'] ? x['_id'] : x;
      });
    }

    if (selectedApikey['brand']) {
      req['brand'] = selectedApikey.brand;
    }

    req['current'] = {
      name: selectedApikey.name,
      id: selectedApikey._id,
      type: AuthType.APIKEY,
      role: selectedApikey.roles
        ? selectedApikey.roles[0]
          ? selectedApikey.roles[0]['name']
          : ''
        : '',
    };
  }

  private isCustomerToken(
    tokenPayload: any,
  ): tokenPayload is CustomerTokenPayload {
    return (
      typeof tokenPayload === 'object' &&
      'for' in tokenPayload &&
      tokenPayload.for === AuthType.CUSTOMER
    );
  }

  private async populateCustomerRequest(
    req: Request,
    customerTokenPayload: CustomerTokenPayload,
  ) {
    const { customerId, companyId } = plainToInstance(
      CustomerTokenPayload,
      customerTokenPayload,
    );
    req['customer'] = {
      _id: customerId,
      roles: [await this.roleService.getDefaultCustomerRole()],
    };
    req['company_id'] = companyId;
    req['companies'] = [companyId];
    req['current'] = {
      name: 'customer',
      id: customerId,
      phone: '',
      type: AuthType.CUSTOMER,
      role: CollectionName.CUSTOMER,
    };
  }

  private async populateUserRequest(req: Request, token: string) {
    const selectedUser = await this.authService.get_me(token);
    req['user'] = selectedUser;
    if (selectedUser.company) {
      req['company_id'] = selectedUser.company['_id'];
      req['company'] = selectedUser.company;
    }
    if (selectedUser.companies) req['companies'] = selectedUser.companies;

    if (selectedUser.branches && selectedUser.branches.length) {
      req['branches'] = selectedUser.branches.map((x) => {
        return x['_id'] ? x['_id'] : x;
      });
    }

    req['current'] = {
      name: selectedUser.name,
      id: selectedUser._id,
      phone: selectedUser.phone,
      type: AuthType.USER,
      role: selectedUser.roles
        ? selectedUser.roles[0]
          ? selectedUser.roles[0]['name']
          : ''
        : '',
    };
  }

  private validateStatus(selectedUser: any, res: Response) {
    if (selectedUser.company && selectedUser.company['status'] != 'active') {
      return res.status(400).send({
        message:
          'Your Company is not active right now, please contact administration',
        code: responseCode.COMPANY_NOT_ACTIVE,
      });
    }
    if (selectedUser.status != 'active') {
      return res.status(400).send({
        message: 'Your are no active right now, please contact administration',
        code: responseCode.USER_NOT_ACTIVE,
      });
    }
  }
}
