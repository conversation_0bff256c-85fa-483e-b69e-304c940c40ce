import {
  EmbeddedBrandDto,
  LoggerService,
  OrderDocument,
  OrderItem,
  OrderLogActionEnum,
  OrderPaymentMethod,
  OrderPaymentStatus,
  SavedLocation,
  SavedLocationType,
} from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { RawAxiosRequestHeaders } from 'axios';
import * as moment from 'moment-timezone';
import { ThirdPartyTaskCreationDto } from '../../../../dto/third-party-task-creation.dto';

import { IntegrationLogRepositoryInterface } from '../../../../../integration/integration-log/repositories/interfaces/integration-log.repository.interface';
import { SavedLocationService } from '../../../../../location/services/saved-location/saved-location.service';
import { LocationType } from '../../../../../shared/enums/location-type.enum';
import { HelperService } from '../../../../../shared/services/helper/helper.service';
import { ThirdPartiesServiceInterface } from '../../third-parties.service.interface';
import { ThirdPartySharedService } from '../../third-party-shared.service';
import { CallbackFalconFlexDto } from '../dtos/callback-falcon-flex.dto';
import { CreateFalconFlexTaskDto } from '../dtos/create-falcon-flex-task.dto';
import { FalconFlexLocationTypeId } from '../enums/falcon-flex-location-type-id.enum';
import { FalconFlexTransportTypeIdEnum } from '../enums/falcon-flex-transport-type-id.enum';
import { FalconFlexItem } from '../types/falcon-flex-item.type';
import { FalconFlexLocationCommunicationDataDto } from '../types/falcon-flex-location-communication-data.dto';
import { FalconFlexLocationInternational } from '../types/falcon-flex-location-international.type';
import { FalconFlexLocationQatar } from '../types/falcon-flex-location-qatar.type';
import { FalconFlexTaskCreationResponse } from '../types/falcon-flex-task-creation-response.type';

@Injectable()
export class FalconFlexService implements ThirdPartiesServiceInterface {
  vehicleTypes: string[] = ['bicycle', 'bike', 'car', 'truck'] as const;
  defaultVehicleType: string = 'bike';
  private readonly loggerService = new LoggerService(FalconFlexService.name);

  constructor(
    private configService: ConfigService,
    private httpService: HttpService,
    private thirdPartySharedService: ThirdPartySharedService,
    @Inject('IntegrationLogRepositoryInterface')
    private readonly integrationLogRepository: IntegrationLogRepositoryInterface,
    private readonly helperService: HelperService,
    private eventEmitter: EventEmitter2,
    private readonly savedLocationService: SavedLocationService,
  ) {}

  async createTask(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
  ): Promise<any> {
    const createFalconFlexTaskDto = await this.constructCreateFalconFlexTaskDto(
      thirdPartyTaskCreationDto,
    );

    try {
      const taskCreationResponse = await this.create(createFalconFlexTaskDto);
      this.loggerService.log(
        'Falcon Flex Task Created',
        createFalconFlexTaskDto,
        taskCreationResponse,
      );
      await this.integrationLogRepository.logSuccess(
        OrderLogActionEnum.FALCON_FLEX_TASK_CREATION,
        createFalconFlexTaskDto,
        taskCreationResponse,
        createFalconFlexTaskDto?.clientGeneratedId,
      );
      return {
        request: createFalconFlexTaskDto,
        response: taskCreationResponse,
      };
    } catch (error) {
      this.loggerService.error(
        `Error while : ${OrderLogActionEnum.FALCON_FLEX_TASK_CREATION}` +
          error.message,
        error.stacktrace,
        createFalconFlexTaskDto,
      );
      await this.integrationLogRepository.logError(
        OrderLogActionEnum.FALCON_FLEX_TASK_CREATION,
        createFalconFlexTaskDto,
        this.helperService.transformError(error),
      );
    }
  }

  async create(
    createFalconFlexTaskDto: CreateFalconFlexTaskDto,
  ): Promise<FalconFlexTaskCreationResponse> {
    const FALCON_FLEX_TOKEN = this.configService.get('FALCON_FLEX_TOKEN');
    const FALCON_FLEX_BASE_URL = this.configService.get('FALCON_FLEX_BASE_URL');
    const CREATE_TASK_URL = FALCON_FLEX_BASE_URL + '/api/v1/tasks/create';
    const HEADERS = this.constructRequestHeaders(FALCON_FLEX_TOKEN);

    return new Promise(async (resolve, reject) => {
      this.httpService
        .post(CREATE_TASK_URL, createFalconFlexTaskDto, {
          headers: HEADERS,
        })
        .subscribe({
          next: (data) => {
            resolve(data.data ?? {});
          },
          error: (error) => {
            this.loggerService.log(
              'Error while create falcon flex task' + error.message,
              error.stacktrace,
              createFalconFlexTaskDto,
            );
            reject(error);
          },
        });
    });
  }

  async applyPostFunction(
    falconFlexTaskCreationResponse: FalconFlexTaskCreationResponse,
    order: OrderDocument,
  ) {
    if (falconFlexTaskCreationResponse)
      await this.updateOrder(order, falconFlexTaskCreationResponse);
    else
      this.loggerService.error(
        'there is no response coming from Falcon Flex Service with order: ',
        { falconFlexTaskCreationResponse, order },
      );
  }

  async updateFalconFlexDeliveryTaskStatus(
    callbackFalconFlexDTO: CallbackFalconFlexDto,
  ) {
    this.integrationLogRepository.logSuccess(
      'updateFalconFlexDeliveryStatus',
      callbackFalconFlexDTO,
      {},
      callbackFalconFlexDTO.Data.ClientGeneratedId,
    );
    this.eventEmitter.emit('falcon.flex.updated', callbackFalconFlexDTO);
  }

  async cancel(order: OrderDocument) {
    const FALCON_FLEX_TOKEN = this.configService.get('FALCON_FLEX_TOKEN');
    const FALCON_FLEX_BASE_URL = this.configService.get('FALCON_FLEX_BASE_URL');
    const CANCEL_TASK_URL =
      FALCON_FLEX_BASE_URL +
      `/api/v1/tasks/cancel?id=${order.falconFlexTaskId}`;
    return new Promise(async (resolve, reject) => {
      this.httpService
        .patch(
          CANCEL_TASK_URL,
          {},
          {
            headers: {
              Authorization: FALCON_FLEX_TOKEN,
            },
          },
        )
        .subscribe({
          next: (data) => {
            resolve(data.data ?? {});
          },
          error: (error) => {
            this.loggerService.error(
              'Error While Cancel Falcon Flex Task',
              error,
            );
            reject(error);
          },
        });
    });
  }

  private constructRequestHeaders(bearerToken: string) {
    const CONTENT_TYPE = 'application/json';
    return {
      Authorization: bearerToken,
      Accept: CONTENT_TYPE,
      'Content-Type': CONTENT_TYPE,
    };
  }

  private async constructCreateFalconFlexTaskDto(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
  ): Promise<CreateFalconFlexTaskDto> {
    const pickupLocation = await this.savedLocationService.getDetails(
      thirdPartyTaskCreationDto?.order?.pickupLocationId.toHexString(),
    );

    const pickupLocationTypeId =
      pickupLocation && pickupLocation?.type === SavedLocationType.PIN_LOCATION
        ? FalconFlexLocationTypeId.PIN_LOCATION
        : FalconFlexLocationTypeId.NATIONAL_ADDRESS;
    const deliveryLocationTypeId =
      thirdPartyTaskCreationDto.order?.deliveryLocation?.type ===
      SavedLocationType.PIN_LOCATION
        ? FalconFlexLocationTypeId.PIN_LOCATION
        : FalconFlexLocationTypeId.NATIONAL_ADDRESS;

    const pickup = await this.getFalconFlexLocation(
      thirdPartyTaskCreationDto,
      pickupLocationTypeId,
      LocationType.PICKUP,
    );

    const delivery = await this.getFalconFlexLocation(
      thirdPartyTaskCreationDto,
      deliveryLocationTypeId,
      LocationType.DELIVERY,
    );

    const fallbackDate = moment()
      .utc()
      .add(1, 'minutes')
      .tz(
        thirdPartyTaskCreationDto?.order?.localization?.timezone ??
          'Asia/Qatar',
      )
      .toDate();
    const pickupDate = moment()
      .utc()
      .isAfter(thirdPartyTaskCreationDto?.order?.pickup_date)
      ? fallbackDate
      : thirdPartyTaskCreationDto?.order?.pickup_date;
    if (pickupDate === fallbackDate)
      this.loggerService.warn(
        `Pickup date expired before delivery task creation for order ${thirdPartyTaskCreationDto.order.code}`,
      );

    const deliveryDate = moment()
      .utc()
      .isAfter(thirdPartyTaskCreationDto?.order?.delivery_date)
      ? fallbackDate
      : thirdPartyTaskCreationDto?.order?.delivery_date;
    if (deliveryDate === fallbackDate)
      this.loggerService.warn(
        `Delivery date expired before delivery task creation for order ${thirdPartyTaskCreationDto.order.code}`,
      );

    const isPaidAlready =
      (thirdPartyTaskCreationDto.order.payment_method ===
        OrderPaymentMethod.online &&
        thirdPartyTaskCreationDto.order.payment_status ===
          OrderPaymentStatus.COMPLETED) ||
      thirdPartyTaskCreationDto.order.payment_method ===
        OrderPaymentMethod.prepaid;

    return {
      transportTypeId:
        FalconFlexTransportTypeIdEnum[
          thirdPartyTaskCreationDto.order.vehicleType.toUpperCase()
        ],
      amountToBeCollected: isPaidAlready
        ? 0
        : thirdPartyTaskCreationDto?.order?.total_amount,
      taskItems: this.constructFalconFlexTaskItems(
        thirdPartyTaskCreationDto?.order?.items,
      ),
      pickupByUtc: pickupDate,
      deliverByUtc: deliveryDate,
      pickupLocationTypeId: pickupLocationTypeId,
      pickup: pickup,
      deliveryLocationTypeId: deliveryLocationTypeId,
      delivery: delivery,
      clientGeneratedId: thirdPartyTaskCreationDto?.order.code,
    };
  }

  private async getFalconFlexLocation(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
    locationTypeId: number,
    locationType: LocationType,
  ): Promise<FalconFlexLocationInternational | FalconFlexLocationQatar> {
    let location: SavedLocation;
    const commonLocationData: FalconFlexLocationCommunicationDataDto = {
      Address: '',
      Name: '',
      PhoneNumber: '',
      Notes: '',
    };

    if (locationType === LocationType.PICKUP) {
      location = await this.thirdPartySharedService.getPickupLocation(
        thirdPartyTaskCreationDto,
      );
      const brandOnBranch = thirdPartyTaskCreationDto.branch?.brands?.find(
        (brand: EmbeddedBrandDto) => {
          return (
            brand._id.toHexString() ===
            thirdPartyTaskCreationDto.order.brand._id.toHexString()
          );
        },
      );
      commonLocationData.Name = thirdPartyTaskCreationDto.branch.name;
      commonLocationData.PhoneNumber = brandOnBranch
        ? brandOnBranch.phoneNumber
        : thirdPartyTaskCreationDto.customer.phone;
    } else if (locationType === LocationType.DELIVERY) {
      location = await this.thirdPartySharedService.getDeliveryLocation(
        thirdPartyTaskCreationDto,
      );
      commonLocationData.Name = thirdPartyTaskCreationDto.customer.full_name;
      commonLocationData.PhoneNumber =
        thirdPartyTaskCreationDto.customer.country_code +
        thirdPartyTaskCreationDto.customer.phone;
    }

    commonLocationData.Address = this.helperService.convertLocationToString(
      location,
      LocationType.DELIVERY,
    );

    if (locationTypeId === FalconFlexLocationTypeId.PIN_LOCATION) {
      return {
        ...commonLocationData,
        Latitude: location.latitude,
        Longitude: location.longitude,
      };
    } else {
      return {
        ...commonLocationData,
        ZoneNumber: location.zoneNumber,
        StreetNumber: location.streetNumber,
        BuildingNumber: location.buildingNumber,
      };
    }
  }

  private constructFalconFlexTaskItems(
    orderItems: OrderItem[],
  ): FalconFlexItem[] {
    if (orderItems)
      return orderItems.map((orderItem: OrderItem) => ({
        name: orderItem.name,
        quantity: orderItem.quantity,
        price: orderItem.totalAmount,
      }));
    else return [{ name: 'unknown item', price: 0, quantity: 1 }];
  }

  private async updateOrder(
    order: OrderDocument,
    falconFlexTaskCreationResponse: FalconFlexTaskCreationResponse,
  ) {
    order.assigned_driver_name = '[Snoonu] Driver';
    order.driver = undefined;
    order.deliveryTaskCreated = true;
    order.falconFlexTaskId = falconFlexTaskCreationResponse.id;
    await order.save();
  }
}
