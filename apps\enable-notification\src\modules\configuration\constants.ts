import {
  ChatConfigDto,
  ChatProvider,
  SmsConfigDto,
  SmsProvider,
} from '@app/shared-stuff';

export const smsProviderToConfigKeyMapping: Record<
  Exclude<SmsProvider, SmsProvider.NO_PROVIDER>,
  keyof SmsConfigDto
> = {
  [SmsProvider.BULK_SMS]: 'bulkSmsConfig',
  [SmsProvider.CEQUENS]: 'cequensConfig',
  [SmsProvider.MITTO]: 'mittoConfig',
  [SmsProvider.TWILIO]: 'twilioConfig',
};

export const chatProviderToConfigKeyMapping: Record<
  Exclude<ChatProvider, ChatProvider.NO_PROVIDER>,
  keyof ChatConfigDto
> = {
  [ChatProvider.EB_CHAT]: 'ebChatConfig',
};
