import { CreateWishboxOrder } from '../dtos/create-wishbox-order.dto';
import { UpdateWishboxDeliveryStatusDto } from '../dtos/update-wishbox-delivery-status.dto';

export interface WishboxServiceInterface {
  updateWishboxDeliveryStatus(
    updateWishboxOrderStatusDto: UpdateWishboxDeliveryStatusDto,
  ): Promise<void>;
  create(createWishboxOrder: CreateWishboxOrder): Promise<unknown>;
}
export const WishboxServiceInterface = Symbol('WishboxServiceInterface');
