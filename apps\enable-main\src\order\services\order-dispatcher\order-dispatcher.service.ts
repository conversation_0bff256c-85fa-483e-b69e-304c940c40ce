import {
  BrandDocument,
  CompanyDocument,
  CurrentUser,
  DispatchedInfo,
  LoggerService,
  OrderChildren,
  OrderCreationSource,
  OrderDeliveryAction,
  OrderDeliveryType,
  OrderDocument,
  OrderItem,
  OrderItemGroupedByBrandId,
  OrderLogActionEnum,
  OrderPaymentMethod,
  OrderPaymentStatus,
  OrderPosToCreate,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OnEvent } from '@nestjs/event-emitter';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import * as randomestring from 'randomstring';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { CompanyService } from '../../../company/services/company/company.service';
import {
  TookanMultipleTasksToCreate,
  TookanTask,
} from '../../../delivery/dto/tookan.dto';
import { TookanService } from '../../../delivery/services/tookan/tookan.service';
import { OrderLogServiceInterface } from '../interfaces/order-log.service.interface';
import { OrderPosService } from '../order-pos/order-pos.service';

@Injectable()
export class OrderDispatcherService {
  private readonly loggerService = new LoggerService(
    OrderDispatcherService.name,
  );

  private readonly TOOKAN_TEMPLATE_NAME = 'Pickup_&_Delivery_Enable';
  private readonly TOOKAN_SPECIAL_INSTRUCTION_LABEL = 'Special_Instruction';
  private readonly TOOKAN_PAYMENT_AMOUNT_LABEL = 'Payment_Amount';
  private readonly TOOKAN_PAYMENT_METHOD_LABEL = 'Payment_Method';

  constructor(
    @Inject('OrderLogServiceInterface')
    private readonly orderLogService: OrderLogServiceInterface,
    private orderPosService: OrderPosService,
    @Inject('BrandServiceInterface')
    private brandService: BrandServiceInterface,
    private companyService: CompanyService,
    private configService: ConfigService,
    private tookanService: TookanService,
  ) {}

  @OnEvent('order.created')
  public async handleOrderDispatching(
    order: OrderDocument,
    company: CompanyDocument,
  ) {
    try {
      const canBeDispatched = this.canBeDispatched(order, company);
      this.loggerService.log(
        `Order With Code: ${order.code} Dispatching Status: ${canBeDispatched}`,
      );
      if (canBeDispatched) {
        const dispatchedInfo: DispatchedInfo = await this.dispatchOrder(order);

        await this.orderLogService.saveOrderLog(
          order,
          { requestedObject: {} },
          { responseObject: dispatchedInfo },
          OrderLogActionEnum.ORDER_DISPATCHED_TO_MULTIPLE_BRANDS,
          {} as CurrentUser,
        );
      }
    } catch (err) {
      await this.orderLogService.saveOrderLog(
        order,
        { requestedObject: { order: order._id, code: order.code } },
        { responseObject: { error: JSON.stringify(err) } },
        OrderLogActionEnum.ORDER_DISPATCHED_TO_MULTIPLE_BRANDS,
        {} as CurrentUser,
      );
    }
  }

  private canBeDispatched(
    order: OrderDocument,
    company: CompanyDocument,
  ): boolean {
    return !(order.parentId || !company?.isDispatchingCompany);
  }

  private async dispatchOrder(order: OrderDocument): Promise<DispatchedInfo> {
    let brands: BrandDocument[] = [],
      companies: CompanyDocument[] = [];

    const childrenOrders: OrderDocument[] = [],
      dispatchedInfo: DispatchedInfo = {
        companyIds: [],
        brandIds: [],
        parentId: order._id,
        childrenIds: [],
      },
      orderItemsGroupsByBrandId = this.groupOrderItemsBasedOnBrandId(
        order.items,
      );

    [dispatchedInfo.brandIds, brands] = await this.getBrands(
      Object.keys(orderItemsGroupsByBrandId),
    );

    [dispatchedInfo.companyIds, companies] =
      await this.getCompaniesFromBrands(brands);

    // Order Dispatching Based On Brand
    for (const [index, brand] of brands.entries()) {
      const children: OrderDocument = await this.dispatchOrderBasedOnBrand(
        order,
        brand,
        companies[index],
        brands.length,
        orderItemsGroupsByBrandId[brand._id.toHexString()],
      );
      childrenOrders.push(children);
      dispatchedInfo.childrenIds.push(children._id);
    }

    await this.saveOrderChildren(order, childrenOrders);

    const parentCompany: CompanyDocument =
      await this.companyService.get_details(
        order.company['_id'] ? order.company['_id'] : order.company,
      );

    await this.createTookanTasks(order, parentCompany, childrenOrders, brands);

    return dispatchedInfo;
  }

  private async saveOrderChildren(
    order: OrderDocument,
    childrenOrders: OrderDocument[],
  ) {
    order.children = this.mapToChildrenArray(childrenOrders);
    await order.save();
  }

  private async getBrands(
    brandIdsStrings: string[],
  ): Promise<[Types.ObjectId[], BrandDocument[]]> {
    const brandIds: Types.ObjectId[] = [],
      brands: BrandDocument[] = [];

    for (const brandId of brandIdsStrings) {
      const brand = await this.brandService.findById(
        new Types.ObjectId(brandId),
      );
      if (brand) {
        brandIds.push(brand._id);
        brands.push(brand);
      }
    }
    return [brandIds, brands];
  }

  private groupOrderItemsBasedOnBrandId(
    orderItems: OrderItem[],
  ): OrderItemGroupedByBrandId {
    return orderItems.reduce(
      (result: OrderItemGroupedByBrandId, orderItem: OrderItem) => {
        if (orderItem.brandId)
          (result[orderItem.brandId] = result[orderItem.brandId] || []).push(
            orderItem,
          );
        return result;
      },
      {},
    );
  }

  private async getCompaniesFromBrands(
    brands: BrandDocument[],
  ): Promise<[Types.ObjectId[], CompanyDocument[]]> {
    const companyIds: Types.ObjectId[] = [],
      companies: CompanyDocument[] = [];

    for (const brand of brands) {
      const company = await this.companyService.get_details(
        brand.companyId.toHexString(),
      );
      companyIds.push(brand.companyId);
      companies.push(company);
    }

    return [companyIds, companies];
  }

  private async dispatchOrderBasedOnBrand(
    parentOrder: OrderDocument,
    brand: BrandDocument,
    company: CompanyDocument,
    numberOfBrands: number,
    items: OrderItem[],
  ): Promise<OrderDocument> {
    const orderPosToCreate: OrderPosToCreate =
      this.constructChildrenOrderToCreateDTO(
        parentOrder,
        items,
        brand,
        company,
        numberOfBrands,
      );

    return await this.orderPosService.create(orderPosToCreate);
  }

  private constructChildrenOrderToCreateDTO(
    parentOrder: OrderDocument,
    items: OrderItem[],
    brand: BrandDocument,
    company: CompanyDocument,
    numberOfBrands: number,
  ): OrderPosToCreate {
    const invoicedAmount = this.calculateChildrenSubInvoicedAmount(items);
    const deliveryAmount = 0;
    return {
      items,
      first_name: parentOrder.customer_name,
      last_name: '',
      country_code: parentOrder.country_code,
      phone: parentOrder.customer_phone,
      is_gift: parentOrder.is_gift,
      recipient_name: parentOrder.recipient_name,
      recipient_phone: parentOrder.recipient_phone,
      company: company._id.toHexString(),
      brandId: brand._id.toHexString(),
      autoAssign: false,
      branch: undefined,
      code: '',
      deliveryLocation: parentOrder.deliveryLocation as any,
      delivery_date: moment
        .utc(parentOrder.delivery_date)
        .add(5, 'minutes')
        .format('YYYY-MM-DD HH:mm'),
      pickup_date: moment
        .utc(parentOrder.pickup_date)
        .add(5, 'minutes')
        .format('YYYY-MM-DD HH:mm'),
      pickup_time: '',
      brand: undefined,
      callback_url: '',
      payment_method: OrderPaymentMethod.prepaid,
      cardMessage: parentOrder.cardMessage,
      paymentCode: '',
      delivery_action: OrderDeliveryAction.IN_STORE_PICKUP,
      dispatch_type: OrderDeliveryType.urgent, // Need TO Be Validated

      current_user: parentOrder.createdBy,
      deliverectPosOrderId: '',
      customer: undefined,
      delivery_slot_from: parentOrder.delivery_slot_from,
      delivery_slot_id: undefined,
      deliveryParty: undefined,
      delivery_slot_to: parentOrder.delivery_slot_to,
      delivery_time: undefined,
      delivery_type: OrderDeliveryType.urgent, // Need TO Be Validated
      deliveryLocationId: undefined,
      discount: 0,
      driver: undefined,
      driver_id: undefined,
      deliveryMethod: undefined,
      invoice_number: parentOrder.invoice_number,
      invoiced_amount: invoicedAmount,
      total_amount: invoicedAmount + deliveryAmount,
      total_amount_after_discount: invoicedAmount,
      delivery_amount: deliveryAmount, // Need TO Be Validated
      order_remarks: parentOrder.order_remarks,
      email: '',
      is_secret: parentOrder.is_secret,
      is_test: parentOrder.is_test,
      isCustomerUpdatable: false,
      orderType: parentOrder.orderType,
      language: parentOrder.language,
      orderIsAlreadyPaid: true,
      payment_status: OrderPaymentStatus.COMPLETED,
      pickupLocationId: undefined,
      source: parentOrder.source,
      creationSource: OrderCreationSource.DISPATCHER,
      status: undefined,
      recipient_country_code: parentOrder.recipient_country_code,
      traceId: '',
      transport_type: parentOrder.transport_type,
      parentId: parentOrder._id,
    };
  }

  private calculateChildrenSubInvoicedAmount(items: OrderItem[]) {
    return items.reduce(
      (totalAmount: number, orderItem: OrderItem) =>
        totalAmount +
        (orderItem.totalAmountAfterDiscount ?? orderItem.totalAmount),
      0,
    );
  }

  private mapToChildrenArray(orders: OrderDocument[]) {
    return orders.map<OrderChildren>((order: OrderDocument) => ({
      orderId: new Types.ObjectId(order._id),
      status: order.status,
    }));
  }

  private async createTookanTasks(
    parentOrder: OrderDocument,
    parentCompany: CompanyDocument,
    subOrders: OrderDocument[],
    brands: BrandDocument[],
  ) {
    const tookanMultipleTasksToCreate: TookanMultipleTasksToCreate =
      this.initTookanMultipleTaskDTO(parentCompany);
    try {
      tookanMultipleTasksToCreate.deliveries.push(
        this.constructDeliveryTask(parentOrder),
      );

      // Filling The Pickup Tasks BASED on BRANDS and SUB_ORDERs
      for (const [index, brand] of brands.entries()) {
        tookanMultipleTasksToCreate.pickups.push(
          this.constructPickupTask(brand, parentOrder, subOrders[index]),
        );
      }

      const createdTookanTask = await this.tookanService.createMultipleTask(
        tookanMultipleTasksToCreate,
      );

      await this.updateOrderTookanDetails(parentOrder, createdTookanTask);

      await this.orderLogService.saveOrderLog(
        parentOrder,
        { requestedObject: tookanMultipleTasksToCreate },
        { responseObject: createdTookanTask },
        OrderLogActionEnum.MULTIPLE_TOOKAN_TASK_TO_CREATE,
        parentOrder.createdBy,
      );

      return parentOrder;
    } catch (e) {
      await this.orderLogService.saveOrderLog(
        parentOrder,
        { requestedObject: tookanMultipleTasksToCreate },
        { responseObject: JSON.stringify(e) },
        OrderLogActionEnum.MULTIPLE_TOOKAN_TASK_TO_CREATE,
        parentOrder.createdBy,
      );
    }
  }

  private initTookanMultipleTaskDTO(
    company: CompanyDocument,
  ): TookanMultipleTasksToCreate {
    return {
      api_key: this.configService.get('TOOKAN_API_KEY'),
      tags: `${company._id.toHexString()}`,
      timezone: -180,
      auto_assignment: 1,
      team_id: company.tookan_team_id,
      fleet_id: 0,
      geofence: 0,
      layout_type: 0,
      has_delivery: 1,
      has_pickup: 1,
      pickups: [],
      deliveries: [],
    };
  }

  private constructDeliveryTask(order: OrderDocument): TookanTask {
    return {
      address: `${order.customer_name} DFC mall`,
      email: 'default' + randomestring.generate(5) + '@e-butler.com',
      job_description: order.order_remarks,
      name: order.customer_name,
      order_id: order.code,
      time: moment(order.pickup_date).add(5, 'minutes').format('HH:mm'),
      latitude: 25.3854748,
      longitude: 51.4242886,
      ref_images: [],
      barcode: order.barCode.toString(),
      template_data: [
        {
          label: this.TOOKAN_PAYMENT_AMOUNT_LABEL,
          data:
            order.payment_method == OrderPaymentMethod.online
              ? 'Paid Already'
              : order.total_amount.toString(),
        },
        {
          label: this.TOOKAN_PAYMENT_METHOD_LABEL,
          data:
            (order.payment_method == OrderPaymentMethod.online &&
              order.payment_status != OrderPaymentStatus.COMPLETED) ||
            order.payment_method == OrderPaymentMethod.cash
              ? 'cash'
              : 'online',
        },
        {
          label: this.TOOKAN_SPECIAL_INSTRUCTION_LABEL,
          data: order.order_remarks,
        },
      ],
      template_name: this.TOOKAN_TEMPLATE_NAME,
    };
  }

  private constructPickupTask(
    brand: BrandDocument,
    parentOrder: OrderDocument,
    subOrder: OrderDocument,
  ): TookanTask {
    return {
      address: `Brand: ${brand.name} - Customer: ${parentOrder.customer_name}`,
      email: 'default' + randomestring.generate(5) + '@e-butler.com',
      job_description: subOrder?.order_remarks,
      name: subOrder?.order_remarks,
      order_id: subOrder?.code,
      time: moment(subOrder?.pickup_date).add(5, 'minutes').format('HH:mm'),
      latitude: 25.3854748,
      longitude: 51.4242886,
      barcode: subOrder?.barCode.toString(),
      ref_images: [],
      template_data: [
        {
          label: this.TOOKAN_PAYMENT_AMOUNT_LABEL,
          data:
            subOrder?.payment_method == OrderPaymentMethod.online
              ? 'Paid Already'
              : subOrder?.total_amount.toString(),
        },
        {
          label: this.TOOKAN_PAYMENT_METHOD_LABEL,
          data:
            (subOrder?.payment_method == OrderPaymentMethod.online &&
              subOrder?.payment_status != OrderPaymentStatus.COMPLETED) ||
            subOrder?.payment_method == OrderPaymentMethod.cash
              ? 'cash'
              : 'online',
        },
        {
          label: this.TOOKAN_SPECIAL_INSTRUCTION_LABEL,
          data: `Brand Name: ${subOrder?.code} - Code: ${subOrder?.code} - Remarks: ${subOrder?.order_remarks}`,
        },
      ],
      template_name: this.TOOKAN_TEMPLATE_NAME,
    };
  }

  private getPickupTaskIds(tookanTask: any) {
    const pickupsIds: number[] = [];
    for (let i = 0; i < tookanTask.pickups.length; i++) {
      pickupsIds.push(tookanTask['pickups'][i]['job_id']);
    }
    return pickupsIds;
  }

  private async updateOrderTookanDetails(
    order: OrderDocument,
    createdTookanTask: any,
  ) {
    order.assigned_driver_name = 'DFC Mall Runner';
    order.tookan_job_id = createdTookanTask['deliveries'][0]['job_id'];
    order.tookanDeliveryJobId = createdTookanTask['deliveries'][0]['job_id'];
    order.tookan_delivery_track_url =
      createdTookanTask['deliveries'][0]['result_tracking_link'];
    order.tookanPickupJobIds = this.getPickupTaskIds(createdTookanTask);

    await order.save();
  }
}
