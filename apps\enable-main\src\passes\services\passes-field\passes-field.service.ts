import {
  BenefitMaximumUsageType,
  BrandDocument,
  CalendarSystem,
  CompanyDocument,
  CouponDocument,
  Customer,
  CustomerDocument,
  CustomerOrdableInfo,
  GoogleWalletLocalizedString,
  LabelledField,
  LanguageCode,
  LinksModuleData,
  LocalizedString,
  LoyaltyTierDocument,
  LoyaltyTierProgramProgress,
  mapAsync,
  newLineCharacter,
  NoTier,
  PassField,
  PassFieldContext,
  PassFieldName,
  PunchCardBenefit,
  UnreachableError,
  FieldConfig,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { ar } from 'assets/strings/ar';
import { en } from 'assets/strings/en';
import * as moment from 'moment-timezone';
import { CompanyService } from '../../../company/services/company/company.service';
import { CouponServiceInterface } from '../../../coupon/services/coupon.service.interface';
import { CustomerPunchCardServiceInterface } from '../../../customer/modules/customer-punch-card/customer-punch-card.service.interface';
import { CustomerTierInfoServiceInterface } from '../../../customer/modules/customer-tier-info/customer-tier-info.service.interface';
import { parsePhoneNumber } from '../../../shared/services/helper/helper.service';
import { PassesFieldServiceInterface } from './passes-field.service.interface';

@Injectable()
export class PassesFieldService implements PassesFieldServiceInterface {
  constructor(
    @Inject(CustomerTierInfoServiceInterface)
    private readonly customerTierInfoService: CustomerTierInfoServiceInterface,
    @Inject(CustomerPunchCardServiceInterface)
    private readonly customerPunchCardService: CustomerPunchCardServiceInterface,
    @Inject(CouponServiceInterface)
    private readonly couponService: CouponServiceInterface,
    private readonly companyService: CompanyService,
  ) {}

  private getLocalizedString<STRING_KEY extends keyof typeof en>(
    key: STRING_KEY,
    ...args: Parameters<(typeof en)[STRING_KEY]>
  ): LocalizedString {
    return {
      en: en[key].call(null, ...args),
      ar: ar[key].call(null, ...args),
    };
  }

  public async getFields(
    labelledFields: LabelledField[],
    fieldConfig: FieldConfig,
    passFieldContext: PassFieldContext,
  ): Promise<PassField[]> {
    const fields = await mapAsync(
      labelledFields,
      async ({ fieldName, label }) => {
        const field = await this.getField(
          fieldName,
          fieldConfig,
          passFieldContext,
        );

        return Array.isArray(field)
          ? field.map((field) => field.addLabel(label))
          : field?.addLabel(label);
      },
    );

    return fields.flat().filter(Boolean);
  }

  private getField(
    fieldName: PassFieldName,
    fieldConfig: FieldConfig,
    {
      customer,
      company,
      nextTier,
      loyaltyTierProgramProgress,
      availableCoupons,
    }: PassFieldContext,
  ): Promise<PassField | PassField[]> | PassField | PassField[] {
    switch (fieldName) {
      case PassFieldName.CUSTOMER_FIRST_NAME:
        return this.getCustomerNameField(customer);
      case PassFieldName.TIER:
        return this.getTierField(customer, company);
      case PassFieldName.TIER_VALID_TILL:
        return this.getValidTillField(customer, company);
      case PassFieldName.NEXT_TIER:
        return this.getNextTierField(
          company,
          nextTier,
          loyaltyTierProgramProgress,
        );
      case PassFieldName.POINTS_PROGRESS:
        return this.getPointsRateField(
          fieldConfig,
          company,
          loyaltyTierProgramProgress,
        );
      case PassFieldName.POINTS_BALANCE:
        return this.getPointsField(customer, company);
      case PassFieldName.NEXT_COUPON:
        return this.getNextCouponField(customer, company);
      case PassFieldName.CUSTOMER_AVAILABLE_COUPONS:
        return this.getCouponsField(availableCoupons);
      case PassFieldName.CUSTOMER_AVAILABLE_REWARDS:
        return this.getYourRewardsField(customer);
      case PassFieldName.PUNCH_CARD_PROGRESS:
        return this.getNextRewardFields(customer);
      case PassFieldName.HIGHEST_UNLOCKED_COUPON:
        return this.getHighestUnlockedCoupon(availableCoupons);
      default:
        throw new UnreachableError(fieldName);
    }
  }

  public getHighestUnlockedCoupon(
    availableCoupons: CouponDocument[],
  ): PassField | null {
    if (!availableCoupons?.length) return null;

    let highestCoupon = availableCoupons[0];
    for (let i = 1; i < availableCoupons.length; i++) {
      if (availableCoupons[i].loyaltyPointCost > highestCoupon.loyaltyPointCost)
        highestCoupon = availableCoupons[i];
    }

    return new PassField(
      'highestUnlockedCoupon',
      this.getLocalizedString('l_highest_unlocked_coupon'),
      { en: highestCoupon.titleEn, ar: highestCoupon.titleAr },
    );
  }

  public getCustomerNameField(customer: Customer): PassField {
    return new PassField(
      'customerName',
      this.getLocalizedString('l_customer_name'),
      { en: customer.first_name, ar: customer.first_name },
    );
  }

  public getPointsField(
    customer: Customer,
    company: CompanyDocument,
  ): PassField {
    return new PassField(
      'LoyaltyPoints',
      {
        en: company.loyaltyProgramConfig.pointsBalanceTitleEn,
        ar: company.loyaltyProgramConfig.pointsBalanceTitleAr,
      },
      this.getLocalizedString(
        'v_points',
        Math.floor(customer.loyaltyPoints || 0),
      ),
    );
  }

  public getTierField(
    customer: CustomerDocument,
    company: CompanyDocument,
  ): PassField {
    const tierNameValue = this.getTierNameValue(customer, company);
    if (!tierNameValue) return;
    return new PassField(
      'LoyaltyTier',
      this.getLocalizedString('l_tier_name'),
      tierNameValue,
    );
  }

  public async getNextTierField(
    company: CompanyDocument,
    nextTier: LoyaltyTierDocument,
    { amountSpent, orderRate, pointsRate }: LoyaltyTierProgramProgress,
  ): Promise<PassField> {
    if (!nextTier) return null;

    const {
      nameEn,
      orderRateThreshold,
      amountSpentThreshold,
      pointsRateThreshold,
    } = nextTier;
    const nameAr = nextTier.nameAr || nameEn;

    const getOrderRateString = () => ({
      en: en.v_next_tier(nameEn, orderRate, orderRateThreshold),
      ar: ar.v_next_tier(nameAr, orderRate, orderRateThreshold),
    });
    const getAmountSpentString = () => ({
      en: en.v_next_tier_amount_spent(
        nameEn,
        amountSpent,
        amountSpentThreshold,
        company.localization.currency,
      ),
      ar: ar.v_next_tier_amount_spent(
        nameAr,
        amountSpent,
        amountSpentThreshold,
        company.localization.currency,
      ),
    });
    const getPointsRateString = () => ({
      en: en.v_next_tier_points(
        nameEn,
        Math.floor(pointsRate),
        pointsRateThreshold,
        company.loyaltyProgramConfig.calendarPointsTitleEn,
      ),
      ar: ar.v_next_tier_points(
        nameAr,
        Math.floor(pointsRate),
        pointsRateThreshold,
        company.loyaltyProgramConfig.calendarPointsTitleAr,
      ),
    });

    return new PassField(
      'NextTier',
      this.getLocalizedString('l_next_tier'),
      await this.companyService.evaluateTierLevellingUpMethod(company, {
        orderRate: getOrderRateString,
        both: getOrderRateString,
        amountSpent: getAmountSpentString,
        pointsRate: getPointsRateString,
      }),
    );
  }

  private getTierNameValue(
    customer: CustomerDocument,
    company: CompanyDocument,
  ): LocalizedString {
    if (!customer.loyaltyTier)
      return {
        en: NoTier.getName(company, LanguageCode.en),
        ar: NoTier.getName(company, LanguageCode.ar),
      };

    const { nameEn, nameAr, percentDiscount } = customer.loyaltyTier;

    const benefitsTextEn = percentDiscount
      ? ` - ${percentDiscount}%`
      : ' - Free Delivery';
    const benefitsTextAr = percentDiscount
      ? ` - ${percentDiscount}%`
      : ' - توصيل مجاني';

    return {
      en: nameEn + benefitsTextEn,
      ar: nameAr || nameEn + benefitsTextAr,
    };
  }

  public async getValidTillField(
    customer: CustomerDocument,
    company: CompanyDocument,
  ): Promise<PassField | null> {
    if (!customer.loyaltyTier || customer.loyaltyTier.isVipTier) return null;

    const validTill = await this.customerTierInfoService.getTierValidTill(
      customer,
      company,
    );
    if (!validTill) return null;

    const isTierMaintained =
      await this.customerTierInfoService.hasCustomerMaintainedTier(customer);

    const showWarning =
      !isTierMaintained && Math.abs(moment.utc().diff(validTill, 'days')) <= 10;

    const showYear =
      company.loyaltyProgramConfig?.calendarSystem ==
      CalendarSystem.LAST_12_MONTHS;

    return new PassField(
      'ValidTill',
      this.getLocalizedString('l_valid_till'),
      this.getLocalizedString('v_valid_till', validTill, showWarning, showYear),
    );
  }

  public async getNextCouponField(
    customer: CustomerDocument,
    company: CompanyDocument,
  ): Promise<PassField | null> {
    const nextCoupon =
      await this.couponService.findCustomerNextCoupon(customer);
    if (!nextCoupon) return null;

    const remainingPoints = Number(
      Math.max(0, nextCoupon.loyaltyPointCost - customer.loyaltyPoints).toFixed(
        2,
      ),
    );

    const enDescription = en.v_remaining_points_until_next_coupon(
      remainingPoints,
      company.loyaltyProgramConfig.pointsBalanceTitleEn,
      nextCoupon.titleEn,
    );
    const arDescription = ar.v_remaining_points_until_next_coupon(
      remainingPoints,
      company.loyaltyProgramConfig.pointsBalanceTitleAr,
      nextCoupon.titleAr,
    );

    const localizedValue: LocalizedString = {
      en: enDescription,
      ar: arDescription,
    };

    return new PassField(
      'NextCoupon',
      this.getLocalizedString('l_next_coupon'),
      localizedValue,
    );
  }

  public getCouponsField(availableCoupons: CouponDocument[]): PassField {
    const value = availableCoupons.length.toString();

    return new PassField('Coupons', this.getLocalizedString('l_coupons'), {
      en: value,
      ar: value,
    });
  }

  public getPointsRateField(
    fieldConfig: FieldConfig,
    company: CompanyDocument,
    loyaltyTierProgramProgress: LoyaltyTierProgramProgress,
  ): PassField {
    const pointsRate = Math.floor(loyaltyTierProgramProgress.pointsRate || 0);

    const calendarSystemAr = {
      [CalendarSystem.MONTHLY]: 'شهر',
      [CalendarSystem.QUARTERLY]: '3 اشهر',
      [CalendarSystem.LAST_12_MONTHS]: 'سنة',
    }[company.loyaltyProgramConfig.calendarSystem];

    return new PassField(
      'PointsRate',
      {
        en:
          fieldConfig?.pointsProgressFieldLabel?.en ??
          `${company.loyaltyProgramConfig.calendarPointsTitleEn} Progress`,

        ar:
          fieldConfig?.pointsProgressFieldLabel?.ar ??
          `رصيدك خلال ${calendarSystemAr}`,
      },
      { en: pointsRate.toString(), ar: pointsRate.toString() },
    );
  }

  public getPassDescriptionField(
    fieldConfig: FieldConfig,
    brand: BrandDocument,
  ): PassField {
    const descriptionEn =
      fieldConfig?.description?.en || en.v_description(brand.name);

    const descriptionAr =
      fieldConfig?.description?.ar ||
      fieldConfig?.description?.en ||
      ar.v_description(brand.name);

    return new PassField(
      'Description',
      this.getLocalizedString('l_description'),
      { en: descriptionEn, ar: descriptionAr },
    );
  }

  public async getTierDescriptionField(
    customer: CustomerDocument,
    brand: BrandDocument,
    nextTier: LoyaltyTierDocument,
    loyaltyTierProgramProgress: LoyaltyTierProgramProgress,
  ): Promise<PassField> {
    return new PassField(
      'TierDescription',
      this.getLocalizedString('l_tier'),
      await this.getTierDescriptionValue(
        customer,
        brand,
        nextTier,
        loyaltyTierProgramProgress,
      ),
    );
  }

  private async getTierDescriptionValue(
    customer: CustomerDocument,
    brand: BrandDocument,
    nextTier: LoyaltyTierDocument,
    loyaltyTierProgramProgress: LoyaltyTierProgramProgress,
  ): Promise<LocalizedString> {
    const enLines: string[] = [];
    const arLines: string[] = [];

    if (customer.loyaltyTier) {
      enLines.push(
        `${customer.loyaltyTier.nameEn} - ${customer.loyaltyTier.percentDiscount}%`,
      );
      arLines.push(
        `${customer.loyaltyTier.nameAr || customer.loyaltyTier.nameEn} - ${
          customer.loyaltyTier.percentDiscount
        }%`,
      );
    }

    this.pushActions(enLines, arLines, customer, brand);

    const remainingOrdersLines = await this.getRemainingOrdersLines(
      customer,
      nextTier,
      loyaltyTierProgramProgress,
    );
    if (remainingOrdersLines) {
      enLines.push(remainingOrdersLines.en);
      arLines.push(remainingOrdersLines.ar);
    }

    return this.joinLines(enLines, arLines);
  }

  private async getRemainingOrdersLines(
    customer: CustomerDocument,
    nextTier: LoyaltyTierDocument,
    { orderRate }: LoyaltyTierProgramProgress,
  ): Promise<LocalizedString> {
    const remainingOrdersCurrentTier =
      await this.customerTierInfoService.getRemainingOrdersCurrentTier(
        customer,
        orderRate,
      );

    if (remainingOrdersCurrentTier) {
      return {
        en: en.v_orders_to_maintain(
          customer.loyaltyTier?.nameEn,
          remainingOrdersCurrentTier,
        ),
        ar: ar.v_orders_to_maintain(
          customer.loyaltyTier?.nameAr || customer.loyaltyTier?.nameEn,
          remainingOrdersCurrentTier,
        ),
      };
    }

    if (!nextTier) return null;

    const remainingOrdersUpperTier =
      await this.customerTierInfoService.getRemainingOrdersUpperTier(
        customer,
        nextTier,
        orderRate,
      );

    if (remainingOrdersUpperTier)
      return {
        en: en.v_orders_to_upgrade(
          nextTier.nameEn,
          remainingOrdersUpperTier,
          nextTier.percentDiscount,
        ),
        ar: ar.v_orders_to_upgrade(
          nextTier.nameAr || nextTier.nameEn,
          remainingOrdersUpperTier,
          nextTier.percentDiscount,
        ),
      };

    return null;
  }

  public getPointsDescriptionField(
    customer: CustomerDocument,
    brand: BrandDocument,
    company: CompanyDocument,
    availableCoupons: CouponDocument[],
  ): PassField {
    return new PassField(
      'PointsDescription',
      {
        en: company.loyaltyProgramConfig.pointsBalanceTitleEn,
        ar: company.loyaltyProgramConfig.pointsBalanceTitleAr,
      },
      this.getPointsDescriptionValue(
        customer,
        brand,
        company,
        availableCoupons,
      ),
    );
  }

  private getPointsDescriptionValue(
    customer: CustomerDocument,
    brand: BrandDocument,
    company: CompanyDocument,
    availableCoupons: CouponDocument[],
  ): LocalizedString {
    const enLines: string[] = [];
    const arLines: string[] = [];

    enLines.push(
      en.v_points_details_description(
        availableCoupons.length,
        customer.loyaltyPoints,
        company.loyaltyProgramConfig.pointsBalanceTitleEn,
      ),
    );
    arLines.push(
      ar.v_points_details_description(
        availableCoupons.length,
        customer.loyaltyPoints,
        company.loyaltyProgramConfig.pointsBalanceTitleAr,
      ),
    );

    for (const coupon of availableCoupons) {
      const { loyaltyPointCost, benefits } = coupon;

      for (const benefit of benefits) {
        const discountValue = benefit.value;

        enLines.push(
          en.v_points_details_coupon(
            loyaltyPointCost,
            discountValue,
            benefit.type,
            company.localization.currency,
            company.loyaltyProgramConfig.pointsBalanceTitleEn,
          ),
        );

        arLines.push(
          ar.v_points_details_coupon(
            loyaltyPointCost,
            discountValue,
            benefit.type,
            company.localization.currency,
            company.loyaltyProgramConfig.pointsBalanceTitleAr,
          ),
        );
      }
    }

    enLines.push(en.v_order_to_get_coupons());
    arLines.push(ar.v_order_to_get_coupons());

    this.pushActions(enLines, arLines, customer, brand);
    return this.joinLines(enLines, arLines);
  }

  private pushActions(
    enLines: string[],
    arLines: string[],
    customer: CustomerDocument,
    brand: BrandDocument,
  ) {
    const ordableLinks = this.getOrdableLinks(customer);
    if (ordableLinks && ordableLinks.length) {
      enLines.push(...ordableLinks, '');
      arLines.push(...this.getOrdableLinks(customer, true), '');
    }

    if (brand.phoneNumber) {
      const phoneLink = `tel:${brand.phoneNumber}`;
      enLines.push(this.hyperlink(en.v_claim_discount_phone(), phoneLink), '');
      arLines.push(this.hyperlink(ar.v_claim_discount_phone(), phoneLink), '');
    }

    if (brand.whatsappNumber) {
      const phone = parsePhoneNumber(
        brand.whatsappNumber,
        brand.whatsappCountryCode,
      );
      const whatsappLink = `whatsapp://send?phone=${phone}`;
      enLines.push(
        this.hyperlink(en.v_claim_discount_whatsapp(), whatsappLink),
      );
      arLines.push(
        this.hyperlink(ar.v_claim_discount_whatsapp(), whatsappLink),
      );
    }
  }

  private getOrdableLinks(
    customer: CustomerDocument,
    useArabic = false,
  ): string[] {
    if (!customer || !customer.ordableStores) return;
    return Object.values(customer.ordableStores).map(
      (ordableInfo: CustomerOrdableInfo) =>
        this.formatOrdableInfo(ordableInfo, useArabic),
    );
  }

  private formatOrdableInfo(
    { ordableBrands, ordableLink }: CustomerOrdableInfo,
    useArabic = false,
  ): string {
    const brandNames = ordableBrands.map((brand) => brand.name).join(', ');
    const link = this.hyperlink(
      (useArabic ? ar : en).v_claim_discount(),
      ordableLink,
    );
    return `${brandNames}\t\t\t${link}\n`;
  }

  // TODO: google wallet support?
  private hyperlink(text: string, url: string): string {
    return `<a href=${url}>${text}</a>`;
  }

  public getSocialLinksField(
    brand: BrandDocument,
    company: CompanyDocument,
  ): PassField {
    return new PassField(
      'SocialLinks',
      this.getLocalizedString('l_socials', brand.name),
      this.getSocialLinksValue(company),
    );
  }

  private getSocialLinksValue(company: CompanyDocument): LocalizedString {
    const enLinks: string[] = [];
    const arLinks: string[] = [];

    if (company.facebook) {
      enLinks.push(en.v_socials_facebook(company.facebook));
      arLinks.push(ar.v_socials_facebook(company.facebook));
    }
    if (company.instgram) {
      enLinks.push(en.v_socials_instagram(company.instgram));
      arLinks.push(ar.v_socials_instagram(company.instgram));
    }
    if (company.twitter) {
      enLinks.push(en.v_socials_twitter(company.twitter));
      arLinks.push(ar.v_socials_twitter(company.twitter));
    }

    return this.joinLines(enLinks, arLinks);
  }

  public getPoweredByField(): PassField {
    const value = this.hyperlink('Enable.tech', 'https://enable.tech');

    return new PassField(
      'EnableLink',
      this.getLocalizedString('l_powered_by'),
      { en: value, ar: value },
    );
  }

  public getYourRewardsField(customer: CustomerDocument): PassField {
    const value = (customer.rewards || []).length.toString();

    return new PassField(
      'YourRewards',
      this.getLocalizedString('l_your_rewards'),
      { en: value, ar: value },
    );
  }

  public getCustomerBenefitField(customer: CustomerDocument): PassField {
    if (!customer.earnedBenefits || customer.earnedBenefits.length === 0)
      return null;

    const shownBenefits = (customer.earnedBenefits || []).filter(
      (benefit) =>
        benefit.config.maximumUsageType === BenefitMaximumUsageType.UNLIMITED ||
        benefit.config.maximumUsage - benefit.numberOfUsages > 0,
    );

    if (shownBenefits.length === 0) return null;

    const benefits = {
      en: shownBenefits.map(en.v_customer_benefits).join(newLineCharacter),
      ar: shownBenefits.map(ar.v_customer_benefits).join(newLineCharacter),
    };

    return new PassField(
      'CustomerBenefits',
      this.getLocalizedString('l_customer_benefits'),
      benefits,
    );
  }

  public async getNextRewardFields(
    customer: CustomerDocument,
  ): Promise<PassField[]> {
    const nextAchievements =
      await this.customerPunchCardService.getNextAchievements(customer);
    if (!nextAchievements || nextAchievements.length === 0) return [];

    return nextAchievements
      .filter((nextAchievement) => nextAchievement)
      .map((nextAchievement, i) => {
        const target = nextAchievement?.requirement?.targetValue;
        const current = target - nextAchievement.remainingAmount;
        const value = `${current}/${target}`;

        return new PassField(
          `next_reward_${i}`,
          {
            en: nextAchievement.punchCardNameEn,
            ar: nextAchievement.punchCardNameAr,
          },
          { en: value, ar: value },
        );
      });
  }

  public async getRewardsDescriptionField(
    customer: CustomerDocument,
    brand: BrandDocument,
  ): Promise<PassField> {
    return new PassField(
      'RewardsDescription',
      this.getLocalizedString('l_reward_description'),
      await this.getRewardDescriptionValue(customer, brand),
    );
  }

  private async getRewardDescriptionValue(
    customer: CustomerDocument,
    brand: BrandDocument,
  ): Promise<LocalizedString> {
    const enLines: string[] = [];
    const arLines: string[] = [];

    if (!customer.rewards?.length) {
      const nextAchievement =
        await this.customerPunchCardService.getNextAchievement(customer);
      if (nextAchievement) {
        enLines.push(
          en.v_reward_description_no_rewards(nextAchievement.remainingAmount),
        );
        arLines.push(
          ar.v_reward_description_no_rewards(nextAchievement.remainingAmount),
        );
      }
    } else {
      enLines.push(en.v_reward_description_list_header());
      arLines.push(ar.v_reward_description_list_header());

      enLines.push(
        ...customer.rewards.map(({ benefit, amount, menuItem }) =>
          benefit === PunchCardBenefit.MENU_ITEM
            ? `1 ${menuItem.nameEn}`
            : en.v_reward_description_list_item(amount),
        ),
      );
      arLines.push(
        ...customer.rewards.map(({ benefit, amount, menuItem }) =>
          benefit === PunchCardBenefit.MENU_ITEM
            ? `‎1 ${menuItem.nameAr}`
            : ar.v_reward_description_list_item(amount),
        ),
      );
    }

    this.pushActions(enLines, arLines, customer, brand);
    return this.joinLines(enLines, arLines);
  }

  private joinLines(enLines: string[], arLines: string[]): LocalizedString {
    return {
      en: enLines.join(newLineCharacter),
      ar: arLines.join(newLineCharacter),
    };
  }

  public getLinksModuleData(brand: BrandDocument): LinksModuleData {
    return {
      uris: [
        ...(brand.phoneNumber
          ? [
              {
                id: 'CLAIM_DISCOUNT_PHONE',
                localizedDescription: new GoogleWalletLocalizedString(
                  this.getLocalizedString('v_claim_discount_phone'),
                ),
                uri: `tel:${brand.phoneNumber}`,
              },
            ]
          : []),
        ...(brand.whatsappNumber
          ? [
              {
                id: 'CLAIM_DISCOUNT_CHAT',
                localizedDescription: new GoogleWalletLocalizedString(
                  this.getLocalizedString('v_claim_discount_whatsapp'),
                ),
                uri: `whatsapp://send?phone=${parsePhoneNumber(
                  brand.whatsappNumber,
                  brand.whatsappCountryCode,
                )}`,
              },
            ]
          : []),
        {
          id: 'POWERED_BY',
          description: 'Enable.tech',
          uri: 'https://enable.tech',
        },
      ],
    };
  }

  public getMessagesField(customer: CustomerDocument): PassField | null {
    if (
      !customer.activeWalletPassMessages ||
      customer.activeWalletPassMessages.length === 0
    )
      return null;

    const messagesLabel = 'Messages';
    const messagesValue = customer.activeWalletPassMessages.join('\n\n');
    return new PassField(
      'messages',
      { en: messagesLabel, ar: messagesLabel },
      { en: messagesValue, ar: messagesValue },
      '%@',
    );
  }
}
