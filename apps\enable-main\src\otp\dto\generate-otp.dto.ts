import {
  CountryDialCode,
  IsPhoneNumberForRegion,
  LanguageCode,
  TemplateType,
  ObjectIdTransform,
} from '@app/shared-stuff';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { Types } from 'mongoose';

export class GenerateOTPDto {
  @ApiProperty({
    type: String,
    enum: LanguageCode,
    default: LanguageCode.en,
  })
  @IsEnum(LanguageCode)
  @IsOptional()
  language = LanguageCode.en;

  @ApiProperty({
    type: String,
    required: true,
    default: CountryDialCode.QATAR,
  })
  @IsNotEmpty()
  @IsEnum(CountryDialCode)
  countryCode: CountryDialCode;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsPhoneNumberForRegion('countryCode')
  phone: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @ObjectIdTransform()
  brandId: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: false,
    default: '1h',
  })
  @IsString()
  @IsOptional()
  expiresIn = '1h';

  @ApiProperty({
    type: String,
    required: false,
    enum: [TemplateType.SMS, TemplateType.CHAT],
    default: TemplateType.SMS,
  })
  @IsEnum(TemplateType)
  @IsOptional()
  templateType?: TemplateType.SMS | TemplateType.CHAT = TemplateType.SMS;
}
