// Reset loyalty points and carry over for non-members of a given company
// To reset loyalty tiers call the bulk manual tier assignment API
// Will output bulk manual tier assignment DTO.

const COMPANY_ID = ObjectId('');

const customers = db.customers
  .find(
    {
      company: COMPANY_ID,
      loyaltyStatus: { $ne: 'member' },
      $or: [{ loyaltyPoints: { $gt: 0 } }, { loyaltyTier: { $ne: null } }],
    },
    { loyaltyPoints: 1, loyaltyTier: 1 },
  )
  .toArray();

const loyaltyPointLogs = customers
  .filter((c) => c.loyaltyPoints > 0)
  .map((c) => ({
    action: 'ON_POINTS_RESET',
    oldBalance: c.loyaltyPoints,
    loyaltyPointsEarned: -c.loyaltyPoints,
    newBalance: 0,
    customerId: c._id,
    updatedAt: new Date(),
    createdAt: new Date(),
  }));

db.loyaltypointlogs.insertMany(loyaltyPointLogs, { ordered: false });
db.customers.updateMany(
  { _id: { $in: customers.map((c) => c._id) } },
  {
    $set: {
      loyaltyPoints: 0,
      carryOverOrderRate: 0,
      carryOverAmountSpent: 0,
      carryOverUpdatedAt: new Date(),
      updatedAt: new Date(),
    },
  },
);

const customerIdsToReset = customers
  .filter((c) => c.loyaltyTier)
  .map((c) => c._id.toString());
console.log(
  'Please POST /customer/manualTierAssignment with the following DTO:\n',
  JSON.stringify({
    customerIds: customerIdsToReset,
    tierId: null,
  }),
);
