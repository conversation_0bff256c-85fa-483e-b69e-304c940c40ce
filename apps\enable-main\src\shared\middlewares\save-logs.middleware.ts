import { Request } from 'express';
import { Injectable, NestMiddleware } from '@nestjs/common';

@Injectable()
export class SaveLogsMiddleware implements NestMiddleware {
  // constructor(public logService: LogsService) {}

  async use(req: Request, res: any, next: () => void) {
    try {
      // await this.logService.createLog({
      //   route: req.route,
      //   url: req.url,
      //   ip: req.ip,
      //   method: req.method,
      //   headers: req.headers,
      //   userAgent : req.headers['user-agent']

      // });
      next();
    } catch (err) {
      next();
    }
  }
}
