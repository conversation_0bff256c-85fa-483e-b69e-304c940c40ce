import { HttpStatus } from '@nestjs/common';
import { GenerateOTPDto } from '../dto/generate-otp.dto';
import { VerifyOTPResponseDTO } from '../dto/verify-otp-response.dto';
import { VerifyOTPDto } from '../dto/verify-otp.dto';

export interface OTPServiceInterface {
  generateOTP(generateOTPDto: GenerateOTPDto): Promise<HttpStatus>;
  verifyOTP(verifyOTPDto: VerifyOTPDto): Promise<VerifyOTPResponseDTO>;
}

export const OTPServiceInterface = Symbol('OTPServiceInterface');
