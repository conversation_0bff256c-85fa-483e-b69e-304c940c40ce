export const PaymentSearchMapping = (search_type, search_key) => {
  const mapping = {
    phone: { input: '$customer_phone' },
    name: { input: '$customer_name' },
    order_code: { input: '$order_code' },
    link_id: { input: '$code' },
    status: { input: '$status' },
    amount: { input: '$amount' },
    source: { input: '$source' },
    branch_name: { input: '$branch.name' },
    company_name: { input: '$company_name' },
  };
  if (!mapping[search_type]) {
    mapping[search_type] = { input: '$customer_name' };
  }
  mapping[search_type]['regex'] = new RegExp(`.*${search_key.toLowerCase()}.*`);
  mapping[search_type]['options'] = 'i';
  return mapping[search_type];
};
