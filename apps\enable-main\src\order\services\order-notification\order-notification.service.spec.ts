import {
  Company,
  companyMock,
  CustomerDocument,
  customerMock,
  OrderDocument,
  orderMock,
  TriggerAction,
} from '@app/shared-stuff';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Test, TestingModule } from '@nestjs/testing';
import { CompanyService } from '../../../company/services/company/company.service';
import { CustomerPointsService } from '../../../customer/modules/customer-points/customer-points.service';
import { CustomerReadServiceInterface } from '../../../customer/modules/customer-read/customer-read.service.interface';
import { CustomerReplacementsServiceInterface } from '../../../customer/modules/customer-replacements/customer-replacements.service.interface';
import { NotificationService } from '../../../notification/services/notification/notification.service';
import { TriggerService } from '../../../notification/services/trigger/trigger.service';
import { UserService } from '../../../user/services/user/user.service';
import { OrderPaymentService } from '../order-payment/order-payment.service';
import { OrderNotificationService } from './order-notification.service';

describe('OrderNotificationService', () => {
  let service: OrderNotificationService;
  let configService: Partial<ConfigService>;
  let customerReadService: Partial<CustomerReadServiceInterface>;
  let companyService: Partial<CompanyService>;
  let customerPointsService: Partial<CustomerPointsService>;
  let customerReplacementsService: Partial<CustomerReplacementsServiceInterface>;
  let order: OrderDocument;
  let customer: Partial<CustomerDocument>;
  let company: Partial<Company>;

  beforeEach(async () => {
    company = companyMock as Company;
    customer = customerMock(company._id) as CustomerDocument;
    order = orderMock(company._id, customer._id) as OrderDocument;

    customerReadService = {
      findOne: jest.fn().mockResolvedValue(customer),
    };

    companyService = {
      get_details: jest.fn().mockResolvedValue(company),
    };

    customerPointsService = {
      calculatePointsEarnedOnOrder: jest.fn().mockReturnValue(10),
    };

    customerReplacementsService = {
      getCustomerReplacements: jest.fn().mockResolvedValue({
        customerLoyaltyTier: 'Gold',
        customerPoints: '100',
      }),
    };

    configService = {
      get: jest.fn((key) => {
        const config = {
          PAY_PAGE: 'https://pay.enable.com',
          LOCATION_PAGE: 'https://location.enable.com',
          ORDER_TRACKING_BASE: 'https://track.enable.com',
        };
        return config[key];
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrderNotificationService,
        {
          provide: NotificationService,
          useValue: { fireOrderPusherTrigger: jest.fn() },
        },
        {
          provide: ConfigService,
          useValue: configService,
        },
        {
          provide: TriggerService,
          useValue: { fireTrigger: jest.fn() },
        },
        {
          provide: CompanyService,
          useValue: companyService,
        },
        {
          provide: UserService,
          useValue: { removeAcknowledgeOrdersFromUsers: jest.fn() },
        },
        {
          provide: CustomerReadServiceInterface,
          useValue: customerReadService,
        },
        {
          provide: CustomerReplacementsServiceInterface,
          useValue: customerReplacementsService,
        },
        {
          provide: EventEmitter2,
          useValue: { emit: jest.fn() },
        },
        {
          provide: OrderPaymentService,
          useValue: {},
        },
        {
          provide: CustomerPointsService,
          useValue: customerPointsService,
        },
      ],
    }).compile();

    service = module.get<OrderNotificationService>(OrderNotificationService);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should include khuludBranchId in replacements when populateGenericTriggerModel is called', async () => {
    const result = await service.populateGenericTriggerModel(
      order,
      TriggerAction.ON_ORDER_COMPLETED,
    );
    expect(result.replacements).toBeDefined();
    expect(Object.keys(result.replacements).length).not.toBe(0);
    expect(result.replacements.khuludBranchId).toBe('123');
  });

  it('should not call companyService.get_details when company is provided to getAllOrderReplacements', async () => {
    const spy = jest.spyOn(companyService, 'get_details');

    await service['getAllOrderReplacements'](
      order,
      TriggerAction.ON_ORDER_COMPLETED,
      customer as CustomerDocument,
      company as Company,
    );

    expect(spy).not.toHaveBeenCalled();
  });

  it('should not call customerReadService.findOne when customer is provided to getAllOrderReplacements', async () => {
    const spy = jest.spyOn(customerReadService, 'findOne');

    await service['getAllOrderReplacements'](
      order,
      TriggerAction.ON_ORDER_COMPLETED,
      customer as CustomerDocument,
      company as Company,
    );

    expect(spy).not.toHaveBeenCalled();
  });

  it('should call getAllOrderReplacements with company and customer when populateGenericTriggerModel is called', async () => {
    const spy = jest.spyOn(service as any, 'getAllOrderReplacements');

    await service.populateGenericTriggerModel(
      order,
      TriggerAction.ON_ORDER_COMPLETED,
    );

    expect(spy).toHaveBeenCalledWith(
      order,
      TriggerAction.ON_ORDER_COMPLETED,
      customer,
      company,
    );
  });
});
