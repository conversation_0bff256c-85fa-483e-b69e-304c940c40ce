import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Types, Model } from 'mongoose';
import {
  CalendarCycle,
  CustomerDocument,
  GenericRepository,
  LoyaltyTierLog,
  LoyaltyTierLogAction,
  LoyaltyTierLogDocument,
  LoyaltyTierLogTrigger,
} from '@app/shared-stuff';
import { LoyaltyTierLogRepositoryInterface } from './loyalty-tier-log.repository.interface';

@Injectable()
export class LoyaltyTierLogRepository
  extends GenericRepository<LoyaltyTierLogDocument, LoyaltyTierLog>
  implements LoyaltyTierLogRepositoryInterface
{
  constructor(
    @InjectModel(LoyaltyTierLog.name)
    private loyaltyTierLogModel: Model<LoyaltyTierLogDocument, LoyaltyTierLog>,
  ) {
    super(loyaltyTierLogModel);
  }

  async findHighestAssignedTierId(
    customer: CustomerDocument,
    calendarCycle: CalendarCycle,
  ): Promise<Types.ObjectId> {
    const logs = await this.loyaltyTierLogModel
      .find(
        {
          customerId: customer._id,
          companyId: customer.company,
          createdAt: {
            $gte: calendarCycle.startDate,
            $lte: calendarCycle.endDate,
          },
          $or: [
            {
              action: LoyaltyTierLogAction.ASSIGN,
              trigger: LoyaltyTierLogTrigger.BASE_TIER_ASSIGNMENT,
            },
            {
              action: LoyaltyTierLogAction.UPGRADE,
              trigger: LoyaltyTierLogTrigger.MANUAL_TIER_UPDATE,
            },
            { trigger: LoyaltyTierLogTrigger.MONTHLY_TIER_COMPUTATION },
          ],
        },
        { currentTierId: 1, createdAt: 1 },
        { sort: { createdAt: -1 } },
      )
      .limit(1);

    return logs[0]?.currentTierId;
  }
}
