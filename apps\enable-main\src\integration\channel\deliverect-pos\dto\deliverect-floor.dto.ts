import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class DeliverectFloor {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  description: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  capacity: number;
}
