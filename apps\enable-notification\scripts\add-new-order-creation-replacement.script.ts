// Generic script to add a new order module replacement to all existing order creation/processing triggers and templates

const newOrderReplacement = 'khuludBranchId';

db.triggers.updateMany(
  {
    module: 'ORDER',
    action: {
      $in: [
        'ON_AGGREGATOR_ORDER',
        'ON_AGGREGATOR_ORDER_NON_LOYALTY_MEMBERS',
        'ON_DELIVERY_DELAY',
        'ON_DELIVERY_LOCATION_AND_PAYMENT_REQUEST_SMS',
        'ON_DELIVERY_LOCATION_SUBMITTED',
        'ON_DINEIN_ORDER',
        'ON_FIRST_ACKNOWLEDGE_ORDER_DELAYED',
        'ON_GIFT_ORDER_COMPLETED',
        'ON_LOYALTY_ORDER_LOYALTY_MEMBERS',
        'ON_LOYALTY_ORDER_NON_LOYALTY_MEMBERS',
        'ON_ONLINE_PAYMENT',
        'ON_ORDER_ACKNOWLEDGED',
        'ON_ORDER_ASSIGNED_TO_BRANCH',
        'ON_ORDER_CANCELED',
        'ON_ORDER_COMPLETED',
        'ON_ORDER_COMPLETED_DELIVERECT',
        'ON_ORDER_CREATED',
        'ON_ORDER_CREATED_NON_LOYALTY_MEMBERS',
        'ON_ORDER_DELIVERY_DATE_UPDATED',
        'ON_ORDER_IN_ROUTE',
        'ON_ORDER_READY',
        'ON_ORDER_READY_FOR_PICKUP',
        'ON_ORDER_REFUNDED',
        'ON_ORDER_REJECTED',
        'ON_ORDER_STATUS_UPDATED',
        'ON_PICKUP_DELAY',
        'ON_READY_DELAY',
        'ON_SECOND_ACKNOWLEDGE_ORDER_DELAYED',
        'ON_SEND_DELIVERY_LOCATION_SMS',
        'ON_SEND_DELIVERY_LOCATION_SMS_GIFT',
        'ON_WALK_IN_ORDER',
        'ON_WEBSTORE_ORDER_CREATED',
        'ON_WEBSTORE_ORDER_CREATED_NON_LOYALTY_MEMBERS',
        'ON_WHATSAPP_ORDER_NON_LOYALTY_MEMBERS',
        'THREE_HOURS_AFTER_AGGREGATOR_ORDER',
        'THREE_HOURS_AFTER_AGGREGATOR_ORDER_NON_LOYALTY_MEMBERS',
        'THREE_HOURS_AFTER_ORDER_COMPLETION',
      ],
    },
  },
  { $addToSet: { replacement: newOrderReplacement } },
);

db.templates.updateMany(
  {
    'trigger.module': 'ORDER',
    'trigger.action': {
      $in: [
        'ON_AGGREGATOR_ORDER',
        'ON_AGGREGATOR_ORDER_NON_LOYALTY_MEMBERS',
        'ON_DELIVERY_DELAY',
        'ON_DELIVERY_LOCATION_AND_PAYMENT_REQUEST_SMS',
        'ON_DELIVERY_LOCATION_SUBMITTED',
        'ON_DINEIN_ORDER',
        'ON_FIRST_ACKNOWLEDGE_ORDER_DELAYED',
        'ON_GIFT_ORDER_COMPLETED',
        'ON_LOYALTY_ORDER_LOYALTY_MEMBERS',
        'ON_LOYALTY_ORDER_NON_LOYALTY_MEMBERS',
        'ON_ONLINE_PAYMENT',
        'ON_ORDER_ACKNOWLEDGED',
        'ON_ORDER_ASSIGNED_TO_BRANCH',
        'ON_ORDER_CANCELED',
        'ON_ORDER_COMPLETED',
        'ON_ORDER_COMPLETED_DELIVERECT',
        'ON_ORDER_CREATED',
        'ON_ORDER_CREATED_NON_LOYALTY_MEMBERS',
        'ON_ORDER_DELIVERY_DATE_UPDATED',
        'ON_ORDER_IN_ROUTE',
        'ON_ORDER_READY',
        'ON_ORDER_READY_FOR_PICKUP',
        'ON_ORDER_REFUNDED',
        'ON_ORDER_REJECTED',
        'ON_ORDER_STATUS_UPDATED',
        'ON_PICKUP_DELAY',
        'ON_READY_DELAY',
        'ON_SECOND_ACKNOWLEDGE_ORDER_DELAYED',
        'ON_SEND_DELIVERY_LOCATION_SMS',
        'ON_SEND_DELIVERY_LOCATION_SMS_GIFT',
        'ON_WALK_IN_ORDER',
        'ON_WEBSTORE_ORDER_CREATED',
        'ON_WEBSTORE_ORDER_CREATED_NON_LOYALTY_MEMBERS',
        'ON_WHATSAPP_ORDER_NON_LOYALTY_MEMBERS',
        'THREE_HOURS_AFTER_AGGREGATOR_ORDER',
        'THREE_HOURS_AFTER_AGGREGATOR_ORDER_NON_LOYALTY_MEMBERS',
        'THREE_HOURS_AFTER_ORDER_COMPLETION',
      ],
    },
  },
  { $addToSet: { 'trigger.replacement': newOrderReplacement } },
);
