import { Module } from '@nestjs/common';
import { BrandModule } from '../../../brand/brand.module';
import { CompanyModule } from '../../../company/company.module';
import { OrdableModule } from '../../../integration/webstore/ordable/ordable.module';
import { CustomerCodeModule } from '../customer-code/customer-code.module';
import { CustomerIndexModule } from '../customer-index/customer-index.module';
import { CustomerNotificationModule } from '../customer-notification/customer-notification.module';
import { CustomerPassLinkModule } from '../customer-pass-link/customer-pass-link.module';
import { CustomerRepositoryModule } from '../customer-repository/customer-repository.module';
import { CustomerTierModule } from '../customer-tier/customer-tier.module';
import { CustomerTokenModule } from '../customer-token/customer-token.module';
import { CustomerWriteModule } from '../customer-write/customer-write.module';
import { CustomerLoyaltyMemberService } from './customer-loyalty-member.service';
import { CustomerLoyaltyMemberServiceInterface } from './customer-loyalty-member.service.interface';

@Module({
  providers: [
    {
      provide: CustomerLoyaltyMemberServiceInterface,
      useClass: CustomerLoyaltyMemberService,
    },
  ],
  imports: [
    CompanyModule,
    BrandModule,
    OrdableModule,
    CustomerIndexModule,
    CustomerTierModule,
    CustomerNotificationModule,
    CustomerWriteModule,
    CustomerTokenModule,
    CustomerCodeModule,
    CustomerRepositoryModule,
    CustomerPassLinkModule,
  ],
  exports: [CustomerLoyaltyMemberServiceInterface],
})
export class CustomerLoyaltyMemberModule {}
