import { LoggerService, responseCode } from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { lastValueFrom, map } from 'rxjs';
import * as tookan from 'tookan-api';
import {
  AssignAgentToTookanTaskDto,
  CreateBothTookanTaskDto,
  CreateDeliveryTookanTaskDto,
  TookanDriverToCreate,
  TookanDriverToUpdate,
  TookanMultipleTasksToAssign,
  TookanMultipleTasksToCreate,
  TookanTaskToEdit,
  TookanTeamToCreate,
  TookanTeamToEdit,
} from './../../dto/tookan.dto';

@Injectable()
export class TookanService {
  private readonly loggerService = new LoggerService(TookanService.name);

  client: tookan.Client;
  constructor(
    private configService: ConfigService,
    private http: HttpService,
  ) {
    this.client = new tookan.Client({
      api_key: this.configService.get('TOOKAN_API_KEY'),
    });
  }

  async createTookanTask(
    createTookanTaskDto: CreateBothTookanTaskDto | CreateDeliveryTookanTaskDto,
  ) {
    const tookanTask = await this.client.createTask(createTookanTaskDto);
    return tookanTask['data'];
  }

  async updateTookanTask(tookanTaskToEdit: TookanTaskToEdit) {
    const tookanTask = await this.client.editTask(tookanTaskToEdit);
    return tookanTask['data'];
  }

  async getTaskDetails(task_id: string) {
    const options = {
      order_ids: [task_id],
      include_task_history: 0,
      api_key: this.configService.get<string>('TOOKAN_API_KEY'),
    };
    const tookanTask = await this.client.getTaskDetailsFromOrderId(options);
    return tookanTask['data'][0];
  }

  async createDriver(tookanDrivderToCreate: TookanDriverToCreate) {
    const client = new tookan.Client({
      api_key: this.configService.get('TOOKAN_API_KEY'),
    });
    const createdDriver = await client.addAgent(tookanDrivderToCreate);
    if (createdDriver['status'] && createdDriver['status'] == 201) {
      throw {
        code: responseCode.DUPLICATED_ENTRY,
        statusCode: 401,
        message: createdDriver['message'],
      };
    }
    return createdDriver['data'];
  }

  async updateDriver(driverData: TookanDriverToUpdate) {
    const updtedDriver = await this.client.editAgent(driverData);
    return updtedDriver;
  }

  async createTookanTeam(tookanTeamToCreate: TookanTeamToCreate) {
    const client = new tookan.Client({
      api_key: this.configService.get('TOOKAN_API_KEY'),
    });
    const createdTeam = await client.createTeam(tookanTeamToCreate);
    if (createdTeam['status'] && createdTeam['status'] == 201) {
      throw {
        code: responseCode.DUPLICATED_ENTRY,
        statusCode: 401,
        message: createdTeam['message'],
      };
    }
    return createdTeam['data'];
  }

  async updateTookanTeam(tookenTeamToUpdate: TookanTeamToEdit) {
    const updatedTeam = await this.client.updateTeam(tookenTeamToUpdate);
    if (updatedTeam['status'] && updatedTeam['status'] == 201) {
      throw {
        code: responseCode.DUPLICATED_ENTRY,
        statusCode: 401,
        message: updatedTeam['message'],
      };
    }
    return updatedTeam['data'];
  }

  async createMultipleTask(
    tookanMultipleTaskToCreate: TookanMultipleTasksToCreate,
  ) {
    const createdTasks = await this.client.createMultipleTasks(
      tookanMultipleTaskToCreate,
    );
    if (createdTasks['status'] && createdTasks['status'] != 200) {
      throw {
        code: responseCode.DUPLICATED_ENTRY,
        statusCode: 401,
        message: createdTasks['message'],
      };
    }
    return createdTasks['data'];
  }

  async editTookanMultipleTask(data: any) {
    const tookanResponse = await this.client.editMultipleTask(data);
    return tookanResponse['data'];
  }

  async assignAgentToTask(
    assignAgentToTookanTaskDto: AssignAgentToTookanTaskDto,
  ): Promise<void> {
    const assignedDriver = await this.client.assignAgentToTask(
      assignAgentToTookanTaskDto,
    );

    if (assignedDriver['status'] && assignedDriver['status'] !== 200)
      throw new InternalServerErrorException(
        `Couldn't assign driver with ID : ${assignAgentToTookanTaskDto.fleet_id} to Tookan task : ${assignAgentToTookanTaskDto.job_id} `,
        responseCode.TOOKAN_SERVER_ERROR.toString(),
      );
  }

  async assignDriverToMultipleTask(
    tookanTasksToAssign: TookanMultipleTasksToAssign,
  ): Promise<any> {
    try {
      this.loggerService.log('Assigning driver to multiple tasks', {
        tookanTasksToAssign,
      });

      const response = await lastValueFrom(
        this.http
          .post(
            this.configService.get('TOOKAN_BASE_URL') + 'reassign_open_tasks',
            tookanTasksToAssign,
            {
              headers: {
                'Content-Type': 'application/json',
              },
              timeout: 45000,
            },
          )
          .pipe(map((res) => res.data)),
      );

      if (response['status'] && response['status'] === 201) {
        throw new BadRequestException({
          code: responseCode.DUPLICATED_ENTRY,
          statusCode: 401,
          message: response['message'],
        });
      }

      this.loggerService.log('Successfully assigned driver to tasks', {
        response: response['data'],
      });

      return response['data'];
    } catch (error) {
      this.loggerService.error('Failed to assign driver to tasks', {
        error,
        tookanTasksToAssign,
      });
      throw error;
    }
  }

  async cancelTookanTask(jobIds: number | number[]): Promise<any> {
    const jobIdString = Array.isArray(jobIds)
      ? jobIds.join(',')
      : jobIds.toString();

    const requestBody = {
      api_key: this.configService.get('TOOKAN_API_KEY'),
      job_id: jobIdString,
      job_status: 9,
    };

    this.loggerService.log('request of cancelTookanTask', requestBody);

    await lastValueFrom(
      this.http.post(
        this.configService.get('TOOKAN_BASE_URL') + 'cancel_task',
        requestBody,
        {
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 45000,
        },
      ),
    )
      .then((response) => {
        this.loggerService.log('canceledTask', response);
        return response.data;
      })
      .catch((error) => {
        this.loggerService.log(
          'error in cancelTookanTask',
          JSON.stringify(error),
        );
        return JSON.stringify(error);
      });
  }
}
