import {
  DeleteLoyaltyTierDto,
  DeletionContext,
  LoyaltyTierDocument,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface LoyaltyTierDeletionServiceInterface {
  getDeletionContext(loyaltyTierId: Types.ObjectId): Promise<DeletionContext>;
  delete(
    loyaltytierId: Types.ObjectId,
    deleteLoyaltyTierDto: DeleteLoyaltyTierDto,
  ): Promise<LoyaltyTierDocument>;
}

export const LoyaltyTierDeletionServiceInterface = Symbol(
  'LoyaltyTierDeletionServiceInterface',
);
