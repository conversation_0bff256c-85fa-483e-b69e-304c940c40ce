// [EBL-6012] Wallet Pass Builder v1.0
// Migrate `loyaltyTier.borderDisplay` to `passConfig.stripImageConfig.stampsConfig.tierStamps`

const nameById = Object.fromEntries(
  db.companies
    .find(
      {
        _id: {
          $in: db.loyaltytiers.distinct('companyId', {
            borderDisplay: { $exists: true },
          }),
        },
      },
      { name: 1 },
    )
    .toArray()
    .map((c) => [c._id.toString(), c.name]),
);

db.pass_configs.bulkWrite(
db.loyaltytiers
  .find(
    { borderDisplay: { $exists: true } },
    { companyId: 1, borderDisplay: 1 },
  )
  .toArray()
  .map(({ _id, companyId, borderDisplay }) => {
    return {
      updateOne: {
        filter: { 'owner._id': companyId },
        update: {
          $push: {
            'stripImageConfig.stampsConfig.tierStamps': {
              tierId: _id,
              emptyStamp: borderDisplay.emptyStamp ?? borderDisplay.image,
              filledStamp: borderDisplay.filledStamp ?? borderDisplay.image,
            },
          },
          $setOnInsert: {
            owner: {
              _id: companyId,
              name: nameById[companyId.toString()],
              type: 'company',
            },
          },
        },
        upsert: true,
      },
    };
  });
);

// // Uncomment and run after verifying the results of the above script
// db.loyaltytiers.updateMany({ borderDisplay: { $exists: true } }, [
//   { $unset: 'borderDisplay' },
// ]);
