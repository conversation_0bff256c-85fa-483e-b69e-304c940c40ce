import {
  BulkUpdateLoyaltyTiersDto,
  CreateLoyaltyTierDto,
  LoyaltyTierDocument,
  TierWithRequirements,
  UpdateLoyaltyTierDto,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface LoyaltyTierWriteServiceInterface {
  create(
    createLoyaltyTierDto: CreateLoyaltyTierDto,
  ): Promise<LoyaltyTierDocument>;
  update(
    updateLoyaltyTierDto: UpdateLoyaltyTierDto,
  ): Promise<LoyaltyTierDocument | null>;
  bulkUpdate(
    bulkUpdateLoyaltyTiersDto: BulkUpdateLoyaltyTiersDto,
  ): Promise<LoyaltyTierDocument[]>;
  generateEnrollmentCode(loyaltyTierId: Types.ObjectId): Promise<string>;
  updateTierLevellingUpMethod(
    companyId: Types.ObjectId,
    tiers: TierWithRequirements[],
  ): Promise<void>;
}

export const LoyaltyTierWriteServiceInterface = Symbol(
  'LoyaltyTierWriteServiceInterface',
);
