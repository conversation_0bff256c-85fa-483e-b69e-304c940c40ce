// EBL-3530 - Resend OTP as Whatsapp Message
// add ON_OTP_RESEND_AS_CHAT trigger and template
db.triggers.insertOne({
  name: '[AUTH] ON_OTP_RESEND_AS_CHAT',
  client: 'ENABLE_MAIN',
  action: 'ON_OTP_RESEND_AS_CHAT',
  module: 'AUTH',
  replacement: ['otp'],
  createdAt: new Date(),
  updatedAt: new Date(),
});

db.templates.insertOne({
  name: 'Global Send OTP Template',
  content: {
    enContent:
      '$otp is your verification code, use it to complete your registration and join our Loyalty Program',
    arContent:
      '$otp هو رمز التحقق الخاص بك، برجاء استخدامه لاستكمال تسجيلك والانضمام لبرنامج الولاء🎁',
  },
  to: 'CUSTOMER',
  from: 'COMPANY_SENDER',
  type: 'CHAT',
  trigger: db.triggers.findOne({ name: '[AUTH] ON_OTP_RESEND_AS_CHAT' }),
});
