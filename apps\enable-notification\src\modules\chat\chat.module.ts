import { ChatRepository } from './repositories/chat.repository';
import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { MongooseModule } from '@nestjs/mongoose';
import { Chat, ChatSchema } from './models/chat.model';
import { ChatService } from './services/chat/chat.service';

import { ConfigModule } from '@nestjs/config';
import { EbChatService } from './services/integrations/eb-chat/eb-chat.service';
import { ConfigurationModule } from '../configuration/configuration.module';
import { SharedStuffModule } from '@app/shared-stuff';
import { ChatServiceInterface } from './services/chat/chat.service.interface';
import { ChatRepositoryInterface } from './repositories/chat.repository.interface';

@Module({
  imports: [
    SharedStuffModule,
    HttpModule,
    ConfigModule,
    ConfigurationModule,
    MongooseModule.forFeature([{ schema: ChatSchema, name: Chat.name }]),
  ],
  providers: [
    EbChatService,
    {
      provide: ChatServiceInterface,
      useClass: ChatService,
    },
    {
      provide: ChatRepositoryInterface,
      useClass: ChatRepository,
    },
  ],
  exports: [ChatServiceInterface],
})
export class ChatModule {}
