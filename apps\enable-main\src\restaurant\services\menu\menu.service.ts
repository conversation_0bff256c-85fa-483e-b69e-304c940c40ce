import {
  AssignMasterToSlaveDto,
  Cache<PERSON>eys,
  CacheServiceInterface,
  CollectionName,
  CurrentUser,
  DocumentRedirectDocument,
  EmbeddedBrandDto,
  fullCacheKeys,
  LogError,
  LoggerService,
  Menu,
  MenuCategory,
  MenuCategoryDocument,
  MenuCategoryToCreate,
  MenuDocument,
  MenuGroupDocument,
  MenuGroupToCreate,
  MenuItemDocument,
  MenuItemIndexOutput,
  MenuItemToCreate,
  MenuPosToPush,
  MenuSearchMapping,
  MenuServiceUser,
  MenuSortMapping,
  MenuToCreate,
  MenuToDuplicate,
  MenuToIndex,
  MenuToRemove,
  MenuToUpdate,
  MenuWithId,
  responseCode,
  SyncOrdableMenuDto,
  YesOrNo,
} from '@app/shared-stuff';
import { Brand } from '@app/shared-stuff/models/brand.model';
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { FilterQuery, isObjectIdOrHexString, Model, Types } from 'mongoose';
import { extname } from 'path';
import * as randomstring from 'randomstring';
import { BranchService } from '../../../branch/services/branch/branch.service';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { CompanyService } from '../../../company/services/company/company.service';
import { CreateOrdableOptionsDto } from '../../../integration/webstore/ordable/dtos/options/create-ordable-options.dto';
import { CreateOrdableProductOptionsDto } from '../../../integration/webstore/ordable/dtos/product-options/create-ordable-product-options.dto';
import { CreateOrdableProductsDto } from '../../../integration/webstore/ordable/dtos/products/create-ordable-products.dto';
import { UpdateOrdableProductsDto } from '../../../integration/webstore/ordable/dtos/products/update-ordable-products.dto';
import { OrdableCategoriesServiceInterface } from '../../../integration/webstore/ordable/services/categories/ordable-categories.service.interface';
import { OrdableOptionsServiceInterface } from '../../../integration/webstore/ordable/services/options/ordable-options.service.interface';
import { OrdableProductOptionsServiceInterface } from '../../../integration/webstore/ordable/services/product-options/ordable-product-options.service.interface';
import { OrdableProductsServiceInterface } from '../../../integration/webstore/ordable/services/products/ordable-products.service.interface';
import { GoogleCloudStorageService } from '../../../storage/google-cloud-storage.service';
import { StoreWithId } from '../../../store/dto/store-with-id.dto';
import { StoreDocument } from '../../../store/models/store.model';
import { StoreServiceInterface } from '../../../store/services/store.service.interface';
import {
  OrdableCategoryMap,
  OrdableCategoryMapping,
} from '../../types/ordable-category-mapping';
import { MenuGroupService } from '../menu-group/menu-group.service';
import { MenuCategoryService } from './../menu-category/menu-category.service';
import { MenuItemService } from './../menu-item/menu-item.service';

@Injectable()
export class MenuService {
  private readonly loggerService = new LoggerService(MenuService.name);

  constructor(
    @InjectModel('Menu') private menuModel: Model<MenuDocument, Menu>,
    @InjectModel(CollectionName.DOCUMENT_REDIRECT)
    private documentRedirectModel: Model<DocumentRedirectDocument>,
    private companyService: CompanyService,
    private branchService: BranchService,
    @Inject('BrandServiceInterface')
    private brandService: BrandServiceInterface,
    @Inject(forwardRef(() => MenuCategoryService))
    private menuCategoryService: MenuCategoryService,
    @Inject(forwardRef(() => MenuItemService))
    private menuItemService: MenuItemService,
    @InjectModel('MenuCategory')
    private menuCategoryModel: Model<MenuCategoryDocument, MenuCategory>,
    @Inject('StoreServiceInterface')
    private storeService: StoreServiceInterface,
    @Inject('OrdableProductsServiceInterface')
    private ordableProductsService: OrdableProductsServiceInterface,
    @Inject('OrdableProductOptionsServiceInterface')
    private ordableProductOptionsService: OrdableProductOptionsServiceInterface,
    @Inject('OrdableOptionsServiceInterface')
    private ordableOptionsService: OrdableOptionsServiceInterface,
    @Inject('OrdableCategoriesServiceInterface')
    private ordableCategoriesService: OrdableCategoriesServiceInterface,
    private menuGroupService: MenuGroupService,
    private googleCloudStorageService: GoogleCloudStorageService,
    @Inject(CacheServiceInterface)
    private cacheService: CacheServiceInterface,
  ) {}

  async index(menuToIndex: MenuToIndex) {
    const aggregation = [];

    aggregation.push({ $match: this.buildMenuFilter(menuToIndex) });

    if (menuToIndex.search_key) {
      if (menuToIndex.search_type == 'all') {
        aggregation.push(
          {
            $addFields: {
              matched: {
                $or: [
                  {
                    $regexMatch: {
                      input: '$name',
                      options: 'i',
                      regex: new RegExp(`.*${menuToIndex.search_key}.*`),
                    },
                  },
                  {
                    $regexMatch: {
                      input: '$type',
                      options: 'i',
                      regex: new RegExp(`.*${menuToIndex.search_key}.*`),
                    },
                  },
                  {
                    $regexMatch: {
                      input: '$internalName',
                      options: 'i',
                      regex: new RegExp(`.*${menuToIndex.search_key}.*`),
                    },
                  },
                ],
              },
            },
          },
          { $match: { matched: true } },
        );
      } else {
        aggregation.push(
          {
            $addFields: {
              matched: {
                $regexMatch: MenuSearchMapping(
                  menuToIndex.search_type || 'all',
                  menuToIndex.search_key,
                ),
              },
            },
          },
          { $match: { matched: true } },
        );
      }
    }

    if (menuToIndex.sort_type) {
      aggregation.push({
        $sort: MenuSortMapping[menuToIndex.sort_type],
      });
    }

    if ((menuToIndex.offset || menuToIndex.offset == 0) && menuToIndex.limit) {
      aggregation.push({
        $skip: menuToIndex.offset * menuToIndex.limit,
      });
      aggregation.push({
        $limit: menuToIndex.limit,
      });
    }

    const menus = await this.menuModel.aggregate(aggregation);

    if (menus.length === 0 && menuToIndex.branch) {
      delete menuToIndex.branch;
      menuToIndex.isMaster = YesOrNo.YES;
      aggregation.splice(0, 1, { $match: this.buildMenuFilter(menuToIndex) });
      return await this.menuModel.aggregate(aggregation);
    }

    return menus;
  }

  async getDetails(id: string) {
    const filters = { $or: [{ reference: id }] } as any;
    if (isObjectIdOrHexString(id)) {
      filters.$or.push({ _id: id });
      filters.$or.push({ deliverectId: id });
    }
    const menu = await this.menuModel
      .findOne(filters)
      .populate('company', '_id name acronym');

    return menu;
  }

  async getTotalNumberOfMenus(menuToIndex: MenuToIndex) {
    delete menuToIndex.offset;
    delete menuToIndex.limit;
    return (await this.index(menuToIndex)).length;
  }

  async create(menuToCreate: MenuToCreate) {
    const company = await this.companyService.get_details(menuToCreate.company);
    const brand = await this.brandService.findById(menuToCreate.brand._id);

    if (!brand) {
      throw new BadRequestException({
        code: responseCode.MISSING_DATA,
        statusCode: 422,
        message: 'please provide correct brand',
      });
    }

    if (brand) menuToCreate.brand = this.brandService.toEmbeddedBrandDto(brand);

    const menu = new this.menuModel(menuToCreate);

    const branches = [];
    let branchesName = ' ';
    for (let i = 0; i < menuToCreate.branches.length; i++) {
      const branch = await this.branchService.get_details(
        menuToCreate.branches[i],
      );
      branchesName += branch.name + ', ';
      branches.push(new Types.ObjectId(branch._id));
    }

    menu.branches = branches;
    menu.branchesName = branchesName;

    menu.company = company._id;
    menu.companyName = company?.name;
    menu.createdBy = menuToCreate.currentUser;

    await menu.save();
    return menu;
  }

  async update(menuToUpdate: MenuToUpdate) {
    const company = await this.companyService.get_details(menuToUpdate.company);
    const brand = await this.brandService.findById(menuToUpdate.brand._id);

    if (!brand) {
      throw new BadRequestException({
        code: responseCode.MISSING_DATA,
        statusCode: 422,
        message: 'please provide correct brand',
      });
    }

    if (brand) {
      menuToUpdate.brand = {
        ...brand,
        _id: new Types.ObjectId(brand._id),
      };
    }

    const branches = [];
    let branchesName = ' ';
    for (let i = 0; i < menuToUpdate.branches.length; i++) {
      const branch = await this.branchService.get_details(
        menuToUpdate.branches[i],
      );
      branchesName += branch.name + ', ';
      branches.push(new Types.ObjectId(branch._id));
    }

    menuToUpdate.branches = branches;
    menuToUpdate.branchesName = branchesName;

    menuToUpdate.company = new Types.ObjectId(company._id) as any;
    menuToUpdate.companyName = company?.name;
    menuToUpdate['updatedBy'] = menuToUpdate.currentUser;
    await this.makeAllMenuItemsAvailable(menuToUpdate);
    await this.menuModel.findByIdAndUpdate(
      {
        _id: menuToUpdate._id,
      },
      menuToUpdate,
    );
    return (
      'Menu with Id ' + menuToUpdate._id.toString() + ' Updated Successfully'
    );
  }

  async remove(menuToRemove: MenuToRemove) {
    const menu = await this.menuModel.findOne({ _id: menuToRemove._id });
    await this.menuItemService.deleteItemInsideMenu(menu);
    await this.menuCategoryService.deleteAllCategoryInsideMenu(menu);
    await this.deleteMenu(menu);
  }

  async deleteMenu(menu: MenuDocument) {
    await this.menuModel.deleteOne({ _id: menu._id });
  }

  async pushMenu(menuPosToPush: MenuPosToPush) {
    const company = await this.companyService.get_details(
      menuPosToPush.company,
    );

    let menu = await this.getDetails(menuPosToPush.reference);
    if (!menu) {
      menu = await this.create({
        name: menuPosToPush.name,
        internalName: menuPosToPush.internalName,
        description: menuPosToPush.description,
        deliverectRaw: '',
        deliverectId: '',
        branches: [],
        brand: undefined,
        branchesName: '',
        type: menuPosToPush.type,
        reference: menuPosToPush.reference,
        company: company._id,
        companyName: company.name,
        currentUser: menuPosToPush.currentUser,
        availabilities: [],
        pdfURL: '',
      });
    }

    // Creating the category
    for (let i = 0; i < menuPosToPush.categories.length; i++) {
      const currentCategory = menuPosToPush.categories[i];
      let category = await this.menuCategoryService.getDetails(
        currentCategory.reference,
      );
      if (!category) {
        category = await this.menuCategoryService.create({
          name: currentCategory.name,
          description: currentCategory.description,
          reference: currentCategory.reference,
          menu: menu._id,
          menuName: menu.name,
          createdBy: menuPosToPush.currentUser,
          deliverectRaw: {},
          deliverectId: '',
          image: currentCategory.image,
          plu: '',
          externalImage: '',
          sortOrder: i + 1,
          integrationInfo: {
            ordableInfo: {},
          },
        });
      } else {
        category.name = currentCategory.name;
        category.description = currentCategory.description;
        category.menu = new Types.ObjectId(menu._id);
        category.menuName = menu.name;
        await category.save();
      }
    }

    // Creating and Updating the MenuItems
    for (let i = 0; i < menuPosToPush.items.length; i++) {
      const currentItem = menuPosToPush.items[i];
      let item = await this.menuItemService.getDetails(currentItem.reference);
      if (!item) {
        item = await this.menuItemService.create({
          nameAr: currentItem.nameAr,
          nameEn: currentItem.nameEn,
          descriptionAr: currentItem.descriptionAr,
          descriptionEn: currentItem.descriptionEn,
          reference: currentItem.reference,
          available: currentItem.available,
          calories: currentItem.calories,
          createdBy: menuPosToPush.currentUser,
          code: currentItem.code,
          deliverectId: '',
          deliverectRaw: '',
          deliveryTax: currentItem.deliveryTax,
          takeawayTax: currentItem.takeawayTax,
          max: currentItem.max,
          menu: menu._id,
          images: currentItem.images,
          min: currentItem.min,
          multiSelectMax: currentItem.multiSelectMax,
          plu: currentItem.plu,
          multiply: currentItem.multiply,
          type: currentItem.type,
          tags: currentItem.tags,
          menuGroups: currentItem.menuGroups,
          menuCategory: currentItem.menuCategory,
          price: currentItem.price,
          showInPos: currentItem.showInPos,
          showOnWeb: currentItem.showOnWeb,
          subItems: currentItem.subItems,
          company: company._id,
          externalImage: '',
          forGroupingOnly: false,
          sortOrder: 1,
          adlerId: '',
          integrationInfo: {
            ordableInfo: {},
          },
          availabilities: [],
          isScheduledAvailabilityActive: false,
          externalBrandId: '',
        });
      }
    }

    return menu;
  }

  @OnEvent('cron.every10mins')
  @LogError()
  async checkMenuAvailability() {
    const menus = await this.menuModel.find({ deletedAt: null });
    const dayOfTheWeek = moment().day();
    const currentTime = moment().format('HH:mm');
    for (let i = 0; i < menus.length; i++) {
      const currentMenu = menus[i];
      if (
        currentMenu &&
        currentMenu.availabilities &&
        currentMenu.availabilities.length
      ) {
        for (let j = 0; j < currentMenu.availabilities.length; j++) {
          const currentAvailability = currentMenu.availabilities[j];
          if (
            currentAvailability['dayOfWeek'] == dayOfTheWeek &&
            currentAvailability['startTime'] > currentTime &&
            currentAvailability['endTime'] < currentTime
          ) {
            currentMenu.available = true;
          } else {
            currentMenu.available = false;
          }
        }
        currentMenu.save();
      }
    }
  }

  async duplicateMenu(menuToDuplicate: MenuToDuplicate, branches: any[]) {
    const menu = await this.menuModel.findOne({
      _id: new Types.ObjectId(menuToDuplicate.menuId),
    });

    if (!menu) {
      throw new BadRequestException({
        code: responseCode.MISSING_DATA,
        statusCode: 422,
        message: 'please provide correct menuId',
      });
    }

    const createdMenu = await this.create({
      name: menu.name,
      brand: menu.brand,
      company: menu.company,
      description: menu.name,
      branches: menuToDuplicate.branchId
        ? [menuToDuplicate.branchId]
        : branches,
      availabilities: menu['availabilities'],
      internalName: menu['internalName'],
      type: menu.type,
      deliverectRaw: {},
      deliverectId: '',
      branchesName: '',
      companyName: '',
      reference: menu['_id'].toString(),
      currentUser: MenuServiceUser,
      pdfURL: '',
    });

    let menuItems = await this.menuItemService.index({
      menu: menu['_id'],
      output: MenuItemIndexOutput.FULL,
    } as any);

    menuItems = menuItems[0]['paginatedResult'];

    for (let j = 0; j < menuItems.length; j++) {
      const currentMenuItem = menuItems[j] as MenuItemDocument;
      let currentCategory: MenuCategoryDocument;

      // Find and create the current menu category
      if (currentMenuItem['menuCategory']) {
        const menuCategory = await this.menuCategoryService.getDetails(
          currentMenuItem['menuCategory'].toHexString(),
        );

        currentCategory = await this.createOrFindCategory({
          name: menuCategory.name,
          menu: createdMenu._id,
          createdBy: menuCategory.createdBy,
          deliverectId: menuCategory.deliverectId,
          deliverectRaw: menuCategory.deliverectRaw,
          description: menuCategory.description,
          externalImage: menuCategory.externalImage,
          sortOrder: menuCategory.sortOrder,
          image: undefined,
          menuName: createdMenu.name,
          plu: menuCategory.plu,
          reference: menuCategory.reference,
          integrationInfo: {
            ordableInfo: {},
          },
        });
      }

      const menuItemToCreate: MenuItemToCreate = {
        nameAr: currentMenuItem.nameAr,
        nameEn: currentMenuItem.nameEn,
        descriptionAr: currentMenuItem.descriptionAr,
        descriptionEn: currentMenuItem.descriptionEn,
        adlerId: currentMenuItem.adlerId,
        calories: currentMenuItem.calories,
        available: currentMenuItem.available,
        code: currentMenuItem.code,
        company: currentMenuItem.company as any,
        createdBy: currentMenuItem.createdBy,
        deliverectId: currentMenuItem.deliverectId,
        deliverectRaw: currentMenuItem.deliverectRaw,
        deliveryTax: currentMenuItem.deliveryTax,
        externalImage: currentMenuItem.externalImage,
        forGroupingOnly: currentMenuItem.forGoupingOnly,
        max: currentMenuItem.max,
        menuCategory: currentCategory ? currentCategory._id : undefined,
        menu: createdMenu._id,
        menuGroups: [],
        min: currentMenuItem.min,
        multiSelectMax: currentMenuItem.multiSelectMax,
        multiply: currentMenuItem.multiply,
        plu: currentMenuItem.plu,
        price: currentMenuItem.price,
        reference: currentMenuItem.reference,
        type: currentMenuItem.type,
        tags: currentMenuItem.tags,
        subItems: (currentMenuItem.subItems && currentMenuItem.subItems.length
          ? currentMenuItem.subItems.map((x) => x['_id'])
          : []) as any,
        showInPos: currentMenuItem.showInPos,
        showOnWeb: currentMenuItem.showOnWeb,
        sortOrder: currentMenuItem.sortOrder,
        takeawayTax: currentMenuItem.takeawayTax,
        images: [],
        readyToUseImages: currentMenuItem.images,
        integrationInfo: {
          ordableInfo: {},
        },
        availabilities: [],
        isScheduledAvailabilityActive: false,
        externalBrandId: '',
      };

      if (currentMenuItem.menuGroups && currentMenuItem.menuGroups.length) {
        const menuGroups = await this.menuGroupService.index({
          ids: currentMenuItem.menuGroups,
        });
        for (let i = 0; i < menuGroups.length; i++) {
          const menuGroup = menuGroups[i];
          const menuGroupToCreate: MenuGroupToCreate = {
            nameAr: menuGroup['nameAr'],
            nameEn: menuGroup['nameEn'],
            descriptionAr: menuGroup['descriptionAr'],
            descriptionEn: menuGroup['descriptionEn'],
            index: i.toString(),
            min: menuGroup['min'],
            max: menuGroup['max'],
            deliverectId: menuGroup['deliverectId'],
            totalPrice: menuGroup['totalPrice'],
            plu: menuGroup['plu'],
            reference: menuGroup['reference'],
            multiply: menuGroup['multiply'],
            multiSelectMax: menuGroup['multiSelectMax'],
            createdBy: menuGroup['createdBy'] as CurrentUser,
            items: menuGroup['items'],
            integrationInfo: {
              ordableInfo: {},
            },
          };
          menuItemToCreate.menuGroups.push(menuGroupToCreate);
        }
      }

      await this.menuItemService.create(menuItemToCreate);
    }
  }

  @OnEvent('on-Brand-Update')
  @LogError()
  async updateBrandOnMenu(updatedBrand: Brand, brandId: Types.ObjectId) {
    const menuToIndex = this.constructMenuToIndex({
      brandId: brandId.toHexString(),
      month: '',
      sort_type: '',
      search_type: '',
      branch: '',
      company: '',
      branches: '',
    });
    const menus = await this.index(menuToIndex);
    const menuIds = this.populateMenuIds(menus);
    const brandDto: EmbeddedBrandDto = {
      _id: new Types.ObjectId(brandId),
      name: updatedBrand.name,
      phoneNumber: updatedBrand.phoneNumber,
      senderId: updatedBrand.senderId,
      image: updatedBrand.image,
      emailSenderId: updatedBrand.emailSenderId,
      preferredLanguage: updatedBrand?.preferredLanguage,
    };
    await this.updateMany(menuIds, brandDto);
  }

  checkIntegrationInfoExisting(object: any) {
    if (!object?.integrationInfo) {
      object.integrationInfo = {
        ordableInfo: {},
      };
    } else {
      if (!object.integrationInfo?.ordableInfo) {
        object.integrationInfo.ordableInfo = {};
      }
    }
  }

  async syncOrdableMenu({ storeId, menuId, branchId }: SyncOrdableMenuDto) {
    const store = await this.storeService.findById(storeId);
    const menu = await this.getDetails(menuId.toString());

    if (menu.isMaster) {
      if (!branchId)
        throw new BadRequestException('branchId is required for master menu');

      this.loggerService.log(
        `Syncing menu ${menuId.toHexString()} for store ${storeId.toHexString()} with branchId ${branchId.toHexString()}`,
      );
      this.syncSlaveMenuForBranch(store, menu, branchId);
    } else {
      this.syncOrdableProducts(store, menuId, menu.branches[0]);
    }

    return { menuId };
  }

  async syncOrdableOptions(
    store: StoreDocument,
    menuItem: MenuItemDocument,
    productId: number,
  ) {
    const menuGroups = await this.menuGroupService.findByIdIn(
      menuItem.menuGroups,
    );
    for (let i = 0; i < menuGroups.length; i++) {
      this.checkIntegrationInfoExisting(menuGroups[i]);
      const storeId = store._id.toHexString();
      if (menuGroups[i]?.integrationInfo?.ordableInfo[storeId]) {
        await this.ordableOptionsService.update(
          {
            id: menuGroups[i]?.integrationInfo?.ordableInfo[storeId],
            option_name: menuGroups[i].nameEn
              ? menuGroups[i].nameEn
              : menuGroups[i].nameAr,
            options_ar_name: menuGroups[i].nameAr
              ? menuGroups[i].nameAr
              : menuGroups[i].nameEn,
            minimum: menuGroups[i].min || 1,
            maximum: menuGroups[i].max || 1,
            sort_order: i,
            is_required: menuGroups[i].min > 0,
            multiple: menuGroups[i].max > 1,
          },
          store,
        );
        await this.syncOrdableProductOptions(
          store,
          menuGroups[i],
          productId,
          menuGroups[i]?.integrationInfo?.ordableInfo[storeId],
        );
      } else {
        if (productId === -1) {
          this.loggerService.log(
            `Skipping option ${menuGroups[i]._id.toHexString()} since menu item with id ${menuItem._id.toHexString()} was not synced`,
          );
          continue;
        }
        const createOrdableOptionsDto: CreateOrdableOptionsDto = {
          option_name: menuGroups[i].nameEn
            ? menuGroups[i].nameEn
            : menuGroups[i].nameAr,
          option_ar_name: menuGroups[i].nameAr
            ? menuGroups[i].nameAr
            : menuGroups[i].nameEn,
          product_id: productId,
          minimum: menuGroups[i].min || 1,
          maximum: menuGroups[i].max || 1,
          sort_order: 0,
          multiple: !!menuGroups[i].multiply,
          choices: [
            {
              value: '',
              ar_value: '',
              price: 0,
              sort_order: 1,
              is_active: false,
            },
          ],
          is_required: false,
        };

        const createdOrdableOptions = await this.ordableOptionsService.create(
          {
            option_name: menuGroups[i].nameEn
              ? menuGroups[i].nameEn
              : menuGroups[i].nameAr,
            option_ar_name: menuGroups[i].nameAr
              ? menuGroups[i].nameAr
              : menuGroups[i].nameEn,
            product_id: productId,
            minimum: menuGroups[i].min || 1,
            maximum: menuGroups[i].max || 1,
            sort_order: i,
            is_required: menuGroups[i].min > 0,
            multiple: menuGroups[i].max > 1,
            choices: [
              {
                value: '',
                ar_value: '',
                price: 0,
                sort_order: 1,
                is_active: false,
              },
            ],
          },
          store,
        );
        if (createdOrdableOptions.success) {
          const storeId = store._id.toHexString();
          menuGroups[i].integrationInfo.ordableInfo[storeId] =
            createdOrdableOptions.data[0].option_id;
          menuGroups[i].markModified('integrationInfo');
          await menuGroups[i].save();
          await this.syncOrdableProductOptions(
            store,
            menuGroups[i],
            productId,
            menuGroups[i].integrationInfo.ordableInfo[storeId],
          );
        } else {
          this.loggerService.error(
            `Failed to create option for menu group ${menuGroups[i]._id}`,
            { createOrdableOptionsDto, store, createdOrdableOptions },
          );
        }
      }
    }
  }

  async syncOrdableProductOptions(
    store: StoreDocument,
    menuGroup: MenuGroupDocument,
    productId: number,
    optionId: number,
  ) {
    const modifiers = menuGroup.items;
    const storeId = store._id.toHexString();
    for (let i = 0; i < modifiers.length; i++) {
      this.checkIntegrationInfoExisting(modifiers[i]);
      if (modifiers[i]?.integrationInfo?.ordableInfo[storeId]) {
        await this.ordableProductOptionsService.update(
          {
            id: modifiers[i]?.integrationInfo?.ordableInfo[storeId],
            value: modifiers[i].name,
            ar_value: '',
            price: modifiers[i].price,
            sort_order: 0,
            deliverect_plu: modifiers[i].plu,
          },
          store,
        );
      } else {
        if (productId === -1 || optionId === -1) {
          this.loggerService.log(
            `Skipping modifier ${modifiers[i].name} for menu group ${menuGroup._id.toHexString()}`,
          );
          continue;
        }
        const createOrdableProductOptionsDto: CreateOrdableProductOptionsDto = {
          product_id: productId,
          option_id: optionId,
          value: modifiers[i].name,
          ar_value: modifiers[i].name,
          price: modifiers[i].price,
          sort_order: 0,
          is_active: true,
          deliverect_plu: modifiers[i].plu,
        };

        const createdOrdableProductOptions =
          await this.ordableProductOptionsService.create(
            createOrdableProductOptionsDto,
            store,
          );
        if (createdOrdableProductOptions.success)
          modifiers[i].integrationInfo.ordableInfo[storeId] =
            createdOrdableProductOptions?.data?.id;
        else
          this.loggerService.error(
            `Failed to create modifier ${modifiers[i].name} for menu group ${menuGroup._id}`,
          );
      }
    }

    menuGroup.items = modifiers;
    menuGroup.markModified('items');
    await menuGroup.save();
  }

  async assignMasterToSlave(
    assignMasterToSlaveDto: AssignMasterToSlaveDto,
  ): Promise<Menu> {
    const { slaveMenuId, masterMenuId } = assignMasterToSlaveDto;
    const slaveMenu = await this.menuModel.findById(slaveMenuId).exec();
    if (!slaveMenu)
      throw new NotFoundException(
        `Slave Menu with id ${slaveMenuId.toHexString()} not found`,
      );

    await slaveMenu.updateOne({ parentId: masterMenuId });
    slaveMenu.parentId = masterMenuId;
    return slaveMenu;
  }

  async findByStore(store: StoreWithId | StoreDocument): Promise<MenuWithId> {
    const key = fullCacheKeys[CacheKeys.MENU_WITH_STORE](
      store._id.toHexString(),
    );
    const menuCache = await this.cacheService.getCache(key, MenuWithId);

    if (menuCache) return menuCache;

    const menus = await this.index({
      isMaster: YesOrNo.YES,
      company: store.companyId.toString(),
      brandIds: store.brands.map((brand) => brand._id.toString()).join(','),
    });

    if (!menus.length)
      throw new BadRequestException(
        "No master menus found for this store's brands.",
      );
    else if (menus.length > 1)
      this.loggerService.warn(
        "Multiple master menus found for this store's brands - picking randomly.",
      );

    const menu = menus[0];
    await this.cacheService.setCache(key, menu);
    return menu;
  }

  public async uploadPdfMenuFile(file: Express.Multer.File): Promise<string> {
    const fileOriginalName = file.originalname.split('.')[0];
    const fileExtension = extname(file.originalname);
    const randomName = randomstring.generate({ charset: 'hex', length: 4 });
    const fileName = `${fileOriginalName}-${randomName}${fileExtension}`;

    await this.googleCloudStorageService.uploadDocument(file.buffer, fileName);

    return fileName;
  }

  private buildMenuFilter(menuToIndex: MenuToIndex): FilterQuery<Menu> {
    const match: FilterQuery<Menu> = {
      deletedAt: null,
    };

    if (menuToIndex.company) {
      match.company = new Types.ObjectId(menuToIndex.company);
    }

    if (menuToIndex.month) {
      match.month = menuToIndex.month;
    }

    if (menuToIndex.branch) {
      match.branches = new Types.ObjectId(menuToIndex.branch);
    }

    if (menuToIndex.branches) {
      const branchIds = menuToIndex.branches
        .split(',')
        .map((id) => new Types.ObjectId(id));
      match.$or = [
        { branches: { $elemMatch: { $in: branchIds } } },
        { isMaster: true },
      ];
    }

    if (menuToIndex.brandIds) {
      const brandIds = menuToIndex.brandIds
        .split(',')
        .map((id) => new Types.ObjectId(id));
      match['brand._id'] = { $in: brandIds };
    }

    if (menuToIndex.brandId) {
      match['brand._id'] = new Types.ObjectId(menuToIndex.brandId);
    }

    if (menuToIndex.isMaster) {
      match.isMaster = menuToIndex.isMaster === YesOrNo.YES;
    }

    return match;
  }

  private async makeAllMenuItemsAvailable(menuToUpdate: MenuToUpdate) {
    if (!menuToUpdate.isMaster) return;

    const menu = await this.menuModel.findById(menuToUpdate._id);
    const isAlreadyMaster = menu.isMaster;
    if (!menu || isAlreadyMaster) return;

    const menuItems = await this.menuItemService.findByMenuId(menu._id);
    if (!menuItems) return;

    const unavailableMenuItems = menuItems.filter((item) => !item.available);
    if (unavailableMenuItems.length === 0) return;

    const stores = await this.storeService.findByBrand(menu.brand._id);
    if (!stores || stores.length === 0) return;

    await Promise.all(
      stores.map((store) =>
        this.makeAllStoreItemsAvailable(store, unavailableMenuItems),
      ),
    );
  }

  private async makeAllStoreItemsAvailable(
    store: StoreDocument,
    menuItems: MenuItemDocument[],
  ) {
    if (!store || !menuItems) return;

    const hasMenuItemBeenSyncedToStore = (item: MenuItemDocument) =>
      item?.integrationInfo?.ordableInfo?.hasOwnProperty(store._id.toString());

    const menuItemUpdateDtos: UpdateOrdableProductsDto[] = menuItems
      .filter(hasMenuItemBeenSyncedToStore)
      .map((menuItem) => ({
        id: menuItem.integrationInfo.ordableInfo[store._id.toString()],
        is_active: true,
      }));

    await Promise.all(
      menuItemUpdateDtos.map(
        async (updateDto) =>
          await this.ordableProductsService.update(updateDto, store),
      ),
    );
  }

  private async createOrFindCategory(
    menuCategoryToCreate: MenuCategoryToCreate,
  ): Promise<MenuCategoryDocument> {
    let menuCategory = await this.menuCategoryModel.findOne({
      name: menuCategoryToCreate.name,
      menu: new Types.ObjectId(menuCategoryToCreate.menu),
    });

    if (!menuCategory) {
      menuCategory =
        await this.menuCategoryService.create(menuCategoryToCreate);
    }

    return menuCategory;
  }

  private populateMenuIds(menus: any[]) {
    const menuIds = [];
    for (let i = 0; i < menus.length; i++) {
      menuIds.push(menus[i]._id);
    }
    return menuIds;
  }

  private constructMenuToIndex(indexMenuDto: MenuToIndex): MenuToIndex {
    return {
      offset: 0,
      limit: 100000,
      currentUser: {},
      search_key: '',
      month: indexMenuDto.month,
      sort_type: indexMenuDto.sort_type,
      search_type: indexMenuDto.search_type,
      branch: indexMenuDto.branch,
      company: indexMenuDto.company,
      branches: indexMenuDto.branches,
      brandId: indexMenuDto.brandId,
    };
  }

  private async updateMany(
    menuIds: Types.ObjectId[],
    brandDto: EmbeddedBrandDto,
  ) {
    await this.menuModel.updateMany(
      { _id: { $in: menuIds } },
      { brand: brandDto },
    );
  }

  private async syncSlaveMenuForBranch(
    store: StoreDocument,
    menu: MenuDocument,
    branchId: Types.ObjectId,
  ) {
    const masterMenuItems = await this.menuItemService.findByMenuId(menu._id);

    const menuItemsToSync: MenuItemDocument[] = [];

    for (const masterMenuItem of masterMenuItems) {
      const slaveMenuItem = await this.menuItemService.findMenuItemForBranch(
        masterMenuItem.similarMenuItems ?? [],
        branchId,
      );

      if (slaveMenuItem !== undefined) {
        masterMenuItem.available = slaveMenuItem.available;
      }

      menuItemsToSync.push(masterMenuItem);
    }

    const categoryIdMap = await this.syncOrdableCategories(store, menu._id);
    await this.createOrUpdateOrdableProducts(
      store,
      menuItemsToSync,
      categoryIdMap,
      branchId,
    );

    this.loggerService.log(
      `Done syncing menu ${menu._id.toHexString()} for store ${store._id.toHexString()} with branchId ${branchId.toHexString()}`,
    );
  }

  private async syncOrdableProducts(
    store: StoreDocument,
    menuId: Types.ObjectId,
    branchId: Types.ObjectId,
  ) {
    const menuItems = await this.menuItemService.findByMenuId(menuId);
    const categoryIdMap = await this.syncOrdableCategories(store, menuId);
    await this.createOrUpdateOrdableProducts(
      store,
      menuItems,
      categoryIdMap,
      branchId,
    );
  }

  private async createOrUpdateOrdableProducts(
    store: StoreDocument,
    itemsToSync: MenuItemDocument[],
    categoryIdMap: OrdableCategoryMap,
    branchId?: Types.ObjectId,
  ) {
    // Sync available items before not available items
    const menuItems = itemsToSync.sort((item) => (item.available ? -1 : 1));
    for (let i = 0; i < menuItems.length; i++) {
      const isCategoryExist =
        categoryIdMap.hasOwnProperty(menuItems[i].menuCategory.toHexString()) &&
        categoryIdMap[menuItems[i].menuCategory.toHexString()] !== -1;

      if (!isCategoryExist) {
        this.loggerService.log(
          `Skipping product ${menuItems[i]._id.toHexString()} since category with id ${menuItems[i].menuCategory.toHexString()} was not synced`,
        );
        continue;
      }

      this.loggerService.log(
        `Syncing product ${menuItems[i]._id.toHexString()}`,
      );

      this.checkIntegrationInfoExisting(menuItems[i]);
      const storeId = store._id.toHexString();
      if (menuItems[i]?.integrationInfo?.ordableInfo[storeId]) {
        const response = await this.ordableProductsService.update(
          {
            id: menuItems[i]?.integrationInfo?.ordableInfo[storeId],
            ...this.toOrdableProductDto(menuItems[i], categoryIdMap, branchId),
          },
          store,
        );
        if (
          response.success &&
          response.data.is_active !== menuItems[i].available
        ) {
          this.loggerService.error(
            `Product ${menuItems[i]._id} is_active not synced with ordable`,
          );
          await this.makeMasterMenuItemAvailable(menuItems[i], response, store);
        }
        await this.syncOrdableOptions(
          store,
          menuItems[i],
          menuItems[i]?.integrationInfo?.ordableInfo[storeId],
        );
      } else {
        const create: (
          menuItem: MenuItemDocument,
          categoryIdMap: OrdableCategoryMap,
          store: StoreDocument,
          branchId?: Types.ObjectId,
        ) => Promise<any> = menuItems[i].available
          ? this.createAvailableOrdableProduct.bind(this)
          : this.createUnavailableOrdableProduct.bind(this);

        const createdOrdableProduct = await create(
          menuItems[i],
          categoryIdMap,
          store,
          branchId,
        );

        if (createdOrdableProduct.success) {
          this.loggerService.log(`Successfully created ${menuItems[i].nameEn}`);
          menuItems[i].integrationInfo.ordableInfo[storeId] =
            createdOrdableProduct.data.id;
          await menuItems[i].updateOne({
            $set: {
              [`integrationInfo.ordableInfo.${store._id.toHexString()}`]:
                createdOrdableProduct.data.id,
            },
          });
          await this.syncOrdableOptions(
            store,
            menuItems[i],
            menuItems[i]?.integrationInfo?.ordableInfo[storeId],
          );
        } else {
          this.loggerService.error(
            `Failed to create ordable product for menu item ${menuItems[i]._id.toHexString()}`,
          );
        }
      }
    }
  }

  private async createAvailableOrdableProduct(
    menuItem: MenuItemDocument,
    categoryIdMap: OrdableCategoryMap,
    store: StoreDocument,
    branchId?: Types.ObjectId,
  ) {
    return await this.ordableProductsService.create(
      this.toOrdableProductDto(menuItem, categoryIdMap, branchId),
      store,
    );
  }

  private async createUnavailableOrdableProduct(
    menuItem: MenuItemDocument,
    categoryIdMap: OrdableCategoryMap,
    store: StoreDocument,
    branchId?: Types.ObjectId,
  ) {
    const masterMenuItem = await this.ordableProductsService.create(
      { ...this.toOrdableProductDto(menuItem, categoryIdMap), is_active: true },
      store,
    );
    if (!masterMenuItem.success) return masterMenuItem;

    return await this.ordableProductsService.update(
      {
        id: masterMenuItem.data.id,
        is_active: false,
        branch_id: branchId.toHexString(),
      },
      store,
    );
  }

  private async makeMasterMenuItemAvailable(
    menuItem: MenuItemDocument,
    response: any,
    store: StoreDocument,
  ): Promise<void> {
    const storeId = store._id.toHexString();
    // There is an edge case, where if we try to enable a product that is
    // disabled on the master menu, it will remain disabled. So we need to
    // enable it on the master menu too.
    if (menuItem.available && response?.data?.is_active === false) {
      await this.ordableProductsService.update(
        {
          id: menuItem?.integrationInfo?.ordableInfo[storeId],
          is_active: true,
        },
        store,
      );
    }
  }

  private toOrdableProductDto(
    menuItem: MenuItemDocument,
    categoryIdMap: OrdableCategoryMap,
    branchId?: Types.ObjectId,
  ): CreateOrdableProductsDto {
    return {
      name: menuItem.nameEn ? menuItem.nameEn : menuItem.nameAr,
      ar_name: menuItem.nameAr ? menuItem.nameAr : menuItem.nameEn,
      price: menuItem.price,
      category_id: categoryIdMap[menuItem.menuCategory.toHexString()],
      photo: this.menuItemService.getImageUrl(menuItem),
      sku: menuItem.plu || menuItem?.reference || undefined,
      is_active: menuItem.available,
      description: menuItem.descriptionEn,
      ar_description: menuItem.descriptionAr,
      short_description: menuItem.descriptionEn,
      ar_short_description: menuItem.descriptionAr,
      branch_id: branchId ? branchId.toHexString() : '',
    };
  }

  private async syncOrdableCategories(
    store: StoreDocument,
    menuId: Types.ObjectId,
  ): Promise<OrdableCategoryMap> {
    const menuCategories = await this.menuCategoryService.findByMenuId(menuId);

    const ordableCategoryMapping: Record<string, number> = {};

    for (const menuCategory of menuCategories) {
      const [storeId, ordableCategoryId] =
        await this.createOrUpdateOrdableCategory(store, menuCategory);
      ordableCategoryMapping[storeId] = ordableCategoryId;
    }
    return ordableCategoryMapping;
  }

  private async createOrUpdateOrdableCategory(
    store: StoreDocument,
    menuCategory: MenuCategoryDocument,
  ): Promise<OrdableCategoryMapping> {
    this.checkIntegrationInfoExisting(menuCategory);
    const storeId = store._id.toHexString();
    if (menuCategory?.integrationInfo?.ordableInfo[storeId]) {
      await this.ordableCategoriesService.update(
        {
          id: menuCategory.integrationInfo.ordableInfo[storeId],
          name: menuCategory.name,
          ar_name: menuCategory.name,
          description: menuCategory.description,
          ar_description: '',
          parent: 0,
          photo: menuCategory?.image?.url ?? '',
          is_active: true,
          sort_order: menuCategory.sortOrder,
        },
        store,
      );
    } else {
      const createdOrdableCategory = await this.ordableCategoriesService.create(
        {
          name: menuCategory.name,
          ar_name: menuCategory.name,
          photo: menuCategory?.image?.url ?? '',
          is_active: true,
          sort_order: menuCategory.sortOrder,
        },
        store,
      );

      menuCategory.integrationInfo.ordableInfo[storeId] =
        createdOrdableCategory?.data?.id;
      menuCategory.markModified('integrationInfo');
      await menuCategory.save();
    }
    return [
      menuCategory._id.toHexString(),
      menuCategory.integrationInfo.ordableInfo[storeId],
    ];
  }
}
