// EBL-3615 [Loyalty Program] Add "Takeaway" and "Drive-Thru" Delivery Actions
// Update existing loyalty order delivery actions
db.orders.updateMany(
  { isAggregator: true, source: 'dine_in' },
  { $set: { delivery_action: 'dine_in' } },
);
db.orders.updateMany(
  { isAggregator: true, source: 'walk_in' },
  { $set: { delivery_action: 'walk_in' } },
);
db.orders.updateMany(
  { isAggregator: true, source: 'drive_thru' },
  { $set: { delivery_action: 'drive_thru' } },
);
