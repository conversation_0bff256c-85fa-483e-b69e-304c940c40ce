import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import * as moment from 'moment-timezone';
import {
  Achievement,
  GenericRepository,
  PunchCard,
  PunchCardCounter,
  PunchCardDocument,
} from '@app/shared-stuff';

@Injectable()
export class PunchCardRepository extends GenericRepository<
  PunchCardDocument,
  PunchCard
> {
  constructor(
    @InjectModel(PunchCard.name)
    private punchCardModel: Model<PunchCardDocument, PunchCard>,
  ) {
    super(punchCardModel);
  }

  public async findByCompanyId(
    companyId: Types.ObjectId,
  ): Promise<PunchCardDocument[]> {
    return await this.punchCardModel.find({ companyId, deletedAt: null });
  }

  public async countByCompanyId(companyId: Types.ObjectId): Promise<number> {
    return await this.punchCardModel.count({ companyId, deletedAt: null });
  }

  public async removeAchievement(
    punchCardId: Types.ObjectId,
    achievement: Achievement,
  ): Promise<PunchCardDocument> {
    return await this.punchCardModel.findOneAndUpdate(
      { _id: punchCardId },
      { $pull: { achievements: achievement } },
      { new: true },
    );
  }

  public async findManyById(
    punchCardIds: Types.ObjectId[],
  ): Promise<PunchCardDocument[]> {
    return await this.punchCardModel.find({ _id: { $in: punchCardIds } });
  }

  public async updateCounter(
    punchCardId: Types.ObjectId,
    counter: PunchCardCounter,
  ): Promise<PunchCardDocument> {
    return await this.punchCardModel.findOneAndUpdate(
      { _id: punchCardId },
      {
        $set: {
          counter,
          'achievements.$[].requirement.type': counter.type,
          'achievements.$[].requirement.menuItem': counter.menuItem ?? null,
          'achievements.$[].updatedAt': moment.utc().toDate(),
        },
      },
      { new: true },
    );
  }
}
