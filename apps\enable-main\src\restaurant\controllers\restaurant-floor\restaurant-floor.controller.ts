import { Request, Response } from 'express';
import { HelperService } from './../../../shared/services/helper/helper.service';
import { RestaurantFloorService } from './../../services/restaurant-floor/restaurant-floor.service';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  SetMetadata,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import {
  FloorToCreate,
  FloorToIndex,
  FloorToUpdate,
  responseCode,
} from '@app/shared-stuff';

@Controller('restaurant-floor')
@ApiTags('Restaurant Floor')
@SetMetadata('module', 'restaurant-floor')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class RestaurantFloorController {
  constructor(
    private floorService: RestaurantFloorService,
    private helperService: HelperService,
  ) {}
  @Get()
  @SetMetadata('action', 'get_all')
  async index(@Query() taleToIndex: FloorToIndex, @Res() res: Response) {
    try {
      const floors = await this.floorService.index(taleToIndex);
      const totalNumberOfFloors =
        await this.floorService.getTotalNumberOfFloors(taleToIndex);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to get all restaurant floors',
        { floors: floors, totalFloors: totalNumberOfFloors },
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  async getDetails(@Param('id') id: string, @Res() res: Response) {
    try {
      const item = await this.floorService.getDetails(id);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'success to get all restaurant floor details',
        item,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() floorToCreate: FloorToCreate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      floorToCreate['company'] = req['company_id'];
      // floorToCreate.currentUser = req['current'];
      const item = await this.floorService.create(floorToCreate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success to Create Restaurant floor',
        item,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Put(':id')
  @SetMetadata('action', 'update')
  async update(
    @Body() floorToUpdate: FloorToUpdate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      floorToUpdate['company'] = req['company_id'];
      // floorToUpdate.currentUser = req['current'];
      const item = await this.floorService.update(floorToUpdate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'success to Update restaurant floor ',
        item,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Delete(':id')
  @SetMetadata('action', 'delete')
  async delete(
    @Param('id') id: string,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const item = await this.floorService.remove({
        _id: id,
        deletedBy: req['current'],
      });
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_REMOVE,
        'success to REMOVE Menus item',
        item,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
}
