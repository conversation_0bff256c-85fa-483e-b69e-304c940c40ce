import {
  DeliveryThirdPartyName,
  LoggerService,
  OrderDeliveryAction,
  OrderDocument,
  responseCode,
} from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CompanyService } from '../../../company/services/company/company.service';
import { CustomerReadServiceInterface } from '../../../customer/modules/customer-read/customer-read.service.interface';
import { ThirdPartySharedService } from '../../../delivery/services/third-parties/third-party-shared.service';
import { VehicleConfig } from '../../../delivery/types/third-party-vehicle-response.type';
import { IntegrationLogRepositoryInterface } from '../../../integration/integration-log/repositories/interfaces/integration-log.repository.interface';
import { SavedLocationService } from '../../../location/services/saved-location/saved-location.service';
import { HelperService } from '../../../shared/services/helper/helper.service';
import { ThirdPartyTaskCreationDto } from '../../dto/third-party-task-creation.dto';
import { BeeService } from './bee/bee.service';
import { ClicksService } from './clicks/clicks.service';
import { DeliveryHubService } from './delivery-hub/delivery-hub.service';
import { EnableDeliveryService } from './enable-delivery/enable-delivery.service';
import { EnableRidersService } from './enable-riders/enable-riders.service';
import { FalconFlexService } from './falcon-flex/services/falcon-flex.service';
import { HsDeliveryService } from './hs-delivery/hs-delivery.service';
import { MrDeliveryService } from './mr-delivery/mr-delivery.service';
import { PassDeliveryService } from './pass-delivery/services/pass-delivery.service';
import { ThirdPartiesServiceInterface } from './third-parties.service.interface';
import { WishboxService } from './wishbox/services/wishbox.service';

@Injectable()
export class ThirdPartyService {
  private readonly loggerService = new LoggerService(ThirdPartyService.name);

  private thirdPartyMap: Map<
    DeliveryThirdPartyName,
    ThirdPartiesServiceInterface
  >;

  constructor(
    private configService: ConfigService,
    private httpService: HttpService,
    private eventEmitter: EventEmitter2,
    private thirdPartySharedService: ThirdPartySharedService,
    private companyService: CompanyService,
    @Inject(CustomerReadServiceInterface)
    private customerReadService: CustomerReadServiceInterface,
    private savedLocationService: SavedLocationService,
    private helperService: HelperService,
    @Inject('IntegrationLogRepositoryInterface')
    private readonly integrationLogRepository: IntegrationLogRepositoryInterface,
  ) {
    this.initThirdPartyMap();
  }

  async createThirdPartyTask(
    desiredThirdParty: DeliveryThirdPartyName,
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
  ) {
    if ((await this.validateThirdPartyTask(thirdPartyTaskCreationDto)) == false)
      return;

    const thirdPartyService = this.thirdPartyMap.get(desiredThirdParty);
    if (thirdPartyService) {
      thirdPartyTaskCreationDto.order.vehicleType =
        thirdPartyTaskCreationDto.order.vehicleType ??
        thirdPartyService.defaultVehicleType;
      const taskCreationResponse = await thirdPartyService.createTask(
        thirdPartyTaskCreationDto,
      );
      this.loggerService.log(
        'Create Third party Task',
        thirdPartyTaskCreationDto,
        taskCreationResponse,
      );
      await thirdPartyService.applyPostFunction(
        taskCreationResponse?.response,
        thirdPartyTaskCreationDto.order,
      );
      return taskCreationResponse;
    } else
      throw new BadRequestException(
        `${desiredThirdParty} is not a valid delivery third party, please provide a correct one`,
        responseCode.IN_VALID_INPUT.toString(),
      );
  }

  getVehicles(thirdPartyName: DeliveryThirdPartyName): VehicleConfig {
    const party = this.thirdPartyMap.get(thirdPartyName);
    if (!party) throw new BadRequestException('Invalid third party name');

    return {
      vehicleTypes: party.vehicleTypes,
      default: party.defaultVehicleType,
    };
  }

  private async validateThirdPartyTask(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
  ): Promise<boolean> {
    let validThirdPartyTaskToCreate = true;
    validThirdPartyTaskToCreate = this.isValidToCreate(
      thirdPartyTaskCreationDto.order,
    );
    if (validThirdPartyTaskToCreate)
      await this.populateThirdPartyTaskCreationDto(thirdPartyTaskCreationDto);
    return validThirdPartyTaskToCreate;
  }

  private initThirdPartyMap() {
    this.thirdPartyMap = new Map<
      DeliveryThirdPartyName,
      ThirdPartiesServiceInterface
    >([
      [
        DeliveryThirdPartyName.clicks,
        new ClicksService(
          this.configService,
          this.httpService,
          this.eventEmitter,
          this.thirdPartySharedService,
          this.integrationLogRepository,
          this.helperService,
        ),
      ],
      [
        DeliveryThirdPartyName.mrDelivery,
        new MrDeliveryService(
          this.configService,
          this.httpService,
          this.eventEmitter,
          this.thirdPartySharedService,
        ),
      ],
      [
        DeliveryThirdPartyName.deliveryHub,
        new DeliveryHubService(
          this.configService,
          this.thirdPartySharedService,
        ),
      ],
      [
        DeliveryThirdPartyName.bee,
        new BeeService(
          this.configService,
          this.httpService,
          this.eventEmitter,
          this.thirdPartySharedService,
        ),
      ],
      [
        DeliveryThirdPartyName.enableDelivery,
        new EnableDeliveryService(
          this.configService,
          this.thirdPartySharedService,
        ),
      ],
      [
        DeliveryThirdPartyName.wishbox,
        new WishboxService(
          this.configService,
          this.httpService,
          this.helperService,
          this.integrationLogRepository,
          this.thirdPartySharedService,
          this.eventEmitter,
        ),
      ],
      [
        DeliveryThirdPartyName.hsDelivery,
        new HsDeliveryService(this.configService, this.thirdPartySharedService),
      ],
      [
        DeliveryThirdPartyName.falconFlex,
        new FalconFlexService(
          this.configService,
          this.httpService,
          this.thirdPartySharedService,
          this.integrationLogRepository,
          this.helperService,
          this.eventEmitter,
          this.savedLocationService,
        ),
      ],
      [
        DeliveryThirdPartyName.passDelivery,
        new PassDeliveryService(
          this.configService,
          this.httpService,
          this.thirdPartySharedService,
          this.integrationLogRepository,
          this.helperService,
        ),
      ],
      [
        DeliveryThirdPartyName.enableRiders,
        new EnableRidersService(
          this.configService,
          this.thirdPartySharedService,
        ),
      ],
    ]);
  }

  private isValidToCreate(order: OrderDocument): boolean {
    if (
      order.deliveryTaskCreated ||
      order.delivery_action == OrderDeliveryAction.IN_STORE_PICKUP
    )
      return false;
  }

  private async populateThirdPartyTaskCreationDto(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
  ) {
    // m4 moktne3 b7war 2ni m3ml4 hit lel DB , da m4 he7sl kda kda
    if (!thirdPartyTaskCreationDto.company)
      thirdPartyTaskCreationDto.company = await this.companyService.get_details(
        thirdPartyTaskCreationDto.order['company']['_id']
          ? thirdPartyTaskCreationDto.order['company']['_id']
          : thirdPartyTaskCreationDto.order['company'],
      );
    if (!thirdPartyTaskCreationDto.customer)
      thirdPartyTaskCreationDto.customer =
        await this.customerReadService.findOne(
          thirdPartyTaskCreationDto.order?.customer.toHexString(),
          thirdPartyTaskCreationDto.order?.company,
        );
    if (!thirdPartyTaskCreationDto.pickupLocation)
      thirdPartyTaskCreationDto.pickupLocation =
        await this.savedLocationService.getDetails(
          thirdPartyTaskCreationDto.order?.pickupLocationId.toHexString(),
        );
  }
}
