import {
  CollectionName,
  GenericRepository,
  LoyaltyTransaction,
  LoyaltyTransactionDocument,
} from '@app/shared-stuff';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { LoyaltyTransactionRepositoryInterface } from './loyalty-transaction-repository.interface';

export class LoyaltyTransactionRepository
  extends GenericRepository<LoyaltyTransactionDocument, LoyaltyTransaction>
  implements LoyaltyTransactionRepositoryInterface
{
  constructor(
    @InjectModel(CollectionName.LOYALTY_TRANSACTION)
    private readonly loyaltyTransactionModel: Model<
      LoyaltyTransactionDocument,
      LoyaltyTransaction
    >,
  ) {
    super(loyaltyTransactionModel);
  }

  public async createMany(
    transactions: LoyaltyTransaction[],
  ): Promise<LoyaltyTransactionDocument[]> {
    return this.loyaltyTransactionModel.create(transactions);
  }
}
