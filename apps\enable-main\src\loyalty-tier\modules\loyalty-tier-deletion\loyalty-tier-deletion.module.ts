import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';

import { LoyaltyTierReadModule } from '../loyalty-tier-read/loyalty-tier-read.module';
import { LoyaltyTierRepositoryModule } from '../loyalty-tier-repository/loyalty-tier-repository.module';
import { loyaltyTierDeletionService } from './loyalty-tier-deletion.service';
import { LoyaltyTierDeletionServiceInterface } from './loyalty-tier-deletion.service.interface';

import { CompanyModule } from '../../../company/company.module';
import { CustomerIndexModule } from '../../../customer/modules/customer-index/customer-index.module';
import { CustomerTierModule } from '../../../customer/modules/customer-tier/customer-tier.module';
import { LoyaltyTierIndexModule } from '../loyalty-tier-index/loyalty-tier-index.module';

@Module({
  providers: [
    {
      provide: LoyaltyTierDeletionServiceInterface,
      useClass: loyaltyTierDeletionService,
    },
  ],
  imports: [
    LoyaltyTierRepositoryModule,
    CompanyModule,
    LoyaltyTierReadModule,
    LoyaltyTierIndexModule,
    CustomerIndexModule,
    CustomerTierModule,
    EventEmitterModule,
  ],
  exports: [LoyaltyTierDeletionServiceInterface],
})
export class LoyaltyTierDeletionModule {}
