import { Module } from '@nestjs/common';
import { LoyaltyTierRepositoryModule } from '../loyalty-tier-repository/loyalty-tier-repository.module';
import { LoyaltyTierReadService } from './loyalty-tier-read.service';
import { LoyaltyTierReadServiceInterface } from './loyalty-tier-read.service.interface';

@Module({
  providers: [
    {
      provide: LoyaltyTierReadServiceInterface,
      useClass: LoyaltyTierReadService,
    },
  ],
  imports: [LoyaltyTierRepositoryModule],
  exports: [LoyaltyTierReadServiceInterface],
})
export class LoyaltyTierReadModule {}
