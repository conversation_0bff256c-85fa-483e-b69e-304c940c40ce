# Configure the variables below, then run:
# python import-menu-item-excel.py 301913-Products_Catalog_Plaza_Hollandi_2024-09-03.xlsx
# (You may need to `python -m pip install pandas` first)
BASE_URL = "http://localhost:3004"
# BASE_URL = "https://backend.enable.tech"
COMPANY_ID = ""
MENU_ID = ""
MENU_NAME = "Master Menu"
# AUTH_TOKEN = ""
API_KEY = ""

import pandas as pd
import requests
import re
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock

file_path = sys.argv[1]
menu_category_api = f"{BASE_URL}/menu-category"
menu_item_api = f"{BASE_URL}/menu-item"
headers = {
    "Content-Type": "application/json",
    # "Authorization": f"Bearer {AUTH_TOKEN}",
    "saasapikey": API_KEY,
}
categories = {}
category_lock = Lock()


def get_category_id(unnormalized_name):
    if unnormalized_name == "" or unnormalized_name == "Uncategorized":
        return ""

    name = str(unnormalized_name).split(">")[-1].strip()

    with category_lock:
        if name in categories:
            return categories[name]

        category_response = requests.post(
            menu_category_api,
            json={"name": name, "menu": MENU_ID},
            headers=headers,
        )
        res = category_response.json()
        category_id = res["data"]["_id"]

        if not category_id:
            print(f"Failed to get category ID for category: {name}")
            print(res)
            return

        print(f"Menu category {name} created with ID {category_id}")
        categories[name] = category_id
        return category_id


def post_data_to_api(row):
    category_id = get_category_id(str(row["category_name"]))
    if category_id is None:
        print(
            f"Skipping SKU {row['product_sku']} due to missing category {row['product_category']}"
        )
        return

    description = (
        re.sub(r"<[^>]+>", "", row["product_long_description"])
        if isinstance(row["product_long_description"], str)
        else row["product_long_description"] or ""
    )
    sku = row["product_sku"] or ""
    menu_item_payload = {
        "nameAr": row["product_name"],
        "nameEn": row["product_name"],
        "descriptionEn": description,
        "descriptionAr": description,
        "plu": sku,
        "reference": sku,
        "code": sku,
        "price": row["product_price"],
        "available": "true",
        "menu": MENU_ID,
        "type": "regular",
        "menuCategory": category_id,
        "company": COMPANY_ID,
        "menuGroups": [],
        "images": [],
        "subItems": [],
    }

    item_response = requests.post(
        menu_item_api,
        json=menu_item_payload,
        headers=headers,
    )

    if item_response.status_code != 200:
        print(f"Failed to create menu item for SKU: {sku}")
        print(item_response.json())
    else:
        print(f"Menu item created for SKU: {sku}")


df = pd.read_csv(
    file_path,
)
df["product_long_description"].fillna("", inplace=True)
df["product_category"].fillna("", inplace=True)
df["product_sku"].fillna("", inplace=True)

with ThreadPoolExecutor(max_workers=3) as executor:
    futures = [executor.submit(post_data_to_api, row) for _, row in df.iterrows()]

    for future in as_completed(futures):
        try:
            future.result()  # This will raise exceptions if any occurred in the thread
        except Exception as e:
            print(f"Error occurred: {e}")
