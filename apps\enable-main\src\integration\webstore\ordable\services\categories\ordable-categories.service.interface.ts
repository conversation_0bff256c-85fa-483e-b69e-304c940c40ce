import { Store } from '../../../../../store/models/store.model';
import { CreateOrdableCategoriesDto } from '../../dtos/categories/create-ordable-categories.dto';
import { UpdateOrdableCategoriesDto } from '../../dtos/categories/update-ordable-categories.dto';

export interface OrdableCategoriesServiceInterface {
  create(
    createOrdableCategoriesDto: CreateOrdableCategoriesDto,
    store: Store,
  ): Promise<any>;

  update(
    updateOrdableCategoriesDto: UpdateOrdableCategoriesDto,
    store: Store,
  ): Promise<any>;

  findAll(store: Store): Promise<any>;
}
