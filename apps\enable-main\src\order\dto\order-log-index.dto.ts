import { DataIndex, OrderLogActionEnum } from '@app/shared-stuff';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';

export class OrderLogIndexDto extends DataIndex {
  @ApiProperty({
    required: false,
    type: OrderLogActionEnum,
    enumName: 'OrderLogActionEnum',
  })
  @IsOptional()
  orderAction?: OrderLogActionEnum;

  @ApiProperty({
    required: false,
    type: String,
  })
  orderCode: string;
}
