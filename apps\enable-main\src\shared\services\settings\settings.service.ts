import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import { Model } from 'mongoose';
import { SettingsModelDocument } from '../../models/settings.model';

@Injectable()
export class SettingsService {
  constructor(
    @InjectModel('SettingsModel')
    private settingsModel: Model<SettingsModelDocument>,
  ) {}

  async getSettings() {
    const setting = (await this.settingsModel.find({}))[0];
    return setting;
  }
}
