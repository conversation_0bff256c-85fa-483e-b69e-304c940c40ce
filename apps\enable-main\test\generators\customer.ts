import { CountryDialCode, CreateCustomerDto } from '@app/shared-stuff';
import { faker } from '@faker-js/faker';
import { Types } from 'mongoose';

export const generateRandomCustomer = (
  companyId: string,
  override: Partial<CreateCustomerDto> = {},
): Partial<CreateCustomerDto> => ({
  first_name: 'e2etest',
  last_name: faker.person.lastName(),
  country_code: CountryDialCode.QATAR,
  phone: faker.string.numeric('5#######'),
  email: faker.internet.email({
    firstName: 'e2etest',
    lastName: faker.person.lastName(),
    provider: 'testmail.com',
  }),
  company: new Types.ObjectId(companyId),
  ...override,
});
