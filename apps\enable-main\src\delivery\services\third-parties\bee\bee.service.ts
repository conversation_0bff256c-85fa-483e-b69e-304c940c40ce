import { OrderDocument, OrderPaymentMethod } from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { BeeConfig, BeeTaskToCreate } from '../../../dto/bee.dto';
import { ThirdPartyTaskCreationDto } from '../../../dto/third-party-task-creation.dto';
import { ThirdPartiesServiceInterface } from '../third-parties.service.interface';
import { ThirdPartySharedService } from '../third-party-shared.service';

@Injectable()
export class BeeService implements ThirdPartiesServiceInterface {
  beeConfig: BeeConfig;
  vehicleTypes: string[] = [] as const;
  defaultVehicleType: string = '';

  constructor(
    private configService: ConfigService,
    private httpService: HttpService,
    private eventEmitter: EventEmitter2,
    private thirdPartySharedService: ThirdPartySharedService,
  ) {}

  async createTask(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
  ): Promise<any> {
    const pickupLocation = await this.thirdPartySharedService.getPickupLocation(
      thirdPartyTaskCreationDto,
    );
    const pickupAddressString =
      await this.thirdPartySharedService.getPickupAddressString(pickupLocation);
    const deliveryLocation =
      await this.thirdPartySharedService.getDeliveryLocation(
        thirdPartyTaskCreationDto,
      );
    const deliveryAddressString =
      await this.thirdPartySharedService.getDeliveryAddressString(
        deliveryLocation,
      );
    const beeDeliveryTaskToCreate = this.constructCreateBeeTaskDto(
      thirdPartyTaskCreationDto,
      pickupLocation,
      pickupAddressString,
      deliveryLocation,
      deliveryAddressString,
    );
    beeDeliveryTaskToCreate.mobile = beeDeliveryTaskToCreate.mobile.replace(
      '+',
      '',
    );
    const beeConfig = this.loadBeeConfig();
    const taskCreationResponse = await this.performBeeRequest(
      beeConfig.baseUrl + '/order/add_order',
      beeDeliveryTaskToCreate,
    );
    return { request: beeDeliveryTaskToCreate, response: taskCreationResponse };
  }

  onTaskUpdated(data: any) {
    this.eventEmitter.emit('bee.updated', data);
  }

  loadBeeConfig() {
    if (this.beeConfig) {
      return this.beeConfig;
    }
    this.beeConfig = {
      token: this.configService.get('BEE_TOKEN'),
      baseUrl: this.configService.get('BEE_BASEURL'),
    };
    return this.beeConfig;
  }

  async applyPostFunction(taskCreationResponse: any, order: OrderDocument) {
    await this.updateOrder(order);
  }

  async updateOrder(order: OrderDocument) {
    order.deliveryTaskCreated = true;
    order.assigned_driver_name = 'Bee Driver';
    order.driver = undefined;
    await order.save();
  }

  private constructCreateBeeTaskDto(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
    pickupLocation,
    pickupAddressString,
    deliveryLocation,
    deliveryAddressString,
  ): BeeTaskToCreate {
    const deliveryTime = moment(
      thirdPartyTaskCreationDto.order.delivery_date,
    ).format('YYYY-MM-DD HH:mm:ss');
    return {
      name: thirdPartyTaskCreationDto.order.customer_name,
      mobile:
        (thirdPartyTaskCreationDto.order.country_code ?? '') +
        thirdPartyTaskCreationDto.order.customer_phone,
      branch_name: thirdPartyTaskCreationDto.order.branch['name'],
      ref_number: thirdPartyTaskCreationDto.order.code,
      payment_type:
        thirdPartyTaskCreationDto.order.payment_method ==
          OrderPaymentMethod.online ||
        thirdPartyTaskCreationDto.order.payment_method ==
          OrderPaymentMethod.prepaid
          ? '2'
          : '1',
      total_amount: thirdPartyTaskCreationDto.order.invoiced_amount,
      customer_full_address: deliveryAddressString,
      branch_id: thirdPartyTaskCreationDto.order['branch']['_id'].toHexString(),
      branch_address: pickupAddressString,
      branch_mobile: thirdPartyTaskCreationDto.branch
        ? thirdPartyTaskCreationDto.branch.phone
        : thirdPartyTaskCreationDto.company.phone,
      client_name: thirdPartyTaskCreationDto.company['name'],
      branch_latitude: pickupLocation ? pickupLocation.latitude : 0,
      branch_longitude: pickupLocation ? pickupLocation.longitude : 0,
      customer_address_latitude: deliveryLocation
        ? deliveryLocation.latitude
        : 0,
      customer_address_longitude: deliveryLocation
        ? deliveryLocation.longitude
        : 0,
      client_id: new Types.ObjectId().toHexString(),
      estimate_cooking_time: deliveryTime.toString(),
    };
  }

  private async performBeeRequest(url, data) {
    return new Promise((resolve, reject) => {
      const beeConfig = this.loadBeeConfig();
      this.httpService
        .post(url, data, {
          headers: {
            AUTHORIZATION: `Bearer ${
              data['API_TOKEN'] ? data['API_TOKEN'] : beeConfig.token
            }`,
          },
        })
        .subscribe(
          (data) => {
            resolve(data.data);
          },
          (err) => {
            resolve(err);
          },
        );
    });
  }
}
