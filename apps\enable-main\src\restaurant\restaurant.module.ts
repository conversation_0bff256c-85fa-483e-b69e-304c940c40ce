import {
  CollectionName,
  CustomCacheModule,
  DocumentRedirectSchema,
  MenuCategoryDocument,
  MenuCategoryEvents,
  MenuCategorySchema,
  MenuDocument,
  MenuEvents,
  MenuGroupSchema,
  MenuItemAvailabilitySchema,
  MenuItemSchema,
  MenuSchema,
  SharedStuffModule,
} from '@app/shared-stuff';
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { EventEmitter2, EventEmitterModule } from '@nestjs/event-emitter';
import { MongooseModule } from '@nestjs/mongoose';
import { MenuCategoryListener } from 'apps/enable-main/src/restaurant/listeners/menu-category.listener';
import { BrandModule } from '../brand/brand.module';
import { CustomerReadModule } from '../customer/modules/customer-read/customer-read.module';
import { CustomerWriteModule } from '../customer/modules/customer-write/customer-write.module';
import { OrdableModule } from '../integration/webstore/ordable/ordable.module';
import { PunchCardReadModule } from '../punch-card/modules/punch-card-read/punch-card-read.module';
import { StorageModule } from '../storage/storage.module';
import { StoreModule } from '../store/store.module';
import { BranchModule } from './../branch/branch.module';
import { CompanyModule } from './../company/company.module';
import { NotificationModule } from './../notification/notification.module';
import { SharedModule } from './../shared/shared.module';
import { MenuCategoryController } from './controllers/menu-category/menu-category.controller';
import { MenuGroupController } from './controllers/menu-group/menu-group.controller';
import { MenuItemController } from './controllers/menu-item/menu-item.controller';
import { MenuController } from './controllers/menu/menu.controller';
import { RestaurantFloorController } from './controllers/restaurant-floor/restaurant-floor.controller';
import { RestaurantTableController } from './controllers/restaurant-table/restaurant-table.controller';
import { FloorSchema } from './models/restaurant-floor.model';
import { TableSchema } from './models/restaurant-table.model';
import { MenuItemAvailabilityRepository } from './repositories/menu-item-availability.repository';
import { MenuCategoryService } from './services/menu-category/menu-category.service';
import { MenuGroupService } from './services/menu-group/menu-group.service';
import { MenuItemAvailabilityService } from './services/menu-item-availability/menu-item-availability.service';
import { MenuItemNotificationService } from './services/menu-item-notification/menu-item-notification.service';
import { MenuItemService } from './services/menu-item/menu-item.service';
import { MenuTalabatService } from './services/menu-talabat/menu-talabat.service';
import { MenuService } from './services/menu/menu.service';
import { RestaurantFloorService } from './services/restaurant-floor/restaurant-floor.service';
import { RestaurantTableService } from './services/restaurant-table/restaurant-table.service';

@Module({
  imports: [
    OrdableModule,
    StoreModule,
    BranchModule,
    CompanyModule,
    BrandModule,
    SharedModule,
    NotificationModule,
    HttpModule,
    EventEmitterModule,
    SharedStuffModule,
    CustomerReadModule,
    CustomerWriteModule,
    PunchCardReadModule,
    StorageModule,
    CustomCacheModule,
    MongooseModule.forFeatureAsync([
      {
        name: 'Menu',
        useFactory: (eventEmitter: EventEmitter2) => {
          const menuSchema = MenuSchema;
          menuSchema.post<MenuDocument>(
            'save',
            async function (menu: MenuDocument, next: any) {
              eventEmitter.emit(MenuEvents.MENU_UPDATED, menu);
              next();
            },
          );

          return menuSchema;
        },
        inject: [EventEmitter2],
      },
      {
        name: 'MenuCategory',
        useFactory: (eventEmitter: EventEmitter2) => {
          const menuCategorySchema = MenuCategorySchema;
          menuCategorySchema.post<MenuCategoryDocument>(
            'save',
            async function (menuCategory: MenuCategoryDocument, next: any) {
              eventEmitter.emit(
                MenuCategoryEvents.MENU_CATEGORY_UPDATED,
                menuCategory,
                this.isModified('name'),
                this._previousName,
              );
              next();
            },
          );
          return menuCategorySchema;
        },
        inject: [EventEmitter2],
      },
    ]),

    MongooseModule.forFeature([
      { name: 'MenuCategory', schema: MenuCategorySchema },

      { name: 'MenuItem', schema: MenuItemSchema },
      { name: 'MenuGroup', schema: MenuGroupSchema },
      { name: 'Table', schema: TableSchema },
      { name: 'Floor', schema: FloorSchema },
      { name: 'MenuItemAvailability', schema: MenuItemAvailabilitySchema },
      {
        name: CollectionName.DOCUMENT_REDIRECT,
        schema: DocumentRedirectSchema,
      },
    ]),
  ],
  controllers: [
    MenuController,
    MenuCategoryController,
    MenuItemController,
    MenuGroupController,
    RestaurantTableController,
    RestaurantFloorController,
  ],
  providers: [
    MenuService,
    MenuCategoryService,
    MenuItemService,
    MenuGroupService,
    RestaurantTableService,
    MenuTalabatService,
    RestaurantFloorService,
    MenuItemNotificationService,
    {
      provide: 'MenuItemAvailabilityServiceInterface',
      useClass: MenuItemAvailabilityService,
    },
    {
      provide: 'MenuItemAvailabilityRepositoryInterface',
      useClass: MenuItemAvailabilityRepository,
    },
    MenuCategoryListener,
  ],
  exports: [
    MenuItemService,
    MenuCategoryService,
    MenuService,
    MenuGroupService,

    RestaurantFloorService,
    RestaurantTableService,
  ],
})
export class RestaurantModule {}
