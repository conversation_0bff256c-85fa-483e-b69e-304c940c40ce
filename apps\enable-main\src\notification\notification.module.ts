import {
  MicroserviceCommunicationModule,
  SharedStuffModule,
} from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { BranchModule } from '../branch/branch.module';
import { BrandModule } from '../brand/brand.module';
import { CustomerReadModule } from '../customer/modules/customer-read/customer-read.module';
import { CustomerUserModule } from '../customer/modules/customer-user/customer-user.module';
import { LoyaltyTierReadModule } from '../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.module';
import { SharedModule } from '../shared/shared.module';
import { UserModule } from '../user/user.module';
import { CompanyModule } from './../company/company.module';
import { ConfigurationController } from './controllers/configuration/configuration.controller';
import { FcmController } from './controllers/fcm/fcm.controller';
import { TemplateController } from './controllers/template/template.controller';
import { TriggerController } from './controllers/trigger/trigger.controller';
import { ConfigurationService } from './services/configuration/configuration.service';
import { GenericReplacementService } from './services/generic-replacement/generic-replacement.service';
import { NotificationService } from './services/notification/notification.service';
import { TemplateService } from './services/template/template.service';
import { TriggerService } from './services/trigger/trigger.service';

@Module({
  controllers: [
    TriggerController,
    TemplateController,
    ConfigurationController,
    FcmController,
  ],
  providers: [
    NotificationService,
    TriggerService,
    TemplateService,
    ConfigurationService,
    GenericReplacementService,
  ],
  imports: [
    UserModule,
    SharedModule,
    CompanyModule,
    ConfigModule,
    CustomerReadModule,
    CustomerUserModule,
    EventEmitterModule,
    BrandModule,
    MicroserviceCommunicationModule.forChild(
      'enable-main-notification-producer',
    ),
    LoyaltyTierReadModule,
    BranchModule,
    SharedStuffModule,
  ],
  exports: [
    NotificationService,
    TriggerService,
    ConfigurationService,
    TemplateService,
    GenericReplacementService,
  ],
})
export class NotificationModule {}
