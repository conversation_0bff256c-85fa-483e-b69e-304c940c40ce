import {
  DeliveryMethod,
  DeliveryThirdPartyName,
  OrderDeliveryType,
  OrderOwnDriversAssignmentType,
} from '@app/shared-stuff';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsMongoId, IsOptional, ValidateIf } from 'class-validator';
import { Types } from 'mongoose';

export class DeliveryOptionsDto {
  @ApiProperty({
    type: String,
    enum: OrderDeliveryType,
    required: true,
  })
  @IsEnum(OrderDeliveryType)
  deliveryType: OrderDeliveryType;

  @ApiProperty({
    type: String,
    enum: DeliveryMethod,
    required: true,
  })
  @IsOptional()
  @IsEnum(DeliveryMethod)
  deliveryMethod?: DeliveryMethod;

  @ApiProperty({
    type: String,
    enum: DeliveryThirdPartyName,
    required: false,
    description: 'Required if deliveryParty is "thirdParty"',
  })
  @ValidateIf(
    (deliveryOptions: DeliveryOptionsDto) =>
      deliveryOptions.deliveryMethod === DeliveryMethod.THIRD_PARTY,
  )
  @IsEnum(DeliveryThirdPartyName)
  thirdParty?: DeliveryThirdPartyName;

  @ApiProperty({
    type: String,
    enum: OrderOwnDriversAssignmentType,
    required: true,
  })
  @ValidateIf(
    (deliveryOptions: DeliveryOptionsDto) =>
      deliveryOptions.deliveryMethod == DeliveryMethod.BRANCH_DRIVERS,
  )
  @IsEnum(OrderOwnDriversAssignmentType)
  assignmentType: OrderOwnDriversAssignmentType;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
    description: 'Required if assignmentType is "assignNow"',
  })
  @ValidateIf(
    (deliveryOptions: DeliveryOptionsDto) =>
      deliveryOptions.assignmentType ===
      OrderOwnDriversAssignmentType.assignNow,
  )
  @IsMongoId()
  driverId?: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: false,
    description:
      'YYYY-MM-DD HH:mm format, UTC timezone. Only retail companies may update the pickup_date.',
  })
  @IsOptional()
  pickup_date?: string;

  @ApiProperty({
    type: String,
    required: false,
    description:
      'YYYY-MM-DD HH:mm format, UTC timezone. Only retail companies may update the delivery_date.',
  })
  @IsOptional()
  delivery_date?: string;
}
