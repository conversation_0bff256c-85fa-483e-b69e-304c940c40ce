import { Module } from '@nestjs/common';
import { SharedModule } from '../../../shared/shared.module';
import { CustomerReadModule } from '../customer-read/customer-read.module';
import { CustomerUserService } from './customer-user.service';
import { CustomerUserServiceInterface } from './customer-user.service.interface';

@Module({
  providers: [
    {
      provide: CustomerUserServiceInterface,
      useClass: CustomerUserService,
    },
  ],
  imports: [SharedModule, CustomerReadModule],
  exports: [CustomerUserServiceInterface],
})
export class CustomerUserModule {}
