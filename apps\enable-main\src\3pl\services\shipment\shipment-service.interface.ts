import {
  AddressDto,
  CreateShipmentDto,
  GetAllShipmentDto,
  GetShipmentTrackingResponseDto,
  IndexResultDto,
  Shipment,
  ShipmentDocument,
  ShipmentStatus,
  UpdateShipmentDto,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface ShipmentServiceInterface {
  findAll(
    getAllShipmentDto: GetAllShipmentDto,
  ): Promise<IndexResultDto<Shipment>[]>;

  create(createShipmentDto: CreateShipmentDto): Promise<ShipmentDocument>;

  findOne(id: Types.ObjectId): Promise<ShipmentDocument>;

  update(updateShipmentDto: UpdateShipmentDto): Promise<ShipmentDocument>;

  updateShipmentsAddress(
    _id: Types.ObjectId[],
    location: AddressDto,
  ): Promise<ShipmentDocument>;

  generateWayBill(idOrTrackingNumber: string): Promise<Buffer>;

  findByIdIn(ids: Types.ObjectId[]): Promise<ShipmentDocument[]>;

  findBySalesOrderId(salesOrderId: string): Promise<ShipmentDocument[]>;

  getTrackingDetails(
    id: Types.ObjectId,
  ): Promise<GetShipmentTrackingResponseDto>;

  findByTrackingNumber(trackingNumber: string): Promise<ShipmentDocument>;

  changeShipmentStatus(
    shipmentRef: string,
    status: ShipmentStatus,
  ): Promise<void>;
}

export const ShipmentServiceInterface = Symbol('ShipmentServiceInterface');
