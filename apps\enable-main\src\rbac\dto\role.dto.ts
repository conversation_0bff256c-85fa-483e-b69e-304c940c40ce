import { IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { DataIndex } from '@app/shared-stuff';

export class RolePrivilege {
  @ApiProperty({
    type: String,
    required: false,
  })
  module: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  title: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  type: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  key: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  parentKey: string;
}

export class RoleToCreate {
  @IsNotEmpty()
  @ApiProperty()
  name: string;

  @IsNotEmpty()
  @ApiProperty()
  description: string;

  @IsNotEmpty()
  @ApiProperty()
  permissions: [string];

  @ApiProperty({
    type: () => [RolePrivilege],
    required: false,
  })
  @Type(() => RolePrivilege)
  privileges: RolePrivilege[];

  company: any;
  company_name: string;
  currentUser: {};
}

export class RoleToUpdate {
  @IsNotEmpty()
  @ApiProperty()
  _id: string;

  @IsNotEmpty()
  @ApiProperty()
  name: string;

  @IsNotEmpty()
  @ApiProperty()
  description: string;

  @IsNotEmpty()
  @ApiProperty()
  permissions: [string];

  @ApiProperty({
    type: [RolePrivilege],
    required: false,
  })
  @Type(() => RolePrivilege)
  privileges: RolePrivilege[];

  company: any;
  currentUser: {};
}

export class RoleToIndex extends DataIndex {
  @ApiProperty({
    required: false,
    type: String,
    enum: ['name', 'date_created', 'all'],
  })
  search_type?: string;

  @ApiProperty({
    required: false,
    type: String,
  })
  name: string;

  @ApiProperty({
    required: false,
    type: String,
    enum: [
      'name_a_z',
      'name_z_a',
      'date_created',
      'date_Created_a_z',
      'date_created_z_a',
    ],
  })
  sort_type?: string;

  @ApiProperty({
    required: false,
    type: String,
    enum: [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
  })
  month?: string;
  isDefault?: boolean;
  company: string;
}

export class RoleExcelToImport {
  @ApiProperty({
    required: true,
    type: String,
  })
  file_path: string;

  company: any;
}
