import {
  CustomerDocument,
  CustomerOrdableInfo,
  CustomerOrdableStores,
  LoggerService,
} from '@app/shared-stuff';
import {
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';

import { Types } from 'mongoose';
import * as randomstring from 'randomstring';
import { CompanyService } from '../../../../../company/services/company/company.service';
import { CustomerCodeServiceInterface } from '../../../../../customer/modules/customer-code/customer-code.service.interface';
import { CustomerReadServiceInterface } from '../../../../../customer/modules/customer-read/customer-read.service.interface';
import { parsePhoneNumber } from '../../../../../shared/services/helper/helper.service';
import { StoreDocument } from '../../../../../store/models/store.model';
import { IntegrationLogRepositoryInterface } from '../../../../integration-log/repositories/interfaces/integration-log.repository.interface';
import { ShortenUrlServiceInterface } from '../../../../shorten-url/services/shorten-url.service.interface';
import { OrdableAddLoyaltyPointsDto } from '../../dtos/customers/add-loyalty-points.dto';
import { CreateOrdableCustomerResponseDto } from '../../dtos/customers/create-ordable-customer-response.dto';
import { CreateOrdableCustomerDto } from '../../dtos/customers/create-ordable-customer.dto';
import { GetCustomerPointsResponseDto } from '../../dtos/customers/get-customer-points-response.dto';
import { OrdableHttpRequestsServiceInterface } from '../ordable-http-requests.service.interface';
import { OrdablePromotionsSyncServiceInterface } from '../promotions-sync/ordable-promotions-sync.service.interface';
import { OrdableStoresServiceInterface } from '../stores/ordable-stores.service.interface';
import { OrdableCustomersServiceInterface } from './ordable-customers.service.interface';

@Injectable()
export class OrdableCustomersService
  implements OrdableCustomersServiceInterface
{
  private readonly loggerService = new LoggerService(
    OrdableCustomersService.name,
  );

  private readonly CUSTOMERS_URI = '/api/users/';
  private readonly POINTS_URI = '/api/add_user_points/';
  constructor(
    @Inject('OrdableHttpRequestsServiceInterface')
    private readonly ordableHttpRequestsService: OrdableHttpRequestsServiceInterface,
    @Inject(OrdableStoresServiceInterface)
    private readonly ordableStoresService: OrdableStoresServiceInterface,
    @Inject('IntegrationLogRepositoryInterface')
    private readonly integrationLogRepository: IntegrationLogRepositoryInterface,
    @Inject(OrdablePromotionsSyncServiceInterface)
    private readonly ordablePromotionsSyncService: OrdablePromotionsSyncServiceInterface,
    @Inject(ShortenUrlServiceInterface)
    private readonly shortenUrlService: ShortenUrlServiceInterface,
    @Inject(CustomerCodeServiceInterface)
    private readonly customerCodeService: CustomerCodeServiceInterface,
    @Inject(CustomerReadServiceInterface)
    private readonly customerReadService: CustomerReadServiceInterface,
    private readonly companyService: CompanyService,
  ) {}

  async initCustomers(store: StoreDocument): Promise<CustomerDocument[]> {
    const customers =
      await this.customerReadService.findSyncableForOrdableStore(store);
    const ordableCustomers = await Promise.all(
      customers.map((customer) => this.initCustomerForStore(customer, store)),
    );
    return ordableCustomers.filter((customer) => customer !== null);
  }

  async initCustomerForStore(
    customer: CustomerDocument,
    store: StoreDocument,
  ): Promise<CustomerDocument | null> {
    try {
      const ordableStores: CustomerOrdableStores = customer.ordableStores || {};
      const ordableInfo = await this.createForStore(store, customer);
      if (ordableInfo) ordableStores[store._id.toHexString()] = ordableInfo;
      await customer.updateOne({ ordableStores });
      return customer;
    } catch (e) {
      this.loggerService.error(
        `Failed to create customer ${customer._id} for store ${store._id}`,
      );
      this.loggerService.error(e.message, e.stack);
      return null;
    }
  }

  async syncLoyaltyPoints(
    customer: CustomerDocument,
  ): Promise<CustomerDocument> {
    if (!customer) return customer;
    customer = await this.create(customer);
    customer = await this.ordableStoresService.forEachStore(
      customer,
      async (store): Promise<CustomerOrdableInfo> =>
        await this.syncLoyaltyPointsForStore(store, customer),
    );

    await this.ordablePromotionsSyncService.syncCustomerCoupons(customer);
    return customer;
  }

  private async syncLoyaltyPointsForStore(
    store: StoreDocument,
    customer: CustomerDocument,
  ): Promise<CustomerOrdableInfo> {
    const ordableInfo: CustomerOrdableInfo =
      customer.ordableStores[store._id.toHexString()];

    const currentLoyaltyPoints = await this.getLoyaltyPoints(
      store,
      ordableInfo,
    );
    if (currentLoyaltyPoints === null) return ordableInfo;

    const pointsToAdd = customer.loyaltyPoints - currentLoyaltyPoints;
    await this.addLoyaltyPoints(store, ordableInfo, pointsToAdd);
    return ordableInfo;
  }

  private async getLoyaltyPoints(
    { apiBaseUrl, apiKey }: StoreDocument,
    ordableInfo: CustomerOrdableInfo,
  ): Promise<number | null> {
    const URL = `${apiBaseUrl}${this.POINTS_URI}${ordableInfo.ordableId}`;
    const response: GetCustomerPointsResponseDto =
      await this.ordableHttpRequestsService.createOrdableGetRequest(
        URL,
        apiKey,
      );

    if (response.success === false) {
      await this.integrationLogRepository.logError(
        '[Ordable] Get Loyalty Points',
        { url: URL },
        response,
      );
      return null;
    }

    await this.integrationLogRepository.logSuccess(
      '[Ordable] Get Loyalty Points',
      { url: URL },
      response,
      ordableInfo.ordableId.toString(),
    );
    return response.data.user_balance;
  }

  private async addLoyaltyPoints(
    { apiBaseUrl, apiKey }: StoreDocument,
    ordableInfo: CustomerOrdableInfo,
    amount: number,
  ): Promise<CustomerOrdableInfo> {
    const addLoyaltyPointsDto: OrdableAddLoyaltyPointsDto = {
      user_id: ordableInfo.ordableId,
      amount,
    };

    const response: CreateOrdableCustomerResponseDto =
      await this.ordableHttpRequestsService.createOrdablePostRequest(
        `${apiBaseUrl}${this.POINTS_URI}`,
        apiKey,
        addLoyaltyPointsDto,
      );

    if (response.success === false) {
      await this.integrationLogRepository.logError(
        '[Ordable] Add Loyalty Points',
        addLoyaltyPointsDto,
        response,
      );
    } else {
      await this.integrationLogRepository.logSuccess(
        '[Ordable] Add Loyalty Points',
        addLoyaltyPointsDto,
        response,
        ordableInfo.ordableId.toString(),
      );
    }

    return ordableInfo;
  }

  async regenerateOrdableLink(
    customer: CustomerDocument,
  ): Promise<CustomerDocument> {
    customer = await this.create(customer);
    await this.ordablePromotionsSyncService.syncCustomerCoupons(customer);
    await this.ordablePromotionsSyncService.syncCustomerTier(customer);
    await this.ordablePromotionsSyncService.syncCustomerRewards(customer);
    return customer;
  }

  async create(customer: CustomerDocument): Promise<CustomerDocument> {
    return await this.ordableStoresService.forEachStore(
      customer,
      async (store: StoreDocument): Promise<CustomerOrdableInfo> =>
        await this.createForStore(store, customer),
    );
  }

  private async createForStore(
    store: StoreDocument,
    customer: CustomerDocument,
  ): Promise<CustomerOrdableInfo> {
    if (customer.ordableStores?.[store._id.toHexString()]?.ordableId) {
      this.loggerService.warn(
        `Customer ${customer._id} already created for store ${store._id}`,
      );
      return customer.ordableStores[store._id.toHexString()];
    }

    const URL = store.apiBaseUrl + this.CUSTOMERS_URI;
    const API_KEY = store.apiKey;
    const createOrdableCustomerDto =
      await this.toCreateOrdableCustomerDto(customer);

    const response: CreateOrdableCustomerResponseDto =
      await this.ordableHttpRequestsService.createOrdablePostRequest(
        URL,
        API_KEY,
        createOrdableCustomerDto,
      );

    await this.logCreateCustomerResponse(
      store,
      createOrdableCustomerDto,
      response,
      customer._id,
    );

    if (response.success == false)
      throw new InternalServerErrorException(response.message);

    const customerOrdableInfo = {
      ordableId: response.data.user_id,
      ordableLink: response.data.login_url,
      ordableLinkRaw: response.data.login_url,
      ordableBrands: store.brands,
    };

    return await this.shortenOrdableLink(
      store,
      customerOrdableInfo,
      customer.company,
      await this.customerCodeService.getOrGenerateCustomerShortCode(customer),
    );
  }

  async shortenOrdableLink(
    store: StoreDocument,
    ordableInfo: CustomerOrdableInfo,
    companyId: Types.ObjectId,
    customerCode: string,
  ): Promise<CustomerOrdableInfo> {
    if (!ordableInfo?.ordableLinkRaw) return ordableInfo;

    const brandName =
      store.brands.length === 1
        ? store.brands[0].name
        : await this.getCompanyName(companyId);

    const shortenedLink = await this.shortenUrlService.shortenUrl({
      url: ordableInfo.ordableLinkRaw,
      code: `${brandName.replaceAll(/\s/g, '')}-${customerCode}`,
      canExpire: false,
    });

    return {
      ...ordableInfo,
      ordableLink: shortenedLink,
    };
  }

  private async getCompanyName(companyId: Types.ObjectId): Promise<string> {
    const company = await this.companyService.findById(companyId);
    return company.name;
  }

  private async logCreateCustomerResponse(
    store: StoreDocument,
    createOrdableCustomerDto: CreateOrdableCustomerDto,
    response: CreateOrdableCustomerResponseDto,
    customerId: Types.ObjectId,
  ): Promise<void> {
    const requestBody = {
      storeId: store._id,
      storeUrl: store.apiBaseUrl,
      customerId,
      payload: createOrdableCustomerDto,
    };
    if (response.success === true) {
      await this.integrationLogRepository.logSuccess(
        '[Ordable] Create Customer',
        requestBody,
        response,
        customerId.toHexString(),
      );
    } else {
      this.loggerService.error(
        `Create Ordable Customer failed: ${response.message}`,
        { requestBody, response },
      );
      await this.integrationLogRepository.logError(
        '[Ordable] Create Customer',
        requestBody,
        response,
      );
    }
  }

  private async toCreateOrdableCustomerDto(
    customer: CustomerDocument,
  ): Promise<CreateOrdableCustomerDto> {
    const phone = parsePhoneNumber(customer.phone, customer.country_code);
    // We keep getting "This email is already registered" from Ordable,
    // so we're sending a random email instead.
    // const email = customer.email || `${phone.replace('+', '')}@enable.tech`;
    const email = `placeholder${randomstring.generate({
      length: 10,
      charset: 'numeric',
    })}@enable.tech`;

    return {
      phone,
      email,
      first_name: customer.first_name,
      last_name: customer.last_name,
    };
  }
}
