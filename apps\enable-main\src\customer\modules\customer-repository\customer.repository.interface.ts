import {
  BenefitDocument,
  CouponDocument,
  Customer,
  CustomerDocument,
  CustomerEarnedBenefit,
  CustomerUsedBenefit,
  EarnedReward,
  EmbeddedBrandDto,
  EmbeddedTierDto,
  FindByPhoneAndCompanyIdOptions,
  IGenericRepository,
  LoyaltyTierDocument,
  MessageCustomerDto,
  PunchCardDocument,
  RegisteredPass,
  TierStatus,
  UsedReward,
} from '@app/shared-stuff';

import { FilterQuery, Types, UpdateQuery } from 'mongoose';
import { StoreDocument } from '../../../store/models/store.model';

export interface CustomerRepositoryInterface
  extends IGenericRepository<CustomerDocument, Customer> {
  findEligibleForTierComputation(
    companyIds: Types.ObjectId[],
    tierUpdatedBefore?: Date,
    tierUpdatedAfter?: Date,
    tierStatus?: TierStatus,
  ): Promise<CustomerDocument[]>;
  findRecentlyComputed(companyId: Types.ObjectId): Promise<CustomerDocument[]>;
  findEligibleForGracePeriodReminder(
    companyIds: Types.ObjectId[],
    registrationDay: Date,
  ): Promise<CustomerDocument[]>;
  findEligibleForTierDiscount(
    loyaltyTier: LoyaltyTierDocument,
  ): Promise<CustomerDocument[]>;
  findEligibleForCoupon(coupon: CouponDocument): Promise<CustomerDocument[]>;
  findByRegisteredPasses(
    registeredPass: Partial<RegisteredPass>,
    updatedSince?: Date,
  ): Promise<CustomerDocument[]>;
  removeLatestPayment(id: Types.ObjectId): Promise<void>;
  findByCompanyId(
    companyId: Types.ObjectId | Types.ObjectId[],
    hasPasses?: boolean,
  ): Promise<CustomerDocument[]>;
  findOne(filterObj: FilterQuery<CustomerDocument>): Promise<CustomerDocument>;
  insertMany(customers: Customer[]): Promise<void>;
  findByPhoneOrEmail(
    customerPhone: string,
    customerEmail: string,
    company: Types.ObjectId,
  ): Promise<CustomerDocument | null>;
  findByPhoneAndCompanyId(
    phone: string,
    companyId: Types.ObjectId,
    options?: FindByPhoneAndCompanyIdOptions,
  ): Promise<CustomerDocument>;
  populateField(
    customers: CustomerDocument[],
    field: string,
  ): Promise<CustomerDocument[]>;
  updateOne(
    filter: FilterQuery<CustomerDocument>,
    updateQuery: UpdateQuery<CustomerDocument>,
  ): Promise<void>;
  findManyById(customerIds: Types.ObjectId[]): Promise<CustomerDocument[]>;
  findByLoyaltyTier(
    loyaltyTierIds: Types.ObjectId[],
    companyId?: Types.ObjectId,
    hasPasses?: boolean,
  ): Promise<CustomerDocument[]>;
  findByBrandId(
    brandId: Types.ObjectId,
    hasPasses: boolean,
  ): Promise<CustomerDocument[]>;
  syncBrandChanges(brand: EmbeddedBrandDto): Promise<void>;
  updateEmbeddedTier(embeddedTierDto: EmbeddedTierDto): Promise<void>;
  updateEmbeddedStore(store: StoreDocument): Promise<void>;
  findSyncableForOrdableStore(
    store: StoreDocument,
  ): Promise<CustomerDocument[]>;
  addRewardsToCustomer(
    customer: CustomerDocument,
    rewards: EarnedReward[],
  ): Promise<CustomerDocument>;
  redeemRewards(
    customer: CustomerDocument,
    rewards: EarnedReward[],
  ): Promise<void>;
  updateRewards(
    customerId: Types.ObjectId,
    rewards: EarnedReward[] | UsedReward[],
  ): Promise<void>;
  findByPunchCardId(punchCardId: Types.ObjectId): Promise<CustomerDocument[]>;
  findByPunchCardProgress(
    punchCardId: Types.ObjectId,
    minimumCount: number,
  ): Promise<CustomerDocument[]>;
  findByEmail(
    company: Types.ObjectId,
    email: string,
  ): Promise<CustomerDocument | null>;
  updateRegisteredPassTimestamp(registeredPass: RegisteredPass): Promise<void>;
  handlePunchCardCompletionLowered(
    punchCard: PunchCardDocument,
    newCompletionPoint: number,
  ): Promise<void>;
  resetCarryOver(companyId: Types.ObjectId): Promise<void>;
  isShortCodeTaken(
    company: Types.ObjectId,
    shortCode: string,
  ): Promise<boolean>;
  updateCustomerEarnedBenefits(benefit: BenefitDocument): Promise<void>;
  updateBenefits(
    customerId: Types.ObjectId,
    benefits: CustomerEarnedBenefit[] | CustomerUsedBenefit[],
    benefitField: 'earnedBenefits' | 'usedBenefits',
  ): Promise<void>;
  updateCustomerUsageByTier(
    loyaltyTierId: Types.ObjectId,
    usesField:
      | 'freeDeliveryRemainingNumberOfUses'
      | 'percentDiscountRemainingNumberOfUses',
    uses: number,
  ): Promise<void>;
  addRemainingUsesByTier(
    loyaltyTierId: Types.ObjectId,
    usesField:
      | 'freeDeliveryRemainingNumberOfUses'
      | 'percentDiscountRemainingNumberOfUses',
    additionalUses: number,
    maximumUses: number,
  ): Promise<void>;
  messageCustomer(
    messageCustomerDto: MessageCustomerDto,
  ): Promise<CustomerDocument>;
  getCompanyIdForCustomerId(
    customerId: Types.ObjectId,
  ): Promise<Types.ObjectId>;
}

export const CustomerRepositoryInterface = Symbol(
  'CustomerRepositoryInterface',
);
