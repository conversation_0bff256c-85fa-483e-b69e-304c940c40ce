// EBL-3553 - [Loyalty Program] Promote LRP
// Create Send Loyalty Registration trigger and global template
db.triggers.insertOne({
  name: '[CUSTOMER] ON_SEND_LOYALTY_CARD',
  client: 'ENABLE_MAIN',
  action: 'ON_SEND_LOYALTY_CARD',
  module: 'CUSTOMER',
  replacement: ['walletPassLink'],
  createdAt: new Date(),
  updatedAt: new Date(),
});

db.templates.insertOne({
  name: 'Global Promote Loyalty Card Status Template',
  content: {
    // Still Waiting for the Verified Content
    enContent: 'Your Card Status is $walletPassLink',
    arContent: 'Your Card Status is $walletPassLink',
  },
  to: 'CUSTOMER',
  from: 'COMPANY_SENDER',
  type: 'SMS',
  trigger: db.triggers.findOne({
    name: '[CUSTOMER] ON_SEND_LOYALTY_CARD',
  }),
  createdAt: new Date(),
  updatedAt: new Date(),
});
