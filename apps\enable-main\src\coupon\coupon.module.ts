import { CollectionName, CouponSchema } from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CompanyModule } from '../company/company.module';
import { SharedModule } from '../shared/shared.module';

import { CouponController } from './controllers/coupon.controller';
import { CouponRepository } from './repositories/coupon.repository';
import { CouponRepositoryInterface } from './repositories/coupon.repository.interface';
import { CouponService } from './services/coupon.service';
import { CouponServiceInterface } from './services/coupon.service.interface';
import { BenefitUtilityModule } from '../benefit/benefit-utility/benefit-utility.module';

@Module({
  controllers: [CouponController],
  providers: [
    { provide: CouponServiceInterface, useClass: CouponService },
    { provide: CouponRepositoryInterface, useClass: CouponRepository },
  ],
  imports: [
    SharedModule,
    BenefitUtilityModule,
    MongooseModule.forFeature([
      { name: CollectionName.COUPON, schema: CouponSchema },
    ]),
    CompanyModule,
  ],
  exports: [CouponServiceInterface],
})
export class CouponModule {}
