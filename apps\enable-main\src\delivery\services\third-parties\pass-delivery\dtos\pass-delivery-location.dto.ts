import { IsPhoneNumberForRegion } from '@app/shared-stuff';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  MaxLength,
  Min,
} from 'class-validator';

export class PassDeliveryLocationDto {
  @IsNotEmpty()
  @IsNumber()
  @Min(-90)
  @Max(90)
  lat: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(-180)
  @Max(180)
  long: number;

  @IsNotEmpty()
  @IsString()
  @MaxLength(100)
  name: string;

  @IsNotEmpty()
  @IsPhoneNumberForRegion('+974')
  phone: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(150)
  address: string;

  @IsOptional()
  @IsString()
  @MaxLength(150)
  description?: string;
}
