import {
  forEachAsync,
  GroupedPasses,
  LoggerService,
  RegisteredPass,
  WalletApp,
} from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs/promises';
import * as apn from 'node-apn';
import { catchError, firstValueFrom, of } from 'rxjs';
import { IntegrationLogRepositoryInterface } from '../../../integration/integration-log/repositories/interfaces/integration-log.repository.interface';
import { WalletPassesPushRequestDto } from '../../dto/wallet-passes-push-request.dto';
import { PassTypeIdentifierConfigRepository } from '../../repositories/pass-type-identifier-config.repository';
import { GooglePassesServiceInterface } from '../google-passes/google-passes.service.interface';

@Injectable()
export class PassesRefreshService {
  private readonly loggerService = new LoggerService(PassesRefreshService.name);

  private readonly WALLET_PASSES_PUSH_API_URL =
    'https://walletpasses.appspot.com/api/v1/push';

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    @Inject('IntegrationLogRepositoryInterface')
    private readonly integrationLogRepository: IntegrationLogRepositoryInterface,
    @Inject(GooglePassesServiceInterface)
    private readonly googlePassesService: GooglePassesServiceInterface,
    private readonly passTypeIdentifierConfigRepository: PassTypeIdentifierConfigRepository,
  ) {}

  async updatePasses(passes: RegisteredPass[]) {
    if (!passes || passes.length === 0) return;

    const isPassKept = (pass: RegisteredPass) => !pass.deletedAt;
    const keptPasses = passes.filter(isPassKept);

    const applePasses = keptPasses.filter(
      (pass) => pass.walletApp === WalletApp.APPLE_WALLET,
    );
    if (applePasses.length > 0) {
      const groupedApplePasses = this.groupByPassTypeIdentifiers(applePasses);
      await forEachAsync(
        groupedApplePasses,
        async ({ passTypeIdentifier, passes }) => {
          const response = await this.notifyAppleWallet(
            passTypeIdentifier,
            passes.map((pass) => pass.pushToken),
          );

          await this.integrationLogRepository.logSuccess(
            'apple-wallet-passes-update',
            { passTypeIdentifier, passes },
            response,
          );
        },
      );
    }

    const androidPasses = keptPasses.filter(
      (pass) => pass.walletApp === WalletApp.WALLET_PASSES,
    );
    const groupedAndroidPasses = this.groupByPassTypeIdentifiers(androidPasses);
    const pushRequestDtos = groupedAndroidPasses.map((group) => ({
      passTypeIdentifier: group.passTypeIdentifier,
      pushTokens: group.passes.map((pass) => pass.pushToken),
    }));
    await Promise.all(
      pushRequestDtos.map(async (pushRequestDto) => {
        const response = await this.notifyWalletPasses(pushRequestDto);
        await this.integrationLogRepository.logSuccess(
          'wallet-passes-update',
          { pushRequestDto },
          response,
        );
      }),
    );

    const googlePasses = keptPasses.filter(
      (pass) => pass.walletApp === WalletApp.GOOGLE_WALLET,
    );
    await this.googlePassesService.updatePasses(googlePasses);
  }

  private groupByPassTypeIdentifiers(
    passes: RegisteredPass[],
  ): GroupedPasses[] {
    const groupedPasses: GroupedPasses[] = [];

    passes.forEach((pass) => {
      const passGroup = groupedPasses.find(
        (group) => group.passTypeIdentifier === pass.passTypeIdentifier,
      );

      if (passGroup) passGroup.passes.push(pass);
      else
        groupedPasses.push({
          passTypeIdentifier: pass.passTypeIdentifier,
          passes: [pass],
        });
    });

    return groupedPasses;
  }

  private async initApnProvider(
    passTypeIdentifier: string,
  ): Promise<apn.Provider> {
    const passTypeIdentifierConfig =
      await this.passTypeIdentifierConfigRepository.findByPassTypeIdentifier(
        passTypeIdentifier,
      );
    return new apn.Provider({
      cert: await fs.readFile(passTypeIdentifierConfig.passCertFile),
      key: await fs.readFile(passTypeIdentifierConfig.passKeyFile),
      passphrase: this.configService.get('APPLE_WALLET_PASSPHRASE'),
      production: true,
    });
  }

  private async notifyAppleWallet(
    passTypeIdentifier: string,
    pushTokens: string[],
  ): Promise<apn.Responses> {
    const apnProvider = await this.initApnProvider(passTypeIdentifier);
    const responses = await apnProvider.send(
      new apn.Notification(),
      pushTokens,
    );

    const failedResponses = responses.failed.length;
    if (failedResponses > 0) {
      this.loggerService.error(
        `Error while sending APN to ${failedResponses} out of ${pushTokens.length} devices`,
        { responses, pushTokens },
      );
    }
    return responses;
  }

  private async notifyWalletPasses(
    walletPassesPushRequestDto: WalletPassesPushRequestDto,
  ): Promise<any> {
    const apiKey = this.configService.get('WALLET_PASSES_API_KEY');
    const response = await firstValueFrom(
      this.httpService
        .post(this.WALLET_PASSES_PUSH_API_URL, walletPassesPushRequestDto, {
          headers: {
            Authorization: apiKey,
            'Content-Type': 'application/json',
          },
        })
        .pipe(
          catchError((err) => {
            this.loggerService.error(err);
            return of(err);
          }),
        ),
    );

    if (response.status !== 200) {
      this.loggerService.error(`Error while pushing wallet passes`, {
        walletPassesPushRequestDto,
        response,
      });
    }

    return response.data;
  }
}
