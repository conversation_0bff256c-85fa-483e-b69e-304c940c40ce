import {
  Achievement,
  CalendarCycle,
  CallCenterWebhookRepositoryInterface,
  CompanyDocument,
  CountryDialCode,
  CustomerDocument,
  IndexCustomerDto,
  Language,
  LanguageToLanguageCode,
  LookupCallCenterCustomerDto,
  LoyaltyCustomer,
  LoyaltyPointLogAction,
  LoyaltyPointLogDocument,
  LoyaltyStatus,
  LoyaltyTierDocument,
  LRPSource,
  mapAsync,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { CompanyService } from '../../../company/services/company/company.service';
import { ShortenUrlServiceInterface } from '../../../integration/shorten-url/services/shorten-url.service.interface';
import { LoyaltyPointLogServiceInterface } from '../../../loyalty-point-log/services/loyalty-point-log.service.interface';
import { LoyaltyTierReadServiceInterface } from '../../../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.service.interface';
import { PunchCardAchievementService } from '../../../punch-card/modules/punch-card-achievement/punch-card-achievement.service';
import {
  formatText,
  HelperService,
} from '../../../shared/services/helper/helper.service';
import { CustomerIndexServiceInterface } from '../customer-index/customer-index.service.interface';
import { CustomerPunchCardServiceInterface } from '../customer-punch-card/customer-punch-card.service.interface';
import { CustomerReadServiceInterface } from '../customer-read/customer-read.service.interface';
import { CustomerTierInfoServiceInterface } from '../customer-tier-info/customer-tier-info.service.interface';
import { CustomerTokenServiceInterface } from '../customer-token/customer-token.service.interface';
import { CustomerLoyaltyServiceInterface } from './customer-loyalty.service.interface';

@Injectable()
export class CustomerLoyaltyService implements CustomerLoyaltyServiceInterface {
  constructor(
    private readonly configService: ConfigService,
    private readonly helperService: HelperService,
    private readonly companyService: CompanyService,
    private readonly punchCardAchievementService: PunchCardAchievementService,
    @Inject(LoyaltyPointLogServiceInterface)
    private readonly loyaltyPointLogService: LoyaltyPointLogServiceInterface,
    @Inject(LoyaltyTierReadServiceInterface)
    private readonly loyaltyTierService: LoyaltyTierReadServiceInterface,
    @Inject(CustomerReadServiceInterface)
    private readonly customerReadService: CustomerReadServiceInterface,
    @Inject(CustomerTierInfoServiceInterface)
    private readonly customerLoyaltyTierInfoService: CustomerTierInfoServiceInterface,
    @Inject(CustomerTokenServiceInterface)
    private readonly customerTokenService: CustomerTokenServiceInterface,
    @Inject(CustomerPunchCardServiceInterface)
    private readonly customerPunchCardService: CustomerPunchCardServiceInterface,
    @Inject(ShortenUrlServiceInterface)
    private shortenUrlService: ShortenUrlServiceInterface,
    @Inject(CustomerIndexServiceInterface)
    private readonly customerIndexService: CustomerIndexServiceInterface,
    @Inject(CallCenterWebhookRepositoryInterface)
    private readonly callCenterWebhookRepository: CallCenterWebhookRepositoryInterface,
  ) {}

  public async getCustomerDetailsForFrontendByAuthToken(
    authHeader: string,
  ): Promise<CustomerDocument | LoyaltyCustomer> {
    const { customerId, companyId } =
      this.helperService.parseCustomerTokenPayload(authHeader);

    return await this.getCustomerDetailsForFrontend(
      customerId.toHexString(),
      companyId,
    );
  }

  async getCustomerDetailsForFrontend(
    uniqueIdentifier: string,
    companyId: Types.ObjectId,
    countryCode?: CountryDialCode,
  ): Promise<CustomerDocument | LoyaltyCustomer> {
    const customer = await this.customerReadService.findOne(
      uniqueIdentifier,
      companyId,
      countryCode,
    );
    const company = await this.companyService.findById(customer.company);

    if (company.hasLoyaltyProgram) {
      return await this.getCustomerWithLoyalty(customer, company);
    }
    return customer;
  }

  async getSimplifiedCustomerDetails(
    phoneNumber: string,
    companyId: string,
    countryCode?: CountryDialCode,
  ): Promise<CustomerDocument> {
    return await this.customerReadService.findByPhoneAndCompanyId(
      phoneNumber,
      new Types.ObjectId(companyId),
      countryCode,
      {
        first_name: 1,
        last_name: 1,
        phone: 1,
        country_code: 1,
        email: 1,
        title: 1,
        birthDate: 1,
      },
    );
  }

  async lookupCallCenterCustomer({
    companyId,
    phoneNumber,
    countryCode,
  }: LookupCallCenterCustomerDto): Promise<CustomerDocument | LoyaltyCustomer> {
    const company = await this.companyService.findById(companyId);
    const { hardPhoneNumbers } = company;

    if (hardPhoneNumbers.includes(phoneNumber)) {
      const bufferedWebhook =
        await this.callCenterWebhookRepository.findCalleeByPhoneNumber(
          phoneNumber,
        );

      if (bufferedWebhook) phoneNumber = bufferedWebhook.callerNumber;
    }

    return await this.getCustomerDetailsForFrontend(
      phoneNumber,
      company._id,
      countryCode,
    );
  }

  async getCustomerWithLoyalty(
    customer: CustomerDocument,
    company: CompanyDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<LoyaltyCustomer> {
    const currentTier = customer.loyaltyTier
      ? await this.loyaltyTierService.findById(customer.loyaltyTier?._id)
      : null;

    const nextTier = await this.loyaltyTierService.findNextTier(
      customer.company,
      currentTier?.tierIndex,
    );

    const loyaltyPointLogs =
      await this.loyaltyPointLogService.getLogsForCustomer(customer._id);

    const orderRate = this.computeOrderRateFromLogs(loyaltyPointLogs);
    const loyaltyOrderRate =
      await this.customerLoyaltyTierInfoService.getTotalLoyaltyOrderRate(
        customer,
        calendarCycle,
      );
    const remainingOrdersCurrentTier =
      await this.customerLoyaltyTierInfoService.getRemainingOrdersCurrentTier(
        customer,
        loyaltyOrderRate,
      );
    const remainingOrdersUpperTier =
      await this.customerLoyaltyTierInfoService.getRemainingOrdersUpperTier(
        customer,
        nextTier,
        loyaltyOrderRate,
      );
    const amountSpent =
      await this.customerLoyaltyTierInfoService.getTotalLoyaltyAmountSpent(
        customer,
        calendarCycle,
      );
    const remainingAmountSpentCurrentTier =
      this.customerLoyaltyTierInfoService.computeRemainingAmount(
        customer.loyaltyTier,
        amountSpent,
      );
    const remainingAmountSpentUpperTier =
      await this.customerLoyaltyTierInfoService.getRemainingAmountSpentUpperTier(
        customer,
        nextTier,
        amountSpent,
      );

    const populatedPunchCardProgress = (customer.punchCardProgress || []).map(
      (progress) => {
        const upcomingAchievements = progress.punchCard.achievements.filter(
          (achievement) =>
            achievement?.requirement?.targetValue > progress.count,
        );

        const nextAchievement = upcomingAchievements?.length
          ? upcomingAchievements.reduce(
              (nextAchievement, currentAchievement) =>
                currentAchievement?.requirement?.targetValue <
                nextAchievement?.requirement?.targetValue
                  ? currentAchievement
                  : nextAchievement,
            )
          : null;

        const latestAchievement = progress.punchCard.achievements?.length
          ? progress.punchCard.achievements.reduceRight(
              (nextAchievement, currentAchievement) =>
                currentAchievement?.requirement?.targetValue >
                nextAchievement?.requirement?.targetValue
                  ? currentAchievement
                  : nextAchievement,
            )
          : null;

        const formatAchievement = (achievement: Achievement) => {
          if (!achievement) return null;
          return {
            ...achievement,
            formattedReward:
              this.punchCardAchievementService.formatAchievementReward(
                achievement.reward,
              ),
          };
        };

        return {
          ...progress,
          nextAchievement: formatAchievement(nextAchievement),
          latestAchievement: formatAchievement(latestAchievement),
        };
      },
    );

    return {
      ...customer.toObject(),
      loyaltyTier: customer.loyaltyTier
        ? {
            ...customer.loyaltyTier,
            benefits: currentTier ? currentTier.benefits : [],
          }
        : null,
      loyaltyPointLogs,
      orderRate,
      loyaltyOrderRate,
      remainingOrdersCurrentTier,
      remainingOrdersUpperTier,
      amountSpent,
      remainingAmountSpentCurrentTier,
      remainingAmountSpentUpperTier,
      remainingRequirementsCurrentTier: await this.getCurrentTierRequirements(
        customer,
        company,
      ),
      remainingRequirementsUpperTier: await this.getUpperTierRequirements(
        customer,
        nextTier,
        company,
      ),
      remainingStampsNextReward:
        await this.customerPunchCardService.getRemainingStampsNextReward(
          customer,
        ),
      punchCardProgress: populatedPunchCardProgress,
      remainingStampsNextRewards: populatedPunchCardProgress.map(
        (progress) =>
          `${progress.nextAchievement ? progress.nextAchievement?.requirement?.targetValue - progress.count : 0} ${
            customer.language === Language.arabic
              ? progress.punchCard.nameAr
              : progress.punchCard.nameEn
          }`,
      ),
      nextTier,
      pointsRate:
        await this.customerLoyaltyTierInfoService.getTotalLoyaltyPointsRate(
          customer,
          calendarCycle,
        ),
    };
  }

  private computeOrderRateFromLogs(
    loyaltyPointLogs: LoyaltyPointLogDocument[],
  ): number {
    if (!loyaltyPointLogs || loyaltyPointLogs.length === 0) return 0;

    const completedOrders = loyaltyPointLogs.filter(
      (log) => log.action === LoyaltyPointLogAction.ON_ORDER_COMPLETED,
    );

    const orderRatePeriodInDays = 30;
    const orderRatePeriodInMs = orderRatePeriodInDays * 24 * 60 * 60 * 1000;
    const recentOrders = completedOrders.filter(
      (log) => Date.now() - log.createdAt.valueOf() < orderRatePeriodInMs,
    );

    return recentOrders.length;
  }

  private async getCurrentTierRequirements(
    customer: CustomerDocument,
    company: CompanyDocument,
  ): Promise<string> {
    if (!customer.loyaltyTier) return 'No tier assigned';

    const validTill =
      await this.customerLoyaltyTierInfoService.getTierValidTill(
        customer,
        company,
      );
    const validTillString = moment.utc(validTill).format('DD/MM/YYYY');

    const hasMaintainedTier =
      await this.customerLoyaltyTierInfoService.hasCustomerMaintainedTier(
        customer,
      );
    if (hasMaintainedTier) return `Maintained till ${validTillString}`;

    const hasOrders = Boolean(customer.loyaltyTier?.orderRateThreshold);
    const hasAmount = Boolean(customer.loyaltyTier?.amountSpentThreshold);
    const hasPoints = Boolean(customer.loyaltyTier?.pointsRateThreshold);

    if (hasPoints) {
      const pointsRemaining =
        this.customerLoyaltyTierInfoService.computeRemainingPoints(
          customer.loyaltyTier,
          await this.customerLoyaltyTierInfoService.getTotalLoyaltyPointsRate(
            customer,
          ),
        );

      return `Maintain by earning ${+pointsRemaining.toFixed(2)} more ${company.loyaltyProgramConfig.calendarPointsTitleEn} by ${validTillString}`;
    }

    const ordersRemaining =
      this.customerLoyaltyTierInfoService.computeRemainingOrders(
        customer.loyaltyTier,
        await this.customerLoyaltyTierInfoService.getTotalLoyaltyOrderRate(
          customer,
        ),
      );

    if (hasOrders && !hasAmount)
      return `Maintain by making ${ordersRemaining} more orders by ${validTillString}`;

    const amountRemaining =
      this.customerLoyaltyTierInfoService.computeRemainingAmount(
        customer.loyaltyTier,
        await this.customerLoyaltyTierInfoService.getTotalLoyaltyAmountSpent(
          customer,
        ),
      );

    if (!hasOrders && hasAmount)
      return `Maintain by spending ${+amountRemaining.toFixed(2)} ${
        company.localization?.currency ?? 'QAR'
      } by ${validTillString}`;

    return `Maintain by making ${ordersRemaining} more orders or spending ${+amountRemaining.toFixed(2)} ${
      company.localization?.currency ?? 'QAR'
    } by ${validTillString}`;
  }

  private async getUpperTierRequirements(
    customer: CustomerDocument,
    loyaltyTier: LoyaltyTierDocument,
    company: CompanyDocument,
  ): Promise<string> {
    if (!loyaltyTier) return 'Highest Tier Achieved';

    const tierName = formatText(loyaltyTier.nameEn);
    const hasOrders = Boolean(loyaltyTier?.orderRateThreshold);
    const hasAmount = Boolean(loyaltyTier?.amountSpentThreshold);
    const hasPoints = Boolean(loyaltyTier?.pointsRateThreshold);

    if (hasPoints) {
      const pointsRemaining =
        this.customerLoyaltyTierInfoService.computeRemainingPoints(
          loyaltyTier,
          await this.customerLoyaltyTierInfoService.getTotalLoyaltyPointsRate(
            customer,
          ),
        );

      return `${+pointsRemaining.toFixed(2)} ${company.loyaltyProgramConfig.calendarPointsTitleEn} to ${tierName} Tier`;
    }

    const ordersRemaining =
      this.customerLoyaltyTierInfoService.computeRemainingOrders(
        loyaltyTier,
        await this.customerLoyaltyTierInfoService.getTotalLoyaltyOrderRate(
          customer,
        ),
      );

    if (hasOrders && !hasAmount)
      return `${ordersRemaining} orders to ${tierName} Tier`;

    const amountRemaining =
      this.customerLoyaltyTierInfoService.computeRemainingAmount(
        loyaltyTier,
        await this.customerLoyaltyTierInfoService.getTotalLoyaltyAmountSpent(
          customer,
        ),
      );

    if (!hasOrders && hasAmount)
      return `${+amountRemaining.toFixed(2)}${
        company.localization.currency ?? 'QAR'
      } to ${tierName} Tier`;
    return `${ordersRemaining} orders or ${+amountRemaining.toFixed(2)} ${
      company.localization.currency ?? 'QAR'
    } to ${tierName} Tier`;
  }

  async getLoyaltyRegistrationPageLink(
    brandId: Types.ObjectId,
    company?: CompanyDocument,
    customer?: CustomerDocument,
    source?: LRPSource,
    branchId?: Types.ObjectId,
  ): Promise<string> {
    if (!brandId) return '';

    let url: string;
    const loyaltyPageBaseUrl = this.configService.get<string>(
      'LOYALTY_PAGE_BASE_URL',
    );
    if (!customer || customer.loyaltyStatus !== LoyaltyStatus.MEMBER)
      url = `${loyaltyPageBaseUrl}${brandId}?source=${source}`;
    else {
      const lang =
        LanguageToLanguageCode[customer.language ?? Language.english];
      const token = this.customerTokenService.generateCustomerToken({
        companyId: customer.company,
        customerId: customer._id,
        brandId,
      });

      url = `${loyaltyPageBaseUrl}${brandId}?lang=${lang}&token=${token}&source=${source}`;
    }

    if (branchId) url += `&branchId=${branchId}`;

    return this.shortenUrlService.shortenUrl({
      url,
      canExpire: false,
      partialCode: 'loyalty-registration',
    });
  }

  async indexLoyaltyCustomers(
    indexCustomerDto: IndexCustomerDto,
  ): Promise<unknown[]> {
    const customers = await this.customerIndexService.index(indexCustomerDto);
    if (indexCustomerDto.populateLoyaltyInfo)
      customers[0]['paginatedResult'] = await this.populateLoyaltyInfo(
        customers[0]['paginatedResult'],
      );
    return customers;
  }

  private async populateLoyaltyInfo(customers: CustomerDocument[]) {
    return await mapAsync(customers, async (customer) => {
      const loyaltyTier = customer.loyaltyTier
        ? await this.loyaltyTierService.findById(customer.loyaltyTier._id)
        : null;
      const nextTier = await this.loyaltyTierService.findNextTier(
        customer.company,
        loyaltyTier?.tierIndex,
      );
      return {
        ...customer,
        nextTier,
        remainingRequirements:
          await this.customerLoyaltyTierInfoService.calculateRemainingRequirements(
            customer,
            nextTier,
          ),
      };
    });
  }
}
