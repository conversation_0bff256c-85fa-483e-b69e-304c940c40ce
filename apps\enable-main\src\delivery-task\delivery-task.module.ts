import {
  CollectionName,
  SharedStuffModule,
  createOrderSchemaWithHooks,
} from '@app/shared-stuff';
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { MongooseModule } from '@nestjs/mongoose';
import { IntegrationLogModule } from '../integration/integration-log/integration-log.module';
import { LocationModule } from '../location/location.module';
import { BranchModule } from './../branch/branch.module';
import { CompanyModule } from './../company/company.module';
import { DeliveryModule } from './../delivery/delivery.module';
import { SharedModule } from './../shared/shared.module';
import { DeliveryPartyController } from './controllers/delivery-party/delivery-party.controller';
import { DeliveryTaskController } from './controllers/delivery-task/delivery-task.controller';
import { DeliveryPartySchema } from './models/delivery-party.model';
import { DeliveryTaskLogSchema } from './models/delivery-task-log.model';
import { DeliveryTaskSchema } from './models/delivery-task.model';
import { DeliveryPartyService } from './services/delivery-party/delivery-party.service';
import { DeliveryTaskService } from './services/delivery-task/delivery-task.service';

@Module({
  imports: [
    BranchModule,
    SharedStuffModule,
    DeliveryModule,
    CompanyModule,
    SharedModule,
    DeliveryModule,
    LocationModule,
    HttpModule,
    IntegrationLogModule,

    MongooseModule.forFeatureAsync([
      { name: 'DeliveryTask', useFactory: () => DeliveryTaskSchema },
      { name: 'DeliveryParty', useFactory: () => DeliveryPartySchema },
      { name: 'DeliveryTaskLog', useFactory: () => DeliveryTaskLogSchema },
      {
        name: CollectionName.ORDER,
        imports: [],
        useFactory: (eventEmitter: EventEmitter2) => {
          return createOrderSchemaWithHooks(eventEmitter);
        },
        inject: [EventEmitter2],
      },
    ]),
  ],
  controllers: [DeliveryTaskController, DeliveryPartyController],
  providers: [DeliveryTaskService, DeliveryPartyService],
  exports: [DeliveryTaskService, DeliveryPartyService],
})
export class DeliveryTaskModule {}
