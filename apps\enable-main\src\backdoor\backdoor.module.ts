import { MicroserviceCommunicationModule } from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { BrandModule } from '../brand/brand.module';
import { CompanyModule } from '../company/company.module';
import { CustomerLoyaltyModule } from '../customer/modules/customer-loyalty/customer-loyalty.module';
import { CustomerNotificationModule } from '../customer/modules/customer-notification/customer-notification.module';
import { CustomerReadModule } from '../customer/modules/customer-read/customer-read.module';
import { CustomerReplacementsModule } from '../customer/modules/customer-replacements/customer-replacements.module';
import { CustomerRepositoryModule } from '../customer/modules/customer-repository/customer-repository.module';
import { CustomerTierModule } from '../customer/modules/customer-tier/customer-tier.module';
import { ShopifyModule } from '../integration/webstore/shopify/shopify.module';
import { NotificationModule } from '../notification/notification.module';
import { SharedModule } from '../shared/shared.module';
import { BackdoorController } from './controllers/backdoor.controller';
import { BackdoorService } from './services/backdoor.service';

@Module({
  imports: [
    SharedModule,
    BrandModule,
    NotificationModule,
    CompanyModule,
    CustomerRepositoryModule,
    CustomerNotificationModule,
    CustomerReadModule,
    CustomerLoyaltyModule,
    CustomerReplacementsModule,
    CustomerTierModule,
    ShopifyModule,
    MicroserviceCommunicationModule.forChild(
      'enable-main-notification-producer',
    ),
  ],
  controllers: [BackdoorController],
  providers: [BackdoorService],
})
export class BackdoorModule {}
