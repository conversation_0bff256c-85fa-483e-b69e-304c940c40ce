import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  Matches,
  MaxLength,
  MinLength,
} from 'class-validator';

export class CustomerTESSPaymentIntegration {
  @ApiProperty({
    type: String,
    required: true,
    maxLength: 32,
    minLength: 2,
  })
  @IsNotEmpty()
  @Matches('a-zA-Z')
  @MaxLength(32)
  @MinLength(2)
  name: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;
}
