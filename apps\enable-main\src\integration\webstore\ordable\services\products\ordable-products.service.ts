import { LoggerService } from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';

import { Store } from '../../../../../store/models/store.model';
import { CreateOrdableProductsDto } from '../../dtos/products/create-ordable-products.dto';
import { UpdateOrdableProductsDto } from '../../dtos/products/update-ordable-products.dto';
import { OrdableHttpRequestsServiceInterface } from '../ordable-http-requests.service.interface';
import { OrdableProductsServiceInterface } from './ordable-products.service.interface';

@Injectable()
export class OrdableProductsService implements OrdableProductsServiceInterface {
  private readonly loggerService = new LoggerService(
    OrdableProductsService.name,
  );
  PRODUCT_URI = '/api/products/';

  constructor(
    @Inject('OrdableHttpRequestsServiceInterface')
    private readonly ordableHttpRequestsService: OrdableHttpRequestsServiceInterface,
  ) {}

  async create(
    createOrdableProductsDto: CreateOrdableProductsDto,
    store: Store,
  ): Promise<any> {
    const URL = store.apiBaseUrl + this.PRODUCT_URI;
    const API_KEY = store.apiKey;
    this.loggerService.log(`Creating Ordable Product: `, {
      createOrdableProductsDto,
      store,
    });
    return this.ordableHttpRequestsService.createOrdablePostRequest(
      URL,
      API_KEY,
      createOrdableProductsDto,
    );
  }

  async update(
    updateOrdableProductsDto: UpdateOrdableProductsDto,
    store: Store,
  ): Promise<any> {
    const URL = store.apiBaseUrl + this.PRODUCT_URI;
    const API_KEY = store.apiKey;
    return this.ordableHttpRequestsService.createOrdablePatchRequest(
      URL,
      API_KEY,
      updateOrdableProductsDto,
    );
  }

  async findAll(store: Store): Promise<any> {
    const URL = store.apiBaseUrl + this.PRODUCT_URI;
    const API_KEY = store.apiKey;
    return this.ordableHttpRequestsService.createOrdableGetRequest(
      URL,
      API_KEY,
    );
  }
}
