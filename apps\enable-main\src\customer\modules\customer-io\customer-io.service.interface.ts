import {
  ImportExcelCustomerDto,
  ImportLoyaltyCustomersDto,
  IndexCustomerDto,
} from '@app/shared-stuff';

export interface CustomerIOServiceInterface {
  uploadFile(importExcelCustomerDto: ImportExcelCustomerDto): Promise<void>;
  downloadFile(indexCustomerDto: IndexCustomerDto): Promise<string>;

  importLoopyCustomers(
    importLoopyCustomersDto: ImportLoyaltyCustomersDto,
  ): Promise<unknown[]>;
  importBrandWalletCustomers(
    importBrandWalletCustomersDto: ImportLoyaltyCustomersDto,
  ): Promise<unknown[]>;
}

export const CustomerIOServiceInterface = Symbol('CustomerIOServiceInterface');
