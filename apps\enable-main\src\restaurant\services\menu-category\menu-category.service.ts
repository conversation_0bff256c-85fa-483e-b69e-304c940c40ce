import {
  Cache<PERSON>eys,
  CacheServiceInterface,
  CurrentUser,
  fullCacheKeys,
  ImageToCreate,
  Menu,
  MenuCategory,
  MenuCategoryDocument,
  MenuCategorySearchMapping,
  MenuCategorySortMapping,
  MenuCategoryToCreate,
  MenuCategoryToIndex,
  MenuCategoryToRemove,
  MenuCategoryToUpdate,
  MenuCategoryWithId,
  MenuDocument,
  responseCode,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { isValidObjectId, Model, Types } from 'mongoose';
import * as randomstring from 'randomstring';
import { ImageService } from '../../../shared/services/image/image.service';

@Injectable()
export class MenuCategoryService {
  constructor(
    @InjectModel('MenuCategory')
    private menuCategory: Model<MenuCategoryDocument, MenuCategory>,
    @InjectModel('Menu') private menuModel: Model<Menu>,
    private imageService: ImageService,
    @Inject(CacheServiceInterface)
    private cacheService: CacheServiceInterface,
  ) {}

  async index(menuCategoryToIndex: MenuCategoryToIndex) {
    const aggregation = [];

    if (menuCategoryToIndex.month) {
      aggregation.push({
        $match: { month: menuCategoryToIndex.month },
      });
    }

    if (menuCategoryToIndex.sort_type) {
      aggregation.push({
        $sort: MenuCategorySortMapping[menuCategoryToIndex.sort_type],
      });
    } else {
      aggregation.push({
        $sort: { sortOrder: 1 },
      });
    }

    if (menuCategoryToIndex.search_key) {
      if (menuCategoryToIndex.search_type == 'all') {
        aggregation.push(
          {
            $addFields: {
              matched: {
                $or: [
                  {
                    $regexMatch: {
                      input: '$name',
                      options: 'i',
                      regex: new RegExp(
                        `.*${menuCategoryToIndex.search_key}.*`,
                      ),
                    },
                  },
                  {
                    $regexMatch: {
                      input: '$reference',
                      options: 'i',
                      regex: new RegExp(
                        `.*${menuCategoryToIndex.search_key}.*`,
                      ),
                    },
                  },
                ],
              },
            },
          },
          { $match: { matched: true } },
        );
      } else {
        aggregation.push(
          {
            $addFields: {
              matched: {
                $regexMatch: MenuCategorySearchMapping(
                  menuCategoryToIndex.search_type || 'all',
                  menuCategoryToIndex.search_key,
                ),
              },
            },
          },
          { $match: { matched: true } },
        );
      }
    }
    if (menuCategoryToIndex.menu) {
      aggregation.push({
        $match: { menu: new Types.ObjectId(menuCategoryToIndex.menu) },
      });
    }

    aggregation.push({ $match: { deletedAt: { $eq: null } } });

    if (
      (menuCategoryToIndex.offset || menuCategoryToIndex.offset == 0) &&
      menuCategoryToIndex.limit
    ) {
      aggregation.push({
        $skip: menuCategoryToIndex.offset * menuCategoryToIndex.limit,
      });
      aggregation.push({
        $limit: menuCategoryToIndex.limit,
      });
    }
    // aggregation.push({
    //   $lookup: {
    //     from: 'menus',
    //     localField: 'menu',
    //     foreignField: '_id',
    //     as: 'menuDetails',
    //   },
    // });
    const categories = await this.menuCategory.aggregate(aggregation);
    return categories;
  }

  async findByMenuId(menuId: Types.ObjectId): Promise<MenuCategoryDocument[]> {
    return await this.menuCategory.find({ menu: new Types.ObjectId(menuId) });
  }

  async getMenuCategoryTotalNumbers(menuCategoryToIndex: MenuCategoryToIndex) {
    delete menuCategoryToIndex.offset;
    delete menuCategoryToIndex.limit;
    return await (
      await this.index(menuCategoryToIndex)
    ).length;
  }

  async getDetails(id: string) {
    const filters = { $or: [{ reference: id }] } as any;
    if (isValidObjectId(id)) {
      filters.$or.push({ _id: id });
      filters.$or.push({ deliverectId: id });
    }
    const category = await this.menuCategory.findOne(filters).populate('menu');
    return category;
  }

  async create(menuCategoryToCreate: MenuCategoryToCreate) {
    const categoryCheck = await this.menuCategory.findOne({
      name: menuCategoryToCreate.name,
      menu: menuCategoryToCreate.menu,
      deletedAt: undefined,
    });
    if (categoryCheck) {
      throw {
        code: responseCode.MISSING_DATA,
        statusCode: 400,
        message: 'category already exist',
      };
    }
    const menu = await this.menuModel.findOne({
      _id: menuCategoryToCreate.menu,
    });
    menuCategoryToCreate.menuName = menu.name;
    menuCategoryToCreate.menu = menu._id;

    if (menuCategoryToCreate.image) {
      menuCategoryToCreate['image'] = (await this.imageService.uploadImage(
        menuCategoryToCreate.image,
      )) as any;
    }

    const category = await this.menuCategory.create(menuCategoryToCreate);
    return category;
  }

  async update(menuCategoryToUpdate: MenuCategoryToUpdate) {
    const category = await this.menuCategory.findOne({
      _id: menuCategoryToUpdate._id,
    });
    const menu = await this.menuModel.findOne({
      _id: menuCategoryToUpdate.menu,
    });

    menuCategoryToUpdate.menuName = menu.name;
    menuCategoryToUpdate.menu = menu._id.toString();
    if (menuCategoryToUpdate.image) {
      const imageToCreate: ImageToCreate = {
        name: randomstring.generate(10),
        alt: menuCategoryToUpdate.image.alt,
        description: menuCategoryToUpdate.image.description,
        for: menuCategoryToUpdate.image.for,
        base64: menuCategoryToUpdate.image.base64,
      };
      menuCategoryToUpdate['image'] = (await this.imageService.uploadImage(
        imageToCreate,
      )) as any;
    }
    await this.menuCategory.updateOne(
      { _id: category._id },
      menuCategoryToUpdate,
    );
    return category;
  }

  async findOrCreateWithCache(
    name: string,
    menu: Types.ObjectId,
    menuName: string,
    createdBy: CurrentUser,
  ): Promise<MenuCategoryWithId> {
    const cacheKey = fullCacheKeys[CacheKeys.MENU_CATEGORY_WITH_NAME_AND_MENU](
      name,
      menu,
    );
    const cachedCategory = await this.cacheService.getCache(
      cacheKey,
      MenuCategoryWithId,
    );
    if (cachedCategory) return cachedCategory;
    const category = await this.findOrCreate(name, menu, menuName, createdBy);
    await this.cacheService.setCache(cacheKey, category.toJSON());
    return category;
  }

  async findOrCreate(
    name: string,
    menu: Types.ObjectId,
    menuName: string,
    createdBy: CurrentUser,
  ): Promise<MenuCategoryDocument> {
    const category = await this.menuCategory.findOne({
      name,
      menu,
      deletedAt: null,
    });

    if (category) return category;

    const createdCategory = await this.create({
      name,
      menu,
      menuName,
      createdBy,
    });

    return createdCategory;
  }

  async remove(menuCategoryToRemove: MenuCategoryToRemove) {
    const category = await this.menuCategory.findOne({
      _id: menuCategoryToRemove._id,
    });
    category.deletedAt = moment.utc().toDate();
    category.deletedBy = menuCategoryToRemove.deletedBy;
    await category.save();
    return category;
  }

  async deleteAllCategoryInsideMenu(menu: MenuDocument) {
    await this.menuCategory.deleteMany({ menu: menu._id });
  }
}
