import {
  AddressDto,
  CompanyDocument,
  CreateShipmentDto,
  EmbeddedBrandDto,
  GetAllShipmentDto,
  GetShipmentTrackingResponseDto,
  HelperSharedServiceInterface,
  IHelperSharedService,
  IndexResultDto,
  LoggerService,
  PusherService,
  responseCode,
  Shipment,
  ShipmentDocument,
  ShipmentEvent,
  ShipmentEventDto,
  UpdateShipmentDto,
} from '@app/shared-stuff';
import { ShipmentConsignorDto } from '@app/shared-stuff/dtos/shipment/shipment-consignor.dto';
import { ShipmentReplacementDto } from '@app/shared-stuff/dtos/shipment/shipment-replacement.dto';
import { ShipmentStatus } from '@app/shared-stuff/enums/shipment/shipment-status.enum';
import { Inject, Injectable } from '@nestjs/common';
import {
  BadRequestException,
  NotFoundException,
} from '@nestjs/common/exceptions';
import { ConfigService } from '@nestjs/config';
import { plainToInstance } from 'class-transformer';
import * as moment from 'moment-timezone';
import { FilterQuery, PipelineStage, Types } from 'mongoose';
import * as randomstring from 'randomstring';
import { CompanyService } from '../../../company/services/company/company.service';
import { ShipmentRepositoryInterface } from '../../repositories/shipment/shipment-repository.interface';
import { Shared3plService } from '../shared-3pl.service';
import { TempCustomerServiceInterface } from '../temp-customer/temp-customer-service.interface';
import { ShipmentNotificationService } from './shipment-notification.service';
import { ShipmentServiceInterface } from './shipment-service.interface';
import { WayBillService } from './way-bill.service';

@Injectable()
export class ShipmentService implements ShipmentServiceInterface {
  private readonly logger = new LoggerService(ShipmentService.name);
  private trackingEvents: Record<ShipmentEvent, ShipmentEventDto>;

  constructor(
    @Inject(ShipmentRepositoryInterface)
    private repository: ShipmentRepositoryInterface,
    @Inject(TempCustomerServiceInterface)
    private tempCustomerService: TempCustomerServiceInterface,
    private sharedService: Shared3plService,
    private companyService: CompanyService,
    private waybillService: WayBillService,
    private configService: ConfigService,
    private pusherService: PusherService,
    @Inject(IHelperSharedService)
    private helperSharedService: HelperSharedServiceInterface,
    private shipmentNotificationService: ShipmentNotificationService,
  ) {
    this.fillTrackingEvents();
  }

  async findAll(
    getAllShipmentDto: GetAllShipmentDto,
  ): Promise<IndexResultDto<Shipment>[]> {
    const pipeline: PipelineStage[] = [];
    this.addMatchStage(pipeline, getAllShipmentDto);
    this.addSortStage(pipeline);
    this.addPaginationStage(pipeline, getAllShipmentDto);
    return (await this.repository.aggregate(pipeline)) as [
      IndexResultDto<Shipment>,
    ];
  }

  async findByIdIn(ids: Types.ObjectId[]) {
    return this.repository.findAll({ _id: { $in: ids } });
  }

  async create({
    location,
    customer,
    eblBrandId,
    ...createShipmentDto
  }: CreateShipmentDto): Promise<ShipmentDocument> {
    const id = new Types.ObjectId();
    const shipment = plainToInstance(Shipment, {
      ...createShipmentDto,
      _id: id,
      status: ShipmentStatus.PACKING,
      wayBill: `${this.configService.get(
        'HOST_URL',
      )}/shipment/public/way-bill/${id}`,
      brand: await this.getShipmentBrand(
        createShipmentDto.companyId,
        createShipmentDto.consignor,
        eblBrandId,
      ),
    });
    const company = await this.companyService.get_details(
      shipment.companyId.toHexString(),
    );

    shipment['_id'] = id;
    shipment.trackingNumber = this.generateTrackingNumber(company.acronym);

    let tempCustomer = await this.tempCustomerService.findBySalesOrderId(
      shipment.salesOrderId,
    );
    if (!tempCustomer) {
      tempCustomer = await this.tempCustomerService.create({
        ...customer,
        brand: shipment.brand,
        salesOrderId: shipment.salesOrderId,
        companyId: shipment.companyId,
      });
    } else {
      tempCustomer = await this.tempCustomerService.update({
        ...customer,
        _id: tempCustomer._id,
        brand: shipment.brand,
        salesOrderId: shipment.salesOrderId,
        companyId: shipment.companyId,
      });
    }

    if (location) {
      shipment.location = await this.sharedService.createAddress(location);
    }

    const createdShipment = await this.repository.create(shipment);
    this.fireShipmentPusherEvent(company, createdShipment);
    const replacements = this.getShipmentReplacements(createdShipment);
    await this.shipmentNotificationService.fireOnShipmentCreated(
      createdShipment,
      replacements,
    );
    return createdShipment;
  }

  async update({
    location,
    _id,
    ...updateShipmentDto
  }: UpdateShipmentDto): Promise<ShipmentDocument> {
    const shipment = await this.findOne(_id);

    const company = await this.companyService.get_details(
      shipment.companyId.toHexString(),
    );

    const shipmentDto = plainToInstance(Shipment, {
      ...shipment.toJSON(),
      ...updateShipmentDto,
    });

    if (location) {
      shipmentDto.location = await this.sharedService.updateAddress(
        shipmentDto.location,
        location,
      );
    }

    const updatedShipment = await this.repository.findOneAndUpdate(
      { _id },
      { $set: { ...shipmentDto, _id } },
    );

    this.fireShipmentPusherEvent(company, updatedShipment);

    if (updatedShipment.status !== shipment.status) {
      const replacements = this.getShipmentReplacements(updatedShipment);
      await this.shipmentNotificationService.fireOnShipmentStatusChanged(
        shipment,
        replacements,
      );
    }

    return updatedShipment;
  }

  async findOne(id: Types.ObjectId): Promise<ShipmentDocument> {
    const shipment = await this.repository.findById(id);
    if (!shipment) {
      throw new NotFoundException('Shipment not found');
    }
    return shipment;
  }

  async updateShipmentsAddress(ids: Types.ObjectId[], location: AddressDto) {
    return this.repository.findOneAndUpdate(
      { _id: { $in: ids } },
      { $set: { location } },
    );
  }

  async findOneByTrackingNumberOrId(
    idOrTrackingNumber: string,
  ): Promise<ShipmentDocument> {
    const shipment =
      await this.repository.findOneByTrackingNumberOrId(idOrTrackingNumber);
    if (!shipment) {
      throw new NotFoundException('Shipment not found');
    }
    return shipment;
  }

  async generateWayBill(idOrTrackingNumber: string): Promise<Buffer> {
    const shipment = await this.findOneByTrackingNumberOrId(idOrTrackingNumber);
    const company = await this.companyService.get_details(
      shipment.companyId.toHexString(),
    );
    const tempCustomer = await this.tempCustomerService.findBySalesOrderId(
      shipment.salesOrderId,
    );

    return this.waybillService.generateWayBill(shipment, company, tempCustomer);
  }

  async findBySalesOrderId(salesOrderId: string): Promise<ShipmentDocument[]> {
    return this.repository.findAll({ salesOrderId });
  }

  async findByTrackingNumber(
    trackingNumber: string,
  ): Promise<ShipmentDocument> {
    const shipment = await this.repository.findByTrackingNumber(trackingNumber);
    if (!shipment) {
      throw new NotFoundException('Shipment not found');
    }
    return shipment;
  }

  async getTrackingDetails(
    id: Types.ObjectId,
  ): Promise<GetShipmentTrackingResponseDto> {
    const shipment = await this.findOne(id);
    const statusToEvents: Record<ShipmentStatus, ShipmentEvent> = {
      [ShipmentStatus.PACKING]: ShipmentEvent.PACKING,
      [ShipmentStatus.AWAITING]: ShipmentEvent.AWAITING,
      [ShipmentStatus.DELIVERED]: ShipmentEvent.DELIVERED,
      [ShipmentStatus.PACKED]: ShipmentEvent.PACKED,
      [ShipmentStatus.DELIVERING]: ShipmentEvent.DELIVERING,
      [ShipmentStatus.SHIPPED]: ShipmentEvent.SHIPPED,
      [ShipmentStatus.CANCELED]: ShipmentEvent.CANCELED,
    };

    return {
      statuses: Object.values(ShipmentStatus),
      events: Object.values(ShipmentEvent),
      event: this.trackingEvents[statusToEvents[shipment.status]],
      status: shipment.status,
      shipmentDetails: shipment.toObject(),
    };
  }

  private async getShipmentBrand(
    companyId: Types.ObjectId,
    consignor?: ShipmentConsignorDto,
    eblBrandId?: Types.ObjectId,
  ): Promise<EmbeddedBrandDto> {
    if (consignor && consignor.name) {
      const consignorBrand = await this.sharedService.fetchBrandByName(
        consignor.name,
        companyId,
      );

      if (consignorBrand) return consignorBrand;
      else
        this.logger.warn(
          `Received consignor name '${consignor.name}', but it did not match any brand for company '${companyId}'`,
        );
    }

    return this.sharedService.fetchEmbeddedBrand(eblBrandId);
  }

  private addMatchStage(
    pipeline: PipelineStage[],
    getAllShipmentDto: GetAllShipmentDto,
  ) {
    const match: FilterQuery<Shipment> = {};

    if (getAllShipmentDto.companyId) {
      match.companyId = getAllShipmentDto.companyId;
    }
    if (getAllShipmentDto.brandId) {
      match['brand._id'] = getAllShipmentDto.brandId;
    }

    if (getAllShipmentDto.salesOrderId) {
      match.salesOrderId = getAllShipmentDto.salesOrderId;
    }

    if (getAllShipmentDto.externalId) {
      match.externalId = getAllShipmentDto.externalId;
    }

    if (getAllShipmentDto.status) {
      match.status = getAllShipmentDto.status;
    }

    if (getAllShipmentDto.trackingNumber) {
      match.trackingNumber = getAllShipmentDto.trackingNumber;
    }

    if (getAllShipmentDto.searchQuery) {
      const searchQuery = getAllShipmentDto.searchQuery.replace(
        /[-\/\\^$*+?.()|[\]{}]/g,
        '\\$&',
      );

      match.$or = [
        { salesOrderId: { $regex: searchQuery, $options: 'i' } },
        { trackingNumber: { $regex: searchQuery, $options: 'i' } },
        { externalId: { $regex: searchQuery, $options: 'i' } },
      ];
    }
    pipeline.push({ $match: match });
  }

  private addPaginationStage(
    pipeline: PipelineStage[],
    getAllDistributionCenterDto: GetAllShipmentDto,
  ) {
    pipeline.push(
      this.helperSharedService.createPaginationStage(
        getAllDistributionCenterDto.offset,
        getAllDistributionCenterDto.limit,
      ),
    );
  }

  private generateTrackingNumber(companyAcronym: string): string {
    const datePart = moment.utc().format('YYMMDD');

    const uniquePart = randomstring.generate({
      capitalization: 'uppercase',
      charset: 'alphanumeric',
      length: 5,
    });

    return companyAcronym + datePart + uniquePart;
  }

  private fillTrackingEvents() {
    this.trackingEvents = {
      [ShipmentEvent.PACKING]: {
        name: ShipmentEvent.PACKING,
        description: 'Your shipment is packing.',
        iconName: 'pending.svg',
        isViewableStepper: true,
      },
      [ShipmentEvent.PACKED]: {
        name: ShipmentEvent.PACKED,
        description: 'Your shipment has been packed.',
        iconName: 'shipmentPacked.svg',
        isViewableStepper: true,
      },
      [ShipmentEvent.SHIPPED]: {
        name: ShipmentEvent.SHIPPED,
        description: 'Your shipment is on the way to the courier.',
        iconName: 'shipmentShipped.svg',
        isViewableStepper: true,
      },
      [ShipmentEvent.AWAITING]: {
        name: ShipmentEvent.AWAITING,
        description: 'Your shipment is awaiting delivery.',
        iconName: 'shipmentAwaiting.svg',
        isViewableStepper: true,
      },
      [ShipmentEvent.DELIVERED]: {
        name: ShipmentEvent.DELIVERED,
        description: 'Your shipment is delivered!',
        iconName: 'shipmentDelivered.svg',
        isViewableStepper: true,
      },
      [ShipmentEvent.DELIVERING]: {
        name: ShipmentEvent.DELIVERING,
        description: 'Your shipment is on the way!',
        iconName: 'shipmentDelivered.svg',
        isViewableStepper: true,
      },
      [ShipmentEvent.CANCELED]: {
        name: ShipmentEvent.CANCELED,
        description: 'Unfortunately, Your shipment has been deleted',
        iconName: 'cancel.svg',
        isViewableStepper: true,
      },
    };
  }

  private addSortStage(pipeline: PipelineStage[]) {
    pipeline.push({ $sort: { createdAt: -1 } });
  }

  private fireShipmentPusherEvent(
    company: CompanyDocument,
    shipment: ShipmentDocument,
  ) {
    this.pusherService.fireEvent(
      this.pusherService.getCompanyChannelName(company),
      'shipmentUpdated',
      shipment,
    );
  }

  async changeShipmentStatus(
    shipmentRef: string,
    status: ShipmentStatus,
  ): Promise<void> {
    const shipment = await this.repository.findOneAndUpdate(
      {
        $or: [{ trackingNumber: shipmentRef }, { externalId: shipmentRef }],
      },
      { status },
    );

    if (!shipment) {
      throw new BadRequestException(
        `Shipment not found by provided shipmentRef : ${shipmentRef} `,
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    }
    const company = await this.companyService.findById(shipment.companyId);
    this.fireShipmentPusherEvent(company, shipment);
    const replacements = this.getShipmentReplacements(shipment);
    await this.shipmentNotificationService.fireOnShipmentStatusChanged(
      shipment,
      replacements,
    );
  }

  private getShipmentReplacements({
    externalId,
    salesOrderId,
    status,
    trackingNumber,
  }: ShipmentDocument): ShipmentReplacementDto {
    return {
      externalId,
      salesOrderId,
      status,
      trackingNumber,
    };
  }
}
