import {
  AcquireLocationTriggerDto,
  CreateTempCustomerDto,
  GenericExceptionFilter,
  GetAllTempCustomerDto,
  SingleIdDto,
  TransformInterceptor,
  UpdateTempCustomerDto,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Get,
  Inject,
  Param,
  Patch,
  Post,
  Query,
  Req,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { CurrentUserService } from '../../shared/services/current-user/current-user.service';
import { TempCustomerServiceInterface } from '../services/temp-customer/temp-customer-service.interface';

@Controller('temp-customer')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags('Third Party Logistics')
@SetMetadata('module', 'temp-customer')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class TempCustomerController {
  constructor(
    @Inject(TempCustomerServiceInterface)
    private tempCustomerService: TempCustomerServiceInterface,
    private currentUserService: CurrentUserService,
  ) {}

  @Get()
  @SetMetadata('action', 'index')
  async findAll(
    @Query() getAllTempCustomerDto: GetAllTempCustomerDto,
    @Req() req: Request,
  ) {
    getAllTempCustomerDto.companyId = req['company_id']
      ? req['company_id']
      : getAllTempCustomerDto.companyId;

    const result = await this.tempCustomerService.findAll(
      getAllTempCustomerDto,
    );
    return {
      tempCustomers: result[0].paginatedResult,
      totalTempCustomers: result[0].totalCount[0]?.createdAt,
    };
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  async findOne(@Param() { id }: SingleIdDto) {
    return this.tempCustomerService.findOne(id);
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() createTempCustomerDto: CreateTempCustomerDto,
    @Req() req: Request,
  ) {
    createTempCustomerDto.companyId = req['company_id']
      ? req['company_id']
      : createTempCustomerDto.companyId;
    return this.tempCustomerService.create(createTempCustomerDto);
  }

  @Patch()
  @SetMetadata('action', 'update')
  async update(
    @Body() updateTempCustomerDto: UpdateTempCustomerDto,
    @Req() req: Request,
  ) {
    updateTempCustomerDto.companyId = req['company_id']
      ? req['company_id']
      : updateTempCustomerDto.companyId;
    return this.tempCustomerService.update(updateTempCustomerDto);
  }

  @Post('acquire-location-trigger')
  @SetMetadata('action', 'acquire-location-trigger')
  async acquireLocationTrigger(
    @Body() acquireLocationTriggerDto: AcquireLocationTriggerDto,
  ) {
    return this.tempCustomerService.fireAcquireLocationTrigger({
      ...acquireLocationTriggerDto,
      currentUser: this.currentUserService.getCurrentUser(),
    });
  }
}
