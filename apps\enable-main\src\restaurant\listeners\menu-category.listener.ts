import {
  <PERSON><PERSON><PERSON><PERSON>s,
  CacheServiceInterface,
  fullCacheKeys,
  MenuCategory,
  MenuCategoryEvents,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';

@Injectable()
export class MenuCategoryListener {
  constructor(
    @Inject(CacheServiceInterface)
    private readonly cacheService: CacheServiceInterface,
  ) {}

  @OnEvent(MenuCategoryEvents.MENU_CATEGORY_UPDATED)
  async handleMenuCategoryCreated(
    menuCategory: MenuCategory,
    isNameChanged: boolean,
    previousName: string,
  ) {
    if (isNameChanged) {
      const key = fullCacheKeys[CacheKeys.MENU_CATEGORY_WITH_NAME_AND_MENU](
        previousName,
        menuCategory.menu,
      );
      await this.cacheService.deleteCache(key);
      return;
    }

    const key = fullCacheKeys[CacheKeys.MENU_CATEGORY_WITH_NAME_AND_MENU](
      menuCategory.name,
      menuCategory.menu,
    );
    const keyExists = await this.cacheService.getCache(key, MenuCategory);

    if (keyExists) {
      await this.cacheService.setCache(key, menuCategory);
    }
  }
}
