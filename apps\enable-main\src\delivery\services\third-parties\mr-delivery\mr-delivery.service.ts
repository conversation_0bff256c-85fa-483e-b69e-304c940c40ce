import { OrderDocument, OrderPaymentMethod } from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as moment from 'moment-timezone';
import * as randomestring from 'randomstring';
import {
  MrDeliveryConfig,
  MrDeliveryStatusToUpdate,
  MrDeliveryTaskToCreate,
} from '../../../dto/mr-delivery.dto';
import { ThirdPartyTaskCreationDto } from '../../../dto/third-party-task-creation.dto';
import { ThirdPartiesServiceInterface } from '../third-parties.service.interface';
import { ThirdPartySharedService } from '../third-party-shared.service';

@Injectable()
export class MrDeliveryService implements ThirdPartiesServiceInterface {
  vehicleTypes: string[] = [] as const;
  defaultVehicleType: string = '';
  constructor(
    private configService: ConfigService,
    private http: HttpService,
    private eventEmitter: EventEmitter2,
    private thirdPartySharedService: ThirdPartySharedService,
  ) {}

  async createTask(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
  ): Promise<any> {
    const pickupLocation = await this.thirdPartySharedService.getPickupLocation(
      thirdPartyTaskCreationDto,
    );
    const pickupAddressString =
      await this.thirdPartySharedService.getPickupAddressString(pickupLocation);
    const deliveryLocation =
      await this.thirdPartySharedService.getDeliveryLocation(
        thirdPartyTaskCreationDto,
      );
    const deliveryAddressString =
      await this.thirdPartySharedService.getDeliveryAddressString(
        deliveryLocation,
      );
    const mrDeliveryTaskToCreate = this.constructCreateMrDeliveryTaskDto(
      thirdPartyTaskCreationDto,
      pickupAddressString,
      deliveryAddressString,
      pickupLocation,
      deliveryLocation,
    );
    const mrDeliveryConfig = this.loadMrDeliveryConfig();
    this.fillMrDeliveryConfigStaticInformation(
      mrDeliveryTaskToCreate,
      mrDeliveryConfig,
    );
    const taskCreationResponse = await this.performMrDeliveryRequest(
      mrDeliveryConfig.baseUrl,
      mrDeliveryTaskToCreate,
    );

    return { request: mrDeliveryTaskToCreate, response: taskCreationResponse };
  }

  async applyPostFunction(taskCreationResponse: any, order: OrderDocument) {
    await this.updateOrder(order);
  }

  async onMrDeliveryTaskUpdated(data: MrDeliveryStatusToUpdate) {
    this.eventEmitter.emit('mrDelivery.updated', data);
  }

  private constructCreateMrDeliveryTaskDto(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
    pickupAddressString: string,
    deliveryAddressString: string,
    pickupLocation,
    deliveryLocation,
  ): MrDeliveryTaskToCreate {
    const deliveryTime = moment
      .utc(thirdPartyTaskCreationDto.order.delivery_date)
      .format('YYYY-MM-DD HH:mm:ss');
    return {
      api_key: '',
      vendor_id:
        thirdPartyTaskCreationDto.order.branch &&
        typeof thirdPartyTaskCreationDto.order.branch !== 'string'
          ? parseInt(thirdPartyTaskCreationDto.order.branch.mrDeliveryVendorId)
          : -1,
      marketplace_user_id: -1,
      amount: thirdPartyTaskCreationDto.order.invoiced_amount,
      currency_id: 26,
      payment_method:
        thirdPartyTaskCreationDto.order.payment_method ==
        OrderPaymentMethod.cash
          ? 8
          : 33554432,
      delivery_charge: thirdPartyTaskCreationDto.order.delivery_amount,
      restaurant_name: thirdPartyTaskCreationDto.branch
        ? thirdPartyTaskCreationDto.branch.name
        : thirdPartyTaskCreationDto.company.name,
      restaurant_email: thirdPartyTaskCreationDto.branch
        ? thirdPartyTaskCreationDto.branch.email
        : thirdPartyTaskCreationDto.company.email,
      restaurant_phone: thirdPartyTaskCreationDto.branch
        ? thirdPartyTaskCreationDto.branch.phone
        : thirdPartyTaskCreationDto.company.phone,
      restaurant_address: pickupAddressString,
      restaurant_latitude: pickupLocation ? pickupLocation.latitude : 0,
      restaurant_longitude: pickupLocation ? pickupLocation.longitude : 0,
      customer_address: deliveryAddressString,
      customer_email: thirdPartyTaskCreationDto.customer.email
        ? thirdPartyTaskCreationDto.customer.email
        : 'default' + randomestring.generate(5) + '@e-butler.com',
      customer_name: thirdPartyTaskCreationDto.customer.full_name,
      customer_phone: thirdPartyTaskCreationDto.order.customer_phone,
      is_custom_order: 1,
      job_created_from: 5,
      order_id: thirdPartyTaskCreationDto.order.code,
      job_delivery_latitude: deliveryLocation ? deliveryLocation.latitude : 0,
      job_delivery_longitude: deliveryLocation ? deliveryLocation.longitude : 0,
      job_pickup_datetime: deliveryTime,
      job_description: thirdPartyTaskCreationDto.order.order_remarks
        ? thirdPartyTaskCreationDto.order.order_remarks
        : 'EButler Order',
    };
  }

  private loadMrDeliveryConfig(): MrDeliveryConfig {
    return {
      apiKey: this.configService.get('MR_DELIVERY_API_KEY'),
      baseUrl: this.configService.get('MR_DELIVERY_BASE'),
      vendorId: this.configService.get('MR_DELIVERY_VENDOR_ID'),
      marketplaceUserId: this.configService.get(
        'MR_DELIVERY_MARKETPLACE_USER_ID',
      ),
    };
  }

  private fillMrDeliveryConfigStaticInformation(
    mrDeliveryTaskToCreate: MrDeliveryTaskToCreate,
    mrDeliveryConfig: MrDeliveryConfig,
  ) {
    // mrDeliveryTaskToCreate.vendor_id = mrDeliveryConfig.vendorId;
    mrDeliveryTaskToCreate.marketplace_user_id =
      mrDeliveryConfig.marketplaceUserId;
    mrDeliveryTaskToCreate.api_key = mrDeliveryConfig.apiKey;
  }

  private async performMrDeliveryRequest(
    url: string,
    mrDeliveryTaskToCreate: MrDeliveryTaskToCreate,
  ) {
    return new Promise((resolve, reject) => {
      this.http.post(url, mrDeliveryTaskToCreate).subscribe(
        (data) => {
          resolve(data.data);
        },
        (err) => {
          reject(err);
        },
      );
    });
  }

  private async updateOrder(order: OrderDocument) {
    order.assigned_driver_name = 'Mr.Delivery Driver';
    order.driver = undefined;
    order.deliveryTaskCreated = true;
    await order.save();
  }
}
