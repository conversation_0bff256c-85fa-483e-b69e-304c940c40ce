import { MrDeliveryService } from '../../services/third-parties/mr-delivery/mr-delivery.service';
import { HelperService } from './../../../shared/services/helper/helper.service';
import { Response } from 'express';
import { Body, Controller, Post, Res, SetMetadata } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { responseCode } from '@app/shared-stuff';

@Controller('mr-delivery')
@ApiTags('Mr-Delivery')
export class MrDeliveryController {
  constructor(
    private helperService: HelperService,
    private mrDeliveryService: MrDeliveryService,
  ) {}

  @Post('status/updated')
  @SetMetadata('public', 'true')
  async onTaskUpdated(@Body() data: any, @Res() res: Response) {
    try {
      const mrDeliveryResponse =
        await this.mrDeliveryService.onMrDeliveryTaskUpdated(data);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'success to update Mr Delivery delivery status',
        mrDeliveryResponse,
        res,
      );
    } catch (err) {}
  }
}
