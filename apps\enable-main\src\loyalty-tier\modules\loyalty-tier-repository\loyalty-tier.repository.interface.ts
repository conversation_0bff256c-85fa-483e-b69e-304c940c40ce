import { FilterQuery, Types } from 'mongoose';

import {
  IGenericRepository,
  LoyaltyTier,
  LoyaltyTierDocument,
  LoyaltyTierWithIdDto,
  TierWithRequirements,
} from '@app/shared-stuff';

export interface LoyaltyTierRepositoryInterface
  extends IGenericRepository<LoyaltyTierDocument, LoyaltyTier> {
  find(
    filter: FilterQuery<LoyaltyTierDocument>,
  ): Promise<LoyaltyTierDocument[]>;
  findNextTier(
    companyId: Types.ObjectId,
    tierIndex: number,
    mustHaveAmountSpent?: boolean,
  ): Promise<LoyaltyTierDocument>;
  findPreviousTier(
    companyId: Types.ObjectId,
    tierIndex: number,
    mustHaveAmountSpent?: boolean,
  ): Promise<LoyaltyTierDocument>;
  findByCompanyId(companyId: Types.ObjectId): Promise<LoyaltyTierDocument[]>;
  findHighestTier(
    companyId: Types.ObjectId,
  ): Promise<LoyaltyTierDocument | null>;
  bulkUpdate(tiers: LoyaltyTierWithIdDto[]): Promise<LoyaltyTierDocument[]>;
  findByEnrollmentCode(enrollmentCode: string): Promise<LoyaltyTierDocument>;
  findNonVipTiersByCompanyId(
    companyId: Types.ObjectId,
  ): Promise<LoyaltyTierDocument[]>;
  applyTierRequirementsUpdate(tiers: TierWithRequirements[]): Promise<void>;
  delete(loyaltyTier: LoyaltyTierDocument): Promise<LoyaltyTierDocument>;
}

export const LoyaltyTierRepositoryInterface = Symbol(
  'LoyaltyTierRepositoryInterface',
);
