import {
  CacheServiceInterface,
  GenericExceptionFilter,
  RecreateShopifyOrdersDto,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Inject,
  Param,
  Post,
  Query,
  SetMetadata,
  UploadedFile,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiExcludeController } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { memoryStorage } from 'multer';
import { UtilityService } from './utility.service';

@Controller('utility')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiExcludeController()
export class UtilityController {
  constructor(
    private readonly utilityService: UtilityService,
    private readonly eventEmitter: EventEmitter2,
    @Inject(CacheServiceInterface)
    private readonly cacheService: CacheServiceInterface,
  ) {}

  @Post('/customer/regenerateOrdableLinks')
  @SetMetadata('public', 'true')
  public async regenerateCustomerOrdableLinks(
    @Body() { customerIds }: { customerIds: string[] },
  ) {
    return await this.utilityService.regenerateOrdableLinksForCustomers(
      customerIds.map((customerId) => new Types.ObjectId(customerId)),
    );
  }

  @Post('/customer/resync')
  @SetMetadata('public', 'true')
  public async resyncCustomers(
    @Body() { customerIds }: { customerIds: string[] },
  ) {
    return await this.utilityService.resyncCustomers(
      customerIds.map((customerId) => new Types.ObjectId(customerId)),
    );
  }

  // This API will set the customer's tier to their highest eligible tier.
  // This could result in the customer being demoted.
  @Post('/customer/recomputeTier')
  @SetMetadata('public', 'true')
  public async recomputeTier(
    @Body() { customerIds }: { customerIds: string[] },
  ) {
    return await this.utilityService.recomputeTier(
      customerIds.map((customerId) => new Types.ObjectId(customerId)),
    );
  }

  @Post('passes/forceRefresh/:passTypeIdentifier')
  @SetMetadata('public', 'true')
  forceRefresh(@Param('passTypeIdentifier') passTypeIdentifier: string) {
    return this.eventEmitter.emit('passes.forceRefresh', passTypeIdentifier);
  }

  @Post('cache/clearAll')
  @SetMetadata('public', 'true')
  async clearAllCache() {
    return this.cacheService.clearAllCache();
  }

  @Post('shopify/recreateOrders')
  @SetMetadata('public', 'true')
  recreateShopifyOrders(
    @Body()
    recreateShopifyOrdersDto: RecreateShopifyOrdersDto,
  ) {
    return this.utilityService.recreateShopifyOrders(recreateShopifyOrdersDto);
  }

  @Get('memoryUsage')
  @SetMetadata('public', 'true')
  getMemoryUsage() {
    const memoryUsage = process.memoryUsage();
    return {
      instance: process.env.NODE_APP_INSTANCE,
      rss: `${(memoryUsage.rss / 1024 / 1024).toFixed(2)} MB`,
      heapTotal: `${(memoryUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`,
      heapUsed: `${(memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`,
      external: `${(memoryUsage.external / 1024 / 1024).toFixed(2)} MB`,
    };
  }

  @Post('rectifyOrdablePoints')
  @UseInterceptors(FileInterceptor('file', { storage: memoryStorage() }))
  @SetMetadata('public', 'true')
  rectifyOrdablePoints(
    @Query('companyId') companyId: string,
    @UploadedFile('file') file: Express.Multer.File,
    @Query('commit') commit = false,
  ) {
    if (!companyId)
      throw new BadRequestException("Must include 'companyId' query param");
    if (!file) throw new BadRequestException("Must include 'file' form-data");
    return this.utilityService.rectifyOrdablePoints(
      new Types.ObjectId(companyId),
      file,
      commit,
    );
  }
}
