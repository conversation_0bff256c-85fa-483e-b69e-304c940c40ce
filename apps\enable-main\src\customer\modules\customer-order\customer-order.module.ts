import { TrackingModule } from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { BenefitModule } from '../../../benefit/benefit.module';
import { CompanyModule } from '../../../company/company.module';
import { CouponModule } from '../../../coupon/coupon.module';
import { LoyaltyPointLogModule } from '../../../loyalty-point-log/loyalty-point-log.module';
import { LoyaltyTierLogModule } from '../../../loyalty-tier-log/loyalty-tier-log.module';
import { OrderInvoiceModule } from '../../../order/modules/order-invoice/order-invoice.module';
import { OrderRedemptionModule } from '../../../order/modules/order-redemption/order-redemption.module';
import { CompletedPunchCardModule } from '../../../punch-card/modules/completed-punch-card/completed-punch-card.module';
import { CustomerLoyaltyMemberModule } from '../customer-loyalty-member/customer-loyalty-member.module';
import { CustomerPointsModule } from '../customer-points/customer.points.module';
import { CustomerPunchCardModule } from '../customer-punch-card/customer-punch-card.module';
import { CustomerReadModule } from '../customer-read/customer-read.module';
import { CustomerRepositoryModule } from '../customer-repository/customer-repository.module';
import { CustomerTierInfoModule } from '../customer-tier-info/customer-tier-info.module';
import { CustomerTierModule } from '../customer-tier/customer-tier.module';
import { CustomerWebstoreModule } from '../customer-webstore/customer-webstore.module';
import { CustomerOrderService } from './customer-order.service';
import { CustomerOrderServiceInterface } from './customer-order.service.interface';

@Module({
  providers: [
    { provide: CustomerOrderServiceInterface, useClass: CustomerOrderService },
  ],
  imports: [
    EventEmitterModule,
    CompanyModule,
    CouponModule,
    TrackingModule,
    LoyaltyPointLogModule,
    LoyaltyTierLogModule,
    CustomerRepositoryModule,
    CustomerReadModule,
    CustomerTierModule,
    CustomerTierInfoModule,
    CustomerLoyaltyMemberModule,
    CustomerPunchCardModule,
    CustomerWebstoreModule,
    CustomerPointsModule,
    OrderRedemptionModule,
    OrderInvoiceModule,
    CompletedPunchCardModule,
    BenefitModule,
  ],
  exports: [CustomerOrderServiceInterface],
})
export class CustomerOrderModule {}
