import { FalconFlexAutoAssignmentConfig } from './falcon-flex-auto-assignment-config.type';
import { FalconFlexMetaDataField } from './falcon-flex-meta-data-field.type';
import { FalconFlexLocation } from './falcon-flex-location.type';
import { FalconFlexSkillCriteria } from './falcon-flex-skill-criteria.type';
import { FalconFlexTaskItem } from './falcon-flex-task-item.type';

export class FalconFlexTaskCreationResponse {
  transportType: string;
  autoAssignmentConfig: FalconFlexAutoAssignmentConfig;
  amountToBeCollected: number;
  pickupByUtc: string;
  deliverByUtc: string;
  companyId: string;
  executingCompanyId: string;
  notes: string;
  priority: number;
  canGroupTask: boolean;
  taskItems: FalconFlexTaskItem[];
  pickupToDeliveryDistanceKm: number;
  isProofOfDeliveryRequired: boolean;
  isProofOfPickupRequired: boolean;
  driverToPickupDistanceKm: number;
  pickupToDeliveryEtaMin: number;
  status: string;
  paymentStatus: string;
  sendTrackingMessage: boolean;
  paymentStatusId: number;
  didAutoAssign: boolean;
  pickup: FalconFlexLocation;
  delivery: FalconFlexLocation;
  shortId: string;
  clientGeneratedId: string;
  totalCollectionAmountRemaining: number;
  totalAmountCollected: number;
  isOffloaded: boolean;
  offloadingResponsibilityInfo: string;
  costPerMinuteDelayInDelivery: number;
  skillCriteriaDto: FalconFlexSkillCriteria;
  taskAgentDetails: string;
  id: string;
  metaDataFields: FalconFlexMetaDataField[];
  createdAtUtc: string;
  createdBy: string;
  updatedAtUtc: string;
  updatedBy: string;
}
