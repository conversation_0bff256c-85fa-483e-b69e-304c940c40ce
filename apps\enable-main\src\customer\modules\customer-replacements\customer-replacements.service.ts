import {
  CompanyDocument,
  CustomerDocument,
  CustomerReplacementOptions,
  Language,
  LanguageCode,
  LoyaltyCustomerReplacementsDto,
  LoyaltyTierDocument,
  LoyaltyTierProgramProgress,
  LRPSource,
  NextAchievement,
  NoTier,
  WalletApp,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';

import { ar } from 'assets/strings/ar';
import { en } from 'assets/strings/en';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { CalendarSystemService } from '../../../company/services/calendar-system/calendar-system.service';
import { CompanyService } from '../../../company/services/company/company.service';
import { CouponServiceInterface } from '../../../coupon/services/coupon.service.interface';
import { ShortenUrlServiceInterface } from '../../../integration/shorten-url/services/shorten-url.service.interface';
import { LoyaltyTierReadServiceInterface } from '../../../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.service.interface';
import { GooglePassesServiceInterface } from '../../../passes/services/google-passes/google-passes.service.interface';
import { PunchCardAchievementService } from '../../../punch-card/modules/punch-card-achievement/punch-card-achievement.service';
import { PunchCardReadService } from '../../../punch-card/modules/punch-card-read/punch-card-read.service';
import { capitalizeFirstLetter } from '../../../shared/services/helper/helper.service';
import { CustomerLoyaltyServiceInterface } from '../customer-loyalty/customer-loyalty.service.interface';
import { CustomerPassLinkServiceInterface } from '../customer-pass-link/customer-pass-link-service.interface';
import { CustomerPunchCardServiceInterface } from '../customer-punch-card/customer-punch-card.service.interface';
import { CustomerTierInfoServiceInterface } from '../customer-tier-info/customer-tier-info.service.interface';
import { CustomerWebstoreServiceInterface } from '../customer-webstore/customer-webstore.service.interface';
import { CustomerReplacementsServiceInterface } from './customer-replacements.service.interface';

@Injectable()
export class CustomerReplacementsService
  implements CustomerReplacementsServiceInterface
{
  constructor(
    private readonly companyService: CompanyService,
    private readonly calendarSystemService: CalendarSystemService,
    private readonly punchCardService: PunchCardReadService,
    private readonly punchCardAchievementService: PunchCardAchievementService,
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandServiceInterface,
    @Inject(LoyaltyTierReadServiceInterface)
    private readonly loyaltyTierService: LoyaltyTierReadServiceInterface,
    @Inject(CustomerTierInfoServiceInterface)
    private readonly customerTierInfoService: CustomerTierInfoServiceInterface,
    @Inject(CustomerLoyaltyServiceInterface)
    private readonly customerLoyaltyService: CustomerLoyaltyServiceInterface,
    @Inject(CustomerWebstoreServiceInterface)
    private readonly customerWebstoreService: CustomerWebstoreServiceInterface,
    @Inject(CustomerPunchCardServiceInterface)
    private readonly customerPunchCardService: CustomerPunchCardServiceInterface,
    @Inject(CouponServiceInterface)
    private readonly couponService: CouponServiceInterface,
    @Inject(CustomerTierInfoServiceInterface)
    private readonly customerLoyaltyTierInfoService: CustomerTierInfoServiceInterface,
    @Inject(GooglePassesServiceInterface)
    private readonly googlePassesService: GooglePassesServiceInterface,
    @Inject(CustomerPassLinkServiceInterface)
    private readonly customerPassLinkService: CustomerPassLinkServiceInterface,
    @Inject(ShortenUrlServiceInterface)
    private readonly shortenUrlService: ShortenUrlServiceInterface,
  ) {}
  async getCustomerReplacements(
    customer: CustomerDocument,
    {
      brandId,
      preferredLanguage,
      source,
      branchId,
    }: CustomerReplacementOptions = {},
  ): Promise<LoyaltyCustomerReplacementsDto> {
    if (!brandId) brandId = await this.getActiveBrandId(customer);

    const isArabicPreferred =
      (preferredLanguage ?? customer.language) === Language.arabic;
    const company = await this.companyService.findById(customer.company);

    const orderRate =
      await this.customerTierInfoService.getTotalLoyaltyOrderRate(customer);
    const amountSpent =
      await this.customerTierInfoService.getTotalLoyaltyAmountSpent(customer);
    const pointsRate = Math.floor(
      await this.customerLoyaltyTierInfoService.getTotalLoyaltyPointsRate(
        customer,
      ),
    );
    const tierLoyaltyProgress = { orderRate, amountSpent, pointsRate };
    const currentTier = customer.loyaltyTier
      ? await this.loyaltyTierService.findById(customer.loyaltyTier._id)
      : null;
    const remainingOrdersCurrentTier =
      this.customerTierInfoService.computeRemainingOrders(
        currentTier,
        orderRate,
      );
    const remainingRequirementsCurrentTier = this.getRemainingRequirements(
      company,
      customer,
      currentTier,
      tierLoyaltyProgress,
    );

    const previousTier = await this.loyaltyTierService.findPreviousTier(
      customer.company._id,
      customer.loyaltyTier?._id,
    );
    const lowerTierEn = previousTier?.nameEn ?? NoTier.getName(company);
    const lowerTierAr =
      previousTier?.nameAr ?? NoTier.getName(company, LanguageCode.ar);

    const nextTier = await this.loyaltyTierService.findNextTier(
      customer.company._id,
      currentTier?.tierIndex,
    );
    const upperTier = nextTier ?? currentTier;
    const remainingOrdersUpperTier =
      this.customerTierInfoService.computeRemainingOrders(upperTier, orderRate);
    const remainingRequirementsUpperTier = this.getRemainingRequirements(
      company,
      customer,
      upperTier,
      tierLoyaltyProgress,
    );

    const remainingAmountSpentUpperTier = async () => {
      const remainingAmountSpent =
        await this.customerLoyaltyTierInfoService.getRemainingAmountSpentUpperTier(
          customer,
          nextTier,
          amountSpent,
        );
      return remainingAmountSpent
        ? `${Number.isInteger(remainingAmountSpent) ? remainingAmountSpent.toString() : remainingAmountSpent.toFixed(2)} ${company.localization?.currency ?? 'QAR'}`
        : '';
    };

    const remainingAmountSpentCurrentTier =
      this.customerLoyaltyTierInfoService.computeRemainingAmount(
        customer.loyaltyTier,
        amountSpent,
      );

    const currentTierRequirementThresholdsPercent = () =>
      this.customerTierInfoService.getThresholdPercentage(customer, company);

    const upperTierRequirementThresholdsPercent = () =>
      this.customerTierInfoService.getThresholdPercentage(
        customer,
        company,
        upperTier,
      );

    const loyaltyRegistrationPageLink = () =>
      this.customerLoyaltyService.getLoyaltyRegistrationPageLink(
        brandId,
        company,
        customer,
        source || LRPSource.NOTIFICATION_ENGINE,
        branchId,
      );

    const validTillDate = await this.customerTierInfoService.getTierValidTill(
      customer,
      company,
      false,
    );

    const firstGracePeriodReminderDate =
      await this.customerTierInfoService.getGracePeriodReminderDate(
        customer,
        company,
      );

    const punchCards = await this.punchCardService.index({
      companyId: customer.company,
    });
    const nextAchievements =
      await this.customerPunchCardService.getNextAchievements(customer);
    const nextPunchCardAchievementRequirements = nextAchievements
      .filter((achievement) => achievement)
      .map((achievement) =>
        this.formatPunchCardAchievementTargetRequirements(
          achievement,
          isArabicPreferred,
        ),
      )
      .join(isArabicPreferred ? ' ،' : ', ');
    const remainingUntilNextPunchCardAchievement = nextAchievements
      .filter((achievement) => achievement)
      .map((achievement) =>
        this.formatPunchCardAchievementRemainingRequirements(
          achievement,
          isArabicPreferred,
        ),
      )
      .join(isArabicPreferred ? ' ،' : ', ');

    const tierCustomBenefitsList = customer.earnedBenefits
      ?.map((benefit) =>
        isArabicPreferred ? benefit.titleAr : benefit.titleEn,
      )
      .join(', ');

    const nextCoupon =
      await this.couponService.findCustomerNextCoupon(customer);
    const highestUnlockedCoupon =
      await this.couponService.findCustomerHighestRedeemableCoupon(customer);
    return {
      phoneNumber: `${customer.country_code}${customer.phone}`,
      email: customer.email ?? '',
      firstName: capitalizeFirstLetter(customer.first_name),
      fullName: capitalizeFirstLetter(customer.full_name),
      customerShortCode: customer.shortCode ?? '',
      lastName: customer.last_name
        ? capitalizeFirstLetter(customer.last_name)
        : '',
      customerTitle: customer.title,
      loyaltyPointBalance: customer.loyaltyPoints,
      loyaltyTier: isArabicPreferred
        ? currentTier?.nameAr ||
          currentTier?.nameEn ||
          NoTier.getName(company, LanguageCode.ar)
        : currentTier?.nameEn || NoTier.getName(company),
      upperLoyaltyTier: isArabicPreferred
        ? upperTier?.nameAr || upperTier?.nameEn || ''
        : upperTier?.nameEn || '',
      lowerTier: isArabicPreferred ? lowerTierAr : lowerTierEn,
      remainingOrdersCurrentTier: remainingOrdersCurrentTier,
      remainingOrdersUpperTier: remainingOrdersUpperTier,
      remainingRequirementsCurrentTier: remainingRequirementsCurrentTier,
      remainingRequirementsUpperTier: remainingRequirementsUpperTier,
      upperTierDiscountValue: (upperTier?.percentDiscount ?? 0) + '%',
      tierDiscountValue: (currentTier?.percentDiscount ?? 0) + '%',
      upperTierDiscountValueNumberOfUses:
        upperTier?.percentDiscountMaximumNumberOfUses
          ? upperTier?.percentDiscountMaximumNumberOfUses
          : '',
      tierDiscountValueNumberOfUses:
        currentTier?.percentDiscountMaximumNumberOfUses
          ? currentTier.percentDiscountMaximumNumberOfUses
          : '',
      upperTierFreeDeliveryNumberOfUses:
        upperTier?.freeDeliveryMaximumNumberOfUses
          ? upperTier?.freeDeliveryMaximumNumberOfUses
          : '',
      tierFreeDeliveryNumberOfUses: currentTier?.freeDeliveryMaximumNumberOfUses
        ? currentTier?.freeDeliveryMaximumNumberOfUses
        : '',
      tierFreeDeliveryRemainingNumberOfUses: customer.loyaltyTier
        ?.freeDeliveryRemainingNumberOfUses
        ? customer.loyaltyTier.freeDeliveryRemainingNumberOfUses
        : '',
      tierDiscountValueRemainingNumberOfUses: customer.loyaltyTier
        ?.percentDiscountRemainingNumberOfUses
        ? customer?.loyaltyTier?.percentDiscountRemainingNumberOfUses
        : '',
      tierDiscountOrderValueThreshold:
        currentTier?.minimumCartValueForDiscount ?? 0,
      ordableLink:
        this.customerWebstoreService.getOrdableLink(customer, brandId) ?? '',
      loyaltyRegistrationPageLink: loyaltyRegistrationPageLink,
      walletPassAccessPageLink: loyaltyRegistrationPageLink,
      walletPassLink: async () =>
        this.customerPassLinkService.getWalletPassLink(
          customer,
          await this.brandService.findById(brandId),
          customer.deviceData?.operatingSystem?.name == 'iOS'
            ? WalletApp.APPLE_WALLET
            : WalletApp.WALLET_PASSES,
          true,
        ),
      googleWalletPassLink: async () =>
        this.shortenUrlService.shortenUrl({
          url: await this.googlePassesService.generatePass(customer, brandId),
          partialCode: 'loyalty-card',
          canExpire: false,
        }),
      gracePeriodRemainingDays: moment
        .utc(this.calendarSystemService.getEndDate(customer, company))
        .diff(moment.utc(), 'days'),
      firstGracePeriodReminderDate: moment
        .utc(firstGracePeriodReminderDate)
        .format('DD/MM/YYYY'),
      validTill: moment.utc(validTillDate).format('DD/MM/YYYY'),
      totalLoyaltyOrderRate: orderRate,
      totalLoyaltyAmountSpent: amountSpent,
      nextComputationDate: this.calendarSystemService
        .getEndDate(customer, company)
        .toString(),
      currentTierRequirementThresholdsPercent,
      upperTierRequirementThresholdsPercent,
      nextPunchCardAchievementRequirements:
        nextPunchCardAchievementRequirements,
      remainingUntilNextPunchCardAchievement:
        remainingUntilNextPunchCardAchievement,
      nextPunchCardAchievementBenefits:
        this.getNextPunchCardAchievementBenefits(
          nextAchievements,
          isArabicPreferred,
        ),
      nextCouponBenefit: nextCoupon?.titleEn,
      remainingPointsNextCoupon: nextCoupon
        ? Math.ceil(nextCoupon.loyaltyPointCost - customer.loyaltyPoints)
        : 0,
      remainingAmountSpentUpperTier,
      remainingAmountSpentCurrentTier: remainingAmountSpentCurrentTier
        ? `${Number.isInteger(remainingAmountSpentCurrentTier) ? remainingAmountSpentCurrentTier.toString() : remainingAmountSpentCurrentTier.toFixed(2)} ${
            company.localization?.currency ?? 'QAR'
          }`
        : '',
      remainingPointsCurrentTier:
        Math.floor(
          this.customerLoyaltyTierInfoService.computeRemainingPoints(
            customer.loyaltyTier,
            pointsRate,
          ),
        ) || '0',
      remainingPointsUpperTier:
        Math.floor(
          this.customerLoyaltyTierInfoService.computeRemainingPoints(
            upperTier,
            pointsRate,
          ),
        ) || '0',
      highestUnlockedCouponBenefit: highestUnlockedCoupon?.titleEn,
      nextPunchCardAchievementRequirementsFirstTrack:
        this.formatPunchCardAchievementTargetRequirements(
          nextAchievements[0],
          isArabicPreferred,
        ),
      nextPunchCardAchievementRequirementsSecondTrack:
        this.formatPunchCardAchievementTargetRequirements(
          nextAchievements[1],
          isArabicPreferred,
        ),
      remainingUntilNextPunchCardAchievementFirstTrack:
        this.formatPunchCardAchievementRemainingRequirements(
          nextAchievements[0],
          isArabicPreferred,
        ),
      remainingUntilNextPunchCardAchievementSecondTrack:
        this.formatPunchCardAchievementRemainingRequirements(
          nextAchievements[1],
          isArabicPreferred,
        ),
      punchCardFirstTrackName:
        punchCards.length >= 1
          ? isArabicPreferred
            ? punchCards[0].nameAr
            : punchCards[0].nameEn
          : '',
      punchCardSecondTrackName:
        punchCards.length >= 2
          ? isArabicPreferred
            ? punchCards[1].nameAr
            : punchCards[1].nameEn
          : '',
      nextPunchCardAchievementBenefitsFirstTrack: () =>
        this.getNextPunchCardAchievementBenefits(
          [nextAchievements[0]],
          isArabicPreferred,
        ),
      nextPunchCardAchievementBenefitsSecondTrack: () =>
        this.getNextPunchCardAchievementBenefits(
          [nextAchievements[1]],
          isArabicPreferred,
        ),
      tierCustomBenefitsList,
      tierPointsExchangeRate:
        company.loyaltyProgramConfig?.earningExchangeRates?.tierExchangeRates?.[
          customer.loyaltyTier?._id.toString()
        ] ?? company.loyaltyProgramConfig?.earningExchangeRates?.global,
      upperTierPointsExchangeRate:
        company.loyaltyProgramConfig?.earningExchangeRates?.tierExchangeRates?.[
          upperTier?._id.toString()
        ] ?? company.loyaltyProgramConfig?.earningExchangeRates?.global,
      tierProgramPointsProgress: pointsRate,
      tierProgramPointsTitleAr:
        company.loyaltyProgramConfig?.pointsBalanceTitleAr,
      tierProgramPointsTitleEn:
        company.loyaltyProgramConfig?.pointsBalanceTitleEn,
      numberOfRewards: customer.rewards
        ? customer.rewards.length.toString()
        : '0',
      punchCardProgressFirstTrack: customer.punchCardProgress?.[0]
        ? `${customer.punchCardProgress[0].count}`
        : '0',
      punchCardProgressSecondTrack: customer.punchCardProgress?.[1]
        ? `${customer.punchCardProgress[1].count}`
        : '0',
      loyaltyPointsBalanceInCash: company.loyaltyProgramConfig
        ?.hasFlexiblePointsRedemption
        ? (customer.loyaltyPoints ?? 0) /
          company.loyaltyProgramConfig.redemptionExchangeRate
        : 0,
      enableWalletPass: async () =>
        this.customerPassLinkService.getWalletPassLink(
          customer,
          await this.brandService.findById(brandId),
          WalletApp.ENABLE_WALLET,
          true,
        ),
    };
  }

  private async getActiveBrandId(
    customer: CustomerDocument,
  ): Promise<Types.ObjectId> {
    if (customer.activeBrand) return customer.activeBrand._id;
    return (await this.brandService.findLoyaltyBrand(customer?.company))._id;
  }

  private formatPunchCardAchievementRemainingRequirements(
    achievement: NextAchievement,
    isArabicPreferred: boolean,
  ): string {
    if (!achievement) return '';
    return isArabicPreferred
      ? `${achievement.remainingAmount.toLocaleString('ar')} ${
          achievement.punchCardNameAr
        }`
      : `${achievement.remainingAmount} ${achievement.punchCardNameEn}`;
  }

  private formatPunchCardAchievementTargetRequirements(
    achievement: NextAchievement,
    isArabicPreferred: boolean,
  ): string {
    if (!achievement) return '';
    return isArabicPreferred
      ? `${achievement.requirement.targetValue.toLocaleString('ar')} ${
          achievement.punchCardNameAr
        }`
      : `${achievement.requirement.targetValue} ${achievement.punchCardNameEn}`;
  }

  private getNextPunchCardAchievementBenefits(
    nextAchievements: NextAchievement[],
    isArabicPreferred?: boolean,
  ): string {
    if (!nextAchievements || nextAchievements.length === 0) return '';

    const nextAchievement = nextAchievements.reduce((previous, current) =>
      previous && previous.remainingAmount < current?.remainingAmount
        ? previous
        : current,
    );

    return this.punchCardAchievementService.formatAchievementReward(
      nextAchievement?.reward,
      isArabicPreferred,
    );
  }

  public getRemainingRequirements(
    company: CompanyDocument,
    customer: CustomerDocument,
    loyaltyTier: LoyaltyTierDocument,
    { orderRate, amountSpent, pointsRate }: LoyaltyTierProgramProgress,
  ): string | number {
    if (!loyaltyTier) return '';
    if (loyaltyTier.amountSpentThreshold || loyaltyTier.orderRateThreshold) {
      const ordersRemaining =
        this.customerTierInfoService.computeRemainingOrders(
          loyaltyTier,
          orderRate,
        );

      const strings = customer.language === Language.arabic ? ar : en;
      const orderRemainingString = strings.r_remainingOrders(ordersRemaining);

      if (!loyaltyTier?.amountSpentThreshold) return orderRemainingString;

      const amountRemaining = Math.round(
        this.customerTierInfoService.computeRemainingAmount(
          loyaltyTier,
          amountSpent,
        ),
      );
      const amountRemainingString = strings.r_remainingAmount(
        amountRemaining,
        company.localization.currency,
      );
      if (!loyaltyTier?.orderRateThreshold) return amountRemainingString;

      const isCloseToThreshold =
        amountRemaining < loyaltyTier.amountSpentThreshold * 0.7;
      if (!isCloseToThreshold) return orderRemainingString;

      return strings.r_remainingOrdersOrAmount(
        ordersRemaining,
        amountRemaining,
        company.localization.currency,
      );
    } else {
      return this.customerTierInfoService.computeRemainingPoints(
        loyaltyTier,
        pointsRate,
      );
    }
  }
}
