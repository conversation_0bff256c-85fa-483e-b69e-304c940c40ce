import { ApiProperty } from '@nestjs/swagger';
import { ObjectIdTransform, OrderItem } from '@app/shared-stuff';
import { Types } from 'mongoose';
import { IsOptional } from 'class-validator';

export class itemToRemove {
  @ApiProperty({
    type: String,
    required: true,
  })
  @ObjectIdTransform()
  order_id: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: true,
  })
  @ObjectIdTransform()
  item_id: Types.ObjectId;
}

export class ItemToUpdate extends OrderItem {
  @ApiProperty({
    type: String,
    required: true,
  })
  @ObjectIdTransform()
  _id: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: true,
  })
  @ObjectIdTransform()
  order_id: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: false,
  })
  @ObjectIdTransform()
  company: Types.ObjectId;
}

export class OrderItemToAdd extends OrderItem {
  @ApiProperty({
    type: String,
    required: false,
  })
  company: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @ObjectIdTransform()
  order_id: Types.ObjectId;
}

export class OrderItemToIndex {
  @ApiProperty({
    required: false,
  })
  @ObjectIdTransform({ optional: true })
  company?: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: false,
  })
  @ObjectIdTransform()
  orderId?: Types.ObjectId;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  searchKey?: string;
}
