import {
  areObjectIdsEqual,
  BrandDocument,
  CompanyDocument,
  CustomerDocument,
  LabelledField,
  LoyaltyCardStatus,
  LoyaltyTierProgramProgress,
  PassBarcodePayload,
  PassConfig,
  PassField,
  PassFieldContext,
  PassFieldName,
  PusherService,
  RegisteredPass,
  TierLevellingUpMethod,
  UnreachableError,
  WalletApp,
} from '@app/shared-stuff';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as moment from 'moment-timezone';
import { Barcode } from 'passkit-generator';
import { CompanyService } from '../../company/services/company/company.service';
import { CouponServiceInterface } from '../../coupon/services/coupon.service.interface';
import { CustomerCodeServiceInterface } from '../../customer/modules/customer-code/customer-code.service.interface';
import { CustomerTierInfoServiceInterface } from '../../customer/modules/customer-tier-info/customer-tier-info.service.interface';
import { LoyaltyTierReadServiceInterface } from '../../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.service.interface';
import { ImageService } from '../../shared/services/image/image.service';
import { PassesFieldServiceInterface } from './passes-field/passes-field.service.interface';
import { PassesImageServiceInterface } from './passes-image/passes-image.service.interface';

@Injectable()
export class PassesService {
  constructor(
    @Inject(CustomerCodeServiceInterface)
    private readonly customerCodeService: CustomerCodeServiceInterface,
    @Inject(PassesImageServiceInterface)
    private readonly passesImageService: PassesImageServiceInterface,
    @Inject(PassesFieldServiceInterface)
    private readonly passesFieldService: PassesFieldServiceInterface,
    @Inject(LoyaltyTierReadServiceInterface)
    private readonly loyaltyTierReadService: LoyaltyTierReadServiceInterface,
    @Inject(CustomerTierInfoServiceInterface)
    private readonly customerTierInfoService: CustomerTierInfoServiceInterface,
    @Inject(CouponServiceInterface)
    private readonly couponService: CouponServiceInterface,
    private readonly companyService: CompanyService,
    private readonly imageService: ImageService,
    private readonly eventEmitter: EventEmitter2,
    private readonly pusherService: PusherService,
  ) {}

  public async getPassFieldContext(
    customer: CustomerDocument,
    brand: BrandDocument,
  ): Promise<PassFieldContext> {
    if (!areObjectIdsEqual(customer.company, brand.companyId))
      throw new BadRequestException({
        message: `Cannot generate pass context. Customer company is ${customer.company}. Brand company is ${brand.companyId}. These must match.`,
        statusCode: 400,
        data: {
          brandId: brand._id,
          customerId: customer._id,
          brandCompanyId: brand.companyId,
          customerCompanyId: customer.company,
        },
      });

    const company = await this.companyService.findById(brand.companyId);

    const isPointsWithoutCoupons =
      company.loyaltyProgramConfig?.hasLoyaltyTiers &&
      company.loyaltyProgramConfig?.hasLoyaltyPoints &&
      company.loyaltyProgramConfig?.tierLevellingUpMethod ===
        TierLevellingUpMethod.POINTS_RATE &&
      !company.loyaltyProgramConfig?.hasCoupons;

    const loyaltyTier =
      company.loyaltyProgramConfig?.hasLoyaltyTiers && customer.loyaltyTier
        ? await this.loyaltyTierReadService.findById(customer.loyaltyTier._id)
        : null;

    const nextTier = company.loyaltyProgramConfig?.hasLoyaltyTiers
      ? await this.loyaltyTierReadService.findNextTier(
          company._id,
          loyaltyTier?.tierIndex,
        )
      : null;

    const loyaltyTierProgramProgress = company.loyaltyProgramConfig
      ?.hasLoyaltyTiers
      ? await this.customerTierInfoService.getLoyaltyTierProgramProgress(
          customer,
          company,
        )
      : new LoyaltyTierProgramProgress();

    const availableCoupons =
      company.loyaltyProgramConfig?.hasLoyaltyPoints && !isPointsWithoutCoupons
        ? await this.couponService.findCustomerRedeemableCoupons(customer)
        : [];

    return {
      customer,
      company,
      brand,
      availableCoupons,
      loyaltyTierProgramProgress,
      loyaltyTier,
      nextTier,
      isPointsWithoutCoupons,
    };
  }

  async saveNewPass(
    customer: CustomerDocument,
    newPass: RegisteredPass,
  ): Promise<CustomerDocument> {
    customer.registeredPasses = [newPass, ...(customer.registeredPasses || [])];
    if (customer.loyaltyCardStatus === LoyaltyCardStatus.NOT_ADDED) {
      this.eventEmitter.emit('customer.passes.added', customer);
    }
    customer.loyaltyCardStatus = newPass.walletApp;
    await customer.save();

    await this.pusherService.fireEvent(
      customer._id.toHexString(),
      'loyalty-card-status-changed',
      { cardStatus: customer.loyaltyCardStatus },
    );

    return customer;
  }

  async deletePass(
    customer: CustomerDocument,
    passToDelete: RegisteredPass,
  ): Promise<CustomerDocument> {
    const newRegisteredPasses: RegisteredPass[] = [
      ...(customer.registeredPasses || []).filter(
        (pass) => pass !== passToDelete,
      ),
      {
        ...passToDelete,
        deletedAt: moment.utc().toDate(),
      },
    ];
    const update = {
      registeredPasses: newRegisteredPasses,
      loyaltyCardStatus: this.getLoyaltyCardStatus(newRegisteredPasses),
    };
    await customer.updateOne(update);

    await this.pusherService.fireEvent(
      customer._id.toHexString(),
      'loyalty-card-status-changed',
      { cardStatus: customer.loyaltyCardStatus },
    );

    return customer.set(update);
  }

  async generateQRCodePayload(
    customer: CustomerDocument,
    passConfig: PassConfig,
  ): Promise<string> {
    const payloadType =
      passConfig?.barcodePayload ?? PassBarcodePayload.JSON_ID;
    switch (payloadType) {
      case PassBarcodePayload.ID:
        return customer._id.toString();
      case PassBarcodePayload.JSON_ID:
        return JSON.stringify({ id: customer._id.toString() });
      case PassBarcodePayload.SHORT_CODE:
        return await this.customerCodeService.getOrGenerateCustomerShortCode(
          customer,
        );
      default:
        throw new UnreachableError(payloadType);
    }
  }

  private getLoyaltyCardStatus(
    registeredPasses: RegisteredPass[],
  ): LoyaltyCardStatus | WalletApp {
    const hasAddedPasses = registeredPasses?.length > 0;
    if (!hasAddedPasses) return LoyaltyCardStatus.NOT_ADDED;

    const isPassKept = (pass: RegisteredPass) => !pass.deletedAt;
    const keptPasses = registeredPasses.filter(isPassKept);
    const hasKeptPasses = keptPasses.length > 0;
    if (!hasKeptPasses) return LoyaltyCardStatus.REMOVED;

    const latestKeptPass = keptPasses.reduce(
      (latestPass: RegisteredPass, currentPass: RegisteredPass) =>
        currentPass.createdAt > latestPass.createdAt ? currentPass : latestPass,
      keptPasses[0],
    );
    return latestKeptPass.walletApp;
  }

  async getStripImage(
    customer: CustomerDocument,
    loyaltyTierProgramProgress: LoyaltyTierProgramProgress,
    passConfig: PassConfig,
    company: CompanyDocument,
  ): Promise<Buffer> {
    const staticStripImage = passConfig?.stripImageConfig?.staticStripImage;
    return staticStripImage
      ? await this.imageService.readImageData(staticStripImage)
      : await this.passesImageService.generateImage(
          customer,
          loyaltyTierProgramProgress,
          passConfig,
          company,
        );
  }

  async getHeaderFields(
    passConfig: PassConfig,
    passFieldContext: PassFieldContext,
  ): Promise<PassField[]> {
    return this.passesFieldService.getFields(
      this.getHeaderLabelledFields(passConfig, passFieldContext),
      passConfig?.fieldConfig,
      passFieldContext,
    );
  }

  private getHeaderLabelledFields(
    passConfig: PassConfig,
    { company, customer }: PassFieldContext,
  ): LabelledField[] {
    if (passConfig?.fieldConfig?.headerFields)
      return passConfig.fieldConfig.headerFields;

    const isPointsAndTierPass =
      company?.loyaltyProgramConfig?.hasLoyaltyTiers &&
      company?.loyaltyProgramConfig?.hasLoyaltyPoints &&
      !company?.loyaltyProgramConfig?.hasLoyaltyPunchCards;

    const isVIPTierPass =
      customer.loyaltyTier?.isVipTier &&
      !passConfig.fieldConfig?.hideVipTierPrimaryField;

    return isPointsAndTierPass && !isVIPTierPass
      ? [{ fieldName: PassFieldName.TIER }]
      : [{ fieldName: PassFieldName.CUSTOMER_FIRST_NAME }];
  }

  async getPrimaryFields(
    passConfig: PassConfig,
    passFieldContext: PassFieldContext,
  ): Promise<PassField[]> {
    return this.passesFieldService.getFields(
      this.getPrimaryLabelledFields(passConfig, passFieldContext),
      passConfig?.fieldConfig,
      passFieldContext,
    );
  }

  private getPrimaryLabelledFields(
    passConfig: PassConfig,
    { customer, company }: PassFieldContext,
  ): LabelledField[] {
    const isPointsPass =
      company?.loyaltyProgramConfig?.hasLoyaltyPoints &&
      !company?.loyaltyProgramConfig?.hasLoyaltyTiers &&
      !company?.loyaltyProgramConfig?.hasLoyaltyPunchCards;
    if (isPointsPass) return [{ fieldName: PassFieldName.POINTS_BALANCE }];

    const isVIPTierPass =
      customer.loyaltyTier?.isVipTier &&
      !passConfig.fieldConfig?.hideVipTierPrimaryField;
    if (isVIPTierPass) return [{ fieldName: PassFieldName.TIER }];

    return [];
  }

  async getSecondaryFields(
    passConfig: PassConfig,
    passFieldContext: PassFieldContext,
  ): Promise<PassField[]> {
    return this.passesFieldService.getFields(
      this.getSecondaryLabelledFields(passConfig, passFieldContext),
      passConfig?.fieldConfig,
      passFieldContext,
    );
  }

  private getSecondaryLabelledFields(
    passConfig: PassConfig,
    passFieldContext: PassFieldContext,
  ): LabelledField[] {
    if (passConfig?.fieldConfig?.secondaryFields)
      return passConfig.fieldConfig.secondaryFields;

    if (passFieldContext.isPointsWithoutCoupons)
      return [
        { fieldName: PassFieldName.TIER_VALID_TILL },
        { fieldName: PassFieldName.POINTS_PROGRESS },
      ];

    const { company, customer } = passFieldContext;
    const isTiersAndPointsAndPunchCardPass =
      company?.loyaltyProgramConfig?.hasLoyaltyTiers &&
      company?.loyaltyProgramConfig?.hasLoyaltyPoints &&
      company?.loyaltyProgramConfig?.hasLoyaltyPunchCards;
    if (isTiersAndPointsAndPunchCardPass)
      return [
        { fieldName: PassFieldName.CUSTOMER_AVAILABLE_REWARDS },
        { fieldName: PassFieldName.POINTS_BALANCE },
        ...(passConfig.fieldConfig?.hideTierSecondaryField
          ? []
          : [{ fieldName: PassFieldName.TIER }]),
      ];

    const isPointsAndPunchCardPass =
      !company?.loyaltyProgramConfig?.hasLoyaltyTiers &&
      company?.loyaltyProgramConfig?.hasLoyaltyPoints &&
      company?.loyaltyProgramConfig?.hasLoyaltyPunchCards;
    if (isPointsAndPunchCardPass)
      return [
        { fieldName: PassFieldName.CUSTOMER_AVAILABLE_REWARDS },
        { fieldName: PassFieldName.PUNCH_CARD_PROGRESS },
        { fieldName: PassFieldName.POINTS_BALANCE },
      ];

    const isTiersAndPunchCardPass =
      company?.loyaltyProgramConfig?.hasLoyaltyTiers &&
      !company?.loyaltyProgramConfig?.hasLoyaltyPoints &&
      company?.loyaltyProgramConfig?.hasLoyaltyPunchCards;
    if (isTiersAndPunchCardPass)
      return [
        { fieldName: PassFieldName.PUNCH_CARD_PROGRESS },
        { fieldName: PassFieldName.CUSTOMER_AVAILABLE_REWARDS },
        { fieldName: PassFieldName.TIER_VALID_TILL },
      ];

    const isTiersAndPointsPass =
      company?.loyaltyProgramConfig?.hasLoyaltyTiers &&
      company?.loyaltyProgramConfig?.hasLoyaltyPoints &&
      !company?.loyaltyProgramConfig?.hasLoyaltyPunchCards;
    if (isTiersAndPointsPass)
      return [
        { fieldName: PassFieldName.TIER_VALID_TILL },
        { fieldName: PassFieldName.POINTS_BALANCE },
        { fieldName: PassFieldName.CUSTOMER_AVAILABLE_COUPONS },
      ];

    if (company?.loyaltyProgramConfig?.hasLoyaltyTiers)
      return [
        ...(customer.loyaltyTier &&
        !passConfig.fieldConfig?.hideTierSecondaryField
          ? [{ fieldName: PassFieldName.TIER }]
          : []),
        { fieldName: PassFieldName.TIER_VALID_TILL },
        { fieldName: PassFieldName.NEXT_TIER },
      ];

    if (company?.loyaltyProgramConfig?.hasLoyaltyPoints)
      return [
        { fieldName: PassFieldName.NEXT_COUPON },
        { fieldName: PassFieldName.CUSTOMER_AVAILABLE_COUPONS },
      ];

    if (company?.loyaltyProgramConfig?.hasLoyaltyPunchCards)
      return [
        { fieldName: PassFieldName.CUSTOMER_AVAILABLE_REWARDS },
        { fieldName: PassFieldName.PUNCH_CARD_PROGRESS },
      ];

    return [];
  }

  async getBackFields(
    passConfig: PassConfig,
    {
      customer,
      brand,
      company,
      nextTier,
      availableCoupons,
      loyaltyTierProgramProgress,
      isPointsWithoutCoupons,
    }: PassFieldContext,
  ): Promise<PassField[]> {
    const fields = [
      this.passesFieldService.getMessagesField(customer),
      this.passesFieldService.getPassDescriptionField(
        passConfig?.fieldConfig,
        brand,
      ),
    ];

    if (company?.loyaltyProgramConfig?.hasLoyaltyTiers)
      fields.push(
        await this.passesFieldService.getTierDescriptionField(
          customer,
          brand,
          nextTier,
          loyaltyTierProgramProgress,
        ),
      );

    if (
      company?.loyaltyProgramConfig?.hasLoyaltyPoints &&
      !isPointsWithoutCoupons
    )
      fields.push(
        this.passesFieldService.getPointsDescriptionField(
          customer,
          brand,
          company,
          availableCoupons,
        ),
      );

    if (company?.loyaltyProgramConfig?.hasLoyaltyPunchCards)
      fields.push(
        await this.passesFieldService.getRewardsDescriptionField(
          customer,
          brand,
        ),
      );

    fields.push(this.passesFieldService.getCustomerBenefitField(customer));

    const hasSocialLinks =
      company.facebook || company.instgram || company.twitter;
    if (hasSocialLinks) {
      fields.push(this.passesFieldService.getSocialLinksField(brand, company));
    }

    fields.push(this.passesFieldService.getPoweredByField());

    return fields;
  }

  async getQRCode(
    customer: CustomerDocument,
    passConfig: PassConfig,
  ): Promise<Barcode> {
    const payload = await this.generateQRCodePayload(customer, passConfig);
    return {
      format: 'PKBarcodeFormatQR',
      message: payload,
    };
  }
}
