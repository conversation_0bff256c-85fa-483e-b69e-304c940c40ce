import {
  BrandDocument,
  CustomerDocument,
  LoyaltyClass,
  LoyaltyObject,
  PassObjectId,
} from '@app/shared-stuff';
import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GoogleAuth } from 'google-auth-library';
import { sign } from 'jsonwebtoken';

@Injectable()
export class GooglePassesHttpService {
  private readonly baseUrl: string;
  private readonly classUrl: string;
  private readonly objectUrl: string;
  private readonly httpClient: GoogleAuth;
  private readonly credentials: Record<string, any>;
  private readonly issuerId: string;

  constructor(private readonly configService: ConfigService) {
    this.baseUrl = this.configService.get<string>('GOOGLE_WALLET_BASE_URL');
    this.classUrl = `${this.baseUrl}/loyaltyClass`;
    this.objectUrl = `${this.baseUrl}/loyaltyObject`;
    this.issuerId = this.configService.get('GOOGLE_WALLET_ISSUER_ID');
    const rawCredentials = this.configService.get('GOOGLE_WALLET_CREDENTIALS');
    if (rawCredentials) {
      this.credentials = JSON.parse(rawCredentials);
      this.httpClient = new GoogleAuth({
        credentials: this.credentials,
        scopes: 'https://www.googleapis.com/auth/wallet_object.issuer',
      });
    } else {
      this.logger.error(
        'GOOGLE_WALLET_CREDENTIALS env variable is not defined',
      );
    }
  }
  private readonly logger = new Logger(GooglePassesHttpService.name);

  public async findLoyaltyClass(loyaltyClassId: string): Promise<LoyaltyClass> {
    try {
      const response = await this.httpClient.request<LoyaltyClass>({
        url: `${this.classUrl}/${loyaltyClassId}`,
        method: 'GET',
      });

      return response.data;
    } catch (err) {
      this.logger.error(
        `Error while finding loyalty class ${loyaltyClassId}: ${err.message}`,
        err,
      );
      return null;
    }
  }

  public async findLoyaltyObject(
    loyaltyObjectId: string,
  ): Promise<LoyaltyObject> {
    try {
      const response = await this.httpClient.request<LoyaltyObject>({
        url: `${this.objectUrl}/${loyaltyObjectId}`,
        method: 'GET',
      });

      return response.data;
    } catch (err) {
      this.logger.error(
        `Error while finding loyalty object ${loyaltyObjectId}: ${err.message}`,
        err,
      );
      return null;
    }
  }

  public async createLoyaltyClass(
    loyaltyClass: LoyaltyClass,
  ): Promise<LoyaltyClass> {
    const response = await this.httpClient.request<LoyaltyClass>({
      url: this.classUrl,
      method: 'POST',
      data: loyaltyClass,
    });

    if (response.status !== 200) {
      throw new InternalServerErrorException(
        `Failed to create Google Wallet Loyalty Class for id ${loyaltyClass.id}`,
      );
    }

    return response.data;
  }

  public async updateLoyaltyClass(
    loyaltyClass: Partial<LoyaltyClass>,
  ): Promise<LoyaltyClass> {
    const response = await this.httpClient.request<LoyaltyClass>({
      url: `${this.classUrl}/${loyaltyClass.id}`,
      method: 'PATCH',
      data: loyaltyClass,
    });

    if (response.status !== 200) {
      throw new InternalServerErrorException(
        `Failed to update Google Wallet Loyalty Class for id ${loyaltyClass.id}`,
      );
    }

    return response.data;
  }

  public async createLoyaltyObject(
    loyaltyObject: LoyaltyObject,
  ): Promise<LoyaltyObject> {
    const response = await this.httpClient.request({
      url: this.objectUrl,
      method: 'POST',
      data: loyaltyObject,
    });

    if (response.status !== 200) {
      throw new InternalServerErrorException(
        `Failed to create Google Wallet Loyalty Object for id ${loyaltyObject.id}`,
      );
    }

    return response.data;
  }

  public async updateLoyaltyObject(
    loyaltyObject: Partial<LoyaltyObject>,
  ): Promise<LoyaltyObject> {
    const response = await this.httpClient.request({
      url: `${this.objectUrl}/${loyaltyObject.id}`,
      method: 'PATCH',
      data: loyaltyObject,
    });

    if (response.status !== 200) {
      throw new InternalServerErrorException(
        `Failed to update Google Wallet Loyalty Object for id ${loyaltyObject.id}`,
      );
    }

    return response.data;
  }

  public async updateOrCreateClass(
    loyaltyClass: LoyaltyClass,
  ): Promise<LoyaltyClass> {
    const exists = await this.findLoyaltyClass(loyaltyClass.id);
    if (exists) return await this.updateLoyaltyClass(loyaltyClass);
    else return await this.createLoyaltyClass(loyaltyClass);
  }

  public async updateOrCreateObject(
    loyaltyObject: LoyaltyObject,
  ): Promise<LoyaltyObject> {
    const exists = await this.findLoyaltyObject(loyaltyObject.id);
    if (exists) return await this.updateLoyaltyObject(loyaltyObject);
    else return await this.createLoyaltyObject(loyaltyObject);
  }

  public async createPassLink(loyaltyObjectId: string): Promise<string> {
    const payload = {
      iss: this.credentials.client_email,
      aud: 'google',
      origins: ['enable.tech'],
      typ: 'savetowallet',
      payload: {
        loyaltyObjects: [{ id: loyaltyObjectId }],
      },
    };

    const token = sign(payload, this.credentials.private_key, {
      algorithm: 'RS256',
    });

    this.logger.log('Add to Google Wallet link');
    this.logger.log(`https://pay.google.com/gp/v/save/${token}`);

    return `https://pay.google.com/gp/v/save/${token}`;
  }

  public getLoyaltyClassId(brand: BrandDocument): string {
    return `${this.issuerId}.${brand._id}`;
  }

  public getLoyaltyObjectId(
    brand: BrandDocument,
    customer: CustomerDocument,
  ): PassObjectId {
    return PassObjectId.build(this.issuerId, brand._id, customer._id);
  }
}
