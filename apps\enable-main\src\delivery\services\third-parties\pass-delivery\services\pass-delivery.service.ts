import {
  EmbeddedBrandDto,
  LoggerService,
  OrderDocument,
  OrderLogActionEnum,
  OrderPaymentMethod,
  OrderPaymentStatus,
  SavedLocation,
} from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import { Inject, Injectable } from '@nestjs/common';

import { ConfigService } from '@nestjs/config';
import { RawAxiosRequestHeaders } from 'axios';
import { IntegrationLogRepositoryInterface } from '../../../../../integration/integration-log/repositories/interfaces/integration-log.repository.interface';
import { LocationType } from '../../../../../shared/enums/location-type.enum';
import { HelperService } from '../../../../../shared/services/helper/helper.service';
import { ThirdPartyTaskCreationDto } from '../../../../dto/third-party-task-creation.dto';
import { ThirdPartiesServiceInterface } from '../../third-parties.service.interface';
import { ThirdPartySharedService } from '../../third-party-shared.service';
import { CreatePassDeliveryTaskDto } from '../dtos/create-pass-delivery-task.dto';
import { PassDeliveryLocationDto } from '../dtos/pass-delivery-location.dto';
import { PassDeliveryPaymentType } from '../enums/pass-delivery-payment-type.enum';
import { PassDeliveryCreationResponse } from '../types/pass-delivery-creation-response.type';
import { PassDeliveryGetTaskDetailsResponse } from '../types/pass-delivery-get-task-details-response.type';

@Injectable()
export class PassDeliveryService implements ThirdPartiesServiceInterface {
  vehicleTypes: string[] = [] as const;
  defaultVehicleType: string = '';

  private readonly loggerService = new LoggerService(PassDeliveryService.name);
  private readonly PASS_DELIVERY_TOKEN: string;
  private readonly PASS_DELIVERY_BASE_URL: string;

  constructor(
    private configService: ConfigService,
    private httpService: HttpService,
    private thirdPartySharedService: ThirdPartySharedService,
    @Inject('IntegrationLogRepositoryInterface')
    private readonly integrationLogRepository: IntegrationLogRepositoryInterface,
    private readonly helperService: HelperService,
  ) {
    this.PASS_DELIVERY_TOKEN = this.configService.get<string>(
      'PASS_DELIVERY_TOKEN',
    );
    this.PASS_DELIVERY_BASE_URL = this.configService.get<string>(
      'PASS_DELIVERY_BASE_URL',
    );
  }

  async createTask(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
  ): Promise<any> {
    const createPassDeliveryTaskDto =
      await this.constructCreatePassDeliveryTaskDto(thirdPartyTaskCreationDto);

    try {
      const taskCreationResponse = await this.create(createPassDeliveryTaskDto);
      this.loggerService.log(
        '[Pass Delivery] Task Created',
        createPassDeliveryTaskDto,
        taskCreationResponse,
      );
      await this.integrationLogRepository.logSuccess(
        OrderLogActionEnum.PASS_DELIVERY_TASK_CREATION,
        createPassDeliveryTaskDto,
        taskCreationResponse,
        taskCreationResponse?.order_id?.toString(),
      );
      return {
        request: createPassDeliveryTaskDto,
        response: taskCreationResponse,
      };
    } catch (error) {
      this.loggerService.error(
        `[Pass Delivery] Error while : ${OrderLogActionEnum.PASS_DELIVERY_TASK_CREATION}` +
          error.message,
        error.stacktrace,
        createPassDeliveryTaskDto,
      );
      await this.integrationLogRepository.logError(
        OrderLogActionEnum.PASS_DELIVERY_TASK_CREATION,
        createPassDeliveryTaskDto,
        this.helperService.transformError(error),
      );
    }
  }

  async create(
    createPassDeliveryTaskDto: CreatePassDeliveryTaskDto,
  ): Promise<PassDeliveryCreationResponse> {
    const HEADERS = this.constructRequestHeaders(this.PASS_DELIVERY_TOKEN);

    return new Promise(async (resolve, reject) => {
      this.httpService
        .post(this.PASS_DELIVERY_BASE_URL, createPassDeliveryTaskDto, {
          headers: HEADERS,
        })
        .subscribe({
          next: (data) => {
            resolve(data.data.data ?? {});
          },
          error: (error) => {
            this.loggerService.log(
              '[Pass Delivery] Error while create pass delivery task' +
                error.message,
              error.stacktrace,
              createPassDeliveryTaskDto,
            );
            reject(error);
          },
        });
    });
  }

  async applyPostFunction(
    passDeliveryCreationResponse: PassDeliveryCreationResponse,
    order: OrderDocument,
  ) {
    if (passDeliveryCreationResponse)
      await this.updateOrder(order, passDeliveryCreationResponse);
    else
      this.loggerService.error(
        '[Pass Delivery] there is no response coming from Pass Delivery Service with order: ',
        { passDeliveryCreationResponse, order },
      );
  }

  async cancel(order: OrderDocument) {
    const CANCEL_TASK_URL =
      this.PASS_DELIVERY_BASE_URL + `/${order.passDeliveryTaskId}/cancel`;

    return new Promise(async (resolve, reject) => {
      this.httpService
        .get(CANCEL_TASK_URL, {
          headers: {
            Authorization: this.PASS_DELIVERY_TOKEN,
          },
        })
        .subscribe({
          next: (data) => {
            resolve(data.data ?? {});
          },
          error: (error) => {
            this.loggerService.error(
              '[Pass Delivery] Error While Cancel Pass Delivery Task',
              error,
            );
            reject(error);
          },
        });
    });
  }

  async getTaskDetails(
    passDeliveryTaskId: number,
  ): Promise<PassDeliveryGetTaskDetailsResponse> {
    const GET_TASK_DETAILS_URL =
      this.PASS_DELIVERY_BASE_URL + `/${passDeliveryTaskId}`;

    return new Promise(async (resolve, reject) => {
      this.httpService
        .get(GET_TASK_DETAILS_URL, {
          headers: {
            Authorization: this.PASS_DELIVERY_TOKEN,
          },
        })
        .subscribe({
          next: (data) => {
            resolve(data.data ?? {});
          },
          error: (error) => {
            this.loggerService.error(
              '[Pass Delivery] Error While Get Pass Delivery Task',
              error,
            );
            reject(error);
          },
        });
    });
  }

  private constructRequestHeaders(bearerToken: string) {
    const CONTENT_TYPE = 'application/json';
    return {
      Authorization: bearerToken,
      Accept: CONTENT_TYPE,
      'Content-Type': CONTENT_TYPE,
    };
  }

  private async constructCreatePassDeliveryTaskDto(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
  ): Promise<CreatePassDeliveryTaskDto> {
    const pickup = await this.getPassDeliveryPickupLocation(
      thirdPartyTaskCreationDto,
    );

    const dropoffs = await this.getPassDeliveryDropoffsLocations(
      thirdPartyTaskCreationDto,
    );

    const isPaidAlready =
      (thirdPartyTaskCreationDto.order.payment_method ===
        OrderPaymentMethod.online &&
        thirdPartyTaskCreationDto.order.payment_status ===
          OrderPaymentStatus.COMPLETED) ||
      thirdPartyTaskCreationDto.order.payment_method ===
        OrderPaymentMethod.prepaid;

    return {
      addresses: {
        pickup,
        dropoffs,
      },
      payment_type: isPaidAlready
        ? PassDeliveryPaymentType.WALLET
        : PassDeliveryPaymentType.CASH,
    };
  }

  private async getPassDeliveryPickupLocation(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
  ): Promise<PassDeliveryLocationDto> {
    const location = await this.thirdPartySharedService.getPickupLocation(
      thirdPartyTaskCreationDto,
    );
    const brandOnBranch = thirdPartyTaskCreationDto.branch?.brands?.find(
      (brand: EmbeddedBrandDto) =>
        brand._id.toHexString() ===
        thirdPartyTaskCreationDto.order.brand._id.toHexString(),
    );

    return {
      lat: location?.latitude,
      long: location?.longitude,
      name: thirdPartyTaskCreationDto?.branch?.name,
      phone:
        thirdPartyTaskCreationDto?.company?.localization?.countryCode.toString() +
        (brandOnBranch
          ? brandOnBranch?.phoneNumber
          : thirdPartyTaskCreationDto?.company?.phone),
      address: this.helperService.convertLocationToString(
        location,
        LocationType.PICKUP,
      ),
    };
  }

  private async getPassDeliveryDropoffsLocations(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
  ): Promise<PassDeliveryLocationDto[]> {
    const location: SavedLocation =
      await this.thirdPartySharedService.getDeliveryLocation(
        thirdPartyTaskCreationDto,
      );

    return [
      {
        lat: location.latitude,
        long: location.longitude,
        name: thirdPartyTaskCreationDto.customer.full_name,
        phone:
          thirdPartyTaskCreationDto.customer.country_code +
          thirdPartyTaskCreationDto.customer.phone,
        address: this.helperService.convertLocationToString(
          location,
          LocationType.DELIVERY,
        ),
      },
    ];
  }

  private async updateOrder(
    order: OrderDocument,
    passDeliveryCreationResponse: PassDeliveryCreationResponse,
  ) {
    order.assigned_driver_name = '[Pass Delivery] Driver';
    order.driver = undefined;
    order.deliveryTaskCreated = true;
    order.passDeliveryTaskId = passDeliveryCreationResponse.order_id;
    await order.save();
  }
}
