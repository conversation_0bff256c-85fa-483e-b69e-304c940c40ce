import { CollectionName } from '@app/shared-stuff';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { HydratedDocument, Types } from 'mongoose';
import { RolePrivilege } from '../dto/role.dto';

export type RoleDocument = HydratedDocument<Role>;
@Schema({ timestamps: true, collection: CollectionName.ROLES })
export class Role {
  @Prop({
    required: true,
  })
  name: string;

  @Prop({
    required: false,
  })
  company_name: string;

  @Prop({
    required: true,
  })
  description: string;

  @Prop({
    type: [{ type: Types.ObjectId, ref: CollectionName.PERMISSIONS }],
  })
  permissions: [string];

  @Prop({
    type: [],
    required: false,
  })
  privileges: RolePrivilege[];

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: CollectionName.COMPANY,
  })
  company: Types.ObjectId;

  @Prop({
    type: Boolean,
    required: false,
    default: false,
  })
  cannotBeViewed: boolean;

  @Prop({
    type: {},
    required: false,
  })
  updatedBy: {};

  @Prop({
    type: Date,
    required: false,
  })
  updatedAt: Date;

  @Prop({
    type: {},
    required: false,
  })
  createdBy: {};

  @Prop({
    type: Date,
    required: false,
  })
  createdAt: Date;

  @Prop({
    type: {},
    required: false,
  })
  assignedTo: {};

  @Prop({
    type: {},
    required: false,
  })
  deletedBy: {};

  @Prop({
    type: Date,
    required: false,
  })
  deletedAt: Date;

  @Prop({
    type: String,
    default: moment().startOf('month').format('MMMM'),
  })
  month: string;

  @Prop({
    type: Number,
    default: moment().week(),
  })
  week: number;

  @Prop({
    type: Number,
    default: moment().date(),
  })
  day: number;

  @Prop({
    type: Number,
    default: moment().year(),
  })
  year: number;
}

export const RoleSchema = SchemaFactory.createForClass(Role);
