import { Customer, CustomerDocument } from '@app/shared-stuff';
import {
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import * as randomstring from 'randomstring';
import { CustomerRepositoryInterface } from '../customer-repository/customer.repository.interface';
import { CustomerCodeServiceInterface } from './customer-code.service.interface';

@Injectable()
export class CustomerCodeService implements CustomerCodeServiceInterface {
  private readonly MAX_SHORT_CODE_ATTEMPTS = 5;

  constructor(
    @Inject(CustomerRepositoryInterface)
    private readonly customerRepository: CustomerRepositoryInterface,
  ) {}

  async getOrGenerateCustomerShortCode(
    customer: CustomerDocument,
  ): Promise<string> {
    if (customer.shortCode) return customer.shortCode;

    const initialShortCode = this.generateInitialShortCode(customer);
    const isShortCodeTaken = (shortCode: string): Promise<boolean> =>
      this.customerRepository.isShortCodeTaken(customer.company, shortCode);

    let shortCode = initialShortCode;
    let attempts = 0;

    while (await isShortCodeTaken(shortCode)) {
      if (attempts < this.MAX_SHORT_CODE_ATTEMPTS) {
        shortCode = this.generateRandomShortCode();
        attempts++;
      } else {
        throw new InternalServerErrorException(
          `Could not generate unique short code after ${this.MAX_SHORT_CODE_ATTEMPTS} attempts. Try increasing the short code length for company ${customer.company}`,
        );
      }
    }

    await this.customerRepository.updateOne(
      { _id: customer._id },
      { shortCode },
    );

    return shortCode;
  }

  private getCodeStart(customer: Customer): string {
    return customer.last_name?.trim()
      ? customer.first_name.trim().charAt(0).toUpperCase() +
          customer.last_name.trim().charAt(0).toUpperCase()
      : customer.first_name.trim().slice(0, 2).toUpperCase();
  }

  private generateInitialShortCode(customer: CustomerDocument): string {
    if (customer.first_name === 'anonymous')
      return this.generateRandomShortCode();

    const codeStart = this.getCodeStart(customer);

    const isValidCodeStart = /[A-Z][A-Z]/.test(codeStart);
    if (!isValidCodeStart) return this.generateRandomShortCode();

    const codeEnd = customer.phone.slice(-3);
    return codeStart + codeEnd;
  }

  private generateRandomShortCode(): string {
    const codeStart = randomstring.generate({
      length: 2,
      charset: 'alphabetic',
      capitalization: 'uppercase',
    });
    const codeEnd = randomstring.generate({ length: 3, charset: 'numeric' });
    return codeStart + codeEnd;
  }
}
