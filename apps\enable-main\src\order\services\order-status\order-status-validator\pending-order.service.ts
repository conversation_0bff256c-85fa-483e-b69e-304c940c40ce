import {
  CurrentUser,
  OrderDeliveryType,
  OrderDocument,
  OrderLogActionEnum,
  OrderStatusEnum,
  OrderTransitionTrigger,
  responseCode,
} from '@app/shared-stuff';
import { BadRequestException, Inject } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { OrderLogServiceInterface } from '../../interfaces/order-log.service.interface';
import { OrderNotificationService } from '../../order-notification/order-notification.service';
import { OrderStatusServiceInterface } from '../order-status.interface';
import { OrderStatusService } from '../order-status.service';

export class PendingOrderService implements OrderStatusServiceInterface {
  transitionalStatuses = [
    OrderStatusEnum.UNASSIGNED,
    OrderStatusEnum.SCHEDULED,
    OrderStatusEnum.PENDING,
    OrderStatusEnum.PREPARING,
  ];
  transitionalTrigger = [
    OrderTransitionTrigger.CREATED,
    OrderTransitionTrigger.ASSIGNED_TO_BRANCH,
    OrderTransitionTrigger.REMINDER_TIME,
    OrderTransitionTrigger.AUTOMATIC_ACKNOWLEDGEMENT,
  ];

  constructor(
    @Inject('OrderLogServiceInterface')
    private readonly orderLogService: OrderLogServiceInterface,
    private orderNotificationService: OrderNotificationService,
    private orderStatusService: OrderStatusService,
    private eventEmitter: EventEmitter2,
  ) {}

  validateStatus(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
  ) {
    return (
      this.ValidateTransition(order, orderTransitionTrigger) &&
      this.validatePreCondition(order)
    );
  }

  private ValidateTransition(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
  ): boolean {
    if (
      this.transitionalStatuses.includes(order.status) &&
      this.transitionalTrigger.includes(orderTransitionTrigger)
    )
      return true;

    throw new BadRequestException(
      "Can't Change From " + order.status + ' To Pending',
      responseCode.STATUS_NOT_VALID.toString(),
    );
  }

  private validatePreCondition(order: OrderDocument): boolean {
    if (order.branch) return true;

    throw new BadRequestException(
      "Can't change From " +
        order.status +
        ' to  Pending' +
        'The order with ID ' +
        order._id.toHexString() +
        ' is not assigned to a branch',
      responseCode.STATUS_NOT_VALID.toString(),
    );
  }

  async applyPostFunction(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
    oldStatus: OrderStatusEnum,
    user: CurrentUser,
  ) {
    if (orderTransitionTrigger == OrderTransitionTrigger.ASSIGNED_TO_BRANCH) {
      await this.orderNotificationService.onOrderAssigned(
        order,
        typeof order.branch !== 'string' ? order.branch : undefined,
      );
      this.eventEmitter.emit('order.branch.assigned', order);
    } else if (orderTransitionTrigger == OrderTransitionTrigger.CREATED) {
      await this.orderNotificationService.onOrderCreatedWithBranch(
        order,
        typeof order.branch !== 'string' ? order.branch : undefined,
        order.createdBy,
      );
    }

    order.delayTag = undefined;
    await order.save();

    await this.orderLogService.saveOrderLog(
      order,
      { oldStatus: oldStatus },
      { newStatus: OrderStatusEnum.PENDING, trigger: orderTransitionTrigger },
      OrderLogActionEnum.ON_ORDER_PENDING,
      user,
    );

    // By definition, a PENDING order status indicates that an order is waiting
    // for acknowledgement. If an order is already acknowledged, such as in the
    // case where the company is using automatic order acknowledgement, we
    // override this illegal state by transitioning to the next order status.
    if (order.isAcknowledged) {
      const nextOrderStatus =
        order.delivery_type === OrderDeliveryType.urgent
          ? OrderStatusEnum.PREPARING
          : OrderStatusEnum.SCHEDULED;

      await this.orderStatusService.changeOrderStatus(
        order,
        nextOrderStatus,
        OrderTransitionTrigger.AUTOMATIC_ACKNOWLEDGEMENT,
        user,
      );
    }
  }
}
