import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model, Types } from 'mongoose';

import {
  GenericRepository,
  LoyaltyPointLog,
  LoyaltyPointLogDocument,
  PopulatedLoyaltyPointLog,
} from '@app/shared-stuff';
import { LoyaltyPointLogRepositoryInterface } from './loyalty-point-log.repository.interface';

@Injectable()
export class LoyaltyPointLogRepository
  extends GenericRepository<LoyaltyPointLogDocument, LoyaltyPointLog>
  implements LoyaltyPointLogRepositoryInterface
{
  constructor(
    @InjectModel(LoyaltyPointLog.name)
    private loyaltyPointLogModel: Model<
      LoyaltyPointLogDocument,
      LoyaltyPointLog
    >,
  ) {
    super(loyaltyPointLogModel);
  }

  async findByCustomerId(
    customerId: Types.ObjectId,
  ): Promise<PopulatedLoyaltyPointLog[]> {
    return await this.loyaltyPointLogModel
      .find({
        deletedAt: null,
        customerId,
      })
      .populate('brandId', 'name')
      .populate('branchId', 'name')
      .populate('orderId', 'code source');
  }

  async exists(filter: FilterQuery<LoyaltyPointLog>): Promise<boolean> {
    return !!(await this.loyaltyPointLogModel.exists(filter));
  }
}
