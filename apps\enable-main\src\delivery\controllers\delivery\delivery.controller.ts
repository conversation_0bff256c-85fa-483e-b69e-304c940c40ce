import { DeliveryService } from './../../services/delivery/delivery.service';
import { Response } from 'express';
import { HelperService } from './../../../shared/services/helper/helper.service';
import { Body, Controller, Post, Res, SetMetadata } from '@nestjs/common';
import { NationalAddressToGet } from '../../../delivery/dto/delivery.dto';
import { responseCode } from '@app/shared-stuff';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';

@Controller('delivery')
@ApiTags('Delivery')
@SetMetadata('module', 'delivery')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class DeliveryController {
  constructor(
    private helperService: HelperService,
    private deliveryService: DeliveryService,
  ) {}

  @Post('national/address')
  @SetMetadata('public', 'true')
  async get_national_address(
    @Body() nationalAddressToGet: NationalAddressToGet,
    @Res() res: Response,
  ) {
    try {
      const nationalAddress =
        await this.deliveryService.get_national_address(nationalAddressToGet);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'success to get national address',
        nationalAddress,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
}
