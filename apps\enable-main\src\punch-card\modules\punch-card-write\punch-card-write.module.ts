import { Modu<PERSON> } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';

import { CompanyModule } from '../../../company/company.module';
import { PassesModule } from '../../../passes/passes.module';
import { RestaurantModule } from '../../../restaurant/restaurant.module';
import { SharedModule } from '../../../shared/shared.module';
import { PunchCardReadModule } from '../punch-card-read/punch-card-read.module';
import { PunchCardRepositoryModule } from '../punch-card-repository/punch-card-repository.module';
import { PunchCardWriteService } from './punch-card-write.service';

@Module({
  imports: [
    PunchCardRepositoryModule,
    PunchCardReadModule,
    SharedModule,
    EventEmitterModule,
    RestaurantModule,
    CompanyModule,
    PassesModule,
  ],
  providers: [PunchCardWriteService],
  exports: [PunchCardWriteService],
})
export class PunchCardWriteModule {}
