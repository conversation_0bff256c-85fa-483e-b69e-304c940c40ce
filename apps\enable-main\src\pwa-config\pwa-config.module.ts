import { CollectionName } from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { PwaConfigSchema } from '../../../../libs/shared-stuff/src/models/pwa-config.model';
import { PwaConfigController } from './controllers/pwa-config.controller';
import { PwaConfigService } from './services/pwa-config.service';
import { PwaConfigRepository } from './repositories/pwa-config.repository';
import { CloudflarePWAService } from './services/cloudflare/cloudflare-pwa.service';
import { StorageModule } from '../storage/storage.module';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { BrandModule } from '../brand/brand.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: CollectionName.PWA_CONFIG, schema: PwaConfigSchema },
    ]),
    StorageModule,
    HttpModule,
    ConfigModule,
    BrandModule,
  ],
  controllers: [PwaConfigController],
  providers: [PwaConfigService, PwaConfigRepository, CloudflarePWAService],
  exports: [PwaConfigService],
})
export class PwaConfigModule {}
