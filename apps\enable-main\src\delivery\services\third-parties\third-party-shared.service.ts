import {
  BranchWithId,
  CompanyDocument,
  Customer,
  DriverDocument,
  OrderDocument,
  OrderPaymentMethod,
  OrderPaymentStatus,
  SavedLocation,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import * as randomestring from 'randomstring';
import { SavedLocationService } from '../../../location/services/saved-location/saved-location.service';
import { LocationType } from '../../../shared/enums/location-type.enum';
import { HelperService } from '../../../shared/services/helper/helper.service';
import { ThirdPartyTaskCreationDto } from '../../dto/third-party-task-creation.dto';
import {
  CreateBothTookanTaskDto,
  CreateDeliveryTookanTaskDto,
  CreateTookanTaskDto,
} from '../../dto/tookan.dto';
import { TookanService } from '../tookan/tookan.service';

@Injectable()
export class ThirdPartySharedService {
  constructor(
    private savedLocationService: SavedLocationService,
    private helperService: HelperService,
    private configService: ConfigService,
    private tookanService: TookanService,
  ) {}

  async getPickupLocation(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
  ): Promise<SavedLocation> {
    let pickupLocation: SavedLocation = undefined;
    if (!thirdPartyTaskCreationDto.pickupLocation) {
      pickupLocation = await this.savedLocationService.getDetails(
        thirdPartyTaskCreationDto.order.pickupLocationId
          ? thirdPartyTaskCreationDto.order.pickupLocationId.toHexString()
          : thirdPartyTaskCreationDto.branch
            ? thirdPartyTaskCreationDto.branch.locationId.toHexString()
            : new Types.ObjectId().toHexString(),
      );
    } else pickupLocation = thirdPartyTaskCreationDto.pickupLocation;
    return pickupLocation;
  }

  async getPickupAddressString(pickupLocation: SavedLocation): Promise<string> {
    let pickupAddressString = 'Please Contact Branch For Further Information';
    if (pickupLocation) {
      pickupAddressString = this.helperService.convertLocationToString(
        pickupLocation,
        LocationType.PICKUP,
      );
    }
    return pickupAddressString;
  }

  async getDeliveryLocation(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
  ) {
    if (!thirdPartyTaskCreationDto.order?.deliveryLocation) {
      return await this.getPickupLocation(thirdPartyTaskCreationDto);
    }
    return thirdPartyTaskCreationDto.order?.deliveryLocation as SavedLocation;
  }

  async getDeliveryAddressString(
    deliveryLocation: SavedLocation,
  ): Promise<string> {
    let deliveryAddressString =
      'Please Contact Customer For Further Information';
    if (deliveryLocation) {
      deliveryAddressString = await this.helperService.convertLocationToString(
        deliveryLocation,
        LocationType.DELIVERY,
      );
    }
    return deliveryAddressString;
  }

  async createTookanTask(
    order: OrderDocument,
    customer: Customer,
    branch: BranchWithId,
    company: CompanyDocument,
    driver: DriverDocument,
    customTask = false,
    customTeamId = '',
  ) {
    const deliveryLocation = await this.getLocationDetails(
      order.deliveryLocationId,
    );
    const pickupLocation = await this.getLocationDetails(
      order.pickupLocationId,
    );

    if (company.has_delivery_system || customTask) {
      const taskToCreate = this.prepareTaskDto(
        order,
        customer,
        branch,
        company,
        driver,
        customTask,
        customTeamId,
        pickupLocation,
        deliveryLocation,
      );

      const createdTask =
        await this.tookanService.createTookanTask(taskToCreate);

      this.updateOrderWithTaskDetails(order, createdTask);
    }

    order.deliveryTaskCreated = true;
    await order.save();
    return order;
  }

  getTookanTransport(vehicleType: string) {
    const transportMapping = {
      car: 1,
      motor_cycle: 2,
      bicycle: 3,
      scooter: 4,
      foot: 5,
      truck: 6,
    };
    return transportMapping[vehicleType];
  }

  private prepareTaskDto(
    order: OrderDocument,
    customer: Customer,
    branch: BranchWithId,
    company: CompanyDocument,
    driver: DriverDocument,
    customTask: boolean,
    customTeamId: string,
    pickupLocation: SavedLocation | null,
    deliveryLocation: SavedLocation | null,
  ) {
    const deliveryAddress = this.formatAddress(
      deliveryLocation,
      'Please Contact Customer For Further Information',
      LocationType.DELIVERY,
    );
    const pickupAddress = this.formatAddress(
      pickupLocation,
      'Please Contact Branch For Further Information',
      LocationType.PICKUP,
    );

    const createTookanTaskDto: CreateTookanTaskDto = {
      api_key: this.configService.get('TOOKAN_API_KEY'),
      order_id: order.code,
      auto_assignment: order.autoAssign || customTask ? 1 : 0,
      job_description: order.order_remarks,
      team_id: customTask
        ? customTeamId
        : driver
          ? driver.team_id
          : company.tookan_team_id
            ? company.tookan_team_id
            : '409744',
      fleet_id: driver ? driver.tookan_driver_id : '',
      barcode: order.barCode,
      tags: order.autoAssign ? `${company._id}` : '',
      timezone: moment.tz
        .zone(
          order?.localization?.timezone
            ? order?.localization?.timezone
            : 'Asia/Qatar',
        )
        .utcOffset(moment.utc().valueOf()),
      layout_type: 0,
      custom_field_template: 'Pickup_&_Delivery_Enable',
      transport_type: this.getTookanTransport(order.vehicleType),
    };

    const createDeliveryTookanTaskDto: CreateDeliveryTookanTaskDto = {
      ...createTookanTaskDto,
      customer_email:
        customer && customer.email
          ? customer.email
          : 'default' + randomestring.generate(5) + '@e-butler.com',
      customer_username: order.is_gift
        ? order.recipient_name
        : order.customer_name,
      customer_phone: order.is_gift
        ? order.recipient_country_code + order.recipient_phone
        : order.country_code + order.customer_phone,

      customer_address: `${deliveryAddress} - ${order.code}`,

      latitude: deliveryLocation ? deliveryLocation?.latitude?.toString() : '0',
      longitude: deliveryLocation
        ? deliveryLocation?.longitude?.toString()
        : '0',

      job_delivery_datetime: moment
        .utc(order.delivery_date)
        .clone()
        .tz(
          order.localization?.timezone
            ? order.localization?.timezone
            : 'Asia/Qatar',
        )
        .toString() as any,

      has_delivery: 1,
      has_pickup: 0,
      layout_type: 0,

      meta_data: this.constructTookanTaskMetadata(order),
    };

    const createBothTookanTaskDto: CreateBothTookanTaskDto = {
      ...createDeliveryTookanTaskDto,
      has_pickup: 1,
      job_pickup_phone: branch ? branch.phone : company.phone,
      job_pickup_name: branch ? branch.name : company.name,
      job_pickup_email: branch ? branch.email : company.email,
      job_pickup_address: `${pickupAddress} - ${order.code}`,
      job_pickup_latitude: pickupLocation
        ? pickupLocation?.latitude?.toString()
        : '0',
      job_pickup_longitude: pickupLocation
        ? pickupLocation?.longitude?.toString()
        : '0',

      job_pickup_datetime: moment
        .utc(order.pickup_date)
        .clone()
        .tz(
          order.localization?.timezone
            ? order.localization?.timezone
            : 'Asia/Qatar',
        )
        .toString() as any,
      pickup_custom_field_template: 'Pickup_&_Delivery_Enable',
      pickup_meta_data: this.constructTookanTaskMetadata(order),
    };

    const shouldCreatePickupTask = true;

    return shouldCreatePickupTask
      ? createBothTookanTaskDto
      : createDeliveryTookanTaskDto;
  }

  private updateOrderWithTaskDetails(order: OrderDocument, createdTask: any) {
    order.tookan_job_id = createdTask['job_id'];

    order.tookan_delivery_track_url =
      createdTask['delivery_tracing_link'] || createdTask['tracking_link'];

    order.tookan_pickup_track_url = createdTask['pickup_tracking_link'];

    order.deliveryJobId = createdTask['delivery_job_id']
      ? createdTask['delivery_job_id']
      : createdTask['job_id'];
    order.pickupJobId = createdTask['pickup_job_id'];
  }

  private async getLocationDetails(locationId: Types.ObjectId | undefined) {
    return this.savedLocationService.getDetails(
      locationId
        ? locationId.toHexString()
        : new Types.ObjectId().toHexString(),
    );
  }

  private formatAddress(
    location: SavedLocation | null,
    defaultMsg: string,
    locationType: LocationType,
  ) {
    return location
      ? this.helperService.convertLocationToString(location, locationType)
      : defaultMsg;
  }

  private constructTookanTaskMetadata(order: OrderDocument) {
    const isPaidAlready =
      order.payment_method === OrderPaymentMethod.online &&
      order.payment_status === OrderPaymentStatus.COMPLETED;

    return [
      {
        label: 'Payment_Amount',
        data: isPaidAlready ? 'Paid Already' : order.total_amount.toString(),
      },
      {
        label: 'Payment_Method',
        data: isPaidAlready
          ? 'Online'
          : this.formatPaymentMethod(order.payment_method),
      },
      {
        label: 'Special_Instruction',
        data: order.order_remarks,
      },
      {
        label: 'Order_Is_Gift',
        data: order.is_gift ? 'Yes' : 'No',
      },
    ];
  }

  private formatPaymentMethod(paymentMethod: OrderPaymentMethod) {
    const paymentMethodMapping = {
      [OrderPaymentMethod.online]: 'Cash',
      [OrderPaymentMethod.cash]: 'Cash',
      [OrderPaymentMethod.card_machine]: 'Card Machine',
      [OrderPaymentMethod.prepaid]: 'Prepaid',
    };

    return paymentMethodMapping[paymentMethod]
      ? paymentMethodMapping[paymentMethod]
      : paymentMethod;
  }
}
