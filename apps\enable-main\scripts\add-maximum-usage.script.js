// regarding redo maximum usage EBL-5193
// do not run that on the staging or prod
db.customers
  .find({
    loyaltyTier: { $exists: true, $ne: null },
    'loyaltyTier.rewards': { $exists: true, $ne: null, $type: 'array' },
  })
  .forEach(function (customer) {
    db.customers.updateOne(
      { _id: customer._id },
      { $unset: { 'loyaltyTier.rewards': '' } },
    );
  });

db.getCollection('customers').updateMany(
  { rewards: { $type: 'array', $exist: true, $ne: null } },
  { $pull: { rewards: { source: 'loyalty_tier' } } },
);

db.loyaltytiers.updateMany(
  {
    rewards: { $exists: true, $ne: null, $type: 'array' },
  },
  {
    $unset: { rewards: '' },
  },
);
// run the following on staging & prod
db.getCollection('customers').updateMany(
  { loyaltyTier: { $exists: true, $ne: null } },
  {
    $set: {
      'loyaltyTier.percentDiscountMaximumNumberOfUsesType':
        'until_they_lose_their_tier',
      'loyaltyTier.freeDeliveryMaximumNumberOfUsesType':
        'until_they_lose_their_tier',
    },
  },
);

db.getCollection('loyaltytiers').updateMany(
  {},
  {
    $set: {
      percentDiscountMaximumNumberOfUsesType: 'until_they_lose_their_tier',
      freeDeliveryMaximumNumberOfUsesType: 'until_they_lose_their_tier',
    },
  },
);
