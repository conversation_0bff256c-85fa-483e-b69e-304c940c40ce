// EBL-5536 Prepaid Payment Method Options Configuration
// Migrate order.isPrepaidByEnable to order.prepaidBy

db.orders.updateMany(
  { payment_method: 'prepaid', isPrepaidByEnable: false },
  { $set: { prepaidBy: 'customer' } },
);

db.orders.updateMany(
  { payment_method: 'prepaid', isPrepaidByEnable: true },
  { $set: { prepaidBy: 'enable' } },
);

// Run after frontend merged & other commands succeeded
// db.orders.updateMany({}, [{ $unset: 'isPrepaidByEnable' }]);
