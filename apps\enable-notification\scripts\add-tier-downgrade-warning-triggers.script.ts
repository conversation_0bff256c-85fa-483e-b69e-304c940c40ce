// EBL-3921 [Loyalty Program] Tier Downgrade Warning
// Init TIER_DOWNGRADE_WARNING_EXCEEDED_THRESHOLD AN TIER_DOWNGRADE_WARNING_BELOW_THRESHOLD triggers

db.triggers.insertMany([
  {
    name: '[CUSTOMER] TIER_DOWNGRADE_WARNING_BELOW_THRESHOLD',
    client: 'ENABLE_MAIN',
    action: 'TIER_DOWNGRADE_WARNING_BELOW_THRESHOLD',
    module: 'CUSTOMER',
    replacement: [
      'firstName',
      'fullName',
      'loyaltyPointBalance',
      'upperLoyaltyTier',
      'loyaltyTier',
      'lowerTier',
      'remainingOrdersCurrentTier',
      'remainingOrdersUpperTier',
      'upperTierDiscountValue',
      'tierDiscountValue',
      'tierDiscountOrderValueThreshold',
      'loyaltyRegistrationPageLink',
      'walletPassAccessPageLink',
      'walletPassLink',
      'ordableLink',
      'gracePeriodRemainingDays',
    ],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    name: '[CUSTOMER] TIER_DOWNGRADE_WARNING_EXCEEDED_THRESHOLD',
    client: 'ENABLE_MAIN',
    action: 'TIER_DOWNGRADE_WARNING_EXCEEDED_THRESHOLD',
    module: 'CUSTOMER',
    replacement: [
      'firstName',
      'fullName',
      'loyaltyPointBalance',
      'loyaltyTier',
      'upperLoyaltyTier',
      'lowerTier',
      'remainingOrdersCurrentTier',
      'remainingOrdersUpperTier',
      'upperTierDiscountValue',
      'tierDiscountValue',
      'tierDiscountOrderValueThreshold',
      'loyaltyRegistrationPageLink',
      'walletPassAccessPageLink',
      'walletPassLink',
      'ordableLink',
      'gracePeriodRemainingDays',
    ],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
]);
