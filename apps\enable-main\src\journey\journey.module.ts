import { MicroserviceCommunicationModule } from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { BrandModule } from '../brand/brand.module';
import { SharedModule } from '../shared/shared.module';
import { ActionGroupController } from './controllers/action-group.controller';
import { JourneyEventController } from './controllers/journey-event.controller';
import { JourneyController } from './controllers/journey.controller';
import { MessageVariantController } from './controllers/message-variant.controller';
import { ActionGroupService } from './services/action/action-group.service';
import { ActionGroupServiceInterface } from './services/action/action-group.service.interface';
import { JourneyEventService } from './services/journey-event/journey-event.service';
import { JourneyEventServiceInterface } from './services/journey-event/journey-event.service.interface';
import { JourneyService } from './services/journey/journey.service';
import { JourneyServiceInterface } from './services/journey/journey.service.interface';
import { MessageVariantService } from './services/message-variant/message-variant.service';
import { MessageVariantServiceInterface } from './services/message-variant/message-variant.service.interface';
import { ActionVariantController } from './controllers/action-variant.controller';
import { ActionVariantServiceInterface } from './services/action-variant/action-variant.service.interface';
import { ActionVariantService } from './services/action-variant/action-variant.service';

@Module({
  imports: [
    SharedModule,
    BrandModule,
    MicroserviceCommunicationModule.forChild(
      'enable-main-notification-producer',
    ),
    EventEmitterModule,
  ],
  providers: [
    { provide: JourneyServiceInterface, useClass: JourneyService },
    { provide: JourneyEventServiceInterface, useClass: JourneyEventService },
    { provide: ActionGroupServiceInterface, useClass: ActionGroupService },
    {
      provide: MessageVariantServiceInterface,
      useClass: MessageVariantService,
    },
    {
      provide: ActionVariantServiceInterface,
      useClass: ActionVariantService,
    },
  ],
  controllers: [
    JourneyController,
    JourneyEventController,
    ActionGroupController,
    MessageVariantController,
    ActionVariantController,
  ],
})
export class JourneyModule {}
