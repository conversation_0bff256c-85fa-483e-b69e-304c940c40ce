import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsNotEmpty, IsString } from 'class-validator';

export class UpdateOrdableCategoriesDto {
  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  @IsInt()
  id: number;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsString()
  name: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsString()
  ar_name: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsString()
  description: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsString()
  ar_description: string;

  @ApiProperty({
    type: Number,
    required: false,
    description: 'The id of the parent category. Subcategories must be enabled',
  })
  @IsInt()
  parent: number;

  @ApiProperty({
    type: String,
    required: false,
    description: 'null will delete the photo if exists',
  })
  photo: string;

  @ApiProperty({
    type: Boolean,
    required: false,
  })
  @IsBoolean()
  is_active: boolean;

  @ApiProperty({
    type: Number,
    required: false,
  })
  @IsInt()
  sort_order: number;
}
