import { FalconFlexPaymentStatus } from '../enums/falcon-flex-payment-status.enum';
import { FalconFlexTaskStatus } from '../enums/falcon-flex-task-status.enum';
import { FalconFlexAgentTask } from './falcon-flex-agent-task.type';
import { FalconFlexAutoAssignment } from './falcon-flex-auto-assignment.type';
import { FalconFlexItem } from './falcon-flex-item.type';
import { FalconFlexLocation } from './falcon-flex-location.type';
import { FalconFlexMetaDataField } from './falcon-flex-meta-data-field.type';
import { FalconFlexSkillCriteria } from './falcon-flex-skill-criteria.type';

export class FalconFlexData {
  Id: string;
  ShortId: string;
  ClientGeneratedId: string;
  AgentId?: string;
  CompanyId?: string;
  TripId?: string;
  TripStepTypeId?: number;
  Task: FalconFlexAgentTask;
  DeliveryFee: number;
  CreatedAtUtc: string;
  UpdatedAtUtc: string;
  CreatedBy: string;
  UpdatedBy: string;
  PickupByUtc: string;
  DeliverByUtc: string;
  EstimatedPickupByUtc: string;
  EstimatedDeliveryByUtc: string;
  EstimatedDistanceRemainingToPickupKm: number;
  EstimatedDistanceRemainingToDeliverKm: number;
  EtaToCompletionMinFromUtcNow: number;
  OwningCompanyName: string;
  OwningCompanyId: string;
  ExecutingCompanyId: string;
  PaymentStatus: FalconFlexPaymentStatus;
  TaskStatus: FalconFlexTaskStatus;
  Pickup: FalconFlexLocation;
  Delivery: FalconFlexLocation;
  CollectionAmount: number;
  TaskItems: FalconFlexItem[];
  AgentTripDto: any; // There is no documentation for the webhook and did not receive any data in this object
  AutoAssignmentDto: FalconFlexAutoAssignment;
  SkillCriteriaDto: FalconFlexSkillCriteria;
  MetaDataFields: FalconFlexMetaDataField[];
  TransportTypeId: number;
}
