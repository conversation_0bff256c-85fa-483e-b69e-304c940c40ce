import { GenericRepository, LoggerService } from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  ErrorBody,
  IntegrationLog,
  IntegrationLogDocument,
} from '../models/integration.log.model';
import { IntegrationLogRepositoryInterface } from './interfaces/integration-log.repository.interface';

@Injectable()
export class IntegrationLogRepository
  extends GenericRepository<IntegrationLogDocument, IntegrationLog>
  implements IntegrationLogRepositoryInterface
{
  private readonly logger = new LoggerService(IntegrationLogRepository.name);

  constructor(
    @InjectModel(IntegrationLog.name)
    private integrationLogModel: Model<IntegrationLogDocument, IntegrationLog>,
  ) {
    super(integrationLogModel);
  }

  async logSuccess(
    action: string,
    requestBody: Record<string, any>,
    responseBody: Record<string, any>,
    itemId: string | Types.ObjectId,
    requestHeaders?: Record<string, any>,
  ) {
    const logToCreate: IntegrationLog = {
      action,
      requestBody,
      requestHeaders,
      responseBody,
      itemId: itemId?.toString(),
      requestStatus: 'success',
    };
    await this.integrationLogModel.create(logToCreate);

    this.logger.log(`Integration Log: ${action}`, logToCreate);
  }

  async logError(
    action: string,
    requestBody: Record<string, any>,
    requestHeaders: Record<string, any>,
    responseBody: Record<string, any>,
    errorBody?: ErrorBody,
  ) {
    const logToCreate: IntegrationLog = {
      action,
      requestBody,
      requestHeaders,
      responseBody,
      errorBody,
      requestStatus: 'failure',
    };

    await this.integrationLogModel.create(logToCreate);
    this.logger.error(`Integration Log: ${action}`, logToCreate);
  }
}
