import { clamp, CustomerEarnedBenefit } from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { BenefitConcreteInterface } from './benefit-concrete.interface';

@Injectable()
export class FixedAmountConcreteService implements BenefitConcreteInterface {
  constructor() {}

  redeem(benefit: CustomerEarnedBenefit, amount: number): [number, number] {
    return [
      clamp(0, amount - benefit.value, amount),
      clamp(0, benefit.value, amount),
    ];
  }
}
