import { Types } from 'mongoose';
import { CreatePaymentConfigurationDto } from '../../../../../../libs/shared-stuff/src/dtos/payment-configuration/create-payment-configuration.dto';
import { UpdatePaymentConfigurationDto } from '../../../../../../libs/shared-stuff/src/dtos/payment-configuration/update-payment-configuration.dto';
import { PaymentConfigurationDocument } from '../../../../../../libs/shared-stuff/src/models/payment.configuration.model';

export interface PaymentConfigurationServiceInterface {
  create(
    createPaymentConfigurationDto: CreatePaymentConfigurationDto,
  ): Promise<PaymentConfigurationDocument>;
  update(
    updatePaymentConfigurationDto: UpdatePaymentConfigurationDto,
  ): Promise<PaymentConfigurationDocument>;
  findPaymentConfig(
    branchId?: Types.ObjectId,
    brandId?: Types.ObjectId,
    companyId?: Types.ObjectId,
  ): Promise<PaymentConfigurationDocument>;
  findPaymentConfigByIdentifier(
    id: Types.ObjectId,
  ): Promise<PaymentConfigurationDocument>;
  findPublicPaymentConfigByIdentifier(
    id: Types.ObjectId,
  ): Promise<PaymentConfigurationDocument>;
}

export const PaymentConfigurationServiceInterface = Symbol(
  'PaymentConfigurationServiceInterface',
);
