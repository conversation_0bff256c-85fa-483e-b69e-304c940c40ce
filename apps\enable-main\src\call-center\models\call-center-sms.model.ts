import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type CallCenterSmsSentDocument = HydratedDocument<CallCenterSmsSent>;
@Schema({ timestamps: true })
export class CallCenterSmsSent {
  @Prop({
    required: true,
    trim: true,
  })
  calleeNumber: string;

  @Prop({
    required: true,
    trim: true,
  })
  callerNumber: string;
}
const CallCenterSmsSentSchema = SchemaFactory.createForClass(CallCenterSmsSent);

export { CallCenterSmsSentSchema };
