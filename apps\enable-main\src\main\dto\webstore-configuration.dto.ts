import {
  BranchDocument,
  CustomerDocument,
  LanguageCode,
} from '@app/shared-stuff';
import { Brand } from '@app/shared-stuff/models/brand.model';
import { CompanyDocument } from '@app/shared-stuff/models/company.model';

export class WebStoreConfigurationDto {
  company: CompanyDocument;
  brand: Brand;
  branch: BranchDocument;
  customer: CustomerDocument;
  senderId: string;
  language: LanguageCode;
  webStoreLink: string;
}
