import { Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { IsString } from 'class-validator';
import { DeliveryThirdPartyName, EnumField, Field } from '@app/shared-stuff';
import { DeliverySettlementParty } from '../enums/delivery-settlement-party.enum';
import { DeliverySettlementSchema } from '../enums/delivery-settlement-schema.enum';

export type DeliveryThirdPartyDocument = DeliveryThirdParty & Document;

@Schema({ timestamps: true })
export class DeliveryThirdParty {
  @Field({
    type: String,
    additionalDecorators: [IsString()],
  })
  internalName: string;

  @EnumField({
    type: String,
    required: true,
    enum: DeliveryThirdPartyName,
  })
  name: DeliveryThirdPartyName;

  @EnumField({
    type: String,
    required: true,
    enum: DeliverySettlementParty,
  })
  settlementParty: DeliverySettlementParty;

  @EnumField({
    type: String,
    required: true,
    enum: DeliverySettlementSchema,
  })
  settlementSchema: DeliverySettlementSchema;
}
export const DeliveryThirdPartySchema =
  SchemaFactory.createForClass(DeliveryThirdParty);
