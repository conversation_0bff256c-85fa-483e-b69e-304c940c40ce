import { EmailRepositoryInterface } from './email.repository.interface';
import { Injectable } from '@nestjs/common';
import { GenericRepository } from '@app/shared-stuff';
import { Email, EmailDocument } from '../models/email.model';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
@Injectable()
export class EmailRepository
  extends GenericRepository<EmailDocument, Email>
  implements EmailRepositoryInterface
{
  constructor(
    @InjectModel(Email.name) private emailModel: Model<EmailDocument, Email>,
  ) {
    super(emailModel);
  }
}
