import {
  Create<PERSON>ourneyEventDto,
  JourneyDocument,
  MicroserviceCommunicationService,
  UpdateJourneyEventDto,
} from '@app/shared-stuff';
import {
  Inject,
  Injectable,
  OnModuleDestroy,
  OnModuleInit,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { Types } from 'mongoose';
import { JourneyEventServiceInterface } from './journey-event.service.interface';

@Injectable()
export class JourneyEventService
  implements JourneyEventServiceInterface, OnModuleInit, OnModuleDestroy
{
  constructor(
    @Inject('enable-main-notification-producer')
    private client: ClientProxy,
    private microserviceCommunicationService: MicroserviceCommunicationService,
  ) {}

  onModuleInit() {
    this.microserviceCommunicationService.connect(this.client);
  }

  onModuleDestroy() {
    this.microserviceCommunicationService.disconnect(this.client);
  }

  async create(
    createJourneyEventDto: CreateJourneyEventDto,
  ): Promise<JourneyDocument> {
    return await this.microserviceCommunicationService.produceAndWaitForResponse(
      this.client,
      'journey-event.create.request',
      createJourneyEventDto,
    );
  }

  async update(
    updateJourneyEventDto: UpdateJourneyEventDto,
  ): Promise<JourneyDocument> {
    return await this.microserviceCommunicationService.produceAndWaitForResponse(
      this.client,
      'journey-event.update.request',
      updateJourneyEventDto,
    );
  }

  async delete(eventId: Types.ObjectId): Promise<number> {
    return await this.microserviceCommunicationService.produceAndWaitForResponse(
      this.client,
      'journey-event.delete.request',
      { eventId },
    );
  }
}
