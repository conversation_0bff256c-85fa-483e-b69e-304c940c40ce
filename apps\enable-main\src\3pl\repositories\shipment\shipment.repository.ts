import { Injectable } from '@nestjs/common';
import {
  CollectionName,
  GenericRepository,
  Shipment,
  ShipmentDocument,
} from '@app/shared-stuff';
import { ShipmentRepositoryInterface } from './shipment-repository.interface';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model, Types } from 'mongoose';

@Injectable()
export class ShipmentRepository
  extends GenericRepository<ShipmentDocument, Shipment>
  implements ShipmentRepositoryInterface
{
  constructor(
    @InjectModel(CollectionName.SHIPMENT)
    private shipmentModel: Model<ShipmentDocument, Shipment>,
  ) {
    super(shipmentModel);
  }

  async findByTrackingNumber(
    trackingNumber: string,
  ): Promise<ShipmentDocument> {
    return this.shipmentModel.findOne({ trackingNumber }).exec();
  }

  async findOneByTrackingNumberOrId(
    idOrTrackingNumber: string,
  ): Promise<ShipmentDocument> {
    const match: FilterQuery<ShipmentDocument> = { $or: [] };

    if (Types.ObjectId.isValid(idOrTrackingNumber)) {
      match.$or.push({ _id: idOrTrackingNumber });
    }

    match.$or.push({ trackingNumber: idOrTrackingNumber });
    return this.shipmentModel.findOne(match).exec();
  }
}
