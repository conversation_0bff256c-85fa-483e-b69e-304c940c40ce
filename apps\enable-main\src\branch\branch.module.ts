import {
  BranchSchema,
  CollectionName,
  SharedStuffModule,
} from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { MongooseModule } from '@nestjs/mongoose';
import { BrandModule } from '../brand/brand.module';
import { CompanyModule } from '../company/company.module';
import { DeliveryModule } from '../delivery/delivery.module';
import { LocationModule } from '../location/location.module';
import { SharedModule } from '../shared/shared.module';
import { UserModule } from '../user/user.module';
import { BranchController } from './controllers/branch/branch.controller';
import { BranchService } from './services/branch/branch.service';

@Module({
  controllers: [BranchController],
  providers: [BranchService],
  imports: [
    SharedModule,
    SharedStuffModule,
    CompanyModule,
    UserModule,
    MongooseModule.forFeature([
      { name: CollectionName.BRANCH, schema: BranchSchema },
    ]),
    EventEmitterModule,
    LocationModule,
    BrandModule,
    DeliveryModule,
  ],
  exports: [BranchService, MongooseModule],
})
export class BranchModule {}
