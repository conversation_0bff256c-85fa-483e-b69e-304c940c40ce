import {
  Discount,
  DiscountApplyTo,
  DiscountSource,
  DiscountType,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { OrderInvoice } from '../../types/order-invoice.type';

@Injectable()
export class OrderInvoiceService {
  constructor() {}

  public computeFromInvoicedAmount(
    invoicedAmount: number,
    deliveryAmount: number = 0,
    discounts: Discount[] = [],
  ): OrderInvoice {
    const cartDiscounts = (discounts || []).filter(
      (discount) => discount.applyTo === DiscountApplyTo.CART,
    );
    const total_discount = this.getTotalDiscount(invoicedAmount, cartDiscounts);
    const total_amount_after_discount = Math.max(
      0,
      invoicedAmount - total_discount,
    );

    const deliveryDiscounts = (discounts || []).filter(
      (discount) => discount.applyTo === DiscountApplyTo.DELIVERY,
    );
    const deliveryDiscount = this.getTotalDiscount(
      deliveryAmount,
      deliveryDiscounts,
    );
    const deliveryAmountAfterDiscount = Math.max(
      0,
      deliveryAmount - deliveryDiscount,
    );
    const total_amount =
      total_amount_after_discount + deliveryAmountAfterDiscount;

    return {
      invoiced_amount: invoicedAmount,
      delivery_amount: deliveryAmount,
      discounts,
      total_discount,
      total_amount_after_discount,
      total_amount,
    };
  }

  public computeFromPaidAmount(
    paidAmount: number,
    deliveryAmount: number = 0,
    discounts: Discount[] = [],
  ): OrderInvoice {
    const deliveryDiscounts = (discounts || []).filter(
      (discount) => discount.applyTo === DiscountApplyTo.DELIVERY,
    );
    const deliveryDiscount = this.getTotalDiscount(
      deliveryAmount,
      deliveryDiscounts,
    );
    const deliveryAmountAfterDiscount = Math.max(
      0,
      deliveryAmount - deliveryDiscount,
    );

    const cartDiscounts = (discounts || []).filter(
      (discount) => discount.applyTo === DiscountApplyTo.CART,
    );
    const totalAmount = paidAmount - deliveryAmountAfterDiscount;
    const invoicedAmount = this.getInvoicedAmountFromPaidAmount(
      totalAmount,
      cartDiscounts,
    );
    const totalDiscount = invoicedAmount - paidAmount;

    return {
      total_amount: paidAmount,
      delivery_amount: deliveryAmount,
      discounts,
      invoiced_amount: invoicedAmount,
      total_discount: totalDiscount,
      total_amount_after_discount: totalAmount,
    };
  }

  public applyLegacyDiscount(amount: number, discount: number): number {
    // Legacy discount can be a float percent discount or an integer flat discount
    const isFlatAmount = Number.isInteger(discount);
    if (!isFlatAmount && discount < 1) {
      amount -= amount * discount;
    } else {
      amount -= discount;
    }

    return amount;
  }

  public convertLegacyDiscountToDiscounts(discount: number): Discount[] {
    const discounts: Discount[] = [];
    if (!discount) return discounts;

    const isFlatAmount = Number.isInteger(discount) || discount >= 1;
    discounts.push({
      type: isFlatAmount ? DiscountType.FLAT : DiscountType.PERCENTAGE,
      amount: isFlatAmount ? discount : discount * 100,
      applyTo: DiscountApplyTo.CART,
      source: DiscountSource.LEGACY,
    });
    return discounts;
  }

  public addCorrectionalDiscount(order: OrderInvoice): OrderInvoice {
    if (
      order.total_amount_after_discount &&
      order.total_amount_after_discount < order.invoiced_amount &&
      !order.discounts
    ) {
      order.discounts = [
        {
          type: DiscountType.PERCENTAGE,
          amount:
            (order.invoiced_amount - order.total_amount_after_discount) /
            order.invoiced_amount,
          applyTo: DiscountApplyTo.CART,
          source: DiscountSource.CORRECTIONAL,
        },
      ];
    }
    return order;
  }

  public getTotalDiscount(amount: number, discounts: Discount[]): number {
    if (!discounts || discounts.length === 0) return 0;

    const percentDiscount = this.getTotalPercentDiscount(discounts);
    const flatDiscountSum = this.getFlatDiscountSum(discounts);

    return Math.min(amount, (amount ?? 0) * percentDiscount + flatDiscountSum);
  }

  public getInvoicedAmountFromPaidAmount(
    paidAmount: number,
    discounts: Discount[],
  ): number {
    if (!discounts || discounts.length === 0) return paidAmount;

    const percentDiscount = this.getTotalPercentDiscount(discounts);
    const flatDiscountSum = this.getFlatDiscountSum(discounts);
    const invoicedAmount =
      ((paidAmount + flatDiscountSum) * 100) / (1 - percentDiscount);

    return Math.round(invoicedAmount) / 100;
  }

  private getTotalPercentDiscount(discounts: Discount[]) {
    const percentDiscountSum = discounts
      .filter((discount) => discount.type === DiscountType.PERCENTAGE)
      .reduce((partialSum, discount) => partialSum + discount.amount, 0);
    // percent discount cannot exceed 100%
    const percentDiscount = Math.min(percentDiscountSum / 100, 1);
    return percentDiscount;
  }

  private getFlatDiscountSum(discounts: Discount[]) {
    return discounts
      .filter((discount) => discount.type === DiscountType.FLAT)
      .reduce((partialSum, discount) => partialSum + discount.amount, 0);
  }
}
