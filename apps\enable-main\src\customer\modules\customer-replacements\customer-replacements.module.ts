import { Module } from '@nestjs/common';

import { BrandModule } from '../../../brand/brand.module';
import { CompanyModule } from '../../../company/company.module';
import { CouponModule } from '../../../coupon/coupon.module';
import { ShortenUrlModule } from '../../../integration/shorten-url/shorten-url.module';
import { LoyaltyTierReadModule } from '../../../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.module';
import { PassesModule } from '../../../passes/passes.module';
import { PunchCardAchievementModule } from '../../../punch-card/modules/punch-card-achievement/punch-card-achievement.module';
import { PunchCardReadModule } from '../../../punch-card/modules/punch-card-read/punch-card-read.module';
import { CustomerLoyaltyModule } from '../customer-loyalty/customer-loyalty.module';
import { CustomerPassLinkModule } from '../customer-pass-link/customer-pass-link.module';
import { CustomerPassModule } from '../customer-pass/customer-pass.module';
import { CustomerPunchCardModule } from '../customer-punch-card/customer-punch-card.module';
import { CustomerRepositoryModule } from '../customer-repository/customer-repository.module';
import { CustomerTierInfoModule } from '../customer-tier-info/customer-tier-info.module';
import { CustomerWebstoreModule } from '../customer-webstore/customer-webstore.module';
import { CustomerReplacementsService } from './customer-replacements.service';
import { CustomerReplacementsServiceInterface } from './customer-replacements.service.interface';

@Module({
  providers: [
    {
      provide: CustomerReplacementsServiceInterface,
      useClass: CustomerReplacementsService,
    },
  ],
  imports: [
    CompanyModule,
    BrandModule,
    PassesModule,
    LoyaltyTierReadModule,
    PunchCardReadModule,
    PunchCardAchievementModule,
    CustomerRepositoryModule,
    CustomerTierInfoModule,
    CustomerLoyaltyModule,
    CustomerWebstoreModule,
    CustomerPassModule,
    CustomerPunchCardModule,
    CouponModule,
    CustomerPassLinkModule,
    ShortenUrlModule,
  ],
  exports: [CustomerReplacementsServiceInterface],
})
export class CustomerReplacementsModule {}
