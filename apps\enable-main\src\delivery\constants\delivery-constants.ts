import { DriverToCreate } from '../dto/driver.dto';

export const EXCEL_HEADERS = [
  'Driver Id',
  'Tookan Driver Id',
  'First Name',
  'Last Name',
  'UserName',
  'Phone',
  'Email',
  'License',
  'Transport Color',
  'Transport Type',
  'Tookan Team Id',
  'Created At',
] as const;

export const EXCEL_HEADERS_LOWER = EXCEL_HEADERS.map((s) => s.toLowerCase());

export type excelRow = Record<(typeof EXCEL_HEADERS)[number], string>;

export const EXCEL_HEADERS_MAPPING: Record<
  Lowercase<(typeof EXCEL_HEADERS)[number]>,
  keyof DriverToCreate
> = {
  'driver id': null,
  'tookan driver id': 'tookan_driver_id',
  'first name': 'first_name',
  'last name': 'last_name',
  username: 'username',
  phone: 'phone',
  email: 'email',
  license: 'license',
  'transport color': 'color',
  'transport type': 'transport_type_name',
  'tookan team id': 'team_id',
  'created at': null,
} as const;

export type mappedDriverToCreate = Pick<
  DriverToCreate,
  | 'tookan_driver_id'
  | 'first_name'
  | 'last_name'
  | 'username'
  | 'phone'
  | 'email'
  | 'license'
  | 'color'
  | 'transport_type_name'
  | 'team_id'
>;
