const countriesCursor = db
  .getCollection('LocationItems')
  .find({ type: 'country' });

countriesCursor.forEach((country) => {
  const parentId = country._id;

  const relatedCount = db.getCollection('LocationItems').countDocuments({
    'parent._id': parentId,
    type: { $in: ['city', 'area'] },
  });

  if (relatedCount > 0) {
    db.getCollection('LocationItems').updateOne(
      { _id: parentId },
      { $set: { isEditable: false } },
    );
  } else {
    db.getCollection('LocationItems').updateOne(
      { _id: parentId },
      { $set: { isEditable: true } },
    );
  }
});
