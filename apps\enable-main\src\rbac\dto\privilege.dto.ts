import { DataIndex } from '@app/shared-stuff';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class PrivilegeToCreate {
  @IsNotEmpty()
  @ApiProperty()
  value: string;

  @IsNotEmpty()
  @ApiProperty()
  module: string;

  @IsNotEmpty()
  @ApiProperty()
  label: string;

  @ApiProperty()
  parentKey: string;
}

export class PrivilegeToUpdate {
  @IsNotEmpty()
  @ApiProperty()
  _id: string;

  @IsNotEmpty()
  @ApiProperty()
  value: string;

  @IsNotEmpty()
  @ApiProperty()
  module: string;

  @IsNotEmpty()
  @ApiProperty()
  label: string;

  @ApiProperty()
  parentKey: string;
}

export class PrivilegeToIndx extends DataIndex {
  @ApiProperty({
    type: Boolean,
    required: false,
  })
  groupped: boolean;

  user_data: any;
}
