import {
  CountryDialCode,
  DataIndex,
  ImageToCreate,
  ImageToUpdate,
  IsPassword,
  IsPhoneNumberForRegion,
  ObjectIdTransform,
} from '@app/shared-stuff';
import { BooleanTransform } from '@app/shared-stuff/decorators/boolean-transform.decorator';
import { ApiProperty, PickType } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEmail,
  IsEnum,
  IsMongoId,
  IsNotEmpty,
  IsOptional,
} from 'class-validator';
import { Types } from 'mongoose';
import { CallCenterProvider } from '../enums/call-center-provider.enum';

export class UserToLogin {
  @ApiProperty()
  email: string;

  @ApiProperty()
  phone: string;

  @ApiProperty()
  fcm_token: string;

  @ApiProperty()
  socket_id: string;

  @ApiProperty()
  @IsNotEmpty()
  password: string;
}

export class UserToRegister {
  @ApiProperty()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsNotEmpty()
  phone: string;

  @ApiProperty()
  @IsNotEmpty()
  password: string;

  @ApiProperty({
    type: String,
  })
  company: string;

  @ApiProperty({
    type: String,
  })
  branch: string;

  @ApiProperty({
    type: ImageToCreate,
  })
  image: ImageToCreate;
}

export class UserIndex extends DataIndex {
  @ApiProperty({
    type: 'string',
    enum: ['active', 'not_active'],
    required: false,
  })
  status: string;

  @ApiProperty({
    type: Boolean,
    required: false,
    default: false,
  })
  @BooleanTransform()
  isEnableUser = false;

  @ApiProperty({ required: false })
  role_id: string;

  @ApiProperty({ required: false })
  role_name: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  company: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: false,
  })
  branch: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  id: string;
}

export class UserToUpdate {
  @ApiProperty()
  @IsNotEmpty()
  _id: string;

  @ApiProperty()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsNotEmpty()
  phone: string;

  @ApiProperty()
  position: string;

  @ApiProperty({
    type: String,
    default: 'male',
    enum: ['male', 'female'],
  })
  gender: string;
  @ApiProperty({
    type: String,
    default: 'active',
    enum: ['active', 'not_active'],
  })
  status: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsPassword()
  new_password?: string;

  @ApiProperty({
    type: ImageToUpdate,
    required: true,
  })
  image_to_update: ImageToUpdate;

  @ApiProperty({
    type: [Types.ObjectId],
    required: false,
  })
  companies: Types.ObjectId[];
}

export class UserStatusToUpdate {
  @ApiProperty({
    type: String,
    required: true,
    enum: ['active', 'not_active'],
  })
  status: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  id: string;
}

export class UsersToAssignToBranch {
  @ApiProperty({
    type: [String],
    required: true,
  })
  users: [string];

  @ApiProperty({
    type: String,
    required: true,
  })
  branch_id: string;
}

export class UserAndRoleToAssign {
  @ApiProperty({
    type: Types.ObjectId,
    required: true,
  })
  @ObjectIdTransform()
  role_id: Types.ObjectId;

  @ApiProperty({
    type: Types.ObjectId,
    required: true,
  })
  @ObjectIdTransform()
  user_id: Types.ObjectId;
}

export class PasswordToCompare {
  @ApiProperty({
    type: String,
    required: true,
  })
  user_id: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  password: string;
}

export class UserToCreate {
  @ApiProperty()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsPhoneNumberForRegion('countryCode')
  phone: string;

  @ApiProperty({
    type: String,
    required: true,
    enum: CountryDialCode,
    default: '+974',
  })
  @IsOptional()
  @IsEnum(CountryDialCode)
  countryCode: CountryDialCode = CountryDialCode.QATAR;

  @ApiProperty()
  position: string;

  @ApiProperty({
    type: String,
    default: 'male',
    enum: ['male', 'female'],
  })
  gender: string;

  @ApiProperty({
    type: String,
    default: 'active',
    enum: ['active', 'not_active'],
  })
  status: string;

  @ApiProperty({
    type: Boolean,
    required: false,
    default: false,
  })
  @IsBoolean()
  isEnableUser = false;

  @ApiProperty({
    type: String,
    required: false,
  })
  company: string;

  @ApiProperty({
    type: [Types.ObjectId],
    required: false,
  })
  companies: Types.ObjectId[];

  @ApiProperty({ required: false })
  @IsNotEmpty()
  @IsPassword()
  password: string;

  @ApiProperty({
    type: ImageToCreate,
    required: true,
  })
  image_to_create: ImageToCreate;

  @ApiProperty({
    type: String,
    required: false,
    enum: ['Operations Executive', 'CallCenter Agent'],
  })
  role: 'Operations Executive' | 'CallCenter Agent';

  @ApiProperty({
    type: String,
    required: false,
    enum: CallCenterProvider,
    default: CallCenterProvider.MAQSAM,
  })
  @IsOptional()
  @IsEnum(CallCenterProvider)
  callCenterProvider = CallCenterProvider.MAQSAM;
}

export class UserDetailsToUpdate extends PickType(UserToCreate, [
  'callCenterProvider',
]) {
  @ApiProperty()
  @IsNotEmpty()
  _id: string;

  @ApiProperty({
    required: false,
  })
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    required: false,
  })
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    required: false,
  })
  @IsNotEmpty()
  phone: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  position?: string;

  @ApiProperty({
    type: String,
    default: 'male',
    enum: ['male', 'female'],
  })
  @IsOptional()
  gender?: string;

  @ApiProperty({
    type: String,
    default: 'active',
    enum: ['active', 'not_active'],
  })
  @IsOptional()
  status?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  new_password?: string;

  @ApiProperty({
    type: ImageToUpdate,
    required: false,
  })
  @IsOptional()
  image_to_update?: ImageToUpdate;

  @ApiProperty({
    type: [Types.ObjectId],
    required: false,
  })
  @ObjectIdTransform({ optional: true, coerceArray: true })
  companies?: Types.ObjectId[];

  @ApiProperty({
    type: String,
    required: false,
    enum: ['Operations Executive', 'CallCenter Agent'],
  })
  @IsOptional()
  role?: 'Operations Executive' | 'CallCenter Agent';

  @ApiProperty({
    type: Boolean,
    required: false,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  isEnableUser?: boolean;
}

export class UserToIndex extends DataIndex {
  @ApiProperty({
    required: false,
    type: String,
    enum: [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
  })
  month: string;

  @ApiProperty({
    required: false,
    type: String,
    enum: ['phone', 'name', 'role', 'email', 'all'],
  })
  search_type: string;

  @ApiProperty({
    required: false,
    type: String,
    enum: [
      'name_a_z',
      'name_z_a',
      'email_a_z',
      'email_z_a',
      'date_created',
      'date_created_a_z',
      'date_created_z_a',
    ],
  })
  sort_type: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  company: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  branch: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  socketIoId: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  isEnableUser: 'Yes' | 'No';
}

export class UserToValidate {
  @ApiProperty({
    type: String,
    required: true,
  })
  email: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  phone: string;
}

export class UserOrderToAcknowledge {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsMongoId()
  @IsNotEmpty()
  orderId: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  orderCode: string;

  @ApiProperty({
    type: Date,
    required: true,
  })
  @IsNotEmpty()
  createdAt: Date;
}
