import {
  BranchSchema,
  CollectionName,
  SharedStuffModule,
} from '@app/shared-stuff';
import { CompanyEvents } from '@app/shared-stuff/enums/company/company-events.enum';
import {
  CompanyDocument,
  CompanySchema,
} from '@app/shared-stuff/models/company.model';
import { CompanyConfigSchema } from '@app/shared-stuff/models/companyConfig.model';
import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { MongooseModule } from '@nestjs/mongoose';
import { DeliveryModule } from '../delivery/delivery.module';
import { LocationModule } from '../location/location.module';
import { LoyaltyTierReadModule } from '../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.module';
import { SharedModule } from '../shared/shared.module';
import { UserModule } from '../user/user.module';
import { CompanyController } from './controllers/company/company.controller';
import { CompanyConfigController } from './controllers/companyConfig/companyConfig.controller';
import { CalendarSystemService } from './services/calendar-system/calendar-system.service';
import { CompanyListener } from './services/company-listener/company.listener';
import { CompanyService } from './services/company/company.service';
import { CompanyConfigService } from './services/companyConfig/companyConfig.service';

@Module({
  controllers: [CompanyController, CompanyConfigController],
  providers: [
    CompanyService,
    CompanyConfigService,
    CalendarSystemService,
    CompanyListener,
  ],
  imports: [
    SharedModule,
    DeliveryModule,
    ConfigModule,
    LocationModule,
    SharedStuffModule,
    LoyaltyTierReadModule,
    // forwardRef(() => LoyaltyTierWriteModule),
    forwardRef(() => UserModule),
    MongooseModule.forFeatureAsync([
      {
        name: CollectionName.COMPANY,
        useFactory: (eventEmitter: EventEmitter2) => {
          const companySchema = CompanySchema;
          companySchema.post<CompanyDocument>(
            'save',
            async function (company: CompanyDocument, next: any) {
              eventEmitter.emit(CompanyEvents.COMPANY_UPDATED, company);
              next();
            },
          );

          return companySchema;
        },
        inject: [EventEmitter2],
      },
    ]),

    MongooseModule.forFeature([
      { name: CollectionName.COMPANY_CONFIG, schema: CompanyConfigSchema },
      { name: CollectionName.BRANCH, schema: BranchSchema },
    ]),
  ],
  exports: [CompanyService, CompanyConfigService, CalendarSystemService],
})
export class CompanyModule {}
