import {
  CreatePassConfigDto,
  GenericExceptionFilter,
  GetPassConfigDto,
  PassConfig,
  PassConfigIdDto,
  TransformInterceptor,
  UpdatePassConfigDto,
  UploadPassConfigImageBodyDto,
  UploadPassConfigImageResponseDto,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { CurrentUserService } from '../../shared/services/current-user/current-user.service';
import { PassConfigService } from '../services/pass-config/pass-config.service';

@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@SetMetadata('module', 'pass-config')
@ApiTags('PassConfig')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@Controller('pass-config')
export class PassConfigController {
  constructor(
    private readonly passConfigService: PassConfigService,
    private readonly currentUserService: CurrentUserService,
  ) {}

  @Get()
  @SetMetadata('public', 'true')
  getPassConfig(
    @Query() { companyId, brandId, tierId }: GetPassConfigDto,
  ): Promise<PassConfig> {
    return this.passConfigService.getPassConfig(companyId, brandId, tierId);
  }

  @Get(':passConfigId')
  @SetMetadata('public', 'true')
  getPassConfigById(
    @Param() { passConfigId }: PassConfigIdDto,
  ): Promise<PassConfig> {
    return this.passConfigService.getPassConfigById(passConfigId);
  }

  @Post()
  @SetMetadata('action', 'create')
  createPassConfig(
    @Body() createPassConfigDto: CreatePassConfigDto,
  ): Promise<PassConfig> {
    return this.passConfigService.create(createPassConfigDto);
  }

  @Patch(':passConfigId')
  @SetMetadata('action', 'update')
  updatePassConfig(
    @Param() { passConfigId }: PassConfigIdDto,
    @Body() updatePassConfigDto: UpdatePassConfigDto,
  ): Promise<PassConfig> {
    return this.passConfigService.update(passConfigId, updatePassConfigDto);
  }

  @Post('upload')
  @SetMetadata('action', 'upload')
  async upload(
    @Body() uploadImageBodyDto: UploadPassConfigImageBodyDto,
  ): Promise<UploadPassConfigImageResponseDto> {
    return this.passConfigService.uploadImage({
      ...uploadImageBodyDto,
      companyId: this.currentUserService.getCurrentCompanyId(),
    });
  }
}
