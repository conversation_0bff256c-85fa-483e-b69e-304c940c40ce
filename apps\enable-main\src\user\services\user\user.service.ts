import {
  <PERSON><PERSON><PERSON>,
  CurrentUser,
  Generic<PERSON>riggerModel,
  ImageToCreate,
  OrderDocument,
  PusherService,
  responseCode,
  TemplateTo,
  TriggerUserDto,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as bcrypt from 'bcrypt';
import { hash } from 'bcrypt';
import * as moment from 'moment-timezone';
import { FilterQuery, isValidObjectId, Model, Types } from 'mongoose';
import { RoleService } from '../../../rbac/services/role/role.service';
import { HelperService } from '../../../shared/services/helper/helper.service';
import { ImageService } from '../../../shared/services/image/image.service';
import { UserDocument } from '../../models/user.model';
import {
  UserDetailsToUpdate,
  UserIndex,
  UserOrderToAcknowledge,
  UserToCreate,
  UserToIndex,
  UserToUpdate,
  UserToValidate,
} from './../../dto/user.dto';

@Injectable()
export class UserService {
  constructor(
    @InjectModel('User') private userModel: Model<UserDocument>,
    private imageService: ImageService,
    private roleService: RoleService,
    private helperService: HelperService,
    private pusherService: PusherService,
  ) {}

  async index(userIndex: UserIndex) {
    const filter: FilterQuery<UserDocument> = { deletedAt: null };

    if (userIndex.role_id) filter['roles._id'] = userIndex.role_id;

    if (userIndex.role_name) {
      const roles = await this.roleService.admin_index({
        name: userIndex.role_name,
        company: userIndex.company.toString(),
      });
      const role_ids = roles.map((r) => r._id);
      filter.roles = { $in: role_ids };
    }

    if (userIndex.search_key)
      filter.name = { $regex: `.*${userIndex.search_key}.*` };

    if (userIndex.status) filter.status = userIndex.status;

    if (userIndex.company) filter.company = userIndex.company;

    if (userIndex.branch)
      filter.branches = new Types.ObjectId(userIndex.branch);

    if (userIndex.id) filter._id = userIndex.id;

    let query = this.userModel.find(filter);

    if (userIndex.limit && (userIndex.offset || userIndex.offset == 0)) {
      query = query
        .skip(userIndex.offset * userIndex.limit)
        .limit(userIndex.limit);
    }

    return query
      .populate('roles', 'name _id', CollectionName.ROLES)
      .select(['-notifications', '-password'])
      .exec();
  }

  async get_details(id: Types.ObjectId | string) {
    const filterOBJ = { $or: [{ socketIoId: id }] } as any;
    if (isValidObjectId(id)) {
      filterOBJ.$or.push({ _id: new Types.ObjectId(id) });
    }
    return this.userModel
      .findOne(filterOBJ)
      .populate({
        path: 'roles',
        populate: 'permissions',
        model: CollectionName.ROLES,
      })
      .select(['-password']);
  }

  async findOneByEmail(email: string, companyId: Types.ObjectId) {
    return this.userModel.findOne({ email: email, company: companyId });
  }

  async updatePassword(userId: Types.ObjectId, password: string) {
    await this.userModel.updateOne(
      { _id: userId },
      { password: await hash(password, 10) },
    );
  }

  async updateBranches(userId: Types.ObjectId, branches: Types.ObjectId[]) {
    await this.userModel.findOneAndUpdate(
      { _id: userId },
      { $set: { branches } },
    );
  }

  async createFromEbutlerSource(providerData) {
    const provider = new this.userModel({
      name: providerData.vName + ' ' + providerData.vLastName,
      email: providerData.vEmail,
      phone: providerData.vPhone,
      is_provider: true,
      password: 'Ebutler',
      ebutler_provider_id: providerData.iDriverId,
      registerd: false,
    });
    await provider.save();
    return provider;
  }

  async remove(id, deletedBy: any) {
    const removedUser = await this.userModel.findOne({ _id: id });
    removedUser.deletedBy = deletedBy;
    removedUser.deletedAt = moment().utc().toDate();
    await removedUser.save();
    return removedUser;
  }

  async update(userToUpdate: UserToUpdate, updatedBy: any) {
    if (userToUpdate.new_password) {
      userToUpdate['password'] = await bcrypt.hash(
        userToUpdate.new_password,
        10,
      );
    }
    if (userToUpdate.image_to_update) {
      if (userToUpdate.image_to_update.action == 'deleted') {
        userToUpdate['image'] = null;
      } else {
        const imageToCreate: ImageToCreate = {
          name: userToUpdate.name,
          alt: userToUpdate.image_to_update.alt,
          description: userToUpdate.image_to_update.description,
          for: userToUpdate.image_to_update.for,
          base64: userToUpdate.image_to_update.base64,
        };
        userToUpdate['image'] =
          await this.imageService.uploadImage(imageToCreate);
      }
    }
    const updatedUser = await this.userModel.findOneAndUpdate(
      { _id: userToUpdate._id },
      userToUpdate,
    );
    updatedUser.updatedBy = updatedBy;
    await updatedUser.save();
    return updatedUser;
  }

  async change_status(user_id: string, status: string, updatedBy: any) {
    const selectedUser = await this.userModel.findOneAndUpdate(
      { _id: user_id },
      { status: status },
    );
    // TODO: send notification to user that the user status has been changed
    selectedUser.updatedBy = updatedBy;
    await selectedUser.save();
    return selectedUser;
  }

  async get_details_using_category_id(category_id) {
    const provider = await this.userModel.findOne({
      categories: { $eq: category_id },
    });
    return provider;
  }

  async update_user_details(
    userDetailsToUpdate: UserDetailsToUpdate,
    updatedBy: any,
  ) {
    const updatedFields: Partial<UserDocument> = {};

    if (userDetailsToUpdate.new_password) {
      updatedFields.password = await bcrypt.hash(
        userDetailsToUpdate.new_password,
        10,
      );
    }

    if (userDetailsToUpdate.image_to_update) {
      updatedFields.image = await this.handleImageUpdate(userDetailsToUpdate);
    }

    const updatedUser = await this.userModel.findOneAndUpdate(
      { _id: userDetailsToUpdate._id },
      {
        ...userDetailsToUpdate,
        ...updatedFields,
        updatedBy,
      },
      { new: true },
    );

    if (userDetailsToUpdate.isEnableUser) {
      await this.assignRoleIfNeeded(
        updatedUser,
        userDetailsToUpdate.role,
        updatedBy,
      );
    }

    return updatedUser;
  }

  private async handleImageUpdate(userDetailsToUpdate: UserDetailsToUpdate) {
    if (userDetailsToUpdate.image_to_update.action === 'deleted') {
      return null;
    }

    const imageToCreate: ImageToCreate = {
      name: userDetailsToUpdate.name,
      alt: userDetailsToUpdate.image_to_update.alt,
      description: userDetailsToUpdate.image_to_update.description,
      for: userDetailsToUpdate.image_to_update.for,
      base64: userDetailsToUpdate.image_to_update.base64,
    };

    return await this.imageService.uploadImage(imageToCreate);
  }

  private async assignRoleIfNeeded(
    user: any,
    roleName: string,
    updatedBy: any,
  ) {
    const role = (
      await this.roleService.admin_index({
        name: roleName,
        isDefault: true,
      } as any)
    )[0];

    if (role) {
      await this.add_role_to_user(user._id, role._id, updatedBy);
    }
  }

  async add_role_to_user(
    user_id: Types.ObjectId,
    role_id: Types.ObjectId,
    updatedBy: any,
  ) {
    const user = await this.userModel.findOne({ _id: user_id });
    user.roles = [role_id];
    const role = await this.roleService.get_details(role_id);
    if ((role && role.name == 'Super Admin') || role.name == 'Company Admin') {
      await this.userModel.updateOne(
        { _id: user_id },
        { $unset: { branch: '' } },
      );
    }
    user.updatedBy = updatedBy;
    await user.save();
    return user;
  }

  async create(userToCreate: UserToCreate, createdBy: CurrentUser) {
    if (userToCreate.image_to_create) {
      userToCreate['image'] = await this.imageService.uploadImage(
        userToCreate.image_to_create,
      );
    }
    if (userToCreate.companies && userToCreate.companies.length != 0)
      userToCreate.companies = this.convertCompanyIdsToObjectId(
        userToCreate.companies,
      );

    const createdUser = new this.userModel(userToCreate);
    createdUser.createdBy = createdBy;
    await createdUser.save();

    if (userToCreate.isEnableUser) {
      const role = (
        await this.roleService.admin_index({
          name: userToCreate.role,
          isDefault: true,
        } as any)
      )[0];

      if (role) {
        await this.add_role_to_user(createdUser._id, role._id, createdBy);
      }
    }
    return createdUser;
  }

  async admin_index(userToIndex: UserToIndex) {
    let users = this.userModel
      .find()
      .populate('roles', 'name _id', CollectionName.ROLES)
      .select(['-notifications', '-password']);

    if (userToIndex.limit && (userToIndex.offset || userToIndex.offset == 0)) {
      users = users
        .skip(userToIndex.offset * userToIndex.limit)
        .limit(userToIndex.limit);
    }

    if (userToIndex.isEnableUser == 'Yes' && !userToIndex.company) {
      const roles = await this.roleService.admin_index({
        name: 'Operations Executive,CallCenter Agent',
      } as any);

      const rolesIds = roles.map((r) => new Types.ObjectId(r._id));

      users = users.find({ roles: { $in: rolesIds } });
    }

    if (userToIndex.search_key) {
      if (userToIndex.search_type == 'name') {
        users = users.where('name', {
          $regex: `.*${userToIndex.search_key.toLowerCase()}.*$`,
        });
      } else if (userToIndex.search_type == 'phone') {
        users = users.where('phone', {
          $regex: `.*${userToIndex.search_key.toLowerCase()}.*`,
        });
      } else if (userToIndex.search_type == 'email') {
        users = users.where('email', {
          $regex: `.*${userToIndex.search_key.toLowerCase()}.*`,
        });
      } else if (userToIndex.search_type == 'role') {
        users = users.where('roles.name', {
          $regex: `.*${userToIndex.search_key.toLowerCase()}.*`,
        });
      } else {
        users = users.find({
          $or: [
            { name: { $regex: `.*${userToIndex.search_key.toLowerCase()}.*` } },
            {
              phone: { $regex: `.*${userToIndex.search_key.toLowerCase()}.*` },
            },
            {
              email: { $regex: `.*${userToIndex.search_key.toLowerCase()}.*` },
            },
          ],
        });
      }
    }

    if (userToIndex.company) {
      users = users.where('company', userToIndex.company);
    }
    if (userToIndex.branch) {
      users = users.where('branches', {
        $elemMatch: { $eq: new Types.ObjectId(userToIndex.branch) },
      });
    }
    if (userToIndex.month) {
      users = users.where('month', userToIndex.month);
    }
    if (userToIndex.sort_type) {
      const sortMaping = {
        name_a_z: { name: 1 },
        name_z_a: { name: -1 },
        email_a_z: { email: 1 },
        email_z_a: { email: -1 },
        date_created: { createdAt: -1 },
        date_created_a_Z: { createdAt: 1 },
        date_created_z_a: { createdAt: -1 },
      };
      if (sortMaping[userToIndex.sort_type]) {
        users = users.sort(sortMaping[userToIndex.sort_type]);
      } else {
        users = users.sort({ createdAt: -1 });
      }
    } else {
      users = users.sort({ createdAt: -1 });
    }

    users = users.where('deletedAt', { $eq: null });
    return await users.exec();
  }

  async get_total_users(userToIndex: UserToIndex) {
    delete userToIndex.offset;
    delete userToIndex.limit;
    const users = await this.admin_index(userToIndex);
    return users.length;
  }

  async reflect_database_changes() {
    const users = await this.userModel.find({});
    for (let i = 0; i < users.length; i++) {
      const user = users[i];
      user.company = new Types.ObjectId(user.company);
      await user.save();
    }
  }

  async validateUserData(userToValidate: UserToValidate) {
    const user = await this.userModel.findOne({
      $or: [
        { email: userToValidate.email.trim() },
        { phone: userToValidate.phone.trim() },
      ],
    });

    if (user && user.email == userToValidate.email) {
      throw {
        code: responseCode.WRONG_CREDENTIAL,
        statusCode: 401,
        message: 'EMAIL_EXISTS',
      };
    } else if (user && user.phone == userToValidate.phone) {
      throw {
        code: responseCode.WRONG_CREDENTIAL,
        statusCode: 401,
        message: 'PHONE_EXISTS',
      };
    }
    return null;
  }

  async removeAcknowledgeOrdersFromUsers(
    order: OrderDocument,
    state: 'canceled' | 'deleted' | 'acknowledged',
  ) {
    const orderId = new Types.ObjectId(order._id);
    const users = await this.userModel.find({
      ordersToBeAcknowledged: {
        $elemMatch: { orderId: orderId },
      },
    });

    for (let i = 0; i < users.length; i++) {
      const ordersToBeAcknowledged: UserOrderToAcknowledge[] = [];
      for (let j = 0; j < users[i].ordersToBeAcknowledged.length; j++) {
        if (users[i].ordersToBeAcknowledged[j].orderId.equals(orderId)) {
          continue;
        }
        ordersToBeAcknowledged.push(users[i].ordersToBeAcknowledged[j]);
      }
      users[i].ordersToBeAcknowledged = ordersToBeAcknowledged;
      await this.userModel.updateOne(
        { _id: users[i]['_id'] },
        { ordersToBeAcknowledged: ordersToBeAcknowledged },
      );
    }
    if (state == 'deleted' || state == 'canceled') {
      await this.fireOrderCanceledOrDeleted(users, order, state);
    }
  }

  async updateOrdersToBeAcknowledged(
    _id: Types.ObjectId,
    ordersToBeAcknowledged: UserOrderToAcknowledge[],
  ): Promise<void> {
    await this.userModel.updateOne({ _id }, { ordersToBeAcknowledged });
  }

  async fireOrderCanceledOrDeleted(
    users: UserDocument[],
    order: OrderDocument,
    state: 'deleted' | 'canceled',
  ) {
    if (!users.length && order.branch) {
      users = await this.index({
        company: order.company._id,
        branch: order.branch['_id'] ? order.branch['_id'] : '',
        role_name: 'Branch Manager',
      } as any);
    }

    users.forEach((user) => {
      this.pusherService.fireEvent(
        user._id.toHexString(),
        'orderCanceledOrDeleted',
        {
          id: order._id,
          code: order.code,
          status: state,
          isAcknowledged: order.isAcknowledged,
          timestamp: moment.utc().toDate(),
          acknowledgmentBuffer: user.ordersToBeAcknowledged,
        },
      );
    });
  }

  public async notifyUsersAboutOrderAcknowledged(order: Types.ObjectId) {
    const users = await this.userModel.find({
      ordersToBeAcknowledged: {
        $elemMatch: { orderId: order },
      },
    });

    for (let i = 0; i < users.length; i++) {
      this.pusherService.fireEvent(
        users[i]._id.toHexString(),
        'orderAcknowledgedSuccessfully',
        {
          orderId: order,
        },
      );
    }
  }

  async getBranchManagersUsers(
    genericUserDto: GenericTriggerModel,
    role: TemplateTo,
    users: TriggerUserDto[],
  ): Promise<TriggerUserDto[]> {
    const userIndex = this.constructUserIndex(
      'Branch Manager',
      genericUserDto.companyId,
      genericUserDto.branchId,
    );
    const branchManagers = await this.index(userIndex);
    const mappedBranchManagers = branchManagers.map((branchManager) =>
      this.helperService.mapToTriggerUser(branchManager, genericUserDto, role),
    );
    return users.concat(mappedBranchManagers);
  }

  async getSuperAdminUsers(
    genericTriggerModel: GenericTriggerModel,
    role: TemplateTo,
    users: TriggerUserDto[],
  ): Promise<TriggerUserDto[]> {
    const userIndex = this.constructUserIndex(
      'Super Admin',
      genericTriggerModel.companyId,
    );
    const superAdmins = await this.index(userIndex);
    const mappedSuperAdmins = superAdmins.map((superAdmin) =>
      this.helperService.mapToTriggerUser(
        superAdmin,
        genericTriggerModel,
        role,
      ),
    );
    return users.concat(mappedSuperAdmins);
  }

  async getDispatcherUsers(
    genericTriggerModel: GenericTriggerModel,
    role: TemplateTo,
    users: TriggerUserDto[],
  ): Promise<TriggerUserDto[]> {
    const userIndex = this.constructUserIndex(
      'Dispatcher',
      genericTriggerModel.companyId,
    );
    const dispatchers = await this.index(userIndex);
    const mappedDispatchers = dispatchers.map((dispatcher) =>
      this.helperService.mapToTriggerUser(
        dispatcher,
        genericTriggerModel,
        role,
      ),
    );
    return users.concat(mappedDispatchers);
  }

  getItemCreatorUser(
    genericTriggerModel: GenericTriggerModel,
    users: TriggerUserDto[],
  ): TriggerUserDto[] {
    return genericTriggerModel.createdBy
      ? users.concat(
          this.helperService.mapToTriggerUser(
            genericTriggerModel.createdBy,
            genericTriggerModel,
            TemplateTo.ITEM_CREATOR,
          ),
        )
      : users;
  }

  getGiftRecipientUser(
    genericTriggerModel: GenericTriggerModel,
    users: TriggerUserDto[],
  ): TriggerUserDto[] {
    return users.concat(
      this.helperService.mapToTriggerUser(
        genericTriggerModel.giftRecipientUser,
        genericTriggerModel,
        TemplateTo.GIFT_RECIPIENT,
      ),
    );
  }

  private convertCompanyIdsToObjectId(companies: Types.ObjectId[]) {
    return companies.map((company) => new Types.ObjectId(company));
  }

  private constructUserIndex(
    roleName: string,
    companyId: string,
    branchId?: string,
  ): UserIndex {
    return {
      status: '',
      role_id: '',
      role_name: roleName,
      company: new Types.ObjectId(companyId),
      branch: branchId,
      id: '',
      isEnableUser: false,
    };
  }

  async deleteBranchFromUsers(branchId: Types.ObjectId) {
    await this.userModel.updateMany(
      { branches: branchId },
      { $pull: { branches: branchId } },
    );
  }
}
