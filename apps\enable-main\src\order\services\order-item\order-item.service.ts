import { OrderPaymentService } from './../order-payment/order-payment.service';
import { OrderItemToAdd, OrderItemToIndex } from './../../dto/orderItem.dto';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model, Types } from 'mongoose';
import { OrderItemDocument } from '../../../order/models/orderItems.model';
import { itemToRemove, ItemToUpdate } from '../../../order/dto/orderItem.dto';
import {
  CollectionName,
  OrderDocument,
  OrderItem,
  OrderPaymentMethod,
} from '@app/shared-stuff';
import { OrderInvoiceService } from '../../modules/order-invoice/order-invoice.service';

@Injectable()
export class OrderItemService {
  constructor(
    @InjectModel('OrderItem')
    private orderItemModel: Model<OrderItemDocument, OrderItem>,
    @InjectModel(CollectionName.ORDER) private orderModel: Model<OrderDocument>,
    private orderPaymentService: OrderPaymentService,
    private orderInvoiceService: OrderInvoiceService,
  ) {}

  async index(
    orderItemToIndex: OrderItemToIndex,
  ): Promise<OrderItemDocument[]> {
    const filter: FilterQuery<OrderItemDocument> = {};

    if (orderItemToIndex.company) filter.company = orderItemToIndex.company;

    if (orderItemToIndex.orderId) filter.order_id = orderItemToIndex.orderId;

    if (orderItemToIndex.searchKey) {
      filter.$or = [
        {
          name: new RegExp(`.*${orderItemToIndex.searchKey.toLowerCase()}.*`),
        },
        {
          description: new RegExp(
            `.*${orderItemToIndex.searchKey.toLowerCase()}.*`,
          ),
        },
      ];
    }

    return this.orderItemModel.find(filter);
  }

  async getAllByIds(ids: Types.ObjectId[]): Promise<OrderItemDocument[]> {
    return this.orderItemModel.find({
      _id: {
        $in: ids,
      },
    });
  }

  async create(orderItemToCreate: OrderItem): Promise<OrderItemDocument> {
    return await this.orderItemModel.create(orderItemToCreate);
  }

  async addToOrder(orderItemToAdd: OrderItemToAdd): Promise<OrderItemDocument> {
    const order = await this.orderModel.findOne({
      _id: orderItemToAdd.order_id,
    });
    const newItem: OrderItemDocument =
      await this.orderItemModel.create(orderItemToAdd);
    order.order_items.push(newItem._id);
    await order.save();
    await this.updateOrderInvoiceAmount(order._id);
    return newItem;
  }

  async remove(itemToRemove: itemToRemove): Promise<OrderItemDocument> {
    const selectedOrder = await this.orderModel.findOne({
      _id: itemToRemove.order_id,
    });
    const itemIndex = selectedOrder.order_items.findIndex(
      (item: Types.ObjectId) => item.equals(itemToRemove.item_id),
    );
    selectedOrder.order_items.splice(itemIndex, 1);
    await selectedOrder.save();
    await this.updateOrderInvoiceAmount(itemToRemove.order_id);
    return this.orderItemModel.findOneAndRemove({
      _id: itemToRemove.item_id,
    });
  }

  async update(itemToUpdate: ItemToUpdate): Promise<OrderItemDocument> {
    const selectedItem: OrderItemDocument =
      await this.orderItemModel.findOneAndUpdate(
        { _id: itemToUpdate._id },
        itemToUpdate,
      );
    await selectedItem.save();
    await this.updateOrderInvoiceAmount(itemToUpdate.order_id);
    return selectedItem;
  }

  async updateOrderInvoiceAmount(order_id: Types.ObjectId): Promise<void> {
    if (!order_id) {
      return;
    }
    const selectedOrder = await this.orderModel
      .findOne({ _id: order_id })
      .populate('order_items');
    let invoiced_amount = 0;
    for (let i = 0; i < selectedOrder.order_items.length; i++) {
      const orderItem = selectedOrder.order_items[i];
      if (orderItem['discount']) {
        const isFlatAmount = Number.isInteger(orderItem['discount']),
          totalAmount = orderItem['price'] * orderItem['quantity'];
        if (!isFlatAmount && orderItem['discount'] < 1) {
          invoiced_amount += totalAmount - totalAmount * orderItem['discount'];
        } else {
          invoiced_amount += totalAmount - orderItem['discount'];
        }
      } else {
        invoiced_amount += orderItem['price'] * orderItem['quantity'];
      }
    }

    selectedOrder.set(
      this.orderInvoiceService.computeFromInvoicedAmount(
        invoiced_amount,
        selectedOrder.delivery_amount,
        selectedOrder.discounts,
      ),
    );

    if (
      selectedOrder.payment_method == OrderPaymentMethod.online &&
      selectedOrder.payment_code
    ) {
      await this.orderPaymentService.updateOrderPaymentAmount(
        selectedOrder.payment_code,
        selectedOrder.total_amount,
      );
    }

    await selectedOrder.save();
  }
}
