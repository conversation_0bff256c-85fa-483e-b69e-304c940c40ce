import { BenefitConcreteInterface } from './concretes/benefit-concrete.interface';

import {
  AggregatorOrderToCreate,
  BenefitAppliedTo,
  BenefitMaximumUsageType,
  BenefitType,
  CustomerDocument,
  CustomerEarnedBenefit,
  OrderDocument,
  OrderPosToCreate,
  responseCode,
} from '@app/shared-stuff';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CustomerRepositoryInterface } from '../../../customer/modules/customer-repository/customer.repository.interface';
import { LoyaltyTransactionServiceInterface } from '../../../loyalty-transaction/services/loyalty-transaction-service.interface';
import { FixedAmountConcreteService } from './concretes/fixed-amount.concrete.service';
import { PercentageDiscountConcreteService } from './concretes/percentage-discount.concrete.service';

@Injectable()
export class BenefitTypeFactoryService {
  private benefitTypes: Map<BenefitType, BenefitConcreteInterface> = new Map();
  private orderAppliedToMapping: Partial<
    Record<
      BenefitAppliedTo,
      'delivery_amount' | 'invoiced_amount' | 'total_amount'
    >
  >;
  constructor(
    private readonly fixedAmountConcreteService: FixedAmountConcreteService,
    private readonly percentageDiscountConcreteService: PercentageDiscountConcreteService,
    private readonly eventEmitter: EventEmitter2,
    @Inject(CustomerRepositoryInterface)
    private readonly customerRepository: CustomerRepositoryInterface,
    @Inject(LoyaltyTransactionServiceInterface)
    private readonly loyaltyTransactionService: LoyaltyTransactionServiceInterface,
  ) {
    this.initMap();
    this.initOrderAppliedToMapping();
  }

  private initMap() {
    this.benefitTypes = new Map([
      [BenefitType.PERCENTAGE_DISCOUNT, this.percentageDiscountConcreteService],
      [BenefitType.FIXED_DISCOUNT, this.fixedAmountConcreteService],
    ]);
  }
  private initOrderAppliedToMapping() {
    this.orderAppliedToMapping = {
      [BenefitAppliedTo.ORDER_DELIVERY_AMOUNT]: 'delivery_amount',
      [BenefitAppliedTo.ORDER_INVOICED_AMOUNT]: 'invoiced_amount',
      [BenefitAppliedTo.ORDER_TOTAL_AMOUNT]: 'total_amount',
    };
  }

  async redeem(
    benefit: CustomerEarnedBenefit,
    customer: CustomerDocument,
    order: OrderDocument,
  ): Promise<OrderDocument> {
    const { isValid, validationReasons } = this.validate(benefit, order);
    if (!isValid) {
      throw new BadRequestException(
        validationReasons.join(', '),
        responseCode.BAD_REQUEST.toString(),
      );
    }

    const itemIndex = this.findOrderMenuItemIndex(benefit, order);
    const amountToApply =
      benefit.appliedTo === BenefitAppliedTo.MENU_ITEM
        ? order.items[itemIndex]?.price || 0
        : order[this.orderAppliedToMapping[benefit.appliedTo]];

    const [amountAfterDiscount, discountValue] = this.benefitTypes
      .get(benefit.type)
      .redeem(benefit, amountToApply);

    this.applyDiscountToOrder(
      benefit,
      order,
      amountAfterDiscount,
      discountValue,
    );
    await this.postRedeem(benefit, customer);
    return order;
  }

  validate(
    benefit: CustomerEarnedBenefit,
    order: OrderDocument | OrderPosToCreate | AggregatorOrderToCreate,
  ): { isValid: boolean; validationReasons: string[] } {
    const validationReasons: string[] = [];
    const isValid =
      this.benefitIsApplicableToMenuItem(benefit, order, validationReasons) &&
      this.benefitIsNotExpired(benefit, validationReasons) &&
      this.benefitIsUsable(benefit, validationReasons) &&
      this.benefitIsApplicable(benefit, order, validationReasons) &&
      this.benefitTypes.has(benefit.type);

    return { isValid, validationReasons };
  }

  private applyDiscountToOrder(
    benefit: CustomerEarnedBenefit,
    order: OrderDocument,
    amountAfterDiscount: number,
    discountValue: number,
  ) {
    if (
      benefit.appliedTo === BenefitAppliedTo.MENU_ITEM &&
      !order.isManuallyCaptured
    ) {
      const menuItemIndex = this.findOrderMenuItemIndex(benefit, order);
      const orderItem = order.items[menuItemIndex];
      const quantity = orderItem.quantity || 1;

      const singleItemPrice = orderItem.price;
      const singleItemDiscount = discountValue;
      const singleItemAmountAfterDiscount = amountAfterDiscount;

      orderItem.totalAmountAfterDiscount =
        singleItemAmountAfterDiscount + singleItemPrice * (quantity - 1);
      orderItem.discount = (orderItem.discount ?? 0) + singleItemDiscount;

      order.invoiced_amount = order.items.reduce(
        (acc, item) => acc + item.totalAmountAfterDiscount,
        0,
      );
    } else {
      order.total_discount = (order.total_discount ?? 0) + discountValue;
    }

    order.total_amount_after_discount =
      order.invoiced_amount - order.total_discount;

    order.total_amount =
      order.total_amount_after_discount + order.delivery_amount;

    order.benefits = [...(order.benefits ?? []), benefit];
  }

  private benefitIsApplicableToMenuItem(
    benefit: CustomerEarnedBenefit,
    order: OrderDocument | OrderPosToCreate | AggregatorOrderToCreate,
    validationReasons: string[],
  ): boolean {
    if (benefit.appliedTo !== BenefitAppliedTo.MENU_ITEM) return true;
    if (this.isManuallyCaptured(order)) return true;
    const menuItemIndex = this.findOrderMenuItemIndex(benefit, order);
    if (menuItemIndex === -1) {
      validationReasons.push(
        'There is no menu item in the order Matched by the Benefit MenuItem id',
      );
      return false;
    }
    return true;
  }

  private isManuallyCaptured(
    order: OrderDocument | OrderPosToCreate | AggregatorOrderToCreate,
  ): order is AggregatorOrderToCreate {
    return 'isManuallyCaptured' in order && order.isManuallyCaptured;
  }

  private findOrderMenuItemIndex(
    benefit: CustomerEarnedBenefit,
    order: OrderDocument | OrderPosToCreate,
  ): number | null {
    return order.items.findIndex((item) =>
      benefit.menuItem?.eligibleMenuItemIds?.some(
        (id) =>
          id.toString() === item.itemReference?.toString() ||
          id.toString() === item.code,
      ),
    );
  }

  private benefitIsNotExpired(
    benefit: CustomerEarnedBenefit,
    validationReasons: string[],
  ): boolean {
    if (!benefit.config.expiryDate) return true;
    if (benefit.config.expiryDate > new Date()) return true;
    validationReasons.push('Benefit is expired');
    return false;
  }

  private benefitIsUsable(
    benefit: CustomerEarnedBenefit,
    validationReasons: string[],
  ): boolean {
    if (
      benefit.config.maximumUsageType === BenefitMaximumUsageType.UNLIMITED ||
      benefit.numberOfUsages < benefit.config.maximumUsage
    ) {
      return true;
    }
    validationReasons.push('Benefit has reached its maximum usage');
    return false;
  }

  private benefitIsApplicable(
    benefit: CustomerEarnedBenefit,
    order: OrderDocument | OrderPosToCreate | AggregatorOrderToCreate,
    validationReasons: string[],
  ): boolean {
    if (!benefit.config.minimumAmountToRedeem) return true;
    if (order.invoiced_amount == null) return true;
    if (order.invoiced_amount >= benefit.config.minimumAmountToRedeem)
      return true;
    validationReasons.push(
      'Order amount is less than the minimum amount to redeem',
    );
    return false;
  }

  async postRedeem(benefit: CustomerEarnedBenefit, customer: CustomerDocument) {
    benefit.numberOfUsages += 1;

    if (
      benefit.numberOfUsages >= benefit.config.maximumUsage &&
      benefit.config.maximumUsageType !== BenefitMaximumUsageType.UNLIMITED
    ) {
      customer.earnedBenefits = customer.earnedBenefits?.filter(
        (customerEarnedBenefit) =>
          !(
            customerEarnedBenefit._id.equals(benefit._id) &&
            customerEarnedBenefit.source === benefit.source
          ),
      );
      customer.usedBenefits = [
        ...(customer.usedBenefits ?? []),
        { ...benefit, usedAt: new Date() },
      ];

      await this.customerRepository.updateBenefits(
        customer._id,
        customer.earnedBenefits,
        'earnedBenefits',
      );

      await this.customerRepository.updateBenefits(
        customer._id,
        customer.usedBenefits,
        'usedBenefits',
      );

      this.eventEmitter.emit('customer.benefits.updated', customer, benefit);
    } else {
      customer.earnedBenefits = customer.earnedBenefits.map(
        (customerEarnedBenefit) =>
          customerEarnedBenefit._id.equals(benefit._id) &&
          customerEarnedBenefit.source === benefit.source
            ? {
                ...customerEarnedBenefit,
                numberOfUsages: benefit.numberOfUsages,
              }
            : customerEarnedBenefit,
      );
      await this.customerRepository.updateBenefits(
        customer._id,
        customer.earnedBenefits,
        'earnedBenefits',
      );
    }
    await this.loyaltyTransactionService.recordRedeemedBenefit(
      customer,
      benefit,
    );

    this.eventEmitter.emit('benefit.redeemed', customer, benefit);
  }
}
