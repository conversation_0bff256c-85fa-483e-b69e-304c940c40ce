import { CollectionName } from '@app/shared-stuff';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { HydratedDocument } from 'mongoose';

export type PermissionDocument = HydratedDocument<Permission>;
@Schema({ timestamps: true, collection: CollectionName.PERMISSIONS })
export class Permission {
  @Prop({
    required: true,
  })
  name: string;

  @Prop({
    required: true,
    index: true,
  })
  module: string;

  @Prop({
    required: true,
  })
  action: string;

  @Prop({
    required: true,
  })
  route: string;

  @Prop({
    type: Date,
    required: false,
  })
  updatedAt: Date;

  @Prop({
    type: {},
    required: false,
  })
  updatedBy: {};

  @Prop({
    type: Date,
    required: false,
  })
  createdAt: Date;

  @Prop({
    type: {},
    required: false,
  })
  createdBy: {};

  @Prop({
    type: {},
    required: false,
  })
  assignedTo: {};
  @Prop({
    type: {},
    required: false,
  })
  deletedBy: {};

  @Prop({
    type: Date,
    required: false,
  })
  deletedAt: Date;

  @Prop({
    type: String,
    default: moment().startOf('month').format('MMMM'),
  })
  month: string;

  @Prop({
    type: Number,
    default: moment().week(),
  })
  week: number;

  @Prop({
    type: Number,
    default: moment().date(),
  })
  day: number;

  @Prop({
    type: Number,
    default: moment().year(),
  })
  year: number;
}

export const PermissionSchema = SchemaFactory.createForClass(Permission);
