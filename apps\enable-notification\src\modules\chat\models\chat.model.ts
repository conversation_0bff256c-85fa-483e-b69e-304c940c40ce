import { Field } from '@app/shared-stuff';
import { Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, SchemaTypes } from 'mongoose';
import { AxiosRequestHeaders } from 'axios';
import { ChatTemplateComponent } from '../types/dtos/send-chat-using-template.dto';

export type ChatDocument = Chat & Document;

@Schema({
  timestamps: true,
  collection: 'chat_logs',
})
export class Chat {
  @Field({
    type: String,
    required: true,
  })
  name: string;

  @Field({
    type: String,
    required: true,
  })
  phone: string;

  @Field({
    type: String,
    required: false,
  })
  text?: string;

  @Field({
    type: Object,
    required: false,
  })
  templateComponents?: ChatTemplateComponent[];

  @Field({
    type: Object,
    required: false,
  })
  metadata?: Record<string, any>;

  @Field({
    type: Object,
    required: false,
  })
  headers?: AxiosRequestHeaders;

  @Field({
    type: () => Object,
    required: false,
  })
  rawResponse?: Record<string, any>;

  @Field({
    type: SchemaTypes.Mixed,
    required: false,
  })
  responseData?: any;
}

export const ChatSchema = SchemaFactory.createForClass(Chat);
