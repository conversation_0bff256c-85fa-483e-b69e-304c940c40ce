// EBL-5227 Refactor pass configurations to standalone entity
// Refactor brand/company loyalty program configs to pass config

const DEFAULT_PASS_TYPE_IDENTIFIER = 'pass.tech.enable';
// const DEFAULT_PASS_TYPE_IDENTIFIER = 'pass.tech.enable.testing';

const brandPassConfigs = db.brands
  .find({
    $or: [
      { isGeofencingDisabled: { $exists: true } },
      { palette: { $exists: true } },
      { passEmptyIcon: { $exists: true } },
      { passFilledIcon: { $exists: true } },
      { passBackgroundImage: { $exists: true } },
      { 'loyaltyProgramConfig.passTypeIdentifier': { $exists: true } },
      { 'loyaltyProgramConfig.passDescriptionEn': { $exists: true } },
    ],
  })
  .map((brand) => ({
    passTypeIdentifierConfig: db.pass_type_identifier_config.findOne(
      {
        passTypeIdentifier:
          brand.loyaltyProgramConfig?.passTypeIdentifier ||
          DEFAULT_PASS_TYPE_IDENTIFIER,
      },
      { _id: 1, passTypeIdentifier: 1, passKeyFile: 1, passCertFile: 1 },
    ),

    passTemplate: {
      type: 'file',
      name: `files/${brand.name}.pass`,
      backgroundColor: brand.palette?.passBackgroundColor,
    },

    stripImageConfig: {
      backgroundImage: brand.passBackgroundImage,
      backgroundColor: brand.palette?.stripBackgroundColor,
      barConfig: {
        fillColor: brand.palette?.progressBarFillColor,
        outlineColor: brand.palette?.progressBarOutlineColor,
        textColor: brand.palette?.textColor,
      },
      minibarConfig: {
        fillColor: brand.palette?.progressBarFillColor,
        outlineColor: brand.palette?.progressBarOutlineColor,
        textColor: brand.palette?.textColor,
      },
      stampsConfig: {
        emptyStamp: brand.passEmptyIcon,
        filledStamp: brand.passFilledIcon,
      },
    },

    ...(brand.loyaltyProgramConfig?.passDescriptionEn && {
      fieldConfig: {
        description: {
          en: brand.loyaltyProgramConfig.passDescriptionEn,
          ar: brand.loyaltyProgramConfig.passDescriptionAr,
        },
      },
    }),

    isGeofencingDisabled: brand.isGeofencingDisabled,

    owner: {
      _id: brand._id,
      name: brand.name,
      type: 'brand',
    },
  }))
  .toArray();

const companyPassConfigs = db.companies
  .find(
    {
      $or: [
        { 'loyaltyProgramConfig.passBackgroundImage': { $exists: true } },
        { 'loyaltyProgramConfig.passEmptyIcon': { $exists: true } },
        { 'loyaltyProgramConfig.passFilledIcon': { $exists: true } },
        { 'loyaltyProgramConfig.passYOffset': { $exists: true } },
        { 'loyaltyProgramConfig.passStampScale': { $exists: true } },
        { 'loyaltyProgramConfig.palette': { $exists: true } },
        { 'loyaltyProgramConfig.hidePassProgressBar': { $exists: true } },
        { 'loyaltyProgramConfig.passBarcodePayload': { $exists: true } },
        { 'loyaltyProgramConfig.passBarDisplayText': { $exists: true } },
        { 'loyaltyProgramConfig.hidePassBarDisplayEndText': { $exists: true } },
      ],
    },
    { name: 1, loyaltyProgramConfig: 1 },
  )
  .map(({ _id, name, loyaltyProgramConfig }) => ({
    stripImageConfig: {
      backgroundImage: loyaltyProgramConfig.passBackgroundImage,
      backgroundColor: loyaltyProgramConfig.palette?.stripBackgroundColor,
      barConfig: {
        fillColor: loyaltyProgramConfig.palette?.progressBarFillColor,
        outlineColor: loyaltyProgramConfig.palette?.progressBarOutlineColor,
        textColor: loyaltyProgramConfig.palette?.textColor,
        yOffset: loyaltyProgramConfig.passYOffset,
        tierDisplayText: loyaltyProgramConfig.passBarDisplayText,
        hideEndText: loyaltyProgramConfig.hidePassBarDisplayEndText,
      },
      minibarConfig: {
        isHidden: loyaltyProgramConfig.hidePassProgressBar,
        fillColor: loyaltyProgramConfig.palette?.progressBarFillColor,
        outlineColor: loyaltyProgramConfig.palette?.progressBarOutlineColor,
        textColor: loyaltyProgramConfig.palette?.textColor,
      },
      stampsConfig: {
        emptyStamp: loyaltyProgramConfig.passEmptyIcon,
        filledStamp: loyaltyProgramConfig.passFilledIcon,
        yOffset: loyaltyProgramConfig.passYOffset,
        stampScale: loyaltyProgramConfig.passStampScale,
      },
    },

    barcodePayload: loyaltyProgramConfig.passBarcodePayload,

    owner: { _id, name, type: 'company' },
  }))
  .toArray();

db.pass_configs.insertMany([...brandPassConfigs, ...companyPassConfigs]);

db.brands.updateMany({}, [
  {
    $unset: [
      'isGeofencingDisabled',
      'palette',
      'passEmptyIcon',
      'passFilledIcon',
      'passBackgroundImage',
      'loyaltyProgramConfig.passDescriptionEn',
      'loyaltyProgramConfig.passTypeIdentifier',
    ],
  },
]);

db.companies.updateMany({}, [
  {
    $unset: [
      'loyaltyProgramConfig.passBackgroundImage',
      'loyaltyProgramConfig.passEmptyIcon',
      'loyaltyProgramConfig.passFilledIcon',
      'loyaltyProgramConfig.passYOffset',
      'loyaltyProgramConfig.passStampScale',
      'loyaltyProgramConfig.palette',
      'loyaltyProgramConfig.hidePassProgressBar',
      'loyaltyProgramConfig.passBarcodePayload',
      'loyaltyProgramConfig.passBarDisplayText',
      'loyaltyProgramConfig.hidePassBarDisplayEndText',
    ],
  },
]);
