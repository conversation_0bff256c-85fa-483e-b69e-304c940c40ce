import { BeeService } from '../../services/third-parties/bee/bee.service';
import { HelperService } from './../../../shared/services/helper/helper.service';
import { Request, Response } from 'express';
import { Body, Controller, Post, Req, Res, SetMetadata } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { responseCode } from '@app/shared-stuff';

@Controller('bee')
@ApiTags('Bee Delivery')
export class BeeController {
  constructor(
    private helperService: HelperService,
    private beeService: BeeService,
  ) {}

  @Post('status/updated')
  @SetMetadata('public', 'true')
  async onTaskUpdate(
    @Body() data: any,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const beeResponse = await this.beeService.onTaskUpdated(data);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'success to update bee delivery status',
        beeResponse,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
}
