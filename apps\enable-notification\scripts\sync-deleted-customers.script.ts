// EBL-4884 Deleted Customer Enrolled to a Journey
// Remove journey progress for deleted customers

// enable-main
const customerIds = db.customers
  .find({ deletedAt: { $ne: null } })
  .map((c) => c._id);

// enable-notification

db.recipients.updateMany(
  { _id: { $in: customerIds } },
  { deletedAt: new Date() },
);

db.journeys.updateMany(
  { enrolledRecipientIds: { $in: customerIds } },
  {
    $pull: {
      enrolledRecipientIds: { $in: customerIds },
      'events.$[].reachedRecipientIds': { $in: customerIds },
    },
  },
);
