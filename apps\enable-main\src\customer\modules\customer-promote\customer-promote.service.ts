import {
  CustomerDocument,
  PromoteForBrandDto,
  SendOrdableLinkDto,
  TriggerAction,
} from '@app/shared-stuff';
import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { Types } from 'mongoose';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { CompanyService } from '../../../company/services/company/company.service';
import { OrdableCustomersServiceInterface } from '../../../integration/webstore/ordable/services/customers/ordable-customers.service.interface';
import { CustomerLoyaltyServiceInterface } from '../customer-loyalty/customer-loyalty.service.interface';
import { CustomerNotificationServiceInterface } from '../customer-notification/customer-notification.service.interface';
import { CustomerRepositoryInterface } from '../customer-repository/customer.repository.interface';
import { CustomerPromoteServiceInterface } from './customer-promote.service.interface';

@Injectable()
export class CustomerPromoteService implements CustomerPromoteServiceInterface {
  constructor(
    private readonly companyService: CompanyService,
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandServiceInterface,
    @Inject(OrdableCustomersServiceInterface)
    private readonly ordableCustomersService: OrdableCustomersServiceInterface,
    @Inject(CustomerRepositoryInterface)
    private readonly customerRepository: CustomerRepositoryInterface,
    @Inject(CustomerNotificationServiceInterface)
    private readonly customerNotificationService: CustomerNotificationServiceInterface,
    @Inject(CustomerLoyaltyServiceInterface)
    private readonly customerLoyaltyService: CustomerLoyaltyServiceInterface,
  ) {}

  async sendOrdableLink({
    customerId,
    brandId,
  }: SendOrdableLinkDto): Promise<void> {
    if (!customerId) throw new BadRequestException('customerId is required');

    let customer = await this.customerRepository.findById(customerId);
    if (!customer)
      throw new NotFoundException(`Customer with ID ${customerId} not found`);

    customer =
      await this.ordableCustomersService.regenerateOrdableLink(customer);

    await this.customerNotificationService.fireOnSendOrdableLinkTrigger(
      customer,
      brandId,
    );
  }

  private async updateActiveBrandOnPromote(
    customers: CustomerDocument[],
    brandId: Types.ObjectId,
  ): Promise<void> {
    const brand = await this.brandService.findById(brandId);
    const embeddedBrandDto = this.brandService.toEmbeddedBrandDto(brand);
    const needsActiveBrand = (customer: CustomerDocument) =>
      !customer.activeBrand;
    const updateActiveBrand = async (customer: CustomerDocument) =>
      await customer.updateOne({ activeBrand: embeddedBrandDto });

    await Promise.all(
      customers.filter(needsActiveBrand).map(updateActiveBrand),
    );
  }

  async promoteLoyaltyForBrand(
    promoteLoyaltyStuffDto: PromoteForBrandDto,
    action: 'LoyaltyCard' | 'Registration',
  ): Promise<void> {
    if (promoteLoyaltyStuffDto.phone && action != 'LoyaltyCard') {
      await this.promoteLoyaltyForBrandByPhone(promoteLoyaltyStuffDto, action);
    }

    if (!promoteLoyaltyStuffDto.customerIds) return;

    const { customerIds, brandId } = promoteLoyaltyStuffDto;
    const customers: CustomerDocument[] =
      await this.customerRepository.findManyById(
        Array.isArray(customerIds) ? customerIds : [customerIds],
      );

    const sendNotification = async (customer: CustomerDocument) =>
      await this.customerNotificationService.fireLoyaltyCardStatusOrRegistrationPromotion(
        customer,
        brandId,
        action == 'Registration'
          ? TriggerAction.ON_SEND_LOYALTY_REGISTRATION
          : TriggerAction.ON_SEND_LOYALTY_CARD,
        promoteLoyaltyStuffDto.branchId,
      );

    await Promise.all([
      ...customers.map(sendNotification),
      this.updateActiveBrandOnPromote(customers, brandId),
    ]);
  }

  private async promoteLoyaltyForBrandByPhone(
    { phone, countryCode, brandId, source, branchId }: PromoteForBrandDto,
    action: 'LoyaltyCard' | 'Registration',
  ): Promise<void> {
    const brand = await this.brandService.findById(brandId);
    const company = await this.companyService.get_details(
      brand.companyId.toHexString(),
    );
    return this.customerNotificationService.fireLoyaltyCardStatusOrRegistrationPromotionByPhone(
      brand,
      phone,
      countryCode,
      action == 'Registration'
        ? TriggerAction.ON_SEND_LOYALTY_REGISTRATION
        : TriggerAction.ON_SEND_LOYALTY_CARD,
      await this.customerLoyaltyService.getLoyaltyRegistrationPageLink(
        brand._id,
        company,
        null,
        source,
        branchId,
      ),
    );
  }
}
