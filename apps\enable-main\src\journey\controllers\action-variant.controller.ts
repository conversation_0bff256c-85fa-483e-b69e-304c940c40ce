import {
  Action,
  ActionVariantIdDto,
  CreateActionVariantDto,
  GenericExceptionFilter,
  IndexActionVariantDto,
  Journey,
  TemplateDocument,
  TransformInterceptor,
  UpdateActionVariantDto,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  Param,
  Patch,
  Post,
  Query,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { ActionVariantServiceInterface } from '../services/action-variant/action-variant.service.interface';

@Controller('action-variant')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags('ActionVariant')
@SetMetadata('module', 'action-variant')
export class ActionVariantController {
  constructor(
    @Inject(ActionVariantServiceInterface)
    private readonly actionVariantService: ActionVariantServiceInterface,
  ) {}

  @Get()
  @ApiOkResponse({ type: Action, isArray: true })
  @SetMetadata('action', 'index')
  async index(
    @Query() indexActionVariantDto: IndexActionVariantDto,
  ): Promise<Action> {
    return await this.actionVariantService.index(indexActionVariantDto);
  }

  @Post()
  @ApiOkResponse({ type: Journey })
  @SetMetadata('action', 'create')
  async create(
    @Body() createActionVariantDto: CreateActionVariantDto,
  ): Promise<TemplateDocument> {
    return await this.actionVariantService.create(createActionVariantDto);
  }

  @Patch(':actionVariantId')
  @ApiOkResponse({ type: Journey })
  @SetMetadata('action', 'update')
  async update(
    @Param() { actionVariantId }: ActionVariantIdDto,
    @Body()
    updateActionVariantDto: Omit<UpdateActionVariantDto, '_id'>,
  ): Promise<TemplateDocument> {
    return await this.actionVariantService.update({
      _id: actionVariantId,
      ...updateActionVariantDto,
    });
  }

  @Delete(':actionVariantId')
  @ApiOkResponse({ type: Journey })
  @SetMetadata('action', 'delete')
  async delete(@Param() { actionVariantId }: ActionVariantIdDto) {
    return await this.actionVariantService.delete(actionVariantId);
  }
}
