import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class TokenToGenerate {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  userEmail: string;
}

export class CallToNotify {
  id: string;
  caller: string;
  callee: string;
  caller_number: string;
  callee_number: string;
  inputs: string[];
  state: string;
  flags: string[];
  direction: string;
  timestamp: string;
  calls: any[];
  audient: any;
  agents: any[];
}
