import {
  ActionGroup,
  ActionGroupIdDto,
  CreateActionGroupDto,
  GenericExceptionFilter,
  Journey,
  TransformInterceptor,
  UpdateActionGroupDto,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Inject,
  Param,
  Patch,
  Post,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { ActionGroupServiceInterface } from '../services/action/action-group.service.interface';

@Controller('action-group')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags('Action Group')
@SetMetadata('module', 'action')
export class ActionGroupController {
  constructor(
    @Inject(ActionGroupServiceInterface)
    private readonly actionGroupService: ActionGroupServiceInterface,
  ) {}

  @Post()
  @ApiOkResponse({ type: ActionGroup })
  @SetMetadata('action', 'create')
  async create(
    @Body() createActionGroupDto: CreateActionGroupDto,
  ): Promise<ActionGroup> {
    return await this.actionGroupService.create(createActionGroupDto);
  }

  @Patch(':groupId')
  @ApiOkResponse({ type: ActionGroup })
  @SetMetadata('action', 'update')
  async update(
    @Param() { groupId }: ActionGroupIdDto,
    @Body() updateActionGroupDto: Omit<UpdateActionGroupDto, 'groupId'>,
  ): Promise<ActionGroup> {
    return await this.actionGroupService.update({
      groupId,
      ...updateActionGroupDto,
    });
  }

  @Delete(':groupId')
  @ApiOkResponse({ type: Journey })
  @SetMetadata('action', 'delete')
  async delete(@Param() { groupId }: ActionGroupIdDto) {
    return await this.actionGroupService.delete(groupId);
  }
}
