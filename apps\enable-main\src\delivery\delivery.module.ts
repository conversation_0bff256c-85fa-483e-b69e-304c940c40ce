import {
  <PERSON><PERSON><PERSON>,
  createOrderSchemaWithHooks,
  SharedStuffModule,
} from '@app/shared-stuff';
import { HttpModule } from '@nestjs/axios';
import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitter2, EventEmitterModule } from '@nestjs/event-emitter';
import { MongooseModule } from '@nestjs/mongoose';

import { DriverSchema } from '../../../../libs/shared-stuff/src/models/driver.model';
import { CustomerReadModule } from '../customer/modules/customer-read/customer-read.module';
import { IntegrationLogModule } from '../integration/integration-log/integration-log.module';
import { LocationModule } from '../location/location.module';
import { SharedModule } from '../shared/shared.module';
import { StorageModule } from '../storage/storage.module';
import { CompanyModule } from './../company/company.module';
import { Bee<PERSON>ontroller } from './controllers/bee/bee.controller';
import { DeliveryConfigurationController } from './controllers/delivery-configuration/delivery-configuration.controller';
import { DeliveryThirdPartyController } from './controllers/delivery-third-party/delivery-third-party.controller';
import { DeliveryController } from './controllers/delivery/delivery.controller';
import { DriverController } from './controllers/driver/driver.controller';
import { MrDeliveryController } from './controllers/mr-delivery/mr-delivery.controller';
import { SlotController } from './controllers/slot/slot.controller';
import { DeliveryConfigurationSchema } from './models/delivery-configuration.model';
import {
  DeliveryThirdParty,
  DeliveryThirdPartySchema,
} from './models/delivery-third-party.model';
import { SlotSchema } from './models/slot.model';
import { DeliveryConfigurationRepository } from './repositories/delivery-configuration.repository';
import { DeliveryConfigurationRepositoryInterface } from './repositories/delivery-configuration.repository.interface';
import { DeliveryThirdPartyRepositoryInterface } from './repositories/delivery-third-party/delivery-third-party.repositories.interface';
import { DeliveryThirdPartyRepository } from './repositories/delivery-third-party/delivery-third-party.repository';
import { DeliveryConfigurationService } from './services/delivery-configuration/delivery-configuration.service';
import { DeliveryConfigurationServiceInterface } from './services/delivery-configuration/delivery-configuration.service.interface';
import { DeliveryThirdPartyService } from './services/delivery-third-party/delivery-third-party.service';
import { DeliveryThirdPartyServiceInterface } from './services/delivery-third-party/delivery-third-party.service.interface';
import { DeliveryService } from './services/delivery/delivery.service';
import { DriverService } from './services/driver/driver.service';
import { SlotService } from './services/slot/slot.service';
import { BeeService } from './services/third-parties/bee/bee.service';
import { ClicksService } from './services/third-parties/clicks/clicks.service';
import { FalconFlexController } from './services/third-parties/falcon-flex/controllers/falcon-flex.controller';
import { FalconFlexListener } from './services/third-parties/falcon-flex/listeners/falcon-flex.listener';
import { FalconFlexService } from './services/third-parties/falcon-flex/services/falcon-flex.service';
import { MrDeliveryService } from './services/third-parties/mr-delivery/mr-delivery.service';
import { PassDeliveryListener } from './services/third-parties/pass-delivery/listeners/pass-delivery.listener';
import { PassDeliveryService } from './services/third-parties/pass-delivery/services/pass-delivery.service';
import { ThirdPartySharedService } from './services/third-parties/third-party-shared.service';
import { ThirdPartyService } from './services/third-parties/third-party.service';
import { WishboxController } from './services/third-parties/wishbox/controllers/wishbox.controller';
import { WishboxService } from './services/third-parties/wishbox/services/wishbox.service';
import { WishboxServiceInterface } from './services/third-parties/wishbox/services/wishbox.service.interface';
import { TookanService } from './services/tookan/tookan.service';

@Module({
  imports: [
    LocationModule,
    SharedModule,
    ConfigModule,
    forwardRef(() => CompanyModule),
    HttpModule,
    SharedStuffModule,
    EventEmitterModule,
    IntegrationLogModule,
    CustomerReadModule,
    StorageModule,
    MongooseModule.forFeatureAsync([
      { name: 'Slot', useFactory: () => SlotSchema },
      { name: 'Driver', useFactory: () => DriverSchema },
      {
        name: 'DeliveryConfiguration',
        useFactory: () => DeliveryConfigurationSchema,
      },
      {
        name: DeliveryThirdParty.name,
        useFactory: () => DeliveryThirdPartySchema,
      },
      {
        name: CollectionName.ORDER,
        imports: [],
        useFactory: (eventEmitter: EventEmitter2) => {
          return createOrderSchemaWithHooks(eventEmitter);
        },
        inject: [EventEmitter2],
      },
    ]),
  ],
  exports: [
    SlotService,
    DriverService,
    TookanService,
    BeeService,
    MrDeliveryService,
    ClicksService,
    WishboxService,
    PassDeliveryService,
    ThirdPartySharedService,
    DeliveryConfigurationServiceInterface,
    ThirdPartyService,
  ],
  providers: [
    SlotService,
    DeliveryService,
    TookanService,
    DriverService,
    BeeService,
    MrDeliveryService,
    ClicksService,
    ThirdPartySharedService,
    WishboxService,
    FalconFlexService,
    FalconFlexListener,
    PassDeliveryService,
    PassDeliveryListener,
    ThirdPartyService,
    {
      provide: DeliveryThirdPartyServiceInterface,
      useClass: DeliveryThirdPartyService,
    },
    {
      provide: DeliveryThirdPartyRepositoryInterface,
      useClass: DeliveryThirdPartyRepository,
    },
    {
      provide: DeliveryConfigurationServiceInterface,
      useClass: DeliveryConfigurationService,
    },
    {
      provide: DeliveryConfigurationRepositoryInterface,
      useClass: DeliveryConfigurationRepository,
    },
    {
      provide: WishboxServiceInterface,
      useClass: WishboxService,
    },
  ],
  controllers: [
    SlotController,
    DeliveryController,
    DriverController,
    BeeController,
    MrDeliveryController,
    DeliveryConfigurationController,
    WishboxController,
    FalconFlexController,
    DeliveryThirdPartyController,
  ],
})
export class DeliveryModule {}
