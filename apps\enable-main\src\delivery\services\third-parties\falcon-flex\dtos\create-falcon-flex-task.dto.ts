import { Type } from 'class-transformer';
import { IsNotEmpty, IsOptional } from 'class-validator';

import { FalconFlexItem } from '../types/falcon-flex-item.type';
import { FalconFlexLocationQatar } from '../types/falcon-flex-location-qatar.type';
import { FalconFlexLocationInternational } from '../types/falcon-flex-location-international.type';
import { FalconFlexAutoAssignmentConfig } from '../types/falcon-flex-auto-assignment-config.type';
import { FalconFlexTransportTypeIdEnum } from '../enums/falcon-flex-transport-type-id.enum';
import { FalconFlexLocationTypeId } from '../enums/falcon-flex-location-type-id.enum';

export class CreateFalconFlexTaskDto {
  @IsNotEmpty()
  transportTypeId: FalconFlexTransportTypeIdEnum;

  @IsNotEmpty()
  amountToBeCollected: number;

  @IsNotEmpty()
  @Type(() => FalconFlexItem)
  taskItems: FalconFlexItem[];

  @IsNotEmpty()
  pickupByUtc: Date;

  @IsNotEmpty()
  deliverByUtc: Date;

  @IsNotEmpty()
  pickupLocationTypeId: FalconFlexLocationTypeId;

  @IsNotEmpty()
  pickup: FalconFlexLocationInternational | FalconFlexLocationQatar;

  @IsNotEmpty()
  deliveryLocationTypeId: FalconFlexLocationTypeId;

  @IsNotEmpty()
  delivery: FalconFlexLocationInternational | FalconFlexLocationQatar;

  @IsOptional()
  notes?: string;

  @IsOptional()
  clientGeneratedId?: string;

  @IsOptional()
  sendTrackingMessage? = false;

  @IsOptional()
  isProofOfDeliveryRequired? = false;

  @IsOptional()
  isProofOfPickupRequired? = false;

  @IsOptional()
  @Type(() => FalconFlexAutoAssignmentConfig)
  autoAssignmentConfig?: FalconFlexAutoAssignmentConfig;
}
