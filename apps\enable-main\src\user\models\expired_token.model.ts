import { CurrentUser } from '@app/shared-stuff';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { HydratedDocument } from 'mongoose';

export type ExpiredTokenDocument = HydratedDocument<ExpiredToken>;

@Schema({ timestamps: true, collection: 'expired_tokens' })
export class ExpiredToken {
  @Prop({
    type: String,
    required: true,
    index: true,
  })
  token: string;

  @Prop({
    type: Date,
    required: true,
    default: () => new Date(),
    expires: '30d', // TTL index to auto-remove expired tokens after 30 days
  })
  createdAt: Date;

  @Prop({ type: {}, required: false })
  updatedBy: CurrentUser;

  @Prop({ type: {}, required: false })
  createdBy: CurrentUser;

  @Prop({ type: {}, required: false })
  assignedTo: CurrentUser;

  @Prop({ type: {}, required: false })
  deletedBy: CurrentUser;

  @Prop({ type: Date, required: false })
  deletedAt: Date;

  @Prop({
    type: String,
    default: moment().startOf('month').format('MMMM'),
  })
  month: string;

  @Prop({
    type: Number,
    default: moment().week(),
  })
  week: number;

  @Prop({
    type: Number,
    default: moment().date(),
  })
  day: number;

  @Prop({
    type: Number,
    default: moment().year(),
  })
  year: number;
}

export const ExpiredTokenSchema = SchemaFactory.createForClass(ExpiredToken);

ExpiredTokenSchema.index({ token: 1 });
ExpiredTokenSchema.index({ createdAt: 1 }, { expireAfterSeconds: 2592000 }); // TTL 30 days
