import {
  LoggerService,
  MicrosAuthGrantType,
  MicrosSignInParams,
  MicrosSignInResponse,
} from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import { Injectable, InternalServerErrorException } from '@nestjs/common';

@Injectable()
export class MicrosAuthHttpService {
  private readonly logger = new LoggerService(MicrosAuthHttpService.name);
  private readonly CODE_CHALLENGE_METHOD = 'S256';

  constructor(private readonly httpService: HttpService) {}

  async getToken(
    authBaseUrl: string,
    authorizationCodeOrRefreshToken: string,
    clientId: string,
    codeVerifier: string,
    grantType: MicrosAuthGrantType,
  ): Promise<any> {
    const URL = `${authBaseUrl}/token`;
    this.logger.log(
      'authorizationCode or refresh token inside getToken',
      authorizationCodeOrRefreshToken,
    );
    return new Promise(async (resolve) => {
      this.httpService
        .post(
          URL,
          {
            grant_type: grantType,
            client_id: clientId,
            code_verifier: codeVerifier,
            scope: 'openid',
            [grantType === MicrosAuthGrantType.AUTHORIZATION_CODE
              ? 'code'
              : 'refresh_token']: authorizationCodeOrRefreshToken,
          },
          { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } },
        )
        .subscribe({
          next: (data) => {
            resolve(data.data ?? {});
          },
          error: (error) => {
            this.logger.error(
              'Error while get token micros',
              error.stacktrace,
              error,
            );
            resolve({ status: 'error', message: JSON.stringify(error) });
          },
        });
    });
  }

  async authorize(
    authBaseUrl: string,
    clientId: string,
    codeChallenge: string,
  ): Promise<void> {
    const URL =
      `${authBaseUrl}/authorize?` +
      `scope=openid&response_type=code&client_id=${clientId}&` +
      `redirect_uri=apiaccount://callback&` +
      `code_challenge=${codeChallenge}&` +
      `code_challenge_method=${this.CODE_CHALLENGE_METHOD}`;

    return new Promise(async (resolve, reject) => {
      this.httpService.get(URL).subscribe({
        next: (data) => {
          resolve(data.data ?? {});
        },
        error: (error) => {
          this.logger.error(
            'Error while authorize micros',
            error.stacktrace,
            error,
          );
          reject(error);
        },
      });
    });
  }

  async signIn({
    clientId,
    apiAccountUsername,
    apiAccountPassword,
    organizationName,
    authBaseUrl,
    codeChallenge,
  }: MicrosSignInParams): Promise<string> {
    const signInUrl = `${authBaseUrl}/signin`;
    const HEADERS = {
      'Content-Type': 'application/x-www-form-urlencoded',
      Cookie: ` client_id=${clientId}; redirect_uri=apiaccount://callback; code_challenge=${codeChallenge}; code_challenge_method=${this.CODE_CHALLENGE_METHOD}`,
    };

    return new Promise(async (resolve, reject) => {
      this.httpService
        .post(
          signInUrl,
          {
            username: apiAccountUsername,
            password: apiAccountPassword,
            orgname: organizationName,
          },
          {
            headers: HEADERS,
          },
        )
        .subscribe({
          next: (data: { data: MicrosSignInResponse }) => {
            const redirectUrl = data?.data?.redirectUrl;
            if (data?.data?.success && redirectUrl) {
              resolve(new URL(redirectUrl).searchParams.get('code'));
            } else {
              reject(
                new InternalServerErrorException(
                  data,
                  'Unknown response while sign in micros',
                ),
              );
            }
          },
          error: (error) => {
            this.logger.error(
              'Error while sign in micros',
              error.stacktrace,
              error,
            );
            reject(error);
          },
        });
    });
  }
}
