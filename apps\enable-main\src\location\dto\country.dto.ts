import { DataIndex } from '@app/shared-stuff';
import { ApiProperty } from '@nestjs/swagger';

export class CountryToUpdate {
  @ApiProperty({
    type: String,
    required: true,
  })
  _id: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  name: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  code: string;
}

export class CountryToCreate {
  @ApiProperty({
    type: String,
    required: true,
  })
  name: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  code: string;
}

export class CountryToIndex extends DataIndex {}
