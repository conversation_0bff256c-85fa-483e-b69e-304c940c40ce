// EBL-4518 Optional OTP
// Refactor VerificationContext into RegistrationContext

db.customers.updateMany(
  { loyaltyStatus: 'member', verificationContext: { $exists: true } },
  [
    {
      $set: {
        registrationContext: {
          brandId: '$verificationContext.brandId',
          enrollmentCode: '$verificationContext.enrollmentCode',
          loyaltyRegistrationBranchId:
            '$verificationContext.loyaltyRegistrationBranchId',
          source: '$verificationContext.source',
          rawSource: '$verificationContext.rawSource',
        },
      },
    },
    {
      $unset: [
        'verificationContext.enrollmentCode',
        'verificationContext.loyaltyRegistrationBranchId',
        'verificationContext.source',
        'verificationContext.rawSource',
      ],
    },
  ],
);
