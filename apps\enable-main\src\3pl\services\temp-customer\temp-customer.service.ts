import {
  AcquireLocationTriggerDto,
  CreateTempCustomerDto,
  GetAllTempCustomerDto,
  HelperSharedServiceInterface,
  IHelperSharedService,
  IndexResultDto,
  LanguageCode,
  LanguageToLanguageCode,
  sanitizePhoneNumber,
  TempCustomer,
  TempCustomerDocument,
  TemplateTo,
  TriggerAction,
  TriggerModule,
  UpdateTempCustomerDto,
} from '@app/shared-stuff';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { isValidPhoneNumber } from 'libphonenumber-js';
import { FilterQuery, PipelineStage, Types } from 'mongoose';
import { TriggerService } from '../../../notification/services/trigger/trigger.service';
import { TempCustomerRepositoryInterface } from '../../repositories/temp-customer/temp-customer-repository.interface';
import { Shared3plService } from '../shared-3pl.service';
import { TempCustomerServiceInterface } from './temp-customer-service.interface';

@Injectable()
export class TempCustomerService implements TempCustomerServiceInterface {
  constructor(
    @Inject(TempCustomerRepositoryInterface)
    private repository: TempCustomerRepositoryInterface,
    private sharedService: Shared3plService,
    @Inject(IHelperSharedService)
    private helperSharedService: HelperSharedServiceInterface,
    private triggerService: TriggerService,
  ) {}

  async findAll(
    getAllTempCustomerDto: GetAllTempCustomerDto,
  ): Promise<IndexResultDto<TempCustomer>[]> {
    const pipeline: PipelineStage[] = [];
    this.addMatchStage(pipeline, getAllTempCustomerDto);
    this.addPaginationStage(pipeline, getAllTempCustomerDto);
    return (await this.repository.aggregate(pipeline)) as [
      IndexResultDto<TempCustomer>,
    ];
  }

  async create({
    phone,
    countryCode,
    location,
    ...createTempCustomerDto
  }: CreateTempCustomerDto): Promise<TempCustomerDocument> {
    const customer = plainToInstance(TempCustomer, createTempCustomerDto);
    const extractedPhoneNumber = sanitizePhoneNumber(phone, countryCode);

    customer.phone = extractedPhoneNumber.phone;
    customer.countryCode = extractedPhoneNumber.countryCode;

    if (location) {
      customer.location = await this.sharedService.createAddress(location);
    }

    if (!customer.brand && createTempCustomerDto.eblBrandId) {
      customer.brand = await this.sharedService.fetchEmbeddedBrand(
        createTempCustomerDto.eblBrandId,
      );
    }

    return this.repository.create(customer);
  }

  async update({
    phone,
    countryCode,
    location,
    ...updateTempCustomerDto
  }: UpdateTempCustomerDto): Promise<TempCustomerDocument> {
    const customer = await this.findOne(updateTempCustomerDto._id);
    const extractedPhoneNumber = sanitizePhoneNumber(phone, countryCode);

    customer.phone = extractedPhoneNumber.phone;
    customer.countryCode = extractedPhoneNumber.countryCode;

    if (location) {
      customer.location = await this.sharedService.createAddress(location);
    }

    if (updateTempCustomerDto.eblBrandId) {
      customer.brand = await this.sharedService.fetchEmbeddedBrand(
        updateTempCustomerDto.eblBrandId,
      );
    }

    return this.repository.findOneAndUpdate(
      { _id: updateTempCustomerDto._id },
      { $set: { ...customer.toJSON(), ...updateTempCustomerDto } },
    );
  }

  async updateOne(
    salesOrderId: string,
    { countryCode, phone, location }: Partial<UpdateTempCustomerDto>,
  ): Promise<TempCustomerDocument> {
    const customer = await this.findBySalesOrderId(salesOrderId);
    const updates: Partial<TempCustomer> = {};

    if (phone !== customer.phone || countryCode !== customer.countryCode) {
      const newCountryCode = countryCode ?? customer.countryCode;
      if (
        !isValidPhoneNumber(phone, {
          defaultCallingCode: newCountryCode.replace('+', ''),
        })
      )
        throw new BadRequestException(
          `Phone number '${phone}' is invalid for countryCode '${newCountryCode}'`,
        );

      updates.countryCode = newCountryCode;
      updates.phone = phone;
    }

    if (location) {
      updates.location = await this.sharedService.updateAddress(
        customer.location,
        location,
      );
    }

    return this.repository.findOneAndUpdate(
      { _id: customer._id },
      { $set: updates },
    );
  }

  async findOne(id: Types.ObjectId) {
    const tempCustomer = await this.repository.findById(id);
    if (!tempCustomer) {
      throw new Error('Temp customer not found');
    }
    return tempCustomer;
  }

  async findBySalesOrderId(
    salesOrderId: string,
  ): Promise<TempCustomerDocument> {
    return this.repository.findOneBySalesOrderId(salesOrderId);
  }

  async fireAcquireLocationTrigger({
    tempCustomerId,
    currentUser,
  }: AcquireLocationTriggerDto) {
    const tempCustomer = await this.findOne(tempCustomerId);

    if (!tempCustomer.countryCode || !tempCustomer.phone)
      throw new BadRequestException(
        'Customer must have country code and phone number.',
      );

    const language =
      tempCustomer.brand?.preferredLanguage in LanguageToLanguageCode
        ? LanguageToLanguageCode[tempCustomer.brand.preferredLanguage]
        : LanguageCode.en;

    await this.triggerService.fireTrigger(
      {
        companyId: tempCustomer.companyId.toString(),
        branchId: '',
        brandId: tempCustomer.brand?._id.toString(),
        senderId: tempCustomer.brand?.senderId,
        emailSenderId: tempCustomer.brand?.emailSenderId,
        language,
        customerId: null,
        countryCode: tempCustomer.countryCode,
        createdBy: currentUser,
        giftRecipientUser: null,
        isGift: false,
        triggerModule: TriggerModule.TEMP_CUSTOMER,
        replacements: {},
      },
      TriggerAction.ON_ACQUIRE_CUSTOMER_LOCATION,
      [
        {
          id: tempCustomer._id,
          countryCode: tempCustomer.countryCode,
          phone: tempCustomer.phone,
          firstName: tempCustomer.firstName,
          lastName: tempCustomer.lastName,
          email: tempCustomer.email,
          role: TemplateTo.CUSTOMER,
          preferredLanguage: language,
        },
      ],
    );
  }

  private addMatchStage(
    pipeline: PipelineStage[],
    getAllTempCustomerDto: GetAllTempCustomerDto,
  ) {
    const match: FilterQuery<TempCustomer> = {};
    if (getAllTempCustomerDto.companyId) {
      match.companyId = getAllTempCustomerDto.companyId;
    }
    if (getAllTempCustomerDto.brandId) {
      match['brand._id'] = getAllTempCustomerDto.brandId;
    }

    if (getAllTempCustomerDto.salesOrderId) {
      match.salesOrderId = getAllTempCustomerDto.salesOrderId;
    }
    pipeline.push({ $match: match });
  }

  private addPaginationStage(
    pipeline: PipelineStage[],
    getAllTempCustomerDto: GetAllTempCustomerDto,
  ) {
    pipeline.push(
      this.helperSharedService.createPaginationStage(
        getAllTempCustomerDto.offset,
        getAllTempCustomerDto.limit,
      ),
    );
  }
}
