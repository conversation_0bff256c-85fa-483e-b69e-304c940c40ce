import {
  ChatConfigDto,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  HelperSharedServiceInterface,
  IHelperSharedService,
  LoggerService,
  SuccessResult,
} from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import { Inject, Injectable } from '@nestjs/common/decorators';
import { ConfigService } from '@nestjs/config';
import { AxiosError } from 'axios';
import * as bcrypt from 'bcrypt';
import { lastValueFrom } from 'rxjs';
import { ChatSendDto } from '../../../types/dtos/chat-send.dto';
import { SendChatUsingTemplateDto } from '../../../types/dtos/send-chat-using-template.dto';
import { ChatIntegrationsServiceInterface } from '../chat-intergrations.service.interface';

@Injectable()
export class EbChatService implements ChatIntegrationsServiceInterface {
  private readonly loggerService = new LoggerService(EbChatService.name);
  private readonly EBCHAT_BASE_URL: string;

  constructor(
    private httpService: HttpService,
    private configService: ConfigService,
    @Inject(IHelperSharedService)
    private helperSharedService: HelperSharedServiceInterface,
  ) {
    this.EBCHAT_BASE_URL = this.configService.get('EB_CHAT_BASE_URL');
  }

  async send(
    chatSendDto: ChatSendDto,
    chatConfigDto: ChatConfigDto,
  ): Promise<SuccessResult | ErrorResult> {
    const baseURL = `${this.EBCHAT_BASE_URL}/whatsapp/notifyWhatsappEnable`;
    const HEADERS = await this.constructRequestHeaders(
      chatConfigDto.ebChatConfig.apiKey,
      chatSendDto,
    );
    return new Promise<SuccessResult | ErrorResult>(async (resolve) => {
      this.httpService
        .post(baseURL, chatSendDto, {
          headers: HEADERS,
        })
        .subscribe({
          next: (data) => {
            resolve({
              success: true,
              status: 'success',
              data: data.data ?? data,
              headers: HEADERS,
            });
          },
          error: (err: AxiosError) => {
            this.loggerService.error(
              `Trying to POST ${baseURL} With Request Body : ${chatSendDto} + and got Error Message ` +
                err.message,
              err.stack,
              chatConfigDto,
            );
            resolve(this.helperSharedService.transformError(err));
          },
        });
    });
  }

  private async constructRequestHeaders(
    apiKey: string,
    body: any,
  ): Promise<any> {
    return {
      EBCHATKEY: apiKey,
      APP: 'enable_app',
      signature: await this.generateSignature(body),
    } as any;
  }

  async generateSignature(body: any): Promise<string> {
    const salt = this.configService.get('EB_CHAT_SALT');
    return await bcrypt.hash(JSON.stringify(body), salt);
  }

  async getTemplates(chatConfigDto: ChatConfigDto) {
    const baseURL = `${this.EBCHAT_BASE_URL}/whatsapp/getTemplates`;

    const HEADERS = await this.constructRequestHeaders(
      chatConfigDto.ebChatConfig.apiKey,
      {},
    );
    return lastValueFrom(this.httpService.get(baseURL, { headers: HEADERS }));
  }
  async sendUsingTemplate(
    chatSendDto: SendChatUsingTemplateDto,
    chatConfigDto: ChatConfigDto,
  ) {
    const baseURL = `${this.EBCHAT_BASE_URL}/whatsapp/sendTemplateMessage`;
    const HEADERS = await this.constructRequestHeaders(
      chatConfigDto.ebChatConfig.apiKey,
      chatSendDto,
    );
    const response = await lastValueFrom(
      this.httpService.post(baseURL, chatSendDto, { headers: HEADERS }),
    );
    return response.data;
  }
}
