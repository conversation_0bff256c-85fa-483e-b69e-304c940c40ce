import { Inject, Injectable } from '@nestjs/common';
import { Types } from 'mongoose';

import {
  CalendarCycle,
  CurrentUser,
  CustomerDocument,
  LoyaltyTierLog,
  LoyaltyTierLogAction,
  LoyaltyTierLogDocument,
  LoyaltyTierLogTrigger,
} from '@app/shared-stuff';
import { LoyaltyTierLogRepositoryInterface } from '../repositories/loyalty-tier-log.repository.interface';
import { LoyaltyTierLogServiceInterface } from './loyalty-tier-log.service.interface';

@Injectable()
export class LoyaltyTierLogService implements LoyaltyTierLogServiceInterface {
  constructor(
    @Inject(LoyaltyTierLogRepositoryInterface)
    private readonly loyaltyTierLogRepository: LoyaltyTierLogRepositoryInterface,
  ) {}

  log(log: LoyaltyTierLog): Promise<LoyaltyTierLogDocument> {
    return this.loyaltyTierLogRepository.create(log);
  }

  findHighestAssignedTierId(
    customer: CustomerDocument,
    calendarCycle: CalendarCycle,
  ): Promise<Types.ObjectId> {
    return this.loyaltyTierLogRepository.findHighestAssignedTierId(
      customer,
      calendarCycle,
    );
  }

  onManualTierDowngrade(
    customer: CustomerDocument,
    previousTierId: Types.ObjectId,
  ): Promise<LoyaltyTierLogDocument> {
    return this.loyaltyTierLogRepository.create({
      ...this.mapToLog(customer, previousTierId),
      action: LoyaltyTierLogAction.DOWNGRADE,
      trigger: LoyaltyTierLogTrigger.MANUAL_TIER_UPDATE,
    });
  }

  onManualTierUpgrade(
    customer: CustomerDocument,
    previousTierId: Types.ObjectId = null,
  ): Promise<LoyaltyTierLogDocument> {
    return this.loyaltyTierLogRepository.create({
      ...this.mapToLog(customer, previousTierId),
      action: LoyaltyTierLogAction.UPGRADE,
      trigger: LoyaltyTierLogTrigger.MANUAL_TIER_UPDATE,
    });
  }

  onOrderCompletionUpgrade(
    customer: CustomerDocument,
    previousTierId?: Types.ObjectId,
  ): Promise<LoyaltyTierLogDocument> {
    return this.loyaltyTierLogRepository.create({
      ...this.mapToLog(customer, previousTierId),
      action: LoyaltyTierLogAction.UPGRADE,
      trigger: LoyaltyTierLogTrigger.ORDER_COMPLETION,
    });
  }

  onTierRequirementsLowered(
    customer: CustomerDocument,
    previousTierId?: Types.ObjectId,
  ): Promise<LoyaltyTierLogDocument> {
    return this.loyaltyTierLogRepository.create({
      ...this.mapToLog(customer, previousTierId),
      action: LoyaltyTierLogAction.UPGRADE,
      trigger: LoyaltyTierLogTrigger.TIER_REQUIREMENTS_LOWERED,
    });
  }

  onCalendarSystemUpdated(
    customer: CustomerDocument,
    previousTierId?: Types.ObjectId,
  ): Promise<LoyaltyTierLogDocument> {
    return this.loyaltyTierLogRepository.create({
      ...this.mapToLog(customer, previousTierId),
      action: LoyaltyTierLogAction.UPGRADE,
      trigger: LoyaltyTierLogTrigger.CALENDAR_SYSTEM_UPDATED,
    });
  }

  onTierComputationMaintain(
    customer: CustomerDocument,
  ): Promise<LoyaltyTierLogDocument> {
    return this.loyaltyTierLogRepository.create({
      ...this.mapToLog(customer),
      action: LoyaltyTierLogAction.MAINTAIN,
      trigger: LoyaltyTierLogTrigger.MONTHLY_TIER_COMPUTATION,
    });
  }

  onTierComputationDowngrade(
    customer: CustomerDocument,
    previousTierId: Types.ObjectId,
  ): Promise<LoyaltyTierLogDocument> {
    return this.loyaltyTierLogRepository.create({
      ...this.mapToLog(customer, previousTierId),
      action: LoyaltyTierLogAction.DOWNGRADE,
      trigger: LoyaltyTierLogTrigger.MONTHLY_TIER_COMPUTATION,
    });
  }

  onOrderDeletionDowngrade(
    customer: CustomerDocument,
    previousTierId: Types.ObjectId,
  ): Promise<LoyaltyTierLogDocument> {
    return this.loyaltyTierLogRepository.create({
      ...this.mapToLog(customer, previousTierId),
      action: LoyaltyTierLogAction.DOWNGRADE,
      trigger: LoyaltyTierLogTrigger.ORDER_DELETION,
    });
  }

  onTierDiscountRedeem(
    customer: CustomerDocument,
  ): Promise<LoyaltyTierLogDocument> {
    return this.loyaltyTierLogRepository.create({
      ...this.mapToLog(customer),
      action: LoyaltyTierLogAction.REDEEM,
      trigger: LoyaltyTierLogTrigger.ORDER_COMPLETION,
    });
  }

  onBaseTierAssignment(
    customer: CustomerDocument,
    previousTierId: Types.ObjectId,
  ): Promise<LoyaltyTierLogDocument> {
    return this.loyaltyTierLogRepository.create({
      ...this.mapToLog(customer, previousTierId),
      action: LoyaltyTierLogAction.ASSIGN,
      trigger: LoyaltyTierLogTrigger.BASE_TIER_ASSIGNMENT,
    });
  }

  onBulkTierAssignment(
    customer: CustomerDocument,
    previousTierId: Types.ObjectId,
    currentUser: CurrentUser,
  ): Promise<LoyaltyTierLogDocument> {
    return this.loyaltyTierLogRepository.create({
      ...this.mapToLog(customer, previousTierId, currentUser),
      action: LoyaltyTierLogAction.ASSIGN,
      trigger: LoyaltyTierLogTrigger.BULK_TIER_ASSIGNMENT,
    });
  }

  onFreeDeliveryRedeem(
    customer: CustomerDocument,
  ): Promise<LoyaltyTierLogDocument> {
    return this.loyaltyTierLogRepository.create({
      ...this.mapToLog(customer),
      action: LoyaltyTierLogAction.FREE_DELIVERY_REDEEM,
      trigger: LoyaltyTierLogTrigger.ORDER_COMPLETION,
    });
  }

  onCustomerDeletion(
    customer: CustomerDocument,
    previousTierId: Types.ObjectId,
  ): Promise<LoyaltyTierLogDocument> {
    return this.loyaltyTierLogRepository.create({
      ...this.mapToLog(customer, previousTierId),
      action: LoyaltyTierLogAction.UNASSIGN,
      trigger: LoyaltyTierLogTrigger.CUSTOMER_DELETION,
    });
  }

  onTierDeletion(
    customer: CustomerDocument,
    previousTierId: Types.ObjectId,
  ): Promise<LoyaltyTierLogDocument> {
    return this.loyaltyTierLogRepository.create({
      ...this.mapToLog(customer, previousTierId),
      action: LoyaltyTierLogAction.ASSIGN,
      trigger: LoyaltyTierLogTrigger.TIER_DELETION,
    });
  }

  onForceTierRecomputation(
    customer: CustomerDocument,
    previousTierId: Types.ObjectId,
  ): Promise<LoyaltyTierLogDocument> {
    return this.loyaltyTierLogRepository.create({
      ...this.mapToLog(customer, previousTierId),
      action: LoyaltyTierLogAction.RECOMPUTE,
      trigger: LoyaltyTierLogTrigger.RECOMPUTE_TIER_API,
    });
  }

  private mapToLog(
    customer: CustomerDocument,
    previousTierId?: Types.ObjectId,
    currentUser?: CurrentUser,
  ): Pick<
    LoyaltyTierLog,
    | 'triggeredBy'
    | 'customerId'
    | 'companyId'
    | 'currentTierId'
    | 'previousTierId'
  > {
    return {
      triggeredBy: currentUser,
      customerId: customer._id,
      companyId: customer.company,
      currentTierId: customer.loyaltyTier?._id,
      previousTierId: previousTierId,
    };
  }
}
