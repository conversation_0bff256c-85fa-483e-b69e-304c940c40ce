import { Module } from '@nestjs/common';
import { BigCommerceModule } from './big-commerce/bigcommerce.module';
import { AdlerModule } from './channel/adler/adler.module';
import { DeliverectPosModule } from './channel/deliverect-pos/deliverect-pos.module';
import { MicrosModule } from './channel/micros/micros.module';
import { SolutionERPModule } from './channel/solution-erp/solution-erp.module';
import { EnableModule } from './enable/enable.module';
import { IntegrationLogModule } from './integration-log/integration-log.module';
import { DeliverectModule } from './pos/deliverect-channel/deliverect.module';
import { ShortenUrlModule } from './shorten-url/shorten-url.module';
import { OrdableModule } from './webstore/ordable/ordable.module';
import { ShopifyModule } from './webstore/shopify/shopify.module';

@Module({
  imports: [
    EnableModule,
    DeliverectModule,
    DeliverectPosModule,
    AdlerModule,
    OrdableModule,
    IntegrationLogModule,
    SolutionERPModule,
    BigCommerceModule,
    MicrosModule,
    ShortenUrlModule,
    ShopifyModule,
  ],
})
export class IntegrationModule {}
