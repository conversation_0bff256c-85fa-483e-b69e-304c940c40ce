import {
  BranchDocument,
  BranchWithId,
  CompanyDocument,
  CurrentUser,
  OrderDocument,
  OrderPaymentMethod,
  OrderPaymentStatus,
  PaymentMethod,
  PaymentStatusEnum,
} from '@app/shared-stuff';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { PaymentToCreate } from '../../../payment/dto/payment-to-create.dto';
import { PaymentService } from '../../../payment/services/payment/payment.service';
import { OrderPaymentStatusMapping } from '../../constants';

@Injectable()
export class OrderPaymentService {
  constructor(
    @Inject(forwardRef(() => PaymentService))
    private paymentService: PaymentService,
    private eventEmitter: EventEmitter2,
  ) {}

  async createOrderPayment(
    order: OrderDocument,
    branch: BranchWithId,
    company: CompanyDocument,
    callback_url: string,
    delivery_action: string,
    current_user,
  ) {
    const paymentToCreate: PaymentToCreate = {
      amount: order.total_amount,
      company: order.company,
      company_name: company.name,
      customer: order.customer.toHexString(),
      customer_name: order.customer_name,
      customer_phone: order.customer_phone,
      country_code: order.country_code ? order.country_code : ' ',
      language: order.language ? order.language : 'english',
      payment_method: PaymentMethod.CREDIT_CARDS,
      order_code: order.code,
      source: order.source,
      callback_url: callback_url,
      fireTrigger: false,
      branch: branch ? branch._id : '',
      comments: `payment generated for order no. ${order.code}`,
      order: order._id,
      is_test: order.is_test,
      brandId: order.brand ? order.brand._id : undefined,
    } as any;
    const createdPayment = await this.paymentService.create(
      paymentToCreate,
      current_user,
    );
    order.payment_link_id = createdPayment._id;
    order.payment_code = createdPayment.code;
    order.payment_status = OrderPaymentStatus.PENDING;
    order.paymentShortenURL = createdPayment.shortenUrl;

    this.eventEmitter.emit('order.status.updated', order);
    await order.save();
    return createdPayment;
  }

  async findOneByCode(code: string) {
    return await this.paymentService.get_details(code);
  }

  async sendPaymentSmsToCustomer(paymentCode: string) {
    await this.paymentService.resendPaymentLink(paymentCode);
  }

  async assignPaymentToBranch(
    branchId: string,
    paymentCode: string,
    oldBranch: string,
  ) {
    await this.paymentService.assignPaymentToBranch({
      branch: branchId,
      payment: paymentCode,
      old_branch: oldBranch,
    });
  }

  async updateOrderPaymentAmount(paymentCode: string, amount: number) {
    const payment = await this.paymentService.get_details(paymentCode);
    payment.amount = amount;
    await payment.save();
  }

  async removeOrderFromPayment(
    order: OrderDocument,
    paymentMethod: OrderPaymentMethod,
    deletedBy: CurrentUser,
  ) {
    const payment = await this.paymentService.get_details(order.payment_code);
    if (payment.status != PaymentStatusEnum.TRANSACTION_COMPLETED) {
      payment.deletedAt = moment.utc().toDate();
      payment.deletedBy = deletedBy;
      await payment.save();
    }
    order.payment_code = undefined;
    order.payment_link_id = undefined;
    order.payment_method = paymentMethod;

    await order.save();
  }

  async addPaymentToOrder(
    order: OrderDocument,
    branch: BranchDocument,
    paymentCode: string,
  ) {
    const payment = await this.paymentService.get_details(paymentCode);
    if (!payment) {
      return;
    }
    order.payment_code = payment.code;
    order.payment_link_id = new Types.ObjectId(payment._id) as any;
    order.payment_method = OrderPaymentMethod.online;
    order.payment_status = OrderPaymentStatusMapping[payment.status];

    if (branch) {
      payment.branch = new Types.ObjectId(branch._id);
      payment.branchName = branch.name;
    }

    payment.brand = order.brand;
    payment.order = new Types.ObjectId(order._id) as any;
    payment.order_code = order.code;

    await payment.save();
    await order.save();
    await this.eventEmitter.emit('order.status.updated', order);
  }
}
