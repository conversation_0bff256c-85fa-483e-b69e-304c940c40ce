// regarding DOE-345 task to remove thirty days ago documents
const THIRTY_DAYS_AGO = new Date();
THIRTY_DAYS_AGO.setDate(THIRTY_DAYS_AGO.getDate() - 30);

const deleteResult = db.expired_tokens.deleteMany({
  createdAt: { $lt: THIRTY_DAYS_AGO },
});
print(`Deleted ${deleteResult.deletedCount} expired tokens.`);

db.expired_tokens.createIndex(
  { createdAt: 1 },
  { expireAfterSeconds: 2592000 },
);

print("TTL index on 'createdAt' has been created successfully.");

db.expired_tokens.createIndex({ token: 1 });
print("Index on 'token' has been created successfully.");
