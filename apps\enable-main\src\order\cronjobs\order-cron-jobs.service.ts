import {
  CollectionName,
  LogError,
  LoggerService,
  OrderDocument,
  isPrimaryInstance,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { Model } from 'mongoose';
import { ClicksService } from '../../delivery/services/third-parties/clicks/clicks.service';
import { TookanTaskWebhookService } from '../services/order-tookan/tookan-task-webhook-service';

@Injectable()
export class OrderCronJobsService {
  private readonly loggerService = new LoggerService(OrderCronJobsService.name);

  constructor(
    @InjectModel(CollectionName.ORDER) private orderModel: Model<OrderDocument>,
    private clicksService: ClicksService,
    private tookanTaskWebhookService: TookanTaskWebhookService,
  ) {}

  @Cron(CronExpression.EVERY_5_MINUTES)
  @LogError()
  async syncClicksOrders() {
    if (!isPrimaryInstance) {
      return;
    }

    const orders = await this.orderModel.find({
      deliveryParty: 'clicks',
    });

    for (let i = 0; i < orders.length; i++) {
      const currentOrder = orders[i];
      const clicksTask = (await this.clicksService.getClicksDetails(
        currentOrder.integrationInfo?.clicksInfo?.taskIds,
      )) as any[];
      this.loggerService.log(
        `Syncing CLICKS task status for ${currentOrder._id.toHexString()}`,
        { clicksTask, clicksInfo: currentOrder.integrationInfo?.clicksInfo },
      );
      for (let j = 0; j < clicksTask.length; j++) {
        const currentClicksTask = clicksTask[j];
        if (!currentOrder.integrationInfo.clicksInfo?.taskLastStatus) {
          currentOrder.integrationInfo.clicksInfo.taskLastStatus = {};
        }
        if (
          currentOrder.integrationInfo.clicksInfo?.taskLastStatus[
            currentClicksTask['job_id']
          ] != currentClicksTask['job_status']
        ) {
          await this.tookanTaskWebhookService.handleTookanTaskUpdated(
            currentClicksTask,
            true,
          );
          currentOrder.integrationInfo.clicksInfo.taskLastStatus[
            currentClicksTask['job_id']
          ] = currentClicksTask['job_status'];
          await this.orderModel.updateOne(
            { _id: currentOrder._id },
            {
              $set: {
                integrationInfo: {
                  ...currentOrder.integrationInfo,
                  clicksInfo: {
                    taskLastStatus:
                      currentOrder.integrationInfo.clicksInfo.taskLastStatus,
                    taskIds: currentOrder.integrationInfo.clicksInfo.taskIds,
                  },
                },
              },
            },
          );
        }
      }
    }
  }
}
