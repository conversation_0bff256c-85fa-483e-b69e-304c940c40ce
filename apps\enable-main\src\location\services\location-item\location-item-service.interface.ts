import {
  CreateLocationItemDto,
  GetAllLocationItemDto,
  IndexResultDto,
  LocationItem,
  LocationItemDocument,
  LocationItemType,
  UpdateLocationItemDto,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface LocationItemServiceInterface {
  findAll(
    getAllLocationItemDto: GetAllLocationItemDto,
  ): Promise<IndexResultDto<LocationItem>[]>;

  create(
    createLocationItemDto: CreateLocationItemDto,
  ): Promise<LocationItemDocument>;

  update(
    updateLocationItemDto: UpdateLocationItemDto,
  ): Promise<LocationItemDocument>;

  findOne(id: Types.ObjectId): Promise<LocationItemDocument>;

  delete(id: Types.ObjectId): Promise<void>;

  findOneByNameAndType(
    name: string,
    type: LocationItemType,
  ): Promise<LocationItemDocument>;
}

export const LocationItemServiceInterface = Symbol(
  'LocationItemServiceInterface',
);
