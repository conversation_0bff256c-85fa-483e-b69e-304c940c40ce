import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class CyberSourcePaymentToProcess {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  access_key: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  amount: number;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  currency: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  locale: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  profile_id: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  reference_number: number;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  signed_date_time: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  signed_field_names: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  unsigned_field_names: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  transaction_type: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  transaction_uuid: string;
}
