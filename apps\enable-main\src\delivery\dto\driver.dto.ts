import {
  <PERSON>Index,
  ImageToCreate,
  ImageToUpdate,
  IsPassword,
  ObjectIdTransform,
} from '@app/shared-stuff';
import { ApiProperty } from '@nestjs/swagger';
import { Allow, IsNotEmpty, IsOptional } from 'class-validator';
import { Types } from 'mongoose';

export class DriverToCreate {
  @ApiProperty({
    required: true,
    type: String,
  })
  first_name: string;

  @ApiProperty({
    required: false,
    type: String,
  })
  last_name: string;

  @ApiProperty({
    required: false,
    type: String,
  })
  username: string;

  @ApiProperty({
    required: false,
    type: String,
  })
  phone: string;

  @ApiProperty({
    required: false,
    type: String,
  })
  email: string;

  @ApiProperty({
    required: false,
    type: String,
    enum: ['Car', 'Motor Cycle', 'Bicycle', 'Scooter', 'Foot', 'Truck'],
  })
  transport_type_name: string;

  @ApiProperty({
    required: false,
    type: String,
  })
  license: string;

  @ApiProperty({
    required: false,
    type: String,
  })
  color: string;

  @ApiProperty({
    required: false,
    type: String,
  })
  team_id: string;

  @ApiProperty({
    required: false,
    type: String,
  })
  @IsNotEmpty()
  @IsPassword()
  password: string;

  @ApiProperty({
    required: false,
    type: Number,
    default: 1,
  })
  fleet_type: number;

  @ApiProperty({
    type: [String],
  })
  @ObjectIdTransform({ optional: true, coerceArray: true })
  branches: Types.ObjectId[];

  @ApiProperty({
    type: String,
    required: true,
  })
  company: string;

  @ApiProperty({
    type: ImageToCreate,
    required: true,
  })
  image_to_create: ImageToCreate;

  tookan_driver_id: string;
}

export class DriverToIndex extends DataIndex {
  @ApiProperty({
    required: false,
    type: String,
    enum: [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
  })
  month: string;

  @ApiProperty({
    required: false,
    type: String,
    enum: ['phone', 'name', 'role', 'all'],
  })
  search_type: string;

  @ApiProperty({
    required: false,
    type: String,
    enum: ['name_a_z', 'date_created'],
  })
  sort_type: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  branch: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  company: string;
}

export class DriverToUpdate {
  @ApiProperty({
    required: true,
    type: String,
  })
  @ObjectIdTransform()
  @Allow()
  _id: Types.ObjectId;

  @ApiProperty({
    required: true,
    type: String,
  })
  @Allow()
  first_name: string;

  @ApiProperty({
    required: false,
    type: String,
  })
  @Allow()
  last_name: string;

  @ApiProperty({
    required: false,
    type: String,
  })
  @Allow()
  username: string;

  @ApiProperty({
    required: false,
    type: String,
  })
  @Allow()
  phone: string;

  @ApiProperty({
    required: false,
    type: String,
  })
  @Allow()
  email: string;

  @ApiProperty({
    required: false,
    type: String,
    enum: ['Car', 'Motor Cycle', 'Bicycle', 'Scooter', 'Foot', 'Truck'],
  })
  @Allow()
  transport_type_name: string;

  @ApiProperty({
    required: false,
    type: String,
  })
  @Allow()
  license: string;

  @ApiProperty({
    required: false,
    type: String,
  })
  @Allow()
  color: string;

  @ApiProperty({
    required: false,
    type: String,
  })
  @Allow()
  team_id: string;

  @ApiProperty({
    required: true,
    type: String,
  })
  @IsOptional()
  @IsPassword()
  @Allow()
  password?: string;

  @ApiProperty({
    required: false,
    type: Number,
    default: 1,
  })
  @Allow()
  fleet_type: number;

  @ApiProperty({
    type: ImageToUpdate,
    required: true,
  })
  @Allow()
  image_to_update: ImageToUpdate;

  @ApiProperty({
    type: [String],
  })
  @ObjectIdTransform({ optional: true, coerceArray: true })
  @Allow()
  branches: Types.ObjectId[];

  @Allow()
  tookan_driver_id: string;
}

export class DriverBranchToAssign {
  @ApiProperty({
    required: true,
    type: String,
  })
  driver_id: string;

  @ApiProperty({
    required: true,
    type: String,
  })
  branch_id: string;
}

export class DriverBranchToRemove {
  @ApiProperty({
    required: true,
    type: String,
  })
  driver_id: string;

  @ApiProperty({
    required: true,
    type: String,
  })
  branch_id: string;
}

export class DriverExcelToImport {
  @ApiProperty({
    required: false,
    type: String,
  })
  company: string;

  @ApiProperty({
    required: true,
    type: String,
  })
  file_path: string;
}

export class DriverUsernameToGet {
  @ApiProperty({
    type: String,
    required: true,
  })
  first_name: string;
  @ApiProperty({
    type: String,
    required: true,
  })
  last_name: string;
  @ApiProperty({
    type: String,
    required: false,
  })
  company: string;
}
