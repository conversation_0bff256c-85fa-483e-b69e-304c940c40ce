import { RegisteredPass } from '@app/shared-stuff';
import { IntersectionType, PartialType, PickType } from '@nestjs/swagger';
import { GeneratePassDto } from './generate-pass.dto';

export class RegisterEnablePassDto extends IntersectionType(
  PickType(GeneratePassDto, ['customerId', 'brandId']),
  PartialType(
    PickType(RegisteredPass, [
      'deviceLibraryIdentifier',
      'passTypeIdentifier',
      'serialNumber',
    ]),
  ),
) {}
