import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { PaymentLogAction } from '../enums/payment-log-action.enum';

export type PaymentLogDocument = PaymentLog & Document;

@Schema({ timestamps: true })
export class PaymentLog {
  @Prop({
    type: {},
    required: false,
  })
  sentObject: Record<string, any>;

  @Prop({
    type: {},
    required: false,
  })
  receivedObject: Record<string, any>;

  @Prop({
    type: String,
    enum: PaymentLogAction,
    required: false,
  })
  logAction: PaymentLogAction;

  @Prop({
    type: String,
    required: false,
  })
  paymentCode: string;

  @Prop({
    type: Types.ObjectId,
    required: true,
  })
  paymentId: Types.ObjectId;
}

export const PaymentLogSchema = SchemaFactory.createForClass(PaymentLog);
