import { ObjectIdTransform, SavedLocationToCreate } from '@app/shared-stuff';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { Types } from 'mongoose';

export class UpdateIntegrationOrderDto {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  orderId: string;

  @ApiProperty({
    type: () => SavedLocationToCreate,
    required: false,
  })
  @IsOptional()
  @Type(() => SavedLocationToCreate)
  deliveryLocation?: SavedLocationToCreate;

  @ApiProperty({
    type: String,
    required: false,
  })
  @ObjectIdTransform({ optional: true })
  deliveryLocationId?: Types.ObjectId;

  @ApiProperty({
    required: false,
    type: String,
    description: "Format 'YYYY-MM-DD' in UTC timezone.",
  })
  @IsOptional()
  @IsString()
  deliveryDate?: string;

  @ApiProperty({
    required: false,
    type: String,
    description: "Format 'HH:mm' in UTC timezone.",
  })
  @IsOptional()
  @IsString()
  deliveryTime?: string;

  @ApiProperty({
    required: false,
    type: String,
    description: "Format 'YYYY-MM-DD' in UTC timezone.",
  })
  @IsOptional()
  @IsString()
  pickupDate?: string;

  @ApiProperty({
    required: false,
    type: String,
    description: "Format 'HH:mm' in UTC timezone.",
  })
  @IsOptional()
  @IsString()
  pickupTime?: string;
}

export type UpdateOrderDispatchDetailsType = Pick<
  UpdateIntegrationOrderDto,
  'orderId' | 'deliveryDate' | 'deliveryTime' | 'pickupTime' | 'pickupDate'
>;
