import { CustomerDocument, LoyaltyTierDocument } from '@app/shared-stuff';
import { StoreDocument } from '../../../../../store/models/store.model';

export interface OrdableFreeDeliveryServiceInterface {
  addFreeDelivery(customer: CustomerDocument): Promise<void>;
  removeFreeDelivery(customer: CustomerDocument): Promise<void>;
  handleFreeDeliveryRemoved(loyaltyTier: LoyaltyTierDocument): Promise<void>;
  handleFreeDeliveryAdded(loyaltyTier: LoyaltyTierDocument): Promise<void>;
  initFreeDelivery(
    store: StoreDocument,
    customers: CustomerDocument[],
  ): Promise<void>;
}

export const OrdableFreeDeliveryServiceInterface = Symbol(
  'OrdableFreeDeliveryServiceInterface',
);
