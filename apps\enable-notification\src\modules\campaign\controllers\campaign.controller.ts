import {
  CampaignDocument,
  CreateCampaignDtoWithAuth,
  IndexCampaignDto,
  LaunchCampaignDto,
} from '@app/shared-stuff';
import { Controller, Inject, SetMetadata } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ApiTags } from '@nestjs/swagger';
import { CampaignServiceInterface } from '../services/campaign/campaign.service.interface';

@ApiTags('Campaign')
@SetMetadata('module', 'campaign')
@Controller('campaign')
export class CampaignController {
  constructor(
    @Inject(CampaignServiceInterface)
    private readonly campaignService: CampaignServiceInterface,
  ) {}

  @MessagePattern('index.campaign.request')
  async index(
    @Payload() indexCampaignDto: IndexCampaignDto,
  ): Promise<CampaignDocument[]> {
    return await this.campaignService.index(indexCampaignDto);
  }

  @MessagePattern('create.campaign.request')
  async create(
    @Payload() createCampaignDto: CreateCampaignDtoWithAuth,
  ): Promise<CampaignDocument> {
    return await this.campaignService.create(createCampaignDto);
  }

  @MessagePattern('launch.campaign.request')
  async launch(@Payload() launchCampaignDto: LaunchCampaignDto): Promise<void> {
    return await this.campaignService.launch(launchCampaignDto);
  }
}
