import { responseCode } from '@app/shared-stuff';
import { Body, Controller, Post, Res, SetMetadata } from '@nestjs/common';
import { ApiB<PERSON>cAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import {
  CallToNotify,
  TokenToGenerate,
} from '../../../call-center/dto/call-center.dto';
import { HelperService } from '../../../shared/services/helper/helper.service';
import { CallCenterService } from './../../services/call-center/call-center.service';

@Controller('call-center')
@ApiTags('Call Center')
@SetMetadata('module', 'call-center')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class CallCenterController {
  constructor(
    private helperService: HelperService,
    private callCenterService: CallCenterService,
  ) {}

  @Post('token')
  @SetMetadata('action', 'maqsam-generate-token')
  async generateToken(
    @Body() tokenToGenerate: TokenToGenerate,
    @Res() res: Response,
  ) {
    try {
      const token = await this.callCenterService.generateMaqsamAuthToken(
        tokenToGenerate.userEmail,
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to get generate token',
        token,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('maqsam/webhook')
  @SetMetadata('public', 'true')
  async onMaqsamCallCame(
    @Body() maqsamCallToNotify: CallToNotify,
    @Res() res: Response,
  ) {
    try {
      const response =
        await this.callCenterService.onCallCame(maqsamCallToNotify);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to get generate token',
        response,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('ziwo/webhook')
  @SetMetadata('public', 'true')
  async onZiwoCallCame(
    @Body() ziwoCallToNotify: CallToNotify,
    @Res() res: Response,
  ) {
    try {
      const response =
        await this.callCenterService.onCallCame(ziwoCallToNotify);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to get generate token',
        response,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
}
