// EBL-5020 - Webhook Engine

// Adding New Replacement for ORDER Triggers
db.getCollection('triggers').updateMany(
  { module: 'ORDER' },
  { $addToSet: { replacement: 'orderStatus' } },
);
db.getCollection('templates').updateMany(
  { 'trigger.module': 'ORDER' },
  { $addToSet: { 'trigger.replacement': 'orderStatus' } },
);

// Adding new Trigger ON_ORDER_STATUS_UPDATED
db.getCollection('triggers').insertOne({
  name: '[ORDER] ON_ORDER_STATUS_UPDATED',
  client: 'ENABLE_MAIN',
  action: 'ON_ORDER_STATUS_UPDATED',
  module: 'ORDER',
  replacement: [
    'totalAmount',
    'orderCode',
    'orderSource',
    'paymentLinkId',
    'paymentLink',
    'brandName',
    'customerName',
    'phoneNumber',
    'email',
    'companyName',
    'userName',
    'amountWithoutDelivery',
    'paymentMethod',
    'recipientName',
    'deliveryAction',
    'pickupDate',
    'deliveryDate',
    'locationShortenURL',
    'locationLink',
    'orderTrackingLink',
    'paymentShortenURL',
    'orderTrackingShortenURL',
    'walletPassLink',
    'firstName',
    'fullName',
    'loyaltyPointBalance',
    'loyaltyTier',
    'remainingOrdersCurrentTier',
    'remainingOrdersUpperTier',
    'upperTierDiscountValue',
    'tierDiscountValue',
    'tierDiscountOrderValueThreshold',
    'loyaltyRegistrationPageLink',
    'walletPassAccessPageLink',
    'ordableLink',
    'upperLoyaltyTier',
    'validTill',
    'nextPunchCardAchievementBenefits',
    'nextPunchCardAchievementRequirements',
    'remainingRequirementsUpperTier',
    'remainingRequirementsCurrentTier',
    'remainingAmountSpentUpperTier',
    'remainingAmountSpentCurrentTier',
    'remainingPointsNextCoupon',
    'nextCouponBenefit',
    'orderStatus',
  ],
  createdAt: new Date(),
  updatedAt: new Date(),
});
