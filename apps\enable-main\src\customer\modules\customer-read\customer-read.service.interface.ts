import {
  CountryDialCode,
  CouponDocument,
  CustomerDocument,
  CustomerProjection,
  LoyaltyTierDocument,
  RegisteredPass,
} from '@app/shared-stuff';
import { Types } from 'mongoose';
import { StoreDocument } from '../../../store/models/store.model';

export interface CustomerReadServiceInterface {
  findById(customerId: Types.ObjectId): Promise<CustomerDocument>;

  findOne(
    id: string,
    companyId?: Types.ObjectId,
    countryCode?: CountryDialCode,
  ): Promise<CustomerDocument>;

  getCustomer(
    uniqueIdentifier: string,
    companyId: Types.ObjectId,
  ): Promise<CustomerDocument | undefined>;

  getCompanyIdForCustomerId(
    customerId: Types.ObjectId,
  ): Promise<Types.ObjectId>;

  findByRegisteredPasses(
    registeredPasses: Partial<RegisteredPass>,
    updatedSince?: Date,
  ): Promise<CustomerDocument[]>;

  findEligibleForTierDiscount(
    loyaltyTier: LoyaltyTierDocument,
  ): Promise<CustomerDocument[]>;

  findEligibleForCoupon(coupon: CouponDocument): Promise<CustomerDocument[]>;

  findByPhoneAndCompanyId(
    phone: string,
    companyId: Types.ObjectId,
    countryCode?: string,
    projection?: CustomerProjection,
  ): Promise<CustomerDocument>;

  findByLoyaltyTier(
    loyaltyTierIds: Types.ObjectId[],
    companyId: Types.ObjectId,
    hasPasses?: boolean,
  ): Promise<CustomerDocument[]>;

  findByBrandId(
    brandId: Types.ObjectId,
    hasPasses?: boolean,
  ): Promise<CustomerDocument[]>;

  findSyncableForOrdableStore(
    store: StoreDocument,
  ): Promise<CustomerDocument[]>;

  findByPunchCard(punchCardId: Types.ObjectId): Promise<CustomerDocument[]>;

  findByCompanyId(
    companyId: Types.ObjectId | Types.ObjectId[],
    hasPasses?: boolean,
  ): Promise<CustomerDocument[]>;

  findByEmail(
    companyId: Types.ObjectId,
    email: string,
  ): Promise<CustomerDocument>;
}

export const CustomerReadServiceInterface = Symbol(
  'CustomerReadServiceInterface',
);
