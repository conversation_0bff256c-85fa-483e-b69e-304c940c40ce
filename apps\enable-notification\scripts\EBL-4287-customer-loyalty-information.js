db.templates.insertOne({
  name: 'Global Promote Loyalty Card Android',
  content: {
    enContent: `Your Loyalty Card is Ready! 🔥

We wanted to remind you that you can enhance your loyalty experience by adding your Digital Loyalty Card to your Android WalletPasses | Passbook application. With just a few taps, you'll have quick access to your rewards and exclusive benefits.

Download the digital wallet application {{walletPasses}} then click on your Loyalty Card Link $walletPassLink.
Don't miss out on this seamless experience! Happy savings! 🎁`,
    arContent: `Your Loyalty Card is Ready! 🔥

We wanted to remind you that you can enhance your loyalty experience by adding your Digital Loyalty Card to your Android WalletPasses | Passbook application. With just a few taps, you'll have quick access to your rewards and exclusive benefits.

Download the digital wallet application {{walletPasses}} then click on your Loyalty Card Link $walletPassLink.
Don't miss out on this seamless experience! Happy savings! 🎁`,
  },
  to: 'CUSTOMER',
  from: 'COMPANY_SENDER',
  type: 'SMS',

  trigger: db.triggers.findOne({
    name: '[CUSTOMER] ON_SEND_LOYALTY_CARD',
  }),
  condition: {
    groupsOperator: 'and',
    groups: [
      {
        filters: [
          {
            entity: 'customer',
            variable: 'device os',
            operator: '==',
            reference: 'Android',
          },
        ],
        filtersOperator: 'and',
      },
    ],
  },
  createdAt: new Date(),
  updatedAt: new Date(),
});

db.templates.insertOne({
  name: 'Global Promote Loyalty Card IOS',
  content: {
    enContent: `Your Loyalty Card is Ready! 🔥

Did you know you can easily access your loyalty benefits and rewards by adding our wallet pass to your iOS Apple Wallet? It's a convenient way to keep track of your progress and redeem your rewards.

Here's your Loyalty Card Link $walletPassLink, simply click the Link and it will be automatically added to your Apple Wallet. Don't miss out on this seamless experience! Happy savings! 🎁`,
    arContent: `Your Loyalty Card is Ready! 🔥

Did you know you can easily access your loyalty benefits and rewards by adding our wallet pass to your iOS Apple Wallet? It's a convenient way to keep track of your progress and redeem your rewards.

Here's your Loyalty Card Link $walletPassLink, simply click the Link and it will be automatically added to your Apple Wallet. Don't miss out on this seamless experience! Happy savings! 🎁`,
  },
  to: 'CUSTOMER',
  from: 'COMPANY_SENDER',
  type: 'SMS',

  trigger: db.triggers.findOne({
    name: '[CUSTOMER] ON_SEND_LOYALTY_CARD',
  }),
  condition: {
    groupsOperator: 'and',
    groups: [
      {
        filters: [
          {
            entity: 'customer',
            variable: 'device os',
            operator: '==',
            reference: 'iOS',
          },
        ],
        filtersOperator: 'and',
      },
    ],
  },
  createdAt: new Date(),
  updatedAt: new Date(),
});
