import {
  LoyaltyPointLog,
  LoyaltyPointLogAction,
  LoyaltyPointLogDocument,
  PopulatedLoyaltyPointLog,
  TransformedLoyaltyPointLog,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { Types } from 'mongoose';
import { LoyaltyPointLogRepositoryInterface } from '../repositories/loyalty-point-log.repository.interface';
import { LoyaltyPointLogServiceInterface } from './loyalty-point-log.service.interface';

@Injectable()
export class LoyaltyPointLogService implements LoyaltyPointLogServiceInterface {
  constructor(
    @Inject(LoyaltyPointLogRepositoryInterface)
    private readonly loyaltyPointLogRepository: LoyaltyPointLogRepositoryInterface,
  ) {}

  async create(
    log: Omit<LoyaltyPointLog, 'createdAt' | 'updatedAt'>,
  ): Promise<LoyaltyPointLogDocument> {
    return await this.loyaltyPointLogRepository.create(log);
  }

  async getLogsForCustomer(
    customerId: Types.ObjectId,
  ): Promise<TransformedLoyaltyPointLog[]> {
    const logs =
      await this.loyaltyPointLogRepository.findByCustomerId(customerId);

    if (!logs) return [];

    return logs.map(this.depopulateLog);
  }

  async getWasOrderCompleted(orderId: Types.ObjectId): Promise<boolean> {
    return this.loyaltyPointLogRepository.exists({
      orderId,
      action: LoyaltyPointLogAction.ON_ORDER_COMPLETED,
    });
  }

  private depopulateLog(
    log: PopulatedLoyaltyPointLog,
  ): TransformedLoyaltyPointLog {
    if (log.populated('brandId')) {
      log.set('brandName', log.brandId?.name, { strict: false });
      log.depopulate('brandId');
    }

    if (log.populated('branchId')) {
      log.set('branchName', log.branchId?.name, { strict: false });
      log.depopulate('branchId');
    }

    if (log.populated('orderId')) {
      log.set('orderCode', log.orderId?.code, { strict: false });
      log.set('orderSource', log.orderId?.source, { strict: false });
    }

    log.set('orderId', undefined);

    return log as any;
  }
}
