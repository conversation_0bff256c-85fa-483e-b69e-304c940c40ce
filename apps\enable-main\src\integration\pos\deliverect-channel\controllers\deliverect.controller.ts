import {
  GenericExceptionFilter,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Inject,
  Param,
  Post,
  Req,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ApiBearerAuth,
  ApiExcludeEndpoint,
  ApiTags,
} from '@nestjs/swagger';

import { Request } from 'express';
import { CompanyService } from '../../../../company/services/company/company.service';
import { CustomerReadServiceInterface } from '../../../../customer/modules/customer-read/customer-read.service.interface';
import { SavedLocationService } from '../../../../location/services/saved-location/saved-location.service';
import { OrderService } from '../../../../order/services/order/order.service';
import { IntegrationLogInterceptor } from '../../../integration-log/interceptors/integration-log.interceptor';
import { DeliverectOrderDeliveryUpdateDto } from '../dto/deliverect-order-delivery-update.dto';
import { DeliverectOrderStatusUpdateDto } from '../dto/deliverect-order-status-update.dto';
import { DeliverectChannelService } from '../services/deliverect-channel.service';

@Controller('deliverect')
@UseInterceptors(IntegrationLogInterceptor)
@UseFilters(GenericExceptionFilter)
@ApiTags('Deliverect Integration')
@SetMetadata('module', 'deliverect')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class DeliverectController {
  constructor(
    private deliverectChannelService: DeliverectChannelService,
    @Inject(CustomerReadServiceInterface)
    private customerReadService: CustomerReadServiceInterface,
    private orderService: OrderService,
    private companyService: CompanyService,
    private savedLocationService: SavedLocationService,
  ) {}

  @Post(':branchId/menuPush')
  @SetMetadata('public', 'true')
  @UseInterceptors(TransformInterceptor)
  @HttpCode(HttpStatus.OK)
  async onMenuPush(@Param('branchId') branchId: string, @Body() data: any) {
    await this.deliverectChannelService.onMenuPush(data, branchId);
    return 'test';
  }

  @Post(':branchId/SnoozeOrUnSnooze')
  @SetMetadata('public', 'true')
  @UseInterceptors(TransformInterceptor)
  @HttpCode(HttpStatus.OK)
  async snoozeOrUnSnoozeProducts(@Body() data: any) {
    return await this.deliverectChannelService.snoozeOrUnSnooze(data);
  }

  @Post('ChannelStatus')
  @SetMetadata('public', 'true')
  @HttpCode(HttpStatus.OK)
  async onChannelStatusChanged(@Body() data: any) {
    return await this.deliverectChannelService.onChannelStatusUpdate(data);
  }

  @Post(':branchId/restaurantBusy')
  @SetMetadata('public', 'true')
  @UseInterceptors(TransformInterceptor)
  @HttpCode(HttpStatus.OK)
  async onRestaurantBusy(@Body() data: any) {
    return await this.deliverectChannelService.onRestaurantBusy(data);
  }

  @Post('dispatchOrder/:OrderCode')
  @SetMetadata('action', 'dispatchDeliverectOrder')
  @UseInterceptors(TransformInterceptor)
  @HttpCode(HttpStatus.OK)
  async dispatchOrder(
    @Param('OrderCode') orderCode: string,
    @Req() req: Request,
  ) {
    const order = await this.orderService.get_details(orderCode);
    let deliveryAddress;
    if (order['deliveryLocationId']) {
      deliveryAddress = await this.savedLocationService.getDetails(
        order.deliveryLocationId ? order.deliveryLocationId.toHexString() : '',
      );
    }
    const customer = await this.customerReadService.findOne(
      order['customer']['_id']
        ? order['customer']['_id'].toHexString()
        : order['customer'].toHexString(),
      order.company['_id'],
    );
    const company = await this.companyService.get_details(
      order['company']['_id'],
    );

    return await this.deliverectChannelService.dispatchOrderToDeliverect(
      order,
      company,
      typeof order.branch !== 'string' ? order.branch : undefined,
      customer,
      deliveryAddress,
      req['current'],
    );
  }

  @Post('orderStatusUpdate')
  @SetMetadata('public', 'true')
  @UseInterceptors(TransformInterceptor)
  @HttpCode(HttpStatus.OK)
  async orderStatusUpdate(@Body() data: DeliverectOrderStatusUpdateDto) {
    return await this.deliverectChannelService.onOrderStatusUpdate(data);
  }

  @Post('orderDeliveryUpdate')
  @SetMetadata('public', 'true')
  @UseInterceptors(TransformInterceptor)
  @HttpCode(HttpStatus.OK)
  async orderDeliveryUpdate(@Body() data: DeliverectOrderDeliveryUpdateDto) {
    return this.deliverectChannelService.onOrderStatusUpdate(data);
  }
}
