import {
  MenuItemAvailabilityDto,
  IndexMenuItemAvailabilityDto,
  CreateOrUpdateMenuItemAvailabilityDto,
  MenuItemAvailabilityDocument,
  UpdateMenuItemAvailabilityDto,
  CreateMenuItemAvailabilityDto,
  MenuItemAvailability,
  WeekDays,
  MenuItemAvailabilityType,
} from '@app/shared-stuff';
import { Injectable, Inject } from '@nestjs/common';
import { plainToClass } from 'class-transformer';
import { Types } from 'mongoose';
import { MenuItemAvailabilityRepositoryInterface } from '../../repositories/menu-item-availability.repository.interface';
import { MenuItemAvailabilityServiceInterface } from './menu-item-availability.service.interface';

@Injectable()
export class MenuItemAvailabilityService
  implements MenuItemAvailabilityServiceInterface
{
  constructor(
    @Inject('MenuItemAvailabilityRepositoryInterface')
    private readonly menuItemAvailabilityRepository: MenuItemAvailabilityRepositoryInterface,
  ) {}

  async handleDeletedMenuItems(
    availability: MenuItemAvailabilityDto,
    menuItemId: Types.ObjectId,
  ) {
    const menuItemAvailability = await this.findOne(
      availability.dayOfWeek,
      availability.startTime,
      availability.endTime,
      availability.isWeekly,
      availability.type,
    );
    if (menuItemAvailability) {
      for (let i = 0; i < menuItemAvailability.menuItemIds.length; i++) {
        if (
          menuItemAvailability.menuItemIds[i].toString() ==
          menuItemId.toString()
        )
          menuItemAvailability.menuItemIds.splice(i, 1);
      }
      await menuItemAvailability.save();
    }
    // TODO : momken teRemove using  $Pull
  }

  async index(
    indexMenuItemAvailability: IndexMenuItemAvailabilityDto,
  ): Promise<any> {
    const pipeline = [];
    const match = {};
    if (indexMenuItemAvailability.startTime) {
      match['startTime'] = indexMenuItemAvailability.startTime;
    }
    if (indexMenuItemAvailability.endTime) {
      match['endTime'] = indexMenuItemAvailability.endTime;
    }
    if (indexMenuItemAvailability.type) {
      match['type'] = indexMenuItemAvailability.type;
    }
    if (indexMenuItemAvailability.menuItemId) {
      match['menuItemIds'] = {
        $elemMatch: {
          $eq: new Types.ObjectId(indexMenuItemAvailability.menuItemId),
        },
      };
    }
    pipeline.push({
      $match: match,
    });
    return await this.menuItemAvailabilityRepository.aggregate(pipeline);
  }

  async createOrUpdate(
    createOrUpdateMenuItemAvailabilityDto: CreateOrUpdateMenuItemAvailabilityDto,
  ) {
    const existedMenuItemAvailability = await this.findOne(
      createOrUpdateMenuItemAvailabilityDto.availability.dayOfWeek,
      createOrUpdateMenuItemAvailabilityDto.availability.startTime,
      createOrUpdateMenuItemAvailabilityDto.availability.endTime,
      createOrUpdateMenuItemAvailabilityDto.availability.isWeekly,
      createOrUpdateMenuItemAvailabilityDto.availability.type,
    );
    if (!existedMenuItemAvailability) {
      const createMenuItemAvailabilityDto =
        this.constructCreateMenuItemAvailabilityDto(
          createOrUpdateMenuItemAvailabilityDto.availability,
          createOrUpdateMenuItemAvailabilityDto?.menuItemId,
        );
      await this.create(createMenuItemAvailabilityDto);
    } else {
      const updateMenuItemAvailabilityDto =
        this.constructUpdateMenuItemAvailabilityDto(
          createOrUpdateMenuItemAvailabilityDto.availability,
          createOrUpdateMenuItemAvailabilityDto?.menuItemId,
          existedMenuItemAvailability,
        );
      await this.update(updateMenuItemAvailabilityDto);
    }
  }

  async create(
    createMenuItemAvailabilityDto: CreateMenuItemAvailabilityDto,
  ): Promise<MenuItemAvailabilityDocument> {
    const menuItemAvailability = plainToClass(
      MenuItemAvailability,
      createMenuItemAvailabilityDto,
    );
    menuItemAvailability.menuItemIds =
      createMenuItemAvailabilityDto.menuItemIds;
    const created =
      await this.menuItemAvailabilityRepository.create(menuItemAvailability);
    return created;
  }

  async update(
    updateMenuItemAvailabilityDto: UpdateMenuItemAvailabilityDto,
  ): Promise<MenuItemAvailabilityDocument> {
    const menuItemAvailability = plainToClass(
      MenuItemAvailability,
      updateMenuItemAvailabilityDto,
    );
    menuItemAvailability.menuItemIds =
      updateMenuItemAvailabilityDto.menuItemIds;
    return await this.menuItemAvailabilityRepository.findOneAndUpdate(
      { _id: updateMenuItemAvailabilityDto.id },
      menuItemAvailability,
    );
  }

  async remove(id: Types.ObjectId) {
    await this.menuItemAvailabilityRepository.remove(id);
  }

  async findOne(
    dayOfWeek: WeekDays,
    startTime: string,
    endTime: string,
    isWeekly: boolean,
    type: MenuItemAvailabilityType,
  ): Promise<MenuItemAvailabilityDocument> {
    return await this.menuItemAvailabilityRepository.findOneWithTiming(
      dayOfWeek,
      startTime,
      endTime,
      isWeekly,
      type,
    );
  }

  private constructUpdateMenuItemAvailabilityDto(
    availability: MenuItemAvailabilityDto,
    menuItemId: Types.ObjectId,
    existedMenuItemAvailability: MenuItemAvailabilityDocument,
  ): UpdateMenuItemAvailabilityDto {
    if (menuItemId) existedMenuItemAvailability.menuItemIds.push(menuItemId);
    return {
      dayOfWeek: availability.dayOfWeek,
      startTime: availability.startTime,
      endTime: availability.endTime,
      isWeekly: availability.isWeekly,
      menuItemIds: existedMenuItemAvailability.menuItemIds,
      id: existedMenuItemAvailability._id,
      type: availability.type,
    };
  }

  private constructCreateMenuItemAvailabilityDto(
    availability: MenuItemAvailabilityDto,
    menuItemId: Types.ObjectId,
  ): CreateMenuItemAvailabilityDto {
    const menuItemIds = [];
    if (menuItemId) menuItemIds.push(menuItemId);
    return {
      dayOfWeek: availability.dayOfWeek,
      startTime: availability.startTime,
      endTime: availability.endTime,
      isWeekly: availability.isWeekly,
      menuItemIds: menuItemIds,
      type: availability.type,
    };
  }
}
