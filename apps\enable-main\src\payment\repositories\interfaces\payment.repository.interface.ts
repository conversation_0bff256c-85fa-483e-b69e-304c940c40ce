import {
  IGenericRepository,
  Payment,
  PaymentDocument,
} from '@app/shared-stuff';
import { Brand } from '@app/shared-stuff/models/brand.model';
import { Moment } from 'moment-timezone';
import { FilterQuery, Types, UpdateWriteOpResult } from 'mongoose';
import { PaymentReceiptDocument } from '../../types/payment-receipt-document.type';

export interface PaymentRepositoryInterface
  extends IGenericRepository<PaymentDocument, Payment> {
  findOne(
    entityFilterQuery: FilterQuery<Payment>,
  ): Promise<PaymentDocument | null>;

  count(entityFilterQuery?: FilterQuery<Payment>): Promise<number>;

  findByCode(code: string): Promise<PaymentDocument | null>;

  findByIdWithCustomer(id: Types.ObjectId): Promise<PaymentDocument>;

  findByCodeWithPaymentLinkDetails(
    code: string,
  ): Promise<PaymentDocument | null>;

  findByCodeOrIdWithDetails(
    codeOrId: string | Types.ObjectId,
  ): Promise<PaymentDocument | null>;

  findByCodeOrIdWithFullDetails(
    codeOrId: string,
  ): Promise<PaymentDocument | null>;

  findByCodeWithReceipt(code: string): Promise<PaymentReceiptDocument | null>;

  findByCBPayCode(transaction_code: any): Promise<PaymentDocument | null>;

  findByExternalId(externalId: any): Promise<PaymentDocument | null>;

  findByIdWithBranch(id: Types.ObjectId): Promise<PaymentDocument | null>;

  findByCodeWithBranch(code: string): Promise<PaymentDocument | null>;

  updateManyByBrand(
    startDate: Moment,
    endDate: Moment,
    brandId: Types.ObjectId,
    brand: Partial<Brand>,
  ): Promise<UpdateWriteOpResult>;
}
