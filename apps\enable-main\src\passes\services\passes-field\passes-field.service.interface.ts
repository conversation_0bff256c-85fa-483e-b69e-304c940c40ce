import {
  CompanyDocument,
  CouponDocument,
  CustomerDocument,
  LabelledField,
  LinksModuleData,
  LoyaltyTierDocument,
  LoyaltyTierProgramProgress,
  PassField,
  PassFieldContext,
  PassFieldName,
} from '@app/shared-stuff';
import { FieldConfig } from '@app/shared-stuff/dtos/passes/field-config.dto';
import { BrandDocument } from '@app/shared-stuff/models/brand.model';

export interface PassesFieldServiceInterface {
  getFields(
    fields: LabelledField[],
    fieldConfig: FieldConfig,
    passFieldContext: PassFieldContext,
  ): Promise<PassField[]>;
  getCustomerNameField(customer: CustomerDocument): PassField;
  getPointsField(
    customer: CustomerDocument,
    company: CompanyDocument,
  ): PassField;
  getTierField(customer: CustomerDocument, company: CompanyDocument): PassField;
  getNextTierField(
    company: CompanyDocument,
    nextTier: LoyaltyTierDocument,
    loyaltyTierProgramProgress: LoyaltyTierProgramProgress,
  ): Promise<PassField>;
  getValidTillField(
    customer: CustomerDocument,
    company: CompanyDocument,
  ): Promise<PassField | null>;
  getNextCouponField(
    customer: CustomerDocument,
    company: CompanyDocument,
  ): Promise<PassField>;
  getCouponsField(availableCoupons: CouponDocument[]): PassField;
  getPointsRateField(
    fieldConfig: FieldConfig,
    company: CompanyDocument,
    loyaltyTierProgramProgress: LoyaltyTierProgramProgress,
  ): PassField;
  getPassDescriptionField(
    fieldConfig: FieldConfig,
    brand: BrandDocument,
  ): PassField;
  getTierDescriptionField(
    customer: CustomerDocument,
    brand: BrandDocument,
    nextTier: LoyaltyTierDocument,
    loyaltyTierProgramProgress: LoyaltyTierProgramProgress,
  ): Promise<PassField>;
  getPointsDescriptionField(
    customer: CustomerDocument,
    brand: BrandDocument,
    company: CompanyDocument,
    availableCoupons: CouponDocument[],
  ): PassField;
  getSocialLinksField(
    brand: BrandDocument,
    company: CompanyDocument,
  ): PassField;
  getPoweredByField(): PassField;
  getYourRewardsField(customer: CustomerDocument): PassField;
  getNextRewardFields(customer: CustomerDocument): Promise<PassField[]>;
  getRewardsDescriptionField(
    customer: CustomerDocument,
    brand: BrandDocument,
  ): Promise<PassField>;
  getLinksModuleData(brand: BrandDocument): LinksModuleData;
  getCustomerBenefitField(customer: CustomerDocument): PassField;
  getMessagesField(customer: CustomerDocument): PassField | null;
}

export const PassesFieldServiceInterface = Symbol(
  'PassesFieldServiceInterface',
);
