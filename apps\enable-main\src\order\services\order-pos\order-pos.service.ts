import {
  AcknowledgementType,
  aggregatorOrderSources,
  areObjectIdsEqual,
  AutoRegisterMode,
  BranchDocument,
  CollectionName,
  CompanyDocument,
  CompanyType,
  CountryDialCode,
  CustomerDocument,
  DeliveryMethod,
  Discount,
  DiscountApplyTo,
  DriverDocument,
  EmbeddedBranchDto,
  Language,
  LoggerService,
  LoyaltyStatus,
  LRPSource,
  mapAsync,
  MenuItemDocument,
  omit,
  OrderDeliveryAction,
  OrderDeliveryType,
  OrderDocument,
  OrderItem,
  OrderItemGroup,
  OrderItemModifier,
  OrderLogActionEnum,
  OrderPaymentMethod,
  OrderPaymentStatus,
  OrderPosToCreate,
  OrderSource,
  OrderStatusEnum,
  OrderTransitionTrigger,
  responseCode,
  TriggerAction,
} from '@app/shared-stuff';
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import { BranchService } from '../../../branch/services/branch/branch.service';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { CompanyService } from '../../../company/services/company/company.service';
import { CustomerLoyaltyMemberServiceInterface } from '../../../customer/modules/customer-loyalty-member/customer-loyalty-member.service.interface';
import { CustomerOrderServiceInterface } from '../../../customer/modules/customer-order/customer-order.service.interface';
import { CustomerWriteServiceInterface } from '../../../customer/modules/customer-write/customer-write.service.interface';
import { ThirdPartyTaskCreationDto } from '../../../delivery/dto/third-party-task-creation.dto';
import { DeliveryConfigurationServiceInterface } from '../../../delivery/services/delivery-configuration/delivery-configuration.service.interface';
import { SlotService } from '../../../delivery/services/slot/slot.service';
import { ShortenUrlServiceInterface } from '../../../integration/shorten-url/services/shorten-url.service.interface';
import { MenuGroupService } from '../../../restaurant/services/menu-group/menu-group.service';
import { MenuItemService } from '../../../restaurant/services/menu-item/menu-item.service';
import { OrderItemDocument } from '../../models/orderItems.model';
import { OrderInvoiceService } from '../../modules/order-invoice/order-invoice.service';
import { OrderRedemptionService } from '../../modules/order-redemption/order-redemption.service';
import { OrderLogServiceInterface } from '../interfaces/order-log.service.interface';
import { OrderBenefitService } from '../order-benefit/order-benefit.service';
import { OrderDeliveryService } from '../order-delivery/order-delivery.service';
import { OrderNotificationService } from '../order-notification/order-notification.service';
import { OrderPaymentService } from '../order-payment/order-payment.service';
import { OrderStatusService } from '../order-status/order-status.service';
import { DriverService } from './../../../delivery/services/driver/driver.service';
import { SavedLocationService } from './../../../location/services/saved-location/saved-location.service';
import { OrderItemService } from './../order-item/order-item.service';
import { BranchAssignmentScheme } from '@app/shared-stuff/enums/company/branch-assignment-scheme.enum';
import { OrderService } from '../order/order.service';

@Injectable()
export class OrderPosService {
  private readonly loggerService = new LoggerService(OrderPosService.name);

  constructor(
    @InjectModel(CollectionName.ORDER) private orderModel: Model<OrderDocument>,
    @Inject('OrderLogServiceInterface')
    private readonly orderLogService: OrderLogServiceInterface,
    private companyService: CompanyService,
    private branchService: BranchService,
    @Inject('BrandServiceInterface')
    private brandService: BrandServiceInterface,
    private driverService: DriverService,
    @Inject(CustomerWriteServiceInterface)
    private customerWriteService: CustomerWriteServiceInterface,
    @Inject(CustomerOrderServiceInterface)
    private customerOrderService: CustomerOrderServiceInterface,
    private orderPaymentService: OrderPaymentService,
    private slotService: SlotService,
    private orderItemService: OrderItemService,
    @Inject(forwardRef(() => MenuItemService))
    private menuItemService: MenuItemService,
    private menuGroupService: MenuGroupService,
    private configService: ConfigService,
    private savedLocationService: SavedLocationService,
    private orderStatusService: OrderStatusService,
    private orderNotificationService: OrderNotificationService,
    private orderDeliveryService: OrderDeliveryService,
    private eventEmitter: EventEmitter2,
    @Inject(DeliveryConfigurationServiceInterface)
    private deliveryConfigurationService: DeliveryConfigurationServiceInterface,
    private orderInvoiceService: OrderInvoiceService,
    @Inject(ShortenUrlServiceInterface)
    private shortenUrlService: ShortenUrlServiceInterface,
    private orderBenefitService: OrderBenefitService,
    @Inject(CustomerLoyaltyMemberServiceInterface)
    private customerLoyaltyMemberService: CustomerLoyaltyMemberServiceInterface,
    private readonly orderRedemptionsService: OrderRedemptionService,
    private readonly orderService: OrderService,
  ) {}

  async create(orderPosToCreate: OrderPosToCreate) {
    // Validate the order Data

    let company = await this.companyService.get_details(
      orderPosToCreate.company,
    );
    await this.orderValidation(orderPosToCreate, company);
    this.updateCustomerPhoneNumberOnCreateOrdableDineInOrder(orderPosToCreate);
    // Construct Delivery Address
    await this.orderDeliveryAddressConstruct(orderPosToCreate);

    await this.getOrderBrand(orderPosToCreate, company);
    const branch = await this.getOrderBranch(orderPosToCreate, company);
    if (branch && !areObjectIdsEqual(branch.company, company._id))
      throw new BadRequestException('Branch is not under the same company');

    const driver = await this.getOrderDriver(orderPosToCreate);
    const customer = await this.customerWriteService.findOrCreate(
      {
        first_name: orderPosToCreate.first_name,
        last_name: orderPosToCreate.last_name,
        phone: orderPosToCreate.phone,
        country_code: orderPosToCreate.country_code,
        email: orderPosToCreate.email,
        contact_channel: orderPosToCreate.source,
        language: orderPosToCreate.language,
        company: new Types.ObjectId(orderPosToCreate.company),
      },
      orderPosToCreate.deliveryLocationId,
    );

    this.orderBenefitService.validateBenefits(
      orderPosToCreate.benefits,
      customer,
    );

    if (
      customer.loyaltyStatus !== LoyaltyStatus.MEMBER &&
      company?.loyaltyProgramConfig?.autoRegisterMode ===
        AutoRegisterMode.GLOBAL
    )
      await this.customerLoyaltyMemberService.registerLoyaltyCustomer(
        customer,
        {
          brandId: orderPosToCreate?.brand?._id,
          source: LRPSource.AUTOMATIC_LR,
          rawSource: `[${LRPSource.AUTOMATIC_LR.toUpperCase()}] ${orderPosToCreate?.source}`,
        },
        true,
      );

    //Construct ORder Date
    this.orderDateConstruct(orderPosToCreate, company);

    // Order Order payment method construct
    await this.orderPaymentMethodConstruct(orderPosToCreate);

    // Convert String TO ObjectIds
    orderPosToCreate.company = new Types.ObjectId(company._id) as any;
    orderPosToCreate.customer = new Types.ObjectId(customer._id) as any;
    // Construct Order Customer details
    if (orderPosToCreate.isCustomerUpdatable) {
      const firstName = orderPosToCreate.first_name
        ? orderPosToCreate.first_name
        : customer.first_name;
      const lastName = orderPosToCreate.last_name
        ? orderPosToCreate.last_name
        : customer.last_name;
      orderPosToCreate['customer_name'] = firstName + ' ' + lastName;
    } else {
      orderPosToCreate['customer_name'] = customer.full_name;
      orderPosToCreate['email'] = customer.email;
    }
    orderPosToCreate['customer_phone'] = customer.phone;

    // Construct Order Code
    const codeResult = await this.orderCodeConstruct(company);
    orderPosToCreate['code'] = codeResult.code;
    company = codeResult.company;

    //Generate the Order barCode for Tookan Integrate
    orderPosToCreate['barCode'] = Math.floor(
      1000000000000 + Math.random() * 9000000000000,
    );

    //Setting the delivery reminder to false ans setting company name
    orderPosToCreate['deliverReminderSent'] = false;
    orderPosToCreate['companyName'] = company.name;

    if (orderPosToCreate.discount && !orderPosToCreate.discounts) {
      orderPosToCreate.discounts =
        this.orderInvoiceService.convertLegacyDiscountToDiscounts(
          orderPosToCreate.discount,
        );
    }

    const { couponId, filteredDiscounts } =
      this.orderService.extractCouponInfoForOrdable(
        orderPosToCreate.discounts,
        orderPosToCreate.couponId,
      );
    orderPosToCreate.couponId = couponId;

    const { benefits: earnedCouponBenefits, discounts: couponDiscounts } =
      await this.orderService.applyCouponBenefitToOrder(couponId, customer);

    orderPosToCreate.discounts = [
      ...(filteredDiscounts ?? []),
      ...couponDiscounts,
    ];

    orderPosToCreate.discounts =
      await this.orderRedemptionsService.validateLoyaltyRedemptions(
        company,
        orderPosToCreate,
        customer,
      );

    if (orderPosToCreate.isAggregator === undefined) {
      orderPosToCreate.isAggregator = aggregatorOrderSources.includes(
        orderPosToCreate.source,
      );
    }

    const orderToSave = omit(orderPosToCreate, ['benefits']);
    const order = await this.saveOrderToDatabase(orderToSave, company);
    order.createdBy = orderPosToCreate.current_user;
    order.companyName = company.name;
    order.items = undefined;
    order.status = OrderStatusEnum.UNASSIGNED;

    this.checkOrderNeedsAutomaticAcknowledge(order, company);
    order.localization = company.localization;

    if (orderPosToCreate.traceId) {
      if (!order.tracerIds) {
        order.tracerIds = [orderPosToCreate.traceId];
      } else if (order.tracerIds.indexOf(orderPosToCreate.traceId) == -1) {
        order.tracerIds.push(orderPosToCreate.traceId);
      }
    }

    await order.save();
    this.constructMonth(order);

    await this.setOrderLocationShortenUrl(order);
    await this.setOrderTrackingLinkShortenUrl(order);

    // Creating the order Items
    if (orderPosToCreate.items && orderPosToCreate.items.length) {
      if (order.orderType == 'restaurant') {
        order.items = await this.constructOrderItems(
          order,
          orderPosToCreate.items,
        );
        order.invoiced_amount = order.items
          .map((item) => item.totalAmountAfterDiscount)
          .reduce((a, b) => a + b, 0);
      } else {
        await this.orderSimpleItemsConstruction(order, orderPosToCreate.items);
      }
    }

    order.set(
      this.orderInvoiceService.computeFromInvoicedAmount(
        order.invoiced_amount,
        order.delivery_amount,
        order.discounts,
      ),
    );

    if (driver) {
      const { first_name, last_name, _id } = driver;
      order.assigned_driver_name = `${first_name} ${last_name || ''}`.trim();
      order.driver = new Types.ObjectId(_id);
    } else if (order.autoAssign) {
      order.assigned_driver_name = 'Auto Assignment';
    }

    await this.orderBenefitService.redeemBenefits(
      orderPosToCreate.benefits,
      customer,
      order,
    );

    order.benefits = [...(order.benefits ?? []), ...earnedCouponBenefits];
    await order.save();

    // Handel delivery Slots
    if (orderPosToCreate.delivery_slot_id) {
      await this.slotService.reserveSlotToOrder(
        order,
        orderPosToCreate.delivery_date,
        orderPosToCreate.delivery_slot_id,
      );
    }

    // Create the payment link when the payment method is online
    if (
      orderPosToCreate.payment_method == 'online' &&
      orderPosToCreate.paymentCode
    ) {
      // In case of the payment is online and the client provide a payment code so no need to generate a a new payment
      await this.orderPaymentService.addPaymentToOrder(
        order,
        branch,
        orderPosToCreate.paymentCode,
      );
    } else if (order.payment_method == OrderPaymentMethod.online) {
      await this.orderPaymentService.createOrderPayment(
        order,
        branch,
        company,
        orderPosToCreate.callback_url,
        order.delivery_action,
        order.createdBy,
      );
    }

    order.company = company as any;
    // fire the order creation event based on there is a branch or not
    if (branch) {
      order.assignedAt = moment.utc().toDate();
      order['branch_object'] = new Types.ObjectId(branch._id) as any;
      order['branch'] = branch;
      order['branchName'] = branch.name;
      order['pickupLocationId'] = new Types.ObjectId(branch.locationId) as any;
      order.assignedBy = orderPosToCreate.current_user;
      await order.save();
      await this.orderStatusService.changeOrderStatus(
        order,
        OrderStatusEnum.PENDING,
        OrderTransitionTrigger.CREATED,
        orderPosToCreate.current_user,
      );
    } else {
      order.branch = undefined;
      order.branch_object = undefined;
      await order.save();
      await this.orderStatusService.changeOrderStatus(
        order,
        OrderStatusEnum.UNASSIGNED,
        OrderTransitionTrigger.CREATED,
        orderPosToCreate.current_user,
      );
    }

    // Checking IF the order Can BE Sent To Deliverect Or Not
    if (company.deliverectAccountId && company.usingDeliverectChannel) {
      order.canSentToDeliverectAsChannel = true;
      await order.save();
    }

    order.loyaltyProgress =
      await this.customerOrderService.computeLoyaltyProgress(
        company,
        order,
        customer,
      );
    await order.save();

    await this.orderLogService.saveOrderLog(
      order,
      { requestedObject: {} },
      { responseObject: orderPosToCreate },
      OrderLogActionEnum.ON_ORDER_GENERAL_CREATED,
      orderPosToCreate.current_user,
    );
    order['branch'] = branch as any;
    await this.applyOrderCreationPostFunctions(
      order,
      customer,
      branch,
      company,
    );
    return order;
  }

  async applyOrderCreationPostFunctions(
    order: OrderDocument,
    customer: CustomerDocument,
    branch: BranchDocument,
    company: CompanyDocument,
  ) {
    await this.companyService.assignOrderToCompany(order, company);
    customer = await this.customerOrderService.assignOrderToCustomer(
      order,
      customer,
    );
    if (
      customer.first_name.toLowerCase() === 'anonymous ' &&
      order.source !== OrderSource.DELIVERECT
    )
      await this.updateAnonymousCustomer(order.customer_name, customer);

    // Handle Delivery Task Creation
    const thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto = {
      order: order,
      customer: customer,
      branch: branch,
      company: company,
      pickupLocation: order.pickupLocation,
      currentUser: order.createdBy,
    };
    const deliveryConfiguration =
      await this.deliveryConfigurationService.findByCompanyId(company._id);
    if (
      deliveryConfiguration &&
      deliveryConfiguration.usingEbDelivery == false &&
      deliveryConfiguration.usingCompanyDrivers == false &&
      deliveryConfiguration.usingBranchDrivers == false &&
      deliveryConfiguration.usingThirdParty == true &&
      deliveryConfiguration.thirdPartyConfiguration?.thirdParties?.length === 1
    ) {
      await this.orderDeliveryService.handleThirdPartyTasks(
        deliveryConfiguration.thirdPartyConfiguration.thirdParties[0],
        thirdPartyTaskCreationDto,
      );
    }

    // Firing Event Emitter
    this.eventEmitter.emit('order.created', order, company);

    // Firing the Order Triggers
    await this.firingOrderTriggers(order, customer);
  }

  async setOrderLocationShortenUrl(order: OrderDocument) {
    const locationUrl = `${this.configService.get('EXTERNAL_LOCATION_URL')}/${
      order.language === Language.arabic ? 'ar' : 'en'
    }/${order._id.toString()}`;

    order.shortenUrl = await this.shortenUrlService.shortenUrl({
      url: locationUrl,
      canExpire: false,
    });
    await order.save();
  }

  async setOrderTrackingLinkShortenUrl(order: OrderDocument) {
    order.shortenTrackingLink = await this.shortenUrlService.shortenUrl({
      url: `${this.configService.get('ORDER_TRACKING_BASE')}/${order._id}`,
      canExpire: false,
    });
    await order.save();
  }

  private updateCustomerPhoneNumberOnCreateOrdableDineInOrder(
    orderPosToCreate: OrderPosToCreate,
  ): void {
    if (orderPosToCreate.phone === '00000000')
      orderPosToCreate.phone = this.getRandomPhoneNumber();
  }

  private getRandomPhoneNumber(): string {
    const digits = '0123456789';
    let phoneNumber = '11';

    for (let i = 2; i < 8; i++) {
      const randomIndex = Math.floor(Math.random() * digits.length);
      phoneNumber += digits[randomIndex];
    }

    return phoneNumber;
  }

  private async updateAnonymousCustomer(
    orderCustomerName: string,
    customer: CustomerDocument,
  ) {
    const customerFullName = orderCustomerName.split(' ');
    customer.full_name = orderCustomerName;
    customer.first_name = customerFullName[0];
    customer.last_name = customerFullName[1];
    await customer.save();
  }

  private constructMonth(order: OrderDocument) {
    order.month = new Date(order['createdAt']).toLocaleString('default', {
      month: 'long',
    });
  }

  private async firingOrderTriggers(
    order: OrderDocument,
    customer: CustomerDocument,
  ) {
    const fire = async (trigger: TriggerAction) =>
      await this.orderNotificationService.fireOrderNotificationTrigger(
        order,
        trigger,
      );
    const isNonMember = customer.loyaltyStatus !== LoyaltyStatus.MEMBER;

    if (order.source == OrderSource.WEBSTORE) {
      await fire(TriggerAction.ON_WEBSTORE_ORDER_CREATED);
      if (isNonMember)
        await fire(TriggerAction.ON_WEBSTORE_ORDER_CREATED_NON_LOYALTY_MEMBERS);
    } else if (order.source == OrderSource.WHATSAPP && isNonMember) {
      await fire(TriggerAction.ON_WHATSAPP_ORDER_NON_LOYALTY_MEMBERS);
    } else if (aggregatorOrderSources.includes(order.source)) {
      await fire(TriggerAction.ON_AGGREGATOR_ORDER);
      if (isNonMember)
        await fire(TriggerAction.ON_AGGREGATOR_ORDER_NON_LOYALTY_MEMBERS);
    }

    // Sending the Notification regarding the delivery And Payment
    await this.orderNotificationService.handleDeliveryAndPaymentNotification(
      order,
    );

    await fire(TriggerAction.ON_ORDER_CREATED);
    if (isNonMember)
      await fire(TriggerAction.ON_ORDER_CREATED_NON_LOYALTY_MEMBERS);
  }

  private async orderValidation(
    orderPosToCreate: OrderPosToCreate,
    company: CompanyDocument,
  ) {
    if (orderPosToCreate['delivery_address']) {
      orderPosToCreate['delivery_address'] = undefined;
    }

    // Gift Validation
    if (
      orderPosToCreate.is_gift &&
      (!orderPosToCreate.recipient_name || !orderPosToCreate.recipient_phone)
    ) {
      throw {
        code: responseCode.MISSING_DATA,
        message: 'Please provide the recipient name and recipient phone number',
        statusCode: 400,
      };
    }

    if (orderPosToCreate.delivery_type == 'schedule_later')
      // DEPRECATED: EBL-2058
      throw new BadRequestException(
        'Orders to be scheduled later are no longer supported. Please change the delivery_type.',
        responseCode.IN_VALID_INPUT.toString(),
      );

    if (
      company.type === CompanyType.RETAIL &&
      orderPosToCreate.delivery_type !== OrderDeliveryType.urgent
    ) {
      throw new BadRequestException(
        'Orders of retail companies must have an URGENT delivery_type.',
        responseCode.IN_VALID_INPUT.toString(),
      );
    }

    if (
      orderPosToCreate.total_amount_after_discount <
        orderPosToCreate.invoiced_amount &&
      !orderPosToCreate.discount
    ) {
      // if the total amount after discount smaller that the total amount
      orderPosToCreate.discount =
        (orderPosToCreate.invoiced_amount -
          orderPosToCreate.total_amount_after_discount) /
        orderPosToCreate.invoiced_amount;
    }

    orderPosToCreate['deliveryParty'] = undefined;
    orderPosToCreate.delivery_type =
      orderPosToCreate.delivery_type ?? orderPosToCreate.dispatch_type;
    orderPosToCreate.driver =
      orderPosToCreate.driver ?? orderPosToCreate.driver_id;
    orderPosToCreate.country_code =
      orderPosToCreate.country_code ??
      (orderPosToCreate.source == OrderSource.WEBSTORE
        ? CountryDialCode.UNDEFINED
        : CountryDialCode.QATAR);
  }

  private async orderCodeConstruct(company: CompanyDocument) {
    // Increase it in the database document
    company = await this.companyService.incrementNumberOfOrders(company._id);
    const code =
      company.acronym +
      '-O-' +
      company.number_of_orders.toString().padStart(5, '0');
    return { code, company };
  }

  private orderDateConstruct(
    orderPosToCreate: OrderPosToCreate,
    company: CompanyDocument,
  ): void {
    this.validateRequiredDates(orderPosToCreate, company);
    this.allowEmptyOrderDates(orderPosToCreate, company);
    const isWrongSlots =
      orderPosToCreate.delivery_slot_from &&
      orderPosToCreate.delivery_slot_to &&
      orderPosToCreate.delivery_slot_to < orderPosToCreate.delivery_slot_from;
    if (
      (!orderPosToCreate.delivery_time || !orderPosToCreate.pickup_time) &&
      isWrongSlots
    )
      this.swapWrongSlotsTimes(orderPosToCreate);

    if (orderPosToCreate.pickup_date)
      this.handlePickupDateConstruction(orderPosToCreate);

    if (orderPosToCreate.delivery_date) {
      this.handleDeliveryDateConstruction(orderPosToCreate);
      if (!orderPosToCreate.pickup_date)
        orderPosToCreate.pickup_date = moment
          .utc(orderPosToCreate.delivery_date)
          .subtract(30, 'minutes')
          .toDate() as any;
    }
  }

  private allowEmptyOrderDates(
    orderPosToCreate: OrderPosToCreate,
    company: CompanyDocument,
  ): void {
    if (
      orderPosToCreate.delivery_type === OrderDeliveryType.urgent &&
      company.type === CompanyType.RESTAURANT
    ) {
      orderPosToCreate.delivery_date = undefined;
      orderPosToCreate.pickup_date = undefined;
    }
  }

  private handlePickupDateConstruction(
    orderPosToCreate: OrderPosToCreate,
  ): void {
    if (!moment(orderPosToCreate.pickup_date).isValid())
      throw new BadRequestException(
        'Please Enter Valid Pick-up Date',
        responseCode.IN_VALID_INPUT.toString(),
      );

    if (orderPosToCreate.pickup_time) {
      orderPosToCreate.pickup_date = moment
        .utc(
          orderPosToCreate.pickup_date + ' ' + orderPosToCreate.pickup_time,
          'YYYY-MM-DD HH:mm',
        )
        .toDate() as any;
    } else if (orderPosToCreate.delivery_slot_from) {
      orderPosToCreate.pickup_date = moment
        .utc(
          orderPosToCreate.pickup_date +
            ' ' +
            orderPosToCreate.delivery_slot_from,
          'YYYY-MM-DD HH:mm',
        )
        .toDate() as any;
    }
  }

  private handleDeliveryDateConstruction(
    orderPosToCreate: OrderPosToCreate,
  ): void {
    if (!moment(orderPosToCreate.delivery_date).isValid())
      throw new BadRequestException(
        'Please Enter Valid Delivery Date',
        responseCode.IN_VALID_INPUT.toString(),
      );
    if (orderPosToCreate.delivery_time) {
      orderPosToCreate.delivery_date = moment
        .utc(
          orderPosToCreate.delivery_date + ' ' + orderPosToCreate.delivery_time,
          'YYYY-MM-DD HH:mm',
        )
        .toDate() as any;
    } else if (orderPosToCreate.delivery_slot_from) {
      orderPosToCreate.delivery_date = moment
        .utc(
          orderPosToCreate.delivery_date +
            ' ' +
            orderPosToCreate.delivery_slot_from,
          'YYYY-MM-DD HH:mm',
        )
        .toDate() as any;
    }
  }

  private swapWrongSlotsTimes(orderPosToCreate: OrderPosToCreate): void {
    const temp = orderPosToCreate.delivery_slot_from;
    orderPosToCreate.delivery_slot_from = orderPosToCreate.delivery_slot_to;
    orderPosToCreate.delivery_slot_to = temp;
  }

  private validateRequiredDates(
    orderPosToCreate: OrderPosToCreate,
    company: CompanyDocument,
  ): void {
    if (
      !orderPosToCreate.delivery_date &&
      orderPosToCreate.delivery_type != OrderDeliveryType.schedule_later &&
      orderPosToCreate.delivery_type != OrderDeliveryType.urgent &&
      orderPosToCreate.delivery_action != 'in_store_pickup' &&
      company.type !== CompanyType.RETAIL
    ) {
      throw {
        code: responseCode.MISSING_DATA,
        message: 'Please provide the Delivery Date its required in this case',
        statusCode: 400,
      };
    }

    if (
      !orderPosToCreate.pickup_date &&
      company.type !== CompanyType.RETAIL &&
      orderPosToCreate.delivery_type !== OrderDeliveryType.scheduled
    ) {
      orderPosToCreate.pickup_date = moment.utc().format('YYYY-MM-DD HH:mm');
    }

    if (
      !orderPosToCreate.pickup_date &&
      orderPosToCreate.delivery_type === OrderDeliveryType.scheduled &&
      orderPosToCreate.delivery_action ===
        OrderDeliveryAction.IN_STORE_PICKUP &&
      company.type === CompanyType.RESTAURANT
    ) {
      throw new BadRequestException(
        "Please provide the pickup date, it's required in this case",
        responseCode.MISSING_DATA.toString(),
      );
    }
  }

  private async orderPaymentMethodConstruct(
    orderPosToCreate: OrderPosToCreate,
  ) {
    if (orderPosToCreate.payment_method == 'online')
      orderPosToCreate['payment_status'] = OrderPaymentStatus.PENDING;

    if (
      orderPosToCreate.delivery_action == 'in_store_pickup' &&
      (orderPosToCreate.payment_method == ' cash' ||
        orderPosToCreate.payment_method == 'card_machine')
    ) {
      orderPosToCreate['payment_status'] = OrderPaymentStatus.PENDING;
    }
    if (
      orderPosToCreate.is_gift &&
      (orderPosToCreate.payment_method == 'cash' ||
        orderPosToCreate.payment_method == 'card_machine')
    ) {
      orderPosToCreate['payment_status'] = OrderPaymentStatus.COMPLETED;
    }
    if (orderPosToCreate.payment_method == 'prepaid') {
      orderPosToCreate['payment_status'] = OrderPaymentStatus.COMPLETED;
      orderPosToCreate.prepaidBy = orderPosToCreate.prepaidBy ?? 'customer';
    }

    if (orderPosToCreate.orderIsAlreadyPaid) {
      orderPosToCreate['payment_status'] = OrderPaymentStatus.COMPLETED;
    }

    if (
      !orderPosToCreate.orderIsAlreadyPaid &&
      orderPosToCreate.source == OrderSource.DELIVERECT
    ) {
      orderPosToCreate['payment_status'] = OrderPaymentStatus.PENDING;
    }
    return orderPosToCreate;
  }

  private async orderDeliveryAddressConstruct(
    orderPosToCreate: OrderPosToCreate,
  ) {
    if (
      !orderPosToCreate.deliveryLocationId &&
      orderPosToCreate.deliveryLocation &&
      orderPosToCreate.delivery_action == 'delivery_location'
    ) {
      await this.createLocation(orderPosToCreate);
    } else if (orderPosToCreate.deliveryLocationId) {
      orderPosToCreate.deliveryLocationId = new Types.ObjectId(
        orderPosToCreate.deliveryLocationId,
      ) as any;
      const location = await this.savedLocationService.getDetails(
        orderPosToCreate.deliveryLocationId as any,
      );
      orderPosToCreate['deliveryLocation'] = location as any;
    } else {
      orderPosToCreate.deliveryLocation = undefined;
      orderPosToCreate.deliveryLocationId = undefined;
    }
    return orderPosToCreate;
  }

  private async createLocation(orderPosToCreate: OrderPosToCreate) {
    try {
      const location = await this.savedLocationService.create(
        orderPosToCreate.deliveryLocation,
      );
      orderPosToCreate.deliveryLocationId = location._id;
      orderPosToCreate['deliveryLocation'] = location as any;
    } catch (error) {
      this.loggerService.error('Error in creating the delivery location', {
        error,
      });
      orderPosToCreate.deliveryLocation = undefined;
      orderPosToCreate.deliveryLocationId = undefined;
    }
  }

  private async getOrderBranch(
    orderPosToCreate: OrderPosToCreate,
    company: CompanyDocument,
  ): Promise<BranchDocument | undefined> {
    if (orderPosToCreate.branch) {
      return this.getBranchFromOrder(orderPosToCreate, company);
    }

    if (orderPosToCreate.brand) {
      return this.getBranchFromBrand(orderPosToCreate, company);
    }

    return this.getBranchFromCompany(orderPosToCreate, company);
  }

  private async getBranchFromOrder(
    orderPosToCreate: OrderPosToCreate,
    company: CompanyDocument,
  ): Promise<BranchDocument> {
    return (await this.branchService.get_details(
      orderPosToCreate.branch,
      company._id,
    )) as BranchDocument;
  }

  private async getBranchFromBrand(
    orderPosToCreate: OrderPosToCreate,
    company: CompanyDocument,
  ): Promise<BranchDocument | undefined> {
    const brand = await this.brandService.findById(orderPosToCreate.brand._id);

    if (brand.branches.length === 1) {
      return (await this.branchService.get_details(
        brand.branches[0]._id.toString(),
      )) as BranchDocument;
    }

    if (
      brand.branches.length > 1 &&
      company.branchAssignmentConfig.branchAssignmentScheme ===
        BranchAssignmentScheme.NEAREST &&
      orderPosToCreate.deliveryLocationId
    ) {
      return await this.getNearestBranch(
        orderPosToCreate,
        brand.branches,
        company._id,
      );
    }

    if (
      brand.branches.length > 1 &&
      company?.branchAssignmentConfig?.branchAssignmentScheme ===
        BranchAssignmentScheme.DEFAULT
    ) {
      return await this.getDefaultBranch(
        company.branchAssignmentConfig.defaultBranchId,
      );
    }

    return undefined;
  }

  private async getNearestBranch(
    orderPosToCreate: OrderPosToCreate,
    branches: string[] | EmbeddedBranchDto[] | BranchDocument[],
    companyId: Types.ObjectId,
  ): Promise<BranchDocument | undefined> {
    const location = await this.savedLocationService.getDetails(
      orderPosToCreate.deliveryLocationId.toHexString(),
    );

    if (!location.latitude || !location.longitude) {
      return undefined;
    }

    const branchIds = this.getBranchIds(branches);
    return await this.branchService.getNearestBranch(
      location.latitude,
      location.longitude,
      companyId,
      branchIds,
      orderPosToCreate.brandId,
    );
  }

  private async getBranchFromCompany(
    orderPosToCreate: OrderPosToCreate,
    company: CompanyDocument,
  ): Promise<BranchDocument | undefined> {
    const branches = await this.branchService.index({
      company: orderPosToCreate.company,
    });

    if (branches.length === 1) {
      return branches[0];
    }

    if (
      branches.length > 1 &&
      company.branchAssignmentConfig.branchAssignmentScheme ===
        BranchAssignmentScheme.NEAREST &&
      orderPosToCreate.deliveryLocationId
    ) {
      return await this.getNearestBranch(
        orderPosToCreate,
        branches,
        company._id,
      );
    }

    if (
      branches.length > 1 &&
      company?.branchAssignmentConfig?.branchAssignmentScheme ===
        BranchAssignmentScheme.DEFAULT
    ) {
      return await this.getDefaultBranch(
        company.branchAssignmentConfig.defaultBranchId,
      );
    }

    return undefined;
  }

  private async getDefaultBranch(
    defaultBranchId: Types.ObjectId,
  ): Promise<BranchDocument> {
    return await this.branchService.findById(defaultBranchId);
  }

  private async getOrderBrand(
    orderPosToCreate: OrderPosToCreate,
    company: CompanyDocument,
  ) {
    if (!orderPosToCreate.brand && !orderPosToCreate.brandId) {
      const brands = (
        await this.brandService.index(
          {
            companyId: company._id,
          } as any,
          undefined,
        )
      )[0]['paginatedResult'];

      if (brands && brands.length == 1) {
        orderPosToCreate['brand'] = {
          name: brands[0].name,
          _id: brands[0]['_id'],
          phoneNumber: brands[0]['phoneNumber'],
          senderId: brands[0]['senderId'],
          image: brands[0].image,
          emailSenderId: brands[0].emailSenderId,
        };
        return brands[0];
      }
    } else if (orderPosToCreate.brand || orderPosToCreate.brandId) {
      const brand = await this.brandService.findById(
        new Types.ObjectId(
          orderPosToCreate.brandId
            ? orderPosToCreate.brandId
            : orderPosToCreate.brand._id,
        ),
      );
      if (brand) {
        orderPosToCreate.brand = {
          _id: new Types.ObjectId(brand._id),
          name: brand.name,
          senderId: brand.senderId,
          image: brand.image,
          phoneNumber: brand.phoneNumber,
          emailSenderId: brand.emailSenderId,
        };
      }
      return brand;
    }
  }

  private getBranchIds(
    branches: string[] | EmbeddedBranchDto[] | BranchDocument[],
  ) {
    return branches
      ? branches.map(
          (branch) => new Types.ObjectId(branch._id ? branch._id : branch),
        )
      : [];
  }

  private async getOrderDriver(orderPosToCreate: OrderPosToCreate) {
    let driver: DriverDocument;
    if (orderPosToCreate.autoAssign) {
      orderPosToCreate.driver = undefined;
      driver = undefined;
    } else if (
      orderPosToCreate.driver &&
      orderPosToCreate.deliveryMethod == DeliveryMethod.BRANCH_DRIVERS
    ) {
      driver = await this.driverService.get_details(
        orderPosToCreate.driver.toString(),
      );
      orderPosToCreate.driver = driver._id;
    } else if (orderPosToCreate.deliveryMethod == DeliveryMethod.E_BUTLER) {
      driver = await this.driverService.get_details('5fd09b4c35497a3edbe3d6fb');
      orderPosToCreate.driver = driver._id;
    }

    if (orderPosToCreate['delivery_action'] == 'in_store_pickup') {
      orderPosToCreate['assigned_driver_name'] = 'Pick-up';
    }
    return driver;
  }

  // Converts various item formats (OrderItem[], MenuItem[]) into OrderItem[]
  private async constructOrderItems(
    order: OrderDocument,
    items: any[],
  ): Promise<OrderItem[]> {
    if (!items || items.length === 0) return [];

    // childItem.subItems need to be constructed before childItem
    const processChildItem = async (childItem: any) =>
      await this.constructRestaurantOrderItem(order, {
        ...childItem,
        subItems: await mapAsync(childItem.subItems, (childSubItem) =>
          this.constructRestaurantOrderItem(order, childSubItem),
        ),
      });

    // item.subItems need to be constructed before item
    const processOrderItem = async (item: any) =>
      await this.constructRestaurantOrderItem(order, {
        ...item,
        subItems: await mapAsync(item.subItems, processChildItem),
      });

    return await mapAsync(items, processOrderItem);
  }

  private async reformatModifierGroups(modifierGroups: OrderItemGroup[]) {
    const modifierGroupsExist = modifierGroups && modifierGroups?.length > 0;
    if (!modifierGroupsExist) return [];

    const reformatItemGroupModifier = (
      modifier: OrderItemModifier,
    ): OrderItemModifier => ({
      name: modifier.name,
      price: modifier.price ?? 0,
      quantity: modifier.quantity ?? 1,
      totalAmount: (modifier.price ?? 0) * (modifier.quantity ?? 1),
      plu: modifier.plu,
      modifierReference: modifier.modifierReference ?? modifier['_id'],
      index: modifier.index,
      adlerId: modifier.modifierReference ?? modifier['_id'],
    });

    const reformatItemGroup = (
      orderItemGroup: OrderItemGroup,
    ): OrderItemGroup => {
      const modifiersToUse =
        orderItemGroup['items'] ?? orderItemGroup.modifiers ?? [];

      const reference =
        (orderItemGroup?.groupReference ??
        orderItemGroup['_id'] ??
        modifiersToUse.length > 0)
          ? modifiersToUse[0]
            ? modifiersToUse[0].modifierReference
            : undefined
          : undefined;

      const modifiers = modifiersToUse.map(
        reformatItemGroupModifier,
      ) as OrderItemModifier[];

      return {
        name: orderItemGroup['nameEn'] ?? orderItemGroup.name,
        plu: orderItemGroup.plu,
        price:
          orderItemGroup.price ??
          modifiers.reduce((acc, cur) => {
            acc += cur.price;
            return acc;
          }, 0),
        quantity: orderItemGroup.quantity ?? 1,
        groupReference: reference,
        modifiers: modifiers,
      };
    };

    return await Promise.all(
      modifierGroups
        .map(reformatItemGroup)
        .map(this.lookupMenuGroupPrice, this),
    );
  }

  private async lookupMenuGroupPrice(
    orderItemGroup: OrderItemGroup,
  ): Promise<OrderItemGroup> {
    const realMenuGroup = await this.menuGroupService.findByRefOrPlu(
      orderItemGroup.groupReference,
      orderItemGroup.plu,
    );

    if (!realMenuGroup) return orderItemGroup;

    const lookupModifierPrice = (
      modifier: OrderItemModifier,
    ): OrderItemModifier => {
      const realMenuModifier = realMenuGroup.items.find(
        (menuModifier) =>
          menuModifier.plu && modifier.plu && menuModifier.plu === modifier.plu,
      );

      if (!realMenuModifier) return modifier;

      return {
        ...modifier,
        price: realMenuModifier.price,
        totalAmount: realMenuModifier.price * modifier.quantity,
      };
    };

    return {
      ...orderItemGroup,
      modifiers: orderItemGroup.modifiers.map(lookupModifierPrice),
    };
  }

  private async constructRestaurantOrderItem(
    order: OrderDocument,
    item: OrderItem,
  ) {
    const itemId = item.itemReference ?? item['_id'];
    const realItem = itemId
      ? await this.menuItemService.getDetails(
          itemId,
          undefined,
          new Types.ObjectId(order.company),
        )
      : undefined;

    const modifierGroups = await this.reformatModifierGroups(
      item['menuGroups'] || item['mnodifierGroups'] || item['modifierGroups'],
    );
    const modifierPrice = modifierGroups
      .flatMap((modifierGroup) => modifierGroup.modifiers)
      .map((modifier) => modifier.price * (modifier.quantity ?? 1))
      .reduce((a, b) => a + b, 0);

    const subProducts: OrderItem[] =
      item['subItems'] ?? item['subProducts'] ?? [];
    const subProductPrice = subProducts
      .map((subProduct) => subProduct.totalAmountAfterDiscount)
      .reduce((a, b) => a + b, 0);

    const itemDiscounts = this.filterItemDiscounts(
      order.discounts,
      realItem ?? item,
    );
    const basePrice = realItem ? realItem.price : item.price;

    const quantity = item.quantity ?? 1;
    const singleItemAmount = basePrice + modifierPrice;
    const totalAmount = (singleItemAmount + subProductPrice) * quantity;

    // TODO: support quantity for menu item discounts
    const discount = this.orderInvoiceService.getTotalDiscount(
      singleItemAmount,
      itemDiscounts,
    );

    const totalAmountAfterDiscount = totalAmount - discount;

    const orderItem: OrderItem = {
      name: realItem ? realItem.nameEn : (item['nameEn'] ?? item['name']),
      description: item['descriptionEn'] ?? item['description'],
      price: realItem ? realItem.price : item.price,
      plu: realItem ? realItem.plu : item.plu,
      itemReference: realItem
        ? realItem._id.toString()
        : (item.itemReference ?? item['_id']),
      special_instructions: item.special_instructions,
      adlerId: realItem ? realItem.adlerId : '',
      solutionErpId: realItem ? realItem.solutionErpId : ' ',
      brandId: realItem?.externalBrandId ?? item.brandId,
      pickup_spot_id: item.pickup_spot_id,
      code: realItem?.code,
      subProducts,
      basePrice,
      quantity,
      totalAmount,
      discount,
      totalAmountAfterDiscount,
      modifierGroups,
      microsMenuItemId: realItem ? realItem.microsMenuItemId : 0,
      images: realItem?.images,
      externalImage: realItem ? realItem.externalImage : item.externalImage,
    };
    return orderItem;
  }

  private async orderSimpleItemsConstruction(
    order: OrderDocument,
    items: OrderItem[],
  ) {
    order.total_amount = 0;
    order.invoiced_amount = 0;
    order.items = undefined;
    for (let i = 0; i < items.length; i++) {
      items[i]['company'] = order.company['_id'];
      const orderItem: OrderItemDocument = await this.orderItemService.create(
        items[i],
      );
      order.order_items.push(orderItem._id);
      orderItem.branchId = items[i].pickup_spot_id
        ? new Types.ObjectId(items[i].pickup_spot_id)
        : undefined;

      orderItem.totalAmount = orderItem.price * orderItem.quantity;

      const itemDiscounts = this.filterItemDiscounts(order.discounts, items[i]);
      orderItem.discount = this.orderInvoiceService.getTotalDiscount(
        orderItem.totalAmount,
        itemDiscounts,
      );

      orderItem.totalAmountAfterDiscount =
        orderItem.totalAmount - orderItem.discount;
      order.invoiced_amount += orderItem.totalAmountAfterDiscount;
      await orderItem.save();
    }
    order.total_amount = order.invoiced_amount + order.delivery_amount;
    this.orderInvoiceService.addCorrectionalDiscount(order);
    await order.save();
  }

  private filterItemDiscounts(
    discounts: Discount[],
    item: OrderItem | MenuItemDocument,
  ): Discount[] {
    if (!discounts || discounts.length === 0 || !item) return [];
    return discounts
      .filter((discount) => discount.applyTo === DiscountApplyTo.MENU_ITEM)
      .filter(
        (discount) =>
          (discount.menuItemId instanceof Types.ObjectId &&
            (discount.menuItemId.equals(item['parentMenuItemId']) ||
              discount.menuItemId.equals(item['_id']) ||
              discount.menuItemId.equals(item['itemReference']))) ||
          discount.menuItemId === item['itemReference'] ||
          discount.menuItemId === item['reference'],
      );
  }

  private checkOrderNeedsAutomaticAcknowledge(
    order: OrderDocument,
    company: CompanyDocument,
  ) {
    order.isAcknowledged =
      company.acknowledgementScreenType !== AcknowledgementType.MANUAL ||
      (company.usingDeliverectPos &&
        company.deliverectPosOrderSnooze &&
        order.source === OrderSource.DELIVERECT);
    if (
      order.isAcknowledged &&
      company.type == CompanyType.RESTAURANT &&
      order.delivery_type === OrderDeliveryType.urgent
    ) {
      this.constructUrgentOrderDates(order, 10);
    }
  }

  //TODO: move this function to helper service
  private constructUrgentOrderDates(
    order: OrderDocument,
    preparationTime: number,
  ) {
    const pickupDate = moment()
      .utc()
      .add(preparationTime, 'minutes')
      .format('YYYY-MM-DD HH:mm');
    order.pickup_date = moment.utc(pickupDate, 'YYYY-MM-DD HH:mm').toDate();
    if (order.branch) {
      const deliveryDate = moment(pickupDate);
      if (order.delivery_action != OrderDeliveryAction.IN_STORE_PICKUP) {
        deliveryDate.add(
          order['branch']['callCenterConfig']
            ? order['branch']['callCenterConfig']['currentlyDeliveringIn']
            : 0,
          'minutes',
        );
        order.delivery_date = moment
          .utc(deliveryDate.format('YYYY-MM-DD HH:mm'), 'YYYY-MM-DD HH:mm')
          .toDate();
      } else order.delivery_date = undefined;
    }
  }

  private async saveOrderToDatabase(
    orderPosToCreate: OrderPosToCreate,
    company: CompanyDocument,
  ): Promise<OrderDocument> {
    try {
      return await this.orderModel.create(orderPosToCreate);
    } catch (error: unknown) {
      if (error['code'] == 11000 && error['keyPattern']?.['code']) {
        const codeConstruct = await this.orderCodeConstruct(company);
        orderPosToCreate.code = codeConstruct.code;
        return this.saveOrderToDatabase(orderPosToCreate, company);
      } else {
        throw error;
      }
    }
  }
}
