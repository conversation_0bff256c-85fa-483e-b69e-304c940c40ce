import { OrderLogReceivedObjectDto } from '@app/shared-stuff/dtos/order/order-log-received-object.dto';
import { OrderLogSentObjectDto } from '@app/shared-stuff/dtos/order/order-log-sent-object.dto';
import { OrderLogActionEnum } from '@app/shared-stuff/enums/order/order-log-action.enum';
import { PipelineStage } from 'mongoose';

export interface OrderLogRepositoryInterface {
  aggregate(pipeline: PipelineStage[]): Promise<any[]>;
  save(orderLog: {
    sentObject: OrderLogSentObjectDto;
    receivedObject: OrderLogReceivedObjectDto;
    logAction: OrderLogActionEnum;
    orderCode: string;
  });
}
