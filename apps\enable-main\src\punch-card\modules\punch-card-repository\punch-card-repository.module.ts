import { PunchCard, PunchCardSchema } from '@app/shared-stuff';
import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { PunchCardRepository } from './punch-card.repository';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: PunchCard.name, schema: PunchCardSchema },
    ]),
  ],
  providers: [PunchCardRepository],
  exports: [PunchCardRepository],
})
export class PunchCardRepositoryModule {}
