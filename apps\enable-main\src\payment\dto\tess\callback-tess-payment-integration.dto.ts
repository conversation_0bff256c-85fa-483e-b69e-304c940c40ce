import { ApiProperty } from '@nestjs/swagger';
import {
  IsDate,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';
import { CallbackTESSPaymentStatus } from '../../enums/tess/callback-tess-payment-status.enum';
import { CallbackTESSPaymentType } from '../../enums/tess/callback-tess-payment-type.enum';

export class CallbackTESSPaymentIntegrationDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Transaction ID',
  })
  @IsNotEmpty()
  @IsString()
  id: string;

  @ApiProperty({
    type: String,
    required: true,
    maxLength: 255,
    description: 'Order ID',
  })
  @IsNotEmpty()
  @MaxLength(255)
  order_number: string;

  @ApiProperty({
    type: Number,
    required: true,
    description: 'Product price',
  })
  @IsNotEmpty()
  @IsNumber()
  order_amount: number;

  @ApiProperty({
    type: String,
    required: true,
    maxLength: 3,
  })
  @IsNotEmpty()
  @MaxLength(3)
  order_currency: string;

  @ApiProperty({
    type: String,
    required: true,
    maxLength: 1024,
    description: 'Product description',
  })
  @IsNotEmpty()
  @MaxLength(1024)
  order_description: string;

  @ApiProperty({
    type: String,
    required: true,
    enum: CallbackTESSPaymentType,
    maxLength: 36,
    description: 'Operation type: sale, 3ds, redirect, refund, chargeback',
  })
  @IsNotEmpty()
  @MaxLength(36)
  @IsEnum(CallbackTESSPaymentType)
  type: CallbackTESSPaymentType;

  @ApiProperty({
    type: String,
    required: true,
    maxLength: 20,
    enum: CallbackTESSPaymentStatus,
    description: 'Transaction status: success, fail, waiting',
  })
  @IsNotEmpty()
  @MaxLength(20)
  @IsEnum(CallbackTESSPaymentStatus)
  status: CallbackTESSPaymentStatus;

  @ApiProperty({
    type: String,
    required: false,
    maxLength: 1024,
    description:
      'Decline or error reason (for "sale" and "refund" operation types only).',
  })
  @IsOptional()
  @MaxLength(1024)
  reason?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Retrieval Reference Number value from the acquirer system',
  })
  @IsOptional()
  @IsString()
  rrn?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Approval code value from the acquirer system',
  })
  @IsOptional()
  @IsString()
  approval_code?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Card number mask',
  })
  @IsOptional()
  @IsString()
  card?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  card_expiration_date?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  card_token?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: "Customer's first and last name",
  })
  @IsOptional()
  @IsString()
  customer_name?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  @IsEmail()
  customer_email?: string;

  @ApiProperty({
    type: String,
    required: false,
    maxLength: 3,
  })
  @IsOptional()
  @MaxLength(3)
  customer_country?: string;

  @ApiProperty({
    type: String,
    required: false,
    maxLength: 32,
  })
  @IsOptional()
  @MaxLength(32)
  customer_state?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  customer_city?: string;

  @ApiProperty({
    type: String,
    required: false,
    maxLength: 32,
  })
  @IsOptional()
  @MaxLength(32)
  customer_address?: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  customer_ip: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Transaction date Example: 2020-08-05 07:41:10',
  })
  @IsOptional()
  @IsDate()
  date?: Date;

  @ApiProperty({
    type: String,
    required: false,
    description:
      'Reference to the first transaction that initializes the recurring',
  })
  @IsOptional()
  @IsString()
  recurring_init_trans_id?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  recurring_token?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  schedule_id?: string;

  @ApiProperty({
    type: String,
    required: true,
    description:
      'Special signature, used to validate callback Addition in Signature section',
  })
  @IsNotEmpty()
  @IsString()
  hash: string;
}
