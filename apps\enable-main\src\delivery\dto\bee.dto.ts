import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
export class BeeConfig {
  baseUrl: string;
  token: string;
}
export enum BeePaymentType {
  cash = 1,
  card = 2,
}

export class BeeTaskToCreate {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  mobile: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  customer_address_latitude: number;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  customer_address_longitude: number;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Delivery Address',
  })
  @IsNotEmpty()
  customer_full_address: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  payment_type: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  total_amount: number;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Diffrenece between delivery and pickup as mins',
  })
  @IsNotEmpty()
  estimate_cooking_time: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Enable Company Name',
  })
  @IsNotEmpty()
  client_name: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Enable Branch mobile',
  })
  @IsNotEmpty()
  branch_mobile: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Enable Company Code',
  })
  @IsNotEmpty()
  client_id: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Enable order code',
  })
  @IsNotEmpty()
  ref_number: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  branch_latitude: number;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  branch_longitude: number;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Enable Pickup Address',
  })
  @IsNotEmpty()
  branch_address: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Enable Company Name + BRanch name',
  })
  @IsNotEmpty()
  branch_name: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Enable Branch Code',
  })
  @IsNotEmpty()
  branch_id: string;
}

export class onBeeTaskUpdated {}
