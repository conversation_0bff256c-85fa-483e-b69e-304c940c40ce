import { SharedStuffModule } from '@app/shared-stuff';
import { HttpModule } from '@nestjs/axios';
import { forwardRef, Module } from '@nestjs/common';

import { CompanyModule } from '../../../company/company.module';
import { CouponModule } from '../../../coupon/coupon.module';
import { CustomerCodeModule } from '../../../customer/modules/customer-code/customer-code.module';
import { CustomerReadModule } from '../../../customer/modules/customer-read/customer-read.module';
import { CustomerWriteModule } from '../../../customer/modules/customer-write/customer-write.module';
import { LocationModule } from '../../../location/location.module';
import { LoyaltyTierReadModule } from '../../../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.module';
import { SharedModule } from '../../../shared/shared.module';
import { StoreModule } from '../../../store/store.module';
import { IntegrationLogModule } from '../../integration-log/integration-log.module';
import { ShortenUrlModule } from '../../shorten-url/shorten-url.module';
import { OrdableCategoriesService } from './services/categories/ordable-categories.service';
import { OrdableCustomersService } from './services/customers/ordable-customers.service';
import { OrdableCustomersServiceInterface } from './services/customers/ordable-customers.service.interface';
import { OrdableFreeDeliveryService } from './services/free-delivery/ordable-free-delivery.service';
import { OrdableFreeDeliveryServiceInterface } from './services/free-delivery/ordable-free-delivery.service.interface';
import { OrdableListenerService } from './services/listener/ordable-listener.service';
import { OrdableOptionsService } from './services/options/ordable-options.service';
import { OrdableHttpRequestsService } from './services/ordable-http-requests.service';
import { OrdableProductOptionsService } from './services/product-options/ordable-product-options.service';
import { OrdableProductsService } from './services/products/ordable-products.service';
import { OrdablePromotionsSyncService } from './services/promotions-sync/ordable-promotions-sync.service';
import { OrdablePromotionsSyncServiceInterface } from './services/promotions-sync/ordable-promotions-sync.service.interface';
import { OrdablePromotionsService } from './services/promotions/ordable-promotions.service';
import { OrdablePromotionsServiceInterface } from './services/promotions/ordable-promotions.service.interface';
import { OrdableStoresService } from './services/stores/ordable-stores.service';
import { OrdableStoresServiceInterface } from './services/stores/ordable-stores.service.interface';

@Module({
  imports: [
    HttpModule,
    SharedModule,
    SharedStuffModule,
    StoreModule,
    IntegrationLogModule,
    LoyaltyTierReadModule,
    forwardRef(() => CouponModule),
    IntegrationLogModule,
    ShortenUrlModule,
    CompanyModule,
    CustomerCodeModule,
    CustomerReadModule,
    CustomerWriteModule,
    LocationModule,
  ],
  controllers: [],
  providers: [
    OrdableListenerService,
    {
      provide: 'OrdableProductsServiceInterface',
      useClass: OrdableProductsService,
    },
    {
      provide: 'OrdableProductOptionsServiceInterface',
      useClass: OrdableProductOptionsService,
    },
    {
      provide: 'OrdableOptionsServiceInterface',
      useClass: OrdableOptionsService,
    },
    {
      provide: 'OrdableCategoriesServiceInterface',
      useClass: OrdableCategoriesService,
    },
    {
      provide: 'OrdableHttpRequestsServiceInterface',
      useClass: OrdableHttpRequestsService,
    },
    {
      provide: OrdableCustomersServiceInterface,
      useClass: OrdableCustomersService,
    },
    {
      provide: OrdablePromotionsServiceInterface,
      useClass: OrdablePromotionsService,
    },
    {
      provide: OrdablePromotionsSyncServiceInterface,
      useClass: OrdablePromotionsSyncService,
    },
    {
      provide: OrdableFreeDeliveryServiceInterface,
      useClass: OrdableFreeDeliveryService,
    },
    {
      provide: OrdableStoresServiceInterface,
      useClass: OrdableStoresService,
    },
  ],
  exports: [
    'OrdableProductsServiceInterface',
    'OrdableProductOptionsServiceInterface',
    'OrdableCategoriesServiceInterface',
    'OrdableOptionsServiceInterface',
    OrdableCustomersServiceInterface,
    OrdablePromotionsServiceInterface,
  ],
})
export class OrdableModule {}
