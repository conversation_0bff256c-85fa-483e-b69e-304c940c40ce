import {
  GenericExceptionFilter,
  MicroserviceCommunicationService,
  OrderReportFilterV1Dto,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Controller,
  Get,
  Inject,
  OnModuleDestroy,
  OnModuleInit,
  Query,
  Req,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';

@Controller('analytics')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags('Report-v1')
@SetMetadata('module', 'report-v1')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class ReportV1Controller implements OnModuleInit, OnModuleDestroy {
  constructor(
    @Inject('enable-main-reporting-producer') private client: ClientProxy,
    private microserviceCommunicationService: MicroserviceCommunicationService,
  ) {}

  async onModuleDestroy() {
    await this.microserviceCommunicationService.disconnect(this.client);
  }

  async onModuleInit() {
    await this.microserviceCommunicationService.connect(this.client);
  }

  @Get('order')
  @SetMetadata('action', 'order-report')
  public async generateOrderReport(
    @Query() orderReportFilterV1Dto: OrderReportFilterV1Dto,
    @Req() req: Request,
  ) {
    orderReportFilterV1Dto.companyId = req['company_id']
      ? req['company_id']
      : orderReportFilterV1Dto.companyId;
    return this.microserviceCommunicationService.produceAndWaitForResponse<
      OrderReportFilterV1Dto,
      any
    >(this.client, 'order.report-v1.request', orderReportFilterV1Dto);
  }
}
