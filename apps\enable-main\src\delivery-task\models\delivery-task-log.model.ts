import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

export type DeliveryTaskLogDocument = HydratedDocument<DeliveryTaskLog>;
@Schema({ timestamps: true })
export class DeliveryTaskLog {
  @Prop({
    type: {},
    required: true,
  })
  rawRequest: {};

  @Prop({
    type: {},
    required: true,
  })
  rawResponse: {};

  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: 'DeliveryTask',
  })
  deliveryTask: Types.ObjectId;
}

const DeliveryTaskLogSchema = SchemaFactory.createForClass(DeliveryTaskLog);

export { DeliveryTaskLogSchema };
