// EBL-3925 [Loyalty Program] Loyalty New Customers - Grace Period v1.3
// Add ON_FIRST_GRACE_PERIOD_REMINDER_CURRENT_TIER and ON_FIRST_GRACE_PERIOD_REMINDER_UPPER_TIER
db.triggers.insertMany([
  {
    name: '[CUSTOMER] ON_FIRST_GRACE_PERIOD_REMINDER_CURRENT_TIER',
    client: 'ENABLE_MAIN',
    action: 'ON_FIRST_GRACE_PERIOD_REMINDER_CURRENT_TIER',
    module: 'CUSTOMER',
    replacement: [
      'firstName',
      'fullName',
      'loyaltyPointBalance',
      'loyaltyTier',
      'upperLoyaltyTier',
      'remainingOrdersCurrentTier',
      'remainingOrdersUpperTier',
      'upperTierDiscountValue',
      'tierDiscountValue',
      'tierDiscountOrderValueThreshold',
      'loyaltyRegistrationPageLink',
      'walletPassAccessPageLink',
      'walletPassLink',
      'ordableLink',
      'firstGracePeriodReminderDate',
    ],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    name: '[CUSTOMER] ON_FIRST_GRACE_PERIOD_REMINDER_UPPER_TIER',
    client: 'ENABLE_MAIN',
    action: 'ON_FIRST_GRACE_PERIOD_REMINDER_UPPER_TIER',
    module: 'CUSTOMER',
    replacement: [
      'firstName',
      'fullName',
      'loyaltyPointBalance',
      'loyaltyTier',
      'upperLoyaltyTier',
      'remainingOrdersCurrentTier',
      'remainingOrdersUpperTier',
      'upperTierDiscountValue',
      'tierDiscountValue',
      'tierDiscountOrderValueThreshold',
      'loyaltyRegistrationPageLink',
      'walletPassAccessPageLink',
      'walletPassLink',
      'ordableLink',
      'firstGracePeriodReminderDate',
    ],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
]);
