import { Injectable } from '@nestjs/common';
import {
  CollectionName,
  GenericRepository,
  LocationItem,
  LocationItemDocument,
} from '@app/shared-stuff';
import { LocationItemRepositoryInterface } from './location-item-repository.interface';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model, UpdateQuery } from 'mongoose';

@Injectable()
export class LocationItemRepository
  extends GenericRepository<LocationItemDocument, LocationItem>
  implements LocationItemRepositoryInterface
{
  constructor(
    @InjectModel(CollectionName.LOCATION_ITEM)
    private locationItemModel: Model<LocationItemDocument, LocationItem>,
  ) {
    super(locationItemModel);
  }

  async updateMany(
    filterQuery: FilterQuery<LocationItemDocument>,
    update: UpdateQuery<LocationItemDocument>,
  ) {
    return this.locationItemModel.updateMany(filterQuery, update).exec();
  }
}
