import { forwardRef, Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { CompanyModule } from '../../../company/company.module';
import { SharedModule } from '../../../shared/shared.module';
import { LoyaltyTierReadModule } from '../loyalty-tier-read/loyalty-tier-read.module';
import { LoyaltyTierRepositoryModule } from '../loyalty-tier-repository/loyalty-tier-repository.module';
import { LoyaltyTierWriteService } from './loyalty-tier-write.service';
import { LoyaltyTierWriteServiceInterface } from './loyalty-tier-write.service.interface';
import { BenefitUtilityModule } from 'apps/enable-main/src/benefit/benefit-utility/benefit-utility.module';

@Module({
  providers: [
    {
      provide: LoyaltyTierWriteServiceInterface,
      useClass: LoyaltyTierWriteService,
    },
  ],
  imports: [
    EventEmitterModule,
    SharedModule,
    LoyaltyTierRepositoryModule,
    LoyaltyTierReadModule,
    BenefitUtilityModule,
    forwardRef(() => CompanyModule),
  ],
  exports: [LoyaltyTierWriteServiceInterface],
})
export class LoyaltyTierWriteModule {}
