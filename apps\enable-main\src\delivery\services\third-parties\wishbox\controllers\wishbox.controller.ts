import {
  GenericExceptionFilter,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Inject,
  Post,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { Api<PERSON><PERSON><PERSON>Auth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { IntegrationLogInterceptor } from '../../../../../integration/integration-log/interceptors/integration-log.interceptor';
import { UpdateWishboxDeliveryStatusDto } from '../dtos/update-wishbox-delivery-status.dto';
import { WishboxServiceInterface } from '../services/wishbox.service.interface';

@Controller('wishbox')
@UseInterceptors(TransformInterceptor, IntegrationLogInterceptor)
@UseFilters(GenericExceptionFilter)
@ApiTags('wishbox Controller')
@SetMetadata('module', 'wishbox')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class WishboxController {
  constructor(
    @Inject(WishboxServiceInterface)
    private readonly wishboxService: WishboxServiceInterface,
  ) {}

  @Post('public/webhook/updateOrderStatus')
  @SetMetadata('public', true)
  async updateWishboxDeliveryStatus(
    @Body() updateWishboxDeliveryStatusDto: UpdateWishboxDeliveryStatusDto,
  ) {
    await this.wishboxService.updateWishboxDeliveryStatus(
      updateWishboxDeliveryStatusDto,
    );
  }
}
