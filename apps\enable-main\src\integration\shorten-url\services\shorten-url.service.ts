import {
  CreateShortenUrlDto,
  LoggerService,
  MicroserviceCommunicationService,
  MicroserviceMessagePattern,
  ShortenUrl,
  ShortenUrlCodeDto,
} from '@app/shared-stuff';
import {
  Inject,
  Injectable,
  OnModuleDestroy,
  OnModuleInit,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ClientProxy } from '@nestjs/microservices';
import { lastValueFrom } from 'rxjs';
import { ShortenUrlServiceInterface } from './shorten-url.service.interface';

@Injectable()
export class ShortenUrlService
  implements ShortenUrlServiceInterface, OnModuleInit, OnModuleDestroy
{
  private readonly loggerService = new LoggerService(ShortenUrlService.name);
  private readonly baseUrl: string;
  constructor(
    private readonly configService: ConfigService,
    private microserviceCommunicationService: MicroserviceCommunicationService,
    @Inject('enable-main-shorten-producer')
    private readonly client: ClientProxy,
  ) {
    this.baseUrl = this.configService.get('SHORTEN_BASE_URL');
  }

  async shortenUrl(createShortenUrlDto: CreateShortenUrlDto): Promise<string> {
    try {
      return `${this.baseUrl}/${(await this.create(createShortenUrlDto)).code}`;
    } catch (error) {
      this.loggerService.error(
        `Error while shortening url: ${error.message}`,
        error.stack,
        { createShortenUrlDto, error },
      );
      return createShortenUrlDto.url;
    }
  }

  async onModuleDestroy() {
    await this.microserviceCommunicationService.disconnect(this.client);
  }

  async onModuleInit() {
    await this.microserviceCommunicationService.connect(this.client);
  }

  async create(createShortenUrlDto: CreateShortenUrlDto): Promise<ShortenUrl> {
    return lastValueFrom(
      this.client.send<ShortenUrl, CreateShortenUrlDto>(
        MicroserviceMessagePattern.CREATE_SHORTEN_URL,
        createShortenUrlDto,
      ),
    );
  }

  async getDetails(shortenUrlCodeDto: ShortenUrlCodeDto) {
    return lastValueFrom(
      this.client.send(
        MicroserviceMessagePattern.GET_SHORTEN_URL,
        shortenUrlCodeDto,
      ),
    );
  }
}
