import {
  <PERSON>ache<PERSON>eys,
  Company,
  CompanyDocument,
  fullCacheKeys,
} from '@app/shared-stuff';
import { CompanyEvents } from '@app/shared-stuff/enums/company/company-events.enum';
import { CacheServiceInterface } from '@app/shared-stuff/modules/cache/interfaces/cache.interface';
import { Inject } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';

export class CompanyListener {
  constructor(
    @Inject(CacheServiceInterface)
    private readonly cacheService: CacheServiceInterface,
  ) {}

  @OnEvent(CompanyEvents.COMPANY_UPDATED)
  async handleCompanyUpdated(company: CompanyDocument) {
    const key = fullCacheKeys[CacheKeys.COMPANY](company._id);
    const keyExists = await this.cacheService.getCache(key, Company);

    if (keyExists) {
      await this.cacheService.setCache(key, company, 60 * 60 * 24);
    }
  }
}
