import {
  AssignDriverDto,
  CompanyDocument,
  ContactChannel,
  CreateDeliveryOrderDto,
  CustomerDocument,
  DeliveryOrder,
  DeliveryOrderDocument,
  DeliveryOrderDropOff,
  DeliveryOrderStatus,
  DeliveryOrderTransportProgress,
  DistributionCenterDocument,
  GetAllDeliveryOrderDto,
  HelperSharedServiceInterface,
  IHelperSharedService,
  IndexResultDto,
  Language,
  LoggerService,
  PusherService,
  responseCode,
  ShipmentDocument,
  TempCustomerDocument,
  UpdateDeliveryOrderStatusDto,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import {
  BadRequestException,
  NotFoundException,
} from '@nestjs/common/exceptions';
import { plainToInstance } from 'class-transformer';
import * as moment from 'moment-timezone';
import { FilterQuery, PipelineStage, Types } from 'mongoose';
import { CompanyService } from '../../../company/services/company/company.service';
import { CustomerWriteServiceInterface } from '../../../customer/modules/customer-write/customer-write.service.interface';
import { TookanJobType } from '../../../delivery/enums/tookan-job-type.enum';
import { TookanTaskStatus } from '../../../delivery/enums/tookan-task-status.enum';
import { DriverService } from '../../../delivery/services/driver/driver.service';
import { TookanWebhookPayload } from '../../../delivery/types/tookan-webhook-payload.type';
import { DeliveryOrderRepositoryInterface } from '../../repositories/delivery-order/delivery-order-repository.interface';
import { DistributionCenterServiceInterface } from '../distribution-center/distribution-center-service.interface';
import { ShipmentServiceInterface } from '../shipment/shipment-service.interface';
import { TempCustomerServiceInterface } from '../temp-customer/temp-customer-service.interface';
import { DeliveryOrderServiceInterface } from './delivery-order-service.interface';
import { DeliveryOrderTookanService } from './delivery-order-tookan.service';

@Injectable()
export class DeliveryOrderService implements DeliveryOrderServiceInterface {
  private readonly loggerService = new LoggerService(DeliveryOrderService.name);

  constructor(
    @Inject(DeliveryOrderRepositoryInterface)
    private repository: DeliveryOrderRepositoryInterface,
    @Inject(ShipmentServiceInterface)
    private shipmentService: ShipmentServiceInterface,
    @Inject(TempCustomerServiceInterface)
    private tempCustomerServiceInterface: TempCustomerServiceInterface,
    @Inject(DistributionCenterServiceInterface)
    private distributionCenterService: DistributionCenterServiceInterface,
    private companyService: CompanyService,
    private deliveryOrderTookanTask: DeliveryOrderTookanService,
    @Inject(CustomerWriteServiceInterface)
    private customerWriteService: CustomerWriteServiceInterface,
    private driverService: DriverService,
    private pusherService: PusherService,
    @Inject(IHelperSharedService)
    private helperSharedService: HelperSharedServiceInterface,
  ) {}

  async findAll(
    getAllDeliveryOrderDto: GetAllDeliveryOrderDto,
  ): Promise<IndexResultDto<DeliveryOrder>[]> {
    const pipeline: PipelineStage[] = [];
    this.addMatchStage(pipeline, getAllDeliveryOrderDto);
    this.addSortStage(pipeline);
    this.addPaginationStage(pipeline, getAllDeliveryOrderDto);
    return (await this.repository.aggregate(pipeline)) as [
      IndexResultDto<DeliveryOrder>,
    ];
  }

  async create(
    createDeliveryOrderDto: CreateDeliveryOrderDto,
  ): Promise<DeliveryOrderDocument> {
    const logTracker = new Types.ObjectId().toHexString();
    this.loggerService.log(
      `delivery order ${logTracker}: createDto`,
      createDeliveryOrderDto,
    );
    const shipments = await this.shipmentService.findByIdIn(
      createDeliveryOrderDto.shipmentIds,
    );
    this.validateShipments(shipments, createDeliveryOrderDto.dropOff);

    const company = await this.companyService.get_details(
      shipments[0].companyId.toHexString(),
    );
    this.loggerService.log(
      `Delivery order ${logTracker}: shipments`,
      shipments,
    );
    const { customer, enableCustomer } = await this.getCustomer(
        createDeliveryOrderDto,
        shipments[0].salesOrderId,
      ),
      distributionCenter: DistributionCenterDocument =
        await this.getDistributionCenter(createDeliveryOrderDto);

    const deliveryOrder = plainToInstance(DeliveryOrder, {
      ...createDeliveryOrderDto,
      status: DeliveryOrderStatus.PENDING,
      pickupLocation: shipments[0].location,
      shipments: shipments.map((shipment) => shipment.toJSON()),
      salesOrdersIds: this.getSalesOrderIdsFromShipments(shipments),
      deliveryDate: createDeliveryOrderDto.deliveryDate
        ? createDeliveryOrderDto.deliveryDate
        : moment.utc().toDate(),
      deliveryLocation:
        createDeliveryOrderDto.dropOff ==
        DeliveryOrderDropOff.DISTRIBUTION_CENTER
          ? distributionCenter.location
          : customer.location,
      cashToCollect: shipments.reduce(
        (sum, shipment) => sum + (shipment.cashToCollect ?? 0),
        0,
      ),
      code: await this.generateCode(company),
      enableCustomerId: enableCustomer?._id,
      tempCustomerId: customer?._id,
      deliveryTask: {
        tookanTasksIds: [],
      },
      logTracker,
    });
    this.loggerService.log(
      `delivery order ${logTracker}: before create`,
      deliveryOrder,
    );
    const createdDeliveryOrder = await this.repository.create(deliveryOrder);
    await this.postCreate(
      createdDeliveryOrder,
      company,
      distributionCenter,
      customer,
    );
    this.loggerService.log(
      `delivery order ${logTracker}: after create`,
      createdDeliveryOrder,
    );
    return createdDeliveryOrder;
  }

  async findOne(id: Types.ObjectId): Promise<DeliveryOrderDocument> {
    const deliveryOrder = await this.repository.findById(id);
    if (!deliveryOrder) {
      throw new NotFoundException('Delivery Order not found');
    }
    return deliveryOrder;
  }

  async getSalesRelated(salesOrderId: string) {
    const customer: TempCustomerDocument =
      await this.tempCustomerServiceInterface.findBySalesOrderId(salesOrderId);

    const distributionCenters: DistributionCenterDocument[] =
      await this.distributionCenterService.findByCompanyId(customer.companyId);

    return {
      customer,
      distributionCenters,
    };
  }

  async findOneByCode(code: string) {
    return this.repository.findOneByCode(code);
  }

  async updateStatus({ _id, status }: UpdateDeliveryOrderStatusDto) {
    const updateFields: any = { status };
    if (status === DeliveryOrderStatus.REJECTED)
      updateFields['deliveryTask.assignedDriverName'] = 'Failed to assign';

    const deliveryOrder = await this.repository.findOneAndUpdate(
      { _id },
      { $set: updateFields },
    );

    if (!deliveryOrder) {
      throw new NotFoundException(
        'Delivery Order not found',
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    }

    if (deliveryOrder.status == DeliveryOrderStatus.COMPLETED) {
      await this.shipmentService.updateShipmentsAddress(
        deliveryOrder.shipments.map(
          (shipment) => new Types.ObjectId(shipment['_id']),
        ),
        deliveryOrder.deliveryLocation,
      );
    }

    const company = await this.companyService.get_details(
      deliveryOrder.companyId.toHexString(),
    );
    this.fireOrderPusherEvent(deliveryOrder, company);
  }

  async updateTransportProgress(
    deliveryOrderId: Types.ObjectId,
    transportProgress: DeliveryOrderTransportProgress,
  ) {
    const updateFields: any = { transportProgress };
    if (transportProgress === DeliveryOrderTransportProgress.REJECTED)
      updateFields['deliveryTask.assignedDriverName'] = 'Failed to assign';

    const deliveryOrder = await this.repository.findOneAndUpdate(
      { _id: deliveryOrderId },
      { $set: updateFields },
    );

    if (!deliveryOrder) {
      throw new NotFoundException(
        'Delivery Order not found',
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    }
    const company = await this.companyService.get_details(
      deliveryOrder.companyId.toHexString(),
    );
    this.fireOrderPusherEvent(deliveryOrder, company);
  }

  async deliveryTaskUpdated(
    deliveryOrder: DeliveryOrderDocument,
    deliveryTaskPayload: TookanWebhookPayload,
  ) {
    const company = await this.companyService.get_details(
      deliveryOrder.companyId.toHexString(),
    );

    const statusUpdateHandlers = {
      [`${TookanTaskStatus.ACCEPTED}`]: async () => {
        await this.updateStatus({
          _id: deliveryOrder._id,
          status: DeliveryOrderStatus.ASSIGNED,
        });
      },
      [`${TookanTaskStatus.STARTED}_${TookanJobType.PICKUP}`]: async () => {
        await this.updateStatus({
          _id: deliveryOrder._id,
          status: DeliveryOrderStatus.IN_PROGRESS,
        });
        await this.updateTransportProgress(
          deliveryOrder._id,
          DeliveryOrderTransportProgress.PICKING_UP,
        );
      },
      [`${TookanTaskStatus.SUCCESSFUL}_${TookanJobType.PICKUP}`]: async () => {
        await this.updateTransportProgress(
          deliveryOrder._id,
          DeliveryOrderTransportProgress.PICKED_UP,
        );
      },
      [`${TookanTaskStatus.STARTED}_${TookanJobType.DELIVERY}`]: async () => {
        await this.updateTransportProgress(
          deliveryOrder._id,
          DeliveryOrderTransportProgress.DELIVERING,
        );
      },
      [`${TookanTaskStatus.SUCCESSFUL}_${TookanJobType.DELIVERY}`]:
        async () => {
          await this.updateStatus({
            _id: deliveryOrder._id,
            status: DeliveryOrderStatus.COMPLETED,
          });
        },
      [`${TookanTaskStatus.REJECTED}_${TookanJobType.PICKUP}`]: async () => {
        await this.updateTransportProgress(
          deliveryOrder._id,
          DeliveryOrderTransportProgress.REJECTED,
        );
      },
      [`${TookanTaskStatus.REJECTED}_${TookanJobType.DELIVERY}`]: async () => {
        await this.updateStatus({
          _id: deliveryOrder._id,
          status: DeliveryOrderStatus.REJECTED,
        });
      },
    };

    const statusUpdateKey = `${deliveryTaskPayload.job_status}_${deliveryTaskPayload.job_type}`;
    const statusUpdateHandler =
      statusUpdateHandlers[statusUpdateKey] ||
      statusUpdateHandlers[deliveryTaskPayload.job_status];

    if (statusUpdateHandler) await statusUpdateHandler();

    this.fireOrderPusherEvent(deliveryOrder, company);
  }

  private addMatchStage(
    pipeline: PipelineStage[],
    getAllDeliveryOrderDto: GetAllDeliveryOrderDto,
  ) {
    const {
      companyId,
      brandId,
      salesOrderId,
      distributionCenterId,
      status,
      tempCustomerId,
    } = getAllDeliveryOrderDto;

    const match: FilterQuery<DeliveryOrder> = {};

    if (companyId) match.companyId = companyId;

    if (brandId) match['brand._id'] = brandId;

    if (salesOrderId) {
      match.salesOrdersIds = {
        $elemMatch: { $eq: salesOrderId },
      };
    }
    if (distributionCenterId) match.distributionCenterId = distributionCenterId;

    if (status) match.status = status;

    if (tempCustomerId) match.tempCustomerId = tempCustomerId;

    pipeline.push({ $match: match });
  }

  private addPaginationStage(
    pipeline: PipelineStage[],
    getAllDeliveryOrderDto: GetAllDeliveryOrderDto,
  ) {
    pipeline.push(
      this.helperSharedService.createPaginationStage(
        getAllDeliveryOrderDto.offset,
        getAllDeliveryOrderDto.limit,
      ),
    );
  }

  private getSalesOrderIdsFromShipments(
    shipments: ShipmentDocument[],
  ): string[] {
    return [...new Set(shipments.map((shipment) => shipment.salesOrderId))];
  }

  private async postCreate(
    deliveryOrder: DeliveryOrderDocument,
    company: CompanyDocument,
    distributionCenter: DistributionCenterDocument,
    tempCustomer: TempCustomerDocument,
  ) {
    this.fireOrderPusherEvent(deliveryOrder, company);
    await this.deliveryOrderTookanTask.createTookanTask(
      deliveryOrder,
      company,
      tempCustomer,
      distributionCenter,
    );
  }

  private fireOrderPusherEvent(
    deliveryOrder: DeliveryOrderDocument,
    company: CompanyDocument,
  ) {
    return this.pusherService.fireEvent(
      [this.pusherService.getCompanyChannelName(company), deliveryOrder.code],
      'DeliveryOrderUpdated',
      deliveryOrder,
    );
  }

  private validateShipments(
    shipments: ShipmentDocument[],
    dropOff: DeliveryOrderDropOff,
  ) {
    // Check if the shipments array is empty
    if (shipments.length === 0) {
      throw new NotFoundException('No shipments provided');
    }

    // Check if all shipments have the same sales order ID
    const firstSalesOrderId = shipments[0].salesOrderId;
    if (
      dropOff == DeliveryOrderDropOff.CUSTOMER &&
      shipments.some((shipment) => shipment.salesOrderId !== firstSalesOrderId)
    ) {
      throw new BadRequestException(
        'All shipments must have the same sales order',
      );
    }

    // Check if all shipments have a location
    if (shipments.some((shipment) => !shipment.location)) {
      throw new NotFoundException('All shipments must have a location');
    }

    // Check if all shipments have the same address
    const firstShipmentAddress = shipments[0].location.address;
    if (
      shipments.some(
        (shipment) => shipment.location.address !== firstShipmentAddress,
      )
    ) {
      throw new BadRequestException('All shipments must have the same address');
    }
  }

  private async getCustomer(
    createDeliveryOrderDto: CreateDeliveryOrderDto,
    salesOrderId: string,
  ) {
    let customer: TempCustomerDocument, enableCustomer: CustomerDocument;
    if (createDeliveryOrderDto.dropOff == DeliveryOrderDropOff.CUSTOMER) {
      customer =
        await this.tempCustomerServiceInterface.findBySalesOrderId(
          salesOrderId,
        );
      if (!customer) {
        throw new NotFoundException('Customer not found');
      }
      enableCustomer = await this.customerWriteService.findOrCreate({
        first_name: customer.firstName,
        last_name: customer.lastName,
        phone: customer.phone,
        country_code: customer.countryCode,
        email: customer.email,
        contact_channel: ContactChannel.UNKNOWN,
        language: Language.arabic,
        company: new Types.ObjectId(createDeliveryOrderDto.companyId),
      });
    }
    return { customer, enableCustomer };
  }

  private async getDistributionCenter(
    createDeliveryOrderDto: CreateDeliveryOrderDto,
  ) {
    let distributionCenter: DistributionCenterDocument;
    if (
      createDeliveryOrderDto.dropOff == DeliveryOrderDropOff.DISTRIBUTION_CENTER
    ) {
      distributionCenter = await this.distributionCenterService.findOne(
        createDeliveryOrderDto.distributionCenterId,
      );
      if (!distributionCenter) {
        throw new NotFoundException('Distribution Center not found');
      }

      if (!distributionCenter.location) {
        throw new NotFoundException('Distribution Center location not found');
      }
    }
    return distributionCenter;
  }

  private async generateCode(company: CompanyDocument) {
    const totalDeliveryOrders = await this.repository.countDocuments({
      companyId: company._id,
    });

    return `${company.acronym}-D-${totalDeliveryOrders
      .toString()
      .padStart(5, '0')}`;
  }

  private addSortStage(pipeline: PipelineStage[]) {
    pipeline.push({ $sort: { createdAt: -1 } });
  }

  async assignDriverToTask(assignDriverDto: AssignDriverDto): Promise<void> {
    const deliveryOrder = await this.findOne(assignDriverDto.deliveryOrderId);
    const driver = await this.driverService.get_details(
      assignDriverDto.enableDriverId.toHexString(),
    );
    const company = await this.companyService.findById(
      assignDriverDto.companyId,
    );

    await this.deliveryOrderTookanTask.assignAgentToTask(
      deliveryOrder,
      driver,
      company,
    );
    this.fireOrderPusherEvent(deliveryOrder, company);
  }
}
