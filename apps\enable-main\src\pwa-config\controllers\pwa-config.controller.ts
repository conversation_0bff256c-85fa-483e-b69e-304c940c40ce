import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Query,
  Body,
  HttpCode,
  UseFilters,
  UseInterceptors,
  SetMetadata,
} from '@nestjs/common';

import {
  CreatePwaConfigDto,
  GeneratePresignedUrlDto,
  GenericExceptionFilter,
  IndexPwaConfigDto,
  PublicPwaConfig,
  TransformInterceptor,
  UpdatePwaConfigDto,
} from '@app/shared-stuff';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { PwaConfigService } from '../services/pwa-config.service';
import { Types } from 'mongoose';

@Controller()
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags('pwa-config')
@SetMetadata('module', 'pwa-config')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class PwaConfigController {
  constructor(private readonly pwaConfigService: PwaConfigService) {}

  @Get('pwa-config/public')
  @SetMetadata('public', 'true')
  async findPublicByHost(
    @Query('host') host: string,
  ): Promise<PublicPwaConfig> {
    return this.pwaConfigService.findPublicByHost(host);
  }

  @Get('admin/pwa-config')
  @SetMetadata('action', 'findAll')
  async findAll(@Query() { companyId }: IndexPwaConfigDto) {
    return this.pwaConfigService.findAll(companyId);
  }

  @Get('admin/pwa-config/:id')
  @SetMetadata('action', 'findById')
  async findById(@Param('id') id: Types.ObjectId) {
    return this.pwaConfigService.findById(id);
  }

  @Post('admin/pwa-config')
  @SetMetadata('action', 'create')
  async create(@Body() createPwaConfigDto: CreatePwaConfigDto) {
    return this.pwaConfigService.create(createPwaConfigDto);
  }

  @Patch('admin/pwa-config/:id')
  @SetMetadata('action', 'update')
  async update(
    @Param('id') id: Types.ObjectId,
    @Body() updatePwaConfigDto: UpdatePwaConfigDto,
  ) {
    return this.pwaConfigService.update(id, updatePwaConfigDto);
  }

  @Post('admin/pwa-config/presigned')
  @SetMetadata('action', 'generateSignedUploadUrl')
  async generateSignedUploadUrl(
    @Body() generatePresignedUrlDto: GeneratePresignedUrlDto,
  ) {
    return this.pwaConfigService.generateSignedUploadUrl(
      generatePresignedUrlDto,
    );
  }

  @Delete('admin/pwa-config/:id')
  @HttpCode(204)
  @SetMetadata('action', 'delete')
  async delete(@Param('id') id: Types.ObjectId) {
    await this.pwaConfigService.delete(id);
  }
}
