import {
  BranchWithId,
  CompanyDocument,
  Customer,
  DriverDocument,
  OrderDocument,
  OrderPaymentMethod,
  OrderPaymentStatus,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import * as randomestring from 'randomstring';
import { SavedLocationService } from '../../../../location/services/saved-location/saved-location.service';
import { LocationType } from '../../../../shared/enums/location-type.enum';
import { HelperService } from '../../../../shared/services/helper/helper.service';
import { ThirdPartyTaskCreationDto } from '../../../dto/third-party-task-creation.dto';
import {
  CreateBothTookanTaskDto,
  CreateDeliveryTookanTaskDto,
  CreateTookanTaskDto,
} from '../../../dto/tookan.dto';
import { TookanService } from '../../tookan/tookan.service';
import { ThirdPartiesServiceInterface } from '../third-parties.service.interface';

@Injectable()
export class WishboxService implements ThirdPartiesServiceInterface {
  vehicleTypes: string[] = [] as const;
  defaultVehicleType: string = '';
  constructor(
    private configService: ConfigService,
    private savedLocationService: SavedLocationService,
    private helperService: HelperService,
    private tookanService: TookanService,
  ) {}

  async applyPostFunction(taskCreationResponse: any, order: OrderDocument) {
    await this.updateOrder(order);
  }

  async createTask(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
  ): Promise<any> {
    const teamId = this.configService.get('WISHBOX_TEAM_ID');
    return await this.createTookanTask(
      thirdPartyTaskCreationDto.order,
      thirdPartyTaskCreationDto.customer,
      thirdPartyTaskCreationDto.branch,
      thirdPartyTaskCreationDto.company,
      undefined,
      true,
      teamId,
    );
  }

  private async createTookanTask(
    order: OrderDocument,
    customer: Customer,
    branch: BranchWithId,
    company: CompanyDocument,
    driver: DriverDocument,
    customTask = false,
    customTeamId = '',
  ) {
    const deliveryLocation = await this.savedLocationService.getDetails(
      order.deliveryLocationId
        ? order.deliveryLocationId.toHexString()
        : new Types.ObjectId().toHexString(),
    );
    const pickupLocation = await this.savedLocationService.getDetails(
      order.pickupLocationId
        ? order.pickupLocationId.toHexString()
        : new Types.ObjectId().toHexString(),
    );

    if (company.has_delivery_system || customTask) {
      let pickupAddressString = 'Please Contact Branch For Further Information',
        deliveryAddressString =
          'Please Contact Customer For Further Information';

      if (deliveryLocation) {
        deliveryAddressString = this.helperService.convertLocationToString(
          deliveryLocation,
          LocationType.DELIVERY,
        );
      }
      if (pickupLocation) {
        pickupAddressString = this.helperService.convertLocationToString(
          pickupLocation,
          LocationType.PICKUP,
        );
      }

      const createTookanTaskDto: CreateTookanTaskDto = {
        api_key: this.configService.get('TOOKAN_API_KEY'),
        order_id: order.code,
        auto_assignment: order.autoAssign || customTask ? 1 : 0,
        job_description: order.order_remarks,
        team_id: customTask
          ? customTeamId
          : driver
            ? driver.team_id
            : company.tookan_team_id
              ? company.tookan_team_id
              : '1468704',
        fleet_id: driver ? driver.tookan_driver_id : '',
        barcode: order.barCode,
        tags: order.autoAssign ? `${company._id}` : '',
        timezone: moment.tz
          .zone(
            order?.localization?.timezone
              ? order?.localization?.timezone
              : 'Asia/Qatar',
          )
          .utcOffset(moment.utc().valueOf()),
        layout_type: 0,
        custom_field_template: 'Pickup_&_Delivery_Enable',
      };

      const createDeliveryTookanTaskDto: CreateDeliveryTookanTaskDto = {
        ...createTookanTaskDto,
        customer_email:
          customer && customer.email
            ? customer.email
            : 'default' + randomestring.generate(5) + '@e-butler.com',
        customer_username: order.is_gift
          ? order.recipient_name
          : order.customer_name,
        customer_phone: order.is_gift
          ? order.recipient_country_code + order.recipient_phone
          : order.country_code + order.customer_phone,

        customer_address: `${deliveryAddressString} - ${order.code}`,

        latitude: deliveryLocation
          ? deliveryLocation?.latitude?.toString()
          : '0',
        longitude: deliveryLocation
          ? deliveryLocation?.longitude?.toString()
          : '0',

        job_delivery_datetime: moment
          .utc(order.delivery_date)
          .clone()
          .tz(
            order.localization?.timezone
              ? order.localization?.timezone
              : 'Asia/Qatar',
          )
          .toString() as any,

        has_delivery: 1,
        has_pickup: 0,
        layout_type: 0,

        meta_data: this.constructTookanTaskMetadata(order),
      };

      const createBothTookanTaskDto: CreateBothTookanTaskDto = {
        ...createDeliveryTookanTaskDto,
        has_pickup: 1,
        job_pickup_phone: branch ? branch.phone : company.phone,
        job_pickup_name: branch ? branch.name : company.name,
        job_pickup_email: branch ? branch.email : company.email,
        job_pickup_address: `${pickupAddressString} - ${order.code}`,
        job_pickup_latitude: pickupLocation
          ? pickupLocation?.latitude?.toString()
          : '0',
        job_pickup_longitude: pickupLocation
          ? pickupLocation?.longitude?.toString()
          : '0',

        job_pickup_datetime: moment
          .utc(order.pickup_date)
          .clone()
          .tz(
            order.localization?.timezone
              ? order.localization?.timezone
              : 'Asia/Qatar',
          )
          .toString() as any,
        pickup_custom_field_template: 'Pickup_&_Delivery_Enable',
        pickup_meta_data: this.constructTookanTaskMetadata(order),
      };

      const shouldCreatePickupTask = true;

      const taskToCreate = shouldCreatePickupTask
        ? createBothTookanTaskDto
        : createDeliveryTookanTaskDto;

      const created_tookan_task =
        await this.tookanService.createTookanTask(taskToCreate);

      order.tookan_job_id = created_tookan_task['job_id'];

      order.tookan_delivery_track_url =
        created_tookan_task['delivery_tracing_link'] ||
        created_tookan_task['tracking_link'];

      order.tookan_pickup_track_url =
        created_tookan_task['pickup_tracking_link'];

      order.deliveryJobId = created_tookan_task['delivery_job_id']
        ? created_tookan_task['delivery_job_id']
        : created_tookan_task['job_id'];
      order.pickupJobId = created_tookan_task['pickup_job_id'];

      order.deliveryTaskCreated = true;
    }

    order.deliveryTaskCreated = true;
    await order.save();
    return order;
  }

  private constructTookanTaskMetadata(order: OrderDocument) {
    const isPaidAlready =
      order.payment_method === OrderPaymentMethod.online &&
      order.payment_status === OrderPaymentStatus.COMPLETED;

    return [
      {
        label: 'Payment_Amount',
        data: isPaidAlready ? 'Paid Already' : order.total_amount.toString(),
      },
      {
        label: 'Payment_Method',
        data: isPaidAlready
          ? 'Online'
          : this.formatPaymentMethod(order.payment_method),
      },
      {
        label: 'Special_Instruction',
        data: order.order_remarks,
      },
      {
        label: 'Order_Is_Gift',
        data: order.is_gift ? 'Yes' : 'No',
      },
    ];
  }

  private formatPaymentMethod(paymentMethod: OrderPaymentMethod) {
    switch (paymentMethod) {
      // Unsuccesful online payments should be shown as Cash
      case OrderPaymentMethod.online:
      case OrderPaymentMethod.cash:
        return 'Cash';
      case OrderPaymentMethod.card_machine:
        return 'Card Machine';
      case OrderPaymentMethod.prepaid:
        return 'Prepaid';
      default:
        return paymentMethod;
    }
  }

  private async updateOrder(order: OrderDocument) {
    order.assigned_driver_name = 'Wishbox';
    order.driver = undefined;
    order.deliveryTaskCreated = true;
    await order.save();
  }
}
