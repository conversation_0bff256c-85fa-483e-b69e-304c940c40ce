import {
  ArrayTransform,
  BooleanTransform,
  capturedLoyaltyOrderSources,
  CurrentUser,
  DataIndex,
  DeliveryMethod,
  DeliveryThirdPartyName,
  EmbeddedBrandDto,
  ObjectIdTransform,
  OrderDeliveryAction,
  OrderDeliveryType,
  OrderEventEnum,
  OrderPaymentMethod,
  OrderSource,
  OrderStatusEnum,
  SavedLocationToCreate,
  TransportType,
  YesOrNo,
} from '@app/shared-stuff';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsMongoId,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateIf,
} from 'class-validator';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';

export enum OrderOwnDriversAssignmentType {
  'assignNow' = 'assignNow',
  'assignLater' = 'assignLater',
  'autoAssign' = 'autoAssign',
}

export enum FirstPartyTaskState {
  'PENDING' = 'PENDING',
  'PICKUP_STARTED' = 'PICKUP_STARTED',
  'PICKUP_ENDED' = 'PICKUP_ENDED',
  'DELIVERY_STARTED' = 'DELIVERY_STARTED',
  'DELIVERY_ENDED' = 'DELIVERY_ENDED',
}
export class OrderIndex extends DataIndex {
  @ApiProperty({
    required: false,
    type: String,
    enum: [
      'customer_name',
      'customer_phone',
      'order_id',
      'payment_code',
      'branch_name',
      'amount',
      'invoice_number',
      'all',
    ],
  })
  search_type?: string;

  @ApiProperty({
    required: false,
    type: String,
    enum: [
      'customer_name_a_z',
      'customer_name_z_a',
      'agent_a_z',
      'last_contact_newest',
      'last_contact_oldest',
      'source_desc',
      'source_asc',
      'pyment_status',
      'linkid_desc',
      'linkid_asc',
      'amount_desc',
      'amount_asc',
      'order_id_asc',
      'order_id_desc',
      'record_updated',
      'date_created',
      'date_created_asc',
      'status_updated',
      'pickup_date_asc',
      'pickup_date_desc',
      'delivery_date_asc',
      'delivery_date_desc',
    ],
  })
  sort_type?: string;

  @ApiProperty({
    required: false,
    type: String,
    enum: [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
  })
  month?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  year?: string;

  @ApiProperty({
    required: false,
    type: String,
  })
  @ArrayTransform()
  order_status?: string[];

  @ApiProperty({
    required: false,
    type: String,
  })
  @ArrayTransform()
  payment_method?: string[];

  @ApiProperty({
    required: false,
    type: String,
  })
  @ArrayTransform()
  payment_status?: string[];

  @ApiProperty({
    required: false,
    type: String,
    description: 'sperated by ,',
  })
  @ArrayTransform()
  branches?: string[];

  @ApiProperty({
    required: false,
  })
  company?: string;

  @ApiProperty({
    required: false,
  })
  branch?: string;

  @ApiProperty({
    required: false,
  })
  brandId?: string;

  @ApiProperty({
    required: false,
  })
  customer?: string;

  @ApiProperty({
    type: String,
    required: false,
    enum: YesOrNo,
  })
  @BooleanTransform()
  includeAggregatorOrders? = false;

  @ApiProperty({
    type: String,
    required: false,
    enum: YesOrNo,
  })
  @BooleanTransform()
  includeCapturedOrders? = false;

  @ApiProperty({
    type: String,
    required: false,
  })
  prepaidBy?: string;

  @ApiProperty({
    required: false,
    type: String,
    enum: [
      'urgent',
      'all',
      'in_store',
      'unassigned',
      'others',
      'assigned',
      'recently_assigned',
      'today',
      'scheduled',
      'deliverectOrder',
    ],
  })
  delivery_type?: string;

  @ApiProperty({
    required: false,
    type: String,
    description: 'Comma Separated String',
  })
  companyIds?: string;

  filter_branches?: string[];

  traceId?: string;

  @ApiProperty({
    type: String,
    format: 'YYYY-MM-DD',
    required: false,
  })
  createdDateFrom?: string;

  @ApiProperty({
    type: String,
    format: 'YYYY-MM-DD',
    required: false,
  })
  createdDateTo?: string;

  @ApiProperty({
    type: String,
    format: 'YYYY-MM-DD',
    required: false,
  })
  deliveryDateFrom?: string;

  @ApiProperty({
    type: String,
    format: 'YYYY-MM-DD',
    required: false,
  })
  deliveryDateTo?: string;

  @ApiProperty({
    required: false,
    type: String,
    enum: Object.keys(OrderSource),
    description: 'Comma Separated String contains OrderSource',
  })
  source?: string;

  @ApiProperty({
    type: String,
    enum: ['yes', 'no'],
    required: false,
  })
  deliverectPosOrderSnooze?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  acknowledgedBy?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  createdBy?: string;

  includedItems?: boolean = false;
}

export class OrderStatusToUpdate {
  @ApiProperty({
    required: true,
  })
  _id: string;

  @ApiProperty({
    required: true,
    type: String,
    enum: Object.keys(OrderStatusEnum),
  })
  status: OrderStatusEnum;

  @ApiProperty({
    required: true,
    type: String,
  })
  comment: string;

  @ApiProperty({
    required: false,
    type: String,
  })
  branch: string;

  traceId: string;
}

export class OrderToAssign {
  @ApiProperty({
    type: String,
    required: true,
  })
  @ObjectIdTransform()
  order_id: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: true,
  })
  @ObjectIdTransform()
  branch: Types.ObjectId;

  @ApiProperty({
    required: true,
    type: String,
  })
  comment: string;

  traceId: string;
}

export class OrderToReject {
  @ApiProperty({
    type: String,
    required: true,
  })
  @ObjectIdTransform()
  order_id: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: true,
  })
  @ObjectIdTransform()
  branch: Types.ObjectId;

  @ApiProperty({
    required: true,
    type: String,
  })
  comment: string;

  traceId: string;
  currentUser: CurrentUser;
}

export class OrderToCancel {
  @ApiProperty({
    type: String,
    required: true,
  })
  @ObjectIdTransform()
  order_id: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: false,
  })
  orderRef?: string;

  @ApiProperty({
    required: true,
    type: String,
  })
  comment: string;

  currentUser: CurrentUser;

  traceId: string;
}

export class OrderDeliveryAddressToUpdate {
  @ApiProperty({
    type: String,
    required: true,
  })
  order_id: string;

  @ApiProperty({
    type: () => SavedLocationToCreate,
    required: false,
  })
  @Type(() => SavedLocationToCreate)
  location?: SavedLocationToCreate;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  locationId?: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: false,
  })
  delivery_date?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  delivery_slot_id?: string;

  send_sms?: boolean;
  current_user?: CurrentUser;

  traceId?: string;
}

export class OrderDeliverySmsToSend {
  @ApiProperty({
    type: String,
    required: true,
  })
  order: string;

  company: string;
}

export class OrderDetailsToEdit {
  @ApiProperty({
    type: String,
    enum: [...Object.keys(OrderPaymentMethod)],
    required: true,
  })
  payment_method: OrderPaymentMethod;

  @ApiProperty({
    type: Number,
    required: true,
  })
  delivery_amount = 0;

  @ApiProperty({
    type: Number,
    required: true,
  })
  invoiced_amount = 0;

  @ApiProperty({
    type: Number,
    required: true,
  })
  total_amount = 0;

  @ApiProperty({
    type: String,
  })
  invoice_number: string;

  @ApiProperty({
    required: true,
    type: String,
    enum: OrderSource,
  })
  source: OrderSource;

  @ApiProperty({
    type: String,
  })
  order_remarks: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  order: string;

  @ApiProperty({
    type: Boolean,
    required: false,
  })
  is_gift: boolean;

  @ApiProperty({
    type: Boolean,
    required: false,
  })
  is_secret: boolean;

  @ApiProperty({
    type: String,
    required: true,
  })
  recipient_name: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  recipient_phone: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  recipient_country_code = '+974';

  @ApiProperty({
    type: String,
    required: false,
  })
  cardMessage: string;

  current_user: CurrentUser;
  traceId: string;
  total_discount: number;
  total_amount_after_discount: number;
}

export class OrderDispatchToEdit {
  @ApiProperty({
    type: String,
    required: false,
    enum: DeliveryMethod,
  })
  deliveryMethod: DeliveryMethod;

  @ApiProperty({
    type: String,
    enum: [...Object.keys(TransportType)],
    required: false,
  })
  transport_type: string;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  delivery_slot_id: Types.ObjectId;

  @ApiProperty({
    required: false,
    type: String,
    description: "Format 'YYYY-MM-DD' in UTC timezone.",
  })
  delivery_date: string;

  @ApiProperty({
    required: false,
    type: String,
    description: "Format 'HH:mm' in UTC timezone.",
  })
  delivery_time: string;

  @ApiProperty({
    required: false,
    type: String,
    description: "Format 'YYYY-MM-DD' in UTC timezone.",
  })
  pickup_date: string;

  @ApiProperty({
    required: false,
    type: String,
    description: "Format 'HH:mm' in UTC timezone.",
  })
  pickup_time: string;

  @ApiProperty({
    type: String,
    required: false,
    enum: [...Object.keys(OrderDeliveryType)],
  })
  delivery_type: OrderDeliveryType;

  @ApiProperty({
    type: String,
    required: false,
  })
  delivery_action: OrderDeliveryAction;

  @ApiProperty({
    type: String,
    required: false,
  })
  driver_id: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  order: string;

  current_user: CurrentUser;
  traceId: string;
}

export class DriverAssignToOrder {
  @ApiProperty({
    type: String,
    required: true,
  })
  driver: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  order: Types.ObjectId;

  traceId: string;
}

export class OrderItemsToEdit {}

export class OrderWarehouseToAssign {
  @ApiProperty({
    type: [String],
    required: true,
  })
  warehouses: string[];

  @ApiProperty({
    type: String,
    required: true,
  })
  order: string;

  currentUser: CurrentUser;
  traceId: string;
}

export const OrderSortMapping = {
  customer_name_a_z: { customer_name: 1 },
  customer_name_z_a: { customer_name: -1 },
  agent_a_z: { customer_name: 1 },
  last_contact_newest: { last_contact: 1 },
  last_contact_oldest: { last_contact: -1 },
  pyment_status: { payment_status: 1 },
  linkid_desc: { payment_link_id: -1 },
  linkid_asc: { payment_link_id: 1 },
  amount_desc: { total_amount: -1 },
  amount_asc: { total_amount: 1 },
  order_id_desc: { code: -1 },
  order_id_asc: { code: 1 },
  date_created: { createdAt: -1 },
  date_created_asc: { createdAt: 1 },
  record_updated: { updatedAt: -1 },
  status_updated: { last_status_update: -1 },
  pickup_date_asc: { pickup_date: 1 },
  pickup_date_desc: { pickup_date: -1 },
  delivery_date_asc: { delivery_date: 1 },
  delivery_date_desc: { delivery_date: -1 },
  source_desc: { source: -1 },
  source_asc: { source: 1 },
};

export const OrderSearchMapping = (search_type: string, search_key: string) => {
  const pattern = `.*${search_key.toLowerCase()}.*`;

  const mapping: Record<string, any> = {
    customer_name: { input: '$customer_name' },
    customer_phone: { input: '$customer_phone' },
    order_id: { input: '$code' },
    branch_name: { input: '$branch.name' },
    amount: { input: '$total_amount' },
    invoice_number: { input: '$invoice_number' },
  };

  if (!mapping[search_type]) {
    mapping[search_type] = { input: '$customer_name' };
  }

  mapping[search_type].regex = pattern;
  mapping[search_type].options = 'i';

  return mapping[search_type];
};

export const OrderDeliveryTypeMapping = (deliveryType) => {
  const start = moment().subtract(1, 'day').toDate();
  const today = moment.utc().startOf('day').toDate();
  const todayEnd = moment.utc().endOf('day').toDate();

  const capturedLoyaltyOrderFilter = {
    $or: [
      { isAggregator: false },
      { source: { $nin: capturedLoyaltyOrderSources } },
    ],
  };
  const mapping = {
    urgent: {
      delivery_type: {
        $in: [OrderDeliveryType.urgent],
        $exists: true,
      },
      ...capturedLoyaltyOrderFilter,
    },
    deliverectOrder: {
      deliverectPosOrderId: { $exists: true },
      ...capturedLoyaltyOrderFilter,
    },
    in_store: {
      delivery_action: {
        $in: [OrderDeliveryAction.IN_STORE_PICKUP],
        $exists: true,
      },
      ...capturedLoyaltyOrderFilter,
    },
    unassigned: {
      branch: null,
      branch_object: null,
      ...capturedLoyaltyOrderFilter,
    },
    others: {
      delivery_action: {
        $in: [OrderDeliveryAction.DELIVERY_LOCATION],
        $exists: true,
      },
      ...capturedLoyaltyOrderFilter,
    },
    recently_assigned: {
      assignedAt: { $ne: null, $exists: true, $gt: start },
      ...capturedLoyaltyOrderFilter,
    },
    assigned: {
      branch_object: { $exists: true },
    },
    today: {
      $or: [
        {
          delivery_date: {
            $ne: null,
            $exists: true,
            $gt: today,
            $lt: todayEnd,
          },
        },
        {
          pickup_date: {
            $ne: null,
            $exists: true,
            $gt: today,
            $lt: todayEnd,
          },
        },
      ],
      ...capturedLoyaltyOrderFilter,
    },
    scheduled: {
      delivery_type: OrderDeliveryType.scheduled,
      ...capturedLoyaltyOrderFilter,
    },
    gift: {
      is_gift: true,
      ...capturedLoyaltyOrderFilter,
    },
    all: {},
  };

  return mapping[deliveryType];
};

export class OrderToAcknowledge {
  @ApiProperty({
    type: Boolean,
    default: false,
    required: false,
  })
  @IsNotEmpty()
  isAcknowledged: boolean;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  order: string;

  currentUser: CurrentUser;

  traceId: string;
}

export class OrderPaymentMethodToChange {
  @ApiProperty({
    type: String,
    enum: [...Object.keys(OrderPaymentMethod)],
    required: true,
  })
  @IsNotEmpty()
  paymentMethod: OrderPaymentMethod;

  @ApiProperty({
    type: String,
    description: 'ID of the Order',
  })
  @IsNotEmpty()
  @IsMongoId()
  orderId: Types.ObjectId;

  @ApiProperty({
    type: String,
    description: 'ID of the Company',
  })
  @IsNotEmpty()
  @IsMongoId()
  companyId: Types.ObjectId;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsNotEmpty()
  @ValidateIf(
    ({ paymentMethod }) => paymentMethod === OrderPaymentMethod.prepaid,
  )
  prepaidBy?: string;

  traceId: string;

  currentUser: CurrentUser;
}

export class AcknowledgeOrderDto {
  @ApiProperty({
    type: String,
    description: 'ID of the Order',
  })
  @IsNotEmpty()
  @IsMongoId()
  orderId: Types.ObjectId;

  @ApiProperty({
    type: Number,
    required: false,
    description: 'Ready Within (Minutes)',
    default: 0,
  })
  preparationTime = 0;

  @ApiProperty({
    type: String,
    enum: [...Object.keys(OrderDeliveryType)],
    required: false,
    description: 'Delivery Type it can be: urgent, in_store_pickup',
  })
  deliveryType?: OrderDeliveryType;

  @ApiProperty({
    type: String,
    required: false,
    enum: DeliveryMethod,
    description:
      'The Order Delivery Method can be : COMPANY_DRIVERS , BRANCH_DRIVERS, THIRD_PARTY , E_BUTLER',
  })
  deliveryMethod?: DeliveryMethod;

  @ApiProperty({
    type: String,
    required: false,
    description:
      'The Order Third Party it can be: bee, mrDelivery, clicks, deliveryHub',
  })
  thirdParty?: DeliveryThirdPartyName;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  vehicleType?: string;

  // Own Drivers Data
  @ApiProperty({
    type: String,
    required: false,
    description:
      'The Type of the driver assignment: it can be assignNow, assignLater, autoAssign',
  })
  assignmentType?: OrderOwnDriversAssignmentType;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsOptional()
  @IsMongoId()
  driverId?: Types.ObjectId;

  traceId: string;
  currentUser: CurrentUser;
}

export class OrderDriverToChange {
  @ApiProperty({
    type: String,
    description: 'ID of the Order',
  })
  @ObjectIdTransform()
  orderId: Types.ObjectId;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsOptional()
  @IsMongoId()
  driverId: Types.ObjectId;

  @ApiProperty({
    type: String,
    enum: [...Object.keys(OrderDeliveryType)],
    required: true,
    description: 'Delivery Type it can be: urgent, in_store_pickup',
  })
  deliveryType: OrderDeliveryType;

  @ApiProperty({
    type: String,
    required: false,
    enum: DeliveryMethod,
    description:
      'The Order Delivery Method can be : COMPANY_DRIVERS , BRANCH_DRIVERS, THIRD_PARTY , E_BUTLER',
  })
  deliveryMethod?: DeliveryMethod;

  @ApiProperty({
    type: String,
    required: false,
    description:
      'The Order Third Party it can be: bee, mrDelivery, clicks, deliveryHub',
  })
  thirdParty: DeliveryThirdPartyName;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  vehicleType?: string;

  @ApiProperty({
    type: String,
    required: false,
    description:
      'The Type of the driver assignment: it can be assignNow, assignLater, autoAssign',
  })
  assignmentType: OrderOwnDriversAssignmentType;

  traceId: string;
}

export class TrackingOrderDto {
  orderId: Types.ObjectId;
  orderCode: string;
  invoiceNumber: string;
  trackingLink: string;
  paymentLink: string;
  customerName: string;
  brand: EmbeddedBrandDto;
  orderStatus: string;
  orderEvent: OrderEventDto;
  statuses: string[];
  deliveryDate: Date;
  locationLink: string;
}

export class OrderEventDto {
  name: OrderEventEnum;
  description: string;
  iconName: string;
  isViewableStepper: boolean;
}
