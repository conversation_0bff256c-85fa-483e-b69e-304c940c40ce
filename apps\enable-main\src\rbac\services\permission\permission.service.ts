import { CollectionName } from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import * as xl from 'excel4node';
import { Model } from 'mongoose';
import {
  PermissionToCreate,
  PermissionToIndx,
  PermissionToUpdate,
} from '../../../rbac/dto/permission.dto';
import { PermissionDocument } from '../../../rbac/models/permission.model';
import { RoleService } from '../role/role.service';
import { GoogleCloudStorageService } from '../../../storage/google-cloud-storage.service';

@Injectable()
export class PermissionService {
  constructor(
    @InjectModel(CollectionName.PERMISSIONS)
    private permissionModel: Model<PermissionDocument>,
    private roleService: RoleService,
    private googleCloudStorageService: GoogleCloudStorageService,
  ) {}

  async index(filterData: PermissionToIndx) {
    let permissions = this.permissionModel.find();
    if (filterData.limit && filterData.limit) {
      permissions = permissions
        .skip(filterData.limit * filterData.offset)
        .limit(filterData.limit);
    }
    if (filterData.search_key) {
      permissions = permissions.find({
        name: { $regex: new RegExp(`.*${filterData.search_key}.*`) },
      });
    }

    if (filterData.user_data) {
      const permissions_ids = await this.build_permission(
        filterData.user_data['roles'],
      );

      permissions = permissions.find({ _id: { $in: permissions_ids } });
    }

    return await permissions.exec();
  }

  private async build_permission(roles) {
    const permissions = [];
    // Logger.log(roles);
    for (let i = 0; i < roles.length; i++) {
      for (let j = 0; j < roles[i]['permissions'].length; j++) {
        const permi_id = roles[i]['permissions'][j]['_id']
          ? roles[i]['permissions'][j]['_id']
          : roles[i]['permissions'][j];
        permissions.push(permi_id);
      }
    }
    return permissions;
  }
  async get_details(id) {
    const selectedPermssion = await this.permissionModel.findById(id);
    return selectedPermssion;
  }

  async create(permToCreate: PermissionToCreate) {
    const createdPermission = new this.permissionModel(permToCreate);
    await createdPermission.save();
    await this.roleService.addToSuperAdmin(createdPermission);
    return createdPermission;
  }

  async update(permToUpdate: PermissionToUpdate) {
    const selectedPermssion = await this.permissionModel.findByIdAndUpdate(
      permToUpdate._id,
      permToUpdate,
      { new: true },
    );
    return selectedPermssion;
  }

  async remove(id) {
    return await this.permissionModel.findByIdAndRemove(id);
  }

  async findOrCreate(module: string, action: string) {
    const permission = await this.permissionModel
      .findOne({ module, action })
      .exec();
    if (permission) return permission;

    return await this.create({
      action,
      module,
      route: `${module}/${action}`,
      name: `${action} ${module}`,
    });
  }
  async export_excel() {
    const wb = new xl.Workbook();
    const ws = wb.addWorksheet('Permissions');
    const permissions = await this.permissionModel.find().sort({ module: -1 });

    const headers = ['Id', 'Name', 'Action', 'Module', 'Route', 'Created At'];
    const style = wb.createStyle({
      font: {
        color: '#FF0800',
        size: 12,
      },
      numberFormat: '$#,##0.00; ($#,##0.00); -',
    });
    for (let i = 1; i <= headers.length; i++) {
      ws.cell(1, i)
        .string(headers[i - 1])
        .style(style);
    }
    for (let i = 0; i < permissions.length; i++) {
      ws.cell(i + 2, 1).string(permissions[i]['_id'].toString());
      ws.cell(i + 2, 2).string(permissions[i]['name'].toString());
      ws.cell(i + 2, 3).string(permissions[i]['action'].toString());
      ws.cell(i + 2, 4).string(permissions[i]['module'].toString());
      ws.cell(i + 2, 5).string(permissions[i]['route'].toString());
      ws.cell(i + 2, 6).string(permissions[i]['createdAt'].toString());
    }
    const fileName =
      'permissions-' + Math.random().toString(36).substring(7) + '.xlsx';

    await this.googleCloudStorageService.uploadDocument(
      await wb.writeToBuffer(),
      fileName,
    );

    return `/documents/${fileName}`;
  }
}
