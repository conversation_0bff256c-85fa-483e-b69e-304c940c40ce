import {
  BranchToCreate,
  BranchToIndex,
  responseCode,
  UpdateBranchDto,
  UpdateBusyBranchDto,
  UserBranchesToAssign,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  SetMetadata,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { HelperService } from '../../../shared/services/helper/helper.service';
import { UsersToAssignToBranch } from '../../../user/dto/user.dto';
import { BranchService } from '../../services/branch/branch.service';

@Controller('branch')
@ApiTags('Branch')
@SetMetadata('module', 'branch')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class BranchController {
  constructor(
    private branchService: BranchService,
    private helperService: HelperService,
  ) {}

  @Get()
  @SetMetadata('action', 'get_all')
  async get_all(
    @Query() branchToIndex: BranchToIndex,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      branchToIndex.company = req['company_id']
        ? req['company_id']
        : branchToIndex.company;
      branchToIndex.branches = req['branches'] ? req['branches'] : '';
      const selectedBranches = await this.branchService.index(branchToIndex);
      const total_branches =
        await this.branchService.get_total_branches(branchToIndex);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to get branches',
        { branches: selectedBranches, counts: [], total_branches },
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() branchToCreate: BranchToCreate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      branchToCreate.company = req['company_id']
        ? req['company_id']
        : branchToCreate.company;
      branchToCreate.current_user = req['current'];

      const createdBranch = await this.branchService.create(branchToCreate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to create branch',
        createdBranch,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Put(':id')
  @SetMetadata('action', 'update')
  async update(
    @Body() updateBranchDto: UpdateBranchDto,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      updateBranchDto.current_user = req['current'];
      updateBranchDto.company = req['company_id'];
      const updatedBranch = await this.branchService.update(
        updateBranchDto,
        req['company_id'],
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'success to update this branch',
        updatedBranch,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Delete(':id')
  @SetMetadata('action', 'delete')
  async remove(
    @Param('id') id: string,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const removedBranch = await this.branchService.delete(id, req['current']);

      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_REMOVE,
        'Success to remove branch',
        removedBranch,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('multiple/assign')
  @SetMetadata('action', 'multiple_branches_assign')
  async assignMultipleBranchesToUser(
    @Body() userBranchesToAssign: UserBranchesToAssign,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      const user =
        await this.branchService.assignBranchesToUser(userBranchesToAssign);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'Success to adding branches to user',
        user,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  async get_details(@Param('id') id: string, @Res() res: Response) {
    try {
      const BranchDetails = await this.branchService.get_details(id);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'Success to get branch details',
        BranchDetails,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('user/assgin')
  @SetMetadata('action', 'assign_users_to_branch')
  async assign_users_to_branch(
    @Req() req: Request,
    @Res() res: Response,
    @Body() userstoAssingToBranch: UsersToAssignToBranch,
  ) {
    try {
      const branch = await this.branchService.assignUsersIntoABranch(
        userstoAssingToBranch,
        req['company_id'],
      );

      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'Success To ASsign USers to branch',
        branch,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Put('update/isBusy')
  @SetMetadata('action', 'isBusy-update')
  async updateIsBusyBranch(@Body() updateBusyBranchDto: UpdateBusyBranchDto) {
    try {
      return await this.branchService.updateIsBusyBranch(updateBusyBranchDto);
    } catch (err) {
      return this.helperService.handelError(err, undefined);
    }
  }
}
