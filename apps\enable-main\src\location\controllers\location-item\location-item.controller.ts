import {
  CountriesNameToIsoCodeMapping,
  CreateLocationItemDto,
  GenericExceptionFilter,
  GetAllLocationItemDto,
  SingleIdDto,
  TransformInterceptor,
  UpdateLocationItemDto,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  Param,
  Patch,
  Post,
  Query,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { LocationItemServiceInterface } from '../../services/location-item/location-item-service.interface';

@Controller('location-item')
@ApiTags('Location Item')
@SetMetadata('module', 'location-item')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
export class LocationItemController {
  constructor(
    @Inject(LocationItemServiceInterface)
    private locationItemService: LocationItemServiceInterface,
  ) {}

  @Get()
  @SetMetadata('action', 'findAll')
  async findAll(@Query() getAllLocationItemDto: GetAllLocationItemDto) {
    const result = await this.locationItemService.findAll(
      getAllLocationItemDto,
    );
    return {
      locationItems: result[0].paginatedResult,
      totalLocationItems: result[0].totalCount[0]?.createdAt,
    };
  }

  @Get('public/all')
  @SetMetadata('public', 'true')
  async findAllPublic(@Query() getAllLocationItemDto: GetAllLocationItemDto) {
    const result = await this.locationItemService.findAll(
      getAllLocationItemDto,
    );
    return {
      locationItems: result[0].paginatedResult,
      totalLocationItems: result[0].totalCount[0]?.createdAt,
    };
  }

  @Get('public/all-countries')
  @SetMetadata('public', 'true')
  async getAllStaticCountries() {
    const countriesArray = Object.entries(CountriesNameToIsoCodeMapping).map(
      ([countryName, isoCode]) => {
        return { countryName, isoCode };
      },
    );
    return countriesArray;
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(@Body() createLocationItemDto: CreateLocationItemDto) {
    return await this.locationItemService.create(createLocationItemDto);
  }

  @Patch()
  @SetMetadata('action', 'update')
  async update(@Body() updateLocationItemDto: UpdateLocationItemDto) {
    return await this.locationItemService.update(updateLocationItemDto);
  }

  @Get(':id')
  @SetMetadata('action', 'findOne')
  async findOne(@Param() singleIdDto: SingleIdDto) {
    return await this.locationItemService.findOne(singleIdDto.id);
  }

  @Delete(':id')
  @SetMetadata('action', 'delete')
  async delete(@Param() singleIdDto: SingleIdDto) {
    await this.locationItemService.delete(singleIdDto.id);
  }
}
