import {
  CurrentUser,
  LoggerService,
  OrderDocument,
  OrderLogActionEnum,
  OrderStatusEnum,
  OrderTransitionTrigger,
  responseCode,
} from '@app/shared-stuff';
import { BadRequestException, Inject } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Types } from 'mongoose';
import { UserService } from '../../../../user/services/user/user.service';
import { OrderLogServiceInterface } from '../../interfaces/order-log.service.interface';
import { OrderNotificationService } from '../../order-notification/order-notification.service';
import { OrderStatusServiceInterface } from '../order-status.interface';

export class PreparingOrderService implements OrderStatusServiceInterface {
  private readonly loggerService = new LoggerService(
    PreparingOrderService.name,
  );
  transitionalStatuses = [OrderStatusEnum.PENDING, OrderStatusEnum.SCHEDULED];
  transitionalTrigger = [
    OrderTransitionTrigger.ACKNOWLEDGED,
    OrderTransitionTrigger.AUTOMATIC_ACKNOWLEDGEMENT,
    OrderTransitionTrigger.DELIVERECT_STATUS_UPDATE,
  ];

  constructor(
    @Inject('OrderLogServiceInterface')
    private readonly orderLogService: OrderLogServiceInterface,
    private userService: UserService,
    private orderNotificationService: OrderNotificationService,
    private eventEmitter: EventEmitter2,
  ) {}

  validateStatus(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
  ): boolean {
    return (
      this.ValidateTransition(order, orderTransitionTrigger) &&
      this.validatePreCondition(order)
    );
  }

  private ValidateTransition(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
  ): boolean {
    if (
      this.transitionalTrigger.includes(orderTransitionTrigger) ||
      this.transitionalStatuses.includes(order.status)
    )
      return true;

    throw new BadRequestException(
      "Can't Change From " + order.status + ' To Preparing',
      responseCode.STATUS_NOT_VALID.toString(),
    );
  }

  private validatePreCondition(order: OrderDocument): boolean {
    if (!order.branch)
      throw new BadRequestException(
        "Can't change From " +
          order.status +
          ' to  Preparing' +
          'The  the branch manager must accepts the order with ID ' +
          order._id,
        responseCode.STATUS_NOT_VALID.toString(),
      );
    return true;
  }

  async applyPostFunction(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
    oldStatus: OrderStatusEnum,
    user: CurrentUser,
  ) {
    try {
      this.loggerService.log(
        'Apply Preparing Post Functions with Order details before order.acknowledged event : ',
        order,
      );
      this.eventEmitter.emit('order.acknowledged', order);
      this.loggerService.log(
        'Apply Preparing Post Functions with Order details After order.acknowledged event : ',
        order,
      );
      await this.orderNotificationService.onOrderNotificationAcknowledge(order);
      await this.userService.notifyUsersAboutOrderAcknowledged(
        new Types.ObjectId(order._id),
      );
      await this.userService.removeAcknowledgeOrdersFromUsers(
        order,
        'acknowledged',
      );
      this.loggerService.log(
        'Apply Preparing Post Functions with Order details before save order log in the try block : ',
        order,
      );
      await this.orderLogService.saveOrderLog(
        order,
        { oldStatus: oldStatus },
        {
          newStatus: OrderStatusEnum.PREPARING,
          trigger: orderTransitionTrigger,
        },
        OrderLogActionEnum.ON_ORDER_NOTIFICATION_ACKNOWLEDGE,
        user,
      );
    } catch (exception: any) {
      this.loggerService.error(
        'Apply Preparing Post Functions with Order details inside catch block : ',
        {
          newStatus: OrderStatusEnum.PREPARING,
          trigger: orderTransitionTrigger,
          responseObject: exception,
          order,
        },
      );
      await this.orderLogService.saveOrderLog(
        order,
        { oldStatus: oldStatus },
        {
          newStatus: OrderStatusEnum.PREPARING,
          trigger: orderTransitionTrigger,
          responseObject: exception,
        },
        OrderLogActionEnum.ON_ORDER_NOTIFICATION_ACKNOWLEDGE,
        user,
      );
    }
  }
}
