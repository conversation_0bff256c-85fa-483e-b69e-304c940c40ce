//
db.triggers.insert({
  name: '[PASS] ON_WALLET_PASS_ADDED',
  client: 'ENABLE_MAIN',
  action: 'ON_WALLET_PASS_ADDED',
  module: 'PASS',
  replacement: [
    'firstName',
    'fullName',
    'loyaltyPointBalance',
    'loyaltyTier',
    'upperLoyaltyTier',
    'lowerTier',
    'remainingOrdersCurrentTier',
    'remainingOrdersUpperTier',
    'remainingRequirementsUpperTier',
    'remainingRequirementsCurrentTier',
    'upperTierDiscountValue',
    'tierDiscountValue',
    'tierDiscountOrderValueThreshold',
    'upperTierDiscountValueNumberOfUses',
    'tierDiscountValueNumberOfUses',
    'upperTierFreeDeliveryNumberOfUses',
    'tierFreeDeliveryNumberOfUses',
    'tierFreeDeliveryRemainingNumberOfUses',
    'tierDiscountValueRemainingNumberOfUses',
    'loyaltyRegistrationPageLink',
    'walletPassAccessPageLink',
    'walletPassLink',
    'googleWalletPassLink',
    'ordableLink',
    'gracePeriodRemainingDays',
    'firstGracePeriodReminderDate',
    'validTill',
    'totalLoyaltyOrderRate',
    'totalLoyaltyAmountSpent',
    'nextComputationDate',
    'currentTierRequirementThresholdsPercent',
    'upperTierRequirementThresholdsPercent',
    'nextPunchCardAchievementRequirements',
    'nextPunchCardAchievementBenefits',
    'remainingPointsNextCoupon',
    'nextCouponBenefit',
    'remainingAmountSpentCurrentTier',
    'remainingAmountSpentUpperTier',
    'remainingPointsCurrentTier',
    'remainingPointsUpperTier',
    'highestUnlockedCouponBenefit',
    'nextPunchCardAchievementRequirementsFirstTrack',
    'nextPunchCardAchievementRequirementsSecondTrack',
    'punchCardFirstTrackName',
    'punchCardSecondTrackName',
    'nextPunchCardAchievementBenefitsFirstTrack',
    'nextPunchCardAchievementBenefitsSecondTrack',
    'tierCustomBenefitsList',
  ],
  createdAt: new Date(),
  updatedAt: new Date(),
});
