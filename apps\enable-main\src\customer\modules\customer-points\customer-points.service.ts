import {
  CompanyDocument,
  CustomerDocument,
  EmbeddedOrderDto,
  OrderDocument,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';

@Injectable()
export class CustomerPointsService {
  constructor() {}

  public calculatePointsEarnedOnOrder(
    order: OrderDocument | EmbeddedOrderDto,
    customer: CustomerDocument,
    company: CompanyDocument,
  ): number {
    const exchangeRates = company.loyaltyProgramConfig?.earningExchangeRates;
    if (!exchangeRates) return 0;

    const tierId = customer.loyaltyTier?._id.toString();
    const exchangeRate =
      exchangeRates.tierExchangeRates &&
      tierId &&
      tierId in exchangeRates.tierExchangeRates
        ? exchangeRates.tierExchangeRates[tierId]
        : exchangeRates.global;

    const orderValue = Math.max(0, order.total_amount);
    return orderValue * exchangeRate;
  }
}
