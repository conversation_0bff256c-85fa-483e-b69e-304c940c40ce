// EBL-3925 [Loyalty Program] Loyalty New Customers - Grace Period v1.3
// Init firstTierEarnedAt based on loyalty tier logs.

const customerIds = [];
const writes = db.loyaltytierlogs
  .find(
    { action: { $in: ['UPGRADE', 'ASSIGN'] } },
    {
      customerId: 1,
      createdAt: 1,
    },
  )
  .sort({ createdAt: 1 })
  .map(({ customerId, createdAt }) => ({
    updateOne: {
      filter: { _id: customerId },
      update: { $set: { firstTierEarnedAt: createdAt } },
    },
  }))
  .toArray()
  .reduce((arr, curr) => {
    if (!customerIds.includes(curr.updateOne.filter._id)) {
      customerIds.push(curr.updateOne.filter._id);
      arr.push(curr);
    }
    return arr;
  }, []);

db.customers.bulkWrite(writes, { ordered: false });
