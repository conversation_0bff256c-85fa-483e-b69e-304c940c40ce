import { MailgunService } from './../integrations/mailgun/mailgun.service';
import { EmailIntegrationsServiceInterface } from './../integrations/email-intergrations.service.interface';
import { EmailRepositoryInterface } from './../../repositories/email.repository.interface';
import { EmailSendDto } from './../../types/dtos/email-send.dto';
import { EmailServiceInterface } from './email.service.interface';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigurationDocument } from '../../../configuration/models/configuration.model';
import { EmailProvider, LoggerService } from '@app/shared-stuff';

@Injectable()
export class EmailService implements EmailServiceInterface {
  private readonly loggerService = new LoggerService(EmailService.name);
  private emailMap: Map<EmailProvider, EmailIntegrationsServiceInterface>;

  constructor(
    @Inject('EmailRepositoryInterface')
    private readonly emailRepository: EmailRepositoryInterface,
  ) {
    this.initEmailMap();
  }

  async send(emailSendDto: EmailSendDto, configuration: ConfigurationDocument) {
    try {
      const emailResponse = await this.emailMap
        .get(configuration.emailConfig.provider)
        .send(emailSendDto, configuration.emailConfig);
      await this.createEmailLog(emailResponse, emailSendDto);
    } catch (error) {
      this.loggerService.error(
        error.message + 'Error while send an email',
        error.stacktrace,
        emailSendDto,
      );
    }
  }

  private async createEmailLog(emailResponse: any, emailSendDto: EmailSendDto) {
    await this.emailRepository.create({
      from: emailSendDto.from,
      cc: emailSendDto.cc,
      bcc: emailSendDto.bcc,
      to: emailSendDto.to,
      content: emailSendDto.content,
      subject: emailSendDto.subject,
      rawResponse: emailResponse,
    });
  }

  private initEmailMap() {
    this.emailMap = new Map<EmailProvider, EmailIntegrationsServiceInterface>([
      [EmailProvider.MAIL_GUN, new MailgunService()],
    ]);
  }
}
