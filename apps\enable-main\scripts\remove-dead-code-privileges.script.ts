// EBL-5197 Dead Code Removal
// Remove dead pruvileges and permissions

const deadPrivileges = [
  { module: 'warehouse' },
  { module: 'orders', value: 'Assign Warehouse' },
  { module: 'company', value: 'edit company documents' },
  { module: 'company', value: 'edit working hours' },
];

const privilegeIds = db.privileges
  .find({ $or: deadPrivileges }, { _id: 1 })
  .map((p) => p._id)
  .toArray();

db.privileges.deleteMany({ _id: { $in: privilegeIds } });
db.roles.updateMany(
  {},
  { $pull: { privileges: { _id: { $in: privilegeIds } } } },
);

const deadPermissions = [
  { module: 'workinghours' },
  { module: 'email' },
  { module: 'email_template' },
  { module: 'sms' },
  { module: 'sms-template' },
  { module: 'warehouse' },
  { module: 'inventoryProduct' },
  { module: 'inventoryCategory' },
  { module: 'inventoryDiscount' },
  { module: 'inventoryBrand' },
  { action: 'assign_warehouses_to_order' },
  { action: 'upload_company_document' },
  { action: 'delete_company_document' },
  { action: 'get_company_document' },
];

const permissionIds = db.permissions
  .find({ $or: deadPermissions }, { _id: 1 })
  .map((p) => p._id)
  .toArray();

db.permissions.deleteMany({ _id: { $in: permissionIds } });
db.roles.updateMany({}, { $pull: { permissions: { $in: permissionIds } } });
