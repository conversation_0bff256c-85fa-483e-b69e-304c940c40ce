import { responseCode } from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  SetMetadata,
  UploadedFile,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiTags,
} from '@nestjs/swagger';
import { Request, Response } from 'express';
import { HelperService } from './../../../shared/services/helper/helper.service';
import {
  DriverBranchToAssign,
  DriverBranchToRemove,
  DriverExcelToImport,
  DriverToCreate,
  DriverToIndex,
  DriverToUpdate,
  DriverUsernameToGet,
} from './../../dto/driver.dto';
import { DriverService } from './../../services/driver/driver.service';

@Controller('driver')
@ApiTags('Driver')
@SetMetadata('module', 'driver')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class DriverController {
  constructor(
    private driverService: DriverService,
    private helperService: HelperService,
  ) {}

  @Get()
  @SetMetadata('action', 'get_all')
  async index(
    @Query() driverToIndex: DriverToIndex,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      driverToIndex.company = req['company_id'] ? req['company_id'] : '';
      // Logger.log(driverToIndex);
      const drivers = await this.driverService.index(driverToIndex);
      const total_drivers =
        await this.driverService.get_total_drivers(driverToIndex);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to get all drivers',
        { drivers: drivers, total_drivers: total_drivers, counts: [] },
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get('username')
  @SetMetadata('action', 'get_next_driver_username')
  async nextDriverUsername(
    @Query() driverUsernameToGet: DriverUsernameToGet,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      const company_id = req['company_id'] ? req['company_id'] : '';
      const driver_username =
        await this.driverService.getNextDriverUsername(driverUsernameToGet);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to get driver username',
        { username: driver_username },
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  async get_details(
    @Param('id') id: string,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const driver = await this.driverService.get_details(id);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'success to get driver details',
        driver,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() driverToCreate: DriverToCreate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      driverToCreate.company = req['company_id'] ? req['company_id'] : '';
      const driver = await this.driverService.create(
        driverToCreate,
        req['current'],
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success to create driver',
        driver,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
  @Put(':id')
  @SetMetadata('action', 'update')
  @UsePipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
    }),
  )
  async update(
    @Body() driverToUpdate: DriverToUpdate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const driver = await this.driverService.update(
        driverToUpdate,
        req['current'],
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'success to update driver',
        driver,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Delete(':id')
  @SetMetadata('action', 'update')
  async delete(
    @Param('id') id: string,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      // driverToCreate.company = req['company_id'] ? req['company_id'] : '';
      const driver = await this.driverService.remove(id, req['current']);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_REMOVE,
        'success to delete driver',
        driver,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('branch/assign')
  @SetMetadata('action', 'assign_driver_to_branch')
  async assign_to_branch(
    @Body() driverBranchToAssign: DriverBranchToAssign,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      const driver =
        await this.driverService.assign_driver_to_branch(driverBranchToAssign);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'success to assign driver',
        driver,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('branch/remove')
  @SetMetadata('action', 'remove_driver_from_branch')
  async remove_from_branch(
    @Body() driverToRemove: DriverBranchToRemove,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      const driver =
        await this.driverService.remove_driver_from_branch(driverToRemove);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'success to remove driver from branch',
        driver,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get('export/excel')
  @SetMetadata('action', 'export_excel')
  async export_excel(@Res() res: Response, @Req() req: Request) {
    try {
      const company_id = req['company_id'];
      const filePath = await this.driverService.export_driver_excel(company_id);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'Success to get all as excel file',
        filePath,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
  @Post('import/excel')
  @SetMetadata('action', 'import_excel')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: 'multipart/form-data',
    required: true,
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile() file,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const driverExcelToImport: DriverExcelToImport = {
        file_path: file['path'],
      } as any;
      driverExcelToImport.company = req['company_id'] ? req['company_id'] : '';
      const data = await this.driverService.import_driver_excel(
        driverExcelToImport,
        req['current'],
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'Success to import  file',
        {},
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
}
