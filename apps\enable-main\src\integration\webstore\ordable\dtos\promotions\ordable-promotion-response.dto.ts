import { OrdableHttpResponseDto } from '../ordable-http-response.dto';

// Based on https://ordable.stoplight.io/docs/menu-management/e1f106a725c8d-create-a-promotion
export type OrdablePromotionResponseDto =
  OrdableHttpResponseDto<OrdablePromotionSuccessResponsePayloadDto>;

export class OrdablePromotionSuccessResponsePayloadDto {
  id: number;
  name: string;
  ar_name: string;
  start__date: string;
  expiry__date: string;
  available_to_users: number;
  quantity: number;
  remaining: number;
}
