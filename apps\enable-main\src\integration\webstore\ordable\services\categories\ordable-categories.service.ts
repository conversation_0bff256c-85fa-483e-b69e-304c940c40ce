import { Inject, Injectable } from '@nestjs/common';

import { Store } from '../../../../../store/models/store.model';
import { CreateOrdableCategoriesDto } from '../../dtos/categories/create-ordable-categories.dto';
import { UpdateOrdableCategoriesDto } from '../../dtos/categories/update-ordable-categories.dto';
import { OrdableHttpRequestsServiceInterface } from '../ordable-http-requests.service.interface';
import { OrdableCategoriesServiceInterface } from './ordable-categories.service.interface';

@Injectable()
export class OrdableCategoriesService
  implements OrdableCategoriesServiceInterface
{
  CATEGORIES_URI = '/api/categories/';
  constructor(
    @Inject('OrdableHttpRequestsServiceInterface')
    private readonly ordableHttpRequestsService: OrdableHttpRequestsServiceInterface,
  ) {}

  create(
    createOrdableCategoriesDto: CreateOrdableCategoriesDto,
    store: Store,
  ): Promise<any> {
    const URL = store.apiBaseUrl + this.CATEGORIES_URI;
    const API_KEY = store.apiKey;
    return this.ordableHttpRequestsService.createOrdablePostRequest(
      URL,
      API_KEY,
      createOrdableCategoriesDto,
    );
  }

  update(
    updateOrdableCategoriesDto: UpdateOrdableCategoriesDto,
    store: Store,
  ): Promise<any> {
    const URL = store.apiBaseUrl + this.CATEGORIES_URI;
    const API_KEY = store.apiKey;
    return this.ordableHttpRequestsService.createOrdablePatchRequest(
      URL,
      API_KEY,
      updateOrdableCategoriesDto,
    );
  }

  findAll(store: Store): Promise<any> {
    const URL = store.apiBaseUrl + this.CATEGORIES_URI;
    const API_KEY = store.apiKey;
    return this.ordableHttpRequestsService.createOrdableGetRequest(
      URL,
      API_KEY,
    );
  }
}
