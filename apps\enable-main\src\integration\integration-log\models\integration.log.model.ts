import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export class ErrorBody {
  name: string;
  message: string;
  stackTrace: string;
}

export type IntegrationLogDocument = HydratedDocument<IntegrationLog>;

@Schema({ timestamps: true, collection: 'integration_log' })
export class IntegrationLog {
  @Prop({
    type: String,
    required: false,
    index: true,
  })
  action: string;

  @Prop({
    type: {},
    required: false,
  })
  requestBody: Record<string, any>;

  @Prop({
    type: {},
    required: false,
  })
  requestHeaders?: Record<string, any>;

  @Prop({
    type: {},
    required: false,
  })
  responseBody: Record<string, any>;

  @Prop({
    type: () => ErrorBody,
    required: false,
  })
  errorBody?: ErrorBody;

  @Prop({
    type: String,
    required: false,
    default: 'pending',
  })
  requestStatus: string;

  @Prop({
    type: String,
    required: false,
    index: true,
  })
  itemId?: string;
}

export const IntegrationLogSchema =
  SchemaFactory.createForClass(IntegrationLog);
