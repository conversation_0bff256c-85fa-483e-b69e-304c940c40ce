import { LogError, OrderDocument } from '@app/shared-stuff';
import { Body, Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { BigCommerceServiceInterface } from '../services/big-commerce.service.interface';

@Injectable()
export class BigCommerceListener {
  constructor(
    @Inject(BigCommerceServiceInterface)
    private bigCommerceService: BigCommerceServiceInterface,
  ) {}

  @OnEvent('order.status.updated')
  @LogError()
  async onOrderStatusUpdates(@Body() order: OrderDocument) {
    await this.bigCommerceService.updateOrderStatus(order);
  }

  @OnEvent('on.order.refunded')
  @LogError()
  async onOrderRefunded(@Body() order: OrderDocument) {
    await this.bigCommerceService.updateOrderStatus(order);
  }
}
