import { LoyaltyPointLog, LoyaltyPointLogSchema } from '@app/shared-stuff';
import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { LoyaltyPointLogRepository } from './repositories/loyalty-point-log.repository';
import { LoyaltyPointLogRepositoryInterface } from './repositories/loyalty-point-log.repository.interface';
import { LoyaltyPointLogService } from './services/loyalty-point-log.service';
import { LoyaltyPointLogServiceInterface } from './services/loyalty-point-log.service.interface';

@Module({
  providers: [
    {
      provide: LoyaltyPointLogRepositoryInterface,
      useClass: LoyaltyPointLogRepository,
    },
    {
      provide: LoyaltyPointLogServiceInterface,
      useClass: LoyaltyPointLogService,
    },
  ],
  imports: [
    MongooseModule.forFeature([
      { name: LoyaltyPointLog.name, schema: LoyaltyPointLogSchema },
    ]),
  ],
  exports: [LoyaltyPointLogServiceInterface],
})
export class LoyaltyPointLogModule {}
