// EBL-4766 Location Localization
// Init `localization.locationItem` for all companies
// Run after location localization migration

const countries = db.companies.distinct('localization.country');

countries.forEach((country) => {
  const locationItem = db.LocationItems.findOne(
    {
      type: 'country',
      name: country,
    },
    {
      _id: 1,
      name: 1,
      type: 1,
      h3Indexes: 1,
      h3LowestResolution: 1,
      h3HighestResolution: 1,
      centerPoint: 1,
    },
  );

  if (!locationItem)
    return console.error('No location item for country', country);

  db.companies.updateMany(
    { 'localization.country': country },
    { $set: { 'localization.countryItem': locationItem } },
  );
});
