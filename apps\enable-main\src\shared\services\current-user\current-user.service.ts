import { CompanyDocument, CurrentUser } from '@app/shared-stuff';
import {
  Inject,
  Injectable,
  Logger,
  Scope,
  UnauthorizedException,
} from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Types } from 'mongoose';
import { HydratedRequest } from '../../dto/hydrated-request.dto';

@Injectable({ scope: Scope.REQUEST })
export class CurrentUserService {
  private readonly logger = new Logger(CurrentUserService.name);
  constructor(@Inject(REQUEST) private request: HydratedRequest) {}

  getCurrentUser(): CurrentUser {
    return this.request.current;
  }

  getCurrentCompanyId(): Types.ObjectId | null {
    if (this.request.company_id) {
      return this.request.company_id;
    }

    if (this.request.companies?.length > 0) {
      this.logger.warn(
        "Current user has multiple companies. Using the first one. This shouldn't happen.",
      );
      return this.request.companies[0];
    }

    return null;
  }

  getCurrentCompany(): CompanyDocument | null {
    return this.request.company;
  }

  getHasAccessToCompany(companyId: Types.ObjectId): boolean {
    const hasAccessToAllCompanies =
      !this.request.company_id &&
      !this.request.companies?.length &&
      ['Super Admin', 'CallCenter Agent'].includes(this.request.current.role);
    if (hasAccessToAllCompanies) return true;

    if (
      this.request.company_id instanceof Types.ObjectId &&
      this.request.company_id.toHexString() === companyId?.toHexString()
    )
      return true;

    if (this.request.companies?.length > 0)
      return this.request.companies.some(
        (company_id) => company_id.toHexString() === companyId?.toHexString(),
      );

    return false;
  }

  validateAccessToCompany(company_id: Types.ObjectId) {
    if (!this.getHasAccessToCompany(company_id)) {
      throw new UnauthorizedException('You do not have access to this company');
    }
  }
}
