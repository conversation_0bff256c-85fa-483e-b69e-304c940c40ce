const replacements = [
  'phoneNumber',
  'email',
  'firstName',
  'fullName',
  'loyaltyPointBalance',
  'loyaltyTier',
  'upperLoyaltyTier',
  'lowerTier',
  'remainingOrdersCurrentTier',
  'remainingOrdersUpperTier',
  'remainingRequirementsCurrentTier',
  'remainingRequirementsUpperTier',
  'upperTierDiscountValue',
  'tierDiscountValue',
  'tierDiscountOrderValueThreshold',
  'ordableLink',
  'loyaltyRegistrationPageLink',
  'walletPassAccessPageLink',
  'walletPassLink',
  'gracePeriodRemainingDays',
  'firstGracePeriodReminderDate',
  'validTill',
  'totalLoyaltyOrderRate',
  'totalLoyaltyAmountSpent',
  'nextComputationDate',
  'currentTierRequirementThresholdsPercent',
  'upperTierRequirementThresholdsPercent',
  'nextPunchCardAchievementRequirements',
  'nextPunchCardAchievementBenefits',
];

db.triggers.updateOne(
  { action: 'ON_CALL_CAME' },
  {
    $addToSet: {
      replacement: {
        $each: replacements,
      },
    },
  },
);
db.templates.updateMany(
  {
    'trigger.action': 'ON_CALL_CAME',
  },
  {
    $addToSet: {
      'trigger.replacement': {
        $each: replacements,
      },
    },
  },
);
