import {
  CustomerDocument,
  LookupCustomerBenefitDto,
  OrderDocument,
} from '@app/shared-stuff';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { BenefitServiceInterface } from '../../../benefit/services/benefit-service.interface';

@Injectable()
export class OrderBenefitService {
  constructor(
    @Inject(BenefitServiceInterface)
    private readonly benefitService: BenefitServiceInterface,
  ) {}

  validateBenefits(
    lookupCustomerBenefit: LookupCustomerBenefitDto[],
    customer: CustomerDocument,
  ) {
    if (!lookupCustomerBenefit || lookupCustomerBenefit.length === 0) {
      return;
    }
    for (const benefit of lookupCustomerBenefit) {
      const customerBenefit = (customer.earnedBenefits || []).find(
        (b) => b._id.equals(benefit.benefitId) && b.source === benefit.source,
      );
      if (!customerBenefit) {
        throw new BadRequestException(
          `Benefit with ID ${benefit.benefitId} not found for customer ${customer.full_name}`,
        );
      }
    }
  }

  async redeemBenefits(
    lookupCustomerBenefit: LookupCustomerBenefitDto[],
    customer: CustomerDocument,
    order: OrderDocument,
  ) {
    if (!lookupCustomerBenefit || lookupCustomerBenefit.length === 0) {
      return order;
    }
    for (const benefit of lookupCustomerBenefit) {
      const customerBenefit = customer.earnedBenefits.find(
        (b) => b._id.equals(benefit.benefitId) && b.source === benefit.source,
      );
      order = await this.benefitService.redeem(
        customerBenefit,
        customer,
        order,
      );
    }
    await order.save();
    return order;
  }
}
