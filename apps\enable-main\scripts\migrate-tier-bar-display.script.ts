// [EBL-6012] Wallet Pass Builder v1.0
// Migrate `loyaltyTier.barDisplay` to `passConfig.stripImageConfig.barConfig.tierMilestones`

const companyNameById = Object.fromEntries(
  db.companies
    .find(
      {
        _id: {
          $in: db.loyaltytiers.distinct('companyId', {
            barDisplay: { $exists: true },
          }),
        },
      },
      { name: 1 },
    )
    .toArray()
    .map((c) => [c._id.toString(), c.name]),
);

db.pass_configs.bulkWrite(
  db.loyaltytiers
    .find({ barDisplay: { $exists: true } }, { companyId: 1, barDisplay: 1 })
    .toArray()
    .map(({ _id, companyId, barDisplay }) => {
      return {
        updateOne: {
          filter: { 'owner._id': companyId },
          update: {
            $push: {
              'stripImageConfig.barConfig.tierMilestones': {
                tierId: _id,
                stamp: barDisplay,
              },
            },
            $setOnInsert: {
              owner: {
                _id: companyId,
                name: companyNameById[companyId.toString()],
                type: 'company',
              },
            },
          },
          upsert: true,
        },
      };
    }),
);

// Uncomment and run after verifying the results of the above script
// db.loyaltytiers.updateMany({ barDisplay: { $exists: true } }, [
//   { $unset: 'barDisplay' },
// ]);
