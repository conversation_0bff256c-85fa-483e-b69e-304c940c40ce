/* eslint-disable prettier/prettier */
// EBL-3922 [Loyalty Program] Loyalty Registration Page - User Experience v2.0
// Init Customer Role

db.roles.insertOne({
  privileges: [],
  permissions: db.permissions
    .find({
      module: 'customer',
      action: { $in: ['update', 'get_details', 'findOne'] },
    })
    .map((p) => p._id)
    .toArray(),
  name: 'Customer',
  description: 'Role for customer auth token.',
  year: new Date().getFullYear(),
  day: new Date().getDate(),
  week: Math.floor(
    (new Date().getMonth() + 1) * 4.3 + (new Date().getDate() / 7),
  ),
  month: new Date().toLocaleString('en-us', { month: 'long' }),
  createdAt: new Date(),
  updatedAt: new Date(),
});
