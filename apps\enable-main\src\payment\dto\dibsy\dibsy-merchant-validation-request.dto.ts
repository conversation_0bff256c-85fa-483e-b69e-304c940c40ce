import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class DibsyMerchantValidatedToRequest {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  paymentCode: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  validationUrl: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  domain: string;
}
