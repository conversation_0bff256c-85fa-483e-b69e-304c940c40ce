import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsNotEmpty, IsString } from 'class-validator';

export class CreateOrdableCategoriesDto {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  ar_name: string;

  @ApiProperty({
    type: Number,
    required: false,
    description: 'The id of the parent category',
  })
  @IsInt()
  parent?: number;

  @ApiProperty({
    type: String,
    required: false,
    description: 'A full URL',
  })
  photo: string;

  @ApiProperty({
    type: Boolean,
    required: false,
  })
  @IsBoolean()
  is_active: boolean;

  @ApiProperty({
    type: Number,
    required: false,
  })
  @IsInt()
  sort_order: number;
}
