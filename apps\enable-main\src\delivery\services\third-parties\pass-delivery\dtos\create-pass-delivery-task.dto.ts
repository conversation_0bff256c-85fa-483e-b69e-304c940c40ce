import { Type } from 'class-transformer';
import {
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { PassDeliveryPaymentType } from '../enums/pass-delivery-payment-type.enum';
import { PassDeliveryAddressesDto } from './pass-delivery-addresses.dto';

export class CreatePassDeliveryTaskDto {
  @IsOptional()
  @IsInt()
  pickup_id?: number;

  @IsOptional()
  @IsInt()
  vehicle_id?: number;

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => PassDeliveryAddressesDto)
  addresses: PassDeliveryAddressesDto;

  @IsOptional()
  @IsEnum(PassDeliveryPaymentType)
  payment_type?: PassDeliveryPaymentType;
}
