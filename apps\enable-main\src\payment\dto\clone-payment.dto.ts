import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { CountryDialCode } from '@app/shared-stuff';

export class ClonePaymentCustomerDetails {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  phone: string;

  @ApiProperty({
    type: String,
    required: true,
    enum: CountryDialCode,
    default: '+974',
  })
  @IsOptional()
  @IsEnum(CountryDialCode)
  countryCode: CountryDialCode = CountryDialCode.QATAR;
}

export class ClonePaymentDto {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  uniqueIdentifier: string;

  @ApiProperty({
    type: ClonePaymentCustomerDetails,
    required: true,
  })
  @IsNotEmpty()
  @ValidateNested()
  customerInfo: ClonePaymentCustomerDetails;
}
