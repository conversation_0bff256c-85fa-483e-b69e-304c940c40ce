import {
  Create<PERSON>rand<PERSON>ourneyDto,
  GenericExceptionFilter,
  IndexJourneyDto,
  IndexJourneyResponseDto,
  Journey,
  JourneyDocument,
  JourneyIdDto,
  TransformInterceptor,
  UpdateBrandJourneyDto,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  Param,
  Patch,
  Post,
  Query,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiExcludeEndpoint, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { CurrentUserService } from '../../shared/services/current-user/current-user.service';
import { JourneyService } from '../services/journey/journey.service';
import { JourneyServiceInterface } from '../services/journey/journey.service.interface';

@Controller('journey')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags('Journey')
@SetMetadata('module', 'journey')
export class JourneyController {
  constructor(
    @Inject(JourneyServiceInterface)
    private readonly journeyService: JourneyService,
    @Inject(CurrentUserService)
    private readonly currentUserService: CurrentUserService,
  ) {}

  @Get()
  @ApiOkResponse({ type: IndexJourneyResponseDto, isArray: true })
  @SetMetadata('action', 'get_all')
  async index(
    @Query() indexJourneyDto: IndexJourneyDto,
  ): Promise<IndexJourneyResponseDto[]> {
    return await this.journeyService.index({
      companyId: this.currentUserService.getCurrentCompanyId(),
      ...indexJourneyDto,
    });
  }

  @Post()
  @ApiOkResponse({ type: Journey })
  @SetMetadata('action', 'create')
  async create(
    @Body() createJourneyDto: CreateBrandJourneyDto,
  ): Promise<JourneyDocument> {
    return await this.journeyService.create(
      createJourneyDto,
      this.currentUserService.getCurrentUser(),
    );
  }

  @Get(':journeyId')
  @ApiOkResponse({ type: Journey })
  @SetMetadata('action', 'get_details')
  async getDetails(
    @Param() { journeyId }: JourneyIdDto,
  ): Promise<JourneyDocument> {
    return await this.journeyService.getDetails(journeyId);
  }

  @Patch(':journeyId')
  @ApiOkResponse({ type: Journey })
  @SetMetadata('action', 'update')
  async update(
    @Param() { journeyId }: JourneyIdDto,
    @Body() updateJourneyDto: UpdateBrandJourneyDto,
  ): Promise<JourneyDocument> {
    return await this.journeyService.update(journeyId, updateJourneyDto);
  }

  @Delete(':journeyId')
  @ApiOkResponse({ type: Journey })
  @SetMetadata('action', 'delete')
  async delete(@Param() { journeyId }: JourneyIdDto) {
    return await this.journeyService.delete(journeyId);
  }
}
