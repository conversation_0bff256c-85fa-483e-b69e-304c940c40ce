import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

export class AdlerBranchToSync {
  @ApiProperty({
    type: String,
    required: true,
  })
  companyId: string;
}

export class AdlerMenuToSync {
  @ApiProperty({
    type: String,
    required: true,
  })
  companyId: string;
}

export class AdlerOrderItem {
  @ApiProperty({
    type: String,
    required: false,
  })
  @IsNotEmpty()
  menuid: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsNotEmpty()
  orderquantity: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsNotEmpty()
  unitrate: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsNotEmpty()
  toppingsrate: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsNotEmpty()
  salesprice: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsNotEmpty()
  notes: string;
}

export class AdlerOrderToCreate {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  companyid: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  outletid: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  employeeid: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  customerid: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  tableno: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  customername: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  customermobilenumber: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  referencenumber: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  orderdateandtime: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  numberofpersons: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  ordertype: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  userid: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  notes: string;

  @ApiProperty({
    type: () => [AdlerOrderItem],
    required: true,
  })
  @Type(() => AdlerOrderItem)
  restaurantorderdtlsave: AdlerOrderItem[];
}
