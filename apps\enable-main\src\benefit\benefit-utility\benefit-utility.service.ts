import {
  CompanyDocument,
  CouponBenefit,
  CreateOrUpdateCouponBenefitDto,
  CreateOrUpdateTierBenefitDto,
  LoyaltyTierBenefit,
  mapAsync,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { Types } from 'mongoose';
import { BenefitServiceInterface } from '../services/benefit-service.interface';

@Injectable()
export class BenefitUtilityService {
  constructor(
    @Inject(BenefitServiceInterface)
    private readonly benefitService: BenefitServiceInterface,
  ) {}

  async upsertEmbedded(
    company: CompanyDocument,
    dtos: Array<CreateOrUpdateCouponBenefitDto | CreateOrUpdateTierBenefitDto>,
  ): Promise<Array<CouponBenefit | LoyaltyTierBenefit>> {
    const [toUpdate, toCreate] = this.partitionBenefits(dtos);

    const updated = await this.updateEmbedded(company, toUpdate);
    const created = await this.createEmbedded(company, toCreate);

    return [...updated, ...created];
  }

  private async createEmbedded(
    company: CompanyDocument,
    dtos: Array<CreateOrUpdateCouponBenefitDto | CreateOrUpdateTierBenefitDto>,
  ): Promise<Array<CouponBenefit | LoyaltyTierBenefit>> {
    const created: Array<CouponBenefit | LoyaltyTierBenefit> = [];

    await mapAsync(dtos, async (dto) => {
      const doc = await this.benefitService.create({
        ...dto,
        companyId: company._id,
        currency: company.localization?.currency,
      });
      const plain = doc.toObject();
      created.push({
        ...plain,
        _id: doc._id,
        config: plain.config,
      } as LoyaltyTierBenefit | CouponBenefit);
    });

    return created;
  }

  private async updateEmbedded(
    company: CompanyDocument,
    dtos: Array<CreateOrUpdateCouponBenefitDto | CreateOrUpdateTierBenefitDto>,
  ): Promise<Array<CouponBenefit | LoyaltyTierBenefit>> {
    const updated: Array<CouponBenefit | LoyaltyTierBenefit> = [];

    await mapAsync(dtos, async (dto) => {
      const doc = await this.benefitService.update({
        ...dto,
        _id: dto.benefitId,
        companyId: company._id,
        updateEmbeddedBenefit: true,
        currency: company.localization?.currency,
      });
      const plain = doc.toObject();
      updated.push({
        ...plain,
        config: plain.config,
      } as LoyaltyTierBenefit | CouponBenefit);
    });

    return updated;
  }

  private partitionBenefits<T extends { benefitId?: Types.ObjectId }>(
    benefits: T[],
  ): [T[], T[]] {
    const toUpdate: T[] = [];
    const toCreate: T[] = [];

    for (const benefit of benefits) {
      if (benefit.benefitId) toUpdate.push(benefit);
      else toCreate.push(benefit);
    }

    return [toUpdate, toCreate];
  }
}
