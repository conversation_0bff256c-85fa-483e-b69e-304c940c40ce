import {
  BenefitSchema,
  CollectionName,
  SharedStuffModule,
} from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CustomerRepositoryModule } from '../customer/modules/customer-repository/customer-repository.module';
import { LoyaltyTransactionModule } from '../loyalty-transaction/loyalty-transaction.module';
import { RestaurantModule } from '../restaurant/restaurant.module';
import { SharedModule } from '../shared/shared.module';
import { BenefitController } from './controllers/benefit.controller';
import { BenefitRepository } from './repositories/benefit.repository';
import { BenefitRepositoryInterface } from './repositories/benefit.repository.interface';
import { BenefitServiceInterface } from './services/benefit-service.interface';
import { BenefitService } from './services/benefit.service';
import { BenefitTypeFactoryService } from './services/types/benefit-type-factory.service';
import { FixedAmountConcreteService } from './services/types/concretes/fixed-amount.concrete.service';
import { PercentageDiscountConcreteService } from './services/types/concretes/percentage-discount.concrete.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: CollectionName.BENEFIT, schema: BenefitSchema },
    ]),
    SharedModule,
    SharedStuffModule,
    RestaurantModule,
    CustomerRepositoryModule,
    LoyaltyTransactionModule,
  ],
  controllers: [BenefitController],
  providers: [
    {
      provide: BenefitRepositoryInterface,
      useClass: BenefitRepository,
    },
    {
      provide: BenefitServiceInterface,
      useClass: BenefitService,
    },
    BenefitTypeFactoryService,
    FixedAmountConcreteService,
    PercentageDiscountConcreteService,
  ],
  exports: [BenefitServiceInterface, BenefitTypeFactoryService],
})
export class BenefitModule {}
