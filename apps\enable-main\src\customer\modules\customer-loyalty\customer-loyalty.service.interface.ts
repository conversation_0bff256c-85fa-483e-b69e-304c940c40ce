import {
  CalendarCycle,
  CompanyDocument,
  CountryDialCode,
  CustomerDocument,
  IndexCustomerDto,
  LookupCallCenterCustomerDto,
  LoyaltyCustomer,
  LRPSource,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface CustomerLoyaltyServiceInterface {
  indexLoyaltyCustomers(indexCustomerDto: IndexCustomerDto): Promise<any[]>;

  getCustomerWithLoyalty(
    customer: CustomerDocument,
    company: CompanyDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<LoyaltyCustomer>;

  getCustomerDetailsForFrontend(
    uniqueIdentifier: string,
    companyId: Types.ObjectId,
    countryCode?: CountryDialCode,
  ): Promise<CustomerDocument | LoyaltyCustomer | undefined>;

  getSimplifiedCustomerDetails(
    phoneNumber: string,
    companyId: string,
    countryCode?: CountryDialCode,
  ): Promise<CustomerDocument>;

  getCustomerDetailsForFrontendByAuthToken(
    authHeader: string,
  ): Promise<CustomerDocument | LoyaltyCustomer>;

  getLoyaltyRegistrationPageLink(
    brandId: Types.ObjectId,
    company?: CompanyDocument,
    customer?: CustomerDocument,
    source?: LRPSource,
    branchId?: Types.ObjectId,
  ): Promise<string>;

  lookupCallCenterCustomer(
    lookupCallCenterCustomerDto: LookupCallCenterCustomerDto,
  ): Promise<CustomerDocument | LoyaltyCustomer>;
}

export const CustomerLoyaltyServiceInterface = Symbol(
  'CustomerLoyaltyServiceInterface',
);
