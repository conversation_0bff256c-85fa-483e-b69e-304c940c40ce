import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { instanceToPlain } from 'class-transformer';
import { sign } from 'jsonwebtoken';
import { CustomerTokenPayload } from '../../../shared/dto/customer-token-payload.dto';
import { CustomerTokenServiceInterface } from './customer-token.service.interface';

@Injectable()
export class CustomerTokenService implements CustomerTokenServiceInterface {
  constructor(private configService: ConfigService) {}

  public generateCustomerToken({
    customerId,
    companyId,
    brandId,
  }: CustomerTokenPayload): string {
    const payload = new CustomerTokenPayload();
    payload.customerId = customerId;
    payload.companyId = companyId;
    payload.brandId = brandId;

    const token: string = sign(
      instanceToPlain(payload),
      this.configService.get<string>('MAIN_JWT_SECRET'),
      { expiresIn: '1y' },
    );

    return token;
  }
}
