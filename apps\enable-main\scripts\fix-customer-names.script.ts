// EBL-5409 Fix the full names saved as first name
// Find customers who have multiple first names and no last names
// and split the name into a first and last name

// 1. [MongoDB] Get all names to be processed
JSON.stringify(
  db.customers.distinct('first_name', {
    first_name: /^\s*[A-Za-z\p{Arabic}\s]{2,}\s[A-Za-z\p{Arabic}\s]+\s*$/,
    last_name: { $in: ['', ' ', null] },
    // Only doing LRP customers to avoid extra processing
    $or: [
      { contact_channel: 'loyalty_registration_page' },
      { loyaltyRegistrationAt: { $ne: null } },
    ],
  }),
);

// 2. [Outside of MongoDB] Parse the name, create a write, batch writes
const INCOMPLETE_NAMES = [
  'mr',
  'ms',
  'mrs',
  'madam',
  'miss',
  'dr',
  'al',
  'om',
  'am',
  'el',
  'um',
  'umm',
  'abu',
  'abo',
  'bo',
  'bu',
  'abou',
  'abd',
  'abdu',
  'abdo',
  'abdl',
  'abdul',
  'abdel',
  'abdol',
  'sir',
  'أبو',
  'ابو',
  'بو',
  'أم',
  'ام',
];

const firstNames = JSON.parse(''); // Paste names from previous step here
const writes = firstNames.map((name) => {
  const names = name.trim().split(/\s+/);
  const includeFirstTwo = INCOMPLETE_NAMES.includes(names[0].toLowerCase());

  return {
    updateOne: {
      filter: { first_name: name, last_name: { $in: ['', ' ', null] } },
      update: {
        $set: {
          first_name: includeFirstTwo ? names.slice(0, 2).join(' ') : names[0],
          last_name: names.slice(includeFirstTwo ? 2 : 1).join(' '),
        },
      },
    },
  };
});
const batches = [];
while (writes.length) {
  batches.push(writes.splice(0, 1000));
}
JSON.stringify(batches);

// 3. [MongoDB] Run updates
batches.forEach((batch) => db.customers.bulkWrite(batch, { ordered: false }));

// Original script, very slow
const INCOMPLETE_NAMES_OLD = [
  'mr',
  'ms',
  'mrs',
  'madam',
  'miss',
  'dr',
  'al',
  'om',
  'am',
  'el',
  'um',
  'umm',
  'abu',
  'abo',
  'bo',
  'bu',
  'abou',
  'abd',
  'abdu',
  'abdo',
  'abdl',
  'abdul',
  'abdel',
  'abdol',
  'sir',
  'أبو',
  'ابو',
  'بو',
  'أم',
  'ام',
];
db.customers
  .find(
    {
      first_name: /^\s*[A-Za-z\p{Arabic}\s]{2,}\s[A-Za-z\p{Arabic}\s]+\s*$/,
      last_name: { $in: ['', ' ', null] },
    },
    { _id: 1, first_name: 1 },
  )
  .forEach((customer) => {
    const names = customer.first_name.trim().split(/\s+/);

    const includeFirstTwo = INCOMPLETE_NAMES_OLD.includes(
      names[0].toLowerCase(),
    );
    const update = {
      first_name: includeFirstTwo ? names.slice(0, 2).join(' ') : names[0],
      last_name: names.slice(includeFirstTwo ? 2 : 1).join(' '),
    };

    db.customers.updateOne({ _id: customer._id }, { $set: update });
  });
