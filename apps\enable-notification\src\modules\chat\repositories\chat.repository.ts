import { ChatRepositoryInterface } from './chat.repository.interface';
import { GenericRepository } from '@app/shared-stuff';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Chat, ChatDocument } from '../models/chat.model';
import { Injectable } from '@nestjs/common/decorators';

@Injectable()
export class ChatRepository
  extends GenericRepository<ChatDocument, Chat>
  implements ChatRepositoryInterface
{
  constructor(
    @InjectModel(Chat.name) private chatModel: Model<ChatDocument, Chat>,
  ) {
    super(chatModel);
  }
}
