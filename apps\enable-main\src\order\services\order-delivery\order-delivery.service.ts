import {
  BranchDocument,
  CollectionName,
  CompanyDocument,
  Customer,
  DeliveryMethod,
  DeliveryThirdPartyName,
  DriverDocument,
  LogError,
  LoggerService,
  OrderDeliveryAction,
  OrderDeliveryType,
  OrderDocument,
  OrderLogActionEnum,
  OrderPaymentMethod,
  OrderPaymentStatus,
  TookanTaskProcessingOptionsInterface,
} from '@app/shared-stuff';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import * as randomestring from 'randomstring';
import { CustomerReadServiceInterface } from '../../../customer/modules/customer-read/customer-read.service.interface';
import { ThirdPartyTaskCreationDto } from '../../../delivery/dto/third-party-task-creation.dto';
import {
  CreateBothTookanTaskDto,
  CreateDeliveryTookanTaskDto,
  CreateTookanTaskDto,
  TookanTask,
} from '../../../delivery/dto/tookan.dto';
import { DeliveryConfigurationServiceInterface } from '../../../delivery/services/delivery-configuration/delivery-configuration.service.interface';
import { ClicksService } from '../../../delivery/services/third-parties/clicks/clicks.service';
import { ThirdPartyService } from '../../../delivery/services/third-parties/third-party.service';
import { TookanService } from '../../../delivery/services/tookan/tookan.service';
import { LocationType } from '../../../shared/enums/location-type.enum';
import { HelperService } from '../../../shared/services/helper/helper.service';
import { ThirdPartyActionCreationMapping } from '../../constants';
import { CreateDeliveryMethod } from '../../types/create-delivery-method.type';
import { OrderLogServiceInterface } from '../interfaces/order-log.service.interface';
import { OrderStatusService } from '../order-status/order-status.service';
import { BranchService } from './../../../branch/services/branch/branch.service';
import { CompanyService } from './../../../company/services/company/company.service';
import {
  MrDeliveryStatusToUpdate,
  MrDeliveryTaskStatus,
} from './../../../delivery/dto/mr-delivery.dto';
import { TookanMultipleTasksToAssign } from './../../../delivery/dto/tookan.dto';
import { DriverService } from './../../../delivery/services/driver/driver.service';
import { SavedLocationService } from './../../../location/services/saved-location/saved-location.service';
import { OrderNotificationService } from './../order-notification/order-notification.service';
import { OrderPaymentService } from './../order-payment/order-payment.service';

@Injectable()
export class OrderDeliveryService {
  private readonly loggerService = new LoggerService(OrderDeliveryService.name);

  constructor(
    private tookanService: TookanService,
    private configService: ConfigService,
    private driverService: DriverService,
    @Inject(CustomerReadServiceInterface)
    private customerReadService: CustomerReadServiceInterface,
    @InjectModel(CollectionName.ORDER) private orderModel: Model<OrderDocument>,
    private orderNotificationService: OrderNotificationService,
    private clicksService: ClicksService,
    private eventEmitter: EventEmitter2,
    @Inject(forwardRef(() => OrderPaymentService))
    private orderPaymentService: OrderPaymentService,
    private companyService: CompanyService,
    private branchService: BranchService,
    private savedLocationService: SavedLocationService,
    private orderStatusService: OrderStatusService,
    @Inject('OrderLogServiceInterface')
    private readonly orderLogService: OrderLogServiceInterface,
    private helperService: HelperService,
    private thirdPartyService: ThirdPartyService,
    @Inject(DeliveryConfigurationServiceInterface)
    private deliveryConfigurationService: DeliveryConfigurationServiceInterface,
  ) {}

  async handleThirdPartyTasks(
    desiredThirdParty: DeliveryThirdPartyName,
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
  ) {
    if (thirdPartyTaskCreationDto.order?.deliveryTaskCreated) return;
    const createdTask = await this.thirdPartyService.createThirdPartyTask(
      desiredThirdParty,
      thirdPartyTaskCreationDto,
    );
    if (createdTask) {
      const orderLogAction = ThirdPartyActionCreationMapping[desiredThirdParty];
      await this.orderLogService.saveOrderLog(
        thirdPartyTaskCreationDto.order,
        { requestedObject: createdTask },
        { responseObject: createdTask },
        orderLogAction,
        thirdPartyTaskCreationDto.order.createdBy,
      );
    }
  }

  async tookanTaskProcessing({
    order,
    customer,
    branch,
    company,
    driver,
    reassignDriver = false,
    customTask = false,
    customTeamId = '',
  }: TookanTaskProcessingOptionsInterface): Promise<void> {
    if (
      order.deliveryMethod === DeliveryMethod.THIRD_PARTY ||
      !order.pickupLocationId ||
      order.delivery_action === OrderDeliveryAction.DINE_IN ||
      order.delivery_action === OrderDeliveryAction.IN_STORE_PICKUP ||
      order.tookanTaskCancelled ||
      (order.children && order.children.length) ||
      (order.delivery_type !== OrderDeliveryType.urgent &&
        order.isOperable === false)
    ) {
      this.loggerService.log(
        'OrderDeliveryService.tookanTaskProcessing conditions led to not proceed',
        {
          order,
          customer,
          branch,
          company,
          driver,
          reassignDriver,
          customTask,
          customTeamId,
        },
      );
      return;
    }

    if (order.tookan_job_id) {
      await this.editTookanTask({
        order,
        customer,
        branch,
        company,
        driver,
        reassignDriver,
      });
    } else {
      await this.createTookanTask({
        order,
        customer,
        branch,
        company,
        driver,
        customTask,
        customTeamId,
      });
    }
  }

  async cancelTookanTask(order: OrderDocument): Promise<void> {
    const jobIds: number[] = [parseInt(order?.pickupJobId)]
      .concat(order?.deliveryJobId ? [parseInt(order.deliveryJobId)] : [])
      .filter((jobId) => !isNaN(jobId));

    let response;
    if (jobIds.length > 0)
      response = await this.tookanService.cancelTookanTask(jobIds);

    await this.orderLogService.saveOrderLog(
      order,
      { requestedObject: { jobIds } },
      { responseObject: response },
      OrderLogActionEnum.ON_TOOKAN_TASK_CANCELED,
      order.createdBy,
    );
    await this.orderModel.updateOne(
      { _id: order._id },
      { $set: { tookanTaskCancelled: true } },
    );
  }

  async assignDriverToTookanTask(driver: DriverDocument, order: OrderDocument) {
    // let driverTookanToAssign: DriverTookanToAssign = {
    //   api_key: this.configService.get('TOOKAN_API_KEY'),
    //   fleet_id: parseInt(driver.tookan_driver_id),
    //   team_id: parseInt(driver.team_id),
    //   job_id: parseInt(order.tookan_job_id),
    //   job_status: '2',
    //   notify: 1,
    // };
    const jobIds = [];
    if (order.pickupJobId) {
      jobIds.push(order.pickupJobId);
    }
    if (order.deliveryJobId) {
      jobIds.push(order.deliveryJobId);
    }
    if (jobIds.length == 0) {
      jobIds.push(order.tookan_job_id);
    }
    // let tookanAssign = await this.tookanService.assignDriverToTask(
    //   driverTookanToAssign,
    // );
    const tookanAssign = await this.assignDriverToMultipleTask(jobIds, driver);
    return tookanAssign;
  }

  async assignMultipleTasksToDriver(
    order: OrderDocument,
    driver: DriverDocument,
  ) {
    const jobIds = [];
    jobIds.push(order.tookanDeliveryJobId);
    for (let i = 0; i < order.tookanPickupJobIds.length; i++) {
      jobIds.push(order.tookanPickupJobIds[i]);
    }
    await this.assignDriverToMultipleTask(jobIds, driver);
  }

  @OnEvent('mrDelivery.updated')
  @LogError()
  async onMrDeliveryTaskUpdated(mrDeliveryData: MrDeliveryStatusToUpdate) {
    const order = await this.orderModel.findOne({
      code: mrDeliveryData.order_id,
    });
    if (!order) {
      return;
    }

    switch (mrDeliveryData.order_status) {
      case MrDeliveryTaskStatus.DeliveryStarted:
        break;
      case MrDeliveryTaskStatus.Dispatched:
        break;
      case MrDeliveryTaskStatus.NotApplicable:
        break;
      case MrDeliveryTaskStatus.Rejected:
        break;
      case MrDeliveryTaskStatus.Delivered:
        break;
      default:
        break;
    }
    //TODO: update the order status based on the above mapping
    await this.orderLogService.saveOrderLog(
      order,
      { requestedObject: {} },
      { responseObject: mrDeliveryData },
      OrderLogActionEnum.ON_MR_DELIVERY_TASK_UPDATED,
      order.createdBy,
    );

    this.eventEmitter.emit('order.status.updated', order);
  }

  @OnEvent('bee.updated')
  @LogError()
  async onBeeTaskUpdated(beeData: any) {
    const order = await this.orderModel.findOne({
      code: beeData['order_id'],
    });
    if (!order) {
      return;
    }
    if (beeData['delivery_status'] == 'delivery_picked_up') {
      order.pickedUpOn = moment().utc().toDate();
    } else if (beeData['delivery_status'] == 'delivery_assigned') {
      order.startedDeliveringOn = moment().utc().toDate();
    } else if (beeData['delivery_status'] == 'delivery_completed') {
      order.payment_status =
        order.payment_method != OrderPaymentMethod.online
          ? OrderPaymentStatus.COMPLETED
          : order.payment_status;
      order.deliveredOn = moment().utc().toDate();
      await this.orderNotificationService.onOrderDeliveredFromTookan(order);
    }
    await order.save();
    this.eventEmitter.emit('order.status.updated', order);
    return order;
  }

  //DeliverX Integration
  async createDeliveryHubTask(
    order: OrderDocument,
    company: CompanyDocument,
    customer: Customer,
    branch: BranchDocument,
  ) {
    if (
      order.deliveryTaskCreated ||
      order.delivery_action == OrderDeliveryAction.IN_STORE_PICKUP
    ) {
      return;
    }
    const teamId = this.configService.get('DELIVERY_HUB_TEAM_ID');
    await this.createTookanTask({
      order,
      customer,
      branch,
      company,
      driver: undefined,
      customTask: true,
      customTeamId: teamId,
    });
  }

  async handleOrderDeliveryMethodCreation(
    createDeliveryMethod: CreateDeliveryMethod,
  ) {
    this.loggerService.log('[onOrderReady] Yes can Delivery Task Created');
    const branch = await this.branchService.get_details(
      createDeliveryMethod.order.branch['_id'],
    );
    const company = await this.companyService.get_details(
      createDeliveryMethod.order.company,
    );
    const deliveryConfiguration =
      await this.deliveryConfigurationService.findByCompanyId(company._id);
    const customer = await this.customerReadService.findOne(
      createDeliveryMethod.order.customer.toHexString(),
      company._id,
    );

    this.loggerService.log(
      '[onOrderReady] deliveryConfiguration',
      deliveryConfiguration,
    );

    this.handleUndefinedOrderDates(createDeliveryMethod.order);

    const thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto = {
      order: createDeliveryMethod.order,
      branch:
        typeof createDeliveryMethod.order.branch !== 'string'
          ? createDeliveryMethod.order.branch
          : undefined,
      company: company,
      customer: customer,
      pickupLocation: createDeliveryMethod.order.pickupLocation,
      currentUser: createDeliveryMethod.currentUser,
    };

    if (
      createDeliveryMethod.order.deliveryMethod != DeliveryMethod.THIRD_PARTY &&
      createDeliveryMethod.order.delivery_action !=
        OrderDeliveryAction.IN_STORE_PICKUP
    ) {
      if (
        createDeliveryMethod.order.deliveryMethod ==
        DeliveryMethod.COMPANY_DRIVERS
      ) {
        createDeliveryMethod.order.autoAssign = true;
        createDeliveryMethod.order.assigned_driver_name = undefined;
        createDeliveryMethod.order.driver = undefined;
        createDeliveryMethod.driverId = undefined;
      }
      const driver = await this.driverService.get_details(
        createDeliveryMethod.deliveryMethod == DeliveryMethod.E_BUTLER
          ? '5fd09b4c35497a3edbe3d6fb'
          : createDeliveryMethod.driverId
            ? (createDeliveryMethod.driverId as any)
            : '',
      );

      this.loggerService.log('[onOrderReady] before tookanTaskProcessing ');
      await this.tookanTaskProcessing({
        order: createDeliveryMethod.order,
        customer,
        branch,
        company,
        driver,
        reassignDriver: true,
      });
    } else if (
      createDeliveryMethod.order.deliveryMethod == DeliveryMethod.THIRD_PARTY ||
      (deliveryConfiguration.defaultDeliveryMethod &&
        deliveryConfiguration.thirdPartyConfiguration?.thirdParties.length ===
          1 &&
        deliveryConfiguration.usingEbDelivery == false &&
        deliveryConfiguration.usingCompanyDrivers == false &&
        deliveryConfiguration.usingBranchDrivers == false &&
        deliveryConfiguration.usingThirdParty == true)
    ) {
      await this.handleThirdPartyTasks(
        createDeliveryMethod.thirdParty,
        thirdPartyTaskCreationDto,
      );
    }
  }

  private async editTookanTask({
    order,
    customer,
    branch,
    company,
    driver,
    reassignDriver = false,
  }: TookanTaskProcessingOptionsInterface): Promise<any> {
    const deliveryLocation = await this.savedLocationService.getDetails(
      order.deliveryLocationId
        ? order.deliveryLocationId.toHexString()
        : new Types.ObjectId().toHexString(),
    );
    const pickupLocation = await this.savedLocationService.getDetails(
      order.pickupLocationId
        ? order.pickupLocationId.toHexString()
        : new Types.ObjectId().toHexString(),
    );

    let pickupAddressString = 'Please Contact Branch For Further Information',
      deliveryAddressString = 'Please Contact Customer For Further Information';

    if (deliveryLocation) {
      deliveryAddressString = this.helperService.convertLocationToString(
        deliveryLocation,
        LocationType.DELIVERY,
      );
    }
    if (pickupLocation) {
      pickupAddressString = this.helperService.convertLocationToString(
        pickupLocation,
        LocationType.PICKUP,
      );
    }

    const customerDetails = this.getCustomerDetailsForTookan(customer, order);
    const tookanMultipleTaskToEdit: {
      api_key: string;
      pickups: TookanTask[];
      deliveries: TookanTask[];
    } = {
      api_key: this.configService.get('TOOKAN_API_KEY'),
      deliveries: [],
      pickups: [],
    };

    if (order.deliveryJobId) {
      tookanMultipleTaskToEdit.deliveries.push({
        job_id: order.deliveryJobId,
        address: `${deliveryAddressString} - ${order.code}`,
        email: customerDetails.email,
        job_description: order.order_remarks,
        name: customerDetails.name,
        phone: customerDetails.phone,
        order_id: order.code,
        time: moment
          .utc(order.delivery_date)
          .clone()
          .tz(
            order.localization?.timezone
              ? order.localization.timezone
              : 'Asia/Qatar',
          )
          .format('YYYY-MM-DD HH:mm:ss'),
        latitude: deliveryLocation ? deliveryLocation.latitude : 0,
        longitude: deliveryLocation ? deliveryLocation.longitude : 0,
        ref_images: [],
        barcode: order.barCode.toString(),
        template_data: this.constructTookanTaskMetadata(order),
        template_name: 'Pickup_&_Delivery_Enable',
      });
    }

    if (order.pickupJobId) {
      tookanMultipleTaskToEdit.pickups.push({
        job_id: order.pickupJobId,
        address: `${pickupAddressString} - ${order.code}`,
        email: branch ? branch.email : company.email,
        job_description: order.order_remarks,
        name: branch ? branch.name : company.name,
        order_id: order.code,
        time: moment
          .utc(order.pickup_date)
          .clone()
          .tz(
            order.localization?.timezone
              ? order.localization?.timezone
              : 'Asia/Qatar',
          )
          .format('YYYY-MM-DD HH:mm:ss'),
        latitude: pickupLocation ? pickupLocation.latitude : 0,
        longitude: pickupLocation ? pickupLocation.longitude : 0,
        barcode: order.barCode.toString(),
        ref_images: [],
        template_data: this.constructTookanTaskMetadata(order),
        template_name: 'Pickup_&_Delivery_Enable',
      });
    }
    const response = await this.tookanService.editTookanMultipleTask(
      tookanMultipleTaskToEdit,
    );
    const jobIds: number[] = [];
    if (order.deliveryJobId) jobIds.push(parseInt(order.deliveryJobId));
    if (order.pickupJobId) jobIds.push(parseInt(order.pickupJobId));
    if (reassignDriver && driver) {
      await this.assignDriverToMultipleTask(jobIds, driver);
    }

    if (driver) {
      order.driver = driver._id;
      order.assigned_driver_name =
        driver.first_name + ' ' + (driver.last_name ? driver.last_name : '');
    } else if (order.autoAssign) {
      order.assigned_driver_name = 'Auto Assignment';
    } else if (order.deliveryParty != DeliveryThirdPartyName.deliveryHub) {
      order.assigned_driver_name = 'Not Detected';
    }

    await this.orderLogService.saveOrderLog(
      order,
      { requestedObject: tookanMultipleTaskToEdit },
      { responseObject: response },
      OrderLogActionEnum.TOOKAN_TASK_EDIT,
      order.createdBy,
    );

    return response;
  }

  private async createTookanTask(
    options: TookanTaskProcessingOptionsInterface,
  ): Promise<OrderDocument> {
    const {
      order,
      customer,
      branch,
      company,
      driver,
      customTask = false,
      customTeamId = '',
    } = options;
    const deliveryLocation = await this.savedLocationService.getDetails(
      order.deliveryLocationId
        ? order.deliveryLocationId.toHexString()
        : new Types.ObjectId().toHexString(),
    );
    const pickupLocation = await this.savedLocationService.getDetails(
      order.pickupLocationId
        ? order.pickupLocationId.toHexString()
        : new Types.ObjectId().toHexString(),
    );

    if (company.has_delivery_system || customTask) {
      if (order.deliveryMethod == DeliveryMethod.E_BUTLER) {
        company.number_of_tookan_tasks_ebutler++;
      } else if (!customTask) {
        company.number_of_tookan_tasks_own++;
      }
      await company.save();

      let pickupAddressString = 'Please Contact Branch For Further Information',
        deliveryAddressString =
          'Please Contact Customer For Further Information';

      if (deliveryLocation) {
        deliveryAddressString = this.helperService.convertLocationToString(
          deliveryLocation,
          LocationType.DELIVERY,
        );
      }
      if (pickupLocation) {
        pickupAddressString = this.helperService.convertLocationToString(
          pickupLocation,
          LocationType.PICKUP,
        );
      }
      const customerDetails = this.getCustomerDetailsForTookan(customer, order);

      const createTookanTaskDto: CreateTookanTaskDto = {
        api_key: this.configService.get('TOOKAN_API_KEY'),
        order_id: order.code,
        auto_assignment: order.autoAssign || customTask ? 1 : 0,
        job_description: order.order_remarks,
        team_id: customTask
          ? customTeamId
          : driver
            ? driver.team_id
            : company.tookan_team_id
              ? company.tookan_team_id
              : '409744',
        fleet_id: driver ? driver.tookan_driver_id : '',
        barcode: order.barCode,
        tags: order.autoAssign ? `${company._id.toHexString()}` : '',
        timezone: moment.tz
          .zone(
            order?.localization?.timezone
              ? order?.localization?.timezone
              : 'Asia/Qatar',
          )
          .utcOffset(moment.utc().valueOf()),
        layout_type: 0,
        custom_field_template: 'Pickup_&_Delivery_Enable',
      };

      const createDeliveryTookanTaskDto: CreateDeliveryTookanTaskDto = {
        ...createTookanTaskDto,
        customer_email: customerDetails.email,
        customer_username: customerDetails.name,
        customer_phone: customerDetails.phone,

        customer_address: `${deliveryAddressString} - ${order.code}`,

        latitude: deliveryLocation
          ? deliveryLocation?.latitude?.toString()
          : '0',
        longitude: deliveryLocation
          ? deliveryLocation?.longitude?.toString()
          : '0',

        job_delivery_datetime: moment
          .utc(order.delivery_date)
          .clone()
          .tz(
            order.localization?.timezone
              ? order.localization?.timezone
              : 'Asia/Qatar',
          )
          .format('YYYY-MM-DD HH:mm:ss'),

        has_delivery: 1,
        has_pickup: 0,
        layout_type: 0,

        meta_data: this.constructTookanTaskMetadata(order),
      };

      const createBothTookanTaskDto: CreateBothTookanTaskDto = {
        ...createDeliveryTookanTaskDto,
        has_pickup: 1,
        job_pickup_phone: branch ? branch.phone : company.phone,
        job_pickup_name: branch ? branch.name : company.name,
        job_pickup_email: branch ? branch.email : company.email,
        job_pickup_address: `${pickupAddressString} - ${order.code}`,
        job_pickup_latitude: pickupLocation
          ? pickupLocation?.latitude?.toString()
          : '0',
        job_pickup_longitude: pickupLocation
          ? pickupLocation?.longitude?.toString()
          : '0',

        job_pickup_datetime: moment
          .utc(order.pickup_date)
          .clone()
          .tz(
            order.localization?.timezone
              ? order.localization?.timezone
              : 'Asia/Qatar',
          )
          .format('YYYY-MM-DD HH:mm:ss'),
        pickup_custom_field_template: 'Pickup_&_Delivery_Enable',
        pickup_meta_data: this.constructTookanTaskMetadata(order),
      };

      const driverExists = !!driver;
      const driverHasMultipleBranches =
        driverExists && driver.branches.length > 1;
      const usingBranchDrivers =
        order.deliveryMethod === DeliveryMethod.BRANCH_DRIVERS;

      const shouldCreatePickupTask =
        !usingBranchDrivers && (!driverExists || driverHasMultipleBranches);

      const taskToCreate = shouldCreatePickupTask
        ? createBothTookanTaskDto
        : createDeliveryTookanTaskDto;

      const created_tookan_task =
        await this.tookanService.createTookanTask(taskToCreate);

      order.tookan_job_id = created_tookan_task['job_id'];

      order.tookan_delivery_track_url =
        created_tookan_task['delivery_tracing_link'] ||
        created_tookan_task['tracking_link'];

      order.tookan_pickup_track_url =
        created_tookan_task['pickup_tracking_link'];

      order.deliveryJobId = created_tookan_task['delivery_job_id']
        ? created_tookan_task['delivery_job_id']
        : created_tookan_task['job_id'];
      order.pickupJobId = created_tookan_task['pickup_job_id'];

      order.deliveryTaskCreated = true;

      await this.orderLogService.saveOrderLog(
        order,
        { requestedObject: taskToCreate },
        { responseObject: created_tookan_task },
        OrderLogActionEnum.TOOKAN_TASK_CREATE,
        order.createdBy,
      );
    }
    // order.is_ready = true;
    if (driver) {
      order.driver = driver._id;
      order.assigned_driver_name =
        driver.first_name + ' ' + (driver.last_name ? driver.last_name : '');
    } else if (order.autoAssign) {
      order.assigned_driver_name = 'Auto Assignment';
    } else if (order.deliveryParty != DeliveryThirdPartyName.deliveryHub) {
      order.assigned_driver_name = 'Not Detected';
    }

    order.deliveryTaskCreated = true;
    await order.save();
    return order;
  }

  private constructTookanTaskMetadata(order: OrderDocument) {
    const isPaidAlready =
      order.payment_method === OrderPaymentMethod.online &&
      order.payment_status === OrderPaymentStatus.COMPLETED;

    return [
      {
        label: 'Payment_Amount',
        data: isPaidAlready ? 'Paid Already' : order.total_amount.toString(),
      },
      {
        label: 'Payment_Method',
        data: isPaidAlready
          ? 'Online'
          : this.formatPaymentMethod(order.payment_method),
      },
      {
        label: 'Special_Instruction',
        data: order.order_remarks,
      },
      {
        label: 'Order_Is_Gift',
        data: order.is_gift ? 'Yes' : 'No',
      },
      ...(order.is_gift
        ? [
            {
              label: 'Gift_Recipient_Details',
              data: `Name: ${order.recipient_name}, Phone: ${
                order.recipient_phone
              }, Gift Message: ${
                order.cardMessage ? order.cardMessage : 'N/A'
              }`,
            },
          ]
        : []),
    ];
  }

  private formatPaymentMethod(paymentMethod: OrderPaymentMethod) {
    switch (paymentMethod) {
      // Unsuccesful online payments should be shown as Cash
      case OrderPaymentMethod.online:
      case OrderPaymentMethod.cash:
        return 'Cash';
      case OrderPaymentMethod.card_machine:
        return 'Card Machine';
      case OrderPaymentMethod.prepaid:
        return 'Prepaid';
      default:
        return paymentMethod;
    }
  }

  private async assignDriverToMultipleTask(
    jobIds: number[],
    driver: DriverDocument,
  ) {
    const tookanMultipleTasksToAAssign: TookanMultipleTasksToAssign = {
      api_key: this.configService.get('TOOKAN_API_KEY'),
      fleet_id: parseInt(driver.tookan_driver_id),
      team_id: parseInt(driver.team_id),
      job_ids: jobIds,
    };
    const result = await this.tookanService.assignDriverToMultipleTask(
      tookanMultipleTasksToAAssign,
    );
    return result;
  }

  private handleUndefinedOrderDates(order: OrderDocument) {
    if (!order.delivery_date) order.delivery_date = moment.utc().toDate();
    if (!order.pickup_date) order.pickup_date = moment.utc().toDate();
  }

  private getCustomerDetailsForTookan(
    customer: Customer,
    order: OrderDocument,
  ) {
    return {
      email:
        customer && customer.email
          ? customer.email
          : 'default' + randomestring.generate(5) + '@e-butler.com',
      name: order.is_gift ? order.recipient_name : order.customer_name,
      phone: order.is_gift
        ? order.recipient_country_code + order.recipient_phone
        : order.country_code + order.customer_phone,
    };
  }
}
