import { responseCode } from '@app/shared-stuff';
import {
  CanActivate,
  ExecutionContext,
  HttpException,
  Injectable,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Reflector } from '@nestjs/core';
import * as moment from 'moment-timezone';
import { PermissionService } from '../../rbac/services/permission/permission.service';
import { RoleWithPermsisions } from '../../rbac/types/role-with-permissions.type';

@Injectable()
export class AuthorizeUserGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private permissionService: PermissionService,
    private configService: ConfigService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const module = this.reflector.get<string>('module', context.getClass());
    const action = this.reflector.get<string>('action', context.getHandler());
    const publicCheck = this.reflector.get<string>(
      'public',
      context.getHandler(),
    );

    if (
      publicCheck ||
      this.configService.get<string>('ENABLE_AUTH_GUARD') == 'false'
    ) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const roles = this.getRoles(request);
    const canAccess = await this.canAccess(roles, module, action);
    if (!canAccess) {
      throw new HttpException(
        {
          code: responseCode.UN_AUTHERIZED,
          message: "You don't have the access to this route",
          error: { message: "You don't have the access to this route" },
        },
        401,
      );
    }
    return true;
  }

  private getRoles(request): RoleWithPermsisions[] {
    if (request['user']) {
      return request['user']['roles'];
    } else if (request['apikey']) {
      return request['apikey']['roles'];
    } else if (request['customer']) {
      return request['customer']['roles'];
    }
    return [];
  }

  private async canAccess(
    roles: RoleWithPermsisions[],
    module: string,
    action: string,
  ): Promise<boolean> {
    if (!roles || roles.length === 0) return false;

    const hasPermission = roles.some((role) =>
      role?.permissions.some(
        (permission) =>
          permission.module === module && permission.action === action,
      ),
    );
    if (hasPermission) return true;

    const superAdminRole = roles.find((role) => role.name === 'Super Admin');
    if (!superAdminRole) return false;

    const permission = await this.permissionService.findOrCreate(
      module,
      action,
    );
    const isNewPermission = moment
      .utc(permission.createdAt)
      .isAfter(moment.utc(superAdminRole.updatedAt));
    if (isNewPermission) return true;

    return false;
  }
}
