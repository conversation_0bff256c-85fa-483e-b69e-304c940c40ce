import {
  CustomerDocument,
  GoogleWalletWebhookPayload,
  RegisteredPass,
} from '@app/shared-stuff';
import { Types } from 'mongoose';
import { GeneratePassDto } from '../../dto/generate-pass.dto';

export interface GooglePassesServiceInterface {
  handleGoogleWalletWebhook(payload: GoogleWalletWebhookPayload): Promise<void>;
  generatePassWithoutAuth(generatePassDto: GeneratePassDto): Promise<string>;
  generatePass(
    customer: CustomerDocument,
    brandId: Types.ObjectId,
  ): Promise<string>;
  updatePasses(googlePasses: RegisteredPass[]): Promise<void>;
}

export const GooglePassesServiceInterface = Symbol(
  'GooglePassesServiceInterface',
);
