import {
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  Param,
  Patch,
  Post,
  Put,
  Query,
  SetMetadata,
  UseFilters,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';

import {
  BulkUpdateLoyaltyTiersDto,
  CreateLoyaltyTierDto,
  DeleteLoyaltyTierDto,
  DeletionContext,
  GenericExceptionFilter,
  IndexLoyaltyTierDto,
  LoyaltyTier,
  LoyaltyTierDocument,
  LoyaltyTierIdDto,
  NoTier,
  TransformInterceptor,
  UpdateLoyaltyTierDto,
} from '@app/shared-stuff';
import { CurrentUserService } from '../../shared/services/current-user/current-user.service';
import { LoyaltyTierDeletionServiceInterface } from '../modules/loyalty-tier-deletion/loyalty-tier-deletion.service.interface';
import { LoyaltyTierIndexService } from '../modules/loyalty-tier-index/loyalty-tier-index.service';
import { LoyaltyTierReadServiceInterface } from '../modules/loyalty-tier-read/loyalty-tier-read.service.interface';
import { LoyaltyTierWriteServiceInterface } from '../modules/loyalty-tier-write/loyalty-tier-write.service.interface';

@Controller('loyalty-tier')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@UsePipes(
  new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidUnknownValues: true,
  }),
)
@ApiTags('LoyaltyTier')
@SetMetadata('module', 'loyaltytier')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class LoyaltyTierController {
  constructor(
    @Inject(LoyaltyTierReadServiceInterface)
    private readonly loyaltyTierReadService: LoyaltyTierReadServiceInterface,
    @Inject(LoyaltyTierWriteServiceInterface)
    private readonly loyaltyTierWriteService: LoyaltyTierWriteServiceInterface,
    @Inject(LoyaltyTierDeletionServiceInterface)
    private readonly loyaltyTierDeletionService: LoyaltyTierDeletionServiceInterface,
    private readonly loyaltyTierIndexService: LoyaltyTierIndexService,
    private readonly currentUserService: CurrentUserService,
  ) {}

  @Get()
  @ApiOkResponse({ type: LoyaltyTier, isArray: true })
  @SetMetadata('public', 'true')
  async index(
    @Query() indexLoyaltyTierDto: IndexLoyaltyTierDto,
  ): Promise<LoyaltyTierDocument[] | [...LoyaltyTierDocument[], NoTier]> {
    return await this.loyaltyTierIndexService.index(indexLoyaltyTierDto);
  }

  @Get(':loyaltyTierId')
  @ApiOkResponse({ type: LoyaltyTier })
  @SetMetadata('public', 'true')
  async getById(
    @Param() { loyaltyTierId }: LoyaltyTierIdDto,
  ): Promise<LoyaltyTierDocument> {
    const loyaltyTier =
      await this.loyaltyTierReadService.findById(loyaltyTierId);
    this.currentUserService.validateAccessToCompany(loyaltyTier.companyId);
    return loyaltyTier;
  }

  @Post()
  @ApiOkResponse({ type: LoyaltyTier })
  @SetMetadata('action', 'create')
  async create(
    @Body() createLoyaltyTierDto: CreateLoyaltyTierDto,
  ): Promise<LoyaltyTierDocument> {
    this.currentUserService.validateAccessToCompany(
      createLoyaltyTierDto.companyId,
    );
    return await this.loyaltyTierWriteService.create(createLoyaltyTierDto);
  }

  @Patch()
  @ApiOkResponse({ type: LoyaltyTier })
  @SetMetadata('action', 'update')
  async update(
    @Body() updateLoyaltyTierDto: UpdateLoyaltyTierDto,
  ): Promise<LoyaltyTierDocument> {
    return await this.loyaltyTierWriteService.update(updateLoyaltyTierDto);
  }

  @Get(':loyaltyTierId/deletion-context')
  @SetMetadata('action', 'deletion-context')
  async getDeletionContext(
    @Param() { loyaltyTierId }: LoyaltyTierIdDto,
  ): Promise<DeletionContext> {
    return await this.loyaltyTierDeletionService.getDeletionContext(
      loyaltyTierId,
    );
  }

  @Delete(':loyaltyTierId')
  @SetMetadata('action', 'delete')
  async delete(
    @Param() { loyaltyTierId }: LoyaltyTierIdDto,
    @Body() deleteLoyaltyTierDto: DeleteLoyaltyTierDto,
  ): Promise<LoyaltyTierDocument> {
    return await this.loyaltyTierDeletionService.delete(
      loyaltyTierId,
      deleteLoyaltyTierDto,
    );
  }

  @Put('bulkUpdate')
  @ApiOkResponse({ type: LoyaltyTier, isArray: true })
  @SetMetadata('action', 'bulkUpdate')
  async bulkUpdate(
    @Body() { loyaltyTiers }: BulkUpdateLoyaltyTiersDto,
  ): Promise<LoyaltyTierDocument[]> {
    loyaltyTiers.forEach(({ companyId }) =>
      this.currentUserService.validateAccessToCompany(companyId),
    );
    return await this.loyaltyTierWriteService.bulkUpdate({ loyaltyTiers });
  }

  @Post(':loyaltyTierId/generateEnrollmentCode')
  @ApiOkResponse({ type: String })
  @SetMetadata('action', 'generateEnrollmentCode')
  async generateEnrollmentCode(
    @Param() { loyaltyTierId }: LoyaltyTierIdDto,
  ): Promise<string> {
    return await this.loyaltyTierWriteService.generateEnrollmentCode(
      loyaltyTierId,
    );
  }
}
