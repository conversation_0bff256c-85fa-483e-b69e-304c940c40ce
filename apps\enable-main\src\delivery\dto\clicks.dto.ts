import { ApiProperty } from '@nestjs/swagger';

export class ClicksTaskToCreate {
  @ApiProperty({
    type: String,
    required: false,
  })
  merchant_id: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  team_id: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  order_id: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  auto_assignment: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  job_description: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  job_pickup_phone: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  job_pickup_name: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  job_pickup_email: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  job_pickup_address: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  job_pickup_latitude: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  job_pickup_longitude: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  job_pickup_datetime: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  customer_email: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  customer_username: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  customer_phone: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  customer_address: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  longitude: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  latitude: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  job_delivery_datetime: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  has_pickup: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  has_delivery: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  layout_type: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  tracking_link: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  timezone: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  custom_field_template: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  pickup_custom_field_template: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  tags: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  meta_data: any;

  @ApiProperty({
    type: String,
    required: false,
  })
  pickup_meta_data: any;

  @ApiProperty({
    type: String,
    required: false,
  })
  transport_type?: string;
}
