import { GenericReplacementDto } from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import * as moment from 'moment';

@Injectable()
export class GenericReplacementService {
  private getMonthReplacements(isArabicPreferred: boolean = false) {
    const currentDate = moment.utc();
    const previousDate = moment.utc().subtract(1, 'month');

    if (isArabicPreferred) {
      currentDate.locale('ar');
      previousDate.locale('ar');
    }

    return {
      currentMonth: currentDate.format('M'),
      currentMonthName: currentDate.format('MMMM'),
      currentMonthShortName: isArabicPreferred
        ? currentDate.format('MMMM') // Arabic uses full name
        : currentDate.format('MMM'),
      previousMonth: previousDate.format('M'),
      previousMonthName: previousDate.format('MMMM'),
      previousMonthShortName: isArabicPreferred
        ? previousDate.format('MMMM') // Arabic uses full name
        : previousDate.format('MMM'),
    };
  }

  getGenericReplacement(
    isArabicPreferred: boolean = false,
  ): GenericReplacementDto {
    return { ...this.getMonthReplacements(isArabicPreferred) };
  }
}
