import { Module } from '@nestjs/common';
import { SharedModule } from '../shared/shared.module';
import { PunchCardController } from './controllers/punch-card.controller';
import { PunchCardAchievementModule } from './modules/punch-card-achievement/punch-card-achievement.module';
import { PunchCardReadModule } from './modules/punch-card-read/punch-card-read.module';
import { PunchCardWriteModule } from './modules/punch-card-write/punch-card-write.module';

@Module({
  imports: [
    PunchCardReadModule,
    PunchCardWriteModule,
    PunchCardAchievementModule,
    SharedModule,
  ],
  controllers: [PunchCardController],
})
export class PunchCardModule {}
