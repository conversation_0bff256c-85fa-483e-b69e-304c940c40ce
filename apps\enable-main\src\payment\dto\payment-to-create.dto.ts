import {
  CountryDialCode,
  EmbeddedBrandDto,
  IsPhoneNumberForRegion,
  OrderSource,
  PaymentMethod,
} from '@app/shared-stuff';
import { ObjectIdTransform } from '@app/shared-stuff';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import { Types } from 'mongoose';

export class PaymentToCreate {
  @ApiProperty({
    type: String,
    required: true,
  })
  customer_name: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsPhoneNumberForRegion('country_code')
  customer_phone: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsEnum(CountryDialCode)
  country_code: CountryDialCode;

  @ApiProperty({
    type: String,
    required: false,
  })
  comments: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  amount: number;

  @ApiProperty({
    type: String,
    enum: ['english', 'arabic'],
    default: 'english',
  })
  language: string;

  @ApiProperty({
    type: String,
    enum: PaymentMethod,
    required: false,
  })
  @IsOptional()
  payment_method?: PaymentMethod;

  @ApiProperty({
    type: Boolean,
    required: false,
  })
  is_test: boolean;

  @ApiProperty({
    type: String,
    required: false,
  })
  callback_url: string;

  @ApiProperty({
    type: String,
    enum: OrderSource,
    required: false,
  })
  source: OrderSource;

  @ApiProperty({
    type: String,
    required: false,
  })
  branch: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: false,
  })
  @ObjectIdTransform({ optional: true })
  branch_id: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: false,
  })
  @ObjectIdTransform({ optional: true })
  brandId: Types.ObjectId;

  fireTrigger = true;

  customer: string;
  company_name: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @ObjectIdTransform()
  company: Types.ObjectId;

  code: string;
  order_code: string;
  brand?: EmbeddedBrandDto;
}
