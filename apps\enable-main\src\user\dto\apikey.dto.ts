import { EmbeddedBrandDto, DataIndex } from '@app/shared-stuff';
import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsMongoId, IsNotEmpty, IsOptional } from 'class-validator';
import { Types } from 'mongoose';

export class ApiKeyToCreate {
  @ApiProperty()
  name: string;

  @ApiProperty()
  expire_date: string;

  secret: string;

  @ApiProperty()
  roles: [string];

  @ApiProperty({
    type: EmbeddedBrandDto,
    required: true,
  })
  brand: EmbeddedBrandDto;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  company: string;

  @ApiProperty({
    type: 'string',
    enum: ['active', 'not_active'],
    required: false,
  })
  status: string;

  @ApiProperty({
    type: [String],
    required: true,
    description: 'Array of branches IDs',
  })
  @IsArray()
  @IsOptional()
  @IsMongoId({ each: true })
  branches: Types.ObjectId[];

  code: string;
}

export class ApiKeyToUpdate {
  @ApiProperty()
  _id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  expire_date: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  company: string;

  @ApiProperty({
    type: EmbeddedBrandDto,
    required: true,
  })
  brand: EmbeddedBrandDto;

  @ApiProperty({
    type: 'string',
    enum: ['active', 'not_active'],
    required: false,
  })
  status: string;

  @ApiProperty()
  roles: [string];

  @ApiProperty({
    type: [String],
    required: true,
    description: 'Array of branches IDs',
  })
  @IsArray()
  @IsOptional()
  @IsMongoId({ each: true })
  branches: Types.ObjectId[];
}

export class ApiKeyToIndex extends DataIndex {
  @ApiProperty({
    type: 'string',
    enum: ['active', 'not_active'],
    required: false,
  })
  status: string;

  @ApiProperty({ required: false })
  role_id: string;

  @ApiProperty({ required: false })
  role_name: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  brandId: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  company: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  branch: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  id: string;
}
