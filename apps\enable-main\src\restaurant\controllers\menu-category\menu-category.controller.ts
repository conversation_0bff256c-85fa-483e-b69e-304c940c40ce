import { Request, Response } from 'express';
import { HelperService } from '../../../shared/services/helper/helper.service';
import { MenuCategoryService } from './../../services/menu-category/menu-category.service';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Put,
  Query,
  Req,
  Res,
  SetMetadata,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import {
  MenuCategoryToCreate,
  MenuCategoryToIndex,
  MenuCategoryToUpdate,
  responseCode,
} from '@app/shared-stuff';
import { Post } from '@nestjs/common/decorators/http';

@Controller('menu-category')
@ApiTags('Restaurant Menu Category')
@SetMetadata('module', 'menu-category')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class MenuCategoryController {
  constructor(
    private menuCategoryService: MenuCategoryService,
    private helperService: HelperService,
  ) {}

  @Get()
  @SetMetadata('action', 'get_all')
  async index(
    @Query() menuToIndex: MenuCategoryToIndex,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      menuToIndex.currentUser = req['current'];
      const menus = await this.menuCategoryService.index(menuToIndex);
      const counts =
        await this.menuCategoryService.getMenuCategoryTotalNumbers(menuToIndex);

      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to get all Menus categories',
        { menus, totalMenus: counts },
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  async get_details(@Param('id') id: string, @Res() res: Response) {
    try {
      const menu = await this.menuCategoryService.getDetails(id);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'success to get menu category',
        menu,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() menuToCreate: MenuCategoryToCreate,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      menuToCreate.createdBy = req['current'];

      const warehouse = await this.menuCategoryService.create(menuToCreate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success to create new Menu category',
        warehouse,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Put(':id')
  @SetMetadata('action', 'update')
  async update(
    @Body() menuToUpdate: MenuCategoryToUpdate,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      menuToUpdate.updatedBy = req['current'];
      const menu = await this.menuCategoryService.update(menuToUpdate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success to update new Menu category',
        menu,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Delete(':id')
  @SetMetadata('action', 'remove')
  async remove(
    @Param('id') id: string,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      const menu = await this.menuCategoryService.remove({
        _id: id,
        deletedBy: req['current'],
      });
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success to remove new Menu category',
        menu,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
}
