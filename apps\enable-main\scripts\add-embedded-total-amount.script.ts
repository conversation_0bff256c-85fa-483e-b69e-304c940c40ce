// EBL-3799 Tier Leveling-up Requirements Configurations - Amount Spent
// Add `total_amount` property to customer's embedded order dto (for orders after 1st of july)
// This script is based on the script used for EBL-3431 Optimize Order Rate Computation

const inserts = db.orders
  .aggregate([
    {
      $match: {
        pickup_date: {
          $gte: new Date(2023, 6, 1), // orders before current month/quarter are irrelevant
        },
      },
    },
    {
      $group: {
        _id: '$customer',
        orders: {
          $push: {
            source: '$source',
            status: '$status',
            isCartValueThresholdMet: '$isCartValueThresholdMet',
            pickup_date: '$pickup_date',
            delivery_date: '$delivery_date',
            total_amount: '$total_amount',
          },
        },
      },
    },
  ])
  .map(({ _id, orders }) => ({
    updateOne: {
      filter: { _id },
      update: { $set: { orders } },
    },
  }))
  .toArray();

db.customers.bulkWrite(inserts, { ordered: false });
