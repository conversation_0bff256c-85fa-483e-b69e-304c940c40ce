import { ApiProperty } from '@nestjs/swagger';
import { StoreProvider } from '../enumerations/store-provider.enum';
import { Types } from 'mongoose';
import { IsMongoId, ValidateNested } from 'class-validator';
import { Field, ShopifyConfig } from '../../../../../libs/shared-stuff/src';

export class UpdateStoreDto {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsMongoId()
  id: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: false,
  })
  link: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  apiKey: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  apiBaseUrl: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  name: string;

  @ApiProperty({
    type: String,
    required: false,
    enum: Object.keys(StoreProvider),
  })
  provider: string;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  companyId: Types.ObjectId;

  @ApiProperty({
    type: [Types.ObjectId],
    required: false,
  })
  brandIds: Types.ObjectId[];

  @Field({
    type: ShopifyConfig,
    required: false,
  })
  shopifyConfig?: ShopifyConfig;
}
