import { Module } from '@nestjs/common';
import { BenefitModule } from 'apps/enable-main/src/benefit/benefit.module';
import { CompanyModule } from 'apps/enable-main/src/company/company.module';
import { CustomerNotificationModule } from 'apps/enable-main/src/customer/modules/customer-notification/customer-notification.module';
import { CustomerReadModule } from 'apps/enable-main/src/customer/modules/customer-read/customer-read.module';
import { CustomerTierInfoModule } from 'apps/enable-main/src/customer/modules/customer-tier-info/customer-tier-info.module';
import { CustomerTierModule } from 'apps/enable-main/src/customer/modules/customer-tier/customer-tier.module';
import { LoyaltyPointLogModule } from 'apps/enable-main/src/loyalty-point-log/loyalty-point-log.module';
import { LoyaltyTierLogModule } from 'apps/enable-main/src/loyalty-tier-log/loyalty-tier-log.module';
import { LoyaltyTierReadModule } from 'apps/enable-main/src/loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.module';
import { LoyaltyTransactionModule } from 'apps/enable-main/src/loyalty-transaction/loyalty-transaction.module';
import { CompletedPunchCardModule } from 'apps/enable-main/src/punch-card/modules/completed-punch-card/completed-punch-card.module';
import { OrderRedemptionService } from './order-redemption.service';
import { CouponModule } from 'apps/enable-main/src/coupon/coupon.module';

@Module({
  imports: [
    BenefitModule,
    CompanyModule,
    CompletedPunchCardModule,
    CouponModule,
    CustomerNotificationModule,
    CustomerReadModule,
    CustomerTierInfoModule,
    CustomerTierModule,
    LoyaltyPointLogModule,
    LoyaltyTierLogModule,
    LoyaltyTierReadModule,
    LoyaltyTransactionModule,
  ],
  providers: [OrderRedemptionService],
  exports: [OrderRedemptionService],
})
export class OrderRedemptionModule {}
