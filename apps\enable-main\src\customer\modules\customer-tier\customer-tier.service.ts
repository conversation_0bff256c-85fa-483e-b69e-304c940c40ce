import {
  BadRequestException,
  Inject,
  Injectable,
  Logger,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';

import {
  BenefitMaximumUsageType,
  BenefitUsage,
  BenefitUsagesKeys,
  BulkActionResponseDto,
  BulkTierAssignmentDto,
  CalendarCycle,
  clamp,
  CompanyDocument,
  CustomerBenefitSource,
  CustomerDocument,
  CustomerEarnedBenefitUsagesKeys,
  CycleOffset,
  EmbeddedTierDto,
  embeddedTierFields,
  LoyaltyCustomerReplacementsDto,
  LoyaltyTierBenefit,
  LoyaltyTierDocument,
  LoyaltyTierLogDocument,
  LoyaltyTierProgramProgress,
  mapAsync,
  NumberOfUsesType,
  pick,
  TierStatus,
  UpdateCustomerDto,
} from '@app/shared-stuff';
import { BenefitServiceInterface } from '../../../benefit/services/benefit-service.interface';
import { CalendarSystemService } from '../../../company/services/calendar-system/calendar-system.service';
import { CompanyService } from '../../../company/services/company/company.service';
import {
  LoyaltyTierLogger,
  LoyaltyTierLogServiceInterface,
} from '../../../loyalty-tier-log/services/loyalty-tier-log.service.interface';
import { LoyaltyTierReadServiceInterface } from '../../../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.service.interface';
import { LoyaltyTransactionServiceInterface } from '../../../loyalty-transaction/services/loyalty-transaction-service.interface';
import { CustomerIndexServiceInterface } from '../customer-index/customer-index.service.interface';
import { CustomerNotificationServiceInterface } from '../customer-notification/customer-notification.service.interface';
import { CustomerReadServiceInterface } from '../customer-read/customer-read.service.interface';
import { CustomerReplacementsServiceInterface } from '../customer-replacements/customer-replacements.service.interface';
import { CustomerRepositoryInterface } from '../customer-repository/customer.repository.interface';
import { CustomerTierInfoServiceInterface } from '../customer-tier-info/customer-tier-info.service.interface';
import { CustomerTierServiceInterface } from './customer-tier.service.interface';

@Injectable()
export class CustomerTierService implements CustomerTierServiceInterface {
  private readonly logger = new Logger(CustomerTierService.name);

  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly calendarSystemService: CalendarSystemService,
    @Inject(CustomerRepositoryInterface)
    private readonly customerRepository: CustomerRepositoryInterface,
    @Inject(CustomerTierInfoServiceInterface)
    private readonly customerLoyaltyTierInfoService: CustomerTierInfoServiceInterface,
    @Inject(CustomerNotificationServiceInterface)
    private readonly customerNotificationService: CustomerNotificationServiceInterface,
    @Inject(CustomerReplacementsServiceInterface)
    private readonly customerReplacementsService: CustomerReplacementsServiceInterface,
    @Inject(CustomerIndexServiceInterface)
    private readonly customerIndexService: CustomerIndexServiceInterface,
    @Inject(CustomerReadServiceInterface)
    private readonly customerReadService: CustomerReadServiceInterface,
    @Inject(LoyaltyTierReadServiceInterface)
    private readonly loyaltyTierService: LoyaltyTierReadServiceInterface,
    @Inject(LoyaltyTierLogServiceInterface)
    private readonly loyaltyTierLogService: LoyaltyTierLogServiceInterface,
    private companyService: CompanyService,
    @Inject(BenefitServiceInterface)
    private readonly benefitService: BenefitServiceInterface,
    @Inject(LoyaltyTransactionServiceInterface)
    private readonly loyaltyTransactionService: LoyaltyTransactionServiceInterface,
  ) {}

  public async resetCarryOver(company: CompanyDocument): Promise<void> {
    const calendarSystem = company.loyaltyProgramConfig.calendarSystem;
    const calendarSystemHasCarryOver =
      this.calendarSystemService.isFixedCalendarSystem(calendarSystem);
    if (calendarSystemHasCarryOver) return;

    await this.customerRepository.resetCarryOver(company._id);
  }

  async reduceNumberOfUses(
    customer: CustomerDocument,
    benefitType: 'PERCENT_DISCOUNT' | 'FREE_DELIVERY',
  ): Promise<void> {
    if (
      benefitType === 'PERCENT_DISCOUNT' &&
      customer?.loyaltyTier?.percentDiscountRemainingNumberOfUses > 0
    ) {
      customer.loyaltyTier.percentDiscountRemainingNumberOfUses -= 1;
      await this.loyaltyTierLogService.onTierDiscountRedeem(customer);
    }
    if (
      benefitType === 'FREE_DELIVERY' &&
      customer.loyaltyTier.freeDeliveryRemainingNumberOfUses > 0
    ) {
      customer.loyaltyTier.freeDeliveryRemainingNumberOfUses -= 1;
      await this.loyaltyTierLogService.onFreeDeliveryRedeem(customer);
    }
    customer.markModified('loyaltyTier');
    await customer.save();
    if (
      customer?.loyaltyTier?.freeDeliveryRemainingNumberOfUses === 0 ||
      customer?.loyaltyTier?.percentDiscountRemainingNumberOfUses === 0
    )
      this.eventEmitter.emit('loyaltyTier.numberOfUses.consumed', customer);
  }

  async reassignForTierDeletion(
    loyaltyTier: LoyaltyTierDocument,
    otherTiers: LoyaltyTierDocument[],
  ): Promise<void> {
    const customers = await this.customerReadService.findByLoyaltyTier(
      [loyaltyTier._id],
      loyaltyTier.companyId,
    );

    await mapAsync(customers, async (customer) => {
      const orderRate =
        await this.customerLoyaltyTierInfoService.getTotalLoyaltyOrderRate(
          customer,
        );
      const amountSpent =
        await this.customerLoyaltyTierInfoService.getTotalLoyaltyAmountSpent(
          customer,
        );
      const pointsRate =
        await this.customerLoyaltyTierInfoService.getTotalLoyaltyPointsRate(
          customer,
        );

      const highestEligibleTier = otherTiers.reduce(
        (previousTier, currentTier) => {
          const isHigherTier =
            !previousTier || currentTier.tierIndex > previousTier.tierIndex;

          const isOrderRateMet =
            currentTier.orderRateThreshold &&
            orderRate > currentTier.orderRateThreshold;
          const isAmountSpentMet =
            currentTier.amountSpentThreshold &&
            amountSpent > currentTier.amountSpentThreshold;
          const isPointsRateMet =
            currentTier.pointsRateThreshold &&
            pointsRate > currentTier.pointsRateThreshold;
          const isCustomerEligible =
            isOrderRateMet || isAmountSpentMet || isPointsRateMet;

          return isHigherTier && isCustomerEligible
            ? currentTier
            : previousTier;
        },
        null,
      );

      await this.updateCustomerTier(
        customer,
        highestEligibleTier?._id,
        this.loyaltyTierLogService.onTierDeletion,
      );
    });
  }

  async updateCustomerTier(
    customer: CustomerDocument,
    newTierId: Types.ObjectId,
    logger: LoyaltyTierLogger,
    isBenefitUsagePreserved = true,
  ): Promise<void> {
    const newTier = newTierId
      ? await this.loyaltyTierService.findById(newTierId)
      : null;

    if (newTier && newTier.companyId.toString() !== customer.company.toString())
      throw new BadRequestException(
        `Cannot update tier. Customer company is ${customer.company}. Loyalty Tier company is ${newTier.companyId}. These must match.`,
      );

    const oldTier = customer.loyaltyTier
      ? await this.loyaltyTierService.findById(customer.loyaltyTier._id)
      : null;

    const benefitUsages = isBenefitUsagePreserved
      ? this.computeBenefitUsages(customer, oldTier)
      : [];
    const oldEmbeddedTier = customer.loyaltyTier;

    customer.loyaltyTier = newTier ? pick(newTier, embeddedTierFields) : null;
    customer.tierUpdatedAt = moment.utc().toDate();
    if (!customer.firstTierEarnedAt)
      customer.firstTierEarnedAt = moment.utc().toDate();
    customer.tierStatus = await this.computeTierStatus(
      customer.tierStatus,
      oldTier?._id,
      newTier,
    );

    if (oldTier && oldTier.benefits)
      await this.removeCustomerBenefits(customer, oldTier.benefits);

    if (newTier && newTier.benefits)
      await this.addBenefitsToCustomer(
        customer,
        newTier.benefits,
        benefitUsages,
      );

    this.updateRemainingNumberOfUses(
      customer,
      oldEmbeddedTier,
      isBenefitUsagePreserved,
    );

    customer.markModified('loyaltyTier');

    await customer.save();
    await logger.bind(this.loyaltyTierLogService)(customer, oldTier?._id);
    this.eventEmitter.emit('customer.tier.updated', customer, oldTier);
  }

  private computeBenefitUsages(
    customer: CustomerDocument,
    tier: LoyaltyTierDocument,
  ): BenefitUsage[] {
    if (!tier?.benefits?.length) return [];

    return tier.benefits
      .filter(
        (benefit) =>
          benefit.config.maximumUsageType === BenefitMaximumUsageType.LIMITED &&
          benefit.config.preserveUsage,
      )
      .map((benefit) => {
        const earnedBenefit = (customer.earnedBenefits || []).find(
          (earnedBenefit) =>
            earnedBenefit._id.equals(benefit._id) &&
            earnedBenefit.source === CustomerBenefitSource.TIER_PROGRAM,
        );

        if (earnedBenefit)
          return pick(earnedBenefit, CustomerEarnedBenefitUsagesKeys);

        // If the benefit is not in the earnedBenefits, we assume it has been
        // completed used, and consider numberOfUsages to be the maximum uses.
        return {
          ...pick(benefit, BenefitUsagesKeys),
          source: CustomerBenefitSource.TIER_PROGRAM,
          numberOfUsages: benefit.config.maximumUsage,
        };
      });
  }

  private async computeTierStatus(
    currentStatus: TierStatus,
    oldTierId?: Types.ObjectId,
    newTier?: LoyaltyTierDocument,
  ): Promise<TierStatus> {
    if (!oldTierId && !newTier) return currentStatus;
    if (!oldTierId && newTier) return TierStatus.UPGRADED;
    if (!newTier) return TierStatus.NO_TIER;
    if (oldTierId.equals(newTier._id)) return currentStatus;

    const oldTier = await this.loyaltyTierService.findById(oldTierId);
    if (oldTier.tierIndex < newTier.tierIndex) return TierStatus.UPGRADED;
    return TierStatus.MAINTAINED;
  }

  async checkForPromotion(
    customer: CustomerDocument,
    logger: LoyaltyTierLogger,
    brandId?: Types.ObjectId,
  ): Promise<void> {
    if (customer.loyaltyTier?.isVipTier) return;

    const totalLoyaltyAmountSpent =
      await this.customerLoyaltyTierInfoService.getTotalLoyaltyAmountSpent(
        customer,
      );
    const totalLoyaltyOrderRate =
      await this.customerLoyaltyTierInfoService.getTotalLoyaltyOrderRate(
        customer,
      );
    const totalLoyaltyPointsRate =
      await this.customerLoyaltyTierInfoService.getTotalLoyaltyPointsRate(
        customer,
      );

    const highestEligibleTier =
      await this.loyaltyTierService.findHighestEligibleTierAfter(
        customer.company,
        customer.loyaltyTier?._id,
        totalLoyaltyOrderRate,
        totalLoyaltyAmountSpent,
        totalLoyaltyPointsRate,
      );

    if (
      !highestEligibleTier ||
      highestEligibleTier._id.equals(customer.loyaltyTier?._id)
    ) {
      return this.logger.log(
        `Customer ${customer._id} not promoted from tier ${customer.loyaltyTier?.nameEn}`,
        {
          customer: customer._id,
          highestEligibleTier,
          totalLoyaltyOrderRate,
          totalLoyaltyAmountSpent,
        },
      );
    }

    const previousTier = customer.loyaltyTier;
    await this.updateCustomerTier(customer, highestEligibleTier._id, logger);
    this.logger.log(
      `Customer ${customer._id} upgraded to tier ${highestEligibleTier._id} from ${previousTier}`,
      {
        customer,
        highestEligibleTier,
        totalLoyaltyOrderRate,
        totalLoyaltyAmountSpent,
      },
    );

    await this.customerNotificationService.fireOnTierUpgradeTrigger(
      customer,
      await this.customerReplacementsService.getCustomerReplacements(customer),
      brandId,
    );
  }

  async checkForTierMaintained(customer: CustomerDocument) {
    if (!customer.loyaltyTier) return;
    if (customer.tierStatus !== TierStatus.NOT_MAINTAINED) return;

    if (
      await this.customerLoyaltyTierInfoService.hasCustomerMaintainedTier(
        customer,
      )
    ) {
      customer.tierStatus = TierStatus.MAINTAINED;
      await this.customerRepository.updateOne(
        { _id: customer._id },
        { $set: { tierStatus: TierStatus.MAINTAINED } },
      );
    }
  }

  async checkForTierUnmaintained(
    customer: CustomerDocument,
    company: CompanyDocument,
  ): Promise<void> {
    if (!customer.loyaltyTier) return;
    if (customer.tierStatus !== TierStatus.MAINTAINED) return;

    const isInGracePeriod =
      await this.customerLoyaltyTierInfoService.isInGracePeriod(
        customer,
        company,
      );
    if (isInGracePeriod) return;

    const hasMetTierRequirements =
      await this.customerLoyaltyTierInfoService.hasCustomerMetTierRequirements(
        customer,
        company,
      );
    if (hasMetTierRequirements) return;

    customer.tierStatus = TierStatus.NOT_MAINTAINED;
    await this.customerRepository.updateOne(
      { _id: customer._id },
      { $set: { tierStatus: TierStatus.NOT_MAINTAINED } },
    );
  }

  async enrollCustomerInTier(
    customer: CustomerDocument,
    enrollmentCode: string,
    brandId?: Types.ObjectId,
  ): Promise<void> {
    try {
      const loyaltyTier =
        await this.loyaltyTierService.findByEnrollmentCode(enrollmentCode);

      const previousTier = customer.loyaltyTier;
      await this.updateCustomerTier(
        customer,
        loyaltyTier._id,
        this.loyaltyTierLogService.onBaseTierAssignment,
      );

      const hasTierChanged =
        previousTier?._id.toHexString() !== loyaltyTier._id.toHexString();
      if (hasTierChanged) {
        await this.customerNotificationService.fireOnBaseTierAssignmentTrigger(
          customer,
          await this.customerReplacementsService.getCustomerReplacements(
            customer,
            { brandId },
          ),
          brandId,
        );
      }
    } catch (error) {
      this.logger.error('Error enrolling customer in tier ', {
        customer,
        enrollmentCode,
        error,
      });
    }
  }

  async handleManualUpdatedLoyaltyTier(
    customer: CustomerDocument,
    { loyaltyTier }: UpdateCustomerDto,
  ) {
    const isLoyaltyTierSame =
      loyaltyTier === undefined ||
      customer.loyaltyTier?._id.equals(loyaltyTier);
    if (isLoyaltyTierSame) return;

    const newTierReplacements = await this.getNewTierReplacements(
      customer,
      loyaltyTier,
    );
    await this.customerNotificationService.fireOnTierManualAssignmentTrigger(
      customer,
      newTierReplacements,
    );

    const hadOldTier = customer.loyaltyTier?._id instanceof Types.ObjectId;
    if (!hadOldTier)
      return await this.notifyOnLoyaltyTierAssigned(customer, loyaltyTier);
    const oldTier = await this.loyaltyTierService.findById(
      customer.loyaltyTier._id,
    );

    const hasNewTier = loyaltyTier instanceof Types.ObjectId;
    if (!hasNewTier) {
      const oldTierReplacements =
        await this.customerReplacementsService.getCustomerReplacements(
          customer,
        );
      await this.updateCustomerTier(
        customer,
        loyaltyTier,
        this.loyaltyTierLogService.onManualTierDowngrade,
      );
      return await this.customerNotificationService.fireOnTierRemoveTrigger(
        customer,
        oldTierReplacements,
      );
    }
    const newTier = await this.loyaltyTierService.findById(loyaltyTier._id);

    const isTierUpgrade = newTier.tierIndex > oldTier.tierIndex;
    await this.updateCustomerTier(
      customer,
      loyaltyTier,
      isTierUpgrade
        ? this.loyaltyTierLogService.onManualTierUpgrade
        : this.loyaltyTierLogService.onManualTierDowngrade,
    );

    if (isTierUpgrade) {
      return await this.customerNotificationService.fireOnTierUpgradeTrigger(
        customer,
        newTierReplacements,
      );
    } else {
      return await this.customerNotificationService.fireOnTierDowngradeTrigger(
        customer,
        newTierReplacements,
      );
    }
  }

  private async getNewTierReplacements(
    customer: CustomerDocument,
    loyaltyTierId: Types.ObjectId,
  ): Promise<LoyaltyCustomerReplacementsDto> {
    const newTier = loyaltyTierId
      ? await this.loyaltyTierService.findById(loyaltyTierId)
      : null;
    const oldTier = customer.loyaltyTier;

    customer.loyaltyTier = newTier ? pick(newTier, embeddedTierFields) : null;
    const newTierReplacements =
      await this.customerReplacementsService.getCustomerReplacements(customer);
    customer.loyaltyTier = oldTier;

    return newTierReplacements;
  }

  private async notifyOnLoyaltyTierAssigned(
    customer: CustomerDocument,
    newTier: Types.ObjectId,
  ): Promise<void> {
    await this.updateCustomerTier(
      customer,
      newTier,
      this.loyaltyTierLogService.onManualTierUpgrade,
    );
    return await this.customerNotificationService.fireOnTierUpgradeTrigger(
      customer,
      await this.customerReplacementsService.getCustomerReplacements(customer),
    );
  }

  async handleCustomerDeletion(customer: CustomerDocument): Promise<void> {
    return await this.updateCustomerTier(
      customer,
      null,
      this.loyaltyTierLogService.onCustomerDeletion,
    );
  }

  async resetCustomerCalendarCycle(
    customer: CustomerDocument,
    company: CompanyDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<any> {
    if (!customer.loyaltyTier) {
      await this.updateCarryOverOrderRate(customer, company, calendarCycle);
      return { message: 'Customer has no loyalty tier set' };
    }

    const hasMaintainedTier =
      await this.customerLoyaltyTierInfoService.hasCustomerMaintainedTier(
        customer,
        calendarCycle,
      );

    if (hasMaintainedTier) {
      return await this.maintainCustomerTier(customer, company, calendarCycle);
    } else {
      return await this.demoteCustomerTier(customer, company, calendarCycle);
    }
  }

  private async updateTierBenefitsOnMaintain(customer: CustomerDocument) {
    this.moveTierBenefitsToEarned(customer);

    const loyaltyTier = await this.loyaltyTierService.findById(
      customer.loyaltyTier._id,
    );
    await this.addBenefitsToCustomer(customer, loyaltyTier.benefits, []);
  }

  private moveTierBenefitsToEarned(customer: CustomerDocument) {
    customer.usedBenefits = (customer.usedBenefits || []).concat(
      (customer.earnedBenefits || [])
        .filter(({ source }) => source === CustomerBenefitSource.TIER_PROGRAM)
        .map((benefit) => ({ ...benefit, usedAt: moment.utc().toDate() })),
    );

    customer.earnedBenefits = (customer.earnedBenefits || []).filter(
      ({ source }) => source !== CustomerBenefitSource.TIER_PROGRAM,
    );
  }

  private updateRemainingNumberOfUses(
    customer: CustomerDocument,
    oldTier?: EmbeddedTierDto,
    isBenefitUsagePreserved?: boolean,
  ) {
    if (!customer.loyaltyTier) return;

    const isPercentDiscountUsageLimited =
      customer.loyaltyTier.percentDiscountMaximumNumberOfUsesType ===
      NumberOfUsesType.LIMITED_USAGE;
    const isPercentDiscountUsagePreserved =
      isBenefitUsagePreserved &&
      oldTier &&
      oldTier.percentDiscountMaximumNumberOfUsesType ===
        NumberOfUsesType.LIMITED_USAGE &&
      oldTier.percentDiscountPreserveUsage;

    this.logger.log(
      'isPercentDiscountUsageLimited ',
      isPercentDiscountUsageLimited,
    );
    this.logger.log(
      'isPercentDiscountUsagePreserved ',
      isPercentDiscountUsagePreserved,
    );

    if (isPercentDiscountUsageLimited && isPercentDiscountUsagePreserved) {
      this.logger.log('customer current tier  ', customer.loyaltyTier);
      this.logger.log('customer old tier  ', oldTier);

      customer.loyaltyTier.percentDiscountRemainingNumberOfUses = Math.max(
        0,
        customer.loyaltyTier.percentDiscountMaximumNumberOfUses -
          oldTier.percentDiscountMaximumNumberOfUses +
          oldTier.percentDiscountRemainingNumberOfUses,
      );

      this.logger.log(
        'customer current tier  percentDiscountRemainingNumberOfUses ',
        customer.loyaltyTier.percentDiscountRemainingNumberOfUses,
      );
    } else if (isPercentDiscountUsageLimited)
      customer.loyaltyTier.percentDiscountRemainingNumberOfUses =
        customer.loyaltyTier.percentDiscountMaximumNumberOfUses;

    const isFreeDeliveryUsageLimited =
      customer.loyaltyTier.freeDeliveryMaximumNumberOfUsesType ===
      NumberOfUsesType.LIMITED_USAGE;
    const isFreeDeliveryUsagePreserved =
      isBenefitUsagePreserved &&
      oldTier &&
      oldTier.freeDeliveryMaximumNumberOfUsesType ===
        NumberOfUsesType.LIMITED_USAGE &&
      oldTier.freeDeliveryPreserveUsage;

    if (isFreeDeliveryUsageLimited && isFreeDeliveryUsagePreserved)
      customer.loyaltyTier.freeDeliveryRemainingNumberOfUses = Math.max(
        0,
        customer.loyaltyTier.freeDeliveryMaximumNumberOfUses -
          oldTier.freeDeliveryMaximumNumberOfUses +
          oldTier.freeDeliveryRemainingNumberOfUses,
      );
    else if (isFreeDeliveryUsageLimited)
      customer.loyaltyTier.freeDeliveryRemainingNumberOfUses =
        customer.loyaltyTier.freeDeliveryMaximumNumberOfUses;
  }

  private async updateCarryOverOrderRate(
    customer: CustomerDocument,
    company: CompanyDocument,
    calendarCycle?: CalendarCycle,
  ) {
    const hasCarryOver = this.companyService.hasCarryOver(company);

    if (hasCarryOver) {
      const { amountSpent, orderRate, pointsRate } =
        await this.customerLoyaltyTierInfoService.getLoyaltyTierProgramProgress(
          customer,
          company,
          calendarCycle ??
            this.calendarSystemService.getCalendarCycle(
              customer,
              company,
              CycleOffset.PREVIOUS_CYCLE,
            ),
        );

      customer.carryOverOrderRate = orderRate;
      customer.carryOverAmountSpent = amountSpent;
      customer.carryOverPointsRate = pointsRate;
    } else {
      customer.carryOverOrderRate = 0;
      customer.carryOverAmountSpent = 0;
      customer.carryOverPointsRate = 0;
    }
    customer.carryOverUpdatedAt = moment.utc().toDate();
    customer.tierUpdatedAt = moment.utc().toDate();
    customer.tierStatus = TierStatus.NO_TIER;
    customer.tier = { ...customer.tier, hasDowngradedToNoTier: false };
    await customer.save();
  }

  async computeCarryOverOnMaintain(
    customer: CustomerDocument,
    company: CompanyDocument,
    {
      orderRate: totalOrderRate,
      amountSpent: totalAmountSpent,
      pointsRate: totalPointsRate,
    }: LoyaltyTierProgramProgress,
    calendarCycle?: CalendarCycle,
  ): Promise<{
    carryOverOrderRate: number;
    carryOverAmountSpent: number;
    carryOverPointsRate: number;
  }> {
    const carryOver = {
      carryOverOrderRate: 0,
      carryOverAmountSpent: 0,
      carryOverPointsRate: 0,
    };

    if (!this.companyService.hasCarryOver(company)) return carryOver;

    const calendarSystem = company.loyaltyProgramConfig.calendarSystem;
    const isFixedCalendarSystem =
      this.calendarSystemService.isFixedCalendarSystem(calendarSystem);
    if (!isFixedCalendarSystem) return carryOver;

    const wasInGracePeriod =
      await this.customerLoyaltyTierInfoService.isInGracePeriod(
        customer,
        company,
        calendarCycle ??
          this.calendarSystemService.getCalendarCycle(
            customer,
            company,
            CycleOffset.PREVIOUS_CYCLE,
          ),
      );

    calendarCycle = calendarCycle ?? {
      startDate: wasInGracePeriod
        ? this.calendarSystemService.getGracePeriodStartDate(
            company,
            this.calendarSystemService.getStartDate(
              company,
              CycleOffset.PREVIOUS_CYCLE,
            ),
          )
        : this.calendarSystemService.getStartDate(
            company,
            CycleOffset.PREVIOUS_CYCLE,
          ),
      endDate: wasInGracePeriod
        ? this.calendarSystemService.getGracePeriodEndDate(
            calendarSystem,
            CycleOffset.PREVIOUS_CYCLE,
          )
        : this.calendarSystemService.getEndDate(
            customer,
            company,
            CycleOffset.PREVIOUS_CYCLE,
          ),
    };

    const orderRate =
      await this.customerLoyaltyTierInfoService.getLoyaltyOrderRate(
        customer,
        calendarCycle,
      );
    carryOver.carryOverOrderRate = wasInGracePeriod
      ? orderRate +
        Math.max(
          0,
          totalOrderRate -
            (customer.carryOverOrderRate || 0) -
            orderRate -
            (customer.loyaltyTier.orderRateThreshold || 0),
        )
      : clamp(
          0,
          totalOrderRate - (customer.loyaltyTier.orderRateThreshold || 0),
          orderRate,
        );

    const amountSpent =
      await this.customerLoyaltyTierInfoService.getLoyaltyAmountSpent(
        customer,
        calendarCycle,
      );
    carryOver.carryOverAmountSpent = wasInGracePeriod
      ? amountSpent +
        Math.max(
          0,
          totalAmountSpent -
            (customer.carryOverAmountSpent || 0) -
            amountSpent -
            (customer.loyaltyTier.amountSpentThreshold || 0),
        )
      : clamp(
          0,
          totalAmountSpent - (customer.loyaltyTier.amountSpentThreshold || 0),
          amountSpent,
        );

    const pointsRate =
      await this.customerLoyaltyTierInfoService.getLoyaltyPointsRate(
        customer,
        calendarCycle,
      );
    carryOver.carryOverPointsRate = wasInGracePeriod
      ? pointsRate +
        Math.max(
          0,
          totalPointsRate -
            (customer.carryOverPointsRate || 0) -
            pointsRate -
            (customer.loyaltyTier.pointsRateThreshold || 0),
        )
      : clamp(
          0,
          totalPointsRate - (customer.loyaltyTier.pointsRateThreshold || 0),
          pointsRate,
        );

    return carryOver;
  }

  private async maintainCustomerTier(
    customer: CustomerDocument,
    company: CompanyDocument,
    calendarCycle?: CalendarCycle,
  ) {
    const loyaltyTierProgramProgress =
      await this.customerLoyaltyTierInfoService.getLoyaltyTierProgramProgress(
        customer,
        company,
        calendarCycle ??
          this.calendarSystemService.getCalendarCycle(
            customer,
            company,
            CycleOffset.PREVIOUS_CYCLE,
          ),
      );

    const carryOver = await this.computeCarryOverOnMaintain(
      customer,
      company,
      loyaltyTierProgramProgress,
      calendarCycle ??
        this.calendarSystemService.getCalendarCycle(
          customer,
          company,
          CycleOffset.PREVIOUS_CYCLE,
        ),
    );

    customer.carryOverOrderRate = carryOver.carryOverOrderRate;
    customer.carryOverAmountSpent = carryOver.carryOverAmountSpent;
    customer.carryOverPointsRate = carryOver.carryOverPointsRate;
    customer.carryOverUpdatedAt = moment.utc().toDate();
    customer.tierUpdatedAt = moment.utc().toDate();
    customer.tier = { ...customer.tier, hasDowngradedToNoTier: false };

    const isTierMaintainedNextCycle =
      customer.loyaltyTier.isVipTier ||
      (customer.carryOverOrderRate >= customer.loyaltyTier.orderRateThreshold &&
        customer.loyaltyTier.orderRateThreshold) ||
      (customer.carryOverAmountSpent >=
        customer.loyaltyTier.amountSpentThreshold &&
        customer.loyaltyTier.amountSpentThreshold) ||
      (customer.carryOverPointsRate >=
        customer.loyaltyTier.pointsRateThreshold &&
        customer.loyaltyTier.pointsRateThreshold);
    customer.tierStatus = isTierMaintainedNextCycle
      ? TierStatus.MAINTAINED
      : TierStatus.NOT_MAINTAINED;

    await this.updateTierBenefitsOnMaintain(customer);
    this.updateRemainingNumberOfUses(customer);
    this.eventEmitter.emit('loyaltyTier.numberOfUses.updated', customer);

    customer.markModified('loyaltyTier');
    await customer.save();
    await this.loyaltyTierLogService.onTierComputationMaintain(customer);
    return {
      message: 'Customer tier maintained',
      loyaltyTierProgramProgress,
      orderRateThreshold: customer.loyaltyTier.orderRateThreshold,
      amountSpentThreshold: customer.loyaltyTier.amountSpentThreshold,
      pointsRateThreshold: customer.loyaltyTier.pointsRateThreshold,
      newCarryOverRate: customer.carryOverOrderRate,
      newCarryOverAmountSpent: customer.carryOverAmountSpent,
      newCarryOverPointsRate: customer.carryOverPointsRate,
      customer,
    };
  }

  private async demoteCustomerTier(
    customer: CustomerDocument,
    company: CompanyDocument,
    calendarCycle?: CalendarCycle,
  ) {
    const { orderRate, amountSpent, pointsRate } =
      await this.customerLoyaltyTierInfoService.getLoyaltyTierProgramProgress(
        customer,
        company,
        calendarCycle ??
          this.calendarSystemService.getCalendarCycle(
            customer,
            company,
            CycleOffset.PREVIOUS_CYCLE,
          ),
      );
    const oldCarryOverRate = customer.carryOverOrderRate;
    const oldCarryOverAmountSpent = customer.carryOverAmountSpent;
    const oldCarryOverPointsRate = customer.carryOverPointsRate;
    const oldTier = customer.loyaltyTier;

    // demotion only occurs one tier at a time, even if previous tier threshold is not met.
    const previousLoyaltyTier = await this.loyaltyTierService.findPreviousTier(
      customer.company._id,
      customer.loyaltyTier?._id,
    );

    customer.carryOverOrderRate = 0;
    customer.carryOverAmountSpent = 0;
    customer.carryOverPointsRate = 0;
    customer.carryOverUpdatedAt = moment.utc().toDate();
    await this.updateCustomerTier(
      customer,
      previousLoyaltyTier?._id || null,
      this.loyaltyTierLogService.onTierComputationDowngrade,
      false,
    );
    customer.tierStatus = TierStatus.NOT_MAINTAINED;
    customer.tier = {
      ...customer.tier,
      hasDowngradedToNoTier: !customer.loyaltyTier,
    };
    await customer.save();

    await this.customerNotificationService.fireOnTierDowngradeTrigger(
      customer,
      await this.customerReplacementsService.getCustomerReplacements(customer),
    );

    return {
      message: 'Customer tier demoted',
      totalLoyaltyOrderRate: orderRate,
      totalLoyaltyAmountSpent: amountSpent,
      totalLoyaltyPointsRate: pointsRate,
      orderRateThreshold: oldTier.orderRateThreshold,
      amountSpentThreshold: oldTier.amountSpentThreshold,
      pointsRateThreshold: oldTier.pointsRateThreshold,
      oldTier,
      newTier: customer.loyaltyTier,
      oldCarryOverRate,
      newCarryOverRate: customer.carryOverOrderRate,
      oldCarryOverAmountSpent,
      newCarryOverAmountSpent: customer.carryOverAmountSpent,
      oldCarryOverPointsRate,
      newCarryOverPointsRate: customer.carryOverPointsRate,
      customer,
    };
  }

  async bulkTierAssignment({
    tierId,
    notifyCustomers,
    currentUser,
    ...bulkActionDto
  }: BulkTierAssignmentDto): Promise<BulkActionResponseDto> {
    const response: BulkActionResponseDto = {
      success: 0,
      unchanged: 0,
      failed: 0,
    };
    // Making sure that the Tier exists
    const tier: LoyaltyTierDocument = tierId
      ? await this.loyaltyTierService.findById(tierId)
      : null;

    const customers: CustomerDocument[] =
      await this.customerIndexService.fetchEligibleCustomersForBulkAction(
        bulkActionDto,
      );

    const eligibleCustomers = customers.filter(
      (customer) =>
        !customer.loyaltyTier || !customer.loyaltyTier._id.equals(tier?._id),
    );
    response.unchanged = customers.length - eligibleCustomers.length;

    const fireTrigger = async (customer: CustomerDocument) => {
      await this.customerNotificationService.fireOnTierManualAssignmentTrigger(
        customer,
      );
    };

    const updateTier = async (customer: CustomerDocument) => {
      await this.updateCustomerTier(
        customer,
        tierId,
        (customer, previousTierId) =>
          this.loyaltyTierLogService.onBulkTierAssignment(
            customer,
            previousTierId,
            currentUser,
          ),
      );
    };

    for (let i = 0; i < eligibleCustomers.length; i++) {
      try {
        await updateTier(eligibleCustomers[i]);
        if (notifyCustomers) await fireTrigger(eligibleCustomers[i]);
        response.success++;
      } catch (exception) {
        response.failed++;
      }
    }

    return response;
  }

  private async removeCustomerBenefits(
    customer: CustomerDocument,
    benefits: LoyaltyTierBenefit[],
  ) {
    const benefitsSet = new Set(benefits.map((b) => b._id.toString()));
    const removedBenefits = (customer.earnedBenefits || []).filter(
      (b) =>
        benefitsSet.has(b._id.toString()) &&
        b.source === CustomerBenefitSource.TIER_PROGRAM,
    );

    await this.benefitService.removeEarnedBenefits(removedBenefits, customer);
  }

  private async addBenefitsToCustomer(
    customer: CustomerDocument,
    benefits: LoyaltyTierBenefit[],
    benefitUsages: BenefitUsage[],
  ) {
    const company = await this.companyService.findById(customer.company);

    const validTill =
      await this.customerLoyaltyTierInfoService.getTierValidTill(
        customer,
        company,
      );

    const earnedBenefits = await Promise.all(
      benefits.map(async (benefit) => {
        const benefitUsage = benefitUsages.find(
          this.benefitService.createBenefitUsageMatcher(
            benefit,
            CustomerBenefitSource.TIER_PROGRAM,
          ),
        );

        const redeemedCount =
          await this.loyaltyTransactionService.getRedeemedBenefitCount(
            customer,
            company,
            benefit._id,
          );

        return {
          ...benefit,
          earnedAt: moment.utc().toDate(),
          numberOfUsages: benefitUsage
            ? benefitUsage.numberOfUsages
            : redeemedCount,
          source: CustomerBenefitSource.TIER_PROGRAM,
          config: {
            ...benefit.config,
            expiryDate: validTill,
          },
        };
      }),
    );

    await this.benefitService.earn(earnedBenefits, customer);
  }

  async customerTierBenefitsUpdated(
    oldTier: LoyaltyTierDocument,
    newTier: LoyaltyTierDocument,
  ) {
    const customers = await this.customerRepository.findByLoyaltyTier(
      [newTier._id],
      newTier.companyId,
    );

    await mapAsync(customers, async (customer) => {
      await this.benefitService.onProgramBenefitUpdated(
        oldTier.benefits,
        newTier.benefits,
        CustomerBenefitSource.TIER_PROGRAM,
        customer,
      );
    });
  }
}
