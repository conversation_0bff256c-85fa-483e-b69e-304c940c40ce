import { ConfigurationDocument } from '../../../configuration/models/configuration.model';
import { ChatSendDto } from '../../types/dtos/chat-send.dto';
import {
  ChatConfigDto,
  TriggerUserDto,
  TemplateDocument,
} from '@app/shared-stuff';

export interface ChatServiceInterface {
  send(chatSendDto: ChatSendDto, configuration: ConfigurationDocument);
  sendUsingTemplate?(
    user: TriggerUserDto,
    template: TemplateDocument,
    configuration: ConfigurationDocument,
    replacement: Record<string, any>,
  );
  getTemplates?(chatConfigDto: ChatConfigDto);
}
export const ChatServiceInterface = Symbol('ChatServiceInterface');
