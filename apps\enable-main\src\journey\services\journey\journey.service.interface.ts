import {
  CreateBrand<PERSON>ourneyDto,
  CurrentUser,
  IndexJourneyDto,
  IndexJourneyResponseDto,
  JourneyDocument,
  UpdateBrandJourneyDto,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface JourneyServiceInterface {
  index(indexJourneyDto: IndexJourneyDto): Promise<IndexJourneyResponseDto[]>;
  create(
    createJourneyDto: CreateBrandJourneyDto,
    currentUser: CurrentUser,
  ): Promise<JourneyDocument>;
  getDetails(journeyId: Types.ObjectId): Promise<JourneyDocument>;
  update(
    journeyId: Types.ObjectId,
    updateJourneyDto: UpdateBrandJourneyDto,
  ): Promise<JourneyDocument>;
  delete(journeyId: Types.ObjectId): Promise<number>;
}

export const JourneyServiceInterface = Symbol('JourneyServiceInterface');
