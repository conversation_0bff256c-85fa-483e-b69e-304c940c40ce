import {
  BigCommerceFormField,
  BigCommerceOrderRawData,
  BigCommerceOrderStatus,
  BigCommercePaymentMethod,
  CountryDialCode,
  CreateBigCommerceOrderDto,
  Language,
  OrderCreationSource,
  OrderDeliveryAction,
  OrderDeliveryType,
  OrderDocument,
  OrderItem,
  OrderPaymentMethod,
  OrderPaymentStatus,
  OrderPosToCreate,
  OrderSource,
  OrderStatusEnum,
  responseCode,
  SavedLocationAddressType,
  SavedLocationToCreate,
  SavedLocationType,
} from '@app/shared-stuff';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';

import { ConfigService } from '@nestjs/config';
import axios, { AxiosResponse } from 'axios';
import { OrderPosService } from '../../../order/services/order-pos/order-pos.service';
import { OrderService } from '../../../order/services/order/order.service';
import { IntegrationLogRepositoryInterface } from '../../integration-log/repositories/interfaces/integration-log.repository.interface';
import { BigCommerceServiceInterface } from './big-commerce.service.interface';

@Injectable()
export class BigCommerceService implements BigCommerceServiceInterface {
  constructor(
    private orderPosService: OrderPosService,
    private configService: ConfigService,
    private orderService: OrderService,
    @Inject('IntegrationLogRepositoryInterface')
    private readonly integrationLogRepository: IntegrationLogRepositoryInterface,
  ) {}

  private async fetchBigCommerceOrderData(
    bigCommerceOrderId: number,
  ): Promise<CreateBigCommerceOrderDto> {
    const endpoint = `https://api.bigcommerce.com/stores/${this.configService.get(
      'BIGCOMMERCE_STORE_HASH',
    )}/v2/orders/${bigCommerceOrderId}`;
    const headers = {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      'X-Auth-Token': this.configService.get('BIGCOMMERCE_ACCESS_TOKEN'),
      'X-Auth-Client': this.configService.get('BIGCOMMERCE_CLIENT_ID'),
    };

    const response: AxiosResponse = await axios.get(endpoint, { headers });

    await this.integrationLogRepository.logSuccess(
      'big-commerce-order-details',
      { headers, endpoint },
      response.data,
      bigCommerceOrderId.toString(),
    );

    return response.data;
  }

  async createOrder(
    bigCommerceOrderRawData: BigCommerceOrderRawData,
    companyId: string,
    brand: string,
  ) {
    if (await this.checkIfOrderExists(bigCommerceOrderRawData.data.id)) {
      await this.integrationLogRepository.logSuccess(
        'big-commerce-order-exists',
        {
          bigCommerceOrderId: bigCommerceOrderRawData.data.id,
        },
        {},
        bigCommerceOrderRawData.data.id.toString(),
      );
      return;
    }
    const bigCommerceOrderData = await this.fetchBigCommerceOrderData(
      bigCommerceOrderRawData.data.id,
    );

    const orderItems = await this.getBigCommerceOrderItems(
      bigCommerceOrderRawData.data.id,
    );

    const orderPosToCreate = await this.constructOrderPosToCreate(
      bigCommerceOrderData,
      companyId,
      brand,
      orderItems,
    );

    await this.orderPosService.create(orderPosToCreate);
  }

  async checkIfOrderExists(bigCommerceOrderId: number): Promise<boolean> {
    const order =
      await this.orderService.findOneByBigCommerceOrderId(bigCommerceOrderId);
    return !!order;
  }

  private async getBigCommerceOrderItems(bigCommerceOrderId: number) {
    const endpoint = `https://api.bigcommerce.com/stores/${this.configService.get(
      'BIGCOMMERCE_STORE_HASH',
    )}/v2/orders/${bigCommerceOrderId}/products`;
    const headers = {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      'X-Auth-Token': this.configService.get('BIGCOMMERCE_ACCESS_TOKEN'),
      'X-Auth-Client': this.configService.get('BIGCOMMERCE_CLIENT_ID'),
    };

    const bigCommerceProducts: AxiosResponse = await axios.get(endpoint, {
      headers,
    });

    await this.integrationLogRepository.logSuccess(
      'big-commerce-order-items-details',
      { headers, endpoint },
      bigCommerceProducts.data,
      bigCommerceOrderId.toString(),
    );

    const orderItems: OrderItem[] = bigCommerceProducts.data.map((product) => {
      const orderItem = new OrderItem();
      orderItem.name = product.name;
      orderItem.plu = product.sku;
      orderItem.description = product.product_options
        ? product.product_options.reduce((acc: string, curr) => {
            return (acc += `${curr.display_name_merchant}: ${curr.display_value_merchant}, `);
          }, '')
        : ' ';
      orderItem.price = product.price_inc_tax;
      orderItem.basePrice = product.price_ex_tax;
      orderItem.quantity = product.quantity;
      orderItem.totalAmount = product.price_inc_tax * product.quantity;
      orderItem.totalAmountAfterDiscount =
        product.price_inc_tax * product.quantity;
      orderItem.itemReference = product.sku;
      orderItem.pickup_spot_id = product.sku.split('-')[0];
      return orderItem;
    });

    return orderItems;
  }

  private async getBigCommerceShippingDetails(bigCommerceOrderId: number) {
    const endpoint = `https://api.bigcommerce.com/stores/${this.configService.get(
      'BIGCOMMERCE_STORE_HASH',
    )}/v2/orders/${bigCommerceOrderId}/shipping_addresses`;
    const headers = {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      'X-Auth-Token': this.configService.get('BIGCOMMERCE_ACCESS_TOKEN'),
      'X-Auth-Client': this.configService.get('BIGCOMMERCE_CLIENT_ID'),
    };

    const bigCommerceShippingDetails: AxiosResponse = await axios.get(
      endpoint,
      { headers },
    );
    await this.integrationLogRepository.logSuccess(
      'big-commerce-order-shipping-details',
      { headers, endpoint },
      bigCommerceShippingDetails.data,
      bigCommerceOrderId.toString(),
    );

    return bigCommerceShippingDetails.data?.[0];
  }

  private async constructOrderPosToCreate(
    bigCommerceOrderData: CreateBigCommerceOrderDto,
    companyId: string,
    brand: string,
    orderItems: OrderItem[],
  ): Promise<OrderPosToCreate> {
    const shippingDetails = await this.getBigCommerceShippingDetails(
      bigCommerceOrderData.id,
    );

    const isDeliveryOrder = (shippingMethod: string) =>
      [
        'Express Delivery (Within 90 Minutes)',
        'Same Day Delivery',
        'Standard Delivery (1-2 days)',
      ].includes(shippingMethod);

    const needsDeliveryLocation = (formFields: BigCommerceFormField[]) => {
      const includesLatitude = formFields.some(
          (field) => field.name === 'Latitude' && field.value,
        ),
        includesLongitude = formFields.some(
          (field) => field.name === 'Longitude' && field.value,
        );

      return includesLatitude && includesLongitude;
    };

    const orderPosToCreate: OrderPosToCreate = {
      first_name:
        bigCommerceOrderData.billing_address.first_name ?? 'Anonymous',
      last_name: bigCommerceOrderData.billing_address.last_name,
      phone: bigCommerceOrderData.billing_address.phone
        ? bigCommerceOrderData.billing_address?.phone
        : '0000000000',
      email: bigCommerceOrderData.billing_address.email,
      country_code:
        bigCommerceOrderData.billing_address.country_iso2 ??
        CountryDialCode.QATAR,
      is_gift: false,
      isCustomerUpdatable: false,
      is_secret: false,
      recipient_name:
        bigCommerceOrderData.billing_address.first_name +
        ' ' +
        bigCommerceOrderData.billing_address.last_name,
      recipient_country_code:
        '+' + bigCommerceOrderData.billing_address.phone.substring(0, 3), // Assuming that the phone number is in the format "+CCxxxxxxxxxx",
      recipient_phone: bigCommerceOrderData.billing_address.phone.substring(3),
      cardMessage: '',
      pickup_date: '',
      pickup_time: '',
      delivery_date: bigCommerceOrderData.date_shipped,
      delivery_time: '',
      invoice_number:
        bigCommerceOrderData.external_id ?? bigCommerceOrderData.id,
      invoiced_amount: bigCommerceOrderData.subtotal_inc_tax,
      delivery_amount: bigCommerceOrderData.shipping_cost_inc_tax,
      total_amount: bigCommerceOrderData.total_inc_tax,
      discount: bigCommerceOrderData.discount_amount,
      total_amount_after_discount:
        bigCommerceOrderData.total_inc_tax -
        bigCommerceOrderData.discount_amount,
      order_remarks: bigCommerceOrderData.customer_message,
      payment_method: this.mapPaymentMethod(
        bigCommerceOrderData.payment_method,
      ),
      orderIsAlreadyPaid: false,
      paymentCode: '',
      callback_url: '',
      source: OrderSource.WEBSTORE,
      creationSource: OrderCreationSource.BIGCOMMERCE,
      orderType: 'simple',
      delivery_action: isDeliveryOrder(shippingDetails.shipping_method)
        ? needsDeliveryLocation(shippingDetails.form_fields)
          ? OrderDeliveryAction.DELIVERY_LOCATION
          : OrderDeliveryAction.SEND_SMS
        : OrderDeliveryAction.IN_STORE_PICKUP,
      branch: '',
      delivery_slot_id: '',
      delivery_slot_from: '',
      delivery_slot_to: '',
      deliveryMethod: undefined,
      transport_type: 'Scooter',
      deliveryParty: undefined,
      driver: '',
      driver_id: '',
      autoAssign: false,
      delivery_type: OrderDeliveryType.urgent,
      dispatch_type: OrderDeliveryType.urgent,
      deliveryLocation: needsDeliveryLocation(shippingDetails.form_fields)
        ? this.getDeliveryLocation(shippingDetails)
        : undefined,
      language: Language.english,
      items: orderItems,
      is_test: false,
      brandId: brand,
      deliverectPosOrderId: '',
      status: this.mapBigCommerceOrderStatusToEnableOrderStatus(
        bigCommerceOrderData.status_id,
      ), // note we need to assign a branch from the branchId (ask Youssef)
      code: '',
      payment_status: OrderPaymentStatus.PENDING,
      current_user: null,
      customer: '',
      company: companyId,
      traceId: '',
      bigcommerceOrderId: bigCommerceOrderData.id,
    };

    await this.integrationLogRepository.logSuccess(
      'big-commerce-order-pos-to-create',
      orderPosToCreate,
      {},
      bigCommerceOrderData.id.toString(),
    );

    return orderPosToCreate;
  }

  private getDeliveryLocation(
    bigCommerceShippingDetails: any,
  ): SavedLocationToCreate {
    const findField = (name: string) =>
      bigCommerceShippingDetails.form_fields.find(
        (field) => field.name === name,
      )?.value ?? 'N/A';

    return {
      area: bigCommerceShippingDetails.state ?? 'Doha',
      type: SavedLocationType.PIN_LOCATION,
      latitude: findField('Latitude'),
      longitude: findField('Longitude'),
      city: bigCommerceShippingDetails.city ?? 'Doha',
      country: bigCommerceShippingDetails.country_iso2 ?? 'Qatar',
      pinLink: findField('Google Maps Link'),
      nearestLandmark: bigCommerceShippingDetails.shipping_zone_name,
      nickname: bigCommerceShippingDetails.first_name ?? 'Anonymous',
      additionalInfo: bigCommerceShippingDetails.street_1 ?? 'N/A',
      addressType: SavedLocationAddressType.HOUSE,
      buildingName: 'N/A',
      buildingNumber: 100,
      floorNumber: 100,
      streetName: 'N/A',
      streetNumber: 100,
      unitNumber: 100,
      zoneNumber: 100,
      customerId: '',
    };
  }

  private mapPaymentMethod(paymentMethod: string): string {
    const paymentMethodMap = new Map<string, OrderPaymentMethod>([
      [BigCommercePaymentMethod.CASH, OrderPaymentMethod.cash],
      [BigCommercePaymentMethod.MANUAL, OrderPaymentMethod.prepaid],
      [BigCommercePaymentMethod.CREDIT_CARD, OrderPaymentMethod.card_machine],
      [
        BigCommercePaymentMethod.TEST_PAYMENT_GATEWAY,
        OrderPaymentMethod.prepaid,
      ],
    ]);
    if (!paymentMethodMap.has(paymentMethod)) {
      return OrderPaymentMethod.prepaid;
    }
    return paymentMethodMap.get(paymentMethod);
  }

  async updateOrderStatus(order: OrderDocument) {
    if (!order.bigcommerceOrderId) return;
    let mappedStatusId;
    if (order.payment_status !== OrderPaymentStatus.REFUNDED)
      mappedStatusId = this.mapOrderStatus(order.status);
    else mappedStatusId = BigCommerceOrderStatus.REFUNDED;

    const headers = {
      'X-Auth-Token': this.configService.get('BIGCOMMERCE_ACCESS_TOKEN'),
      Accept: 'application/json',
      'Content-Type': 'application/json',
    };

    const axiosInstance = axios.create({
      baseURL: `https://api.bigcommerce.com/stores/${this.configService.get(
        'BIGCOMMERCE_STORE_HASH',
      )}/v2`,
      headers: headers,
    });

    const endpoint = `/orders/${order.bigcommerceOrderId}`;
    const data = {
      status_id: mappedStatusId,
    };

    try {
      const response = await axiosInstance.put(endpoint, data);
      await this.integrationLogRepository.logSuccess(
        'big-commerce-update-order-status',
        { body: data, endpoint, headers },
        response.data,
        order.bigcommerceOrderId.toString(),
      );
    } catch (error: any) {
      await this.integrationLogRepository.logSuccess(
        'big-commerce-update-order-status',
        { body: data, endpoint, headers },
        error.response,
        order.bigcommerceOrderId.toString(),
      );
    }
  }

  private mapOrderStatus(status: OrderStatusEnum) {
    const orderStatusMap = new Map<OrderStatusEnum, BigCommerceOrderStatus>([
      [OrderStatusEnum.UNASSIGNED, BigCommerceOrderStatus.PENDING],
      [OrderStatusEnum.PENDING, BigCommerceOrderStatus.PENDING],
      [OrderStatusEnum.PREPARING, BigCommerceOrderStatus.AWAITING_FULFILLMENT],
      [OrderStatusEnum.PENDING_PICKUP, BigCommerceOrderStatus.AWAITING_PICKUP],
      [OrderStatusEnum.IN_ROUTE, BigCommerceOrderStatus.SHIPPED],
      [OrderStatusEnum.COMPLETED, BigCommerceOrderStatus.COMPLETED],
      [OrderStatusEnum.CANCELED, BigCommerceOrderStatus.CANCELLED],
      [OrderStatusEnum.FAILED, BigCommerceOrderStatus.DECLINED],
      [OrderStatusEnum.SCHEDULED, BigCommerceOrderStatus.INCOMPLETE],
    ]);
    if (!orderStatusMap.has(status)) {
      throw new BadRequestException(
        'Invalid status',
        responseCode.IN_VALID_INPUT.toString(),
      );
    }
    return orderStatusMap.get(status);
  }

  private mapBigCommerceOrderStatusToEnableOrderStatus(
    status: BigCommerceOrderStatus,
  ) {
    const orderStatusMap = new Map<BigCommerceOrderStatus, OrderStatusEnum>([
      [BigCommerceOrderStatus.PENDING, OrderStatusEnum.PENDING],
      [BigCommerceOrderStatus.AWAITING_FULFILLMENT, OrderStatusEnum.PREPARING],
      [BigCommerceOrderStatus.AWAITING_PICKUP, OrderStatusEnum.PENDING_PICKUP],
      [BigCommerceOrderStatus.SHIPPED, OrderStatusEnum.IN_ROUTE],
      [BigCommerceOrderStatus.COMPLETED, OrderStatusEnum.COMPLETED],
      [BigCommerceOrderStatus.CANCELLED, OrderStatusEnum.CANCELED],
      [BigCommerceOrderStatus.DECLINED, OrderStatusEnum.FAILED],
      [BigCommerceOrderStatus.AWAITING_PAYMENT, OrderStatusEnum.PENDING],
      [
        BigCommerceOrderStatus.AWAITING_SHIPMENT,
        OrderStatusEnum.PENDING_PICKUP,
      ],
      [BigCommerceOrderStatus.DECLINED, OrderStatusEnum.CANCELED],
      [BigCommerceOrderStatus.REFUNDED, OrderStatusEnum.COMPLETED],
      [BigCommerceOrderStatus.DISPUTED, OrderStatusEnum.COMPLETED],
      [
        BigCommerceOrderStatus.MANUAL_VERIFICATION_REQUIRED,
        OrderStatusEnum.PREPARING,
      ],
      [BigCommerceOrderStatus.PARTIALLY_REFUNDED, OrderStatusEnum.COMPLETED],
      [BigCommerceOrderStatus.INCOMPLETE, OrderStatusEnum.PENDING],
    ]);
    if (!orderStatusMap.has(status)) {
      return OrderStatusEnum.PENDING;
    }
    return orderStatusMap.get(status);
  }
}
