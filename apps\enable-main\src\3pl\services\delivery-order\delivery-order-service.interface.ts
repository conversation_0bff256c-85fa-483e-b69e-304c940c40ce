import {
  AssignDriverDto,
  CreateDeliveryOrderDto,
  DeliveryOrder,
  DeliveryOrderDocument,
  DistributionCenterDocument,
  GetAllDeliveryOrderDto,
  IndexResultDto,
  TempCustomerDocument,
  UpdateDeliveryOrderStatusDto,
} from '@app/shared-stuff';
import { Types } from 'mongoose';
import { TookanWebhookPayload } from '../../../delivery/types/tookan-webhook-payload.type';

export interface DeliveryOrderServiceInterface {
  findAll(
    getAllDeliveryOrderDto: GetAllDeliveryOrderDto,
  ): Promise<IndexResultDto<DeliveryOrder>[]>;

  create(
    createDeliveryOrderDto: CreateDeliveryOrderDto,
  ): Promise<DeliveryOrderDocument>;

  findOne(id: Types.ObjectId): Promise<DeliveryOrderDocument>;

  getSalesRelated(salesOrderId: string): Promise<{
    distributionCenters: DistributionCenterDocument[];
    customer: TempCustomerDocument;
  }>;

  findOneByCode(code: string): Promise<DeliveryOrderDocument>;

  deliveryTaskUpdated(
    deliveryOrder: DeliveryOrderDocument,
    deliveryTaskPayload: TookanWebhookPayload,
  ): Promise<void>;

  updateStatus(
    updateDeliveryOrderStatusDto: UpdateDeliveryOrderStatusDto,
  ): Promise<void>;

  assignDriverToTask(assignDriverDto: AssignDriverDto): Promise<void>;
}

export const DeliveryOrderServiceInterface = Symbol(
  'DeliveryOrderServiceInterface',
);
