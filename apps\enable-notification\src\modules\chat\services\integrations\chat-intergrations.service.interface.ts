import { ChatConfigDto, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SuccessResult } from '@app/shared-stuff';
import { ChatSendDto } from '../../types/dtos/chat-send.dto';
import { SendChatUsingTemplateDto } from '../../types/dtos/send-chat-using-template.dto';

export interface ChatIntegrationsServiceInterface {
  send(
    chatSendDto: ChatSendDto,
    chatConfigDto: ChatConfigDto,
  ): Promise<SuccessResult | ErrorResult>;

  getTemplates?(chatConfigDto: ChatConfigDto): Promise<any>;

  sendUsingTemplate?(
    chatSendDto: SendChatUsingTemplateDto,
    chatConfigDto: ChatConfigDto,
  ): Promise<any>;
}
