import {
  Brand,
  CollectionName,
  LogError,
  LoggerService,
  MyFatoorahGatewayConfiguration,
  OrderDocument,
  OrderSource,
  PaymentDocument,
  PaymentGatewayConfig,
  PaymentGatewayType,
  PaymentStatusEnum,
  responseCode,
  YesOrNo,
} from '@app/shared-stuff';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import * as xl from 'excel4node';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import { PaymentIntegrationType } from '../../../../../../libs/shared-stuff/src/enums/payment/payment-integration-type.enum';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { CompanyService } from '../../../company/services/company/company.service';
import { CustomerIndexServiceInterface } from '../../../customer/modules/customer-index/customer-index.service.interface';
import { CustomerWriteServiceInterface } from '../../../customer/modules/customer-write/customer-write.service.interface';
import { ShortenUrlServiceInterface } from '../../../integration/shorten-url/services/shorten-url.service.interface';
import { PaymentIndex } from '../../../payment/dto/payment.dto';
import { GoogleCloudStorageService } from '../../../storage/google-cloud-storage.service';
import { ENABLE_TECH_URL } from '../../constants/payment-callback.const';
import { PaymentSearchTypeMapping } from '../../constants/payment-search-type-mapping.const';
import { PaymentSortMapping } from '../../constants/payment-sort-mapping.const';
import { PaymentTypeMapping } from '../../constants/payment-type-mapping.const';
import { ClonePaymentDto } from '../../dto/clone-payment.dto';
import { PaymentBranchToAssign } from '../../dto/payment-branch-to-assigned.dto';
import { PaymentToCreate } from '../../dto/payment-to-create.dto';
import { PaymentToProcess } from '../../dto/payment-to-process.dto';
import { PaymentToBeRefunded } from '../../dto/payment-to-refund.dto';
import { PaymentIndexType } from '../../enums/payment-index-type.enum';
import { PaymentRepositoryInterface } from '../../repositories/interfaces/payment.repository.interface';
import { PaymentConfigurationServiceInterface } from '../payment-configuration/payment-configuration.service.interface';
import { PaymentCbpayService } from '../payment-integrations/payment-cbpay/payment-cbpay.service';
import { PaymentCyberSourceService } from '../payment-integrations/payment-cyber-source/payment-cyber-source.service';
import { PaymentDibsyService } from '../payment-integrations/payment-dibsy/payment-dibsy.service';
import { PaymentIntegrationServiceInterface } from '../payment-integrations/payment-integration.service.interface';
import { PaymentMyFatoorahService } from '../payment-integrations/payment-myfatoorah/payment-myfatoorah.service';
import { PaymentStripeService } from '../payment-integrations/payment-stripe/payment-stripe.service';
import { PaymentTapService } from '../payment-integrations/payment-tap/payment-tap.service';
import { PaymentVistamoneyService } from '../payment-integrations/payment-vistamoney/payment-vistamoney.service';
import { BranchService } from './../../../branch/services/branch/branch.service';
import { PaymentSkipCashService } from './../payment-integrations/payment-skip-cash/payment-skip-cash.service';
import { PaymentNotificationService } from './../payment-notification/payment-notification.service';
import { OnEvent } from '@nestjs/event-emitter';

@Injectable()
export class PaymentService {
  private readonly loggerService = new LoggerService(PaymentService.name);

  constructor(
    @Inject(CustomerIndexServiceInterface)
    private customerIndexService: CustomerIndexServiceInterface,
    @Inject(CustomerWriteServiceInterface)
    private customerWriteService: CustomerWriteServiceInterface,
    private configService: ConfigService,
    private companyService: CompanyService,
    private branchService: BranchService,
    private paymentVistamoneyService: PaymentVistamoneyService,
    private paymentCbPayService: PaymentCbpayService,
    private paymentDibsyService: PaymentDibsyService,
    @InjectModel(CollectionName.ORDER)
    private paymentOrderModel: Model<OrderDocument>,
    private paymentNotificationService: PaymentNotificationService,
    private paymentCyberSource: PaymentCyberSourceService,
    private paymentStripeService: PaymentStripeService,
    private paymentSkipCashService: PaymentSkipCashService,
    private paymentTapService: PaymentTapService,
    @Inject('BrandServiceInterface')
    private brandService: BrandServiceInterface,
    private readonly paymentMyFatoorah: PaymentMyFatoorahService,
    @Inject('PaymentRepositoryInterface')
    private readonly paymentRepository: PaymentRepositoryInterface,
    @Inject('PaymentTESSService')
    private readonly paymentTESSService: PaymentIntegrationServiceInterface,
    @Inject(PaymentConfigurationServiceInterface)
    private readonly paymentConfigurationService: PaymentConfigurationServiceInterface,
    @Inject(ShortenUrlServiceInterface)
    private shortenUrlService: ShortenUrlServiceInterface,
    private readonly googleCloudStorageService: GoogleCloudStorageService,
  ) {}

  async refactoredIndex(paymentToIndex: PaymentIndex) {
    const aggregation = [
        {
          $project: {
            cbPayTransactionIds: 0,
            successReconciled: 0,
            reconciled: 0,
            statusChanges: 0,
            paymentTries: 0,
            tracing: 0,
            track_id: 0,
          },
        },
      ] as any,
      match = { deletedAt: null };
    let sort = {};

    if (paymentToIndex.company) {
      match['company'] = new Types.ObjectId(paymentToIndex.company);
    }

    if (paymentToIndex.month) {
      match['month'] = paymentToIndex.month;
    }

    if (paymentToIndex.status) {
      const statues = paymentToIndex.status.split(',');
      match['status'] = { $in: statues };
    }

    if (paymentToIndex.branch) {
      match['branch'] = new Types.ObjectId(paymentToIndex.branch);
    }

    if (paymentToIndex.gatewayType) {
      match['gatewayType'] = paymentToIndex.gatewayType;
    }

    if (paymentToIndex.brandId) {
      match['brand._id'] = new Types.ObjectId(paymentToIndex.brandId);
    }

    if (
      paymentToIndex.branches &&
      paymentToIndex.type != PaymentIndexType.UNASSIGNED &&
      paymentToIndex.type != PaymentIndexType.ALL
    ) {
      match['branch'] = { $in: paymentToIndex.branches, $exists: true };
    }

    if (paymentToIndex.companyIds) {
      const companyIdsArray = paymentToIndex.companyIds.split(',');
      const companyIds = this.convertCompanyIdsToObjectIds(companyIdsArray);
      match['company'] = { $in: companyIds, $exists: true };
    }

    if (paymentToIndex.createdDateFrom && paymentToIndex.createdDateTo) {
      const from = moment.utc(paymentToIndex.createdDateFrom).toDate();

      const isDateOnly = moment
        .utc(paymentToIndex.createdDateTo, 'YYYY-MM-DD', true)
        .isValid();

      const to = isDateOnly
        ? moment.utc(paymentToIndex.createdDateTo).endOf('day').toDate()
        : moment.utc(paymentToIndex.createdDateTo).toDate();

      match['createdAt'] = { $gte: from, $lte: to };
    }

    if (
      paymentToIndex.transactionDateFrom &&
      paymentToIndex.transactionDateTo
    ) {
      const from = moment.utc(paymentToIndex.transactionDateFrom).toDate();

      const isDateOnly = moment
        .utc(paymentToIndex.createdDateTo, 'YYYY-MM-DD', true)
        .isValid();

      const to = isDateOnly
        ? moment.utc(paymentToIndex.transactionDateTo).endOf('day').toDate()
        : moment.utc(paymentToIndex.transactionDateTo).toDate();

      match['transaction_date'] = { $gt: from, $lt: to };
    }

    if (paymentToIndex.type) {
      const typeMapped = PaymentTypeMapping[paymentToIndex.type];
      const keys = Object.keys(typeMapped);
      keys.map((key) => {
        match[key] = typeMapped[key];
      });
    }

    if (paymentToIndex.search_type && paymentToIndex.search_key) {
      const availableFieldsKeys = Object.keys(PaymentSearchTypeMapping);
      if (
        availableFieldsKeys.includes(paymentToIndex.search_type) &&
        paymentToIndex.search_key
      ) {
        if (paymentToIndex.search_type == 'amount') {
          match['amount'] = parseFloat(paymentToIndex.search_key);
        } else {
          match[PaymentSearchTypeMapping[paymentToIndex.search_type]] = {
            $regex: paymentToIndex.search_key.toLowerCase(),
            $options: 'i',
          };
        }
      } else if (paymentToIndex.search_type === 'all') {
        match['$or'] = availableFieldsKeys.map((field) => {
          if (field == 'amount') {
            return {
              amount: parseFloat(paymentToIndex.search_key),
            };
          }
          return {
            [PaymentSearchTypeMapping[field]]: {
              $regex: paymentToIndex.search_key.toLowerCase(),
              $options: 'i',
            },
          };
        });
      }
    }

    if (paymentToIndex.sort_type) {
      sort = PaymentSortMapping[paymentToIndex.sort_type]
        ? PaymentSortMapping[paymentToIndex.sort_type]
        : { createdAt: -1 };
    } else {
      sort = { createdAt: -1 };
    }

    if (paymentToIndex.payment_status) {
      match['status'] = paymentToIndex.payment_status;
    }

    if (paymentToIndex.payment_id) {
      match['code'] = paymentToIndex.payment_id;
    }

    if (paymentToIndex.testing) {
      if (paymentToIndex.testing == YesOrNo.YES) {
        match['is_test'] = true;
      } else {
        match['is_test'] = false;
      }
    }

    if (paymentToIndex.payment_method) {
      match['payment_method'] = paymentToIndex.payment_method;
    }

    if (paymentToIndex.payment_method_used) {
      match['payment_method_used'] = paymentToIndex.payment_method_used;
    }

    if (paymentToIndex.isRefunded) {
      if (paymentToIndex.isRefunded == YesOrNo.NO) {
        match['isRefunded'] = false;
      } else {
        match['isRefunded'] = true;
      }
    }

    aggregation.push({ $match: match });

    aggregation.push({
      $facet: {
        paginatedResult: [
          { $sort: sort },
          ...(Number(paymentToIndex.offset) ||
          Number(paymentToIndex.offset) == 0
            ? [
                {
                  $skip:
                    Number(paymentToIndex.offset) *
                    Number(paymentToIndex.limit),
                },
              ]
            : [
                {
                  $skip: 0,
                },
              ]),
          ...(Number(paymentToIndex.limit)
            ? [
                {
                  $limit: Number(paymentToIndex.limit),
                },
              ]
            : []),
          {
            $lookup: {
              from: 'branches',
              let: { branchId: '$branch' },
              pipeline: [
                {
                  $match: {
                    $expr: { $and: [{ $eq: ['$_id', '$$branchId'] }] },
                  },
                },
                {
                  $project: {
                    name: 1,
                    _id: 1,
                    email: 1,
                    phone: 1,
                    acronym: 1,
                  },
                },
              ],
              as: 'branch',
            },
          },
          { $unwind: { path: '$branch', preserveNullAndEmptyArrays: true } },
        ],
        totalCount: [
          {
            $count: 'createdAt',
          },
        ],
      },
    });

    // Logger.log(aggregation)

    const payments = await this.paymentRepository.aggregate(aggregation);
    return payments;
  }

  async create(paymentToCreate: PaymentToCreate, current) {
    const customer = await this.customerWriteService.findOrCreate({
      first_name: paymentToCreate.customer_name,
      last_name: ' ',
      phone: paymentToCreate.customer_phone,
      country_code: paymentToCreate.country_code,
      email: '',
      company: new Types.ObjectId(paymentToCreate.company),
      contact_channel: OrderSource.WHATSAPP,
      full_name: paymentToCreate.customer_name,
      location: undefined,
      createdBy: current,
      firstBrandOrderdId: null,
      firstBranchOrderdId: null,
    });

    const company = await this.companyService.get_details(
      paymentToCreate.company instanceof Types.ObjectId
        ? paymentToCreate.company.toHexString()
        : paymentToCreate.company,
    );

    if (!paymentToCreate.language) {
      paymentToCreate.language = 'english';
    }

    customer.country_code = paymentToCreate.country_code;
    paymentToCreate.customer = customer._id;
    paymentToCreate.customer_name = customer.full_name;
    paymentToCreate.company_name = company.name;
    paymentToCreate.source = paymentToCreate.source ?? OrderSource.WEBSTORE;

    if (paymentToCreate.branch_id) {
      paymentToCreate.branch = paymentToCreate.branch_id;
    }

    if (paymentToCreate.brandId) {
      const brand = await this.brandService.findById(
        new Types.ObjectId(paymentToCreate.brandId),
      );
      if (brand) {
        paymentToCreate.brand = {
          _id: brand._id,
          name: brand.name,
          phoneNumber: brand.phoneNumber,
          senderId: brand.senderId,
          image: brand.image,
          emailSenderId: brand.emailSenderId,
        };
      }
    }

    //Payment Has a branch
    if (paymentToCreate.branch) {
      const branch = await this.branchService.findById(
        paymentToCreate.branch instanceof Types.ObjectId
          ? paymentToCreate.branch
          : new Types.ObjectId(paymentToCreate.branch),
      );
      if (!branch.number_of_payments) {
        branch.number_of_payments = 0;
      }
      branch.number_of_payments++;
      await branch.save();
      paymentToCreate.branch = branch._id;
      paymentToCreate['branchName'] = branch.name;

      const isBrandGiven = paymentToCreate.brandId || paymentToCreate.brand;
      if (!isBrandGiven && branch.brands.length === 1) {
        paymentToCreate.brand = {
          senderId: branch.senderId ?? company.senderId ?? 'ENABLE',
          emailSenderId: branch.email ?? company.email,
          ...branch.brands[0],
        };
        paymentToCreate.brandId = paymentToCreate.brand._id;
      }
    } else {
      delete paymentToCreate.branch;
    }

    // paymentToCreate.code = randomstring.generate(5).toLowerCase();
    company.number_of_payments = company.number_of_payments + 1;
    paymentToCreate.code =
      (company.acronym ? company.acronym : 'BNO') +
      '-P-' +
      company.number_of_payments.toString().padStart(5, '0');
    await company.save();

    paymentToCreate['month'] = moment().format('MMMM');
    const newPaymentLink = await this.paymentRepository.create(
      paymentToCreate as any,
    );

    newPaymentLink.createdBy = current;
    newPaymentLink.localization = company.localization;
    newPaymentLink.integrationInfo = {
      ...(newPaymentLink.integrationInfo ?? {}),
      callbackUrl: '',
    };
    customer.latestPayment = newPaymentLink._id;
    customer.latestPaymentDate = moment.utc().toDate();
    await Promise.all([newPaymentLink.save(), company.save(), customer.save()]);

    await this.setExtraPaymentProperties(newPaymentLink);

    await this.setPaymentShortenUrl(newPaymentLink);

    //check if the payment without Branch

    if (paymentToCreate.fireTrigger == true) {
      const payment = await this.get_details(newPaymentLink.code);
      await this.paymentNotificationService.onSendPaymentLink(payment);
    }

    return newPaymentLink;
  }

  async processPayment(paymentToProcess: PaymentToProcess) {
    const payment = await this.paymentRepository.findByIdWithCustomer(
      paymentToProcess.paymentId,
    );

    if (!payment) {
      throw {
        code: responseCode.MISSING_DATA,
        message: `Payment with paymentId ${paymentToProcess.paymentId} not found`,
        statusCode: 404,
      };
    }

    if (payment.status == PaymentStatusEnum.TRANSACTION_COMPLETED) {
      throw {
        code: responseCode.TOKEN_EXPIRED,
        message: 'The Payment is already successfully',
        statusCode: 404,
      };
    }

    const company = await this.companyService.get_details(payment.company);

    const paymentConfig =
      await this.paymentConfigurationService.findPaymentConfig(
        payment.branch?._id,
        payment.brand?._id,
        company?._id,
      );

    const gatewayConfig: PaymentGatewayConfig =
      paymentConfig.configuration[paymentToProcess.paymentMethod];

    if (!gatewayConfig) {
      throw new BadRequestException(
        'No payment configuration found for this payment method',
      );
    }

    const gatewayType = gatewayConfig.paymentGatewayType;

    //TODO: Update the payment from the repository
    payment.payment_method = paymentToProcess.paymentMethod;
    await payment.save();

    let callBackURL = ENABLE_TECH_URL;

    if (gatewayType === PaymentGatewayType.CYBER_SOURCE) {
      return await this.paymentCyberSource.processPayment(payment);
    } else if (gatewayType === PaymentGatewayType.VISTA) {
      callBackURL = await this.paymentVistamoneyService.processPayment(
        payment,
        gatewayConfig.configuration,
        paymentToProcess,
      );
    } else if (gatewayType == PaymentGatewayType.CBPAY) {
      return await this.paymentCbPayService.processPayment(payment);
    } else if (gatewayType == PaymentGatewayType.SKIP_CASH) {
      callBackURL = await this.paymentSkipCashService.processPayment(
        payment,
        gatewayConfig.configuration,
      );
    } else if (gatewayType == PaymentGatewayType.TESS) {
      callBackURL = await this.paymentTESSService.processPayment(
        payment,
        company,
      );
    } else if (
      gatewayConfig.paymentGatewayType == PaymentGatewayType.MY_FATOORAH &&
      gatewayConfig.configuration.integrationType ===
        PaymentIntegrationType.REDIRECT
    ) {
      callBackURL = await this.paymentMyFatoorah.sendPaymentLink(
        payment,
        gatewayConfig,
      );
    } else if (
      gatewayConfig.paymentGatewayType == PaymentGatewayType.DIBSY &&
      gatewayConfig.configuration.integrationType ===
        PaymentIntegrationType.REDIRECT
    ) {
      callBackURL = await this.paymentDibsyService.createPaymentLink(
        payment,
        gatewayConfig,
      );
    } else if (gatewayConfig.paymentGatewayType == PaymentGatewayType.TAP) {
      callBackURL = await this.paymentTapService.createPaymentLink(
        payment,
        gatewayConfig.configuration,
      );
    }

    if (!callBackURL)
      throw new BadRequestException(
        `Payment could not be processed from gateway ${gatewayType}`,
      );

    return callBackURL;
  }

  async assignPaymentToBranch(paymentBranchToAssign: PaymentBranchToAssign) {
    const branch = await this.branchService.findById(
      new Types.ObjectId(paymentBranchToAssign.branch),
    );
    const payment = await this.get_details(paymentBranchToAssign.payment);

    if (paymentBranchToAssign.old_branch) {
      await this.branchService.decrementNumberOfPayments(
        paymentBranchToAssign.old_branch,
      );
    }

    payment.branch = branch._id;
    payment.branchName = branch.name;
    if (!branch.number_of_payments) {
      branch.number_of_payments = 0;
    }
    branch.number_of_payments++;
    await Promise.all([payment.save(), branch.save()]);
    return payment;
  }

  async resendPaymentLink(paymentCode) {
    const currentPayment =
      await this.paymentRepository.findByCodeWithPaymentLinkDetails(
        paymentCode,
      );
    await this.paymentNotificationService.onSendPaymentLink(currentPayment);
  }

  async exportExcel(paymentToIndex: PaymentIndex) {
    const wb = new xl.Workbook();
    const ws = wb.addWorksheet('Payments');
    const paymentsPaginated = await this.refactoredIndex(paymentToIndex);
    const payments = paymentsPaginated[0]['paginatedResult'];
    const company = await this.companyService.get_details(
      paymentToIndex.company,
    );

    const headers = [
      'Id',
      'Order Code',
      'Customer Name',
      'Customer Phone',
      'Amount',
      'Language',
      'Card Type',
      'Date Created',
      'Payment Status',
      'Payment Link',
      'Transaction Date',
      'Source',
      'Transaction Id',
    ];
    const style = wb.createStyle({
      font: {
        color: '#FF0800',
        size: 12,
      },
      numberFormat: '$#,##0.00; ($#,##0.00); -',
    });
    for (let i = 1; i <= headers.length; i++) {
      ws.cell(1, i)
        .string(headers[i - 1])
        .style(style);
    }
    for (let i = 0; i < payments.length; i++) {
      ws.cell(i + 2, 1).string(payments[i]['code'].toString());
      ws.cell(i + 2, 2).string(payments[i]['order_code']?.toString());
      ws.cell(i + 2, 3).string(payments[i]['customer_name']?.toString());
      ws.cell(i + 2, 4).string(payments[i]['customer_phone']?.toString());
      ws.cell(i + 2, 5).number(payments[i]['amount']);
      ws.cell(i + 2, 6).string(payments[i]['language']?.toString());
      ws.cell(i + 2, 7).string(payments[i]['payment_method']?.toString());
      ws.cell(i + 2, 8).string(
        moment(payments[i]['createdAt'])
          .tz(company.localization.timezone ?? 'Asia/Qatar')
          .format('YYYY-MM-DD hh:mm a'),
      );
      ws.cell(i + 2, 9).string(payments[i]['status']?.toString());

      const paymentLink =
        this.configService.get('PAY_PAGE') + '/' + payments[i]['code'];
      ws.cell(i + 2, 10).string(paymentLink.toString());

      if (payments[i]['transaction_date']) {
        ws.cell(i + 2, 11).string(
          moment(payments[i]['transaction_date'])
            .tz(company.localization.timezone ?? 'Asia/Qatar')
            .format('YYYY-MM-DD hh:mm a'),
        );
      }
      ws.cell(i + 2, 12).string(payments[i]['source']?.toString());
      ws.cell(i + 2, 12).string(payments[i]['transaction_id']?.toString());
    }
    const fileName =
      payments[0].company_name +
      '-Payments-' +
      moment().format('DD-MM-YYYY HH-mm-ss').toString() +
      '.xlsx';

    await this.googleCloudStorageService.uploadDocument(
      await wb.writeToBuffer(),
      fileName,
    );

    return `/documents/${fileName}`;
  }

  async get_details(code: string) {
    return await this.paymentRepository.findByCodeOrIdWithDetails(code);
  }

  async findOne(uniqueIdentifier: string): Promise<PaymentDocument> {
    const payment =
      await this.paymentRepository.findByCodeOrIdWithFullDetails(
        uniqueIdentifier,
      );
    if (
      payment &&
      (payment.status == PaymentStatusEnum.EXPIRED ||
        payment.status == PaymentStatusEnum.TRANSACTION_COMPLETED)
    )
      throw new BadRequestException({
        message: 'Payment status is not valid',
        data: {
          action: payment.status.toString(),
          ...payment.toJSON(),
        },
      });

    await this.setExtraPaymentProperties(payment);

    return payment;
  }

  async get_details_front(code: string, company_id: string) {
    const selectedPayment =
      await this.paymentRepository.findByCodeOrIdWithFullDetails(code);

    if (!selectedPayment) {
      throw {
        code: responseCode.ENTITY_NOT_FOUND,
        statusCode: 404,
        message: 'No payment found with the given code',
      };
    }

    if (company_id && selectedPayment.company['_id'].toString() != company_id) {
      throw {
        code: responseCode.UNAUTHORIZED,
        statusCode: 401,
        message: "You don't have access to this company payments",
      };
    }
    if (selectedPayment.order_code) {
      const order = await this.paymentOrderModel.findOne({
        code: selectedPayment.order_code,
      });
      selectedPayment['order'] = order as any;
    }

    const payment = selectedPayment.toJSON();

    payment['companyConfig'] = await this.companyService.getCompanyConfig(
      selectedPayment.company['_id'].toString(),
    );
    return payment;
  }

  async remove(id: string, currentUser) {
    const removedPayment = await this.paymentRepository.findById(
      new Types.ObjectId(id),
    );
    removedPayment.deletedAt = moment.utc().toDate();
    removedPayment.deletedBy = currentUser;
    await removedPayment.save();
    return removedPayment;
  }

  async changePaymentRefundedStatus(paymentToBeRefunded: PaymentToBeRefunded) {
    const payment = await this.paymentRepository.findById(
      paymentToBeRefunded.paymentLinkId,
    );
    if (!payment) {
      throw {
        code: responseCode.MISSING_DATA,
        message: 'Please Provide a correct payment link id',
        statusCode: 404,
      };
    }

    payment.refunded = paymentToBeRefunded.isRefunded;

    await payment.save();
    return payment;
  }

  async get_payment_receipt(code: string) {
    const selectedPayment =
      await this.paymentRepository.findByCodeWithReceipt(code);
    if (!selectedPayment) {
      throw {
        code: responseCode.MISSING_DATA,
        message: 'Please Provide a correct payment link id',
        statusCode: 404,
      };
    }

    if (
      selectedPayment.status != PaymentStatusEnum.TRANSACTION_COMPLETED &&
      selectedPayment.status != PaymentStatusEnum.UNSUCCESSFUL
    ) {
      throw {
        code: responseCode.MISSING_DATA,
        message: 'Payment Does not completed yet',
        statusCode: 404,
      };
    }
    return selectedPayment;
  }

  async reflect_database_edits() {
    const payments = await this.paymentRepository.findAll();

    for (let i = 0; i < payments.length; i++) {
      const createdAt = moment(payments[i]['createdAt']);
      if (createdAt.format('MMMM') != payments[i]['month']) {
        payments[i]['month'] = createdAt.format('MMMM');
        await payments[i].save();
      }
    }
  }

  // syncVistaMoneyPayments git revert --no-commit 5446d8c2d97f2959a00a46cf9fd9880e4c1c2b2f

  @OnEvent('cron.every10mins')
  @LogError()
  async syncUnpaidPayment() {
    const todayFrom = moment();
    const customerListAggregated = await this.customerIndexService.index({
      havingLatestPayment: 'yes',
      offset: 0,
      limit: 100,
      updatedAtFrom: todayFrom.startOf('day').format('YYYY-MM-DD'),
      updatedAtTo: todayFrom.endOf('day').format('YYYY-MM-DD'),
    } as any);
    const customers = customerListAggregated[0].paginatedResult;
    for (let i = 0; i < customers.length; i++) {
      const payment = await this.paymentRepository.findById(
        new Types.ObjectId(customers[i].latestPayment),
      );
      if (payment) {
        const duration = moment().diff(moment(payment['createdAt']), 'hours');
        if (
          duration >= 24 &&
          duration <= 48 &&
          payment.status == PaymentStatusEnum.PENDING
        ) {
          await this.paymentNotificationService.onPaymentUnpaid(payment);
          await this.customerWriteService.removeLatestPayment(customers[i]._id);
        }
      }
    }
  }

  // firePaymentNotificationOnReconcile git revert --no-commit 5ff1d86f8ddfbd2b95dbe26eb2874e7891941d50

  async setPaymentShortenUrl(payment: PaymentDocument) {
    const url = `${this.configService.get('EXTERNAL_PAYMENT_URL')}/payment/${
      payment._id
    }?lng=${payment.language == 'arabic' ? 'ar' : 'en'}`;
    payment.shortenUrl = await this.shortenUrlService.shortenUrl({
      url,
      canExpire: false,
    });
    await payment.save();
  }

  async syncPaymentBrands(
    brand: Brand,
    brandId: Types.ObjectId,
  ): Promise<void> {
    const startDate = moment().subtract(10, 'day').startOf('day');
    const endDate = moment().endOf('day');

    await this.paymentRepository.updateManyByBrand(
      startDate,
      endDate,
      brandId,
      brand,
    );
  }

  async clonePaymentWithCustomerDetails(clonePaymentDto: ClonePaymentDto) {
    const payment = await this.get_details(clonePaymentDto.uniqueIdentifier);
    if (!payment)
      throw {
        code: responseCode.MISSING_DATA,
        message: 'Please Provide a correct payment link id',
        statusCode: 404,
      };

    const newPayment = await this.create(
      {
        branch:
          payment.branch && payment.branch['_id']
            ? (payment.branch['_id'] as any)
            : undefined,
        customer: undefined,
        amount: payment.amount,
        brand: payment.brand,
        code: undefined,
        brandId: payment.brand?._id,
        company:
          payment.company && payment['company']['_id']
            ? payment['company']['_id']
            : payment['company'],
        branch_id: undefined,
        payment_method: payment.payment_method,
        callback_url: payment.callback_url,
        comments: payment.comments,
        customer_name: clonePaymentDto.customerInfo.name,
        customer_phone: clonePaymentDto.customerInfo.phone,
        country_code: clonePaymentDto.customerInfo.countryCode,
        company_name: payment.company_name,
        fireTrigger: false,
        is_test: payment.is_test,
        order_code: payment.order_code,
        source: payment.source,
        language: payment.language,
      },
      {},
    );

    return newPayment.code;
  }

  private convertCompanyIdsToObjectIds(companyIds: string[]) {
    const convertedCompanyIds = [];
    for (let i = 0; i < companyIds.length; i++) {
      convertedCompanyIds.push(new Types.ObjectId(companyIds[i]));
    }
    return convertedCompanyIds;
  }

  private async setExtraPaymentProperties(payment: PaymentDocument) {
    const paymentConfig =
      await this.paymentConfigurationService.findPaymentConfig(
        payment.branch?._id,
        payment.brand?._id,
        (payment.company as any)?._id,
      );

    // Check if One of the payment gateways is myFatoorah
    const myFatoorahGatewayConfiguration = (
      Object.values(paymentConfig.configuration) as PaymentGatewayConfig[]
    ).find(
      (config): config is MyFatoorahGatewayConfiguration =>
        config?.paymentGatewayType == PaymentGatewayType.MY_FATOORAH,
    );

    const stripeConfig = (
      Object.values(paymentConfig.configuration) as PaymentGatewayConfig[]
    ).find((config) => config?.paymentGatewayType == PaymentGatewayType.STRIPE);

    if (stripeConfig) {
      const { clientSecret } =
        await this.paymentStripeService.createStripePayment(
          payment,
          stripeConfig.configuration,
        );
      payment.stripeClientSecret = clientSecret;
      await payment.save();
    }

    if (
      myFatoorahGatewayConfiguration &&
      myFatoorahGatewayConfiguration.configuration.integrationType !=
        PaymentIntegrationType.REDIRECT
    ) {
      payment.myFatoorahData = await this.paymentMyFatoorah.initiateSession(
        payment,
        myFatoorahGatewayConfiguration,
      );
    }

    await payment.save();
  }
}
