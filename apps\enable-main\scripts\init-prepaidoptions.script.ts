// EBL-5536 Prepaid Payment Method Options Configuration
// Init `company.interfaceConfig.orderConfig.paymentMethodsConfig.prepaidOptions`

db.companies.updateMany(
  {
    'interfaceConfig.orderConfig.paymentMethodsConfig.prepaidOptions': {
      $exists: false,
    },
  },
  {
    $set: {
      'interfaceConfig.orderConfig.paymentMethodsConfig.prepaidOptions': [
        'customer',
        'company',
        'enable',
      ],
    },
  },
);
