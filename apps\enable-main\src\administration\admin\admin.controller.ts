import {
  CreateCompanyDto,
  ForVersion,
  responseCode,
  UpdateCompanyConfigDto,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  SetMetadata,
} from '@nestjs/common';
import { <PERSON>pi<PERSON><PERSON><PERSON>Auth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { HelperService } from '../../shared/services/helper/helper.service';
import { AdminToLogin, Feedback } from './../dto/admin.dto';
import { AdminService } from './../services/admin/admin.service';
import { VersionService } from '../../mobile/modules/version/services/version.service';
import { mongo, Types } from 'mongoose';

@Controller('admin')
@ApiTags('Administration')
@SetMetadata('module', 'admin')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class AdminController {
  constructor(
    private helperService: HelperService,
    private adminService: AdminService,
    private readonly versionService: VersionService,
  ) {}

  @Post('login')
  @SetMetadata('public', 'true')
  async login(@Body() adminToLogin: AdminToLogin, @Res() res: Response) {
    try {
      const token = await this.adminService.login(adminToLogin);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_LOGIN,
        'Success to Login',
        { token },
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('company/create')
  @SetMetadata('action', 'company_create')
  async companyCreate(
    @Body() companyToCreate: CreateCompanyDto,
    @Res() res: Response,
  ) {
    try {
      const createdCompany =
        await this.adminService.createCompany(companyToCreate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success to create company',
        createdCompany,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Put('company/config/:id')
  @SetMetadata('action', 'update_company_config')
  async updateCompanyConfig(
    @Body() companyConfigToUpdate: UpdateCompanyConfigDto,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      companyConfigToUpdate.currentUser = req['current'];
      const company = await this.adminService.updateCompanyConfig(
        companyConfigToUpdate,
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'Success to Update the Company Config',
        company,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('feedback')
  @SetMetadata('public', 'true')
  async createFeedback(@Body() feedback: Feedback, @Res() res: Response) {
    try {
      const response = await this.adminService.createFeedback(feedback);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'Success to Create the feedback',
        response,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get('feedback')
  @SetMetadata('public', 'true')
  async getFeedback(@Res() res: Response) {
    try {
      const response = await this.adminService.getFeedback();
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'Success to get the feedback',
        response,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Delete(':companyId/hardDelete')
  @SetMetadata('public', 'true')
  async hardDelete(
    @Param('companyId') id: string,
    @Body() { code }: { code: string },
    @Res() res: Response,
  ) {
    try {
      const result: mongo.DeleteResult[] = await this.adminService.hardDelete(
        id,
        code,
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_REMOVE,
        'Success deleting the company and its related documents',
        result,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('version/:versionId/force-update')
  @SetMetadata('action', 'forceUpdateVersion')
  async forceUpdateVersion(
    @Param('versionId') versionId: Types.ObjectId,
    @Res() res: Response,
  ) {
    try {
      const version = await this.versionService.forceUpdateVersion(versionId);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'Success to force update version',
        version,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get('version')
  @SetMetadata('action', 'findAllVersions')
  async findAllVersions(
    @Query('for') forVersion: ForVersion,
    @Res() res: Response,
  ) {
    try {
      const versions = await this.versionService.findAll(forVersion);

      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'Success get all versions for a rewards app',
        versions,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
}
