import { LoyaltyTierDocument } from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface LoyaltyTierReadServiceInterface {
  findById(loyaltytierId: Types.ObjectId): Promise<LoyaltyTierDocument>;
  findByCompanyId(companyId: Types.ObjectId): Promise<LoyaltyTierDocument[]>;
  findHighestEligibleTierAfter(
    companyId: Types.ObjectId,
    currentTierId: Types.ObjectId | null,
    orderRate: number,
    amountSpent: number,
    pointsRate: number,
  ): Promise<LoyaltyTierDocument | null>;
  findNextTier(
    companyId: Types.ObjectId,
    currentTierIndex?: number | null,
    mustHaveAmountSpent?: boolean,
  ): Promise<LoyaltyTierDocument | null>;
  findPreviousTier(
    companyId: Types.ObjectId,
    currentTierId?: Types.ObjectId | null,
    mustHaveAmountSpent?: boolean,
  ): Promise<LoyaltyTierDocument | null>;
  findByEnrollmentCode(enrollmentCode: string): Promise<LoyaltyTierDocument>;
  findNonVipTiersByCompanyId(
    companyId: Types.ObjectId,
  ): Promise<LoyaltyTierDocument[]>;
}

export const LoyaltyTierReadServiceInterface = Symbol(
  'LoyaltyTierReadServiceInterface',
);
