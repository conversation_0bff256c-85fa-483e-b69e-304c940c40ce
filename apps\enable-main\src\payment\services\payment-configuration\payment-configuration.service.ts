import {
  CompanyDocument,
  CreatePaymentConfigurationDto,
  LogError,
  LoggerService,
  PaymentConfiguration,
  PaymentConfigurationDocument,
  PaymentConfigurationDto,
  PaymentGatewayConfig,
  PaymentGatewayCredential,
  PaymentGatewayType,
  PaymentMethod,
  responseCode,
  StripeConfiguration,
  UpdatePaymentConfigurationDto,
  VistaMoneyConfiguration,
} from '@app/shared-stuff';
import { BadRequestException, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OnEvent } from '@nestjs/event-emitter';
import { plainToInstance } from 'class-transformer';
import { Types } from 'mongoose';
import { PaymentConfigurationRepositoryInterface } from '../../repositories/interfaces/payment.configuration.repository.interface';
import { PaymentConfigurationServiceInterface } from './payment-configuration.service.interface';

export class PaymentConfigurationService
  implements PaymentConfigurationServiceInterface
{
  private readonly loggerService = new LoggerService(
    PaymentConfigurationService.name,
  );

  constructor(
    @Inject(PaymentConfigurationRepositoryInterface)
    private paymentConfigurationRepository: PaymentConfigurationRepositoryInterface,
    private configService: ConfigService,
  ) {}

  async create(
    createPaymentConfigurationDto: CreatePaymentConfigurationDto,
  ): Promise<PaymentConfigurationDocument> {
    const createPaymentConfigurationDtoWithDefaults =
      this.addDefaultGatewayConfigs(createPaymentConfigurationDto);
    const paymentConfiguration = plainToInstance(PaymentConfiguration, {
      ...createPaymentConfigurationDtoWithDefaults,
      defaultPaymentMethod: PaymentMethod.CREDIT_CARDS,
    });
    try {
      return await this.paymentConfigurationRepository.create(
        paymentConfiguration,
      );
    } catch (error) {
      this.loggerService.error(
        ' Create payment configuration  Error Message: ' + error.message,
        error.stacktrace,
        createPaymentConfigurationDto,
      );
      throw new BadRequestException(
        'Payment configuration already exists',
        error,
      );
    }
  }

  async update(
    updatePaymentConfigurationDto: UpdatePaymentConfigurationDto,
  ): Promise<PaymentConfigurationDocument> {
    const updatePaymentConfigurationDtoWithDefaults =
      this.addDefaultGatewayConfigs(updatePaymentConfigurationDto);

    const paymentConfiguration = plainToInstance(PaymentConfiguration, {
      ...updatePaymentConfigurationDtoWithDefaults,
      defaultPaymentMethod: PaymentMethod.CREDIT_CARDS,
    });

    const hasNoPaymentMethods =
      Object.keys(paymentConfiguration.configuration).length === 0 ||
      Object.values(paymentConfiguration.configuration).every(
        (gatewayConfig) => !gatewayConfig,
      );

    if (hasNoPaymentMethods)
      throw new BadRequestException(
        'At least one payment method must be configured',
      );

    return await this.paymentConfigurationRepository.findOneAndReplace(
      {
        $or: [
          {
            _id: updatePaymentConfigurationDtoWithDefaults?._id,
          },
          { companyId: updatePaymentConfigurationDtoWithDefaults?.companyId },
        ],
      },
      paymentConfiguration,
    );
  }

  private addDefaultGatewayConfigs<T extends PaymentConfigurationDto>(
    paymentConfiguration: T,
  ): T {
    Object.keys(paymentConfiguration.configuration).forEach((paymentMethod) => {
      const gatewayConfig = paymentConfiguration.configuration[paymentMethod];

      paymentConfiguration.configuration[paymentMethod] =
        this.getNeedsDefaultGatewayConfig(gatewayConfig)
          ? this.useDefaultGatewayConfig(gatewayConfig)
          : gatewayConfig;
    });

    return paymentConfiguration;
  }

  private getNeedsDefaultGatewayConfig(
    gatewayConfig: PaymentGatewayConfig,
  ): boolean {
    if (!gatewayConfig) return false;
    // if (gatewayConfig.configuration) return true;
    //
    // if (
    //   ![
    //     PaymentGatewayType.VISTA,
    //     PaymentGatewayType.DIBSY,
    //     PaymentGatewayType.CBPAY,
    //     PaymentGatewayType.CYBER_SOURCE,
    //     PaymentGatewayType.MY_FATOORAH,
    //     PaymentGatewayType.TESS,
    //   ].includes(gatewayConfig.paymentGatewayType) ||
    //   gatewayConfig.credential == PaymentGatewayCredential.COMPANY_CREDENTIAL
    // )
    //   return false;
    //
    // return true;
    return (
      gatewayConfig.credential == PaymentGatewayCredential.ENABLE_CREDENTIAL
    );
  }

  private useDefaultGatewayConfig(
    gatewayConfig: PaymentGatewayConfig,
  ): PaymentGatewayConfig {
    if (gatewayConfig.paymentGatewayType === PaymentGatewayType.VISTA) {
      return {
        ...gatewayConfig,
        configuration: this.getDefaultVistaGatewayConfig(),
      };
    } else if (gatewayConfig.paymentGatewayType === PaymentGatewayType.DIBSY) {
      return {
        ...gatewayConfig,
        configuration: {
          dibsySecretApiKey: this.configService.get('DIBSY_SECRET_KEY'),
        },
      };
    } else if (gatewayConfig.paymentGatewayType === PaymentGatewayType.CBPAY) {
      return {
        ...gatewayConfig,
        configuration: {},
      };
    } else if (
      gatewayConfig.paymentGatewayType === PaymentGatewayType.CYBER_SOURCE
    ) {
      return {
        ...gatewayConfig,
        configuration: {},
      };
    } else if (
      gatewayConfig.paymentGatewayType === PaymentGatewayType.MY_FATOORAH
    ) {
      return {
        ...gatewayConfig,
        configuration: {
          apiKey: this.configService.get('MY_FATOORAH_API_KEY'),
          webhookSecretKey: '',
        },
      };
    } else if (gatewayConfig.paymentGatewayType === PaymentGatewayType.TESS) {
      return {
        ...gatewayConfig,
        configuration: {
          tessMerchantPass: this.configService.get('TESS_MERCHANT_PASS'),
        },
      };
    } else if (
      gatewayConfig.paymentGatewayType === PaymentGatewayType.SKIP_CASH
    ) {
      return {
        ...gatewayConfig,
        configuration: {
          skipCashKeyClientId: this.configService.get('SKIP_CASH_CLIENT_ID'),
          skipCashKeyId: this.configService.get('SKIP_CASH_KEY_ID'),
          skipCashKeySecret: this.configService.get('SKIP_CASH_KEY_SECRET'),
          skipCashWebHookKey: this.configService.get('SKIP_CASH_WEBHOOK_KEY'),
        },
      };
    }

    return gatewayConfig;
  }

  private getDefaultVistaGatewayConfig(): VistaMoneyConfiguration {
    return {
      terminal_id: this.configService.get('VISTAMONEY_TERMINAL_ID'),
      terminal_password: this.configService.get('VISTAMONEY_PASSWORD'),
    };
  }

  async findPaymentConfig(
    branchId: Types.ObjectId,
    brandId: Types.ObjectId,
    companyId: Types.ObjectId,
  ): Promise<PaymentConfigurationDocument> {
    if (branchId) {
      const branchPaymentConfiguration =
        await this.paymentConfigurationRepository.findByBranchId(branchId);
      if (branchPaymentConfiguration) return branchPaymentConfiguration;
    }

    if (brandId) {
      const brandPaymentConfiguration =
        await this.paymentConfigurationRepository.findByBrandId(brandId);
      if (brandPaymentConfiguration) return brandPaymentConfiguration;
    }

    if (companyId) {
      const companyPaymentConfiguration =
        await this.paymentConfigurationRepository.findByCompanyId(companyId);
      if (companyPaymentConfiguration) return companyPaymentConfiguration;
    }

    throw new BadRequestException(
      'Payment configuration not found',
      responseCode.ENTITY_NOT_FOUND.toString(),
    );
  }

  async findPaymentConfigByIdentifier(
    id: Types.ObjectId,
  ): Promise<PaymentConfigurationDocument> {
    return await this.paymentConfigurationRepository.findByIdentifier(id);
  }

  async findPublicPaymentConfigByIdentifier(
    id: Types.ObjectId,
  ): Promise<PaymentConfigurationDocument> {
    const paymentConfiguration =
      await this.paymentConfigurationRepository.findByIdentifier(id);

    return this.removePrivateFields(paymentConfiguration);
  }

  private removePrivateFields(
    paymentConfiguration: PaymentConfigurationDocument,
  ): PaymentConfigurationDocument {
    const newConfig = {};

    Object.keys(paymentConfiguration.configuration).forEach(
      (paymentMethod: PaymentMethod) => {
        // we remove `configuration` from the response
        const oldConfig = paymentConfiguration.configuration[paymentMethod];
        const { configuration, ...rest } = oldConfig;
        newConfig[paymentMethod] = rest;

        // except in the case of stripe, frontend needs the publishable key
        if (oldConfig.paymentGatewayType === PaymentGatewayType.STRIPE) {
          newConfig[paymentMethod].configuration = {
            publishableKey: (configuration as StripeConfiguration)
              .publishableKey,
          };
        } else if ('integrationType' in configuration) {
          newConfig[paymentMethod].configuration = {
            integrationType: configuration.integrationType,
          };
        }
      },
    );

    paymentConfiguration.configuration = newConfig;
    return paymentConfiguration;
  }

  @OnEvent('company.created')
  @LogError()
  async createDefaultConfig(company: CompanyDocument) {
    const defaultPaymentConfig =
      await this.paymentConfigurationRepository.create({
        companyId: company._id,
        defaultPaymentMethod: PaymentMethod.CREDIT_CARDS,
        configuration: {
          [PaymentMethod.CREDIT_CARDS]: {
            paymentGatewayType: PaymentGatewayType.VISTA,
            configuration: this.getDefaultVistaGatewayConfig(),
            credential: PaymentGatewayCredential.ENABLE_CREDENTIAL,
            supportReceivingCardDetails: true,
          },
          [PaymentMethod.DEBIT_CARDS]: {
            paymentGatewayType: PaymentGatewayType.VISTA,
            configuration: this.getDefaultVistaGatewayConfig(),
            credential: PaymentGatewayCredential.ENABLE_CREDENTIAL,
            supportReceivingCardDetails: false,
          },
          [PaymentMethod.APPLE_PAY]: {
            paymentGatewayType: PaymentGatewayType.DIBSY,
            credential: PaymentGatewayCredential.ENABLE_CREDENTIAL,
            configuration: {
              dibsySecretApiKey: this.configService.get('DIBSY_SECRET_KEY'),
            },
            supportReceivingCardDetails: false,
          },
        },
      });
    this.loggerService.log('defaultPaymentConfig', defaultPaymentConfig);
  }
}
