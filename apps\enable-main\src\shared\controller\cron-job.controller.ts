import {
  GenericExceptionFilter,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Post,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiExcludeController, ApiTags } from '@nestjs/swagger';
import { CronJobRequest } from '../dto/cron-job-request.dto';
import { CronJobService } from '../services/cron-job/cron-job.service';

@Controller('cron-job')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags('Cron-Job')
@ApiExcludeController()
@SetMetadata('module', 'cron-job')
export class CronJobController {
  constructor(private readonly cronJobService: CronJobService) {}

  @Post('public/run')
  @SetMetadata('public', 'true')
  @HttpCode(HttpStatus.OK)
  runCronJob(@Body() body: CronJobRequest): { message: string } {
    const { expression } = body;
    return this.cronJobService.run(expression);
  }
}
