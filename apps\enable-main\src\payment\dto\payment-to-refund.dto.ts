import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsMongoId, IsOptional } from 'class-validator';
import { Types } from 'mongoose';

export class PaymentToBeRefunded {
  @ApiProperty({
    type: Types.ObjectId,
    required: true,
  })
  @IsMongoId()
  paymentLinkId: Types.ObjectId;

  @ApiProperty({
    type: Boolean,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isRefunded = false;
}
