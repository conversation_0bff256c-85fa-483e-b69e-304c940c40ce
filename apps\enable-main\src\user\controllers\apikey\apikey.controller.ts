import { HelperService } from '../../../shared/services/helper/helper.service';
import { ApiKeyToIndex } from './../../dto/apikey.dto';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  SetMetadata,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { ApikeyService } from '../../services/apikey/apikey.service';
import { Request, Response } from 'express';
import { responseCode } from '@app/shared-stuff';
import { ApiKeyToCreate, ApiKeyToUpdate } from '../../dto/apikey.dto';
import { ConfigService } from '@nestjs/config';

@ApiTags('Api Key')
@Controller('apikey')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@SetMetadata('module', 'apikey')
export class ApikeyController {
  constructor(
    private apikeyService: ApikeyService,
    private configService: ConfigService,
    private helperService: HelperService,
  ) {}

  @Get()
  @SetMetadata('action', 'get_all')
  async findAll(@Query() dataIndex: ApiKeyToIndex, @Res() res: Response) {
    try {
      const selectedApiKeys = await this.apikeyService.index(dataIndex);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'success for getting all apikeys',
        selectedApiKeys,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get('validate/:code')
  @SetMetadata('public', 'true')
  async validateApiKey(
    @Param('code') code: string,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      const domain = '';
      const apikey = await this.apikeyService.validateApiKey(code, domain);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'success for getting apikey details',
        apikey,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  async findDetails(@Param('id') id: string, @Res() res: Response) {
    try {
      const selectedApiKey = await this.apikeyService.get_details(id);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success for getting apikey details',
        selectedApiKey,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(@Body() KeyToCreate: ApiKeyToCreate, @Res() res: Response) {
    try {
      const createdKey = await this.apikeyService.create(KeyToCreate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success for creating api key',
        createdKey,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
  @Put(':id')
  @SetMetadata('action', 'update')
  async update(@Body() apikeyToUpdate: ApiKeyToUpdate, @Res() res: Response) {
    try {
      const updatedApiKey = await this.apikeyService.update(apikeyToUpdate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'success for update apkey',
        updatedApiKey,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Delete(':id')
  @SetMetadata('action', 'remove')
  async remove(@Param('id') id: string, @Res() res: Response) {
    try {
      const removedApiKey = await this.apikeyService.remove(id);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_REMOVE,
        'success for remove apikey',
        removedApiKey,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
}
