import { DataIndex, ObjectIdTransform } from '@app/shared-stuff';
import { ApiProperty } from '@nestjs/swagger';
import { Types } from 'mongoose';

export class AreaToCreate {
  @ApiProperty({
    type: String,
    required: true,
  })
  name: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  city: string;
}

export class AreaToUpdate {
  @ApiProperty({
    type: String,
    required: true,
  })
  _id: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  name: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  city: string;
}

export class AreaToIndex extends DataIndex {
  @ApiProperty({
    type: String,
    required: false,
  })
  @ObjectIdTransform({ optional: true })
  city?: Types.ObjectId;

  @ApiProperty({ type: String, required: false })
  country?: string;
}
