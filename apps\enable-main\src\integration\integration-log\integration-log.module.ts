import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  IntegrationLog,
  IntegrationLogSchema,
} from './models/integration.log.model';
import { IntegrationLogRepository } from './repositories/integration-log.repository';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: IntegrationLog.name, schema: IntegrationLogSchema },
    ]),
  ],
  providers: [
    {
      provide: 'IntegrationLogRepositoryInterface',
      useClass: IntegrationLogRepository,
    },
  ],
  exports: ['IntegrationLogRepositoryInterface'],
})
export class IntegrationLogModule {}
