import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { UpdateDeliveryThirdPartyDto } from '../../dto/delivery-third-party/update-delivery-third-party.dto';
import { DeliveryThirdPartyDocument } from '../../models/delivery-third-party.model';
import { IndexDeliveryThirdPartyDto } from '../../dto/delivery-third-party/index-delivery-third-party.dto';
import { DeliveryThirdPartyServiceInterface } from './delivery-third-party.service.interface';
import { DeliveryThirdPartyRepositoryInterface } from '../../repositories/delivery-third-party/delivery-third-party.repositories.interface';
import { DeliveryConfigurationServiceInterface } from '../delivery-configuration/delivery-configuration.service.interface';

@Injectable()
export class DeliveryThirdPartyService
  implements DeliveryThirdPartyServiceInterface
{
  constructor(
    @Inject(DeliveryThirdPartyRepositoryInterface)
    private readonly deliveryThirdPartyRepository: DeliveryThirdPartyRepositoryInterface,
    @Inject(DeliveryConfigurationServiceInterface)
    private readonly deliveryConfigurationService: DeliveryConfigurationServiceInterface,
  ) {}

  async index({
    companyId,
  }: IndexDeliveryThirdPartyDto): Promise<DeliveryThirdPartyDocument[]> {
    if (companyId) {
      const deliveryConfiguration =
        await this.deliveryConfigurationService.findByCompanyId(companyId);
      if (
        deliveryConfiguration &&
        deliveryConfiguration.thirdPartyConfiguration
      ) {
        if (deliveryConfiguration.thirdPartyConfiguration.defaultThirdParty)
          return await this.deliveryThirdPartyRepository.findByName(
            deliveryConfiguration.thirdPartyConfiguration.defaultThirdParty,
          );
        else
          return await this.deliveryThirdPartyRepository.findByNameIn(
            deliveryConfiguration.thirdPartyConfiguration.thirdParties,
          );
      }
    } else return await this.deliveryThirdPartyRepository.findAll();
  }

  async update({
    _id,
    ...updateDeliveryThirdPartyDto
  }: UpdateDeliveryThirdPartyDto): Promise<DeliveryThirdPartyDocument> {
    const deliveryThirdParty =
      await this.deliveryThirdPartyRepository.findOneAndUpdate(
        { _id: _id, deletedAt: null },
        updateDeliveryThirdPartyDto,
      );
    if (!deliveryThirdParty) {
      throw new NotFoundException(
        `Delivery Third Party with id ${_id} not found`,
      );
    }
    return deliveryThirdParty;
  }
}
