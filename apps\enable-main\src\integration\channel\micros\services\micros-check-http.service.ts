import {
  LoggerService,
  MicrosBranchConfig,
  MicrosCompanyConfig,
  MicrosGetAllChecksResponse,
  PostMicrosOrderDto,
} from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { MicrosAuthService } from './micros-auth.service';

@Injectable()
export class MicrosCheckHttpService {
  private readonly logger = new LoggerService(MicrosCheckHttpService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly microsAuthService: MicrosAuthService,
  ) {}

  async performPostRequest(
    url: string,
    checkRequestBody: PostMicrosOrderDto,
    microsCompanyConfig: MicrosCompanyConfig,
    microsBranchConfig?: MicrosBranchConfig,
  ) {
    const HEADERS = await this.constructMicrosCheckHeaders(
      microsCompanyConfig,
      microsBranchConfig,
    );
    this.logger.log('[Micros] Headers', HEADERS);
    return new Promise(async (resolve, reject) => {
      this.httpService
        .post(url, checkRequestBody, {
          headers: HEADERS,
        })
        .subscribe({
          next: (data) => {
            this.logger.log(
              '[Micros] request body to perform post request',
              url,
              checkRequestBody,
            );
            this.logger.log(
              '[Micros] created/updates Micros checkF',
              data.data,
            );
            resolve(data.data ?? {});
          },
          error: (error) => {
            this.logger.error(
              '[Micros] Error while perform POST request',
              error.stacktrace,
              error,
            );
            reject({ status: 'error', message: JSON.stringify(error) });
          },
        });
    });
  }

  async getAllChecksSinceTime(
    sinceTime: string,
    microsCompanyConfig: MicrosCompanyConfig,
    microsBranchConfig: MicrosBranchConfig,
  ): Promise<MicrosGetAllChecksResponse> {
    const URL = `${microsCompanyConfig.checkBaseUrl}/checks/?includeClosed=true&sinceTime=${sinceTime}`;

    const HEADERS = await this.constructMicrosCheckHeaders(
      microsCompanyConfig,
      microsBranchConfig,
    );

    return new Promise(async (resolve, reject) => {
      this.httpService
        .get(URL, {
          headers: HEADERS,
        })
        .subscribe({
          next: (data) => {
            this.logger.log(
              `[Micros] Success calling ${URL}`,
              JSON.stringify(data.data),
            );
            resolve(data.data ?? {});
          },
          error: (error) => {
            this.logger.error(error.message, error.stacktrace, {
              URL,
              HEADERS,
              microsCompanyConfig,
              microsBranchConfig,
            });
            reject({ status: 'error', message: JSON.stringify(error) });
          },
        });
    });
  }

  private async constructMicrosCheckHeaders(
    microsCompanyConfig: MicrosCompanyConfig,
    microsBranchConfig?: MicrosBranchConfig,
  ) {
    const token = await this.microsAuthService.getIdToken(microsCompanyConfig);
    if (!token) return;
    return {
      'Simphony-LocRef': microsBranchConfig.locationReference,
      'Simphony-OrgShortName': microsCompanyConfig.organizationShortName,
      'Simphony-RvcRef': microsBranchConfig.revenueCenterReference,
      Authorization: 'Bearer ' + token,
    };
  }
}
