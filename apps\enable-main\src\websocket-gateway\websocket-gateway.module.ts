import { SharedStuffModule } from '@app/shared-stuff';
import { CompanyModule } from './../company/company.module';
import { UserModule } from './../user/user.module';
import { WebsocketGatewayService } from './services/websocket-gateway/websocket-gateway.service';
import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';

@Module({
  imports: [UserModule, CompanyModule, EventEmitterModule, SharedStuffModule],
  controllers: [],
  providers: [WebsocketGatewayService],
})
export class WebsocketGatewayModule {}
