import { ApiProperty } from '@nestjs/swagger';

export class CardDetails {
  @ApiProperty({
    type: String,
    required: false,
  })
  cardNumber: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  cardName: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  cardCVV: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  cardExpireYear: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  cardExpireMonth: string;
}
