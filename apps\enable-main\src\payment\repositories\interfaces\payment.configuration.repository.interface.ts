import { IGenericRepository } from '@app/shared-stuff';
import { FilterQuery, Types } from 'mongoose';
import {
  PaymentConfiguration,
  PaymentConfigurationDocument,
} from '../../../../../../libs/shared-stuff/src/models/payment.configuration.model';

export interface PaymentConfigurationRepositoryInterface
  extends IGenericRepository<
    PaymentConfigurationDocument,
    PaymentConfiguration
  > {
  findByBranchId(
    branchId: Types.ObjectId,
  ): Promise<PaymentConfigurationDocument | null>;

  findByBrandId(
    brandId: Types.ObjectId,
  ): Promise<PaymentConfigurationDocument | null>;

  findByCompanyId(
    companyId: Types.ObjectId,
  ): Promise<PaymentConfigurationDocument | null>;

  findByIdentifier(
    identifier: Types.ObjectId,
  ): Promise<PaymentConfigurationDocument | null>;

  findOneAndReplace(
    filter: FilterQuery<PaymentConfigurationDocument>,
    paymentConfiguration: PaymentConfiguration,
  ): Promise<PaymentConfigurationDocument>;
}
export const PaymentConfigurationRepositoryInterface = Symbol(
  'PaymentConfigurationRepositoryInterface',
);
