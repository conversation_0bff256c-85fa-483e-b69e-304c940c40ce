import { OrdableDiscountType } from '../../enums/ordable-discount-type.enum';

// Based on https://ordable.stoplight.io/docs/menu-management/e1f106a725c8d-create-a-promotion
export class OrdablePromotionDto {
  id?: number;
  name: string;
  ar_name: string;
  quantity: number;
  start: string; // 'YYYY-MM-DD'
  expiry: string; // 'YYYY-MM-DD'
  discount_type: OrdableDiscountType;
  discount_value?: number; // 1 <= discount_value <= 100 if discount_type percent
  targeted_user?: number | number[];
  min_order?: number;
  loyality_points?: number; // Yes, it's "loyality" not "loyalty"
  products?: number[]; // required if discount_type === 'products_percent'
  external_id?: string;
}
