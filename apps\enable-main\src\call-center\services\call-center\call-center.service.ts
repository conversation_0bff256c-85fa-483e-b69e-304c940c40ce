import {
  BranchDocument,
  Brand,
  CallCenterWebhook,
  CallCenterWebhookRepositoryInterface,
  CountryDialCode,
  CreateCustomerDto,
  GenericTriggerModel,
  LanguageCode,
  LanguageToLanguageCode,
  LoggerService,
  PusherService,
  TriggerAction,
  TriggerModule,
} from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { plainToInstance } from 'class-transformer';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import * as qs from 'qs';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { CustomerReadServiceInterface } from '../../../customer/modules/customer-read/customer-read.service.interface';
import { CustomerReplacementsServiceInterface } from '../../../customer/modules/customer-replacements/customer-replacements.service.interface';
import { CustomerWriteServiceInterface } from '../../../customer/modules/customer-write/customer-write.service.interface';
import { TriggerService } from '../../../notification/services/trigger/trigger.service';
import { CallCenterConfigurationDto } from '../../dto/call-center-configuration.dto';
import { CallToNotify } from '../../dto/call-center.dto';
import { CallCenterReplacements } from '../../types/call-center-replacements.type';
import { BranchService } from './../../../branch/services/branch/branch.service';
import { CompanyService } from './../../../company/services/company/company.service';
import { CallCenterSmsSentDocument } from './../../models/call-center-sms.model';

@Injectable()
export class CallCenterService {
  private readonly loggerService = new LoggerService(CallCenterService.name);

  QATAR_COUNTRY_CODE = '974';

  constructor(
    private httpService: HttpService,
    private configService: ConfigService,
    private branchService: BranchService,
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandServiceInterface,
    private companyService: CompanyService,
    @InjectModel('CallCenterSmsSent')
    private callCenterSmsSentModel: Model<CallCenterSmsSentDocument>,
    private triggerService: TriggerService,
    @Inject(CustomerReadServiceInterface)
    private customerReadService: CustomerReadServiceInterface,
    @Inject(CustomerWriteServiceInterface)
    private customerWriteService: CustomerWriteServiceInterface,
    @Inject(CallCenterWebhookRepositoryInterface)
    private callCenterWebhookRepository: CallCenterWebhookRepositoryInterface,
    private puhserService: PusherService,
    @Inject(CustomerReplacementsServiceInterface)
    private customerReplacementsService: CustomerReplacementsServiceInterface,
  ) {}

  async generateMaqsamAuthToken(userEmail: string) {
    return new Promise((resolve, reject) => {
      const APIKEY = this.configService.get('MAQSAM_API_KEY');
      const API_SECRET = this.configService.get('MAQSAM_API_SECRET');
      const URL = this.configService.get('MAQSAM_BASE_URL');

      this.httpService
        .post(URL, qs.stringify({ UserEmail: userEmail }), {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Authorization:
              'Basic ' +
              Buffer.from(`${APIKEY}:${API_SECRET}`).toString('base64'),
          },
        })
        .subscribe(
          (data) => {
            resolve(data.data['result']['token']);
          },
          (err) => {
            reject(err);
          },
        );
    });
  }

  async fireCallerInfo(caller: string) {
    this.puhserService.fireEvent('all', 'OnCallFired', { caller });
    return { caller };
  }

  async onCallCame(callToNotify: CallToNotify): Promise<string> {
    this.loggerService.log(
      'the entire call center payload (callToNotify):',
      callToNotify,
    );
    this.loggerService.log(
      `Call ${callToNotify.state} from ${callToNotify.caller_number} to ${callToNotify.callee}@${callToNotify.callee_number}`,
    );

    if (!callToNotify.callee_number) return;
    (callToNotify.caller_number = this.formatPhoneNumber(
      callToNotify.caller_number,
    )),
      (callToNotify.callee_number = this.formatPhoneNumber(
        callToNotify.callee_number,
      ));

    let branch = (
      await this.branchService.findByPhoneNumber(callToNotify.callee_number)
    )[0];

    const brand = (
      await this.brandService.index(
        { phoneNumber: callToNotify.callee_number },
        null,
      )
    )[0]['paginatedResult'][0] as Brand;

    if (!branch && brand && brand.branches?.length > 0) {
      branch = (
        await this.branchService.findAllByIds([brand.branches[0]._id])
      )[0];
    }

    const company = await this.getCompany(
      branch,
      brand,
      callToNotify.callee_number,
    );

    if (!company) {
      return;
    }
    this.loggerService.log('formatted callee , hardPhoneNumbers', {
      callee_number: callToNotify.callee_number,
      hardPhoneNumbers: company?.hardPhoneNumbers,
    });
    if (company?.hardPhoneNumbers.includes(callToNotify.callee_number))
      await this.bufferCallCenterWebhookPayload(callToNotify);

    const customer = await this.getCustomer(
      callToNotify.caller_number,
      company._id,
    );
    const websiteLink =
      branch && branch.externalLinks?.webStoreLink
        ? branch.externalLinks?.webStoreLink
        : company.website;
    const senderId =
      company.followBranchMechanism &&
      branch &&
      branch.callCenterConfig?.allowCallerToReceivePromotion
        ? brand
          ? brand.senderId
          : branch.senderId
        : company.senderId;

    await this.fireCallTriggers(
      {
        branch: branch,
        company: company,
        customer: customer,
        senderId: senderId,
        language: LanguageToLanguageCode[customer.language] ?? LanguageCode.ar,
        webSiteLink: websiteLink,
        brand,
      },
      callToNotify.caller_number,
      callToNotify.callee_number,
      callToNotify.state,
    );

    return 'DONE';
  }

  async bufferCallCenterWebhookPayload(
    callToNotify: CallToNotify,
  ): Promise<void> {
    const bufferedCallCenterWebhook =
      await this.callCenterWebhookRepository.findCalleeByPhoneNumber(
        callToNotify.callee_number,
      );
    if (bufferedCallCenterWebhook)
      await this.callCenterWebhookRepository.remove(
        bufferedCallCenterWebhook._id,
      );

    const mappedObject = {
      ...callToNotify,
      callerNumber: callToNotify.caller_number,
      calleeNumber: callToNotify.callee_number,
    };
    const callCenterWebhook = plainToInstance(CallCenterWebhook, mappedObject);
    this.loggerService.log('callCenterWebhook', callCenterWebhook);
    await this.callCenterWebhookRepository.create(callCenterWebhook);
  }

  private async getCompany(
    branch: BranchDocument,
    brand: Brand,
    calleeNumber: string,
  ) {
    let companyUniqueIdentifier: string;
    if (branch) {
      companyUniqueIdentifier = branch.company._id.toString();
    } else if (brand) {
      companyUniqueIdentifier = brand.companyId.toHexString();
    } else {
      companyUniqueIdentifier = calleeNumber;
    }
    return await this.companyService.get_details(companyUniqueIdentifier);
  }

  private async getCustomer(callerNumber: string, companyId: Types.ObjectId) {
    let customer = await this.customerReadService.getCustomer(
      this.formatPhoneNumber(callerNumber),
      companyId,
    );
    if (!customer) {
      const customerToCreate: CreateCustomerDto = {
        full_name: 'anonymous customer',
        first_name: 'anonymous ',
        last_name: 'customer',
        phone: this.formatPhoneNumber(callerNumber),
        country_code: CountryDialCode.QATAR,
        company: new Types.ObjectId(companyId),
        email: '',
        contact_channel: undefined,
        location: undefined,
        createdBy: undefined,
        firstBrandOrderdId: null,
        firstBranchOrderdId: null,
      };
      customer = await this.customerWriteService.create(
        customerToCreate,
        undefined,
      );
    }
    return customer;
  }

  private async isCallerReceiveSmsBefore(
    callerNumber: string,
    calleeNumber: string,
  ) {
    const callerSms = await this.callCenterSmsSentModel
      .find({ callerNumber, calleeNumber })
      .sort({ createdAt: -1 });
    const latestCallerSMS = callerSms[0];
    return (
      latestCallerSMS &&
      moment.utc().diff(moment.utc(latestCallerSMS['createdAt']), 'days') <= 3
    );
  }

  async populateGenericTriggerModel(
    callCenterConfigurationDto: CallCenterConfigurationDto,
  ): Promise<GenericTriggerModel> {
    const replacements = await this.getAllCallCenterReplacements(
      callCenterConfigurationDto,
    );
    return {
      companyId: this.getCompanyId(callCenterConfigurationDto),
      branchId: callCenterConfigurationDto?.branch?._id.toHexString(),
      customerId: new Types.ObjectId(callCenterConfigurationDto.customer._id),
      countryCode: CountryDialCode.QATAR,
      brandId: callCenterConfigurationDto.brand
        ? callCenterConfigurationDto.brand['_id'].toHexString?.()
        : undefined,
      createdBy: undefined,
      giftRecipientUser: undefined,
      isGift: undefined,
      senderId: callCenterConfigurationDto?.senderId,
      emailSenderId: callCenterConfigurationDto?.senderId,
      triggerModule: TriggerModule.CALL_CENTER,
      replacements: replacements,
      language: callCenterConfigurationDto.language,
      context: {
        customer: {
          ...callCenterConfigurationDto.customer?.toJSON(),
          _id: callCenterConfigurationDto.customer?._id,
        },
      },
    };
  }

  private getCompanyId(
    callCenterConfigurationDto: CallCenterConfigurationDto,
  ): string {
    if (callCenterConfigurationDto.company)
      return callCenterConfigurationDto.company._id.toString();
    else if (
      callCenterConfigurationDto.branch &&
      callCenterConfigurationDto.branch.company['_id']
    )
      return callCenterConfigurationDto.branch.company._id.toString();
    else if (
      callCenterConfigurationDto.branch &&
      !callCenterConfigurationDto.branch.company['_id']
    )
      return callCenterConfigurationDto.branch.company.toString();
  }

  private async getAllCallCenterReplacements(
    callCenterConfigurationDto: CallCenterConfigurationDto,
  ): Promise<CallCenterReplacements> {
    return {
      websiteLink: callCenterConfigurationDto?.webSiteLink,
      companyName: callCenterConfigurationDto?.company?.name,
      branchName: callCenterConfigurationDto?.branch?.name,
      customerName: callCenterConfigurationDto?.customer.full_name,
      ...(await this.customerReplacementsService.getCustomerReplacements(
        callCenterConfigurationDto.customer,
      )),
    };
  }

  private async fireCallTriggers(
    callCenterConfigurationDto: CallCenterConfigurationDto,
    callerNumber: string,
    calleeNumber: string,
    callState: string,
  ) {
    const genericTriggerModel = await this.populateGenericTriggerModel(
      callCenterConfigurationDto,
    );
    if (callState == 'abandoned')
      await this.triggerService.fireTrigger(
        genericTriggerModel,
        TriggerAction.ON_CALL_ABANDONED,
      );

    if (callState == 'dropped')
      await this.triggerService.fireTrigger(
        genericTriggerModel,
        TriggerAction.ON_CALL_DROPPED,
      );
    if (callState == 'in_progress') {
      if (
        // !maqsamConfigurationDto.company ||
        // (!maqsamConfigurationDto.company.callCenterConfig
        //   .allowCallerToReceivePromotion &&
        //   ((maqsamConfigurationDto.branch &&
        //     !maqsamConfigurationDto.branch.callCenterConfig
        //       ?.allowCallerToReceivePromotion) ||
        //     !maqsamConfigurationDto.branch)) ||
        await this.isCallerReceiveSmsBefore(callerNumber, calleeNumber)
      )
        return;

      await this.triggerService.fireTrigger(
        genericTriggerModel,
        TriggerAction.ON_CALL_CAME,
      );

      await this.saveCallCenterSmsSent(calleeNumber, callerNumber);
    }
  }

  private async saveCallCenterSmsSent(
    calleeNumber: string,
    callerNumber: string,
  ) {
    const callCenterSmsSent = new this.callCenterSmsSentModel({
      calleeNumber,
      callerNumber,
    });
    await callCenterSmsSent.save();
  }

  private formatPhoneNumber(phoneNumber: string) {
    return phoneNumber.replace(/\s+/g, '').replace(/^\+?974(?=\d{8,})/, '');
  }
}
