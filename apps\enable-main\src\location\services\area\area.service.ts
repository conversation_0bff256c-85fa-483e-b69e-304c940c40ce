import { GetAllLocationItemDto, LocationItemType } from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { AreaToIndex } from '../../dto/area.dto';
import { LocationItemServiceInterface } from '../location-item/location-item-service.interface';
import { LocationItemService } from '../location-item/location-item.service';

@Injectable()
export class AreaService {
  constructor(
    @Inject(LocationItemServiceInterface)
    private readonly locationItemService: LocationItemService,
  ) {}

  public async index(areaToIndex: AreaToIndex) {
    const areas = await this.locationItemService.findAll(
      await this.getIndexLocationItemDto(areaToIndex),
    );

    return areas[0].paginatedResult;
  }

  private async getIndexLocationItemDto({
    city,
    country,
    ...rest
  }: AreaToIndex): Promise<GetAllLocationItemDto> {
    if (city)
      return {
        ...rest,
        type: LocationItemType.AREA,
        parentId: city,
      };
    else if (country) {
      const countryDocument =
        await this.locationItemService.findOneByNameAndType(
          country,
          LocationItemType.COUNTRY,
        );

      return {
        ...rest,
        type: LocationItemType.AREA,
        parentId: countryDocument._id,
        ancestorsSearch: true,
      };
    } else return { ...rest, type: LocationItemType.AREA };
  }
}
