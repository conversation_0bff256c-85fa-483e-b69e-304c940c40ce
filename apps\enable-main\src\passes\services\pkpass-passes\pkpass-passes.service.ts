import {
  BranchDocument,
  BrandDocument,
  Company,
  CustomerDocument,
  EmbeddedBrandDto,
  GeofencingConfig,
  IMAGE_URLS,
  LoggerService,
  PassConfig,
  PassField,
  PassTemplate,
  PassTemplateType,
  PassTypeIdentifierConfig,
  RegisteredPass,
  responseCode,
  UnreachableError,
  WalletApp,
} from '@app/shared-stuff';
import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { instanceToPlain } from 'class-transformer';
import * as fs from 'fs/promises';
import * as handlebars from 'handlebars';
import { sign } from 'jsonwebtoken';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import {
  Field,
  Location,
  OverridablePassProps,
  PKPass,
} from 'passkit-generator';
import * as randomstring from 'randomstring';
import { BranchService } from '../../../branch/services/branch/branch.service';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { CustomerPassServiceInterface } from '../../../customer/modules/customer-pass/customer-pass.service.interface';
import { CustomerReadServiceInterface } from '../../../customer/modules/customer-read/customer-read.service.interface';
import { SavedLocationService } from '../../../location/services/saved-location/saved-location.service';
import { CustomerTokenPayload } from '../../../shared/dto/customer-token-payload.dto';
import { HelperService } from '../../../shared/services/helper/helper.service';
import { ImageService } from '../../../shared/services/image/image.service';
import { GeneratePassDto } from '../../dto/generate-pass.dto';
import { GetUpdatedPassResponse } from '../../dto/get-updated-pass-response.dto';
import { ListPassesResponse } from '../../dto/list-passes-response.dto';
import { ListPassesDto } from '../../dto/list-passes.dto';
import { RegisterPassDto } from '../../dto/register-pass.dto';
import { PassTypeIdentifierConfigRepository } from '../../repositories/pass-type-identifier-config.repository';
import { PassCertificates } from '../../types/pass-certificates.type';
import {
  PassListParams,
  PassRegistrationParams,
  PassUpdateParams,
} from '../../types/pass-registration-params.type';
import { PKPassFieldLocation } from '../../types/pkpass-field.type';
import { PassConfigService } from '../pass-config/pass-config.service';
import { PassesService } from '../passes.service';

@Injectable()
export class PKPassPassesService {
  private readonly loggerService = new LoggerService(PKPassPassesService.name);

  constructor(
    @Inject(CustomerReadServiceInterface)
    private readonly customerReadService: CustomerReadServiceInterface,
    @Inject(CustomerPassServiceInterface)
    private readonly customerPassService: CustomerPassServiceInterface,
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandServiceInterface,
    private readonly configService: ConfigService,
    private readonly helperService: HelperService,
    private readonly branchService: BranchService,
    private readonly imageService: ImageService,
    private readonly passTypeIdentifierConfigRepository: PassTypeIdentifierConfigRepository,
    private readonly passConfigService: PassConfigService,
    private readonly savedLocationService: SavedLocationService,
    private readonly passesService: PassesService,
  ) {}

  async getUpdatedPass(
    authHeader: string,
    { passTypeIdentifier, serialNumber }: PassUpdateParams,
  ): Promise<GetUpdatedPassResponse> {
    const { customerId, brandId } =
      this.helperService.parseCustomerTokenPayload(authHeader);
    const customer = await this.customerReadService.findOne(
      customerId.toHexString(),
    );

    const registeredPass = customer.registeredPasses.find(
      (pass: RegisteredPass) =>
        pass.passTypeIdentifier === passTypeIdentifier &&
        pass.serialNumber === serialNumber,
    );
    if (!registeredPass)
      throw new NotFoundException({
        code: responseCode.MISSING_DATA,
        message: 'Pass not found',
      });

    const brand = await this.brandService.findById(brandId);
    const pass = await this.generatePass(customer, brand, registeredPass);
    await this.customerPassService.applyPassUpdatePostFunctions(registeredPass);
    return {
      lastModified: customer.updatedAt,
      pass: pass,
    };
  }

  async generatePassWithoutAuth({
    customerId,
    brandId,
  }: GeneratePassDto): Promise<PKPass> {
    const customer = await this.customerReadService.findById(customerId);
    const brand = await this.brandService.findById(brandId);
    const pass = await this.generatePass(customer, brand);

    return pass;
  }

  async listUpdatablePasses(
    { deviceLibraryIdentifier, passTypeIdentifier }: PassListParams,
    { passesUpdatedSince }: ListPassesDto,
  ): Promise<ListPassesResponse> {
    const unixTimeInSeconds = parseInt(passesUpdatedSince || '0') / 1000;
    const updatedSince = moment.unix(unixTimeInSeconds).toDate();

    const defaultPassTypeIdentifier = this.configService.get(
      'DEFAULT_PASS_TYPE_IDENTIFIER',
    );
    const isUsingOldPassType = passTypeIdentifier === defaultPassTypeIdentifier;

    const customers = await this.customerReadService.findByRegisteredPasses(
      isUsingOldPassType
        ? { deviceLibraryIdentifier }
        : { deviceLibraryIdentifier, passTypeIdentifier },
      updatedSince,
    );
    if (!customers || customers.length === 0) {
      return {
        serialNumbers: [],
        lastUpdated: moment.utc().valueOf().toString(),
      };
    }

    const updatedPasses = customers
      .flatMap((customer) => customer.registeredPasses || [])
      .filter(
        (pass: RegisteredPass) =>
          pass.passTypeIdentifier === passTypeIdentifier &&
          pass.deviceLibraryIdentifier === deviceLibraryIdentifier,
      );

    return {
      serialNumbers: updatedPasses.map((pass) => pass.serialNumber),
      lastUpdated: moment.utc().valueOf().toString(),
    };
  }

  async registerPass(
    authHeader: string,
    {
      deviceLibraryIdentifier,
      passTypeIdentifier,
      pushToken,
      serialNumber,
    }: PassRegistrationParams & RegisterPassDto,
  ): Promise<number> {
    const { customerId } =
      this.helperService.parseCustomerTokenPayload(authHeader);
    const customer = await this.customerReadService.findOne(
      customerId.toHexString(),
    );

    const exists = (customer.registeredPasses || []).some(
      (existingPass: RegisteredPass) =>
        existingPass.serialNumber === serialNumber &&
        existingPass.passTypeIdentifier === passTypeIdentifier &&
        existingPass.deviceLibraryIdentifier === deviceLibraryIdentifier &&
        !existingPass.deletedAt,
    );
    if (exists) return 200;

    const newPass: RegisteredPass = {
      deviceLibraryIdentifier,
      passTypeIdentifier,
      serialNumber,
      pushToken,
      walletApp: this.getWalletApp(authHeader),
      deviceData: customer.deviceData,
      createdAt: moment.utc().toDate(),
      updatedAt: moment.utc().toDate(),
    };
    await this.passesService.saveNewPass(customer, newPass);

    return 201;
  }

  async unregisterPass(
    authHeader: string,
    {
      deviceLibraryIdentifier,
      passTypeIdentifier,
      serialNumber,
    }: PassRegistrationParams,
  ): Promise<number> {
    const { customerId } =
      this.helperService.parseCustomerTokenPayload(authHeader);
    const customer = await this.customerReadService.findOne(
      customerId.toHexString(),
    );
    const registeredPasses = customer.registeredPasses || [];

    const foundPass = registeredPasses.find(
      (existingPass: RegisteredPass) =>
        existingPass.serialNumber === serialNumber &&
        existingPass.passTypeIdentifier === passTypeIdentifier &&
        existingPass.deviceLibraryIdentifier === deviceLibraryIdentifier &&
        !existingPass.deletedAt,
    );
    if (!foundPass) {
      this.loggerService.warn('Tried to delete pass that does not exist.', {
        deviceLibraryIdentifier,
        passTypeIdentifier,
        serialNumber,
        customer,
      });
    } else {
      await this.passesService.deletePass(customer, foundPass);
    }

    return 200;
  }

  public getNewSerialNumber(customer: CustomerDocument): string {
    return customer._id.toHexString() + randomstring.generate(10);
  }

  private getWalletApp(authHeader: string): WalletApp {
    if (!authHeader || !authHeader.includes(' ')) {
      this.loggerService.error(`Invalid auth header: ${authHeader}`);
      return WalletApp.UNKNOWN;
    }

    const headerParts = authHeader.split(' ');
    const passType = headerParts[0];

    if (passType === 'ApplePass') return WalletApp.APPLE_WALLET;
    if (passType === 'AndroidPass') return WalletApp.WALLET_PASSES;

    this.loggerService.error(`Couldn't determine wallet app from ${passType}`);
    return WalletApp.UNKNOWN;
  }

  private async generatePass(
    customer: CustomerDocument,
    brand: BrandDocument,
    existingPass?: RegisteredPass,
  ): Promise<PKPass> {
    await this.customerPassService.applyPassGenerationPreFunctions(customer);

    const passFieldContext = await this.passesService.getPassFieldContext(
      customer,
      brand,
    );

    const passConfig = await this.passConfigService.getPassConfig(
      brand.companyId,
      brand._id,
      customer.loyaltyTier?._id,
    );

    const passTypeIdentifierConfig = await this.getPassTypeIdentifierConfig(
      passConfig?.passTypeIdentifierConfig,
      existingPass,
    );

    const certificates = await this.getCertificates(passTypeIdentifierConfig);
    const pass = await this.getPassTemplate(passConfig, certificates, {
      serialNumber:
        existingPass?.serialNumber ?? this.getNewSerialNumber(customer),
      webServiceURL: this.configService.get('HOST_URL'),
      authenticationToken: this.getAuthToken(customer, brand),
      organizationName: this.getGeofencingNotificationTitle(
        passConfig?.geofencingConfig,
        passFieldContext.company,
        brand,
        customer,
      ),
      passTypeIdentifier: passTypeIdentifierConfig.passTypeIdentifier,
    });

    await this.addIcon(pass, brand, passConfig?.passTemplate);

    await this.addLocations(
      pass,
      customer,
      passConfig?.geofencingConfig,
      passFieldContext.company,
    );

    pass.addBuffer(
      'strip.png',
      await this.passesService.getStripImage(
        customer,
        passFieldContext.loyaltyTierProgramProgress,
        passConfig,
        passFieldContext.company,
      ),
    );

    this.addFieldsToPass(
      pass,
      'headerFields',
      await this.passesService.getHeaderFields(passConfig, passFieldContext),
    );

    this.addFieldsToPass(
      pass,
      'primaryFields',
      await this.passesService.getPrimaryFields(passConfig, passFieldContext),
    );

    this.addFieldsToPass(
      pass,
      'secondaryFields',
      await this.passesService.getSecondaryFields(passConfig, passFieldContext),
    );

    this.addFieldsToPass(
      pass,
      'backFields',
      await this.passesService.getBackFields(passConfig, passFieldContext),
    );

    pass.setBarcodes(await this.passesService.getQRCode(customer, passConfig));

    return pass;
  }

  private getGeofencingNotificationTitle(
    geofencingConfig: GeofencingConfig,
    company: Company,
    brand: BrandDocument,
    customer: CustomerDocument,
  ): string {
    if (!geofencingConfig?.notificationTitle) return brand.name;

    const template = handlebars.compile(geofencingConfig.notificationTitle);
    return template(
      { company, brand, customer },
      { allowProtoPropertiesByDefault: true },
    );
  }

  private async getPassTypeIdentifierConfig(
    configuredPassTypeIdentifierConfig: PassTypeIdentifierConfig,
    existingPass: RegisteredPass,
  ) {
    const isOutdatedPassTypeIdentifier =
      existingPass &&
      existingPass.passTypeIdentifier !==
        configuredPassTypeIdentifierConfig?.passTypeIdentifier;

    if (isOutdatedPassTypeIdentifier)
      return await this.passTypeIdentifierConfigRepository.findByPassTypeIdentifier(
        existingPass.passTypeIdentifier,
      );

    if (!configuredPassTypeIdentifierConfig)
      return await this.passTypeIdentifierConfigRepository.findByPassTypeIdentifier();

    return configuredPassTypeIdentifierConfig;
  }

  private async getPassTemplate(
    passConfig: PassConfig,
    certificates: PassCertificates,
    overrides: Partial<OverridablePassProps>,
  ) {
    const passTemplate = passConfig?.passTemplate ?? {
      type: PassTemplateType.BUFFER,
      backgroundColor: 'rgb(0,0,0)',
      labelTextColor: 'rgb(255,255,255)',
      valueTextColor: 'rgb(255,255,255)',
      logo: { url: IMAGE_URLS.ENABLE_LOGO },
      icon: { url: IMAGE_URLS.ENABLE_LOGO },
    };
    switch (passTemplate.type) {
      case PassTemplateType.FILE:
        return await PKPass.from(
          { model: passTemplate.name, certificates },
          { ...overrides },
        );
      case PassTemplateType.BUFFER:
        return new PKPass(
          {
            'logo.png': await this.imageService.readImageData(
              passTemplate.logo,
            ),
            'pass.json': Buffer.from(
              JSON.stringify({
                description: `${passConfig?.owner?.name} Loyalty Card`,
                organizationName: 'Enable Tech for Trading & Services',
                backgroundColor: passTemplate.backgroundColor,
                foregroundColor: passTemplate.valueTextColor,
                labelColor: passTemplate.labelTextColor,
                teamIdentifier: 'Q96J53TA69',
                formatVersion: 1,
                storeCard: {},
              }),
            ),
          },
          certificates,
          overrides,
        );
      default:
        throw new UnreachableError(passTemplate);
    }
  }

  private async addIcon(
    pass: PKPass,
    brand: BrandDocument,
    passTemplate: PassTemplate,
  ) {
    const iconBuffer = await this.getIconBuffer(brand, passTemplate);
    if (!iconBuffer) return;

    pass.addBuffer('icon.png', iconBuffer);
    pass.addBuffer('icon2x.png', iconBuffer);
    pass.addBuffer('icon3x.png', iconBuffer);
  }

  private async getIconBuffer(
    brand: BrandDocument,
    passTemplate: PassTemplate,
  ) {
    if (brand.image) return await this.imageService.readImageData(brand.image);

    if (!passTemplate) return;
    if (passTemplate.type === PassTemplateType.FILE) return;
    if (!passTemplate.icon) return;

    return await this.imageService.readImageData(passTemplate.icon);
  }

  private async addLocations(
    pass: PKPass,
    customer: CustomerDocument,
    geofencingConfig: GeofencingConfig,
    company: Company,
  ): Promise<void> {
    if (geofencingConfig?.isDisabled) return;
    const locations: Location[] = [];

    // Having illegal longitude or latitude will cause the pass to break
    const clampCoordinate = (coordinate: number) =>
      Math.min(Math.max(coordinate, -90), 90);

    const branches = await this.branchService.findAllByIds(
      company.branches.map((branchId) => new Types.ObjectId(branchId)),
    );

    const locationIds = branches
      .filter((branch) => branch.locationId)
      .map((branch) => branch.locationId);
    if (!locationIds.length) return;

    const savedLocations =
      await this.savedLocationService.findAllByIds(locationIds);

    const getNotificationContent =
      this.compileGeofencingNotificationContentTemplate(geofencingConfig);

    for (const branch of branches) {
      if (!branch || !branch.locationId) continue;

      const geofencingNotificationContent = getNotificationContent(
        company,
        branch.brands[0],
        branch,
        customer,
      );

      const location = savedLocations.find(
        (savedLocation) =>
          savedLocation._id.toString() === branch.locationId.toString(),
      );

      locations.push({
        latitude: clampCoordinate(location.latitude),
        longitude: clampCoordinate(location.longitude),
        relevantText: geofencingNotificationContent,
      });
    }

    pass.setLocations(...locations);
  }

  private compileGeofencingNotificationContentTemplate(
    geofencingConfig: GeofencingConfig,
  ): (
    company: Company,
    brand: EmbeddedBrandDto,
    branch: BranchDocument,
    customer: CustomerDocument,
  ) => string {
    if (!geofencingConfig?.notificationContent)
      return (
        _company,
        brand,
        _branch,
        customer,
      ) => `${customer.first_name}! Come visit us at ${brand?.name}.
We would love to see you!`;

    const template = handlebars.compile(geofencingConfig.notificationContent);
    return (company, brand, branch, customer) =>
      template(
        { company, brand, branch, customer },
        { allowProtoPropertiesByDefault: true },
      );
  }

  private async getCertificates({
    passCertFile,
    passKeyFile,
  }: PassTypeIdentifierConfig): Promise<PassCertificates> {
    const wwdr = await fs.readFile('./files/wwdr.pem');
    const signerCert = await fs.readFile(
      passCertFile ?? './files/signerCert.pem',
    );
    const signerKey = await fs.readFile(passKeyFile ?? './files/signerKey.pem');
    const signerKeyPassphrase = this.configService.get<string>(
      'APPLE_WALLET_PASSPHRASE',
    );

    return {
      wwdr,
      signerCert,
      signerKey,
      signerKeyPassphrase,
    };
  }

  private getAuthToken(customer: CustomerDocument, brand: BrandDocument) {
    const payload = new CustomerTokenPayload();
    payload.customerId = customer._id;
    payload.companyId = customer.company;
    payload.brandId = brand._id;

    return sign(
      instanceToPlain(payload),
      this.configService.get('MAIN_JWT_SECRET'),
    );
  }

  private addFieldsToPass(
    pass: PKPass,
    location: PKPassFieldLocation,
    passFields: PassField[],
  ) {
    if (!passFields?.length) return;

    passFields
      .filter(Boolean)
      .forEach(({ key, label, value, changeMessage }) => {
        const labelPlaceholder = `l_${key}`;
        const valuePlaceholder = `v_${key}`;

        const field: Field = {
          key,
          changeMessage,
          label: labelPlaceholder,
          value: valuePlaceholder,
        };

        pass.localize('en', {
          [labelPlaceholder]: label.en,
          [valuePlaceholder]: value.en,
        });

        pass.localize('ar', {
          [labelPlaceholder]: label.ar,
          [valuePlaceholder]: value.ar,
        });

        pass[location].push(field);
      });
  }
}
