import {
  CurrentUser,
  OrderDocument,
  OrderLogActionEnum,
  OrderLogReceivedObjectDto,
  OrderLogSentObjectDto,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { OrderLogIndexDto } from '../../dto/order-log-index.dto';
import { OrderLogRepositoryInterface } from '../../repository/interfaces/order-log.repository.interface';
import { OrderLogServiceInterface } from '../interfaces/order-log.service.interface';

@Injectable()
export class OrderLogService implements OrderLogServiceInterface {
  availableActions = [
    OrderLogActionEnum.ON_ORDER_PENDING,
    OrderLogActionEnum.ON_ORDER_CANCELED,
    OrderLogActionEnum.ON_ORDER_COMPLETED,
    OrderLogActionEnum.ON_ORDER_IN_ROUTE,
    OrderLogActionEnum.ON_ORDER_UNASSIGNED,
    OrderLogActionEnum.ON_ORDER_GENERAL_CREATED,
    OrderLogActionEnum.ON_ORDER_NOTIFICATION_ACKNOWLEDGE,
    OrderLogActionEnum.ON_ORDER_PENDING_PICKUP,
    OrderLogActionEnum.ON_ORDER_REJECTED,
    OrderLogActionEnum.ON_ORDER_ASSIGNED_TO_BRANCH,
    OrderLogActionEnum.ON_PAYMENT_COMPLETED,
    OrderLogActionEnum.ON_ORDER_ASSIGNED_TO_DRIVER,
    OrderLogActionEnum.ON_ORDER_DETAILS_EDITED,
    OrderLogActionEnum.ORDER_DELIVERY_LOCATION_UPDATED,
    OrderLogActionEnum.ON_PAYMENT_COMPLETED,
    OrderLogActionEnum.ON_ORDER_PAYMENT_METHOD_CHANGED,
    OrderLogActionEnum.ON_ORDER_MANUALLY_CAPTURED,
    OrderLogActionEnum.ON_TOOKAN_TASK_CANCELED,
  ];

  constructor(
    @Inject('OrderLogRepositoryInterface')
    private readonly orderLogRepository: OrderLogRepositoryInterface,
  ) {}

  async findAll(orderLogIndexDto: OrderLogIndexDto): Promise<any[]> {
    const pipeline = [];
    const match = {};
    this.addMatchStage(match, pipeline, orderLogIndexDto);
    this.addSortStage(pipeline);
    this.addPaginationStage(pipeline, orderLogIndexDto);
    return await this.orderLogRepository.aggregate(pipeline);
  }

  private addMatchStage(
    match: any,
    pipeline: any[],
    orderLogIndexDto: OrderLogIndexDto,
  ) {
    if (orderLogIndexDto.orderCode)
      match['$and'] = [
        { orderCode: orderLogIndexDto.orderCode },
        {
          logAction: {
            $in: this.availableActions,
          },
        },
      ];

    if (orderLogIndexDto.orderAction)
      match['logAction'] = {
        $regex: orderLogIndexDto.orderAction.toString(),
        $options: 'i',
      };
    pipeline.push({ $match: match });
  }

  private addSortStage(pipeline: any[]) {
    pipeline.push({
      $sort: { createdAt: 1 },
    });
  }

  private addPaginationStage(
    pipeline: any,
    orderLogIndexDto: OrderLogIndexDto,
  ) {
    pipeline.push({
      $facet: {
        paginatedResult: [
          ...(Number(orderLogIndexDto.offset)
            ? [
                {
                  $skip:
                    Number(orderLogIndexDto.offset) *
                    Number(orderLogIndexDto.limit),
                },
              ]
            : [
                {
                  $skip: 0,
                },
              ]),
          ...(Number(orderLogIndexDto.limit)
            ? [
                {
                  $limit: Number(orderLogIndexDto.limit),
                },
              ]
            : []),
        ],
        totalCount: [
          {
            $count: 'createdAt',
          },
        ],
      },
    });
  }

  async saveOrderLog(
    order: OrderDocument,
    sentObject: OrderLogSentObjectDto,
    receivedObject: OrderLogReceivedObjectDto,
    logAction: OrderLogActionEnum,
    createdBy: CurrentUser,
  ) {
    const orderLog = {
      sentObject: sentObject,
      receivedObject: receivedObject,
      logAction: logAction,
      orderCode: order.code,
      createdBy: createdBy,
    };
    await this.orderLogRepository.save(orderLog);
  }
}
