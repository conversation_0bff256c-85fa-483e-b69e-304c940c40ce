import {
  PasswordTo<PERSON>ompare,
  UserToLogin,
  UserToRegister,
} from './../../dto/user.dto';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import { CollectionName, LoggerService, responseCode } from '@app/shared-stuff';
import { compare } from 'bcrypt';
import { ConfigService } from '@nestjs/config';
import { JwtPayload, sign, verify } from 'jsonwebtoken';
import { Model } from 'mongoose';
import * as moment from 'moment-timezone';
import { ImageService } from '../../../shared/services/image/image.service';
import { ExpiredTokenDocument } from '../../models/expired_token.model';
import { AuthType } from '../../enums/token-type.enum';
import { UserDocument } from '../../models/user.model';

@Injectable()
export class AuthService {
  private readonly loggerService = new LoggerService(AuthService.name);
  constructor(
    @InjectModel('User') private userModel: Model<UserDocument>,
    private configService: ConfigService,
    @InjectModel('ExpiredToken')
    private expiredTokenModel: Model<ExpiredTokenDocument>,
    private imageService: ImageService,
  ) {}

  async login(userToLogin: UserToLogin) {
    if (!userToLogin.email && !userToLogin.phone) {
      throw {
        code: responseCode.MISSING_DATA,
        statusCode: 422,
        message: 'please provide the user email or phone to login',
      };
    }

    const loggedUser = await this.userModel
      .findOne({
        $or: [{ email: userToLogin.email }, { phone: userToLogin.phone }],
      })
      .exec();

    if (!loggedUser) {
      this.loggerService.error(
        'wrong credential On User failed to Login',
        userToLogin,
      );
      throw {
        code: responseCode.WRONG_CREDENTIAL,
        statusCode: 401,
        message: 'wrong credentials',
      };
    }
    this.loggerService.log(
      'On User Logged in successfully',
      userToLogin,
      loggedUser,
    );

    if (loggedUser.status != 'active') {
      throw {
        code: responseCode.WRONG_CREDENTIAL,
        statusCode: 400,
        message: 'You account is disabled right now',
      };
    }

    const passwordChecked = await compare(
      userToLogin.password,
      loggedUser.password,
    );
    if (!passwordChecked) {
      throw {
        code: responseCode.WRONG_CREDENTIAL,
        statusCode: 401,
        message: 'wrong credentials',
      };
    }

    if (userToLogin.fcm_token) {
      loggedUser.fcm_token = userToLogin.fcm_token;
    }

    const token = sign(
      { id: loggedUser.id, for: AuthType.USER },
      this.configService.get<string>('MAIN_JWT_SECRET'),
      { expiresIn: '30d' },
    );

    loggedUser.auth_token = token;
    await loggedUser.save();
    return token;
  }

  async register(userToRegister: UserToRegister) {
    const createdUser = new this.userModel(userToRegister);
    if (userToRegister.image) {
      userToRegister.image.for = 'user';
      createdUser.image = await this.imageService.uploadImage(
        userToRegister.image,
      );
    }
    await createdUser.save();
    return createdUser;
  }

  async get_me(token) {
    try {
      const verifiedUser = await verify(
        token,
        this.configService.get<string>('MAIN_JWT_SECRET'),
      );

      const selectedUser = await this.userModel
        .findOne({ _id: verifiedUser['id'] })
        .populate({
          path: 'roles',
          populate: { path: 'permissions', model: CollectionName.PERMISSIONS },
          model: CollectionName.ROLES,
        })
        .populate({
          path: 'company',
          select:
            '-skipCashKeyId -skipCashKeySecret -skipCashKeyClientId -skipCashWebHookKey -terminal_id -terminal_password -branches -savedAddress -isDeliveryMethodChangeable',
        })
        .populate('branch')
        .populate({ path: 'branches', model: CollectionName.BRANCH })
        .exec();

      if (!selectedUser) {
        throw {
          code: responseCode.UN_AUTHENTICATED,
          message: 'Invalid Token',
          statusCode: 401,
        };
      }

      return selectedUser;
    } catch (err) {
      console.error(err);
      throw {
        code: responseCode.UN_AUTHERIZED,
        message: 'Token is Not formed well',
        statusCode: 401,
      };
    }
  }

  async checkIfTokenInExpired(token: string) {
    const tokencheck = await this.expiredTokenModel.findOne({ token: token });
    if (tokencheck) {
      return true;
    }
    return false;
  }

  verifyToken(token: string): JwtPayload {
    try {
      return verify(token, this.configService.get<string>('MAIN_JWT_SECRET'), {
        complete: false,
      }) as JwtPayload;
    } catch {
      throw new UnauthorizedException({
        code: responseCode.UN_AUTHERIZED,
        message: 'Token is Not formed well',
        statusCode: 401,
      });
    }
  }

  async logout(token) {
    const expiredToken = new this.expiredTokenModel({ token: token });
    const verifiedUser = await verify(
      token,
      this.configService.get<string>('MAIN_JWT_SECRET'),
    );
    const selectedUser = await this.userModel.findOne({
      _id: verifiedUser['id'],
    });
    selectedUser.last_active = moment().toDate();
    selectedUser.fcm_token = undefined;
    await selectedUser.save();
    await expiredToken.save();
    return expiredToken;
  }

  async compare_password(passwordToCompare: PasswordToCompare) {
    const selectedUser = await this.userModel.findOne({
      _id: passwordToCompare.user_id,
    });
    if (!selectedUser) {
      throw {
        code: responseCode.MISSING_DATA,
        statusCode: 422,
        message: 'please provide the correct user_id',
      };
    }
    const passwordCompare = await compare(
      passwordToCompare.password,
      selectedUser.password,
    );
    return passwordCompare;
  }
}
