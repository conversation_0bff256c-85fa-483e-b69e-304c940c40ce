import { Customer, CustomerOrdableInfo } from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { Types } from 'mongoose';
import { CustomerWebstoreServiceInterface } from './customer-webstore.service.interface';

@Injectable()
export class CustomerWebstoreService
  implements CustomerWebstoreServiceInterface
{
  public getOrdableLink(
    customer: Customer,
    brandId: Types.ObjectId,
  ): string | null {
    if (!customer || !customer.ordableStores) return null;

    const ordableInfos: CustomerOrdableInfo[] = Object.values(
      customer.ordableStores,
    );
    if (ordableInfos.length === 0) return null;

    const matchingOrdableInfos = ordableInfos.filter((ordableInfo) =>
      ordableInfo.ordableBrands.some((brand) => brand._id.equals(brandId)),
    );
    if (matchingOrdableInfos.length === 0) return null;
    if (matchingOrdableInfos.length === 1)
      return matchingOrdableInfos[0].ordableLink;

    const storeWithLeastBrands = matchingOrdableInfos.reduce(
      (withLeastBrands: CustomerOrdableInfo, current: CustomerOrdableInfo) =>
        current.ordableBrands.length < withLeastBrands.ordableBrands.length
          ? current
          : withLeastBrands,
      matchingOrdableInfos[0],
    );

    return storeWithLeastBrands.ordableLink;
  }
}
