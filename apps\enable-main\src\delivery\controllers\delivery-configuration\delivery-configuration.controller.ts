import {
  CreateDeliveryConfigurationDto,
  GenericExceptionFilter,
  GetDeliveryConfigurationDto,
  IndexDeliveryConfigurationDto,
  TransformInterceptor,
  UpdateDeliveryConfigurationDto,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { DeliveryConfiguration } from '../../models/delivery-configuration.model';
import { DeliveryConfigurationServiceInterface } from '../../services/delivery-configuration/delivery-configuration.service.interface';

@Controller('delivery-configuration')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags(DeliveryConfiguration.name)
@SetMetadata('module', 'DeliveryConfiguration')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class DeliveryConfigurationController {
  constructor(
    @Inject(DeliveryConfigurationServiceInterface)
    private readonly deliveryConfigurationService: DeliveryConfigurationServiceInterface,
  ) {}

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() createDeliveryConfigurationDto: CreateDeliveryConfigurationDto,
  ) {
    return await this.deliveryConfigurationService.create(
      createDeliveryConfigurationDto,
    );
  }

  @Put()
  @SetMetadata('action', 'update')
  async update(
    @Body() updateDeliveryConfigurationDto: UpdateDeliveryConfigurationDto,
  ) {
    return await this.deliveryConfigurationService.update(
      updateDeliveryConfigurationDto,
    );
  }

  @Get()
  @SetMetadata('action', 'index')
  async index(
    @Query() indexDeliveryConfigurationDto: IndexDeliveryConfigurationDto,
  ) {
    return await this.deliveryConfigurationService.index(
      indexDeliveryConfigurationDto,
    );
  }

  @Get(':id')
  @SetMetadata('action', 'findById')
  async findById(@Param('id') id: Types.ObjectId) {
    return await this.deliveryConfigurationService.findById(id);
  }

  @Delete(':id')
  @SetMetadata('action', 'delete')
  async delete(@Param('id') id: Types.ObjectId) {
    return await this.deliveryConfigurationService.delete(id);
  }

  @Get('/find/getConfiguration')
  @SetMetadata('action', 'get_details')
  async getDeliveryConfiguration(
    @Query() getDeliveryConfigurationDto: GetDeliveryConfigurationDto,
  ) {
    return await this.deliveryConfigurationService.getDeliveryConfiguration(
      getDeliveryConfigurationDto,
    );
  }
}
