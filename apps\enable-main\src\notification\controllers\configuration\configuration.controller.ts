import {
  GenericExceptionFilter,
  GetConfigurationDto,
  OwnerIdDto,
  TransformInterceptor,
  UpdateConfigurationDto,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { ConfigurationService } from '../../services/configuration/configuration.service';

@Controller('notification/configuration')
@ApiTags('Notification-Configuration')
@SetMetadata('module', 'notification-configuration')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
export class ConfigurationController {
  constructor(private configurationService: ConfigurationService) {}

  @Get()
  @SetMetadata('action', 'getDetails')
  public async index(@Query() getConfigurationDto: GetConfigurationDto) {
    return await this.configurationService.getConfiguration(
      getConfigurationDto,
    );
  }

  @Get(':ownerId/hasWhatsappKey')
  @SetMetadata('public', 'true')
  public async hasWhatsappKey(@Param() { ownerId }: OwnerIdDto) {
    return await this.configurationService.hasWhatsappKey(ownerId);
  }

  @Post()
  @SetMetadata('action', 'update-company')
  public async updateCompanyConfiguration(
    @Body() updateConfigurationDto: UpdateConfigurationDto,
  ) {
    return await this.configurationService.updateConfiguration(
      updateConfigurationDto,
    );
  }

  @Put()
  @SetMetadata('action', 'update-brand')
  public async updateBrandConfiguration(
    @Body() updateConfigurationDto: UpdateConfigurationDto,
  ) {
    return await this.configurationService.updateConfiguration(
      updateConfigurationDto,
    );
  }
}
