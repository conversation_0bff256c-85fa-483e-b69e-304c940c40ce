import {
  CollectionName,
  DocumentRedirectSchema,
  SharedStuffModule,
} from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BrandModule } from '../brand/brand.module';
import { CompanyModule } from '../company/company.module';
import { CustomerIndexModule } from '../customer/modules/customer-index/customer-index.module';
import { CustomerWriteModule } from '../customer/modules/customer-write/customer-write.module';
import { NotificationModule } from '../notification/notification.module';
import { OrderModule } from '../order/order.module';
import { PaymentModule } from '../payment/payment.module';
import { SharedModule } from '../shared/shared.module';
import { StoreModule } from '../store/store.module';
import { BranchModule } from './../branch/branch.module';
import { MainController } from './controllers/main/main.controller';
import { MainService } from './services/main/main.service';
import { PhonePopulateService } from './services/phone-populate/phone-populate.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: CollectionName.DOCUMENT_REDIRECT,
        schema: DocumentRedirectSchema,
      },
    ]),
    OrderModule,
    CustomerIndexModule,
    CustomerWriteModule,
    SharedModule,
    PaymentModule,
    BranchModule,
    CompanyModule,
    BrandModule,
    NotificationModule,
    StoreModule,
    SharedStuffModule,
  ],
  controllers: [MainController],
  providers: [MainService, PhonePopulateService],
})
export class MainModule {}
