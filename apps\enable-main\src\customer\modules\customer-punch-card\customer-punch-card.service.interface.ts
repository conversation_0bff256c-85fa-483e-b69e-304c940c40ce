import {
  CustomerDocument,
  EarnedReward,
  NextAchievement,
  OrderToComplete,
  PunchCardCounter,
  PunchCardDocument,
  PunchCardProgress,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface CustomerPunchCardServiceInterface {
  findPunchCardsToInit(
    customer: CustomerDocument,
  ): Promise<PunchCardDocument[]>;
  initPunchCardProgress(customer: CustomerDocument): Promise<void>;
  incrementPunchCardCounters(
    customer: CustomerDocument,
    order?: OrderToComplete,
  ): Promise<void>;
  getNextAchievement(customer: CustomerDocument): Promise<NextAchievement>;
  getNextAchievements(customer: CustomerDocument): Promise<NextAchievement[]>;
  getRemainingStampsNextReward(
    customer: CustomerDocument,
  ): Promise<number | null>;
  getProgressEarnedRewards(
    customer: CustomerDocument,
    progress: PunchCardProgress,
  ): EarnedReward[];
  getIncrementAmount(
    progress: PunchCardProgress,
    order: OrderToComplete,
  ): number;
  getRewardsUnlockedOnIncrement(
    customer: CustomerDocument,
    progress: PunchCardProgress,
    increment: number,
  ): Promise<number>;
  getPunchCardCounters(
    companyId: Types.ObjectId,
    customerId?: Types.ObjectId,
  ): Promise<PunchCardCounter[]>;
}

export const CustomerPunchCardServiceInterface = Symbol(
  'CustomerPunchCardServiceInterface',
);
