import { DataIndex, OrderDeliveryType } from '@app/shared-stuff';
import { ApiProperty } from '@nestjs/swagger';

export class SlotToCreate {
  @ApiProperty({
    required: true,
    type: String,
  })
  from: string;

  @ApiProperty({
    required: true,
    type: String,
  })
  to: string;

  @ApiProperty({
    type: Number,
    default: 1,
  })
  maximum_number_of_orders: number;

  @ApiProperty({
    type: Number,
    default: 1,
  })
  maximum_number_of_urgent_orders: number;

  @ApiProperty({
    type: String,
    enum: ['ebutler', 'company'],
    default: 'ebutler',
  })
  owner: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  company: string;

  @ApiProperty({
    type: [String],
    required: true,
  })
  days: string[];

  current_user: {};
}

export class SlotToIndex extends DataIndex {
  @ApiProperty({
    type: String,
    enum: ['ebutler', 'company', 'all'],
    default: 'ebutler',
    required: true,
  })
  owner: string;

  @ApiProperty({
    type: String,
    required: false,
    enum: [...Object.keys(OrderDeliveryType)],
  })
  delivery_type: OrderDeliveryType;

  @ApiProperty({
    type: String,
    required: false,
  })
  company: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  day: string;
}

export class SlotToUpdate {
  @ApiProperty({
    required: true,
    type: String,
  })
  _id: string;

  @ApiProperty({
    required: true,
    type: String,
  })
  from: string;

  @ApiProperty({
    required: true,
    type: String,
  })
  to: string;

  @ApiProperty({
    type: Number,
    default: 1,
  })
  maximum_number_of_orders: number;

  @ApiProperty({
    type: String,
    enum: ['ebutler', 'company'],
    default: 'ebutler',
  })
  owner: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  company: string;

  @ApiProperty({
    type: [String],
    required: true,
  })
  days: string[];

  current_user: {};
}
