import { MicroserviceCommunicationService } from '@app/shared-stuff/services/microservice-communication.service';
import {
  INestApplication,
  Logger,
  ValidationPipe,
  VERSION_NEUTRAL,
  VersioningType,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as admin from 'firebase-admin';
import { ServiceAccount } from 'firebase-admin';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import { EnableMainModule } from './enable-main.module';
import corsOptions from './shared/security';

function setupSwagger(app: INestApplication, configService: ConfigService) {
  //Setup Swagger
  const options = new DocumentBuilder()
    .setTitle('EButler Enable')
    .setDescription('Enable API documentation')
    .setVersion('1.0')
    .setBasePath(configService.get('HOST_URL'))
    .addServer(configService.get('HOST_URL'))
    .addBasicAuth(
      { type: 'http', scheme: 'bearer', bearerFormat: 'JWT' },
      'authenticate-token',
    )
    .addBasicAuth(
      { type: 'apiKey', in: 'header', name: 'saasapikey' },
      'authenticate-apikey',
    )
    .build();
  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup(configService.get('MAIN_SWAGGER_UI_URL'), app, document);
}

function setupNotification(configService: ConfigService) {
  const adminConfig: ServiceAccount = {
    projectId: configService.get<string>('FIREBASE_PROJECT_ID'),
    privateKey: configService
      .get<string>('FIREBASE_PRIVATE_KEY')
      .replace(/\\n/g, '\n'),
    clientEmail: configService.get<string>('FIREBASE_CLIENT_EMAIL'),
  };
  admin.initializeApp({
    credential: admin.credential.cert(adminConfig),
    databaseURL: 'https://saas-a6939.firebaseio.com',
  });
}

async function bootstrap() {
  const appName = 'enable-main';
  const app = await NestFactory.create<NestExpressApplication>(
    EnableMainModule,
    { bufferLogs: true, rawBody: true },
  );

  app.useLogger(app.get(WINSTON_MODULE_NEST_PROVIDER));
  const configService: ConfigService = app.get(ConfigService);
  app.useBodyParser('json', { limit: '50mb' });
  app.enableCors(corsOptions);
  app.useStaticAssets('./public');
  app.useStaticAssets('./assets');
  setupNotification(configService);
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: VERSION_NEUTRAL,
  });
  setupSwagger(app, configService);

  const logger = new Logger(appName);

  app.enableShutdownHooks();

  const microserviceCommunicationService = app.get(
    MicroserviceCommunicationService,
  );
  //

  const port = configService.get<number>('MAIN_PORT');
  await app.listen(port).then(() => {
    logger.log(`Start Listening On Port ${port}`);
    if (process.send) process.send('ready');
  });

  // Microservice Connection
  await microserviceCommunicationService.establishMainConnection(app, appName);
}

bootstrap();
