import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type PrivilegeDocument = HydratedDocument<Privilege>;
@Schema({ timestamps: true })
export class Privilege {
  @Prop({
    required: true,
  })
  value: string;

  @Prop({
    required: true,
    index: true,
  })
  module: string;

  @Prop({
    required: true,
  })
  label: string;

  @Prop({
    required: false,
  })
  parentKey: string;

  @Prop({
    type: {},
    required: false,
  })
  updatedBy: {};

  @Prop({
    type: {},
    required: false,
  })
  createdBy: {};
  @Prop({
    type: {},
    required: false,
  })
  deletedBy: {};

  @Prop({
    type: Date,
    required: false,
  })
  deletedAt: Date;
}

export const PrivilegeSchema = SchemaFactory.createForClass(Privilege);
