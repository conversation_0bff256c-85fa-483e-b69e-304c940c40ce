import { ApiProperty } from '@nestjs/swagger';

export class DeliverectProduct {
  @ApiProperty({
    type: Number,
    required: true,
  })
  productType: number;

  @ApiProperty({
    type: String,
    required: true,
  })
  plu: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  name: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  price: number;

  @ApiProperty({
    type: Number,
    required: true,
  })
  min: number;

  @ApiProperty({
    type: Number,
    required: true,
  })
  max: number;

  @ApiProperty({
    type: String,
    required: true,
  })
  posProductId: string;

  @ApiProperty({
    type: [String],
    required: true,
  })
  posCategoryIds: string[];

  @ApiProperty({
    type: String,
    required: true,
  })
  imageUrl: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  uniqueKey: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  description: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  deliveryTax: number;

  @ApiProperty({
    type: Number,
    required: true,
  })
  takeawayTax: number;

  @ApiProperty({
    type: [String],
    required: true,
  })
  subProducts: string[];

  @ApiProperty({
    type: [Number],
    required: true,
  })
  productTags: number[];
}
