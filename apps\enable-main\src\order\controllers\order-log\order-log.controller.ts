import {
  GenericExceptionFilter,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Controller,
  Get,
  Inject,
  Query,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { OrderLogIndexDto } from '../../dto/order-log-index.dto';
import { OrderLogServiceInterface } from '../../services/interfaces/order-log.service.interface';

@ApiTags('OrderLog')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@SetMetadata('module', 'orderLog')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@Controller('orderLog')
export class OrderLogController {
  constructor(
    @Inject('OrderLogServiceInterface')
    private readonly orderLogService: OrderLogServiceInterface,
  ) {}

  @Get()
  @SetMetadata('action', 'findAll')
  async findAll(@Query() orderLogIndexDto: OrderLogIndexDto) {
    const orderLogs = await this.orderLogService.findAll(orderLogIndexDto);
    const result = {
      orderLogs: orderLogs[0]['paginatedResult'],
      totalCount: orderLogs[0]['totalCount'][0]
        ? orderLogs[0]['totalCount'][0]['createdAt']
        : 0,
    };
    return result;
  }
}
