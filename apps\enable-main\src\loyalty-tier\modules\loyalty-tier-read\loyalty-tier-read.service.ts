import { LoyaltyTier, LoyaltyTierDocument } from '@app/shared-stuff';
import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { Types } from 'mongoose';
import { LoyaltyTierRepositoryInterface } from '../loyalty-tier-repository/loyalty-tier.repository.interface';
import { LoyaltyTierReadServiceInterface } from './loyalty-tier-read.service.interface';

@Injectable()
export class LoyaltyTierReadService implements LoyaltyTierReadServiceInterface {
  constructor(
    @Inject(LoyaltyTierRepositoryInterface)
    private readonly loyaltyTierRepository: LoyaltyTierRepositoryInterface,
  ) {}

  async findById(loyaltytierId: Types.ObjectId): Promise<LoyaltyTierDocument> {
    const loyaltyTier =
      await this.loyaltyTierRepository.findById(loyaltytierId);

    if (!loyaltyTier) {
      throw new NotFoundException(
        `Loyalty Tier with id ${loyaltytierId} not found`,
      );
    }

    return loyaltyTier;
  }

  async findNonVipTiersByCompanyId(
    companyId: Types.ObjectId,
  ): Promise<LoyaltyTierDocument[]> {
    return this.loyaltyTierRepository.findNonVipTiersByCompanyId(companyId);
  }

  async findByCompanyId(
    companyId: Types.ObjectId,
  ): Promise<LoyaltyTierDocument[]> {
    return this.loyaltyTierRepository.findByCompanyId(companyId);
  }

  async findHighestEligibleTierAfter(
    companyId: Types.ObjectId,
    currentTierId: Types.ObjectId | null,
    orderRate: number,
    amountSpent: number,
    pointsRate: number,
  ): Promise<LoyaltyTierDocument | null> {
    const loyaltyTiers =
      await this.loyaltyTierRepository.findNonVipTiersByCompanyId(companyId);

    if (!loyaltyTiers || loyaltyTiers.length === 0) return null;

    let currentTier;
    if (currentTierId) {
      currentTier = loyaltyTiers.find(
        (tier) => tier._id.toHexString() === currentTierId?.toHexString(),
      );
    }
    const highestEligibleTier = loyaltyTiers
      .sort((a, b) => b.tierIndex - a.tierIndex)
      .filter((tier) => !currentTier || tier.tierIndex > currentTier?.tierIndex)
      .find(
        ({
          orderRateThreshold,
          amountSpentThreshold,
          pointsRateThreshold,
        }: LoyaltyTier) =>
          (orderRateThreshold && orderRate >= orderRateThreshold) ||
          (amountSpentThreshold && amountSpent >= amountSpentThreshold) ||
          (pointsRateThreshold && pointsRate >= pointsRateThreshold),
      );

    return highestEligibleTier ?? currentTier;
  }

  async findNextTier(
    companyId: Types.ObjectId,
    currentTierIndex?: number | null,
    mustHaveAmountSpent?: boolean,
  ): Promise<LoyaltyTierDocument | null> {
    return await this.loyaltyTierRepository.findNextTier(
      companyId,
      currentTierIndex,
      mustHaveAmountSpent,
    );
  }

  async findPreviousTier(
    companyId: Types.ObjectId,
    currentTierId?: Types.ObjectId | null,
    mustHaveAmountSpent?: boolean,
  ): Promise<LoyaltyTierDocument | null> {
    if (!currentTierId) return null;

    const currentTier = await this.findById(currentTierId);
    if (currentTier.tierIndex === 0) return null;

    return await this.loyaltyTierRepository.findPreviousTier(
      companyId,
      currentTier.tierIndex,
      mustHaveAmountSpent,
    );
  }

  async findByEnrollmentCode(
    enrollmentCode: string,
  ): Promise<LoyaltyTierDocument> {
    const loyaltyTier =
      await this.loyaltyTierRepository.findByEnrollmentCode(enrollmentCode);

    if (!loyaltyTier) {
      throw new NotFoundException(
        `Loyalty Tier with enrollment code ${enrollmentCode} not found`,
      );
    }

    return loyaltyTier;
  }
}
