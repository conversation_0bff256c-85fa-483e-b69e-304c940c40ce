import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CustomerTokenService } from './customer-token.service';
import { CustomerTokenServiceInterface } from './customer-token.service.interface';

@Module({
  providers: [
    {
      provide: CustomerTokenServiceInterface,
      useClass: CustomerTokenService,
    },
  ],
  imports: [ConfigModule],
  exports: [CustomerTokenServiceInterface],
})
export class CustomerTokenModule {}
