// EBL-5451 Points levelling up method & Points balance titles customization
// Init points titles for existing loyalty program configs

db.companies.updateMany(
  {
    loyaltyProgramConfig: { $exists: true },
    'loyaltyProgramConfig.pointsBalanceTitleEn': { $exists: false },
  },
  { $set: { 'loyaltyProgramConfig.pointsBalanceTitleEn': 'Points' } },
);

db.companies.updateMany(
  {
    loyaltyProgramConfig: { $exists: true },
    'loyaltyProgramConfig.pointsBalanceTitleAr': { $exists: false },
  },
  { $set: { 'loyaltyProgramConfig.pointsBalanceTitleAr': 'نقاط' } },
);

db.companies.updateMany(
  {
    loyaltyProgramConfig: { $exists: true },
    'loyaltyProgramConfig.calendarPointsTitleEn': { $exists: false },
  },
  { $set: { 'loyaltyProgramConfig.calendarPointsTitleEn': 'Points' } },
);

db.companies.updateMany(
  {
    loyaltyProgramConfig: { $exists: true },
    'loyaltyProgramConfig.calendarPointsTitleAr': { $exists: false },
  },
  { $set: { 'loyaltyProgramConfig.calendarPointsTitleAr': 'نقاط' } },
);
