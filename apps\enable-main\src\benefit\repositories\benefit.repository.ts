import { CollectionName, GenericRepository } from '@app/shared-stuff';
import {
  Benefit,
  BenefitDocument,
} from '@app/shared-stuff/models/benefit.model';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { BenefitRepositoryInterface } from './benefit.repository.interface';

export class BenefitRepository
  extends GenericRepository<BenefitDocument, Benefit>
  implements BenefitRepositoryInterface
{
  constructor(
    @InjectModel(CollectionName.BENEFIT)
    private benefitModel: Model<BenefitDocument, Benefit>,
  ) {
    super(benefitModel);
  }

  async removeMany(ids: Types.ObjectId[]) {
    await this.benefitModel.deleteMany({ _id: { $in: ids } });
  }
}
