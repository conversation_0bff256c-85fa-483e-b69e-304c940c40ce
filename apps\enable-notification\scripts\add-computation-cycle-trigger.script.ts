// [EBL-5389] Computation Cycle Notification Trigger - Tiers Program
// Add new " ON_COMPUTATION_CYCLE_DAY" trigger

db.triggers.insertOne({
  name: '[CUSTOMER] ON_COMPUTATION_CYCLE_DAY',
  client: 'ENABLE_MAIN',
  action: 'ON_COMPUTATION_CYCLE_DAY',
  module: 'CUSTOMER',
  replacement: [
    'firstName',
    'fullName',
    'loyaltyPointBalance',
    'loyaltyTier',
    'ordableLink',
    'remainingOrdersCurrentTier',
    'remainingOrdersUpperTier',
    'walletPassLink',
    'upperTierDiscountValue',
    'tierDiscountValue',
    'tierDiscountOrderValueThreshold',
    'loyaltyRegistrationPageLink',
    'walletPassAccessPageLink',
    'upperLoyaltyTier',
    'validTill',
    'nextPunchCardAchievementBenefits',
    'nextPunchCardAchievementRequirements',
    'remainingRequirementsUpperTier',
    'remainingRequirementsCurrentTier',
    'remainingAmountSpentUpperTier',
    'remainingAmountSpentCurrentTier',
    'remainingPointsNextCoupon',
    'nextCouponBenefit',
    'highestUnlockedCouponBenefit',
    'nextPunchCardAchievementRequirementsFirstTrack',
    'nextPunchCardAchievementRequirementsSecondTrack',
    'punchCardFirstTrackName',
    'punchCardSecondTrackName',
    'nextPunchCardAchievementBenefitsFirstTrack',
    'nextPunchCardAchievementBenefitsSecondTrack',
    'googleWalletPassLink',
    'remainingPointsCurrentTier',
    'remainingPointsUpperTier',
  ],
  createdAt: new Date(),
  updatedAt: new Date(),
});
