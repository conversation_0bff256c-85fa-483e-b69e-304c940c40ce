import {
  IGenericRepository,
  Shipment,
  ShipmentDocument,
} from '@app/shared-stuff';

export interface ShipmentRepositoryInterface
  extends IGenericRepository<ShipmentDocument, Shipment> {
  findByTrackingNumber(trackingNumber: string): Promise<ShipmentDocument>;

  findOneByTrackingNumberOrId(
    idOrTrackingNumber: string,
  ): Promise<ShipmentDocument>;
}

export const ShipmentRepositoryInterface = Symbol(
  'ShipmentRepositoryInterface',
);
