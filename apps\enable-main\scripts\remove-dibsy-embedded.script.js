const cursor = db.paymentconfigurations.find({});

cursor.forEach((doc) => {
  const update = {};
  const methods = Object.keys(doc.configuration || {});

  let dibsyCount = 0;
  let hasNonInvoiceIntegrationType = false;

  for (const method of methods) {
    const gateway = doc.configuration[method];

    if (gateway?.paymentGatewayType === 'dibsy') {
      dibsyCount++;
    }

    if (gateway?.configuration?.integrationType !== 'invoice') {
      hasNonInvoiceIntegrationType = true;
    }
  }

  const shouldTreatDibsyAsEmbedded =
    dibsyCount < 3 && hasNonInvoiceIntegrationType;

  if (shouldTreatDibsyAsEmbedded) {
    for (const method of methods) {
      const gateway = doc.configuration[method];

      if (gateway?.paymentGatewayType === 'dibsy') {
        if (!update.$set) update.$set = {};
        update.$set[`configuration.${method}.paymentGatewayType`] = '';
        update.$set[`configuration.${method}.configuration`] = {};
      }
    }
  }

  if (update.$set) {
    print(`Updating document _id: ${doc._id}`);
    printjson(update);
    db.paymentconfigurations.updateOne({ _id: doc._id }, update);
  }
});
