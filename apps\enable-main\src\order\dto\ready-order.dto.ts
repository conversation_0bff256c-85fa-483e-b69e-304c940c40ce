import { ApiProperty } from '@nestjs/swagger';
import {
  IsMongoId,
  IsNotEmpty,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { DeliveryOptionsDto } from './delivery-options.dto';

export class ReadyOrderDto {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsMongoId()
  order_id: string;

  @ApiProperty({
    type: () => DeliveryOptionsDto,
    required: false,
    description:
      'If delivery options have not been set for this order, they must be set' +
      ' when the order is marked as ready.',
  })
  @IsOptional()
  @ValidateNested()
  deliveryOptions?: DeliveryOptionsDto;

  traceId: string;
}
