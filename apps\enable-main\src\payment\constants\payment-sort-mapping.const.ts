import { PaymentIndexSortType } from '../enums/payment-index-sort-type.enum';

export const PaymentSortMapping: Record<
  PaymentIndexSortType,
  Record<string, 1 | -1>
> = {
  name_a_z: { customer_name: 1 },
  name_z_a: { customer_name: -1 },
  payment_status_a_z: { status: 1 },
  payment_status_z_a: { status: -1 },
  link_id_asc: { _id: 1 },
  link_id_desc: { _id: -1 },
  amount_asc: { amount: 1 },
  amount_desc: { amount: -1 },
  order_code_asc: { order_code: 1 },
  order_code_desc: { order_code: -1 },
  record_updated: { updatedAt: -1 },
  date_created_asc: { createdAt: 1 },
  date_created_desc: { createdAt: -1 },
  transaction_id_asc: { transaction_date: 1 },
  transaction_id_desc: { transaction_date: -1 },
  code_asc: { code: 1 },
  code_desc: { code: -1 },
  company_asc: { company_name: 1 },
  company_desc: { company_name: -1 },
  transaction_date_asc: { transaction_date: 1 },
  transaction_date_desc: { transaction_date: -1 },
  source_asc: { source: 1 },
  source_desc: { source: -1 },
  created_by_asc: { 'createdBy.name': 1 },
  created_by_desc: { 'createdBy.name': -1 },
};
