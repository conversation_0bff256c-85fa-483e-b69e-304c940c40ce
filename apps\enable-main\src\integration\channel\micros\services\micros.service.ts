import {
  AggregatorOrderToCreate,
  BranchDocument,
  BrandDocument,
  CapturedItem,
  CapturedOrderSource,
  CompanyDocument,
  CountriesNameToIsoCodeMapping,
  CouponDocument,
  CurrentUser,
  CustomerDocument,
  Discount,
  DiscountApplyTo,
  DiscountSource,
  DiscountType,
  LoggerService,
  MicrosBranchConfig,
  MicrosCheckStatus,
  MicrosCompanyConfig,
  MicrosEnableTenderRef,
  MicrosHeader,
  MicrosItemDiscount,
  MicrosMenuItem,
  MicrosServiceCharge,
  MicrosTender,
  omit,
  OrderCreationSource,
  OrderDocument,
  OrderItem,
  OrderPaymentMethod,
  OrderStatusEnum,
  OrderTransitionTrigger,
  PostMicrosOrderDto,
  PunchCardBenefit,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';

import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { BranchService } from '../../../../branch/services/branch/branch.service';
import { CompanyService } from '../../../../company/services/company/company.service';
import { CouponServiceInterface } from '../../../../coupon/services/coupon.service.interface';
import { CustomerReadServiceInterface } from '../../../../customer/modules/customer-read/customer-read.service.interface';
import { AggregatorOrderService } from '../../../../order/services/aggregatorOrder/aggregatorOrder.service';
import { OrderStatusService } from '../../../../order/services/order-status/order-status.service';
import { OrderService } from '../../../../order/services/order/order.service';
import { MenuItemService } from '../../../../restaurant/services/menu-item/menu-item.service';
import { IntegrationLogRepositoryInterface } from '../../../integration-log/repositories/interfaces/integration-log.repository.interface';
import { MicrosCheckHttpService } from './micros-check-http.service';

@Injectable()
export class MicrosService {
  private readonly logger = new LoggerService(MicrosService.name);

  constructor(
    private readonly orderService: OrderService,
    @Inject('IntegrationLogRepositoryInterface')
    private readonly integrationLogRepository: IntegrationLogRepositoryInterface,
    private readonly orderStatusService: OrderStatusService,
    private readonly aggregatorOrderService: AggregatorOrderService,
    @Inject(CustomerReadServiceInterface)
    private readonly customerReadService: CustomerReadServiceInterface,
    private readonly companyService: CompanyService,
    private readonly menuItemService: MenuItemService,
    private readonly microsCheckHttpService: MicrosCheckHttpService,
    private readonly branchService: BranchService,
    @Inject(CouponServiceInterface)
    private readonly couponService: CouponServiceInterface,
  ) {}

  async checkAndUpdateOrderStatus(microsOrderId: string): Promise<boolean> {
    const order = await this.orderService.findOneByMicrosOrderId(microsOrderId);
    if (!order) return false;

    this.logger.log(
      `[Micros] Check ${microsOrderId} already exists with code ${order.code}`,
    );
    if (order.status !== OrderStatusEnum.COMPLETED) {
      order.is_ready = true;
      await this.orderStatusService.changeOrderStatus(
        order,
        OrderStatusEnum.COMPLETED,
        OrderTransitionTrigger.MICROS_CLOSED_CHECK,
        order.createdBy,
      );
      this.logger.log(`[Micros] Order ${order.code} completed `);
    } else {
      await this.integrationLogRepository.logSuccess(
        'micros-order-exists',
        {
          microsOrderId: microsOrderId,
          orderCode: order.code,
        },
        {},
        microsOrderId.toString(),
      );
    }
    return true;
  }

  async getMenuItems(
    menuItems: MicrosMenuItem[],
    companyId: Types.ObjectId,
    redeemedQuantity: number,
  ): Promise<CapturedItem[]> {
    const capturedItems = await Promise.all(
      menuItems.map(async (microsMenuItem: MicrosMenuItem) => {
        const menuItem =
          await this.menuItemService.findByMicrosMenuItemIdAndCompanyId(
            microsMenuItem.menuItemId,
            companyId,
          );
        if (menuItem) {
          const capturedItem = new CapturedItem();
          capturedItem._id = menuItem._id;
          capturedItem.quantity = microsMenuItem.quantity - redeemedQuantity;
          capturedItem.microsMenuItemId = menuItem.microsMenuItemId;
          return capturedItem;
        }
      }),
    );

    return capturedItems.filter((capturedItem) => capturedItem?.quantity > 0);
  }

  async createCheck(
    order: OrderDocument,
    microsCompanyConfig: MicrosCompanyConfig,
    microsBranchConfig: MicrosBranchConfig,
  ) {
    const URL = `${microsCompanyConfig.checkBaseUrl}/checks`;
    this.logger.log('[Micros] URL ', URL);
    const microsMenuItems = this.mapMenuItems(order.items);
    const createCheckRequestBody = this.constructMicrosCheckRequestBody(
      order,
      microsCompanyConfig,
      microsBranchConfig,
      microsMenuItems,
    );
    this.logger.log(
      '[Micros] Create Check Request Body ',
      createCheckRequestBody,
    );
    const createdCheck = await this.microsCheckHttpService.performPostRequest(
      URL,
      createCheckRequestBody,
      microsCompanyConfig,
      microsBranchConfig,
    );
    this.logger.log('[Micros] created check', JSON.stringify(createdCheck));
    order.microsOrderId = createdCheck['header']['checkRef'];
    order.invoice_number = createdCheck['header']['checkNumber']
      ? createdCheck['header']['checkNumber']
      : createdCheck['header']['checkRef'];
    await order.save();
  }

  async updateCheck(
    order: OrderDocument,
    microsCompanyConfig: MicrosCompanyConfig,
    microsBranchConfig?: MicrosBranchConfig,
  ) {
    const checkRef = order.microsOrderId;

    const URL = `${microsCompanyConfig.checkBaseUrl}/checks/${checkRef}/round`;

    const updateCheckRequestBody = this.constructMicrosCheckRequestBody(
      order,
      microsCompanyConfig,
      microsBranchConfig,
    );

    const updatedCheck = await this.microsCheckHttpService.performPostRequest(
      URL,
      updateCheckRequestBody,
      microsCompanyConfig,
      microsBranchConfig,
    );
    this.logger.log(
      '[Micros] returned updated check',
      JSON.stringify(updatedCheck),
    );
  }

  async syncCheck(
    postMicrosOrderDto: PostMicrosOrderDto,
    companyId: Types.ObjectId,
    brandId: Types.ObjectId,
    currentUser: CurrentUser,
  ): Promise<void> {
    const company = await this.companyService.findById(companyId);
    const branch = await this.branchService.findById(
      new Types.ObjectId(postMicrosOrderDto.branchId),
    );
    const microsOrderId = postMicrosOrderDto.header.checkRef;
    if (await this.checkAndUpdateOrderStatus(microsOrderId)) return;

    const aggregatorOrderToCreate = await this.constructAggregatorOrderToCreate(
      postMicrosOrderDto,
      company,
      branch._id,
      brandId,
      currentUser,
    );

    if (aggregatorOrderToCreate)
      await this.aggregatorOrderService.create(aggregatorOrderToCreate);
  }
  async createChecks(
    checkItems: PostMicrosOrderDto[],
    company: CompanyDocument,
    branch: BranchDocument,
    brand: BrandDocument,
  ) {
    for (const checkItem of checkItems) {
      const microsOrderId = checkItem.header.checkRef;
      if (checkItem.header.status !== MicrosCheckStatus.CLOSED) {
        this.logger.log(`[Micros] Skipping open check ${microsOrderId}`);
        continue;
      }

      if (!checkItem.serviceCharges) {
        this.logger.log(
          `[Micros] Skipping closed check ${microsOrderId} without service charges`,
        );
        continue;
      }

      if (!(await this.checkAndUpdateOrderStatus(microsOrderId))) {
        const orderPosToCreate = await this.constructAggregatorOrderToCreate(
          checkItem,
          company,
          branch._id,
          brand._id,
        );

        this.logger.log(`[Micros] Creating closed check ${microsOrderId}`, {
          check: JSON.stringify(checkItem),
          orderPosToCreate,
        });

        if (orderPosToCreate) {
          const order =
            await this.aggregatorOrderService.create(orderPosToCreate);
          this.logger.log(`[Micros] Captured order ${order.code}`, order);
        }
      }
    }
  }

  private async constructAggregatorOrderToCreate(
    createMicrosOrderDto: PostMicrosOrderDto,
    company: CompanyDocument,
    branchId: Types.ObjectId,
    brandId: Types.ObjectId,
    currentUser?: CurrentUser,
  ): Promise<AggregatorOrderToCreate> {
    const checkRef = createMicrosOrderDto?.header?.checkRef;
    const checkNumber = createMicrosOrderDto?.header?.checkNumber
      ? createMicrosOrderDto?.header?.checkNumber
      : createMicrosOrderDto?.header?.checkRef;
    let customerUniqueIdentifier;
    if (createMicrosOrderDto?.serviceCharges?.length > 0)
      customerUniqueIdentifier = this.getCustomerUniqueIdentifier(
        createMicrosOrderDto?.serviceCharges,
      );

    if (!customerUniqueIdentifier) return;
    let customer;
    try {
      customer = await this.customerReadService.findOne(
        customerUniqueIdentifier,
        company._id,
        company.localization.countryCode,
      );
    } catch (exception) {
      this.logger.error('[Micros] customer not found issue', exception);
    }
    const coupons =
      await this.couponService.findCustomerRedeemableCoupons(customer);
    this.logger.log('coupons', coupons);

    let couponId: Types.ObjectId;
    if (createMicrosOrderDto?.itemDiscounts && coupons?.length > 0)
      couponId = this.getCouponId(createMicrosOrderDto.itemDiscounts, coupons);

    const itemsDiscounts = createMicrosOrderDto.itemDiscounts
      ? createMicrosOrderDto.itemDiscounts
      : this.getItemDiscounts(createMicrosOrderDto?.menuItems);

    let discounts: Discount[];
    if (
      itemsDiscounts &&
      (customer?.rewards || customer?.loyaltyTier || coupons?.length > 0)
    )
      discounts = this.getDiscounts(itemsDiscounts, customer, coupons);

    let items: CapturedItem[];
    if (createMicrosOrderDto?.menuItems) {
      const redeemedQuantity = (discounts || []).filter(
        (discount) => discount.applyTo === DiscountApplyTo.MENU_ITEM,
      ).length;
      items = await this.getMenuItems(
        createMicrosOrderDto.menuItems,
        company._id,
        redeemedQuantity,
      );
    }

    const date = moment().utc().format('YYYY-MM-DD HH:mm');
    this.logger.log('discounts', discounts);
    this.logger.log('couponId', couponId);
    return {
      brandId,
      branchId,
      currentUser,
      source: CapturedOrderSource.POS,
      date: date,
      customer: {
        phoneNumber: customer?.phone,
        countryCode: company?.localization?.countryCode,
        firstName: customer ? customer.first_name : 'Micros Customer',
      },
      amount: Number(createMicrosOrderDto.totals.subtotal),
      companyId: company._id,
      invoiced_amount: Number(createMicrosOrderDto.totals.subtotal),
      delivery_amount: 0,
      total_amount_after_discount: Number(
        createMicrosOrderDto.totals.paymentTotal,
      ),
      total_amount: Number(createMicrosOrderDto.totals.paymentTotal),
      total_discount: Math.abs(
        Number(createMicrosOrderDto.totals.subtotalDiscountTotal),
      ),
      microsOrderId: checkRef,
      invoice_number: checkNumber?.toString(),
      creationSource: OrderCreationSource.MICROS,
      items: items,
      couponId: couponId,
      discounts: discounts,
      isManuallyCaptured: true,
    };
  }

  private getItemDiscounts(menuItems: MicrosMenuItem[]): MicrosItemDiscount[] {
    return menuItems.flatMap((menuItem) => menuItem?.itemDiscounts || []);
  }

  private getDiscounts(
    discounts: MicrosItemDiscount[],
    customer: CustomerDocument,
    coupons: CouponDocument[],
  ): Discount[] {
    const allDiscounts: Discount[] = [];

    if (customer?.rewards) {
      const applicableRewards = customer.rewards.filter((reward) =>
        discounts.some(
          (discount) => discount.discountId === reward.microsDiscountId,
        ),
      );

      for (const reward of applicableRewards) {
        const rewardDiscount =
          reward.benefit === PunchCardBenefit.PERCENT_DISCOUNT
            ? {
                type: DiscountType.PERCENTAGE,
                amount: reward.amount,
                applyTo: DiscountApplyTo.CART,
                source: DiscountSource.REWARD,
                rewardId: reward._id,
              }
            : {
                type: DiscountType.PERCENTAGE,
                amount: 100,
                applyTo: DiscountApplyTo.MENU_ITEM,
                source: DiscountSource.REWARD,
                rewardId: reward._id,
                menuItemId: reward.menuItem.masterMenuItemId,
              };
        allDiscounts.push(rewardDiscount);
      }
    }

    if (customer?.loyaltyTier) {
      const tierDiscount = Discount.fromTierPercentDiscount(
        customer.loyaltyTier,
      );
      allDiscounts.push(tierDiscount);
    }

    if (coupons) {
      const applicableCoupons = coupons.filter((coupon) =>
        discounts.some(
          (discount) => discount.discountId === coupon.microsDiscountId,
        ),
      );

      for (const coupon of applicableCoupons) {
        const couponDiscount = Discount.fromCoupon(coupon);
        allDiscounts.push(couponDiscount);
      }
    }

    return allDiscounts;
  }

  private getCouponId(
    discounts: MicrosItemDiscount[],
    coupons: CouponDocument[],
  ): Types.ObjectId {
    const matchedCoupon = coupons.find((coupon) =>
      discounts.some(
        (discount) => discount.discountId === coupon.microsDiscountId,
      ),
    );
    return matchedCoupon ? matchedCoupon._id : undefined;
  }

  private getCustomerUniqueIdentifier(
    serviceCharges: MicrosServiceCharge[],
  ): string {
    const phoneNumberPatterns: { [key: string]: RegExp } = {
      EG: /^1[0125]\d{8}$/, // Egypt without country code
      QA: /^[356789]\d{7}$/, // Qatar without country code
    };

    const regions = [
      CountriesNameToIsoCodeMapping.Egypt,
      CountriesNameToIsoCodeMapping.Qatar,
    ];

    let uniqueIdentifierServiceCharge = (serviceCharges || [])
      .reverse()
      .find((serviceCharge) =>
        regions.some((region) => {
          const pattern = phoneNumberPatterns[region];
          return pattern.test(serviceCharge.referenceText);
        }),
      );

    if (!uniqueIdentifierServiceCharge)
      uniqueIdentifierServiceCharge = serviceCharges[0];

    if (!uniqueIdentifierServiceCharge) {
      this.logger.error(
        '[Micros] unique identifier service charge not found for check.',
        '',
        { serviceCharges },
      );
      return '';
    }

    return uniqueIdentifierServiceCharge.referenceText;
  }

  private constructMicrosCheckRequestBody(
    order: OrderDocument,
    microsCompanyConfig: MicrosCompanyConfig,
    microsBranchConfig: MicrosBranchConfig,
    menuItems?: MicrosMenuItem[],
  ): PostMicrosOrderDto {
    const updatedServiceCharges = this.updateServiceCharges(
      microsCompanyConfig.serviceCharges,
      order.customer_phone,
    );

    const tenders = this.constructMicrosTenders(
      order.payment_method,
      microsCompanyConfig.tenderMedia,
    );

    return {
      header: this.constructMicrosHeader(
        order,
        microsCompanyConfig,
        microsBranchConfig,
      ),
      notificationOptions: {
        enabled: true,
      },
      tenders,
      menuItems,
      serviceCharges: updatedServiceCharges,
    };
  }

  private updateServiceCharges(
    serviceCharges: MicrosServiceCharge[],
    customerPhone: string,
  ): MicrosServiceCharge[] {
    return serviceCharges.map((serviceCharge) => {
      if (serviceCharge.name === 'customer phone number') {
        return { ...serviceCharge, referenceText: customerPhone };
      }
      return serviceCharge;
    });
  }

  private constructMicrosHeader(
    order: OrderDocument,
    microsCompanyConfig: MicrosCompanyConfig,
    microsBranchConfig: MicrosBranchConfig,
  ): MicrosHeader {
    return {
      checkRef: order?.microsOrderId,
      orgShortName: microsCompanyConfig.organizationShortName,
      locRef: microsBranchConfig.locationReference,
      rvcRef: microsBranchConfig.revenueCenterReference,
      idempotencyId: this.generateIdempotencyId(),
      checkName: `Enable - ${order.code}`,
      checkEmployeeRef: microsCompanyConfig.checkEmployeeRef,
      orderTypeRef: microsCompanyConfig.orderTypeRefs[0].orderTypeRef,
    };
  }

  private constructMicrosTenders(
    paymentMethod: OrderPaymentMethod,
    tenders: MicrosTender[],
  ): MicrosTender[] {
    const tenderRef =
      paymentMethod === OrderPaymentMethod.cash
        ? MicrosEnableTenderRef.CASH
        : MicrosEnableTenderRef.ONLINE;
    return tenders
      .filter((tender) => tender.enableTenderRef === tenderRef)
      .map((tender) => omit(tender, ['enableTenderRef']));
  }

  private generateIdempotencyId(): string {
    return uuidv4().replace(/-/g, '');
  }

  //TODO replace this part by menu sync ( out of this scope , waiting for the request from the product team)
  private mapMenuItems(items: OrderItem[]): MicrosMenuItem[] {
    this.logger.log('items', items);
    return items.map((item: OrderItem) => ({
      menuItemId: item?.microsMenuItemId,
      quantity: item?.quantity,
    }));
  }
}
