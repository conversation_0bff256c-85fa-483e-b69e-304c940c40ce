import {
  GenericExceptionFilter,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Post,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { TookanWebhookPayload } from '../../../delivery/types/tookan-webhook-payload.type';
import { OrderTookanService } from '../../services/order-tookan/order-tookan.service';

@ApiTags('Order Tookan')
@Controller('order/tookan')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class OrderTookanController {
  constructor(private readonly orderTookanService: OrderTookanService) {}

  @Post('/delivery/success')
  @SetMetadata('public', 'true')
  async tookanDeliverySuccess(@Body() data: TookanWebhookPayload) {
    return await this.orderTookanService.onDeliveryTaskSuccess(data);
  }
}
