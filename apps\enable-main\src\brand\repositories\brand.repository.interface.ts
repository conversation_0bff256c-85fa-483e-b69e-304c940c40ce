import { EmbeddedBranchDto, IGenericRepository } from '@app/shared-stuff';
import { Brand, BrandDocument } from '@app/shared-stuff/models/brand.model';
import { Types, UpdateQuery, UpdateWriteOpResult } from 'mongoose';

export interface BrandRepositoryInterface
  extends IGenericRepository<BrandDocument, Brand> {
  findByCompanyId(companyId: Types.ObjectId): Promise<BrandDocument[]>;
  findByBranchId(branchId: Types.ObjectId): Promise<BrandDocument[]>;
  findByIdIn(ids: Types.ObjectId[]): Promise<BrandDocument[]>;
  findByNameAndCompanyId(
    name: string,
    companyId: Types.ObjectId,
  ): Promise<BrandDocument>;
  findBrandsWithLoyalty(companyId: Types.ObjectId): Promise<BrandDocument[]>;
  updateByBrandIds(
    brandIds: string[],
    updateQuery: UpdateQuery<BrandDocument>,
  ): Promise<UpdateWriteOpResult>;
  dropBranchFromBrands(
    branchId: Types.ObjectId,
    existingBrandIds: Types.ObjectId[],
  ): Promise<void>;
  addBranchToBrands(
    embeddedBranchDto: EmbeddedBranchDto,
    existingBrandIds: Types.ObjectId[],
  ): Promise<void>;
  updateBranchOnBrands(embeddedBranchDto: EmbeddedBranchDto): Promise<void>;
  updatePwaBaseUrl(brandId: Types.ObjectId, pwaBaseUrl?: string): Promise<void>;
}
