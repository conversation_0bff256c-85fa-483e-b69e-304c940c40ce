import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsString } from 'class-validator';

export class UpdateOrdableOptionsDto {
  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsInt()
  id: number;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsString()
  option_name: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsString()
  options_ar_name: string;

  @ApiProperty({
    type: Number,
    required: false,
    minimum: 1,
  })
  @IsInt()
  minimum: number;

  @ApiProperty({
    type: Number,
    required: false,
    minimum: 1,
  })
  @IsInt()
  maximum: number;

  @ApiProperty({
    type: Number,
    required: false,
  })
  @IsInt()
  sort_order: number;

  @ApiProperty({
    type: Boolean,
    required: false,
  })
  @IsBoolean()
  is_required: boolean;

  @ApiProperty({
    type: Boolean,
    required: false,
  })
  @IsBoolean()
  multiple: boolean;
}
