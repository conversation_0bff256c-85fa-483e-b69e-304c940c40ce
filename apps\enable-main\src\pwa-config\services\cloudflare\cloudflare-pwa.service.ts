import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { LoggerService } from '@app/shared-stuff';

@Injectable()
export class CloudflarePWAService {
  private readonly loggerService = new LoggerService(CloudflarePWAService.name);

  private readonly apiBase = 'https://api.cloudflare.com/client/v4';
  private readonly routingIp = '*********';
  private readonly zoneId: string;
  private readonly authToken: string;
  private readonly workerName: string;
  private readonly configBaseUrl: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    this.zoneId = this.configService.getOrThrow<string>('CF_PWA_ZONE_ID');
    this.authToken = this.configService.getOrThrow<string>('CF_PWA_API_TOKEN');
    this.workerName =
      this.configService.getOrThrow<string>('CF_PWA_WORKER_NAME');
    this.configBaseUrl =
      this.configService.getOrThrow<string>('CF_PWA_CONFIG_BASE');
  }

  async createPWA(host: string) {
    await this.wrapCloudflareAction('addDNSRecord', async () => {
      await this.addDNSRecord(host);
    });
    await this.wrapCloudflareAction('configureGatewayWorkerRoute', () =>
      this.configureGatewayWorkerRoute(host),
    );
  }

  async deletePWA(host: string) {
    await this.wrapCloudflareAction('deleteDNSRecord', async () => {
      if (!host.includes(this.configBaseUrl)) return;
      await this.deleteDNSRecord(host);
    });
    await this.wrapCloudflareAction('deleteGatewayWorkerRoute', () =>
      this.deleteGatewayWorkerRoute(host),
    );
  }

  private async wrapCloudflareAction(name: string, fn: () => Promise<any>) {
    try {
      await fn();
    } catch (error) {
      this.loggerService.error(`Cloudflare ${name} failed:`, {
        name: error?.name,
        message: error?.message,
        response: error?.response?.data,
        stack: error?.stack,
      });
      throw new InternalServerErrorException(`Cloudflare ${name} failed.`);
    }
  }

  private async addDNSRecord(host: string) {
    const url = `${this.apiBase}/zones/${this.zoneId}/dns_records`;
    await firstValueFrom(
      this.httpService.post(
        url,
        {
          type: 'A',
          name: host,
          content: this.routingIp,
          ttl: 3600,
          proxied: true,
        },
        { headers: this.getHeaders() },
      ),
    );
  }

  private async deleteDNSRecord(host: string) {
    const listUrl = `${this.apiBase}/zones/${this.zoneId}/dns_records?name=${host}`;
    const listRes = await firstValueFrom(
      this.httpService.get(listUrl, { headers: this.getHeaders() }),
    );
    const record = listRes.data.result[0];
    if (record?.id) {
      await firstValueFrom(
        this.httpService.delete(
          `${this.apiBase}/zones/${this.zoneId}/dns_records/${record.id}`,
          { headers: this.getHeaders() },
        ),
      );
    }
  }

  private async configureGatewayWorkerRoute(host: string) {
    const url = `${this.apiBase}/zones/${this.zoneId}/workers/routes`;
    await firstValueFrom(
      this.httpService.post(
        url,
        {
          pattern: `${host}/*`,
          script: this.workerName,
        },
        { headers: this.getHeaders() },
      ),
    );
  }

  private async deleteGatewayWorkerRoute(host: string) {
    const listUrl = `${this.apiBase}/zones/${this.zoneId}/workers/routes`;
    const listRes = await firstValueFrom(
      this.httpService.get(listUrl, { headers: this.getHeaders() }),
    );
    const route = listRes.data.result.find((r) => r.pattern === `${host}/*`);
    if (route?.id) {
      await firstValueFrom(
        this.httpService.delete(
          `${this.apiBase}/zones/${this.zoneId}/workers/routes/${route.id}`,
          { headers: this.getHeaders() },
        ),
      );
    }
  }

  private getHeaders() {
    return {
      Authorization: `Bearer ${this.authToken}`,
      'Content-Type': 'application/json',
    };
  }
}
