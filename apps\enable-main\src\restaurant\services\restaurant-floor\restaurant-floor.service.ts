import {
  FloorSearchMapping,
  FloorSortMapping,
  FloorToCreate,
  FloorToIndex,
  FloorToRemove,
  FloorToUpdate,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import { BranchService } from '../../../branch/services/branch/branch.service';
import { CompanyService } from '../../../company/services/company/company.service';
import { FloorDocument } from './../../models/restaurant-floor.model';

@Injectable()
export class RestaurantFloorService {
  constructor(
    @InjectModel('Floor') private floorModel: Model<FloorDocument>,
    private companyService: CompanyService,
    private branchService: BranchService,
  ) {}

  async index(floorToIndex: FloorToIndex) {
    const aggregation = [];

    if (floorToIndex.branch) {
      aggregation.push({
        $match: {
          branch: { $eq: new Types.ObjectId(floorToIndex.branch) },
        },
      });
    }

    if (floorToIndex.month) {
      aggregation.push({
        $match: { month: floorToIndex.month },
      });
    }

    if (floorToIndex.sort_type) {
      aggregation.push({
        $sort: FloorSortMapping[floorToIndex.sort_type],
      });
    }

    if (floorToIndex.company) {
      aggregation.push({
        $match: { company: new Types.ObjectId(floorToIndex.company) },
      });
    }

    if (floorToIndex.search_key) {
      if (floorToIndex.search_type == 'all') {
        aggregation.push(
          {
            $addFields: {
              matched: {
                $or: [
                  {
                    $regexMatch: {
                      input: '$name',
                      options: 'i',
                      regex: new RegExp(`.*${floorToIndex.search_key}.*`),
                    },
                  },
                  {
                    $regexMatch: {
                      input: '$branchName',
                      options: 'i',
                      regex: new RegExp(`.*${floorToIndex.search_key}.*`),
                    },
                  },
                ],
              },
            },
          },
          { $match: { matched: true } },
        );
      } else {
        aggregation.push(
          {
            $addFields: {
              matched: {
                $regexMatch: FloorSearchMapping(
                  floorToIndex.search_type || 'all',
                  floorToIndex.search_key,
                ),
              },
            },
          },
          { $match: { matched: true } },
        );
      }
    }

    aggregation.push({ $match: { deletedAt: { $eq: null } } });

    if (
      (floorToIndex.offset || floorToIndex.offset == 0) &&
      floorToIndex.limit
    ) {
      aggregation.push({
        $skip: floorToIndex.offset * floorToIndex.limit,
      });
      aggregation.push({
        $limit: floorToIndex.limit,
      });
    }

    const floors = await this.floorModel.aggregate(aggregation);
    return floors;
  }

  async getTotalNumberOfFloors(floorToIndex: FloorToIndex) {
    delete floorToIndex.offset;
    delete floorToIndex.limit;
    return (await this.index(floorToIndex)).length;
  }

  async getDetails(id: string) {
    const floor = await (
      await this.floorModel.findOne({ _id: id })
    ).populate('tables');
    return floor;
  }

  async getDetailsWithoutPopulation(id: string) {
    const floor = await await this.floorModel.findOne({ _id: id });
    return floor;
  }

  async create(tableToCreate: FloorToCreate) {
    const branch = await this.branchService.get_details(tableToCreate.branch);

    const company = await this.companyService.get_details(
      branch.company['_id'],
    );

    tableToCreate['companyName'] = company.name;
    tableToCreate['branchName'] = branch.name;

    tableToCreate['branch'] = new Types.ObjectId(branch._id) as any;
    tableToCreate['company'] = new Types.ObjectId(company._id) as any;

    const floor = new this.floorModel(tableToCreate);

    // floor.createdBy = tableToCreate.currentUser;
    await floor.save();
    return floor;
  }

  async update(tableToUpdate: FloorToUpdate) {
    const floor = await this.floorModel.findOne({ _id: tableToUpdate._id });

    const branch = await this.branchService.get_details(tableToUpdate.branch);
    const company = await this.companyService.get_details(
      branch.company['_id'],
    );

    tableToUpdate['companyName'] = company.name;
    tableToUpdate['branchName'] = branch.name;

    tableToUpdate['branch'] = new Types.ObjectId(branch._id) as any;
    tableToUpdate['company'] = new Types.ObjectId(company._id) as any;

    await this.floorModel.updateOne({ _id: floor._id }, tableToUpdate);

    // floor.updatedBy = tableToUpdate.currentUser;

    return floor;
  }

  async remove(tableToRemove: FloorToRemove) {
    const floor = await this.floorModel.findOne({ _id: tableToRemove._id });
    floor.deletedBy = tableToRemove.deletedBy;
    floor.deletedAt = moment.utc().toDate();
    await floor.save();
    return floor;
  }
}
