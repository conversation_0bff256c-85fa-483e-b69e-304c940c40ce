import { CustomerEarnedBenefit } from '@app/shared-stuff';

import { Injectable } from '@nestjs/common';
import { BenefitConcreteInterface } from './benefit-concrete.interface';

@Injectable()
export class PercentageDiscountConcreteService
  implements BenefitConcreteInterface
{
  constructor() {}
  redeem(benefit: CustomerEarnedBenefit, amount: number): [number, number] {
    const discountValue =
      benefit.value > 1 ? benefit.value / 100 : benefit.value;
    return [amount - amount * discountValue, amount * discountValue];
  }
}
