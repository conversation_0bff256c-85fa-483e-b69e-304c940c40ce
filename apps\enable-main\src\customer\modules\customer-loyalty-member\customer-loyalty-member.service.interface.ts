import {
  BulkActionResponseDto,
  BulkLoyaltyRegistrationDto,
  CustomerDocument,
  CustomerWithToken,
  RegisterLoyaltyCustomerDto,
  RegistrationContextDto,
} from '@app/shared-stuff';

export interface CustomerLoyaltyMemberServiceInterface {
  enrollLoyaltyCustomer(customer: CustomerDocument): Promise<void>;

  registerLoyaltyCustomer(
    customer: CustomerDocument,
    registrationContext: RegistrationContextDto,
    fireNotification?: boolean,
    skipVerification?: boolean,
  ): Promise<CustomerDocument>;

  bulkLoyaltyRegistration(
    bulkLoyaltyRegistrationDto: BulkLoyaltyRegistrationDto,
  ): Promise<BulkActionResponseDto>;

  registerCustomerInLoyaltyProgram(
    registerLoyaltyCustomerDto: RegisterLoyaltyCustomerDto,
  ): Promise<CustomerWithToken>;
}

export const CustomerLoyaltyMemberServiceInterface = Symbol(
  'CustomerLoyaltyMemberServiceInterface',
);
