import {
  Achievement,
  AchievementUrlParams,
  CreatePunchCardAchievementDto,
  NamedAchievement,
  PunchCardBenefit,
  PunchCardDocument,
  Reward,
  RewardMenuItem,
  RewardToCreate,
  UpdatePunchCardAchievementDto,
} from '@app/shared-stuff';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { MenuItemService } from '../../../restaurant/services/menu-item/menu-item.service';
import { PunchCardReadService } from '../punch-card-read/punch-card-read.service';
import { PunchCardRepository } from '../punch-card-repository/punch-card.repository';

@Injectable()
export class PunchCardAchievementService {
  constructor(
    private readonly punchCardReadService: PunchCardReadService,
    private readonly punchCardRepository: PunchCardRepository,
    private readonly eventEmitter: EventEmitter2,
    private readonly menuItemService: MenuItemService,
  ) {}

  public async create(
    punchCardId: Types.ObjectId,
    { reward, requirementTargetValue }: CreatePunchCardAchievementDto,
  ): Promise<PunchCardDocument> {
    const punchCard = await this.punchCardReadService.findById(punchCardId);
    const newAchievement: Achievement = {
      _id: new Types.ObjectId(),
      reward: await this.createReward(reward),
      requirement: {
        targetValue: requirementTargetValue,
        type: punchCard.counter.type,
        menuItem: punchCard.counter.menuItem,
      },
      updatedAt: moment.utc().toDate(),
      createdAt: moment.utc().toDate(),
    };
    punchCard.achievements = this.insertAchievementWhileMaintainingSort(
      punchCard.achievements,
      newAchievement,
    );
    await punchCard.save();
    this.eventEmitter.emit(
      'punchcard.achievement.created',
      punchCard,
      newAchievement,
    );
    return punchCard;
  }

  async createReward(reward: RewardToCreate | null): Promise<Reward> {
    if (!reward || !reward.benefit) return reward;
    if (reward.benefit === PunchCardBenefit.PERCENT_DISCOUNT) return reward;
    const { benefit, brandId, menuId, masterMenuItemId, ...rest } = reward;

    const menuItem = await this.createRewardMenuItem(
      brandId,
      menuId,
      masterMenuItemId,
    );

    return { benefit, menuItem, ...rest };
  }

  async createRewardMenuItem(
    brandId: Types.ObjectId,
    menuId: Types.ObjectId,
    masterMenuItemId: Types.ObjectId,
  ): Promise<RewardMenuItem> {
    if (!masterMenuItemId) return null;

    const menuItem = await this.menuItemService.getDetails(
      masterMenuItemId.toString(),
    );

    if (!menuItem)
      throw new BadRequestException(
        `Menu item with ID ${masterMenuItemId} not found.`,
      );

    return {
      brandId,
      menuId,
      masterMenuItemId,
      nameEn: menuItem.nameEn,
      nameAr: menuItem.nameAr,
      images: menuItem.images,
      externalImage: menuItem.externalImage,
      eligibleMenuItemIds: (menuItem.similarMenuItems || []).concat(
        menuItem._id,
      ),
      integrationInfo: menuItem.integrationInfo,
    };
  }

  private insertAchievementWhileMaintainingSort(
    achievements: Achievement[],
    newAchievement: Achievement,
  ): Achievement[] {
    // TODO: optimize (can be done in one pass)
    const nextHighestIndex = achievements.findIndex(
      ({ requirement }: Achievement) =>
        requirement?.targetValue > newAchievement?.requirement?.targetValue,
    );
    if (nextHighestIndex === -1) {
      return [...achievements, newAchievement];
    }
    return [
      ...achievements.slice(0, nextHighestIndex),
      newAchievement,
      ...achievements.slice(nextHighestIndex),
    ];
  }

  public async update(
    { punchCardId, achievementId }: AchievementUrlParams,
    { requirementTargetValue, reward = {} }: UpdatePunchCardAchievementDto,
  ): Promise<PunchCardDocument> {
    const punchCard = await this.punchCardReadService.findById(punchCardId);
    const achievement = this.findAchievement(punchCard, achievementId);
    const oldAchievementCopy = { ...achievement };

    if (requirementTargetValue) {
      achievement.requirement.targetValue = requirementTargetValue;
      punchCard.achievements = punchCard.achievements.sort(
        (achievementA: Achievement, achievementB: Achievement) =>
          achievementA?.requirement?.targetValue -
          achievementB?.requirement?.targetValue,
      );
    }

    const rewardUpdates = reward.masterMenuItemId
      ? await this.createReward({
          ...achievement.reward,
          ...reward,
        })
      : reward;

    achievement.reward = {
      ...achievement.reward,
      ...rewardUpdates,
    };
    achievement.updatedAt = moment.utc().toDate();
    punchCard.markModified('achievements');
    await punchCard.save();
    this.eventEmitter.emit(
      'punchcard.achievement.updated',
      punchCard,
      achievement,
      oldAchievementCopy,
    );
    return punchCard;
  }

  public async delete(
    punchCardId: Types.ObjectId,
    achievementId: Types.ObjectId,
  ): Promise<PunchCardDocument> {
    const punchCard = await this.punchCardReadService.findById(punchCardId);
    const achievement = this.findAchievement(punchCard, achievementId);
    const updatedPunchCard = await this.punchCardRepository.removeAchievement(
      punchCardId,
      achievement,
    );

    this.eventEmitter.emit(
      'punchcard.achievement.deleted',
      punchCard,
      achievement,
    );

    return updatedPunchCard;
  }

  private findAchievement(
    punchCard: PunchCardDocument,
    achievementId: Types.ObjectId,
  ): Achievement {
    const achievement = punchCard.achievements.find(({ _id }: Achievement) =>
      _id.equals(achievementId),
    );
    if (!achievement)
      throw new NotFoundException(
        `Achievement with ID ${achievementId} not found in punch card ${punchCard._id}.`,
      );
    return achievement;
  }

  async findEarliestAchievements(
    companyId: Types.ObjectId,
  ): Promise<(NamedAchievement | null)[]> {
    const punchCards = await this.punchCardReadService.index({ companyId });
    if (!punchCards || punchCards.length === 0) return [null, null];

    return punchCards.map((punchCard) =>
      punchCard.achievements
        .map(
          (achievement: Achievement): NamedAchievement => ({
            ...achievement,
            punchCardNameEn: punchCard.nameEn,
            punchCardNameAr: punchCard.nameAr,
          }),
        )
        .reduce(
          (previous, current) =>
            previous &&
            previous.requirement.targetValue < current.requirement.targetValue
              ? previous
              : current,
          null,
        ),
    );
  }

  async findAllAchievements(companyId: Types.ObjectId): Promise<Achievement[]> {
    const punchCards = await this.punchCardReadService.index({ companyId });
    if (!punchCards || punchCards.length === 0) return [];

    return punchCards.flatMap((punchCard) => punchCard.achievements);
  }

  formatAchievementReward(reward: Reward, isArabicPreferred = false): string {
    if (!reward) return '';

    if (reward.benefit === PunchCardBenefit.PERCENT_DISCOUNT)
      return reward.amount + '%';

    if (isArabicPreferred && reward.menuItem.nameAr)
      return reward.menuItem.nameAr;
    return reward.menuItem.nameEn;
  }
}
