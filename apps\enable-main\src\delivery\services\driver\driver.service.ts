import {
  CollectionName,
  DriverDocument,
  ImageToCreate,
  responseCode,
  TransportType,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { BadRequestException } from '@nestjs/common/exceptions';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import * as xl from 'excel4node';
import * as moment from 'moment-timezone';
import {
  FilterQuery,
  isObjectIdOrHexString,
  isValidObjectId,
  Model,
  Types,
} from 'mongoose';
import * as xlsx from 'xlsx';
import { CompanyService } from '../../../company/services/company/company.service';
import { TookanService } from '../../../delivery/services/tookan/tookan.service';
import { GoogleCloudStorageService } from '../../../storage/google-cloud-storage.service';
import {
  EXCEL_HEADERS_LOWER,
  EXCEL_HEADERS_MAPPING,
  excelRow,
  mappedDriverToCreate,
} from '../../constants/delivery-constants';
import { ImageService } from './../../../shared/services/image/image.service';
import {
  DriverBranchToAssign,
  DriverBranchToRemove,
  DriverExcelToImport,
  DriverToCreate,
  DriverToIndex,
  DriverToUpdate,
  DriverUsernameToGet,
} from './../../dto/driver.dto';
import {
  TookanDriverToCreate,
  TookanDriverToUpdate,
} from './../../dto/tookan.dto';

@Injectable()
export class DriverService {
  constructor(
    @InjectModel('Driver') private driverModel: Model<DriverDocument>,
    private tookanService: TookanService,
    private configService: ConfigService,
    private imageService: ImageService,
    private companyService: CompanyService,
    private googleCloudStorageService: GoogleCloudStorageService,
  ) {}

  async index(driverToIndex: DriverToIndex) {
    let query = this.driverModel.find(this.buildIndexFilter(driverToIndex));

    if (driverToIndex.sort_type) {
      const sortMapping = {
        name_a_z: { first_name: 1 },
        date_created: { createdAt: -1 },
      };
      if (sortMapping[driverToIndex.sort_type]) {
        query = query.sort(sortMapping[driverToIndex.sort_type]);
      } else {
        query = query.sort({ createdAt: -1 });
      }
    }

    if (
      (driverToIndex.offset || driverToIndex.offset == 0) &&
      driverToIndex.limit
    ) {
      query = query
        .skip(driverToIndex.offset * driverToIndex.limit)
        .limit(driverToIndex.limit);
    }

    return query
      .populate({
        path: 'branches',
        model: CollectionName.BRANCH,
        select: '_id name acronym',
      })
      .exec();
  }

  private buildIndexFilter(driverToIndex: DriverToIndex) {
    const filter: FilterQuery<DriverDocument> = { deletedAt: null };

    if (driverToIndex.search_key) {
      if (driverToIndex.search_type == 'name') {
        filter.$or = [
          {
            first_name: {
              $regex: `.*${driverToIndex.search_key.toLowerCase()}.*$`,
            },
          },
          {
            last_name: {
              $regex: `.*${driverToIndex.search_key.toLowerCase()}.*$`,
            },
          },
        ];
      } else if (driverToIndex.search_type == 'phone') {
        filter.phone = {
          $regex: `.*${driverToIndex.search_key.toLowerCase()}.*$`,
        };
      } else {
        filter.$or = [
          {
            first_name: {
              $regex: `.*${driverToIndex.search_key.toLowerCase()}.*`,
            },
          },
          {
            last_name: {
              $regex: `.*${driverToIndex.search_key.toLowerCase()}.*`,
            },
          },
          {
            phone: { $regex: `.*${driverToIndex.search_key.toLowerCase()}.*$` },
          },
        ];
      }
    }

    if (driverToIndex.month) filter.month = driverToIndex.month;

    if (driverToIndex.company)
      filter.company = new Types.ObjectId(driverToIndex.company);

    if (driverToIndex.branch)
      filter.branches = new Types.ObjectId(driverToIndex.branch);

    return filter;
  }

  async get_total_drivers(driverToIndex: DriverToIndex) {
    return this.driverModel.countDocuments(
      this.buildIndexFilter(driverToIndex),
    );
  }

  async get_details(id: string) {
    if (!id) return null;

    const filter: FilterQuery<DriverDocument> = {
      $or: [{ tookan_driver_id: id }],
    };

    if (isObjectIdOrHexString(id))
      filter.$or.push({ _id: new Types.ObjectId(id) });

    const selectedDriver = await this.driverModel
      .findOne(filter)
      .populate({ path: 'branches', model: CollectionName.BRANCH });
    return selectedDriver;
  }

  async create(driverToCreate: DriverToCreate, user: any) {
    driverToCreate['transport_type'] =
      TransportType[driverToCreate.transport_type_name];
    const company = await this.companyService.get_details(
      driverToCreate.company,
    );
    if (company.has_delivery_system) {
      const tookanDriverTocreate: TookanDriverToCreate = {
        api_key: this.configService.get<string>('TOOKAN_API_KEY'),
        color: driverToCreate.color,
        email: driverToCreate.email,
        first_name: driverToCreate.first_name,
        last_name: driverToCreate.last_name,
        fleet_type: '1',
        license: driverToCreate.license,
        password: driverToCreate.password ? driverToCreate.password : '',
        phone: driverToCreate.phone,
        team_id: company.tookan_team_id,
        timezone: '+00',
        rule_id: '',
        transport_desc: 'Transport Description',
        transport_type: TransportType[driverToCreate.transport_type_name],
        username: driverToCreate.username,
        tags: `${company._id},${driverToCreate.branches[0]}`,
      };
      const TookanCreatedDriver =
        await this.tookanService.createDriver(tookanDriverTocreate);
      // Logger.log(TookanCreatedDriver);
      driverToCreate['tookan_driver_id'] = TookanCreatedDriver['fleet_id'];
      driverToCreate['createdBy'] = user;
      if (
        driverToCreate.image_to_create &&
        driverToCreate.image_to_create['base64']
      ) {
        driverToCreate['image_to_create']['for'] = 'driver';
        driverToCreate['image'] = await this.imageService.uploadImage(
          driverToCreate.image_to_create,
        );
      }
      driverToCreate.team_id = company.tookan_team_id;
    }
    const driverBranches = new Set([]);
    driverToCreate.branches.map((x) => {
      driverBranches.add(new Types.ObjectId(x));
    });
    driverToCreate['branches'] = Array.from(driverBranches) as any;
    const createdDriver = new this.driverModel(driverToCreate);
    await createdDriver.save();
    return createdDriver;
  }

  async remove(id: string, user: any) {
    const filterOB = { $or: [{ tookan_driver_id: id }] } as any;
    if (isValidObjectId(id)) {
      filterOB.$or.push({ _id: id });
    }

    const selectedDriver = await this.driverModel.findOne(filterOB);
    selectedDriver.deletedAt = moment().utc().toDate();
    selectedDriver.deletedBy = user;
    await selectedDriver.save();
  }

  async update(driverToUpdate: DriverToUpdate, user: any) {
    const driver = await this.driverModel.findOneAndUpdate(
      { _id: driverToUpdate._id },
      driverToUpdate,
      { new: true },
    );

    if (!driver)
      throw new BadRequestException(
        'Driver not found',
        responseCode.ENTITY_NOT_FOUND.toString(),
      );

    if (driverToUpdate.image_to_update)
      driver['image'] = await this.updateDriverImage(driverToUpdate);

    if (driver.team_id) await this.updateTookanDriver(driverToUpdate, driver);

    driver.updatedBy = user;
    await driver.save();

    return driver;
  }

  private async updateDriverImage(driverToUpdate: DriverToUpdate) {
    const imageToCreate: ImageToCreate = {
      name: driverToUpdate.first_name,
      alt: driverToUpdate.image_to_update.alt,
      description: driverToUpdate.image_to_update.description,
      for: 'driver',
      base64: driverToUpdate.image_to_update.base64,
    };

    return this.imageService.uploadImage(imageToCreate);
  }

  private async updateTookanDriver(
    driverToUpdate: DriverToUpdate,
    driver: any,
  ) {
    const tookanDriverToUpdate: TookanDriverToUpdate = {
      api_key: this.configService.get<string>('TOOKAN_API_KEY'),
      fleet_id: driver.tookan_driver_id,
      color: driverToUpdate.color,
      email: driverToUpdate.email,
      first_name: driverToUpdate.first_name,
      last_name: driverToUpdate.last_name,
      fleet_type: '1',
      license: driverToUpdate.license,
      password: driverToUpdate.password || '',
      phone: driverToUpdate.phone,
      team_id: driver.team_id,
      timezone: '+00',
      rule_id: '',
      transport_desc: 'Transport Description',
      transport_type: TransportType[driverToUpdate.transport_type_name],
      tags: `${driver.company}`,
    };

    await this.tookanService.updateDriver(tookanDriverToUpdate);
  }

  async assign_driver_to_branch(driverBranchToAssign: DriverBranchToAssign) {
    const driver = await this.driverModel.findOne({
      _id: driverBranchToAssign.driver_id,
    });

    const driverBranches = new Set(driver.branches);
    driverBranches.add(new Types.ObjectId(driverBranchToAssign.branch_id));

    driver.branches = Array.from(driverBranches) as any;

    await driver.save();
    return driver;
  }

  async remove_driver_from_branch(driverBranchToRemove: DriverBranchToRemove) {
    const driver = await this.driverModel.findOne({
      _id: driverBranchToRemove.driver_id,
    });

    const branch_index = driver.branches.indexOf(
      new Types.ObjectId(driverBranchToRemove.branch_id),
    );
    if (branch_index != -1) {
      driver.branches.splice(branch_index, 1);
    }
    await driver.save();
    return driver;
  }

  async export_driver_excel(company_id) {
    const wb = new xl.Workbook();
    const ws = wb.addWorksheet('Api Key');
    const drivers = await this.driverModel.find({ company: company_id }).exec();
    const company = await this.companyService.get_details(company_id);
    const headers = [
      'Driver Id',
      'Tookan Driver Id',
      'First Name',
      'Last Name',
      'UserName',
      'Phone',
      'Email',
      'License',
      'Transport Color',
      'Transport Type',
      'Tookan Team Id',
      'Created At',
    ];
    const style = wb.createStyle({
      font: { color: '#FF0800', size: 12 },
      numberFormat: '$#,##0.00; ($#,##0.00); -',
    });
    for (let i = 1; i <= headers.length; i++) {
      ws.cell(1, i)
        .string(headers[i - 1])
        .style(style);
    }
    for (let i = 0; i < drivers.length; i++) {
      ws.cell(i + 2, 1).string(drivers[i]._id.toString());
      ws.cell(i + 2, 2).string(drivers[i].tookan_driver_id?.toString());
      ws.cell(i + 2, 3).string(drivers[i].first_name?.toString());
      ws.cell(i + 2, 4).string(drivers[i].last_name?.toString());
      ws.cell(i + 2, 5).string(drivers[i].username?.toString());
      ws.cell(i + 2, 6).string(drivers[i].phone?.toString());
      ws.cell(i + 2, 7).string(drivers[i].email?.toString());
      ws.cell(i + 2, 8).string(drivers[i].license?.toString());
      ws.cell(i + 2, 9).string(drivers[i].color?.toString());
      ws.cell(i + 2, 10).string(drivers[i].transport_type_name?.toString());
      ws.cell(i + 2, 11).string(drivers[i].team_id?.toString());
      ws.cell(i + 2, 12).string(drivers[i]['createdAt']?.toString());
    }
    const fileName =
      company['acronym'] +
      '-Drivers-' +
      moment().format('DD-MM-YYYY HH-mm-ss').toString() +
      '.xlsx';

    await this.googleCloudStorageService.uploadDocument(
      await wb.writeToBuffer(),
      fileName,
    );

    return `/documents/${fileName}`;
  }

  async import_driver_excel(
    driverExcelToImport: DriverExcelToImport,
    currentUser,
  ) {
    const parseSheet = async (sheet: xlsx.WorkSheet) => {
      const driverData: excelRow[] = xlsx.utils.sheet_to_json(sheet);

      this.validateExcelColumnHeaders(driverData);

      const addMissingFields = (
        driver: mappedDriverToCreate,
      ): DriverToCreate => ({
        company: driverExcelToImport.company,
        fleet_type: 1,
        image_to_create: null,
        branches: [],
        password: '',
        ...driver,
      });

      const drivers = driverData
        .map(this.excelRowToDriver)
        .map(addMissingFields);

      const newDrivers: DriverToCreate[] =
        await this.removeExistingDrivers(drivers);

      await Promise.all(
        newDrivers.map((driver) => this.create(driver, currentUser)),
      );
    };

    const workbook = xlsx.readFile(driverExcelToImport.file_path);
    const sheets = Object.values(workbook.Sheets);
    await Promise.all(sheets.map(parseSheet));
  }

  async getNextDriverUsername(driverUsernameToGet: DriverUsernameToGet) {
    const company = await this.companyService.get_details(
      driverUsernameToGet.company,
    );
    // company.number_of_drivers = company.number_of_drivers + 1;
    // await company.save();

    let driver_name =
      driverUsernameToGet.first_name.toLowerCase() +
      driverUsernameToGet.last_name.toLowerCase();
    const drivers = await this.driverModel.count({ username: driver_name });
    if (drivers != 0) {
      driver_name += (drivers + 1).toString();
    }
    return driver_name;
  }

  private validateExcelColumnHeaders(driverData: excelRow[]): boolean {
    if (!driverData || driverData.length === 0) {
      return true;
    }

    const headers = Object.keys(driverData[0]);

    const invalidHeaders = headers.filter(
      (header) => !EXCEL_HEADERS_LOWER.includes(header.toLowerCase()),
    );

    if (invalidHeaders.length > 0) {
      throw new BadRequestException(
        `Invalid column header: "${invalidHeaders.join('", ')}".`,
      );
    }

    return true;
  }

  private excelRowToDriver(driverRow: excelRow): mappedDriverToCreate {
    return Object.entries(driverRow).reduce((driver, [key, value]) => {
      if (value === '') return driver;

      const targetKey = EXCEL_HEADERS_MAPPING[key.toLowerCase()];
      if (targetKey !== null) driver[targetKey] = value;

      return driver;
    }, {} as Partial<DriverToCreate>) as Pick<
      DriverToCreate,
      (typeof EXCEL_HEADERS_MAPPING)[keyof typeof EXCEL_HEADERS_MAPPING]
    >;
  }

  private async removeExistingDrivers(
    drivers: DriverToCreate[],
  ): Promise<DriverToCreate[]> {
    const newDrivers = [];
    for (const driver of drivers) {
      const existingDriver = await this.driverModel.exists({
        company: driver.company,
        $or: [{ phone: driver.phone }, { email: driver.email }],
        deletedAt: null,
      });

      if (existingDriver === null) {
        newDrivers.push(driver);
      }
    }
    return newDrivers;
  }
}
