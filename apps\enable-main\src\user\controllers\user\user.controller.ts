import {
  GenericExceptionFilter,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  SetMetadata,
  UseFilters,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { <PERSON>pi<PERSON><PERSON><PERSON><PERSON>uth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { Types } from 'mongoose';
import {
  UserAndRoleToAssign,
  UserDetailsToUpdate,
  UserIndex,
  UserStatusToUpdate,
  UserToIndex,
} from '../../dto/user.dto';
import { UserService } from '../../services/user/user.service';
import { UserToCreate, UserToValidate } from './../../dto/user.dto';

@ApiTags('User')
@Controller('user')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@SetMetadata('module', 'user')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
export class UserController {
  constructor(private userService: UserService) {}

  @Get()
  @SetMetadata('action', 'get_all')
  async index(@Query() userIndex: UserIndex, @Req() req: Request) {
    if (userIndex.isEnableUser == false)
      userIndex.company = req['company_id']
        ? req['company_id']
        : new Types.ObjectId(userIndex.company);
    return await this.userService.index(userIndex);
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  async get_details(@Param('id') id: string) {
    return await this.userService.get_details(id);
  }

  @Delete(':id')
  @SetMetadata('action', 'remove')
  async remove(@Param('id') id: string, @Req() req: Request) {
    return await this.userService.remove(id, req['current']);
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(@Body() userToCreate: UserToCreate, @Req() req: Request) {
    if (userToCreate.isEnableUser == false)
      userToCreate.company = req['company_id']
        ? (new Types.ObjectId(req['company_id']) as any)
        : new Types.ObjectId(userToCreate.company);
    return await this.userService.create(userToCreate, req['current']);
  }

  @Get('admin/all')
  @SetMetadata('action', 'get_all')
  async admin_index(@Query() userToIndex: UserToIndex, @Req() req: Request) {
    userToIndex.company = req['company_id']
      ? (new Types.ObjectId(req['company_id']) as any)
      : userToIndex.company
        ? new Types.ObjectId(userToIndex.company)
        : undefined;

    const users = await this.userService.admin_index(userToIndex);
    const totalUsers = await this.userService.get_total_users(userToIndex);
    return { users: users, totalUsers: totalUsers };
  }

  @Put('status')
  @SetMetadata('action', 'change_status')
  async change_status(
    @Body() userStatusToUpdate: UserStatusToUpdate,
    @Req() req: Request,
  ) {
    return await this.userService.change_status(
      userStatusToUpdate.id,
      userStatusToUpdate.status,
      req['current'],
    );
  }

  @Put(':id')
  @SetMetadata('action', 'update_details')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async update_details(
    @Body() userDetailsToUpdate: UserDetailsToUpdate,
    @Req() req: Request,
  ) {
    return await this.userService.update_user_details(
      userDetailsToUpdate,
      req['current'],
    );
  }

  @Post('user/role/add')
  @SetMetadata('action', 'assign_role_to_user')
  async assign_role_to_user(
    @Body() roleToAssign: UserAndRoleToAssign,
    @Req() req: Request,
  ) {
    return await this.userService.add_role_to_user(
      roleToAssign.user_id,
      roleToAssign.role_id,
      req['current'],
    );
  }

  @Post('validate')
  @SetMetadata('action', 'ValidateUserEmailAndPhone')
  async validateUser(@Body() userToValidate: UserToValidate) {
    return await this.userService.validateUserData(userToValidate);
  }
}
