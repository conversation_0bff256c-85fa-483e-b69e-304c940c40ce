import {
  GenericExceptionFilter,
  LocationItemType,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import {
  Api<PERSON><PERSON><PERSON>Auth,
  ApiBearerAuth,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';

import { Types } from 'mongoose';
import {
  AreaToCreate,
  AreaToIndex,
  AreaToUpdate,
} from '../../../location/dto/area.dto';
import { CurrentUserService } from '../../../shared/services/current-user/current-user.service';
import { AreaService } from '../../services/area/area.service';
import { LocationItemServiceInterface } from '../../services/location-item/location-item-service.interface';

@Controller('area')
@ApiTags('Area')
@SetMetadata('module', 'area')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
export class AreaController {
  constructor(
    @Inject(LocationItemServiceInterface)
    private readonly locationItemService: LocationItemServiceInterface,
    private readonly areaService: AreaService,
    private readonly currentUserService: CurrentUserService,
  ) {}

  @ApiOperation({
    deprecated: true,
    description:
      '**Deprecated**, use `GET /location-item` with type `area` instead.',
  })
  @Get()
  @SetMetadata('action', 'get_all')
  async index(@Query() areaToIndex: AreaToIndex) {
    return this.areaService.index({
      ...areaToIndex,
      country:
        this.currentUserService.getCurrentCompany()?.localization?.country ??
        areaToIndex.country,
    });
  }

  @ApiOperation({
    deprecated: true,
    description:
      '**Deprecated**, use `GET /location-item/public` with type `area` instead.',
  })
  @Get('public')
  @SetMetadata('public', 'true')
  async publicIndex(@Query() areaToIndex: AreaToIndex) {
    const result = await this.locationItemService.findAll({
      type: LocationItemType.AREA,
      ...(areaToIndex.country && {
        ancestorsSearch: true,
        parentId: new Types.ObjectId(areaToIndex.country),
      }),
      ...(areaToIndex.city && {
        parentId: new Types.ObjectId(areaToIndex.city),
      }),
    });
    return result[0].paginatedResult;
  }

  @ApiOperation({
    deprecated: true,
    description:
      '**Deprecated**, use `POST /location-item` with type `area` instead.',
  })
  @Post()
  @SetMetadata('action', 'create')
  async create(@Body() areaToCreate: AreaToCreate) {
    return await this.locationItemService.create({
      type: LocationItemType.AREA,
      ...areaToCreate,
      parentId: new Types.ObjectId(areaToCreate.city),
    });
  }

  @ApiOperation({
    deprecated: true,
    description:
      '**Deprecated**, use `PATCH /location-item` with type `area` instead.',
  })
  @Put(':id')
  @SetMetadata('action', 'update')
  async update(@Body() areaToUpdate: AreaToUpdate) {
    return await this.locationItemService.update({
      type: LocationItemType.CITY,
      ...areaToUpdate,
      _id: new Types.ObjectId(areaToUpdate._id),
    });
  }

  @ApiOperation({
    deprecated: true,
    description: '**Deprecated**, use `DELETE /location-item`',
  })
  @Delete(':id')
  @SetMetadata('action', 'remove')
  async remove(@Param('id') id: string) {
    return await this.locationItemService.delete(new Types.ObjectId(id));
  }
}
