import { Types } from 'mongoose';
import { Request, Response } from 'express';
import {
  responseCode,
  SavedAddressToSetDefault,
  SavedLocationToCreate,
  SavedLocationToIndex,
  SavedLocationToUpdate,
} from '@app/shared-stuff';
import { SavedLocationService } from './../../services/saved-location/saved-location.service';
import { HelperService } from './../../../shared/services/helper/helper.service';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  SetMetadata,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';

@Controller('saved-location')
@SetMetadata('module', 'saved-location')
@ApiTags('Saved Location Controller')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class SavedLocationController {
  constructor(
    private helperService: HelperService,
    private savedLocationService: SavedLocationService,
  ) {}

  @Get()
  @SetMetadata('action', 'getAll')
  async index(
    @Query() savedLocationToIndex: SavedLocationToIndex,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      savedLocationToIndex.companyId = req['company_id']
        ? req['company_id']
        : savedLocationToIndex.companyId;
      const savedLocations =
        await this.savedLocationService.index(savedLocationToIndex);

      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to get all saved address',
        {
          savedLocations: savedLocations[0]['paginatedResult'],
          totalCount: savedLocations[0]['totalCount'][0]
            ? savedLocations[0]['totalCount'][0]['createdAt']
            : 0,
        },
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() savedLocationToCreate: SavedLocationToCreate,
    @Res() res: Response,
  ) {
    try {
      const createdCountry = await this.savedLocationService.create(
        savedLocationToCreate,
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success to create SavedAddress',
        createdCountry,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('national-address/:id')
  @SetMetadata('action', 'fetchNationalAddressAndUpdate')
  async fetchNationalAddressAndUpdate(
    @Param('id') locationId: string,
    @Res() res: Response,
  ) {
    try {
      const location =
        await this.savedLocationService.fetchNationalAddressAndUpdate(
          new Types.ObjectId(locationId),
        );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success to Update National Address',
        location,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Put(':id')
  @SetMetadata('action', 'update')
  async update(
    @Body() savedLocationToUpdate: SavedLocationToUpdate,
    @Res() res: Response,
    @Req() req: Request,
    @Param('id') id: string,
  ) {
    try {
      savedLocationToUpdate._id = new Types.ObjectId(id);
      const savedLocation = await this.savedLocationService.update(
        savedLocationToUpdate,
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'success to update SavedLocation',
        savedLocation,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get(':id')
  @SetMetadata('action', 'getDetails')
  async getDetails(
    @Param('id') id: string,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const savedLocation = await this.savedLocationService.getDetails(id);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'success to update new savedLocation',
        savedLocation,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Delete(':id')
  @SetMetadata('action', 'remove')
  async remove(
    @Param('id') id: string,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const savedLocation = await this.savedLocationService.remove(id);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_REMOVE,
        'success to remove savedLocation',
        savedLocation,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('default')
  @SetMetadata('action', 'setDefaultSavedLocation')
  async setCustomerDefaultSavedLocation(
    @Body() savedAddressToSetDefault: SavedAddressToSetDefault,
    @Res() res: Response,
  ) {
    try {
      const response =
        await this.savedLocationService.setDefaultCustomerSavedLocation(
          savedAddressToSetDefault,
        );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'Success to Set The Address As Default',
        response,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
}
