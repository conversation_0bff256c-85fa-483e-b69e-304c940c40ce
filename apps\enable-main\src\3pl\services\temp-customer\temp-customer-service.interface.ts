import {
  AcquireLocationTriggerDto,
  CreateTempCustomerDto,
  GetAllTempCustomerDto,
  IndexResultDto,
  TempCustomer,
  TempCustomerDocument,
  UpdateTempCustomerDto,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface TempCustomerServiceInterface {
  findAll(
    getAllTempCustomerDto: GetAllTempCustomerDto,
  ): Promise<IndexResultDto<TempCustomer>[]>;

  create(
    createTempCustomerDto: CreateTempCustomerDto,
  ): Promise<TempCustomerDocument>;

  update(
    updateTempCustomerDto: UpdateTempCustomerDto,
  ): Promise<TempCustomerDocument>;

  updateOne(
    salesOrderId: string,
    updates: Partial<UpdateTempCustomerDto>,
  ): Promise<TempCustomerDocument>;

  findOne(id: Types.ObjectId): Promise<TempCustomerDocument>;

  findBySalesOrderId(salesOrderId: string): Promise<TempCustomerDocument>;

  fireAcquireLocationTrigger(
    acquireLocationTriggerDto: AcquireLocationTriggerDto,
  ): Promise<void>;
}

export const TempCustomerServiceInterface = Symbol(
  'TempCustomerServiceInterface',
);
