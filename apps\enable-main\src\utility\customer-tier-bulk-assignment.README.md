# Customer Tier Bulk Assignment Service

This service provides functionality to bulk assign loyalty tiers to customers based on data from an Excel file.

## Overview

The `CustomerTierBulkAssignmentService` reads customer data from an Excel file and assigns them to appropriate loyalty tiers based on the tier names specified in the file.

## Features

- Reads Excel files containing customer phone numbers and tier names
- Fetches customers from the database using phone numbers and company ID
- Groups customers by their target loyalty tier
- Performs bulk tier assignments using the existing `CustomerTierService`
- Returns detailed results including success, failed, and unchanged counts

## Excel File Format

The Excel file should contain the following columns:

| Column Name       | Description                                         | Required |
| ----------------- | --------------------------------------------------- | -------- |
| Phone Number      | Customer's phone number                             | Yes      |
| Loyalty Tier Name | Name of the target loyalty tier (English or Arabic) | Yes      |

### Example Excel Data

```
Phone Number    | Loyalty Tier Name
+97412345678   | Gold Tier
+97487654321   | Silver Tier
+97411111111   | Gold Tier
```

## API Endpoint

### POST `/utility/customer/bulkTierAssignmentFromExcel`

Processes bulk tier assignment from an Excel file.

#### Request Body

```json
{
  "companyId": "507f1f77bcf86cd799439011",
  "excelFilePath": "/path/to/excel/file.xlsx"
}
```

#### Response

```json
{
  "success": 10,
  "failed": 2,
  "unchanged": 1
}
```

## Usage Example

1. Prepare an Excel file with customer phone numbers and tier names
2. Upload the file to the server (or ensure it's accessible at the specified path)
3. Make a POST request to the endpoint with the company ID and file path
4. Review the response to see how many customers were successfully processed

### Example with the provided Excel file

```bash
curl -X POST http://localhost:3000/utility/customer/bulkTierAssignmentFromExcel \
  -H "Content-Type: application/json" \
  -d '{
    "companyId": "YOUR_COMPANY_ID_HERE",
    "excelFilePath": "j:\\Projects\\EButler\\enable\\enable-backend\\assets\\Beyti Group Customer Sheets upgraded.xlsx"
  }'
```

## Error Handling

The service handles various error scenarios:

- **File not found**: Returns an error if the Excel file cannot be read
- **Customer not found**: Logs a warning and skips customers not found in the database
- **Tier not found**: Logs a warning and skips customers with invalid tier names
- **Bulk assignment errors**: Counts failed assignments and continues processing

## Dependencies

The service depends on:

- `CustomerRepositoryInterface`: For finding customers by phone and company ID
- `CustomerTierServiceInterface`: For performing bulk tier assignments
- `LoyaltyTierReadServiceInterface`: For fetching company loyalty tiers
- `xlsx` library: For reading Excel files

## Configuration

The service is automatically registered in the `UtilityModule` and can be injected into other services or controllers as needed.

## Notes

- The service sets `notifyCustomers: false` by default to avoid sending notifications during bulk operations
- Phone numbers should match exactly with the format stored in the database
- Tier names are matched against both English (`nameEn`) and Arabic (`nameAr`) names
- The service processes all rows in the first worksheet of the Excel file
