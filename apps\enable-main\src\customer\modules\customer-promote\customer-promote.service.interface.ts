import { PromoteForBrandDto, SendOrdableLinkDto } from '@app/shared-stuff';

export interface CustomerPromoteServiceInterface {
  sendOrdableLink(sendOrdableLinkDto: SendOrdableLinkDto): Promise<void>;

  promoteLoyaltyForBrand(
    promoteLoyaltyStuffDto: PromoteForBrandDto,
    action: 'LoyaltyCard' | 'Registration',
  ): Promise<void>;
}

export const CustomerPromoteServiceInterface = Symbol(
  'CustomerPromoteServiceInterface',
);
