import { OrderLogIndexDto } from '../../dto/order-log-index.dto';
import {
  CurrentUser,
  OrderDocument,
  OrderLogActionEnum,
  OrderLogSentObjectDto,
  OrderLogReceivedObjectDto,
} from '@app/shared-stuff';

export interface OrderLogServiceInterface {
  findAll(orderLogIndexDto: OrderLogIndexDto);
  saveOrderLog(
    order: OrderDocument,
    sentObject: OrderLogSentObjectDto,
    receivedObject: OrderLogReceivedObjectDto,
    logAction: OrderLogActionEnum,
    createdBy: CurrentUser,
  );
}
