import { LoggerService, OrderDocument } from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { PassDeliveryService } from '../services/pass-delivery.service';

@Injectable()
export class PassDeliveryListener {
  private readonly loggerService = new LoggerService(PassDeliveryListener.name);

  constructor(private readonly passDeliveryService: PassDeliveryService) {}

  @OnEvent('order.cancelled')
  async onOrderCancelled(order: OrderDocument) {
    if (!order.passDeliveryTaskId) return;
    this.loggerService.log('[Pass Delivery] order.cancelled Event is fired');
    this.loggerService.log('[Pass Delivery] Order Code', order.code);
    await this.passDeliveryService.cancel(order);
  }

  @OnEvent('order.deleted')
  async onOrderDeleted(order: OrderDocument) {
    if (!order.passDeliveryTaskId) return;
    this.loggerService.log('[Pass Delivery] order.deleted Event is fired');
    this.loggerService.log('[Pass Delivery] Order Code', order.code);
    await this.passDeliveryService.cancel(order);
  }
}
