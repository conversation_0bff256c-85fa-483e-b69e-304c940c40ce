import { PaymentDocument } from '@app/shared-stuff';
import { CompanyDocument } from '@app/shared-stuff/models/company.model';
import { PaymentCallbackResult } from '../../enums/tess/payment-callback-result.enum';

export interface PaymentIntegrationServiceInterface {
  processPayment(
    payment: PaymentDocument,
    company: CompanyDocument,
  ): Promise<string>;
  callback(
    paymentCallbackResult: PaymentCallbackResult,
    paymentCode: string,
  ): Promise<string>;
}
