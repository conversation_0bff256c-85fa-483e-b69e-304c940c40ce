import {
  CreateDistributionCenterDto,
  DistributionCenter,
  DistributionCenterDocument,
  GetAllDistributionCenterDto,
  IndexResultDto,
  UpdateDistributionCenterDto,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface DistributionCenterServiceInterface {
  findAll(
    getAllDistributionCenterDto: GetAllDistributionCenterDto,
  ): Promise<[IndexResultDto<DistributionCenter>]>;

  create(
    createDistributionCenterDto: CreateDistributionCenterDto,
  ): Promise<DistributionCenterDocument>;

  findOne(id: Types.ObjectId): Promise<DistributionCenterDocument>;

  update(
    distributionCenterDto: UpdateDistributionCenterDto,
  ): Promise<DistributionCenterDocument>;

  findByCompanyId(
    companyId: Types.ObjectId,
  ): Promise<DistributionCenterDocument[]>;
}

export const DistributionCenterServiceInterface = Symbol(
  'DistributionCenterServiceInterface',
);
