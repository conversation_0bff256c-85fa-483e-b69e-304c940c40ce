// EBL-4590
// Define a function to update tierLevellingUpMethod based on amountSpentThreshold and sorting criteria
function updateTierLevellingUpMethod(companyId) {
  const company = db.companies.findOne({ _id: companyId });

  if (!company) {
    print(`Company with ID ${companyId} not found.`);
    return;
  }

  const tiers = db.loyaltytiers.find({ companyId: companyId }).toArray();

  const hasAmountSpentThreshold = tiers.some(
    (tier) => tier.amountSpentThreshold,
  );
  const sortingCriteria = hasAmountSpentThreshold
    ? 'order rate and amount spent'
    : 'order rate';

  const updateQuery = {
    $set: {
      'loyaltyProgramConfig.tierLevellingUpMethod': sortingCriteria,
    },
  };

  // set in the company
  db.companies.update({ _id: companyId }, updateQuery);

  print(`Updated tierLevellingUpMethod for Company ID ${companyId}`);
}

// Get all unique company IDs
const companyIds = db.loyaltytiers.distinct('companyId');

// Iterate through each company and update tierLevellingUpMethod
companyIds.forEach(updateTierLevellingUpMethod);
