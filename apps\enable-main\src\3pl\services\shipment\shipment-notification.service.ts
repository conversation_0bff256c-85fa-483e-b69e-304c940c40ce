import {
  GenericTriggerModel,
  Language,
  LanguageCode,
  LanguageToLanguageCode,
  LoggerService,
  ShipmentDocument,
  ShipmentReplacementDto,
  TempCustomerDocument,
  TemplateTo,
  TriggerAction,
  TriggerModule,
  TriggerUserDto,
} from '@app/shared-stuff';
import { Inject } from '@nestjs/common';
import { CompanyService } from '../../../company/services/company/company.service';
import { TriggerService } from '../../../notification/services/trigger/trigger.service';
import { TempCustomerServiceInterface } from '../temp-customer/temp-customer-service.interface';

export class ShipmentNotificationService {
  private readonly loggerService = new LoggerService(
    ShipmentNotificationService.name,
  );

  constructor(
    private readonly triggerService: TriggerService,
    private readonly companyService: CompanyService,
    @Inject(TempCustomerServiceInterface)
    private readonly tempCustomerService: TempCustomerServiceInterface,
  ) {}

  async fireOnShipmentStatusChanged(
    shipment: ShipmentDocument,
    replacements: ShipmentReplacementDto,
  ): Promise<void> {
    await this.fireTrigger(
      shipment,
      replacements,
      TriggerAction.ON_SHIPMENT_STATUS_UPDATE,
    );
  }

  async fireOnShipmentCreated(
    shipment: ShipmentDocument,
    replacements: ShipmentReplacementDto,
  ): Promise<void> {
    await this.fireTrigger(
      shipment,
      replacements,
      TriggerAction.ON_SHIPMENT_CREATED,
    );
  }

  private async fireTrigger(
    shipment: ShipmentDocument,
    replacements: ShipmentReplacementDto,
    triggerAction: TriggerAction,
  ): Promise<void> {
    const genericTriggerModel = await this.fillGenericTriggerModel(
      shipment,
      replacements,
    );
    const tempCustomer = await this.tempCustomerService.findBySalesOrderId(
      shipment?.salesOrderId,
    );
    const customUsers = this.getCustomUsers(tempCustomer);
    const returnedDto = await this.triggerService.fireTrigger(
      genericTriggerModel,
      triggerAction,
      customUsers,
    );
    this.loggerService.log(`${triggerAction} notification sent`, {
      shipment,
      genericTriggerModel,
      returnedDto,
    });
  }

  private getCustomUsers(tempCustomer: TempCustomerDocument): TriggerUserDto[] {
    return [
      {
        id: tempCustomer._id,
        firstName: tempCustomer?.firstName,
        lastName: tempCustomer?.lastName,
        email: tempCustomer?.email,
        phone: tempCustomer?.phone,
        countryCode: tempCustomer?.countryCode,
        preferredLanguage: LanguageCode.en,
        role: TemplateTo.CUSTOMER,
      },
    ];
  }

  private async fillGenericTriggerModel(
    shipment: ShipmentDocument,
    replacements: ShipmentReplacementDto,
  ): Promise<GenericTriggerModel> {
    const company = await this.companyService.findById(shipment.companyId);
    const tempCustomer = await this.tempCustomerService.findBySalesOrderId(
      shipment?.salesOrderId,
    );

    return {
      companyId: company._id.toHexString(),
      branchId: '',
      brandId: shipment?.brand?._id.toHexString(),
      customerId: undefined,
      countryCode: tempCustomer.countryCode,
      createdBy: undefined,
      senderId: tempCustomer.brand?.senderId ?? company.senderId,
      emailSenderId: tempCustomer.brand?.emailSenderId,
      giftRecipientUser: undefined,
      isGift: false,
      language: LanguageToLanguageCode[Language.english],
      triggerModule: TriggerModule.SHIPMENT,
      replacements,
    };
  }
}
