import {
  clientId,
  GetTokenResponse,
  LoggerService,
  MicrosAuthGrantType,
  MicrosAuthState,
  MicrosCompanyConfig,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { createHash } from 'crypto';
import * as moment from 'moment-timezone';
import * as randomstring from 'randomstring';
import { MicrosAuthHttpService } from './micros-auth-http.service';

@Injectable()
export class MicrosAuthService {
  private readonly loggerService = new LoggerService(MicrosAuthService.name);
  private readonly clientAuthState = new Map<clientId, MicrosAuthState>();
  constructor(private readonly microsAuthHttpService: MicrosAuthHttpService) {}

  public async getIdToken(microsConfig: MicrosCompanyConfig): Promise<string> {
    const { authBaseUrl, clientId } = microsConfig;
    if (!authBaseUrl) return '';
    this.loggerService.log(`Using auth base URL: ${authBaseUrl}`);

    const newMicrosAuthState = this.clientAuthState.has(clientId)
      ? await this.validateMicrosAuthState(
          microsConfig,
          this.clientAuthState.get(clientId),
        )
      : await this.initMicrosAuthState(microsConfig);
    this.clientAuthState.set(clientId, newMicrosAuthState);
    return newMicrosAuthState.idToken;
  }

  private async initMicrosAuthState(
    microsConfig: MicrosCompanyConfig,
  ): Promise<MicrosAuthState> {
    const codeVerifier = this.generateCodeVerifier();
    const codeChallenge = this.convertToCodeChallenge(codeVerifier);

    await this.microsAuthHttpService.authorize(
      microsConfig.authBaseUrl,
      microsConfig.clientId,
      codeChallenge,
    );

    const authCode = await this.microsAuthHttpService.signIn({
      ...microsConfig,
      codeChallenge,
    });

    const response: GetTokenResponse =
      await this.microsAuthHttpService.getToken(
        microsConfig.authBaseUrl,
        authCode,
        microsConfig.clientId,
        codeVerifier,
        MicrosAuthGrantType.AUTHORIZATION_CODE,
      );

    return {
      codeVerifier,
      ...this.convertGetTokenResponse(response),
    };
  }

  private generateCodeVerifier(): string {
    return randomstring.generate({ length: 64 });
  }

  private convertToCodeChallenge(codeVerifier: string): string {
    return createHash('sha256').update(codeVerifier).digest('base64url');
  }

  private async validateMicrosAuthState(
    microsConfig: MicrosCompanyConfig,
    microsAuthState: MicrosAuthState,
  ): Promise<MicrosAuthState> {
    if (moment.utc().isBefore(microsAuthState.idTokenExpiry))
      return microsAuthState;
    else return await this.refreshIdToken(microsConfig, microsAuthState);
  }

  private async refreshIdToken(
    microsConfig: MicrosCompanyConfig,
    microsAuthState: MicrosAuthState,
  ): Promise<MicrosAuthState> {
    const response: GetTokenResponse =
      await this.microsAuthHttpService.getToken(
        microsConfig.authBaseUrl,
        microsAuthState.refreshToken,
        microsConfig.clientId,
        microsAuthState.codeVerifier,
        MicrosAuthGrantType.REFRESH_TOKEN,
      );

    return {
      ...microsAuthState,
      ...this.convertGetTokenResponse(response),
    };
  }

  private convertGetTokenResponse(
    response: GetTokenResponse,
  ): Pick<MicrosAuthState, 'idToken' | 'idTokenExpiry' | 'refreshToken'> {
    return {
      idToken: response.id_token,
      idTokenExpiry: moment.utc().add(response.expires_in, 'seconds').toDate(),
      refreshToken: response.refresh_token,
    };
  }
}
