import {
  BenefitDocument,
  CollectionName,
  CouponDocument,
  Customer,
  CustomerDocument,
  CustomerEarnedBenefit,
  CustomerUsedBenefit,
  EarnedReward,
  EmbeddedBrandDto,
  EmbeddedTierDto,
  FindByPhoneAndCompanyIdOptions,
  forEachAsync,
  GenericRepository,
  LoyaltyStatus,
  LoyaltyTierDocument,
  MessageCustomerDto,
  PunchCardDocument,
  PunchCardProgress,
  RegisteredPass,
  TierStatus,
  UsedReward,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import * as moment from 'moment-timezone';
import { FilterQuery, Model, Types, UpdateQuery } from 'mongoose';
import { StoreDocument } from '../../../store/models/store.model';
import { CustomerRepositoryInterface } from './customer.repository.interface';

@Injectable()
export class CustomerRepository
  extends GenericRepository<CustomerDocument, Customer>
  implements CustomerRepositoryInterface
{
  constructor(
    @InjectModel(CollectionName.CUSTOMER)
    private customerModel: Model<CustomerDocument, Customer>,
  ) {
    super(customerModel);
  }

  async removeLatestPayment(id: Types.ObjectId): Promise<void> {
    await this.customerModel.updateOne(
      { _id: id },
      { $unset: { latestPayment: '' } },
    );
  }

  async findByCompanyId(
    companyId: Types.ObjectId | Types.ObjectId[],
    hasPasses?: boolean,
  ): Promise<CustomerDocument[]> {
    return await this.customerModel.find({
      deletedAt: null,
      company: Array.isArray(companyId) ? { $in: companyId } : companyId,
      ...(hasPasses && {
        registeredPasses: { $elemMatch: { deletedAt: null } },
      }),
    });
  }

  async findOne(
    filterObj: FilterQuery<CustomerDocument>,
  ): Promise<CustomerDocument> {
    return this.customerModel.findOne({ deletedAt: null, ...filterObj });
  }

  async insertMany(customers: Customer[]): Promise<void> {
    await this.customerModel.insertMany(customers);
  }

  async findByPhoneOrEmail(
    customerPhone: string,
    customerEmail: string,
    companyId: Types.ObjectId,
  ): Promise<CustomerDocument | null> {
    return await this.customerModel.findOne({
      deletedAt: null,
      company: companyId,
      $or: [{ phone: customerPhone }, { email: customerEmail }],
    });
  }

  async findByPhoneAndCompanyId(
    phone: string,
    companyId: Types.ObjectId,
    options?: FindByPhoneAndCompanyIdOptions,
  ): Promise<CustomerDocument> {
    const { withDeleted, countryCode, projection } = options || {};
    const query = {
      company: companyId,
      phone,
    };
    if (!withDeleted) query['deletedAt'] = null;
    if (countryCode) query['country_code'] = countryCode;
    const mongoQuery = this.customerModel.findOne(query);
    if (projection) mongoQuery.select(projection);
    return mongoQuery.exec();
  }

  async populateField(
    customers: CustomerDocument[],
    field: string,
  ): Promise<CustomerDocument[]> {
    return await this.customerModel.populate(customers, field);
  }

  async findEligibleForTierComputation(
    companyIds: Types.ObjectId[],
    tierUpdatedBefore?: Date,
    tierUpdatedAfter?: Date,
    tierStatus?: TierStatus,
  ): Promise<CustomerDocument[]> {
    return await this.customerModel.find({
      deletedAt: null,
      loyaltyStatus: { $in: [LoyaltyStatus.ENROLLED, LoyaltyStatus.MEMBER] },
      company: { $in: companyIds },
      ...(tierUpdatedAfter && { tierUpdatedAt: { $gte: tierUpdatedAfter } }),
      ...(tierUpdatedBefore && { tierUpdatedAt: { $lt: tierUpdatedBefore } }),
      ...(tierStatus && { tierStatus }),
    });
  }

  async findRecentlyComputed(
    companyId: Types.ObjectId,
  ): Promise<CustomerDocument[]> {
    return await this.customerModel.find({
      deletedAt: null,
      company: companyId,
      carryOverUpdatedAt: { $gte: moment.utc().startOf('day').toDate() },
    });
  }

  async findEligibleForGracePeriodReminder(
    companyIds: Types.ObjectId[],
    registrationDay: Date,
  ): Promise<CustomerDocument[]> {
    return await this.customerModel.find({
      deletedAt: null,
      loyaltyTier: { $exists: true },
      'loyaltyTier.isVipTier': { $ne: true },
      loyaltyStatus: LoyaltyStatus.MEMBER,
      tierStatus: TierStatus.NOT_MAINTAINED,
      company: { $in: companyIds },
      loyaltyRegistrationAt: {
        $gte: moment.utc(registrationDay).startOf('day').toDate(),
        $lte: moment.utc(registrationDay).endOf('day').toDate(),
      },
    });
  }

  async findEligibleForTierDiscount(
    loyaltyTier: LoyaltyTierDocument,
  ): Promise<CustomerDocument[]> {
    return await this.customerModel.find({
      deletedAt: null,
      'loyaltyTier._id': loyaltyTier._id,
      loyaltyStatus: LoyaltyStatus.MEMBER,
    });
  }

  async findEligibleForCoupon(
    coupon: CouponDocument,
  ): Promise<CustomerDocument[]> {
    return await this.customerModel.find({
      deletedAt: null,
      loyaltyPoints: { $gte: coupon.loyaltyPointCost },
      loyaltyStatus: LoyaltyStatus.MEMBER,
    });
  }

  async findByRegisteredPasses(
    registeredPass: Partial<RegisteredPass>,
    updatedSince?: Date,
  ): Promise<CustomerDocument[]> {
    return await this.customerModel.find({
      deletedAt: null,
      registeredPasses: { $elemMatch: { ...registeredPass, deletedAt: null } },
      ...(updatedSince && { updatedAt: { $gte: updatedSince } }),
    });
  }

  async updateOne(
    filter: FilterQuery<CustomerDocument>,
    updateQuery: UpdateQuery<CustomerDocument>,
  ) {
    await this.customerModel.updateOne(filter, updateQuery);
  }

  async findManyById(
    customerIds: Types.ObjectId[],
  ): Promise<CustomerDocument[]> {
    return await this.customerModel.find({
      _id: { $in: customerIds },
      deletedAt: null,
    });
  }

  async syncBrandChanges(brand: EmbeddedBrandDto): Promise<void> {
    await this.customerModel.updateMany(
      { 'activeBrand._id': brand._id },
      { $set: { activeBrand: brand } },
    );

    await this.customerModel.updateMany(
      { 'firstBrandOrderd._id': brand._id },
      { $set: { firstBrandOrderd: brand } },
    );
  }

  findByLoyaltyTier(
    loyaltyTierIds: Types.ObjectId[],
    companyId?: Types.ObjectId,
    hasPasses?: boolean,
  ): Promise<CustomerDocument[]> {
    return this.customerModel.find({
      deletedAt: null,
      'loyaltyTier._id': { $in: loyaltyTierIds },
      ...(hasPasses && {
        registeredPasses: { $elemMatch: { deletedAt: null } },
      }),
      ...(companyId && { company: companyId }),
    });
  }

  findByBrandId(
    brandId: Types.ObjectId,
    hasPasses?: boolean,
  ): Promise<CustomerDocument[]> {
    return this.customerModel.find({
      deletedAt: null,
      $or: [
        { 'activeBrand._id': brandId },
        { 'registrationContext.brandId': brandId },
      ],
      ...(hasPasses && {
        registeredPasses: { $elemMatch: { deletedAt: null } },
      }),
    });
  }

  async updateEmbeddedTier(embeddedTierDto: EmbeddedTierDto): Promise<void> {
    await this.customerModel.updateMany(
      { 'loyaltyTier._id': embeddedTierDto._id },
      [
        {
          $set: {
            loyaltyTier: { $mergeObjects: ['$loyaltyTier', embeddedTierDto] },
          },
        },
      ],
    );
  }

  async updateEmbeddedStore(store: StoreDocument): Promise<void> {
    await this.customerModel.updateMany(
      { 'ordableStores._id': store._id },
      { $set: { 'ordableStores.$.brands': store.brands } },
    );
  }

  async findSyncableForOrdableStore(
    store: StoreDocument,
  ): Promise<CustomerDocument[]> {
    return await this.customerModel.find({
      deletedAt: null,
      company: store.companyId,
      loyaltyStatus: LoyaltyStatus.MEMBER,
      [`ordableStores.${store._id}`]: null,
    });
  }

  async addRewardsToCustomer(
    customer: CustomerDocument,
    rewards: EarnedReward[],
  ): Promise<CustomerDocument> {
    customer.rewards = (customer.rewards || []).concat(rewards);
    await this.customerModel.updateOne(
      { _id: customer._id },
      { $push: { rewards: { $each: rewards } } },
    );
    return customer;
  }

  async redeemRewards(
    customer: CustomerDocument,
    rewards: EarnedReward[],
  ): Promise<void> {
    const rewardIds = rewards.map((reward) => reward._id.toString());
    customer.rewards = customer.rewards.filter(
      (reward) => !rewardIds.includes(reward._id.toString()),
    );
    customer.markModified('rewards');

    const usedRewards = rewards.map((reward) => ({
      ...reward,
      redeemedAt: moment.utc().toDate(),
    }));
    customer.usedRewards = (customer.usedRewards || []).concat(usedRewards);
    customer.markModified('usedRewards');

    await customer.save();
  }

  async updateRewards(
    customerId: Types.ObjectId,
    rewards: EarnedReward[] | UsedReward[],
  ): Promise<void> {
    if (!rewards || rewards.length === 0) return;
    const areUsedRewards = 'redeemedAt' in rewards[0];
    const rewardField = areUsedRewards ? 'usedRewards' : 'rewards';

    await this.customerModel.updateOne(
      { _id: customerId },
      { $set: { [rewardField]: rewards } },
    );
  }

  async findByPunchCardId(
    punchCardId: Types.ObjectId,
  ): Promise<CustomerDocument[]> {
    return await this.customerModel.find({
      deletedAt: null,
      'punchCardProgress.punchCard._id': punchCardId,
    });
  }

  async findByPunchCardProgress(
    punchCardId: Types.ObjectId,
    minimumCount: number,
  ): Promise<CustomerDocument[]> {
    return await this.customerModel.find({
      deletedAt: null,
      punchCardProgress: {
        $elemMatch: {
          'punchCard._id': punchCardId,
          count: { $gte: minimumCount },
          completedAt: null,
        },
      },
    });
  }

  async findByEmail(
    company: Types.ObjectId,
    email: string,
  ): Promise<CustomerDocument | null> {
    return await this.customerModel.findOne({
      deletedAt: null,
      company,
      email,
    });
  }

  async updateRegisteredPassTimestamp({
    passTypeIdentifier,
    serialNumber,
  }: RegisteredPass): Promise<void> {
    await this.customerModel.updateOne(
      {
        registeredPasses: { $elemMatch: { passTypeIdentifier, serialNumber } },
      },
      { $set: { 'registeredPasses.$.updatedAt': moment.utc().toDate() } },
    );
  }

  async handlePunchCardCompletionLowered(
    punchCard: PunchCardDocument,
    newCompletionPoint: number,
  ): Promise<void> {
    const completionDate = moment.utc().toDate();
    await this.customerModel.updateMany(
      { 'punchCardProgress.punchCard._id': punchCard._id },
      {
        $set: {
          'punchCardProgress.$[finished].completedAt': completionDate,
        },
      },
      {
        arrayFilters: [
          {
            'finished.punchCard._id': punchCard._id,
            'finished.deletedAt': null,
            'finished.count': { $gte: newCompletionPoint },
          },
        ],
      },
    );

    const updatedCustomers = await this.customerModel.find({
      punchCardProgress: {
        $elemMatch: {
          'punchCard._id': punchCard._id,
          completedAt: completionDate,
        },
      },
    });

    await forEachAsync(updatedCustomers, async (customer) => {
      const newProgress = new PunchCardProgress(punchCard);
      await this.customerModel.updateOne(
        { _id: customer._id },
        { $push: { punchCardProgress: newProgress } },
      );
    });
  }

  public async resetCarryOver(companyId: Types.ObjectId): Promise<void> {
    await this.customerModel.updateMany({ company: companyId }, [
      {
        $unset: [
          'orderRateCarryOver',
          'amountSpentCarryOver',
          'pointsRateCarryOver',
          'carryOverUpdatedAt',
        ],
      },
    ]);
  }

  public async isShortCodeTaken(
    company: Types.ObjectId,
    shortCode: string,
  ): Promise<boolean> {
    const result = await this.customerModel.exists({
      company,
      shortCode,
      deletedAt: null,
    });
    return !!result;
  }

  public async updateCustomerEarnedBenefits(
    benefit: BenefitDocument,
  ): Promise<void> {
    await this.customerModel.updateMany({ 'earnedBenefits._id': benefit._id }, [
      {
        $set: {
          'earnedBenefits.$': {
            $mergeObjects: [
              '$earnedBenefits.$',
              benefit,
              {
                'earnedBenefits.$.earnedAt': '$earnedBenefits.earnedAt',
                'earnedBenefits.$.config.numberOfUsages':
                  '$earnedBenefits.config.numberOfUsages',
              },
            ],
          },
        },
      },
    ]);
  }

  async updateBenefits(
    customerId: Types.ObjectId,
    benefits: CustomerEarnedBenefit[] | CustomerUsedBenefit[],
    benefitField: 'earnedBenefits' | 'usedBenefits',
  ): Promise<void> {
    await this.customerModel.updateOne(
      { _id: customerId },
      { $set: { [benefitField]: benefits ?? [] } },
    );
  }

  public async updateCustomerUsageByTier(
    loyaltyTierId: Types.ObjectId,
    usesField:
      | 'freeDeliveryRemainingNumberOfUses'
      | 'percentDiscountRemainingNumberOfUses',
    uses: number,
  ): Promise<void> {
    await this.customerModel.updateMany(
      { 'loyaltyTier._id': loyaltyTierId },
      { $set: { [`loyaltyTier.${usesField}`]: uses } },
    );
  }

  public async addRemainingUsesByTier(
    loyaltyTierId: Types.ObjectId,
    usesField:
      | 'freeDeliveryRemainingNumberOfUses'
      | 'percentDiscountRemainingNumberOfUses',
    additionalUses: number,
    maximumUses: number,
  ) {
    const field = `loyaltyTier.${usesField}`;
    const fieldRef = '$' + field;
    const newRemainingUses = { $add: [fieldRef, additionalUses] };
    await this.customerModel.updateMany({ 'loyaltyTier._id': loyaltyTierId }, [
      {
        $set: {
          [field]: {
            $cond: {
              if: { $lte: [newRemainingUses, maximumUses] },
              then: {
                $cond: {
                  if: { $gt: [newRemainingUses, 0] },
                  then: newRemainingUses,
                  else: 0,
                },
              },
              else: maximumUses,
            },
          },
        },
      },
    ]);
  }

  public async messageCustomer({
    customerId,
    message,
  }: MessageCustomerDto): Promise<CustomerDocument> {
    return this.customerModel.findByIdAndUpdate(
      customerId,
      {
        $push: { activeWalletPassMessages: { $each: [message], $position: 0 } },
      },
      { new: true },
    );
  }

  public async getCompanyIdForCustomerId(
    customerId: Types.ObjectId,
  ): Promise<Types.ObjectId> {
    const customer = await this.customerModel.findOne(
      { _id: customerId },
      { company: 1 },
    );
    return customer.company;
  }
}
