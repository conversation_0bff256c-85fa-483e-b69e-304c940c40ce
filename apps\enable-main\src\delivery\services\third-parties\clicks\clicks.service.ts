import {
  EmbeddedBrandDto,
  LoggerService,
  OrderDeliveryType,
  OrderDocument,
  OrderLogActionEnum,
  OrderPaymentMethod,
  OrderPaymentStatus,
  SavedLocation,
} from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as moment from 'moment-timezone';
import * as randomestring from 'randomstring';
import { IntegrationLogRepositoryInterface } from '../../../../integration/integration-log/repositories/interfaces/integration-log.repository.interface';
import { HelperService } from '../../../../shared/services/helper/helper.service';
import { ClicksConfig } from '../../../dto/clicks-config.dto';
import { ClicksTaskToCreate } from '../../../dto/clicks.dto';
import { ThirdPartyTaskCreationDto } from '../../../dto/third-party-task-creation.dto';
import { ThirdPartiesServiceInterface } from '../third-parties.service.interface';
import { ThirdPartySharedService } from '../third-party-shared.service';

@Injectable()
export class ClicksService implements ThirdPartiesServiceInterface {
  vehicleTypes: string[] = [
    'car',
    'motor cycle',
    'bicycle',
    'scooter',
    'foot',
    'truck',
  ] as const;
  defaultVehicleType: string = 'bicycle';
  private readonly loggerService = new LoggerService(ClicksService.name);

  constructor(
    private configService: ConfigService,
    private httpService: HttpService,
    private eventEmitter: EventEmitter2,
    private thirdPartySharedService: ThirdPartySharedService,
    @Inject('IntegrationLogRepositoryInterface')
    private readonly integrationLogRepository: IntegrationLogRepositoryInterface,
    private readonly helperService: HelperService,
  ) {}

  async createTask(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
  ): Promise<any> {
    const deliveryLocation =
      await this.thirdPartySharedService.getDeliveryLocation(
        thirdPartyTaskCreationDto,
      );
    const pickupLocation = await this.thirdPartySharedService.getPickupLocation(
      thirdPartyTaskCreationDto,
    );

    const deliveryAddressString =
      await this.thirdPartySharedService.getDeliveryAddressString(
        deliveryLocation,
      );
    const pickupAddressString =
      await this.thirdPartySharedService.getPickupAddressString(pickupLocation);

    const brandOnBranch = this.getBrandOnBranch(thirdPartyTaskCreationDto);

    const clicksTaskToCreate = this.constructCreateClicksTaskDto(
      thirdPartyTaskCreationDto,
      brandOnBranch,
      deliveryLocation,
      pickupLocation,
      deliveryAddressString,
      pickupAddressString,
    );
    const clicksConfig = this.loadClicksConfig();
    try {
      const taskCreationResponse = await this.performClicksRequest(
        clicksConfig.baseUrl + '/create_task',
        clicksConfig.apiKey,
        clicksTaskToCreate,
      );
      this.loggerService.log(
        'Clicks Task Created',
        clicksTaskToCreate,
        taskCreationResponse,
      );
      await this.integrationLogRepository.logSuccess(
        OrderLogActionEnum.CLICKS_TASK_CREATION,
        {
          clicksConfig: clicksConfig,
          requestBody: clicksTaskToCreate,
        },
        taskCreationResponse,
        clicksTaskToCreate.order_id,
      );
      return { request: clicksTaskToCreate, response: taskCreationResponse };
    } catch (error) {
      this.loggerService.error(
        `Error while : ${OrderLogActionEnum.CLICKS_TASK_CREATION}` +
          error.message,
        error.stacktrace,
        clicksTaskToCreate,
      );

      await this.integrationLogRepository.logError(
        OrderLogActionEnum.CLICKS_TASK_CREATION,
        {
          clicksConfig: clicksConfig,
          requestBody: clicksTaskToCreate,
        },
        this.helperService.transformError(error),
      );
    }
  }

  async getClicksDetails(taskIds: number[]) {
    const BASE_URL = await this.configService.get('CLICKS_BASE_URL');
    const apiKey = await this.configService.get('CLICKS_API_KEY');
    const response = await this.performClicksRequest(
      BASE_URL + '/get_job_details',
      apiKey,
      { job_ids: taskIds.map((x) => x.toString()) },
    );
    return response['data'];
  }

  async applyPostFunction(taskCreationResponse: any, order: OrderDocument) {
    await this.updateOrder(taskCreationResponse, order);
  }

  onTaskUpdated(data: any) {
    this.eventEmitter.emit('clicks.updated', data);
  }

  private loadClicksConfig(): ClicksConfig {
    return {
      baseUrl: this.configService.get('CLICKS_BASE_URL'),
      apiKey: this.configService.get('CLICKS_API_KEY'),
    };
  }

  private getBrandOnBranch(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
  ): EmbeddedBrandDto {
    return thirdPartyTaskCreationDto?.branch?.brands.find((brand) =>
      brand._id.equals(thirdPartyTaskCreationDto?.order.brand?._id),
    );
  }

  private constructCreateClicksTaskDto(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
    brandOnBranch: EmbeddedBrandDto,
    deliveryLocation: SavedLocation,
    pickupLocation: SavedLocation,
    deliveryAddressString: string,
    pickupAddressString: string,
  ): ClicksTaskToCreate {
    // TODO: use brand.callCenterConfig.currentlyDeliveringIn
    const pickupDatePlus20mins = moment().utc().add(20, 'minutes');
    this.loggerService.log('pickupDatePlus20mins', pickupDatePlus20mins);
    const deliveryDateMinus20mins = moment
      .utc(thirdPartyTaskCreationDto.order.delivery_date)
      .subtract(20, 'minutes');
    this.loggerService.log('deliveryDateMinus20mins', deliveryDateMinus20mins);
    const deliveryDatePlus40Mins = moment().utc().add(40, 'minutes');

    const pickupDate =
      thirdPartyTaskCreationDto.order.delivery_type == OrderDeliveryType.urgent
        ? pickupDatePlus20mins
        : deliveryDateMinus20mins;
    this.loggerService.log('final pickupDate', pickupDate);
    const deliveryDate =
      thirdPartyTaskCreationDto.order.delivery_type == OrderDeliveryType.urgent
        ? deliveryDatePlus40Mins
        : thirdPartyTaskCreationDto.order.delivery_date;

    return {
      order_id: thirdPartyTaskCreationDto.order.code,
      merchant_id: '',
      team_id: '',
      tracking_link: '',
      auto_assignment: '1',
      job_description: thirdPartyTaskCreationDto.order.order_remarks,
      job_pickup_phone: brandOnBranch
        ? brandOnBranch.phoneNumber
        : thirdPartyTaskCreationDto.company.phone,
      job_pickup_name: brandOnBranch
        ? brandOnBranch.name
        : thirdPartyTaskCreationDto.company.name,
      job_pickup_email: brandOnBranch
        ? `${brandOnBranch.phoneNumber}@e-butler.com`
        : thirdPartyTaskCreationDto.company.email,
      job_pickup_address: pickupAddressString,
      job_pickup_latitude: pickupLocation
        ? pickupLocation.latitude.toString()
        : '0',
      job_pickup_longitude: pickupLocation
        ? pickupLocation.longitude.toString()
        : '0',
      job_pickup_datetime: moment
        .utc(pickupDate)
        .clone()
        .tz(thirdPartyTaskCreationDto.order.localization?.timezone)
        .toString() as any,
      customer_email:
        thirdPartyTaskCreationDto.customer.email ??
        'default' + randomestring.generate(5) + '@e-butler.com',
      customer_username: thirdPartyTaskCreationDto.order.is_gift
        ? thirdPartyTaskCreationDto.order.recipient_name
        : thirdPartyTaskCreationDto.customer.full_name,
      customer_phone: thirdPartyTaskCreationDto.order.is_gift
        ? (thirdPartyTaskCreationDto.order.recipient_country_code.includes('+')
            ? thirdPartyTaskCreationDto.order.recipient_country_code
            : `+${thirdPartyTaskCreationDto.order.recipient_country_code}`) +
          thirdPartyTaskCreationDto.order.recipient_phone
        : (thirdPartyTaskCreationDto.customer.country_code.includes('+')
            ? thirdPartyTaskCreationDto.customer.country_code
            : `+${thirdPartyTaskCreationDto.customer.country_code}`) +
          thirdPartyTaskCreationDto.customer.phone,
      customer_address: deliveryAddressString,
      latitude: deliveryLocation ? deliveryLocation.latitude.toString() : '0',
      longitude: deliveryLocation ? deliveryLocation.longitude.toString() : '0',
      job_delivery_datetime: moment
        .utc(deliveryDate)
        .clone()
        .tz(thirdPartyTaskCreationDto.order.localization?.timezone)
        .toString() as any,
      tags: '',
      has_delivery: '1',
      has_pickup: '1',
      layout_type: '0',
      transport_type: this.thirdPartySharedService.getTookanTransport(
        thirdPartyTaskCreationDto.order.vehicleType ?? this.defaultVehicleType,
      ),
      timezone: moment.tz
        .zone(thirdPartyTaskCreationDto.order?.localization?.timezone)
        .utcOffset(moment.utc().valueOf())
        .toString(),
      pickup_custom_field_template: 'Request_Driver_2',
      custom_field_template: 'Order_Details',
      meta_data: [
        {
          label: 'totalFare',
          data:
            thirdPartyTaskCreationDto.order.payment_method ==
              OrderPaymentMethod.online &&
            thirdPartyTaskCreationDto.order.payment_status ==
              OrderPaymentStatus.COMPLETED
              ? 'Paid Already'
              : thirdPartyTaskCreationDto.order.total_amount.toString(),
        },
        {
          label: 'Payment_Type',
          data:
            (thirdPartyTaskCreationDto.order.payment_method ==
              OrderPaymentMethod.online &&
              thirdPartyTaskCreationDto.order.payment_status !=
                OrderPaymentStatus.COMPLETED) ||
            thirdPartyTaskCreationDto.order.payment_method ==
              OrderPaymentMethod.cash
              ? 'CASH'
              : 'PAID',
        },
        {
          label: 'Special_Instruction',
          data: thirdPartyTaskCreationDto.order.order_remarks,
        },
      ],
      pickup_meta_data: [
        {
          label: 'Order_Amount',
          data:
            thirdPartyTaskCreationDto.order.payment_method ==
              OrderPaymentMethod.online &&
            thirdPartyTaskCreationDto.order.payment_status ==
              OrderPaymentStatus.COMPLETED
              ? 'Paid Already'
              : thirdPartyTaskCreationDto.order.total_amount.toString(),
        },
        {
          label: 'Customer_Number',
          data: thirdPartyTaskCreationDto.order.customer_phone,
        },
      ],
    };
  }

  private async performClicksRequest(url, apiKey, data) {
    return new Promise((resolve, reject) => {
      this.httpService
        .post(url, data, {
          headers: { api_key: apiKey },
        })
        .subscribe({
          next: (data) => resolve(data.data),
          error: (error) => {
            this.loggerService.error(
              error.message + 'Error while perform Clicks request',
              error.stacktrace,
            );
            reject({ status: 'error', message: JSON.stringify(error) });
          },
        });
    });
  }

  private async updateOrder(response: any, order: OrderDocument) {
    if (response) {
      this.loggerService.log('on update Clicks order, Response: ', response);
      order.assigned_driver_name = 'Clicks Driver';
      order.driver = undefined;
      order.deliveryTaskCreated = true;
      if (response['data']) {
        order.tookan_delivery_track_url = response.data.delivery_tracing_link;
        order.tookan_pickup_track_url = response.data.pickup_tracking_link;
        const taskIds = [
          response['data']['pickup_job_id'],
          response['data']['delivery_job_id'],
        ];
        order.integrationInfo && order.integrationInfo.clicksInfo
          ? (order.integrationInfo.clicksInfo.taskIds = taskIds)
          : (order.integrationInfo = {
              clicksInfo: { taskIds: taskIds, taskLastStatus: undefined },
            });
      }
      await order.save();
    } else {
      this.loggerService.error(
        'there is no response coming from CLicks with order: ',
        order,
      );
    }
  }
}
