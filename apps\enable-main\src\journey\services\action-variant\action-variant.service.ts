import {
  Action,
  CreateActionVariantDto,
  IndexActionVariantDto,
  MicroserviceCommunicationService,
  TemplateDocument,
  UpdateActionVariantDto,
} from '@app/shared-stuff';
import {
  Inject,
  Injectable,
  OnModuleDestroy,
  OnModuleInit,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { Types } from 'mongoose';
import { ActionVariantServiceInterface } from './action-variant.service.interface';

@Injectable()
export class ActionVariantService
  implements ActionVariantServiceInterface, OnModuleInit, OnModuleDestroy
{
  constructor(
    @Inject('enable-main-notification-producer')
    private client: ClientProxy,
    private microserviceCommunicationService: MicroserviceCommunicationService,
  ) {}

  onModuleInit() {
    this.microserviceCommunicationService.connect(this.client);
  }

  onModuleDestroy() {
    this.microserviceCommunicationService.disconnect(this.client);
  }

  async create(
    createActionVariantDto: CreateActionVariantDto,
  ): Promise<TemplateDocument> {
    return await this.microserviceCommunicationService.produceAndWaitForResponse(
      this.client,
      'action-variant.create.request',
      createActionVariantDto,
    );
  }

  async index(indexVariantDto: IndexActionVariantDto): Promise<Action> {
    return await this.microserviceCommunicationService.produceAndWaitForResponse(
      this.client,
      'action-variant.index.request',
      indexVariantDto,
    );
  }

  async update(
    updateActionVariantDto: UpdateActionVariantDto,
  ): Promise<TemplateDocument> {
    return await this.microserviceCommunicationService.produceAndWaitForResponse(
      this.client,
      'action-variant.update.request',
      updateActionVariantDto,
    );
  }

  async delete(actionVariantId: Types.ObjectId): Promise<number> {
    return await this.microserviceCommunicationService.produceAndWaitForResponse(
      this.client,
      'action-variant.delete.request',
      { actionVariantId },
    );
  }
}
