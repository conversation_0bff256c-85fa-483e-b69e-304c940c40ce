import { CollectionName, DocumentRedirectSchema } from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { GoogleCloudStorageService } from './google-cloud-storage.service';

@Module({
  imports: [
    ConfigModule,
    MongooseModule.forFeature([
      {
        name: CollectionName.DOCUMENT_REDIRECT,
        schema: DocumentRedirectSchema,
      },
    ]),
  ],
  providers: [GoogleCloudStorageService],
  exports: [GoogleCloudStorageService],
})
export class StorageModule {}
