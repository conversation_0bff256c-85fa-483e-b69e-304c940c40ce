import {
  Create<PERSON>randDto,
  GenericExceptionFilter,
  IndexBrandDto,
  TransformInterceptor,
  UpdateBrandDto,
} from '@app/shared-stuff';
import { PatchBrandConfigDto } from '@app/shared-stuff/dtos/brand/patch-brand-config.dto';
import {
  Body,
  Controller,
  Get,
  Inject,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Req,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { Types } from 'mongoose';
import { BrandServiceInterface } from '../services/brand/brand.service.interface';

@Controller('brand')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags('Brand')
@SetMetadata('module', 'brand')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class BrandController {
  constructor(
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandServiceInterface,
  ) {}

  @Post()
  @SetMetadata('action', 'create')
  async create(@Body() createBrandDto: CreateBrandDto, @Req() req: Request) {
    createBrandDto.companyId = req['company_id']
      ? req['company_id']
      : createBrandDto.companyId;
    return await this.brandService.create(createBrandDto);
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  async findById(@Param('id') id: Types.ObjectId) {
    return await this.brandService.findById(id);
  }

  @Get('/public/:id')
  @SetMetadata('public', 'true')
  async findByIdForPublicPages(@Param('id') id: Types.ObjectId) {
    return await this.brandService.findByIdForPublicPages(id);
  }

  @Put()
  @SetMetadata('action', 'update')
  async update(@Body() updateBrandDto: UpdateBrandDto) {
    return await this.brandService.update(updateBrandDto);
  }

  @Get()
  @SetMetadata('action', 'index')
  async index(@Query() indexBrandDto: IndexBrandDto, @Req() req: Request) {
    indexBrandDto.companyId = req['company_id']
      ? req['company_id']
      : indexBrandDto.companyId;
    const selectedBrands = await this.brandService.index(
      indexBrandDto,
      req['branches'],
    );
    const res = {
      brands: selectedBrands[0]['paginatedResult'],
      totalCount: selectedBrands[0]['totalCount'][0]
        ? selectedBrands[0]['totalCount'][0]['createdAt']
        : 0,
    };
    return res;
  }

  @Patch('many')
  @SetMetadata('action', 'patchConfig')
  async patchConfig(
    @Body() patchBrandConfigDto: PatchBrandConfigDto,
  ): Promise<void> {
    return await this.brandService.patchConfig(patchBrandConfigDto);
  }
}
