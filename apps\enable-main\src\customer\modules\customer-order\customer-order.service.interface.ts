import {
  CompanyDocument,
  CustomerDocument,
  LoyaltyProgress,
  Order,
  OrderDocument,
} from '@app/shared-stuff';

export interface CustomerOrderServiceInterface {
  computeLoyaltyProgress(
    company: CompanyDocument,
    order: Order,
    customer: CustomerDocument,
  ): Promise<LoyaltyProgress>;
  assignOrderToCustomer(
    order: OrderDocument,
    customer: CustomerDocument,
  ): Promise<CustomerDocument>;
  completeCustomerOrder(order: OrderDocument): Promise<void>;
}

export const CustomerOrderServiceInterface = Symbol(
  'CustomerOrderServiceInterface',
);
