// EBL-4918 - Points as Tier Levelling Up Method
// Init points rate for all customers
// Assumes that the current points exchange rate is applicable to all orders in the current calendar cycle

const getCalendarStartDate = (calendarSystem) => {
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  return calendarSystem === 'quarterly'
    ? new Date(year, Math.floor(month / 3) * 3, 1)
    : new Date(year, month, 1);
};

const loyaltyOrderSources = [
  'dine_in',
  'walk_in',
  'drive_thru',
  'direct_call',
  'call_center',
  'whatsapp',
  'webstore',
  'pos',
  'live_chat',
  'takeaway',
];

db.companies
  .find({
    'loyaltyProgramConfig.earningsExchangeRate': { $exists: true },
    'loyaltyProgramConfig.calendarSystem': { $exists: true },
  })
  .forEach((company) => {
    const calendarStartDate = getCalendarStartDate(
      company.loyaltyProgramConfig.calendarSystem,
    );
    const exchangeRate = company.loyaltyProgramConfig.earningsExchangeRate;

    const getPointsRate = ({ orders }) => {
      return (
        orders
          .filter((order) => new Date(order.pickup_date) > calendarStartDate)
          .filter((order) => order.isOrderCounted)
          .filter((order) => order.isCartValueThresholdMet)
          .filter((order) => loyaltyOrderSources.includes(order.source))
          .filter((order) => order.status === 'COMPLETED')
          .map((order) => order.total_amount)
          .reduce(
            (accumulator, currentValue) => accumulator + currentValue,
            0,
          ) * exchangeRate
      );
    };
    const writes = db.customers
      .find(
        {
          company: company._id,
          last_order_date: { $gt: calendarStartDate },
          'orders.0': { $exists: true },
        },
        { orders: 1 },
      )
      .toArray()
      .map((customer) => ({
        updateOne: {
          filter: { _id: customer._id },
          update: { $set: { pointsRate: getPointsRate(customer) } },
        },
      }));

    if (writes.length > 0) {
      console.log(
        `Initializing pointsRate for ${writes.length} customers of ${company._id}`,
      );
      db.customers.bulkWrite(writes, { ordered: false });
    }
  });
