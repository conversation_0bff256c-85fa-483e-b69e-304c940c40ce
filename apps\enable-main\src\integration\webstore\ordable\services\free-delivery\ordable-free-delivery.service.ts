import {
  CustomerDocument,
  LoggerService,
  LoyaltyTierDocument,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import * as moment from 'moment-timezone';

import { CustomerReadServiceInterface } from '../../../../../customer/modules/customer-read/customer-read.service.interface';
import { StoreDocument } from '../../../../../store/models/store.model';
import { OrdablePromotionResponseDto } from '../../dtos/promotions/ordable-promotion-response.dto';
import { OrdablePromotionDto } from '../../dtos/promotions/ordable-promotion.dto';
import { OrdableDiscountType } from '../../enums/ordable-discount-type.enum';
import { OrdableHttpRequestsServiceInterface } from '../ordable-http-requests.service.interface';
import { OrdableStoresServiceInterface } from '../stores/ordable-stores.service.interface';
import { OrdableFreeDeliveryServiceInterface } from './ordable-free-delivery.service.interface';

@Injectable()
export class OrdableFreeDeliveryService
  implements OrdableFreeDeliveryServiceInterface
{
  private readonly loggerService = new LoggerService(
    OrdableFreeDeliveryService.name,
  );

  private readonly PROMOTIONS_URI = '/api/promotions/';

  constructor(
    @Inject('OrdableHttpRequestsServiceInterface')
    private readonly ordableHttpRequestsService: OrdableHttpRequestsServiceInterface,
    @Inject(OrdableStoresServiceInterface)
    private readonly storeService: OrdableStoresServiceInterface,
    @Inject(CustomerReadServiceInterface)
    private readonly customerReadService: CustomerReadServiceInterface,
  ) {}

  public async initFreeDelivery(
    store: StoreDocument,
    customers: CustomerDocument[],
  ): Promise<void> {
    const customersWithFreeDelivery = customers.filter(
      (customer) => customer.loyaltyTier?.freeDelivery,
    );
    if (!customersWithFreeDelivery || customersWithFreeDelivery.length === 0)
      return;

    const hasOrdableId = (customer: CustomerDocument) =>
      customer.ordableStores?.[store._id.toHexString()]?.ordableId;
    const toOrdableId = hasOrdableId;
    await this.createFreeDeliveryPromotion(
      store,
      customersWithFreeDelivery.filter(hasOrdableId).map(toOrdableId),
    );
  }

  public async addFreeDelivery(customer: CustomerDocument): Promise<void> {
    await this.storeService.forEachStore(customer, async (store) => {
      await this.addFreeDeliveryForStore(store, customer);
    });
  }

  private async addFreeDeliveryForStore(
    store: StoreDocument,
    customer: CustomerDocument,
  ): Promise<void> {
    if (!customer?.ordableStores?.[store?._id?.toHexString()]?.ordableId)
      return this.loggerService.error(
        `Must create customer ${customer._id} on ordable before adding free delivery.`,
      );

    const ordableId = customer.ordableStores[store._id.toHexString()].ordableId;
    if (!store.ordableConfig?.freeDeliveryPromotionId)
      return await this.createFreeDeliveryPromotion(store, [ordableId]);

    const customerIds = store.ordableConfig.freeDeliveryPromotionCustomerIds;
    if (customerIds.includes(ordableId))
      return await this.ordableHttpRequestsService.logOrdableResponse(
        '[Ordable] Add Free Delivery',
        store.ordableConfig,
        { success: true, message: 'Customer already had free delivery' },
      );

    const newCustomerIds = [...customerIds, ordableId];
    const updateOrdablePromotionDto: Partial<OrdablePromotionDto> = {
      id: store.ordableConfig.freeDeliveryPromotionId,
      targeted_user: newCustomerIds,
    };

    const response: OrdablePromotionResponseDto =
      await this.ordableHttpRequestsService.createOrdablePatchRequest(
        `${store.apiBaseUrl}${this.PROMOTIONS_URI}${store.ordableConfig.freeDeliveryPromotionId}/`,
        store.apiKey,
        updateOrdablePromotionDto,
      );

    await this.ordableHttpRequestsService.logOrdableResponse(
      '[Ordable] Add Free Delivery',
      updateOrdablePromotionDto,
      response,
    );

    if (!response.success) return;

    await store.updateOne({
      'ordableConfig.freeDeliveryPromotionCustomerIds': newCustomerIds,
    });
  }

  public async removeFreeDelivery(customer: CustomerDocument): Promise<void> {
    await this.storeService.forEachStore(customer, async (store) => {
      await this.removeFreeDeliveryForStore(store, customer);
    });
  }

  private async removeFreeDeliveryForStore(
    store: StoreDocument,
    customer: CustomerDocument,
  ): Promise<void> {
    if (!customer?.ordableStores?.[store?._id?.toHexString()]?.ordableId)
      return this.loggerService.error(
        `Must create customer ${customer._id} on ordable before removing free delivery.`,
      );

    if (!store.ordableConfig?.freeDeliveryPromotionId)
      return await this.ordableHttpRequestsService.logOrdableResponse(
        '[Ordable] Remove Free Delivery',
        {},
        { success: true, message: 'No free delivery promotion' },
      );

    const ordableId = customer.ordableStores[store._id.toHexString()].ordableId;
    const customerIds = store.ordableConfig.freeDeliveryPromotionCustomerIds;
    if (!customerIds.includes(ordableId))
      return await this.ordableHttpRequestsService.logOrdableResponse(
        '[Ordable] Remove Free Delivery',
        { id: store.ordableConfig.freeDeliveryPromotionId, customerIds },
        { success: true, message: "Customer didn't have free delivery" },
      );

    const newCustomerIds = customerIds.filter((id) => id !== ordableId);

    if (newCustomerIds.length === 0)
      return this.deleteFreeDeliveryPromotion(store);

    const updateOrdablePromotionDto: Partial<OrdablePromotionDto> = {
      id: store.ordableConfig.freeDeliveryPromotionId,
      targeted_user: newCustomerIds,
    };

    const response: OrdablePromotionResponseDto =
      await this.ordableHttpRequestsService.createOrdablePatchRequest(
        `${store.apiBaseUrl}${this.PROMOTIONS_URI}${store.ordableConfig.freeDeliveryPromotionId}/`,
        store.apiKey,
        updateOrdablePromotionDto,
      );

    await this.ordableHttpRequestsService.logOrdableResponse(
      '[Ordable] Remove Free Delivery',
      updateOrdablePromotionDto,
      response,
    );

    if (!response.success) return;

    await store.updateOne({
      'ordableConfig.freeDeliveryPromotionCustomerIds': newCustomerIds,
    });
  }

  private async createFreeDeliveryPromotion(
    store: StoreDocument,
    customerOrdableIds: number[],
  ): Promise<void> {
    if (!customerOrdableIds || customerOrdableIds.length === 0) return;
    const freeDeliveryPromotionDto =
      this.getFreeDeliveryPromotionDto(customerOrdableIds);

    const response: OrdablePromotionResponseDto =
      await this.ordableHttpRequestsService.createOrdablePostRequest(
        `${store.apiBaseUrl}${this.PROMOTIONS_URI}`,
        store.apiKey,
        freeDeliveryPromotionDto,
      );

    await this.ordableHttpRequestsService.logOrdableResponse(
      '[Ordable] Create Free Delivery',
      freeDeliveryPromotionDto,
      response,
    );

    if (!response.success) return;

    await store.updateOne({
      'ordableConfig.freeDeliveryPromotionId': response.data.id,
      'ordableConfig.freeDeliveryPromotionCustomerIds': customerOrdableIds,
    });
  }

  private async deleteFreeDeliveryPromotion(
    store: StoreDocument,
  ): Promise<void> {
    const response: OrdablePromotionResponseDto =
      await this.ordableHttpRequestsService.createOrdableDeleteRequest(
        `${store.apiBaseUrl}${this.PROMOTIONS_URI}${store.ordableConfig.freeDeliveryPromotionId}/`,
        store.apiKey,
      );

    await this.ordableHttpRequestsService.logOrdableResponse(
      '[Ordable] Delete Free Delivery',
      { id: store.ordableConfig.freeDeliveryPromotionId },
      response,
    );

    if (!response.success) return;

    await store.updateOne({
      'ordableConfig.freeDeliveryPromotionId': null,
      'ordableConfig.freeDeliveryPromotionCustomerIds': null,
    });
  }

  private getFreeDeliveryPromotionDto(
    customerOrdableIds: number[],
  ): OrdablePromotionDto {
    return {
      name: 'Free Delivery',
      ar_name: 'توصيل مجاني',
      quantity: 999999,
      start: moment.utc().format('YYYY-MM-DD'),
      expiry: '2099-12-31',
      discount_type: OrdableDiscountType.DELIVERY,
      targeted_user: customerOrdableIds,
    };
  }

  public async handleFreeDeliveryRemoved(
    loyaltyTier: LoyaltyTierDocument,
  ): Promise<void> {
    if (!loyaltyTier || !loyaltyTier.ordableStores) return;

    const customers =
      await this.customerReadService.findEligibleForTierDiscount(loyaltyTier);
    if (!customers || customers.length === 0) return;

    await this.storeService.forEachStore(
      loyaltyTier,
      async (store: StoreDocument) => {
        await this.handleFreeDeliveryRemovedForStore(store, customers);
      },
    );
  }

  async handleFreeDeliveryRemovedForStore(
    store: StoreDocument,
    customers: CustomerDocument[],
  ) {
    const storeId: string = store._id.toHexString();
    const ordableIds = customers
      .filter((customer) => customer?.ordableStores?.[storeId]?.ordableId)
      .map((customer) => customer.ordableStores[storeId].ordableId);
    const targeted_user =
      store.ordableConfig.freeDeliveryPromotionCustomerIds.filter(
        (ordableId) => !ordableIds.includes(ordableId),
      );

    if (targeted_user.length === 0)
      return this.deleteFreeDeliveryPromotion(store);

    const updateOrdablePromotionDto: Partial<OrdablePromotionDto> = {
      id: store.ordableConfig.freeDeliveryPromotionId,
      targeted_user,
    };
    const response: OrdablePromotionResponseDto =
      await this.ordableHttpRequestsService.createOrdablePatchRequest(
        `${store.apiBaseUrl}${this.PROMOTIONS_URI}${store.ordableConfig.freeDeliveryPromotionId}/`,
        store.apiKey,
        updateOrdablePromotionDto,
      );

    await this.ordableHttpRequestsService.logOrdableResponse(
      '[Ordable] Remove Free Delivery',
      updateOrdablePromotionDto,
      response,
    );

    if (!response.success) return;

    await store.updateOne({
      'ordableConfig.freeDeliveryPromotionCustomerIds': targeted_user,
    });
  }

  public async handleFreeDeliveryAdded(
    loyaltyTier: LoyaltyTierDocument,
  ): Promise<void> {
    if (!loyaltyTier) return;

    const customers =
      await this.customerReadService.findEligibleForTierDiscount(loyaltyTier);
    if (!customers || customers.length === 0) return;

    await this.storeService.forEachStore(
      loyaltyTier,
      async (store: StoreDocument) => {
        await this.handleFreeDeliveryAddedForStore(store, customers);
      },
    );
  }

  private async handleFreeDeliveryAddedForStore(
    store: StoreDocument,
    customers: CustomerDocument[],
  ) {
    const storeId: string = store._id.toHexString();
    const ordableIds = customers
      .filter((customer) => customer?.ordableStores?.[storeId]?.ordableId)
      .map((customer) => customer.ordableStores[storeId].ordableId);

    if (!store.ordableConfig?.freeDeliveryPromotionId)
      return await this.createFreeDeliveryPromotion(store, ordableIds);

    const targeted_user = Array.from(
      new Set([
        ...store.ordableConfig.freeDeliveryPromotionCustomerIds,
        ...ordableIds,
      ]),
    );

    const response: OrdablePromotionResponseDto =
      await this.ordableHttpRequestsService.createOrdablePatchRequest(
        `${store.apiBaseUrl}${this.PROMOTIONS_URI}${store.ordableConfig.freeDeliveryPromotionId}/`,
        store.apiKey,
        { id: store.ordableConfig.freeDeliveryPromotionId, targeted_user },
      );

    await this.ordableHttpRequestsService.logOrdableResponse(
      '[Ordable] Add Free Delivery',
      { id: store.ordableConfig.freeDeliveryPromotionId, targeted_user },
      response,
    );

    if (!response.success) return;

    await store.updateOne({
      'ordableConfig.freeDeliveryPromotionCustomerIds': targeted_user,
    });
  }
}
