import {
  Branch,
  BranchDocument,
  CurrentUser,
  Customer,
  DeliverectUser,
  DeliveryMethod,
  LogError,
  LoggerService,
  MenuGroupToCreate,
  MenuWithId,
  OrderDeliveryAction,
  OrderDeliveryType,
  OrderDocument,
  OrderItemGroup,
  OrderLogActionEnum,
  OrderPaymentMethod,
  OrderTransitionTrigger,
  SavedLocation,
  SavedLocationType,
} from '@app/shared-stuff';
import { CompanyDocument } from '@app/shared-stuff/models/company.model';
import { HttpService } from '@nestjs/axios';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { BranchService } from '../../../../branch/services/branch/branch.service';
import { CompanyService } from '../../../../company/services/company/company.service';
import { CustomerReadServiceInterface } from '../../../../customer/modules/customer-read/customer-read.service.interface';
import { OrderLogServiceInterface } from '../../../../order/services/interfaces/order-log.service.interface';
import { OrderStatusService } from '../../../../order/services/order-status/order-status.service';
import { OrderService } from '../../../../order/services/order/order.service';
import { MenuCategoryService } from '../../../../restaurant/services/menu-category/menu-category.service';
import { MenuGroupService } from '../../../../restaurant/services/menu-group/menu-group.service';
import { MenuItemService } from '../../../../restaurant/services/menu-item/menu-item.service';
import { MenuService } from '../../../../restaurant/services/menu/menu.service';
import { deliverectOrderStatusMapping, menuTypeMapping } from '../constants';
import { DeliverectTableToGet } from '../dto/deliverect-table-to-get.dto';
import { DeliverectOrderType } from '../enums/deliverect-order-type.enum';
import { DeliverectPaymentType } from '../enums/deliverect-payment-types.enum';
import { OnEvent } from '@nestjs/event-emitter';

@Injectable()
export class DeliverectChannelService {
  deliverectToken = '';
  deliverectTokenExpire;
  deliverectBaseUrl = '';
  private readonly loggerService = new LoggerService(
    DeliverectChannelService.name,
  );

  constructor(
    private branchService: BranchService,
    private companyService: CompanyService,
    private menuCategoryService: MenuCategoryService,
    private menuItemService: MenuItemService,
    private menuService: MenuService,
    private menuGroupService: MenuGroupService,
    private configService: ConfigService,
    private orderService: OrderService,
    private http: HttpService,
    @Inject('OrderLogServiceInterface')
    private readonly orderLogService: OrderLogServiceInterface,
    @Inject(CustomerReadServiceInterface)
    private customerReadService: CustomerReadServiceInterface,
    private orderStatusService: OrderStatusService,
  ) {
    this.deliverectBaseUrl =
      this.configService.get('ENVIRONMENT') == 'development'
        ? this.configService.get('DELIVERECT_STAGING')
        : this.configService.get('DELIVERECT_PRODUCTION');
  }

  async onMenuPush(menuData: any, branchId: string) {
    for (let i = 0; i < menuData.length; i++) {
      await this.pushOneMenu(menuData[i], branchId);
    }
  }

  // Snooze Product Event
  async onProductSnoozed(items: any, locationId: string) {
    const plu = [];
    const issuePlu = [];
    for (let i = 0; i < items.length; i++) {
      const menuItem = await this.menuItemService.getDetails(items[i]['plu']);
      if (menuItem) {
        menuItem.available = false;
        await menuItem.save();
        plu.push(items[i]['plu']);
      } else {
        issuePlu.push(items[i]['plu']);
      }
    }

    return {
      results: [
        {
          action: 'snooze',
          data: {
            locationId: locationId,
            allSnoozedItems: plu,
          },
        },
      ],
    };
  }

  async onProductUnSnoozed(items: any, locationId: string) {
    const plu = [];
    const issuePlu = [];
    for (let i = 0; i < items.length; i++) {
      const menuItem = await this.menuItemService.getDetails(items[i]['plu']);
      if (menuItem) {
        menuItem.available = true;
        await menuItem.save();
        plu.push(items[i]['plu']);
      } else {
        issuePlu.push(items[i]['plu']);
      }
    }

    return {
      results: [
        {
          action: 'snooze',
          data: {
            locationId: locationId,
            allSnoozedItems: plu,
          },
        },
      ],
    };
  }

  // On Restaurant Busy
  async onRestaurantBusy(busyModeData: any) {
    await this.branchService.setIsRestaurantBusy(
      busyModeData.locationId,
      busyModeData.status === 'ONLINE',
    );
    return { status: 'ONLINE' };
  }

  async snoozeOrUnSnooze(data: any) {
    let snoozingData: any;
    const results = [];
    for (let i = 0; i < data['operations'].length; i++) {
      const currentOperation = data['operations'][i];
      if (currentOperation['action'] == 'snooze') {
        snoozingData = await this.onProductSnoozed(
          currentOperation['data']['items'],
          data['locationId'],
        );
      } else {
        snoozingData = await this.onProductUnSnoozed(
          currentOperation['data']['items'],
          data['locationId'],
        );
      }
      results.push(snoozingData);
    }
    return results;
  }

  // Channel status update [Location => Branch]
  async onChannelStatusUpdate(data: any) {
    const HOST = this.configService.get('DELIVERECT_CURRENT_HOST');
    const branch = await this.branchService.get_details(
      data['channelLocationId'],
    );
    if (!branch) {
      throw new BadRequestException({
        message: 'Please Provider Correct LocationId',
      });
    }

    await this.branchService.updateDeliverectChannelInfo(
      branch._id,
      data['channelLinkName'],
      data['channelLinkId'],
      data['locationId:'],
    );

    const company = await this.companyService.get_details(
      branch.company['_id'] ? branch.company['_id'] : branch.company,
    );

    await this.companyService.updatedCompanyDeliverectInfo(company._id);

    return {
      statusUpdateURL: HOST + '/orderStatusUpdate',
      menuUpdateURL: HOST + `/${branch._id.toString()}/menuPush`,
      snoozeUnsnoozeURL: HOST + `/${branch._id.toString()}/SnoozeOrUnSnooze`,
      busyModeURL: HOST + `/${branch._id.toString()}/restaurantBusy`,
      courierUpdateURL: HOST + '/orderStatusUpdate',
    };
  }

  async onOrderStatusUpdate(data: { channelOrderId: string; status: number }) {
    const order = await this.orderService.get_details(data.channelOrderId);
    if (!order) {
      return;
    }

    if (deliverectOrderStatusMapping.has(data.status)) {
      // Mark order as ready if status is 70
      if (data.status == 70) {
        order.is_ready = true;
        await order.save();
      }

      await this.orderStatusService.changeOrderStatus(
        order,
        deliverectOrderStatusMapping.get(data.status),
        OrderTransitionTrigger.DELIVERECT_STATUS_UPDATE,
        { name: 'Deliverect onOrderStatusUpdate' } as CurrentUser,
      );
    } else {
      this.loggerService.warn(
        `Deliverect order status not found: ${data.status}`,
        order,
      );
    }

    return 'OK';
  }

  // Deliverect API
  async getTables(tablesToGet: DeliverectTableToGet) {
    return new Promise(async (resolve, reject) => {
      const branch = await this.branchService.get_details(tablesToGet.branch);
      if (branch.deliverectLocationId) {
        this.http
          .get(
            this.configService.get('DELIVERICT_STAGING') +
              'tables/' +
              branch.deliverectLocationId,
          )
          .subscribe(
            (data) => {
              resolve(data['data']);
            },
            (err) => {
              reject(err);
            },
          );
      } else {
        reject();
      }
    });
  }

  // Order Dispatch API
  async dispatchOrderToDeliverect(
    order: OrderDocument,
    company: CompanyDocument,
    branch: Branch,
    customer: Customer,
    deliveryAddress: SavedLocation,
    currentUser: any,
  ) {
    if (
      order.deliverectOrder ||
      order.dispatchedToDeliverectChannel ||
      order.deliverectPosOrderId
    ) {
      return;
    }

    //if the order is cash will be fired but if it's online we need to wait until the payment is paid
    const deliverectOrderToCreate = {
      channelOrderId: order.code,
      channelOrderDisplayId: order.code,
      channelLinkId: branch.deliverectChannelId,
      channel: 0,
      by:
        order.deliveryMethod == DeliveryMethod.E_BUTLER
          ? 'EButler'
          : order.company['name'],
      orderType:
        order.delivery_action == OrderDeliveryAction.IN_STORE_PICKUP
          ? DeliverectOrderType.eatIn
          : DeliverectOrderType.delivery,
      pickupTime: order.pickup_date,
      estimatedPickupTime: order.pickup_date,
      deliveryTime: order.delivery_date,
      deliveryIsAsap: order.delivery_type == OrderDeliveryType.urgent,
      courier: 'EButler',
      customer: {
        name: order.is_gift ? order.recipient_name : order.customer_name,
        companyName: company.name,
        phoneNumber: order.is_gift
          ? order.recipient_country_code + order.recipient_phone
          : customer.country_code + customer.phone,
        email: customer.email,
      },
      orderIsAlreadyPaid: order.payment_method == OrderPaymentMethod.online,
      payment: {
        amount: order.total_amount * 100,
        type:
          order.payment_method == OrderPaymentMethod.cash
            ? DeliverectPaymentType.cash
            : DeliverectPaymentType.paidOnline,
      },
      note: order.order_remarks,
      deliveryCost: order.delivery_amount * 100,
      items: [],
      decimalDigits: 2,
    };

    if (deliveryAddress) {
      deliverectOrderToCreate['deliveryAddress'] = {
        street: deliveryAddress.streetName,
        streetNumber: deliveryAddress.streetNumber,
        postalCode: '',
        city: deliveryAddress.city,
        extraAddressInfo:
          deliveryAddress.type == SavedLocationType.NATIONAL_ADDRESS
            ? `Street: ${
                deliveryAddress.streetNumber ?? 'NOT FOUND'
              }, Building: ${
                deliveryAddress.buildingNumber ?? 'NOT FOUND'
              }, Zone: ${deliveryAddress.zoneNumber ?? 'NOT FOUND'}`
            : `https://maps.google.com/?q=${deliveryAddress.latitude},${deliveryAddress.longitude}`,
      };
    }

    for (let i = 0; i < order.items.length; i++) {
      const currentItem = order.items[i];
      const pushedItem = {
        plu: currentItem.plu,
        name: currentItem.name,
        price: currentItem.basePrice * 100,
        quantity: currentItem.quantity || 1,
        productType: 1,
        remark: currentItem.special_instructions,
        subItems: [],
      };

      // Adding the sub - products to deliverect order
      if (currentItem.subProducts && currentItem.subProducts.length) {
        currentItem.subProducts.map((product) => {
          const pushedProduct = {
            plu: product.plu,
            name: product.name,
            price: product.basePrice * 100,
            quantity: product.quantity,
            productType: 1,
            remark: product.special_instructions,
            subItems: [],
          };
          if (product.modifierGroups) {
            product.modifierGroups.map((group) => {
              const pushedGroup = this.getDeliverectOrderGroup(group);
              pushedProduct.subItems.push(...pushedGroup.subItems);
            });
          }

          // Push The Product Sub Items
          if (product.subProducts) {
            product.subProducts.map((subProduct) => {
              const pushedSubProduct = {
                plu: subProduct.plu,
                name: subProduct.name,
                price: subProduct.basePrice * 100,
                quantity: subProduct.quantity,
                productType: 1,
                remark: subProduct.special_instructions,
                subItems: [],
              };
              if (subProduct.modifierGroups) {
                subProduct.modifierGroups.map((group) => {
                  const pushedGroup = this.getDeliverectOrderGroup(group);
                  pushedSubProduct.subItems.push(...pushedGroup.subItems);
                });
              }
              pushedProduct.subItems.push(pushedSubProduct);
            });
          }
          pushedItem.subItems.push(pushedProduct);
        });
      }

      // Adding modifier Group to the deliverect order
      for (let j = 0; j < currentItem.modifierGroups.length; j++) {
        const currentGroup = currentItem.modifierGroups[j];
        const pushedGroup = this.getDeliverectOrderGroup(currentGroup);
        pushedItem.subItems.push(...pushedGroup.subItems);
      }
      deliverectOrderToCreate.items.push(pushedItem);
    }

    const validItems = this.checkItemsPlu(deliverectOrderToCreate.items);
    if (validItems) {
      const response = await this.performDispatchOrderRequest(
        deliverectOrderToCreate,
        branch.deliverectChannelName,
        branch.deliverectChannelId,
        company,
      );
      this.loggerService.log(
        'Perform Dispatch Order Request',
        deliverectOrderToCreate,
        response,
      );
      await this.orderLogService.saveOrderLog(
        order,
        { requestedObject: deliverectOrderToCreate },
        { responseObject: JSON.stringify(response) },
        OrderLogActionEnum.ON_ORDER_SENT_TO_DELIVERECT_FROM_CHANNEL,
        currentUser,
      );
      return response;
    }
    order.hasIssueWithDeliverect = true;
    await order.save();
    this.loggerService.log(
      '`Order with code ${order.code} has an issue with deliverect channel integration',
      'Perform Dispatch Order Request',
      deliverectOrderToCreate,
    );
    await this.orderLogService.saveOrderLog(
      order,
      { requestedObject: deliverectOrderToCreate },
      {
        responseObject: {
          message: `Order with code ${order.code} has an issue with deliverect channel integration`,
        },
      },
      OrderLogActionEnum.ON_ORDER_SENT_TO_DELIVERECT_FROM_CHANNEL,
      currentUser,
    );
    return 'ISSUE WITH DELIVERECT';
  }

  // order.rawResponse = response as any;

  public async getDeliverectAuthenticatedToken(company: CompanyDocument) {
    if (
      !this.deliverectToken ||
      moment().isBefore(this.deliverectTokenExpire)
    ) {
      if (
        !company.deliverectChannelClientId ||
        !company.deliverectChannelSecret
      ) {
        return null;
      }
      const devResponse = await this.performAuthenticationRequest(
        company.deliverectChannelClientId,
        company.deliverectChannelSecret,
      );
      this.deliverectToken = devResponse['access_token'];
      this.deliverectTokenExpire = moment().add(
        devResponse['expires_in'],
        'seconds',
      );
    }
    return this.deliverectToken;
  }

  public async performAuthenticationRequest(
    clientId: string,
    clientSecret: string,
  ) {
    return new Promise((resolve) => {
      this.http
        .post(this.deliverectBaseUrl + 'oauth/token', {
          audience: 'https://api.staging.deliverect.com',
          grant_type: 'token',
          client_id: clientId,
          client_secret: clientSecret,
        })
        .subscribe({
          next: (data) => resolve(data.data),
          error: (err) => resolve({ status: 'error', message: err }),
        });
    });
  }

  @OnEvent('cron.every1min')
  @LogError()
  async dispatchDeliverectOrders() {
    const ordersCanBeDispatched =
      await this.orderService.getOrdersCanBeDispatchedToDeliverect();
    for (let i = 0; i < ordersCanBeDispatched.length; i++) {
      const order = ordersCanBeDispatched[i];
      const { company, branch, customer, deliveryLocation } =
        await this.fetchOrderRelatedItems(order);
      const response = await this.dispatchOrderToDeliverect(
        order,
        company,
        branch as any,
        customer,
        deliveryLocation,
        {},
      );
      if (response && response['status'] != 'error') {
        order.dispatchedToDeliverectChannel = true;
        order.deliverectOrder = true;
        await order.save();
      }
    }
  }

  private async pushOneMenu(menuData: any, branchId: string) {
    if (!menuData['snoozedProducts']) {
      menuData['snoozedProducts'] = {};
    }

    //Getting menuId
    const deliverectMenuId = menuData['menuId'];

    const branch = await this.branchService.get_details(branchId);

    if (!branch) {
      throw new BadRequestException({
        message: 'Please Provide A Correct Branch Id',
      });
    }

    const company = await this.companyService.get_details(
      branch.company['_id'] ? branch.company['_id'] : branch.company,
    );

    //Fetch Brand
    //let brand = await this.bra

    if (!company || !deliverectMenuId) {
      return;
    }

    // Fetching the menu
    const menu = await this.createMenu(
      menuData['menu'],
      menuData['description'],
      deliverectMenuId,
      menuTypeMapping[menuData['menuType']],
      company,
      branch as any,
      menuData,
    );

    menu.name = menuData['menu'];
    menu.type = menuTypeMapping[menuData['menuType']];
    await menu.save();

    // Handle The Product Sorting
    let index = 0;
    for (const key in menuData['products']) {
      menuData['products'][key]['sortOrder'] = ++index;
    }
    for (const key in menuData['bundles']) {
      menuData['bundles'][key]['sortOrder'] = ++index;
    }

    // Products Scenario
    for (const key in menuData['products']) {
      const currentProduct = menuData['products'][key];
      const item = await this.createMenuItem(
        currentProduct,
        menu,
        true,
        menuData['snoozedProducts'][currentProduct['_id']] ? false : true,
      );
      const subItems = new Set([]);
      const menuGroups = new Set([]);
      if (
        currentProduct['subProducts'] &&
        currentProduct['subProducts'].length
      ) {
        for (let i = 0; i < currentProduct['subProducts'].length; i++) {
          const currentSubProduct = currentProduct['subProducts'][i];
          if (menuData['products'][currentSubProduct]) {
            // Sub Product is Product
            const subItem = await this.createMenuItem(
              menuData['products'][currentSubProduct],
              menu,
              true,
              menuData['snoozedProducts'][currentSubProduct] ? false : true,
            );
            subItems.add(new Types.ObjectId(subItem._id));
          } else if (menuData['modifierGroups'][currentSubProduct]) {
            //sub-product is modifier group
            const group = await this.createMenuGroup(
              menuData['modifierGroups'][currentSubProduct],
              menuData['modifiers'],
            );
            menuGroups.add(new Types.ObjectId(group['_id']));
          } else if (menuData['bundles'][currentSubProduct]) {
            // sub product is a bundle
            const bundle = await this.createBundle(
              menuData,
              menuData['bundles'][currentSubProduct],
              menu,
            );
            subItems.add(new Types.ObjectId(bundle._id));
          }
        }
      }
      item.menuGroups = Array.from(menuGroups);
      item.subItems = Array.from(subItems);
      await item.save();
    }

    // Categories Scenario
    for (let i = 0; i < menuData['categories'].length; i++) {
      const currentCategory = menuData['categories'][i];
      let category = await this.menuCategoryService.getDetails(
        currentCategory['_id'],
      );
      if (!category) {
        category = await this.createCategory(
          currentCategory.name,
          currentCategory.description,
          currentCategory._id,
          currentCategory['imageUrl'],
          i + 1,
          menu,
          currentCategory,
        );
      }
      category.name = currentCategory.name;
      category.description = currentCategory.description;
      category.menu = new Types.ObjectId(menu._id);
      category.menuName = menu.name;
      await category.save();
      // Products inside the category Scenario
      if (currentCategory.products && currentCategory.products.length) {
        for (let j = 0; j < currentCategory.products.length; j++) {
          // const item = await this.menuItemService.getDetails(
          //   currentCategory.products[j],
          // );
          // if (item) {
          //   item.menuCategory = category._id;
          //   await item.save();
          // }
          await this.menuItemService.updateCategoryIdForManyDeliverect(
            currentCategory.products,
            category._id,
            menu._id,
          );
        }
      }
    }
  }

  private async createMenu(
    name,
    description,
    menuId,
    menuType,
    company: CompanyDocument,
    branch: BranchDocument,
    raw: Record<string, any>,
  ) {
    const branchId = branch ? branch._id : undefined;

    let menu = await this.menuService.getDetails(menuId);

    if (menu) {
      await this.menuItemService.deleteItemInsideMenu(menu);
      await this.menuCategoryService.deleteAllCategoryInsideMenu(menu);
      await this.menuService.deleteMenu(menu);
    }
    menu = await this.menuService.create({
      company: company._id,
      branches: [branchId],
      name: name,
      deliverectId: menuId,
      description: description,
      type: menuType,
      branchesName: '',
      brand: branch ? branch.brands[0] : undefined,
      companyName: company.name,
      internalName: name,
      currentUser: DeliverectUser,
      reference: '',
      deliverectRaw: {},
      availabilities: raw['availabilities'],
      pdfURL: '',
    });

    return menu;
  }

  private async createCategory(
    name,
    description,
    deliverectId,
    imageUrl: string,
    sortOrder: number,
    menu: MenuWithId,
    raw: Record<string, any>,
  ) {
    let category = await this.menuCategoryService.getDetails(deliverectId);
    if (!category) {
      category = await this.menuCategoryService.create({
        menu: menu._id,
        name: name,
        description: description,
        deliverectId: deliverectId,
        menuName: menu.name,
        reference: deliverectId,
        image: null,
        createdBy: DeliverectUser,
        deliverectRaw: raw,
        plu: '',
        externalImage: imageUrl,
        sortOrder,
        integrationInfo: {
          ordableInfo: {},
        },
      });
    } else {
      await category.updateOne({ name, description, externalImage: imageUrl });
    }
    return category;
  }

  private async createMenuItem(
    currentProduct: any,
    menu: MenuWithId,
    showOnPOS: boolean,
    available = true,
  ) {
    let item = await this.menuItemService.getDetails(
      currentProduct['_id'],
      menu._id.toString(),
    );

    if (!item) {
      item = await this.menuItemService.create({
        nameAr: '',
        nameEn: currentProduct['name'],
        descriptionAr: '',
        descriptionEn: currentProduct['description'],
        price: currentProduct['price']
          ? parseFloat(currentProduct['price']) / 100
          : 0,
        sortOrder: currentProduct['sortOrder'],
        deliveryTax: currentProduct['deliveryTax'] ?? 0,
        takeawayTax: currentProduct['takeawayTax'] ?? 0,
        max: currentProduct['max'],
        min: currentProduct['min'],
        available: available,
        calories: 0,
        showInPos: showOnPOS,
        showOnWeb: true,
        multiSelectMax: 1,
        multiply: 1,
        code: Math.random().toString(),
        adlerId: '',
        type:
          currentProduct['productType'] == 1
            ? currentProduct['isCombo']
              ? 'combo'
              : 'regular'
            : 'bundle',
        createdBy: { name: 'deliverect api' },
        menu: menu._id,
        menuCategory: null,
        tags: this.getMenuItemTags(currentProduct['productTags']),
        images: [],
        subItems: [],
        menuGroups: [],
        reference: '',
        plu: currentProduct['plu'],
        deliverectRaw: currentProduct,
        deliverectId: currentProduct['_id'],
        company: menu.company['_id']
          ? menu.company['_id'].toHexString()
          : menu.company.toHexString(),
        externalImage: currentProduct['imageUrl'],
        forGroupingOnly: false,
        availabilities: [],
        isScheduledAvailabilityActive: false,
        integrationInfo: {
          ordableInfo: {},
        },
        externalBrandId: '',
      });
    }
    return item;
  }

  private async createBundle(
    menuData: any,
    currentBundle: any,
    menu: MenuWithId,
  ) {
    const item = await this.createMenuItem(
      currentBundle,
      menu,
      false,
      menuData['snoozedProducts'][currentBundle['_id']] ? false : true,
    );
    if (currentBundle.subProducts && currentBundle.subProducts.length) {
      const subItems = new Set<Types.ObjectId>();
      for (let i = 0; i < currentBundle.subProducts.length; i++) {
        const currentProduct =
          menuData['products'][currentBundle.subProducts[i]];
        const subProduct = await this.createMenuItem(
          currentProduct,
          menu,
          false,
          menuData['snoozedProducts'][currentProduct['_id']] ? false : true,
        );
        if (subProduct) {
          subItems.add(new Types.ObjectId(subProduct._id));
        }
      }
      item.subItems = [...subItems];
    }
    await item.save();
    return item;
  }

  private getMenuItemTags(tags: any[]) {
    return tags;
  }

  private async createMenuGroup(modifierGroup: any, modifiers: any) {
    let menuGroup = await this.menuGroupService.getDetails(
      modifierGroup['_id'],
    );

    if (!menuGroup) {
      const menuGroupToCreate: MenuGroupToCreate = {
        nameEn: modifierGroup['name'],
        descriptionEn: modifierGroup['description'],
        descriptionAr: '',
        nameAr: '',
        index: '0',
        max: modifierGroup['max'],
        min: modifierGroup['min'],
        multiply: modifierGroup['multiply'],
        totalPrice: modifierGroup['price'] ?? 0,
        multiSelectMax: 1,
        createdBy: DeliverectUser,
        items: [],
        reference: modifierGroup['_id'],
        deliverectId: modifierGroup['_id'],
        plu: modifierGroup['plu'],
        integrationInfo: {
          ordableInfo: {},
        },
      };
      if (modifierGroup['subProducts']) {
        for (let i = 0; i < modifierGroup['subProducts'].length; i++) {
          const currentModifier = modifiers[modifierGroup['subProducts'][i]];
          menuGroupToCreate.items.push({
            name: currentModifier['name'],
            price: currentModifier['price'] / 100,
            index: (i + 1).toString(),
            deliverectRawId: currentModifier['_id'],
            plu: currentModifier['plu'],
            integrationInfo: {
              ordableInfo: {},
            },
          });
        }
      }
      menuGroup = await this.menuGroupService.create(menuGroupToCreate);
    } else {
      menuGroup.nameEn = modifierGroup['name'];
      menuGroup.descriptionEn = modifierGroup['description'];
      menuGroup.max = modifierGroup['max'];
      menuGroup.min = modifierGroup['min'];
      menuGroup.multiply = modifierGroup['multiply'];
      await menuGroup.save();
    }

    return menuGroup;
  }

  private getDeliverectOrderGroup(currentItemGroup: OrderItemGroup) {
    const pushedGroup = {
      plu: currentItemGroup.plu,
      name: currentItemGroup.name,
      price: currentItemGroup.price || 0,
      quantity: currentItemGroup.quantity || 1,
      productType: 3,
      remark: '',
      subItems: [],
    };
    if (currentItemGroup.modifiers) {
      currentItemGroup.modifiers.map((x) => {
        pushedGroup.subItems.push({
          plu: x.plu,
          name: x.name,
          price: x.price * 100,
          quantity: x.quantity,
          productType: 2,
          remark: '',
          subItems: [],
        });
      });
    }
    return pushedGroup;
  }

  private async performDispatchOrderRequest(
    orderToCreate: any,
    channelName: string,
    channelLinkId: string,
    company: CompanyDocument,
  ) {
    return new Promise(async (resolve) => {
      const token = await this.getDeliverectAuthenticatedToken(company);
      this.loggerService.log('perform dispatch order request', {
        orderToCreate,
        token,
        channelName,
        channelLinkId,
      });
      if (!token) return;
      const URL =
        this.deliverectBaseUrl +
        this.configService.get('DELIVERECT_CHANNEL_SCOPE') +
        '/order/' +
        channelLinkId;
      this.http
        .post(URL, orderToCreate, {
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
        })
        .subscribe({
          next: (data) => {
            resolve(data.data);
          },
          error: (err) => {
            this.loggerService.log(
              'Error Ocurred when Perform Dispatch Order Request with message: ' +
                err.message,
              err.stacktrace,
              orderToCreate,
            );
            resolve({ status: 'error', message: JSON.stringify(err) });
          },
        });
    });
  }

  private async fetchOrderRelatedItems(order: OrderDocument) {
    const company = await this.companyService.get_details(
      order['company']['_id'] ? order['company']['_id'] : order.company,
    );
    const branch = await this.branchService.get_details(
      typeof order.branch !== 'string'
        ? order.branch?._id?.toString()
        : order.branch,
    );
    const customer = await this.customerReadService.findOne(
      order.customer.toHexString(),
      company._id,
    );
    const deliveryLocation = order.deliveryLocation;

    return { company, branch, customer, deliveryLocation };
  }

  private checkItemsPlu(items: any[]): boolean {
    let res = true;
    for (let i = 0; i < items.length; i++) {
      if (!items[i].plu) {
        res = false;
      }
      if (items[i].subItems && items[i].subItems.length) {
        res = this.checkSubItemsPlu(items[i].subItems) && res;
      }
    }
    return res;
  }

  private checkSubItemsPlu(items: any[]) {
    for (let i = 0; i < items.length; i++) {
      if (!items[i].plu) {
        return false;
      }
    }
    return true;
  }
}
