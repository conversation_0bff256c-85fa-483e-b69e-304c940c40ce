import {
  CountryDialCode,
  CustomerDocument,
  fillAnonymousTriggerUserDto,
  GenericTriggerModel,
  LanguageCode,
  LanguageToLanguageCode,
  LoggerService,
  LoyaltyCustomerReplacementsDto,
  LoyaltyStatus,
  TriggerAction,
  TriggerModule,
} from '@app/shared-stuff';
import { BrandDocument } from '@app/shared-stuff/models/brand.model';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Types } from 'mongoose';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { CompanyService } from '../../../company/services/company/company.service';
import { TriggerService } from '../../../notification/services/trigger/trigger.service';
import { CustomerReplacementsServiceInterface } from '../customer-replacements/customer-replacements.service.interface';
import { CustomerNotificationServiceInterface } from './customer-notification.service.interface';

@Injectable()
export class CustomerNotificationService
  implements CustomerNotificationServiceInterface
{
  private readonly loggerService = new LoggerService(
    CustomerNotificationService.name,
  );

  constructor(
    private readonly triggerService: TriggerService,
    private readonly companyService: CompanyService,
    private readonly eventEmitter: EventEmitter2,
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandServiceInterface,
    @Inject(CustomerReplacementsServiceInterface)
    private readonly customerReplacementsService: CustomerReplacementsServiceInterface,
  ) {}

  async fireOnSendOrdableLinkTrigger(
    customer: CustomerDocument,
    brandId: Types.ObjectId,
  ): Promise<void> {
    const replacements =
      await this.customerReplacementsService.getCustomerReplacements(customer, {
        brandId,
      });
    if (!replacements.ordableLink) {
      throw new BadRequestException(
        `Customer ${customer._id} does not have an ordable link. Ensure that an ordable store exists for this brand.`,
      );
    }
    await this.fireTrigger(
      customer,
      replacements,
      TriggerAction.SEND_ORDABLE_LINK,
    );
  }

  async fireOnLoyaltyProgramEnrollmentTrigger(
    customer: CustomerDocument,
  ): Promise<void> {
    await this.fireTrigger(
      customer,
      await this.customerReplacementsService.getCustomerReplacements(customer),
      TriggerAction.ON_LOYALTY_PROGRAM_ENROLLMENT,
    );
  }

  async fireOnCouponRedeemedTrigger(customer: CustomerDocument): Promise<void> {
    await this.fireTrigger(
      customer,
      await this.customerReplacementsService.getCustomerReplacements(customer),
      TriggerAction.ON_COUPON_REDEEMED,
      true,
    );
  }

  async fireOnTierUpgradeTrigger(
    customer: CustomerDocument,
    replacements: LoyaltyCustomerReplacementsDto,
    brandId?: Types.ObjectId,
  ): Promise<void> {
    await this.fireTrigger(
      customer,
      replacements,
      TriggerAction.ON_TIER_UPGRADE,
      true,
      brandId,
    );
  }

  async fireOnTierDowngradeTrigger(
    customer: CustomerDocument,
    replacements: LoyaltyCustomerReplacementsDto,
  ): Promise<void> {
    await this.fireTrigger(
      customer,
      replacements,
      TriggerAction.ON_TIER_DOWNGRADE,
      true,
    );
  }

  async fireOnTierRemoveTrigger(
    customer: CustomerDocument,
    replacements: LoyaltyCustomerReplacementsDto,
  ): Promise<void> {
    await this.fireTrigger(
      customer,
      replacements,
      TriggerAction.ON_TIER_REMOVE,
      true,
    );
  }

  async fireOnTierCalendarSystemUpdateTrigger(
    customer: CustomerDocument,
  ): Promise<void> {
    await this.fireTrigger(
      customer,
      await this.customerReplacementsService.getCustomerReplacements(customer),
      TriggerAction.ON_TIERS_CALENDAR_SYSTEM_UPDATE,
      true,
    );
  }

  async fireOnTierManualAssignmentTrigger(
    customer: CustomerDocument,
    replacements?: LoyaltyCustomerReplacementsDto,
  ): Promise<void> {
    await this.fireTrigger(
      customer,
      replacements ??
        (await this.customerReplacementsService.getCustomerReplacements(
          customer,
        )),
      TriggerAction.ON_TIER_MANUAL_ASSIGNMENT,
    );
  }

  async fireOnBaseTierAssignmentTrigger(
    customer: CustomerDocument,
    replacements: LoyaltyCustomerReplacementsDto,
    brandId: Types.ObjectId,
  ): Promise<void> {
    await this.fireTrigger(
      customer,
      replacements,
      TriggerAction.ON_BASE_TIER_ASSIGNMENT,
      false,
      brandId,
    );
  }

  async fireOnLoyaltyProgramRegistrationTrigger(
    customer: CustomerDocument,
    brandId: Types.ObjectId,
  ): Promise<void> {
    await this.fireTrigger(
      customer,
      await this.customerReplacementsService.getCustomerReplacements(customer, {
        brandId,
      }),
      TriggerAction.ON_LOYALTY_PROGRAM_REGISTRATION,
      false,
      brandId,
    );
  }

  async fireOnManualLoyaltyRegistration(
    customer: CustomerDocument,
    brandId: Types.ObjectId,
  ): Promise<void> {
    await this.fireTrigger(
      customer,
      await this.customerReplacementsService.getCustomerReplacements(customer, {
        brandId,
      }),
      TriggerAction.ON_LOYALTY_PROGRAM_MANUAL_REGISTRATION,
      false,
      brandId,
    );
  }

  async fireLoyaltyCardStatusOrRegistrationPromotion(
    customer: CustomerDocument,
    brandId: Types.ObjectId,
    triggerAction:
      | TriggerAction.ON_SEND_LOYALTY_CARD
      | TriggerAction.ON_SEND_LOYALTY_REGISTRATION,
    branchId?: Types.ObjectId,
  ): Promise<void> {
    await this.fireTrigger(
      customer,
      await this.customerReplacementsService.getCustomerReplacements(customer, {
        brandId,
        branchId,
      }),
      triggerAction,
      false,
      brandId,
    );
  }

  async fireLoyaltyCardStatusOrRegistrationPromotionByPhone(
    brand: BrandDocument,
    phone: string,
    countryCode: CountryDialCode,
    triggerAction:
      | TriggerAction.ON_SEND_LOYALTY_CARD
      | TriggerAction.ON_SEND_LOYALTY_REGISTRATION,
    loyaltyRegistrationPageLink?: string,
  ): Promise<void> {
    const genericTriggerModel = this.fillAnonymousGenericTriggerModel(
      brand,
      { loyaltyRegistrationPageLink },
      countryCode,
    );
    const returnedDto = await this.triggerService.fireTrigger(
      genericTriggerModel,
      triggerAction,
      [fillAnonymousTriggerUserDto(phone, countryCode)],
    );
    if (triggerAction == TriggerAction.ON_SEND_LOYALTY_REGISTRATION) {
      this.loggerService.log(
        `Sent registration link ${loyaltyRegistrationPageLink} to phone number ${countryCode}${phone}`,
        { returnedDto },
      );
    }
  }

  async fireOnWalletPassAdded(customer: CustomerDocument): Promise<void> {
    await this.fireTrigger(
      customer,
      await this.customerReplacementsService.getCustomerReplacements(customer),
      TriggerAction.ON_WALLET_PASS_ADDED,
      false,
      undefined,
      TriggerModule.PASS,
    );
  }

  async fireOnTierDowngradeWarningTrigger(
    customer: CustomerDocument,
    isThresholdMet: boolean,
  ): Promise<void> {
    await this.fireTrigger(
      customer,
      await this.customerReplacementsService.getCustomerReplacements(customer),
      isThresholdMet
        ? TriggerAction.ON_TIER_DOWNGRADE_WARNING_EXCEEDED_THRESHOLD
        : TriggerAction.ON_TIER_DOWNGRADE_WARNING_BELOW_THRESHOLD,
      true,
    );
  }

  async fireOnFirstGracePeriodReminderTrigger(
    customer: CustomerDocument,
  ): Promise<void> {
    await this.fireTrigger(
      customer,
      await this.customerReplacementsService.getCustomerReplacements(customer),
      customer.loyaltyTier
        ? TriggerAction.ON_FIRST_GRACE_PERIOD_REMINDER_CURRENT_TIER
        : TriggerAction.ON_FIRST_GRACE_PERIOD_REMINDER_UPPER_TIER,
      true,
    );
    this.eventEmitter.emit('customer.firstGracePeriodReminder.sent', customer);
  }

  async fireOnComputationCycleDay(customer: CustomerDocument): Promise<void> {
    await this.fireTrigger(
      customer,
      await this.customerReplacementsService.getCustomerReplacements(customer),
      TriggerAction.ON_COMPUTATION_CYCLE_DAY,
      true,
    );
  }

  private async fireTrigger(
    customer: CustomerDocument,
    replacements: LoyaltyCustomerReplacementsDto,
    triggerAction: TriggerAction,
    isAutomatedTrigger = false,
    brandId?: Types.ObjectId,
    module: TriggerModule = TriggerModule.CUSTOMER,
  ): Promise<void> {
    const isLoyaltyMember = customer.loyaltyStatus === LoyaltyStatus.MEMBER;
    if (isAutomatedTrigger && !isLoyaltyMember) {
      return this.loggerService.warn(
        `Cannot fire automated trigger for ${customer.loyaltyStatus} customer`,
      );
    }
    const genericTriggerModel = await this.fillGenericTriggerModel(
      customer,
      replacements,
      module,
      brandId,
    );
    const returnedDto = await this.triggerService.fireTrigger(
      genericTriggerModel,
      triggerAction,
    );
    this.loggerService.log(`${triggerAction} notification sent`, {
      customer,
      genericTriggerModel,
      returnedDto,
    });
  }

  private async fillGenericTriggerModel(
    customer: CustomerDocument,
    replacements: LoyaltyCustomerReplacementsDto,
    module: TriggerModule = TriggerModule.CUSTOMER,
    brandId?: Types.ObjectId,
  ): Promise<GenericTriggerModel> {
    const company = await this.companyService.findById(customer.company);
    const branchId =
      customer.firstBranchOrderd?._id instanceof Types.ObjectId
        ? customer.firstBranchOrderd._id.toHexString()
        : customer.firstBranchOrderd?._id;

    return {
      companyId: company._id.toHexString(),
      branchId: branchId,
      brandId: customer.activeBrand?._id?.toHexString(),
      customerId: customer._id,
      countryCode: customer.country_code,
      createdBy: undefined,
      senderId: customer.activeBrand?.senderId ?? company.senderId,
      emailSenderId: customer.activeBrand?.emailSenderId,
      giftRecipientUser: undefined,
      isGift: false,
      language: LanguageToLanguageCode[customer.language],
      triggerModule: module,
      replacements,
      context: {
        customer: { ...customer.toJSON(), _id: customer._id },
      },
      ...(await this.getBrandDetails(brandId)),
    };
  }

  private async getBrandDetails(
    brandId?: Types.ObjectId,
  ): Promise<Partial<GenericTriggerModel>> {
    if (!brandId) return {};
    const brand = await this.brandService.findById(brandId);
    return {
      brandId: brand._id.toHexString(),
      senderId: brand.senderId,
      emailSenderId: brand.emailSenderId,
    };
  }

  private fillAnonymousGenericTriggerModel(
    brand: BrandDocument,
    replacements: Record<string, string>,
    countryCode?: CountryDialCode,
  ): GenericTriggerModel {
    return {
      companyId: brand.companyId.toHexString(),
      branchId: undefined,
      brandId: brand?._id.toHexString(),
      customerId: undefined,
      countryCode,
      createdBy: undefined,
      senderId: brand.senderId,
      emailSenderId: brand.emailSenderId,
      giftRecipientUser: undefined,
      isGift: false,
      language: LanguageCode.en,
      triggerModule: TriggerModule.CUSTOMER,
      replacements,
      context: {},
    };
  }
}
