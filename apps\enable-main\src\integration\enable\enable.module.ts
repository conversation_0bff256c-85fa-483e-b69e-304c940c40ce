import {
  CollectionName,
  CustomCacheModule,
  ExternalOrderSchema,
  SharedStuffModule,
} from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { ThirdPartyLogisticModule } from '../../3pl/third-party-logistic.module';
import { BranchModule } from '../../branch/branch.module';
import { BrandModule } from '../../brand/brand.module';
import { CompanyModule } from '../../company/company.module';
import { CouponModule } from '../../coupon/coupon.module';
import { CustomerLoyaltyMemberModule } from '../../customer/modules/customer-loyalty-member/customer-loyalty-member.module';
import { CustomerPassLinkModule } from '../../customer/modules/customer-pass-link/customer-pass-link.module';
import { CustomerPassModule } from '../../customer/modules/customer-pass/customer-pass.module';
import { CustomerReadModule } from '../../customer/modules/customer-read/customer-read.module';
import { CustomerWebstoreModule } from '../../customer/modules/customer-webstore/customer-webstore.module';
import { CustomerWriteModule } from '../../customer/modules/customer-write/customer-write.module';
import { LoyaltyTierReadModule } from '../../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.module';
import { OrderInvoiceModule } from '../../order/modules/order-invoice/order-invoice.module';
import { OrderModule } from '../../order/order.module';
import { PaymentModule } from '../../payment/payment.module';
import { RestaurantModule } from '../../restaurant/restaurant.module';
import { SharedModule } from '../../shared/shared.module';
import { UserModule } from '../../user/user.module';
import { MicrosModule } from '../channel/micros/micros.module';
import { IntegrationLogModule } from '../integration-log/integration-log.module';
import { ShortenUrlModule } from '../shorten-url/shorten-url.module';
import { ShopifyModule } from '../webstore/shopify/shopify.module';
import { EnableController } from './controllers/enable.controller';
import { EnableService } from './services/enable.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: CollectionName.EXTERNAL_ORDER, schema: ExternalOrderSchema },
    ]),
    SharedModule,
    PaymentModule,
    ShortenUrlModule,
    CompanyModule,
    OrderModule,
    OrderInvoiceModule,
    IntegrationLogModule,
    CustomerReadModule,
    CustomerWebstoreModule,
    CustomerLoyaltyMemberModule,
    LoyaltyTierReadModule,
    CouponModule,
    SharedStuffModule,
    ThirdPartyLogisticModule,
    BranchModule,
    BrandModule,
    RestaurantModule,
    CustomerPassModule,
    CustomerPassLinkModule,
    MicrosModule,
    UserModule,
    CustomerWriteModule,
    ShopifyModule,
    CustomCacheModule,
  ],
  controllers: [EnableController],
  providers: [EnableService],
  exports: [EnableService],
})
export class EnableModule {}
