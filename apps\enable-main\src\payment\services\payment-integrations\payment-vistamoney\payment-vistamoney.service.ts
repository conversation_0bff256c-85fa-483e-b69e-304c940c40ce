import {
  CollectionName,
  OrderDocument,
  Payment,
  PaymentDocument,
  PaymentGatewayType,
  PaymentMethod,
  PaymentStatusEnum,
  VistaMoneyConfiguration,
} from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model } from 'mongoose';
import * as qs from 'qs';
import * as randomstring from 'randomstring';
import * as xml2js from 'xml2js';
import * as xmlbuilder from 'xmlbuilder';
import { CompanyService } from '../../../../company/services/company/company.service';
import { CustomerReadServiceInterface } from '../../../../customer/modules/customer-read/customer-read.service.interface';
import { PaymentToProcess } from '../../../dto/payment-to-process.dto';
import { VistaMoneyStatusToCheck } from '../../../dto/vista-status-to-check.dto';
import { PaymentLogAction } from '../../../enums/payment-log-action.enum';
import { PaymentLog } from '../../../models/payment.log.model';
import { PaymentLogRepositoryInterface } from '../../../repositories/interfaces/payment.log.repository.interface';
import { PaymentRepositoryInterface } from '../../../repositories/interfaces/payment.repository.interface';
import { PaymentHelpersService } from '../../payment-helpers/payment-helpers.service';

@Injectable()
export class PaymentVistamoneyService {
  constructor(
    private configService: ConfigService,
    private http: HttpService,
    @InjectModel(CollectionName.ORDER)
    private paymentOrderModel: Model<OrderDocument>,
    private companyService: CompanyService,
    @InjectModel('PaymentLog') private paymentLogModel: Model<PaymentLog>,
    @Inject(CustomerReadServiceInterface)
    private customerReadService: CustomerReadServiceInterface,
    private paymentHelperService: PaymentHelpersService,
    @Inject('PaymentRepositoryInterface')
    private paymentRepository: PaymentRepositoryInterface,
    @Inject('PaymentLogRepositoryInterface')
    private paymentLogRepository: PaymentLogRepositoryInterface,
  ) {}

  async processPayment(
    payment: PaymentDocument,
    config: VistaMoneyConfiguration,
    paymentToProcess: PaymentToProcess,
  ): Promise<any> {
    return new Promise(async (resolve, reject) => {
      try {
        let gatewayURL = this.configService.get('GATEWAY_URL');
        const trackId = payment.code + randomstring.generate(5);
        payment.terminal_id_used = config.terminal_id;
        payment.terminal_password_used = config.terminal_password;

        // - build intial xml builder with the Request and default header
        let xml = xmlbuilder.create('request', {
          version: '1.0',
          encoding: 'ISO-8859-1',
        });

        // - building the terminal id and the terminal password and tracking_Id
        xml = await this.buildingTerminalId(xml, config);
        xml.ele(
          'action',
          '',
          this.configService.get('VISTAMONEY_PURCHASE_TRANSACTION_CODE'),
        );
        xml.ele('amount', '', payment.amount);
        xml.ele('trackId', '', trackId);

        if (
          this.configService.get('ENVIROMENT') == 'development' ||
          payment.is_test
        ) {
          xml.ele('isTest', '', 'true');
          gatewayURL = this.configService.get('GATEWAY_URL_TESTING');
        }

        // - check if he pay with credit of debit
        if (payment.payment_method == PaymentMethod.DEBIT_CARDS) {
          xml = await this.buildUserDefinedFields(
            xml,
            payment.code,
            '',
            payment.payment_method,
          );
          xml.ele('debit', '', 'true');
        } else if (payment.payment_method == PaymentMethod.CREDIT_CARDS) {
          xml = await this.buildUserDefinedFields(
            xml,
            payment.code,
            paymentToProcess.cardDetails.cardName,
            payment.payment_method,
          );
          xml = await this.buildUserDetails(
            xml,
            paymentToProcess.cardDetails.cardName,
            'default' + randomstring.generate(10) + '@e-butler.com',
          );
          xml = await this.buildCardDetails(
            xml,
            paymentToProcess.cardDetails.cardNumber,
            paymentToProcess.cardDetails.cardCVV,
            paymentToProcess.cardDetails.cardExpireYear,
            paymentToProcess.cardDetails.cardExpireMonth,
          );
          xml = await this.buildAddressDetails(
            xml,
            'EButler',
            'Doha',
            'Doha',
            '38364',
            this.configService.get('VISTAMONEY_COUNTRY_CODE'),
          );
        }

        // Logger.log(xml.end({pretty: true}));
        // resolve(xml.end());

        // Logger.log(xml.end({ pretty: true }));
        // Perform the REquest to the Vista Backend
        this.http
          .post(gatewayURL, xml.end({ pretty: true }), {
            headers: {
              Accept: 'application/xml',
              'Content-Type': 'application/xml',
            },
          })
          .subscribe(
            async (data) => {
              try {
                payment.gatewayType = PaymentGatewayType.VISTA;
                const result = await xml2js.parseStringPromise(data['data']);
                // resolve(result);
                payment.payment_method_used = payment.payment_method as any;
                payment.transaction_date = moment().toDate();
                payment.transaction_id = result['response']['tranid']
                  ? result['response']['tranid'][0]
                  : '';
                if (
                  result['response']['result'] &&
                  result['response']['result'][0] == 'Unsuccessful'
                ) {
                  payment.status = PaymentStatusEnum.UNSUCCESSFUL;
                  await payment.save();
                  reject(result['response']['udf5'][0]);
                } else {
                  payment.status = PaymentStatusEnum.GATEWAY_LINK_GENERATED;
                  payment.gateway_link =
                    result['response']['targetUrl'][0] +
                    result['response']['payId'][0];

                  if (!payment.tracing) {
                    payment.tracing = [];
                  }
                  payment.tracing.push({
                    trackId,
                    payId: result['response']['payId'][0],
                    dateIssuedUTC: moment.utc().toDate(),
                  });
                }
                payment.dateGateway = moment().utc().toDate();
                await payment.save();

                resolve(payment.gateway_link);
              } catch (err) {
                reject(err);
              }
            },
            (err) => {
              reject(err);
            },
          );
      } catch (err) {
        reject(err);
      }
    });
  }

  async afterPaymentDone(vistaMoneyPaymentData: any) {
    const paymentCode = vistaMoneyPaymentData['udf1']
      ? vistaMoneyPaymentData['udf1']
      : vistaMoneyPaymentData['udfs1'];

    // Getting The Payment Details using the transaction UUID
    const payment =
      await this.paymentRepository.findByCodeWithBranch(paymentCode);
    if (!payment) return;

    // Fetching the company details
    const company = await this.companyService.get_details(payment.company);

    // Getting The Order Details
    let order: OrderDocument;
    if (payment.order_code) {
      order = await this.paymentOrderModel.findOne({
        code: payment.order_code,
      });
    }

    if (payment.status == PaymentStatusEnum.TRANSACTION_COMPLETED) {
      vistaMoneyPaymentData['ENABLE_NOTE'] =
        'The Payment is Successful and it trying to update to this one';
      payment.paymentTries
        ? payment.paymentTries.push(vistaMoneyPaymentData)
        : (payment.paymentTries = [vistaMoneyPaymentData]);
      await payment.save();

      return 'https://enable.tech';
    }

    // Handle THe Payment Default Data like the transction date and payment tries
    await this.paymentHelperService.saveTransaction(
      payment,
      vistaMoneyPaymentData['tranid'],
      vistaMoneyPaymentData,
      PaymentGatewayType.VISTA,
    );

    // Handle The Payment Status Changes and The Success and Un Success logic
    const paymentStatus =
      vistaMoneyPaymentData['responsecode'] == '000'
        ? PaymentStatusEnum.TRANSACTION_COMPLETED
        : PaymentStatusEnum.UNSUCCESSFUL;
    await this.paymentHelperService.handlePaymentStatusLogic(
      payment,
      paymentStatus,
      order,
      company,
    );
    // Generate the CallbackURL
    const callbackURL = this.paymentHelperService.generateCallBackUrl(
      payment,
      company,
      vistaMoneyPaymentData['responsecode'],
      order,
    );

    // Payment Log Updating
    await this.paymentLogRepository.create({
      sentObject: { callbackURL },
      receivedObject: vistaMoneyPaymentData,
      logAction: PaymentLogAction.VistaMoneyWebhookFired,
      paymentCode: payment.code,
      paymentId: payment._id,
    });

    return callbackURL;
  }

  async removeLatestPaymentFromCustomer(payment: Payment) {
    const customer = await this.customerReadService.findOne(
      payment.customer._id.toHexString(),
      payment.company,
    );
    if (customer) {
      customer.latestPaymentDate = undefined;
      customer.latestPayment = undefined;
      await customer.save();
    }
  }

  private async buildingTerminalId(
    xml: xmlbuilder.XMLElement,
    config: VistaMoneyConfiguration,
  ) {
    xml.ele(
      'currencyCode',
      '',
      this.configService.get('VISTAMONEY_CURRENCY_CODE'),
    );
    xml.ele('terminalId', config.terminal_id);
    xml.ele('password', config.terminal_password);
    return xml;
  }

  private async buildUserDefinedFields(
    xml: xmlbuilder.XMLElement,
    udf1,
    udf2,
    udf3,
  ) {
    xml.ele('udf1', '', udf1);
    xml.ele('udf2', '', udf2);
    xml.ele('udf3', '', udf3);
    return xml;
  }

  private async buildUserDetails(
    xml: xmlbuilder.XMLElement,
    userName,
    userEmail,
  ) {
    xml.ele('member', '', userName);
    xml.ele('email', '', userEmail);
    return xml;
  }

  private async buildCardDetails(
    xml: xmlbuilder.XMLElement,
    card_number,
    card_cvc,
    card_expire_year,
    card_expire_month,
  ) {
    xml.ele('card', '', card_number);
    xml.ele('cvv2', '', card_cvc);
    xml.ele('expYear', '', card_expire_year);
    xml.ele('expMonth', '', card_expire_month);
    return xml;
  }

  private async buildAddressDetails(
    xml: xmlbuilder.XMLElement,
    address,
    city,
    state_code,
    zip,
    country_code,
  ) {
    xml.ele('address', '', address);
    xml.ele('city', '', city);
    xml.ele('stateCode', '', state_code);
    xml.ele('zip', '', zip);
    xml.ele('countryCode', '', country_code);
    return xml;
  }

  async performCheckStatusRequest(
    vistaMoneyStatusToCheck: VistaMoneyStatusToCheck,
  ) {
    const xml = xmlbuilder.create('request', {
      version: '1.0',
      encoding: 'ISO-8859-1',
    });
    xml.ele('terminalid', vistaMoneyStatusToCheck.terminalId);
    xml.ele('password', vistaMoneyStatusToCheck.terminalPassword);
    xml.ele('action', 15);
    xml.ele('trackid', vistaMoneyStatusToCheck.trackId);
    if (vistaMoneyStatusToCheck.transactionId) {
      xml.ele('transid', vistaMoneyStatusToCheck.transactionId);
    }
    if (vistaMoneyStatusToCheck.payId) {
      xml.ele('payId', vistaMoneyStatusToCheck.payId);
    }
    const gatewayUrl = vistaMoneyStatusToCheck.isTest
      ? this.configService.get('GATEWAY_URL_TESTING')
      : this.configService.get('GATEWAY_URL');

    const data = await this.performRequest(
      gatewayUrl,
      xml.end({ pretty: true }),
    );
    const result = await xml2js.parseStringPromise(data);
    return result;
  }

  private performRequest(url: string, xml: string) {
    return new Promise((resolve, reject) => {
      this.http
        .post(url, xml, {
          headers: {
            Accept: 'application/xml',
            'Content-Type': 'application/xml',
          },
        })
        .subscribe(
          (data) => {
            resolve(data.data);
          },
          (err) => {
            reject(err);
          },
        );
    });
  }

  public async createCreditCardSession(paymentCode: string) {
    const payment = await this.paymentRepository.findByCode(paymentCode);
    if (!payment) {
      return;
    }
    const company = await this.companyService.get_details(payment.company);
    const creditSessionToCreate = {
      apiOperation: 'CREATE_CHECKOUT_SESSION',
      apiPassword: this.configService.get('TESS_CREDIT_API_PASSWORD'),
      apiUsername: this.configService.get('TESS_CREDIT_USERNAME'),
      merchant: this.configService.get('TESS_CREDIT_MERCHANT'),
      'interaction.operation': 'PURCHASE',
      'interaction.returnUrl':
        this.configService.get('HOST_URL') +
        '/payment/tess/callback/' +
        payment.code,
      'interaction.cancelUrl':
        this.configService.get('HOST_URL') +
        '/payment/tess/callback/' +
        payment.code,
      'order.id': payment.code,
      'order.amount': payment.amount,
      'order.currency':
        company && company.localization && company.localization.currency
          ? company.localization.currency
          : 'QAR',
      'order.description': `Pay For ${payment.code}`,
    };
    const tessResponse = qs.parse(
      (await this.performCreditCardSessionRequest(
        creditSessionToCreate,
      )) as string,
    );
    if (tessResponse['result'] != 'ERROR') {
      payment.tessCreditResponse = tessResponse;
      await payment.save();
      return tessResponse;
    }
    throw new BadRequestException(tessResponse);
  }

  private async performCreditCardSessionRequest(
    creditCardSessionToCreate: any,
  ) {
    const URL = this.configService.get('TESS_CREDIT_URL');
    return new Promise((resolve, reject) => {
      this.http
        .post(URL, qs.stringify(creditCardSessionToCreate), {
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        })
        .subscribe({
          next: (data) => {
            resolve(data.data);
          },
          error: (error) => {
            reject(error);
          },
        });
    });
  }

  public async creditCardCallback(paymentCode: string, data: any) {
    // Getting The Payment Details using the transaction UUID
    const payment =
      await this.paymentRepository.findByCodeWithBranch(paymentCode);
    if (!payment) return;

    // Fetching the company details
    const company = await this.companyService.get_details(payment.company);

    // Getting The Order Details
    let order: OrderDocument;
    if (payment.order_code) {
      order = await this.paymentOrderModel.findOne({
        code: payment.order_code,
      });
    }

    if (payment.status == PaymentStatusEnum.TRANSACTION_COMPLETED) {
      data['ENABLE_NOTE'] =
        'The Payment is Successful and it trying to update to this one';
      payment.paymentTries
        ? payment.paymentTries.push(data)
        : (payment.paymentTries = [data]);
      await payment.save();

      return 'https://enable.tech';
    }

    // Handle THe Payment Default Data like the transction date and payment tries
    await this.paymentHelperService.saveTransaction(
      payment,
      data['sessionVersion'],
      data,
      PaymentGatewayType.VISTA,
    );

    // Handle The Payment Status Changes and The Success and Un Success logic
    const paymentStatus =
      data['resultIndicator'] == payment.tessCreditResponse['successIndicator']
        ? PaymentStatusEnum.TRANSACTION_COMPLETED
        : PaymentStatusEnum.UNSUCCESSFUL;
    await this.paymentHelperService.handlePaymentStatusLogic(
      payment,
      paymentStatus,
      order,
      company,
    );

    const responseCode =
      paymentStatus == PaymentStatusEnum.TRANSACTION_COMPLETED ? '000' : '123';
    // Generate the CallbackURL
    const callbackURL = this.paymentHelperService.generateCallBackUrl(
      payment,
      company,
      responseCode,
      order,
    );

    // Payment Log Updating
    await this.paymentLogRepository.create({
      sentObject: { callbackURL },
      receivedObject: data,
      logAction: PaymentLogAction.TessCreditWebhookFired,
      paymentCode: payment.code,
      paymentId: payment._id,
    });

    return callbackURL;
  }
}
