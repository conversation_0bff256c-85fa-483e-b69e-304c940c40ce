import { ConfigurationDocument } from '../../models/configuration.model';
import {
  TemplateOwner,
  UpdateConfigurationByOwnerDto,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface ConfigurationServiceInterface {
  findOrCreateConfiguration(
    owner: TemplateOwner,
  ): Promise<ConfigurationDocument>;
  findConfigurationByOwnerId(
    ownerId: Types.ObjectId,
  ): Promise<ConfigurationDocument | null>;
  hasWhatsappKey(ownerId: Types.ObjectId): Promise<boolean>;
  updateConfig(
    configurationDto: UpdateConfigurationByOwnerDto,
  ): Promise<ConfigurationDocument>;
}
