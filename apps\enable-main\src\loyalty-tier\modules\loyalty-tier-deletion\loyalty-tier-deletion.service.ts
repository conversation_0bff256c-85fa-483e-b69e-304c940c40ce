import {
  CustomerSearchType,
  DeleteLoyaltyTierDto,
  DeletionContext,
  DeletionStrategy,
  IndexCustomerDto,
  isNoTier,
  LoyaltyTierDocument,
  Option,
  responseCode,
} from '@app/shared-stuff';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { isObjectIdOrHexString, Types } from 'mongoose';
import { CompanyService } from '../../../company/services/company/company.service';
import { CustomerIndexServiceInterface } from '../../../customer/modules/customer-index/customer-index.service.interface';
import { CustomerTierServiceInterface } from '../../../customer/modules/customer-tier/customer-tier.service.interface';
import { LoyaltyTierIndexService } from '../loyalty-tier-index/loyalty-tier-index.service';
import { LoyaltyTierReadServiceInterface } from '../loyalty-tier-read/loyalty-tier-read.service.interface';
import { LoyaltyTierRepositoryInterface } from '../loyalty-tier-repository/loyalty-tier.repository.interface';
import { LoyaltyTierDeletionServiceInterface } from './loyalty-tier-deletion.service.interface';

@Injectable()
export class loyaltyTierDeletionService
  implements LoyaltyTierDeletionServiceInterface
{
  constructor(
    @Inject(LoyaltyTierRepositoryInterface)
    private readonly loyaltyTierRepository: LoyaltyTierRepositoryInterface,
    @Inject(LoyaltyTierReadServiceInterface)
    private readonly loyaltyTierReadService: LoyaltyTierReadServiceInterface,
    @Inject(CustomerIndexServiceInterface)
    private readonly customerIndexService: CustomerIndexServiceInterface,
    @Inject(CustomerTierServiceInterface)
    private readonly customerTierService: CustomerTierServiceInterface,
    private readonly loyaltyTierIndexService: LoyaltyTierIndexService,
    private readonly eventEmitter: EventEmitter2,
    private readonly companyService: CompanyService,
  ) {}

  async getDeletionContext(
    loyaltyTierId: Types.ObjectId,
  ): Promise<DeletionContext> {
    const loyaltyTier =
      await this.loyaltyTierReadService.findById(loyaltyTierId);
    const { _id, nameEn, companyId } = loyaltyTier;
    const tierId: string = _id.toString();

    const tiers = await this.loyaltyTierIndexService.index({
      companyId,
      withNoTier: true,
    });
    const company = await this.companyService.findById(companyId);
    const alternativeTiers: Option[] = tiers
      .filter((tier) => tier._id.toString() !== tierId)
      .map((tier) => ({
        label: isNoTier(tier) ? tier.getName(company) : tier.nameEn,
        value: isObjectIdOrHexString(tier._id) ? tier._id.toString() : null,
      }));

    const assignedCustomersCount = await this.customerIndexService.count(
      this.getIndexCustomerDto(loyaltyTierId),
    );

    return {
      loyaltyTierId,
      nameEn,
      alternativeTiers,
      assignedCustomersCount,
    };
  }

  async delete(
    loyaltyTierId: Types.ObjectId,
    { deletionStrategy, targetTierId }: DeleteLoyaltyTierDto,
  ): Promise<LoyaltyTierDocument> {
    const loyaltyTier =
      await this.loyaltyTierReadService.findById(loyaltyTierId);

    const company = await this.companyService.findById(loyaltyTier.companyId);
    if (
      loyaltyTier?.enrollmentCode ===
      company?.loyaltyProgramConfig?.baseTierEnrollmentCode
    ) {
      throw new BadRequestException(
        'You cannot delete the base loyalty registration tier. Please visit the loyalty configurations page and change the base tier first',
        responseCode.IN_VALID_INPUT.toString(),
      );
    }
    if (deletionStrategy === DeletionStrategy.AUTO_ASSIGN) {
      const deletedTier = await this.loyaltyTierRepository.delete(loyaltyTier);
      const allTiers = await this.loyaltyTierReadService.findByCompanyId(
        deletedTier.companyId,
      );
      await this.customerTierService.reassignForTierDeletion(
        deletedTier,
        allTiers,
      );
      this.eventEmitter.emit('loyaltytier.deleted', deletedTier);
      return deletedTier;
    } else if (deletionStrategy === DeletionStrategy.MANUAL_ASSIGN) {
      await this.customerTierService.bulkTierAssignment({
        tierId: targetTierId,
        notifyCustomers: false,
        customerSegment: this.getIndexCustomerDto(loyaltyTierId),
      });
      const deletedTier = await this.loyaltyTierRepository.delete(loyaltyTier);
      this.eventEmitter.emit('loyaltytier.deleted', deletedTier);
      return deletedTier;
    } else {
      throw new BadRequestException(
        `Deletion Strategy '${deletionStrategy}' is not supported.`,
      );
    }
  }

  private getIndexCustomerDto(loyaltyTierId: Types.ObjectId): IndexCustomerDto {
    return {
      search_type: [CustomerSearchType.LOYALTY_TIER],
      search_key: loyaltyTierId.toString(),
    };
  }
}
