import {
  DeliveryThirdPartyName,
  OrderLogActionEnum,
  OrderPaymentStatus,
  PaymentStatusEnum,
} from '@app/shared-stuff';

export const ThirdPartyActionCreationMapping: Record<
  DeliveryThirdPartyName,
  OrderLogActionEnum
> = {
  bee: OrderLogActionEnum.BEE_DELIVERY_TASK_TO_CREATE,
  mrDelivery: OrderLogActionEnum.MR_DELIVERY_TASK_TO_Create,
  clicks: OrderLogActionEnum.CLICKS_TASK_CREATION,
  deliveryHub: OrderLogActionEnum.DELIVERY_HUB_TASK_CREATED,
  enableDelivery: OrderLogActionEnum.ENABLE_DELIVERY_TASK_CREATED,
  wishbox: OrderLogActionEnum.WISHBOX_TASK_CREATED,
  hsDelivery: OrderLogActionEnum.HS_DELIVERY_TASK_CREATED,
  snoonu: OrderLogActionEnum.FALCON_FLEX_TASK_CREATION,
  passDelivery: OrderLogActionEnum.PASS_DELIVERY_TASK_CREATION,
  [DeliveryThirdPartyName.enableRiders]:
    OrderLogActionEnum.ORDER_DELIVERY_LOCATION_UPDATED,
};

export const OrderPaymentStatusMapping: {
  [VALUE in PaymentStatusEnum]: OrderPaymentStatus;
} = {
  [PaymentStatusEnum.PENDING]: OrderPaymentStatus.PENDING,
  [PaymentStatusEnum.TRANSACTION_COMPLETED]: OrderPaymentStatus.COMPLETED,
  [PaymentStatusEnum.UNSUCCESSFUL]: OrderPaymentStatus.UNSUCCESSFUL,
  [PaymentStatusEnum.PROCESSING]: OrderPaymentStatus.PENDING,
  [PaymentStatusEnum.GATEWAY_LINK_GENERATED]: OrderPaymentStatus.PENDING,
  [PaymentStatusEnum.EXPIRED]: OrderPaymentStatus.CANCELLED,
  [PaymentStatusEnum.INTERNAL_FAILED]: OrderPaymentStatus.CANCELLED,
};

// export const OrderDeliveryPartyMapping: {
//   [VALUE in OrderDeliveryParty]: OrderDriversType;
// } = {
//   thirdParty: OrderDriversType.THIRD_PARTY,
//   ownDrivers: OrderDriversType.OWN_DRIVERS,
//   ebDelivery: OrderDriversType.EB_DELIVERY,
//   companyDrivers: OrderDriversType.COMPANY_DRIVERS,
// };

// export const OrderDriversTypeMapping: {
//   [VALUE in OrderDriversType]: OrderDeliveryParty;
// } = {
//   thirdParty: OrderDeliveryParty.THIRD_PARTY,
//   company: OrderDeliveryParty.OWN_DRIVERS,
//   ebutler: OrderDeliveryParty.EB_DELIVERY,
//   companyDrivers: OrderDeliveryParty.COMPANY_DRIVERS,
// };
