import {
  Create<PERSON>rand<PERSON>ourneyDto,
  CreateOwner<PERSON>ourneyDto,
  CurrentUser,
  IndexJourneyDto,
  IndexJourneyResponseDto,
  JourneyDocument,
  LoggerService,
  MicroserviceCommunicationService,
  TemplateOwner,
  UpdateBrandJourneyDto,
  UpdateOwnerJourneyDto,
} from '@app/shared-stuff';
import {
  Inject,
  Injectable,
  OnModuleDestroy,
  OnModuleInit,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';

import { Types } from 'mongoose';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { JourneyServiceInterface } from './journey.service.interface';
import { OnEvent } from '@nestjs/event-emitter';

@Injectable()
export class JourneyService
  implements JourneyServiceInterface, OnModuleInit, OnModuleDestroy
{
  private readonly loggerService = new LoggerService(JourneyService.name);

  constructor(
    @Inject('enable-main-notification-producer')
    private client: ClientProxy,
    private microserviceCommunicationService: MicroserviceCommunicationService,
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandServiceInterface,
  ) {}

  onModuleInit() {
    this.microserviceCommunicationService.connect(this.client);
  }

  onModuleDestroy() {
    this.microserviceCommunicationService.disconnect(this.client);
  }

  async index(
    indexJourneyDto: IndexJourneyDto,
  ): Promise<IndexJourneyResponseDto[]> {
    const response =
      await this.microserviceCommunicationService.produceAndWaitForResponse<
        IndexJourneyDto,
        { data: IndexJourneyResponseDto[] }
      >(this.client, 'journey.index.request', indexJourneyDto);
    return response.data ?? (response as any);
  }

  async create(
    { brandId, ...createJourneyDto }: CreateBrandJourneyDto,
    currentUser: CurrentUser,
  ): Promise<JourneyDocument> {
    const brand = await this.brandService.findById(brandId);
    const createOwnerJourneyDto: CreateOwnerJourneyDto = {
      ...createJourneyDto,
      owner: new TemplateOwner(brand),
      companyId: brand.companyId,
      createdBy: currentUser,
    };
    return await this.microserviceCommunicationService.produceAndWaitForResponse(
      this.client,
      'journey.create.request',
      createOwnerJourneyDto,
    );
  }

  async getDetails(journeyId: Types.ObjectId): Promise<JourneyDocument> {
    return await this.microserviceCommunicationService.produceAndWaitForResponse(
      this.client,
      'journey.getDetails.request',
      { journeyId },
    );
  }

  @OnEvent('cron.every1min')
  async runNotificationJobs(): Promise<void> {
    return await this.microserviceCommunicationService.produceAndWaitForResponse(
      this.client,
      'journey.runNotificationJobs.request',
      {},
    );
  }

  async update(
    journeyId: Types.ObjectId,
    updateBrandJourneyDto: UpdateBrandJourneyDto,
  ): Promise<JourneyDocument> {
    if (updateBrandJourneyDto.brandId) {
      const brand = await this.brandService.findById(
        updateBrandJourneyDto.brandId,
      );
      updateBrandJourneyDto.owner = new TemplateOwner(brand);
    }
    const updateOwnerJourneyDto: UpdateOwnerJourneyDto = {
      journeyId,
      ...updateBrandJourneyDto,
    };
    return await this.microserviceCommunicationService.produceAndWaitForResponse(
      this.client,
      'journey.update.request',
      updateOwnerJourneyDto,
    );
  }

  async delete(journeyId: Types.ObjectId): Promise<number> {
    return await this.microserviceCommunicationService.produceAndWaitForResponse(
      this.client,
      'journey.delete.request',
      { journeyId },
    );
  }
}
