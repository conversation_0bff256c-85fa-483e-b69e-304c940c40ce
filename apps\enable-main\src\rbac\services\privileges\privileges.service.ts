import { ConfigService } from '@nestjs/config';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { PrivilegeDocument } from '../../../rbac/models/privilege.model';
import {
  PrivilegeToCreate,
  PrivilegeToIndx,
  PrivilegeToUpdate,
} from '../../../rbac/dto/privilege.dto';
import { RoleService } from '../role/role.service';

@Injectable()
export class PrivilegesService {
  constructor(
    @InjectModel('Privilege') private privilegeModel: Model<PrivilegeDocument>,
  ) {}

  async index(filterData: PrivilegeToIndx) {
    const privileges = await this.privilegeModel.aggregate([
      // {
      //   $match: {
      //     parentKey: null,
      //   },
      // },
      {
        $graphLookup: {
          from: 'privileges',
          startWith: '$_id',
          connectFromField: '_id',
          connectToField: 'parentKey',
          maxDepth: 0,
          as: 'children',
        },
      },
    ]);
    return privileges;
    // let privileges = this.privilegeModel.find();
    // if (filterData.limit && filterData.limit) {
    //   privileges = privileges
    //     .skip(filterData.limit * filterData.offset)
    //     .limit(filterData.limit);
    // }
    // if (filterData.search_key) {
    //   privileges = privileges.find({
    //     name: { $regex: new RegExp(`.*${filterData.search_key}.*`) },
    //   });
    // }
    // return await privileges.exec();
  }
  async get_details(id) {
    const selectedPrivilege = await this.privilegeModel.findById(id);
    return selectedPrivilege;
  }

  async create(priviToCreate: PrivilegeToCreate) {
    const createdPrivilege = new this.privilegeModel(priviToCreate);
    await createdPrivilege.save();
    return createdPrivilege;
  }

  async update(priviToUpdate: PrivilegeToUpdate) {
    const selectedPrivilege = await this.privilegeModel.findByIdAndUpdate(
      priviToUpdate._id,
      priviToUpdate,
      { new: true },
    );
    return selectedPrivilege;
  }

  async remove(id) {
    const deletedPrivilege = await this.privilegeModel.findByIdAndRemove(id);
    return deletedPrivilege;
  }
}
