// [EBL-5893] Points Redemption Method
// init hasCoupons based on coupon list

const companiesWithCoupons = db.coupons
  .distinct('companyId', { deletedAt: null })
  .map((id) => id.toString());

db.companies.bulkWrite(
  db.companies
    .distinct('_id', { loyaltyProgramConfig: { $exists: true } })
    .map((companyId) => ({
      updateOne: {
        filter: { _id: companyId },
        update: {
          $set: {
            'loyaltyProgramConfig.hasCoupons': companiesWithCoupons.includes(
              companyId.toString(),
            ),
          },
        },
      },
    })),
  { ordered: false },
);
