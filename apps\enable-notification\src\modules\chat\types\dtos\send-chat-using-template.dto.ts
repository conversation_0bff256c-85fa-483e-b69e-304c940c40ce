import { WhatsAppButtonsDto } from '@app/shared-stuff';

export class ChatTemplateComponent {
  type: string;
  sub_type?: string;
  index?: number;
  parameters?: {
    type: string;
    text: string;
  }[];
  buttons?: WhatsAppButtonsDto[];
}

class ChatTemplate {
  name: string;
  language: {
    code: string;
    policy: string;
  };
  components: ChatTemplateComponent[];
}

export class SendChatUsingTemplateDto {
  messaging_product?: string;
  recipient_type?: string;
  to: string;
  type = 'template';
  template: ChatTemplate;
  metadata: Record<string, any>;
}
