import { ApiProperty } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';

// Based on https://tookanapi.docs.apiary.io/#reference/task/create-task
export class CreateTookanTaskDto {
  api_key: string;
  order_id: string;
  team_id: string;
  fleet_id: string;
  auto_assignment: number;
  barcode: string;
  timezone = -180;
  tags: string;
  job_description: string;
  layout_type = 0 as const;
  custom_field_template: string;
  transport_type?: string;
}

export class CreateDeliveryTookanTaskDto extends CreateTookanTaskDto {
  customer_email: string;
  customer_username: string;
  customer_phone: string;
  customer_address: string;
  latitude: string;
  longitude: string;
  job_delivery_datetime: string;
  meta_data: any[];
  has_pickup: 0 | 1 = 0;
  has_delivery = 1 as const;
}

export class TookanMetadata {
  label: string;
  data: string | number;
}

export class CreateBothTookanTaskDto extends CreateDeliveryTookanTaskDto {
  job_pickup_phone: string;
  job_pickup_name: string;
  job_pickup_email: string;
  job_pickup_address: string;
  job_pickup_latitude: string;
  job_pickup_longitude: string;
  job_pickup_datetime: string;
  pickup_meta_data: TookanMetadata[];
  has_pickup = 1 as const;
  has_delivery = 1 as const;
  pickup_custom_field_template: string;
}

export class TaskDetailsToGet {
  @ApiProperty({
    type: String,
  })
  job_id: string;
}

export class TookanDriverToCreate {
  @ApiProperty({
    type: String,
    required: true,
  })
  api_key: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  email: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  phone: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  transport_type: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  transport_desc: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  license: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  color: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  timezone: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  team_id: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  password: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  username: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  first_name: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  last_name: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  rule_id: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  fleet_type: string;

  tags: string;
}

export class TookanDriverToUpdate {
  @ApiProperty({
    type: String,
    required: true,
  })
  api_key: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  fleet_id: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  email: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  phone: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  transport_type: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  transport_desc: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  license: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  color: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  timezone: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  team_id: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  password: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  first_name: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  last_name: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  rule_id: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  fleet_type: string;

  tags: string;
}

export class TookanTeamToCreate {
  api_key: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  team_name: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  battery_usage: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  tags: string;
}

export class TookanTeamToEdit {
  api_key: string;

  @ApiProperty({
    type: Number,
    required: false,
  })
  team_id: number;

  @ApiProperty({
    type: String,
    required: false,
  })
  team_name: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  battery_usage: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  tags: string;
}

export class TookanTaskToEdit {
  @ApiProperty({
    required: false,
    type: String,
  })
  order_id: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  team_id: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  fleet_id: string;

  @ApiProperty({
    type: Boolean,
    required: false,
    default: false,
  })
  auto_assignment: number;

  @ApiProperty({
    type: String,
    required: true,
  })
  job_description: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  job_pickup_phone: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  job_pickup_name: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  job_pickup_email: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  job_pickup_address: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  job_pickup_latitude: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  job_pickup_longitude: string;

  @ApiProperty({
    type: Date,
    required: false,
  })
  job_pickup_datetime: Date;

  @ApiProperty({
    type: String,
    required: false,
  })
  customer_email: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  customer_username: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  customer_phone: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  customer_address: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  latitude: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  longitude: string;

  @ApiProperty({
    type: Date,
    required: false,
  })
  job_delivery_datetime: Date;

  @ApiProperty({
    type: String,
    required: false,
  })
  tags: string;

  @ApiProperty({
    type: [TookanMetadata],
    required: false,
  })
  meta_data: TookanMetadata[];

  @ApiProperty({
    type: [TookanMetadata],
    required: false,
  })
  pickup_meta_data: TookanMetadata[];

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  barcode: string;

  notify: boolean;
  job_id: string;

  api_key: string;
  pickup_custom_field_template: string;
  custom_field_template: string;
  has_pickup = 1;
  has_delivery = 1;
  layout_type = 0;
  timezone = '+00';
}

export class TookanTask {
  @ApiProperty({
    type: String,
    required: true,
  })
  job_id?: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  address: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  latitude: number;

  @ApiProperty({
    type: Number,
    required: true,
  })
  longitude: number;

  @ApiProperty({
    type: String,
    required: true,
  })
  time: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  job_description: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  template_name: string;

  @ApiProperty({
    type: () => [TookanTemplateItem],
    required: true,
  })
  template_data: TookanTemplateItem[];

  @ApiProperty({
    type: () => [String],
  })
  ref_images: string[];

  @ApiProperty({
    type: String,
    required: true,
  })
  name: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  email: string;
  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  barcode: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  order_id: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  phone?: string;
}

export class TookanTemplateItem {
  @ApiProperty({
    type: String,
    required: true,
  })
  label: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  data: string;
}

export class TookanMultipleTasksToCreate {
  @ApiProperty({
    type: String,
    required: true,
  })
  api_key: string;

  @ApiProperty({
    type: Number,
  })
  fleet_id?: number;

  @ApiProperty({
    type: String,
    required: true,
  })
  team_id?: string;

  @ApiProperty({
    type: Number,
  })
  timezone: number;

  @ApiProperty({
    type: Number,
    required: false,
    default: 1,
  })
  has_pickup?: number = 1;

  @ApiProperty({
    type: Number,
    required: false,
    default: 1,
  })
  has_delivery?: number = 1;

  @ApiProperty({
    type: Number,
    required: false,
    default: 0,
  })
  layout_type?: number = 0;

  @ApiProperty({
    type: Number,
    required: false,
    default: 0,
  })
  geofence?: number = 0;

  @ApiProperty({
    type: Number,
    required: false,
    default: 0,
  })
  auto_assignment?: number = 0;

  @ApiProperty({
    type: String,
    required: true,
  })
  tags: string;

  @ApiProperty({
    type: () => [TookanTask],
    required: true,
  })
  pickups: TookanTask[];

  @ApiProperty({
    type: () => [TookanTask],
    required: true,
  })
  deliveries: TookanTask[];
}

export class TookanMultipleTasksToAssign {
  @ApiProperty({
    type: String,
    required: true,
  })
  api_key: string;

  @ApiProperty({
    type: Number,
    required: false,
  })
  team_id: number;

  @ApiProperty({
    type: Number,
    required: true,
  })
  fleet_id: number;

  @ApiProperty({
    type: [Number],
    required: true,
  })
  job_ids: number[];
}

export class AssignAgentToTookanTaskDto {
  @ApiProperty({
    type: String,
    required: true,
  })
  api_key?: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  job_status?: string;

  @ApiProperty({
    type: Number,
  })
  fleet_id: number;

  @ApiProperty({
    type: Number,
  })
  job_id: number;

  @ApiProperty({
    type: Number,
  })
  team_id: number;

  @ApiProperty({
    type: Number,
    required: false,
  })
  notify?: number;

  @ApiProperty({
    type: Number,
    required: false,
  })
  geofence?: number;
}
