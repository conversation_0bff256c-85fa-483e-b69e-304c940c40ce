import {
  <PERSON><PERSON><PERSON>,
  createOrderSchemaWithHooks,
  MicroserviceCommunicationModule,
  SharedStuffModule,
} from '@app/shared-stuff';
import { HttpModule } from '@nestjs/axios';
import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitter2, EventEmitterModule } from '@nestjs/event-emitter';
import { MongooseModule } from '@nestjs/mongoose';
import { ScheduleModule } from '@nestjs/schedule';
import { BenefitModule } from '../benefit/benefit.module';
import { BranchModule } from '../branch/branch.module';
import { BrandModule } from '../brand/brand.module';
import { CompanyModule } from '../company/company.module';
import { CouponModule } from '../coupon/coupon.module';
import { CustomerLoyaltyMemberModule } from '../customer/modules/customer-loyalty-member/customer-loyalty-member.module';
import { CustomerOrderModule } from '../customer/modules/customer-order/customer-order.module';
import { CustomerPointsModule } from '../customer/modules/customer-points/customer.points.module';
import { CustomerReadModule } from '../customer/modules/customer-read/customer-read.module';
import { CustomerReplacementsModule } from '../customer/modules/customer-replacements/customer-replacements.module';
import { CustomerTierInfoModule } from '../customer/modules/customer-tier-info/customer-tier-info.module';
import { CustomerTierModule } from '../customer/modules/customer-tier/customer-tier.module';
import { CustomerWriteModule } from '../customer/modules/customer-write/customer-write.module';
import { ShortenUrlModule } from '../integration/shorten-url/shorten-url.module';
import { LoyaltyPointLogModule } from '../loyalty-point-log/loyalty-point-log.module';
import { LoyaltyTierLogModule } from '../loyalty-tier-log/loyalty-tier-log.module';
import { LoyaltyTierReadModule } from '../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.module';
import { LoyaltyTransactionModule } from '../loyalty-transaction/loyalty-transaction.module';
import { NotificationModule } from '../notification/notification.module';
import { PaymentModule } from '../payment/payment.module';
import { CompletedPunchCardModule } from '../punch-card/modules/completed-punch-card/completed-punch-card.module';
import { RestaurantModule } from '../restaurant/restaurant.module';
import { SharedModule } from '../shared/shared.module';
import { StorageModule } from '../storage/storage.module';
import { DeliveryModule } from './../delivery/delivery.module';
import { LocationModule } from './../location/location.module';
import { UserModule } from './../user/user.module';
import { OrderLogController } from './controllers/order-log/order-log.controller';
import { OrderTookanController } from './controllers/order/order-tookan.controller';
import { OrderController } from './controllers/order/order.controller';
import { OrderCronJobsService } from './cronjobs/order-cron-jobs.service';
import { OrderStatusSchema } from './models/order.status.model';
import { OrderItemSchema } from './models/orderItems.model';
import { OrderInvoiceModule } from './modules/order-invoice/order-invoice.module';
import { OrderInvoiceService } from './modules/order-invoice/order-invoice.service';
import { OrderRedemptionModule } from './modules/order-redemption/order-redemption.module';
import { OrderLogRepository } from './repository/order-log.repository';
import { AggregatorOrderService } from './services/aggregatorOrder/aggregatorOrder.service';
import { OrderBenefitService } from './services/order-benefit/order-benefit.service';
import { OrderBranchDispatcherService } from './services/order-branch-dispatcher/order-branch-dispatcher.service';
import { OrderCronJobService } from './services/order-cron-job/order-cron-job.service';
import { OrderDeliveryService } from './services/order-delivery/order-delivery.service';
import { OrderDispatcherService } from './services/order-dispatcher/order-dispatcher.service';
import { OrderItemService } from './services/order-item/order-item.service';
import { OrderListener } from './services/order-listeners/order-listener.service';
import { OrderLogService } from './services/order-log/order-log.service';
import { OrderNotificationService } from './services/order-notification/order-notification.service';
import { OrderPaymentService } from './services/order-payment/order-payment.service';
import { OrderPosService } from './services/order-pos/order-pos.service';
import { OrderReversionService } from './services/order-reversion/order-reversion.service';
import { OrderStatusService } from './services/order-status/order-status.service';
import { OrderTookanService } from './services/order-tookan/order-tookan.service';
import { TookanTaskWebhookService } from './services/order-tookan/tookan-task-webhook-service';
import { OrderDetailsEditService } from './services/order/order-details-edit.service';
import { OrderService } from './services/order/order.service';

@Module({
  imports: [
    CustomerReadModule,
    CustomerWriteModule,
    CustomerOrderModule,
    CustomerReplacementsModule,
    CustomerPointsModule,
    CustomerLoyaltyMemberModule,
    CustomerTierModule,
    CustomerTierInfoModule,
    LoyaltyPointLogModule,
    LoyaltyTransactionModule,
    LoyaltyTierReadModule,
    LoyaltyTierLogModule,
    CompletedPunchCardModule,
    HttpModule,
    NotificationModule,
    ConfigModule,
    SharedModule,
    NotificationModule,
    CompanyModule,
    BranchModule,
    forwardRef(() => PaymentModule),
    ScheduleModule,
    DeliveryModule,
    EventEmitterModule,
    RestaurantModule,
    LocationModule,
    UserModule,
    SharedStuffModule,
    BrandModule,
    UserModule,
    ShortenUrlModule,
    BenefitModule,
    CouponModule,
    StorageModule,
    OrderRedemptionModule,
    OrderInvoiceModule,
    MicroserviceCommunicationModule.forChild('enable-main-order-producer'),
    MongooseModule.forFeatureAsync([
      {
        name: CollectionName.ORDER,
        imports: [],
        useFactory: (eventEmitter: EventEmitter2) => {
          return createOrderSchemaWithHooks(eventEmitter);
        },
        inject: [EventEmitter2],
      },
      { name: 'OrderStatus', useFactory: () => OrderStatusSchema },
      { name: 'OrderItem', useFactory: () => OrderItemSchema },
    ]),
  ],
  controllers: [OrderController, OrderLogController, OrderTookanController],
  providers: [
    OrderService,
    OrderItemService,
    OrderNotificationService,
    OrderDeliveryService,
    OrderPaymentService,
    OrderPosService,
    {
      provide: 'OrderLogServiceInterface',
      useClass: OrderLogService,
    },
    {
      provide: 'OrderLogRepositoryInterface',
      useClass: OrderLogRepository,
    },
    OrderStatusService,
    AggregatorOrderService,
    OrderCronJobService,
    OrderListener,
    OrderDispatcherService,
    OrderBranchDispatcherService,
    OrderInvoiceService,
    OrderBenefitService,
    OrderTookanService,
    OrderReversionService,
    TookanTaskWebhookService,
    OrderCronJobsService,
    OrderDetailsEditService,
  ],
  exports: [
    OrderService,
    OrderNotificationService,
    OrderPosService,
    OrderDeliveryService,
    'OrderLogServiceInterface',
    OrderStatusService,
    AggregatorOrderService,
  ],
})
export class OrderModule {}
