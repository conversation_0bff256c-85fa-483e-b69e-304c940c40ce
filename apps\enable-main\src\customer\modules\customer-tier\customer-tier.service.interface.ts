import {
  BulkActionResponseDto,
  BulkTierAssignmentDto,
  CalendarCycle,
  CompanyDocument,
  CustomerDocument,
  LoyaltyTierDocument,
  LoyaltyTierProgramProgress,
  UpdateCustomerDto,
} from '@app/shared-stuff';
import { Types } from 'mongoose';
import { LoyaltyTierLogger } from '../../../loyalty-tier-log/services/loyalty-tier-log.service.interface';

export interface CustomerTierServiceInterface {
  resetCarryOver(company: CompanyDocument): Promise<void>;
  reduceNumberOfUses(
    customer: CustomerDocument,
    benefitType: 'PERCENT_DISCOUNT' | 'FREE_DELIVERY',
  ): Promise<void>;
  reassignForTierDeletion(
    loyaltyTier: LoyaltyTierDocument,
    otherTiers: LoyaltyTierDocument[],
  ): Promise<void>;
  checkForPromotion(
    customer: CustomerDocument,
    logger: LoyaltyTierLogger,
    brandId?: Types.ObjectId,
  ): Promise<void>;
  updateCustomerTier(
    customer: CustomerDocument,
    newTierId: Types.ObjectId,
    logger: LoyaltyTierLogger,
    isBenefitUsagePreserved?: boolean,
  ): Promise<void>;
  checkForTierMaintained(customer: CustomerDocument): Promise<void>;
  checkForTierUnmaintained(
    customer: CustomerDocument,
    company: CompanyDocument,
  ): Promise<void>;
  enrollCustomerInTier(
    customer: CustomerDocument,
    enrollmentCode: string,
    brandId?: Types.ObjectId,
  ): Promise<void>;
  resetCustomerCalendarCycle(
    customer: CustomerDocument,
    company: CompanyDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<any>;
  handleManualUpdatedLoyaltyTier(
    customer: CustomerDocument,
    updateCustomerDto: UpdateCustomerDto,
  ): Promise<void>;
  handleCustomerDeletion(customer: CustomerDocument): Promise<void>;
  bulkTierAssignment(
    bulkTierAssignmentDto: BulkTierAssignmentDto,
  ): Promise<BulkActionResponseDto>;
  customerTierBenefitsUpdated(
    oldTier: LoyaltyTierDocument,
    newTier: LoyaltyTierDocument,
  ): Promise<void>;
  computeCarryOverOnMaintain(
    customer: CustomerDocument,
    company: CompanyDocument,
    loyaltyTierProgramProgress: LoyaltyTierProgramProgress,
  ): Promise<{
    carryOverOrderRate: number;
    carryOverAmountSpent: number;
    carryOverPointsRate: number;
  }>;
}

export const CustomerTierServiceInterface = Symbol(
  'CustomerTierServiceInterface',
);
