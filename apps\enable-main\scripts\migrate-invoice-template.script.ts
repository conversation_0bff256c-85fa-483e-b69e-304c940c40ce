// EBL-5534 A4 Invoice Content Configuration
// Migrate `company.invoiceTemplate` to `company.interfaceConfig.orderConfig.invoicesConfig.template`

db.companies.updateMany(
  { invoiceTemplate: '0' },
  { $set: { 'interfaceConfig.orderConfig.invoicesConfig.template': 'a4' } },
);

db.companies.updateMany(
  { invoiceTemplate: '2' },
  {
    $set: { 'interfaceConfig.orderConfig.invoicesConfig.template': 'thermal' },
  },
);

db.companies.updateMany(
  { invoiceTemplate: '3' },
  {
    $set: {
      'interfaceConfig.orderConfig.invoicesConfig.template': 'a4',
      'interfaceConfig.orderConfig.invoicesConfig.hasProductImage': true,
    },
  },
);

// Run after frontend merged & other commands succeeded
// db.companies.updateMany({}, [{ $unset: 'invoiceTemplate' }]);
