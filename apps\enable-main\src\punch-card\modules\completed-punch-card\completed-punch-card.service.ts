import {
  CompletedPunchCardDocument,
  CustomerDocument,
  PunchCardProgress,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { Types } from 'mongoose';
import { CompletedPunchCardRepository } from './completed-punch-card.repository';

@Injectable()
export class CompletedPunchCardService {
  constructor(
    private readonly completedPunchCardRepository: CompletedPunchCardRepository,
  ) {}

  public async save(
    customer: CustomerDocument,
    progress: PunchCardProgress,
  ): Promise<CompletedPunchCardDocument> {
    return await this.completedPunchCardRepository.save(customer, progress);
  }

  public async bulkSave(
    customer: CustomerDocument,
    progresses: PunchCardProgress[],
  ): Promise<void> {
    return await this.completedPunchCardRepository.bulkSave(
      customer,
      progresses,
    );
  }

  public async findLatestProgress(
    customerId: Types.ObjectId,
    punchCardId: Types.ObjectId,
  ): Promise<CompletedPunchCardDocument> {
    return await this.completedPunchCardRepository.findLatestProgress(
      customerId,
      punchCardId,
    );
  }

  public async findAndRemoveLatestProgress(
    customerId: Types.ObjectId,
    punchCardId: Types.ObjectId,
  ): Promise<CompletedPunchCardDocument> {
    return await this.completedPunchCardRepository.findAndRemoveLatestProgress(
      customerId,
      punchCardId,
    );
  }
}
