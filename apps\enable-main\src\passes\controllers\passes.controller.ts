import {
  GenerateStampImageDto,
  GenericExceptionFilter,
  GoogleWalletWebhookDto,
  GoogleWalletWebhookPayload,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Get,
  Header,
  Inject,
  Param,
  Post,
  Query,
  Req,
  Res,
  Response,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { plainToInstance } from 'class-transformer';
import { Response as ExpressResponse, Request } from 'express';
import { IntegrationLogRepositoryInterface } from '../../integration/integration-log/repositories/interfaces/integration-log.repository.interface';
import { EnableWalletPass } from '../dto/enable-wallet-pass.dto';
import { GeneratePassDto } from '../dto/generate-pass.dto';
import { GetUpdatedPassResponse } from '../dto/get-updated-pass-response.dto';
import { ListPassesDto } from '../dto/list-passes.dto';
import { RegisterEnablePassDto } from '../dto/register-enable-pass.dto';
import { RegisterPassDto } from '../dto/register-pass.dto';
import { PassLogInterceptor } from '../interceptors/pass-log.interceptor';
import { EnablePassesService } from '../services/enable-passes/enable-passes.service';
import { GooglePassesServiceInterface } from '../services/google-passes/google-passes.service.interface';
import { PassesImageServiceInterface } from '../services/passes-image/passes-image.service.interface';
import { PKPassPassesService } from '../services/pkpass-passes/pkpass-passes.service';
import {
  PassListParams,
  PassRegistrationParams,
  PassUpdateParams,
} from '../types/pass-registration-params.type';

@Controller({
  version: '1',
})
@UseFilters(GenericExceptionFilter)
@UseInterceptors(PassLogInterceptor)
@ApiTags('Passes')
@SetMetadata('module', 'passes')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class PassesController {
  constructor(
    private readonly pkpassPassesService: PKPassPassesService,
    private readonly enablePassesService: EnablePassesService,
    @Inject(GooglePassesServiceInterface)
    private readonly googlePassesService: GooglePassesServiceInterface,
    @Inject(PassesImageServiceInterface)
    private readonly passesImageService: PassesImageServiceInterface,
    @Inject('IntegrationLogRepositoryInterface')
    private readonly integrationLogRepository: IntegrationLogRepositoryInterface,
  ) {}

  @Get('passes/:brandId/:customerId/pass.pkpass')
  @Header('content-type', 'application/vnd.apple.pkpass')
  @SetMetadata('public', 'true')
  async generatePassWithoutAuth(
    @Param() generatePassDto: GeneratePassDto,
    @Response() res: ExpressResponse,
  ) {
    const pass =
      await this.pkpassPassesService.generatePassWithoutAuth(generatePassDto);

    return pass.getAsStream().pipe(res);
  }

  @Get('passes/:brandId/:customerId/enablePass')
  @SetMetadata('public', 'true')
  async generateEnablePass(
    @Param() generatePassDto: GeneratePassDto,
  ): Promise<EnableWalletPass> {
    return this.enablePassesService.generateEnablePass(generatePassDto);
  }

  @Get('passes/:brandId/:customerId/googlePass')
  @SetMetadata('public', 'true')
  async generateGooglePassWithoutAuth(
    @Param() { brandId, customerId }: GeneratePassDto,
  ): Promise<string> {
    return await this.googlePassesService.generatePassWithoutAuth({
      brandId,
      customerId,
      forceUpdate: true,
    });
  }

  @Post('passes/googleWalletWebhook')
  @SetMetadata('public', 'true')
  async handleGoogleWalletWebhook(
    @Body() { signedMessage }: GoogleWalletWebhookDto,
  ) {
    const payload: GoogleWalletWebhookPayload = plainToInstance(
      GoogleWalletWebhookPayload,
      JSON.parse(signedMessage),
      { enableImplicitConversion: true },
    );
    return await this.googlePassesService.handleGoogleWalletWebhook(payload);
  }

  // https://developer.apple.com/documentation/walletpasses/send_an_updated_pass
  @Get('passes/:passTypeIdentifier/:serialNumber')
  @Header('Content-Description', 'File Transfer')
  @Header('Content-Type', 'application/vnd.apple.pkpass')
  @Header('Content-Transfer-Encoding', 'binary')
  @Header('Content-Disposition', 'attachment; filename=pass.pkpass')
  @SetMetadata('public', 'true')
  async getUpdatedPass(
    @Req() req: Request,
    @Param() passRequestParams: PassUpdateParams,
    @Response() res: ExpressResponse,
  ) {
    const { pass, lastModified }: GetUpdatedPassResponse =
      await this.pkpassPassesService.getUpdatedPass(
        req.headers.authorization,
        passRequestParams,
      );
    res.set({ 'Last-Modified': lastModified.toUTCString() });
    return pass.getAsStream().pipe(res);
  }

  // https://developer.apple.com/documentation/walletpasses/get_the_list_of_updatable_passes
  @Get('devices/:deviceLibraryIdentifier/registrations/:passTypeIdentifier')
  @SetMetadata('public', 'true')
  async listUpdatablePasses(
    @Param() passRegistrationParams: PassListParams,
    @Query() listPassesDto: ListPassesDto,
    @Response() res: ExpressResponse,
  ) {
    const response = await this.pkpassPassesService.listUpdatablePasses(
      passRegistrationParams,
      listPassesDto,
    );

    if (response.serialNumbers.length === 0) return res.sendStatus(204);
    return res.send(response);
  }

  // https://developer.apple.com/documentation/walletpasses/register_a_pass_for_update_notifications
  @Post(
    'devices/:deviceLibraryIdentifier/registrations/:passTypeIdentifier/:serialNumber',
  )
  @SetMetadata('public', 'true')
  async registerPass(
    @Req() req: Request,
    @Param() passRegistrationParams: PassRegistrationParams,
    @Body() body: RegisterPassDto,
  ) {
    return await this.pkpassPassesService.registerPass(
      req.headers.authorization,
      {
        ...passRegistrationParams,
        ...body,
      },
    );
  }

  // https://developer.apple.com/documentation/walletpasses/unregister_a_pass_for_update_notifications
  @Delete(
    'devices/:deviceLibraryIdentifier/registrations/:passTypeIdentifier/:serialNumber',
  )
  @SetMetadata('public', 'true')
  async unregisterPass(
    @Req() req: Request,
    @Param() passRegistrationParams: PassRegistrationParams,
  ) {
    return await this.pkpassPassesService.unregisterPass(
      req.headers.authorization,
      passRegistrationParams,
    );
  }

  @Post('log')
  @SetMetadata('public', 'true')
  async log(@Body() body: any) {
    return await this.integrationLogRepository.logSuccess(
      'log-passes',
      body,
      {},
    );
  }

  @Get('stamps.png')
  @Header('Content-Type', 'image/png')
  @Header('Cache-Control', 'no-cache, no-store')
  @Header('Pragma', 'no-cache')
  @Header('Expires', '-1')
  @SetMetadata('public', 'true')
  async generateStampImage(
    @Query() generateStampImageDto: GenerateStampImageDto,
    @Res() res: ExpressResponse,
  ) {
    const image = await this.passesImageService.generateStreamableImage(
      generateStampImageDto,
    );
    return res.send(image);
  }

  @Post('passes/register')
  @SetMetadata('public', 'true')
  async registerEnablePass(
    @Body() registerEnablePassDto: RegisterEnablePassDto,
  ) {
    return this.enablePassesService.registerPass(registerEnablePassDto);
  }
}
