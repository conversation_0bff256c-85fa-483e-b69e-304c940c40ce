// [EBL-6012] Wallet Pass Builder v1.0
// Migrate the following fields to `passConfig.stripImageConfig.stampsConfig.punchCardStamps`
// 1. `punchCard.emptyStamp` and `punchCard.filledStamp`
// 2. `punchCard.achievementStampOnEarn`
// 3. `punchCard.achievementStampOnRedeem`
// 4. `punchCard.achievements.$[].stampOnEarn`
// 5. `punchCard.achievements.$[].stampOnRedeem`

const cNameById = Object.fromEntries(
  db.companies
    .find({ _id: { $in: db.punchcards.distinct('companyId') } }, { name: 1 })
    .toArray()
    .map((c) => [c._id.toString(), c.name]),
);

db.pass_configs.bulkWrite(
  [
    ...db.punchcards
      .find(
        { emptyStamp: { $exists: true }, filledStamp: { $exists: true } },
        { companyId: 1, emptyStamp: 1, filledStamp: 1 },
      )
      .toArray()
      .map(({ _id, companyId, emptyStamp, filledStamp }) => ({
        updateOne: {
          filter: { 'owner._id': companyId },
          update: {
            $push: {
              'stripImageConfig.stampsConfig.punchCardStamps': {
                type: 'punch card',
                punchCardId: _id,
                emptyStamp: emptyStamp,
                filledStamp: filledStamp,
              },
            },
            $setOnInsert: {
              owner: {
                _id: companyId,
                name: cNameById[companyId.toString()],
                type: 'company',
              },
            },
          },
          upsert: true,
        },
      })),
    ...db.punchcards
      .find(
        { achievementStampOnEarn: { $exists: true } },
        { companyId: 1, achievementStampOnEarn: 1 },
      )
      .toArray()
      .map(({ _id, companyId, achievementStampOnEarn }) => ({
        updateOne: {
          filter: { 'owner._id': companyId },
          update: {
            $push: {
              'stripImageConfig.stampsConfig.punchCardStamps': {
                type: 'achievement on earn',
                punchCardId: _id,
                emptyStamp:
                  achievementStampOnEarn.emptyStamp ??
                  achievementStampOnEarn.image,
                filledStamp:
                  achievementStampOnEarn.filledStamp ??
                  achievementStampOnEarn.image,
              },
            },
            $setOnInsert: {
              owner: {
                _id: companyId,
                name: cNameById[companyId.toString()],
                type: 'company',
              },
            },
          },
          upsert: true,
        },
      })),
    ...db.punchcards
      .find(
        { achievementStampOnRedeem: { $exists: true } },
        { companyId: 1, achievementStampOnRedeem: 1 },
      )
      .toArray()
      .map(({ _id, companyId, achievementStampOnRedeem }) => ({
        updateOne: {
          filter: { 'owner._id': companyId },
          update: {
            $push: {
              'stripImageConfig.stampsConfig.punchCardStamps': {
                type: 'achievement on redeem',
                punchCardId: _id,
                emptyStamp:
                  achievementStampOnRedeem.emptyStamp ??
                  achievementStampOnRedeem.image,
                filledStamp:
                  achievementStampOnRedeem.filledStamp ??
                  achievementStampOnRedeem.image,
              },
            },
            $setOnInsert: {
              owner: {
                _id: companyId,
                name: cNameById[companyId.toString()],
                type: 'company',
              },
            },
          },
          upsert: true,
        },
      })),
    ...db.punchcards
      .find(
        { 'achievements.stampOnEarn': { $exists: true } },
        { companyId: 1, achievements: 1 },
      )
      .toArray()
      .map(({ _id, companyId, achievements }) =>
        achievements
          .filter((a) => a.stampOnEarn)
          .map((a) => ({
            updateOne: {
              filter: { 'owner._id': companyId },
              update: {
                $push: {
                  'stripImageConfig.stampsConfig.punchCardStamps': {
                    type: 'achievement on earn',
                    punchCardId: _id,
                    achievementId: a._id,
                    emptyStamp: a.stampOnEarn.emptyStamp ?? a.stampOnEarn.image,
                    filledStamp:
                      a.stampOnEarn.filledStamp ?? a.stampOnEarn.image,
                  },
                },
                $setOnInsert: {
                  owner: {
                    _id: companyId,
                    name: cNameById[companyId.toString()],
                    type: 'company',
                  },
                },
              },
              upsert: true,
            },
          })),
      ),
    ...db.punchcards
      .find(
        { 'achievements.stampOnRedeem': { $exists: true } },
        { companyId: 1, achievements: 1 },
      )
      .toArray()
      .map(({ _id, companyId, achievements }) =>
        achievements
          .filter((a) => a.stampOnRedeem)
          .map((a) => ({
            updateOne: {
              filter: { 'owner._id': companyId },
              update: {
                $push: {
                  'stripImageConfig.stampsConfig.punchCardStamps': {
                    type: 'achievement on redeem',
                    punchCardId: _id,
                    achievementId: a._id,
                    emptyStamp:
                      a.stampOnRedeem.emptyStamp ?? a.stampOnRedeem.image,
                    filledStamp:
                      a.stampOnRedeem.filledStamp ?? a.stampOnRedeem.image,
                  },
                },
                $setOnInsert: {
                  owner: {
                    _id: companyId,
                    name: cNameById[companyId.toString()],
                    type: 'company',
                  },
                },
              },
              upsert: true,
            },
          })),
      ),
  ].flat(),
);

// // Uncomment and run after verifying the results of the above script
// db.punchcards.updateMany(
//   {},
//   {
//     $unset: {
//       emptyStamp: '',
//       filledStamp: '',
//       achievementStampOnEarn: '',
//       achievementStampOnRedeem: '',
//       'achievements.$[].stampOnEarn': '',
//       'achievements.$[].stampOnRedeem': '',
//     },
//   },
// );
// db.customers.updateMany(
//   {},
//   {
//     $unset: {
//       'punchCardProgress.$[].punchCard.emptyStamp': '',
//       'punchCardProgress.$[].punchCard.filledStamp': '',
//       'punchCardProgress.$[].punchCard.achievementStampOnEarn': '',
//       'punchCardProgress.$[].punchCard.achievementStampOnRedeem': '',
//       'punchCardProgress.$[].punchCard.achievements.$[].stampOnEarn': '',
//       'punchCardProgress.$[].punchCard.achievements.$[].stampOnRedeem': '',
//     },
//   },
// );
// db.completedpunchcards.updateMany(
//   {},
//   {
//     $unset: {
//       'punchCard.emptyStamp': '',
//       'punchCard.filledStamp': '',
//       'punchCard.achievementStampOnEarn': '',
//       'punchCard.achievementStampOnRedeem': '',
//       'punchCard.achievements.$[].stampOnEarn': '',
//       'punchCard.achievements.$[].stampOnRedeem': '',
//     },
//   },
// );
