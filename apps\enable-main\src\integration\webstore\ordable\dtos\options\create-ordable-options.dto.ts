import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayNotEmpty,
  IsBoolean,
  IsInt,
  IsNotEmpty,
  IsString,
} from 'class-validator';
import { OrdableOptionsChoice } from '../../types/ordable-options-choice.type';

export class CreateOrdableOptionsDto {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  option_name: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  option_ar_name: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsInt()
  product_id: number;

  @ApiProperty({
    type: Number,
    required: false,
    minimum: 1,
  })
  @IsInt()
  minimum: number;

  @ApiProperty({
    type: Number,
    required: false,
    minimum: 1,
  })
  @IsInt()
  maximum: number;

  @ApiProperty({
    type: Number,
    required: false,
  })
  @IsInt()
  sort_order: number;

  @ApiProperty({
    type: Boolean,
    required: false,
  })
  @IsBoolean()
  is_required: boolean;

  @ApiProperty({
    type: Boolean,
    required: false,
  })
  @IsBoolean()
  multiple: boolean;

  @ApiProperty({
    type: [OrdableOptionsChoice],
    required: true,
    minItems: 1,
    description:
      'You need to create at least one choice. Additional choices can be created later using the ProductOptions API',
  })
  @ArrayNotEmpty()
  choices: OrdableOptionsChoice[];
}
