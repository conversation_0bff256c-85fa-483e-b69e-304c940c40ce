import {
  LoggerService,
  OrdableEntityDocument,
  OrdableInfo,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';

import { Types } from 'mongoose';
import { StoreProvider } from '../../../../../store/enumerations/store-provider.enum';
import { StoreDocument } from '../../../../../store/models/store.model';
import { StoreServiceInterface } from '../../../../../store/services/store.service.interface';
import { OrdableStoresServiceInterface } from './ordable-stores.service.interface';

@Injectable()
export class OrdableStoresService implements OrdableStoresServiceInterface {
  private readonly logger = new LoggerService(OrdableStoresService.name);
  constructor(
    @Inject('StoreServiceInterface')
    private readonly storeService: StoreServiceInterface,
  ) {}

  public async handleSyncCompleted(store: StoreDocument): Promise<void> {
    await store.updateOne({ needsSync: false });
  }

  public async getStoresForCompany(
    companyId: Types.ObjectId,
  ): Promise<StoreDocument[]> {
    const ordableStores = await this.storeService.findByCompanyId(companyId);
    if (!ordableStores || ordableStores.length === 0) return [];

    return ordableStores.filter(
      (store) => store.provider === StoreProvider.ORDABLE,
    );
  }

  public async forEachStore<ENTITY extends OrdableEntityDocument>(
    entity: ENTITY,
    action: (store: StoreDocument) => Promise<OrdableInfo | void>,
  ): Promise<ENTITY> {
    if (!entity) return entity;

    const stores = await this.getStoresForCompany(
      'company' in entity ? entity.company : entity.companyId,
    );
    if (!stores || stores.length === 0) return entity;

    const ordableStores = entity.ordableStores || {};
    for (const store of stores) {
      try {
        const ordableInfo = await action(store);
        if (ordableInfo) ordableStores[store._id.toHexString()] = ordableInfo;
      } catch (err) {
        this.logger.error(err);
      }
    }

    entity.ordableStores = ordableStores;
    await entity.updateOne({ ordableStores });
    return entity;
  }
}
