
name: Enable Main Service (Testing)

# Controls when the workflow will run

on:
  push:
    branches:
    - testing
    paths:
    - 'apps/enable-main/**'
    - 'libs/**'
  pull_request:
    types:
    - closed
    branches:
    - testing
    paths:
    - 'apps/enable-main/**'
    - 'libs/**'

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  Building:
    if: (github.event.pull_request && github.event.pull_request.merged == 'true') || (github.event.pusher)
    environment: Testing
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v2

      - name: Building And Reloading
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOSTING_URL }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SECRET_KEY }}
          passphrase: ${{ secrets.KEY_PASSPHARE }} 
          port: ${{ secrets.PORT }}
          script: |
            cd ~/enable-backend/enable-main
            ssh-agent bash -c "ssh-add /home/<USER>/.ssh/id_rsa; git reset --hard origin/testing"
            ssh-agent bash -c "ssh-add /home/<USER>/.ssh/id_rsa; git pull"
            changed_files="$(git diff-tree -r --name-only --no-commit-id ORIG_HEAD HEAD)"
            check_run() {
                if echo "$changed_files" | grep -q "$1"; then
                echo "Found diff in $1"
                eval "$2"
                else
                echo "Not found any diff in $1"
                fi
            }
            check_run package.json "npm ci"
            npm run build enable-main
            pm2 reload enable-main-testing
