import {
  CollectionName,
  GenericRepository,
  LoyaltyTier,
  LoyaltyTierDocument,
  LoyaltyTierThresholds,
  LoyaltyTierWithIdDto,
  TierWithRequirements,
} from '@app/shared-stuff';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { FilterQuery, Model, Types } from 'mongoose';
import { LoyaltyTierRepositoryInterface } from './loyalty-tier.repository.interface';

@Injectable()
export class LoyaltyTierRepository
  extends GenericRepository<LoyaltyTierDocument, LoyaltyTier>
  implements LoyaltyTierRepositoryInterface
{
  constructor(
    @InjectModel(CollectionName.LOYALTY_TIER)
    private loyaltyTierModel: Model<LoyaltyTierDocument, LoyaltyTier>,
  ) {
    super(loyaltyTierModel);
  }

  async find(
    filter: FilterQuery<LoyaltyTierDocument>,
  ): Promise<LoyaltyTierDocument[]> {
    return await this.loyaltyTierModel
      .find({
        ...filter,
        deletedAt: null,
      })
      .sort({ tierIndex: 1 });
  }

  async findNextTier(
    companyId: Types.ObjectId,
    tierIndex?: number,
    mustHaveAmountSpent?: boolean,
  ): Promise<LoyaltyTierDocument> {
    return this.loyaltyTierModel
      .findOne({
        companyId,
        ...(tierIndex != null && { tierIndex: { $gt: tierIndex } }),
        ...(mustHaveAmountSpent && { amountSpentThreshold: { $gt: 0 } }),
        $or: [{ isVipTier: { $exists: false } }, { isVipTier: false }],
        deletedAt: null,
      })
      .sort({ tierIndex: 1 });
  }

  async findPreviousTier(
    companyId: Types.ObjectId,
    tierIndex: number,
    mustHaveAmountSpent?: boolean,
  ): Promise<LoyaltyTierDocument> {
    if (tierIndex === 0) return null;
    return await this.loyaltyTierModel
      .findOne({
        companyId,
        tierIndex: { $lt: tierIndex },
        ...(mustHaveAmountSpent && { amountSpentThreshold: { $gt: 0 } }),
        $or: [{ isVipTier: { $exists: false } }, { isVipTier: false }],
        deletedAt: null,
      })
      .sort({ tierIndex: -1 });
  }

  async findByCompanyId(
    companyId: Types.ObjectId,
  ): Promise<LoyaltyTierDocument[]> {
    return this.loyaltyTierModel
      .find({
        companyId,
        deletedAt: null,
      })
      .sort({ tierIndex: 1 });
  }

  async findNonVipTiersByCompanyId(
    companyId: Types.ObjectId,
  ): Promise<LoyaltyTierDocument[]> {
    return this.loyaltyTierModel
      .find({
        companyId,
        $or: [{ isVipTier: { $exists: false } }, { isVipTier: false }],
        deletedAt: null,
      })
      .sort({ tierIndex: 1 });
  }

  async findHighestTier(
    companyId: Types.ObjectId,
  ): Promise<LoyaltyTierDocument> {
    const loyaltyTiers = await this.loyaltyTierModel
      .find({ companyId, deletedAt: null })
      .sort({ tierIndex: -1 })
      .limit(1);

    if (!loyaltyTiers || loyaltyTiers.length === 0) return null;

    return loyaltyTiers[0];
  }

  async bulkUpdate(
    tiers: LoyaltyTierWithIdDto[],
  ): Promise<LoyaltyTierDocument[]> {
    const tierGroups = this.groupByCompany(tiers);

    const updatePromises = [...tierGroups.entries()].map(([companyId, tiers]) =>
      this.bulkUpdateByCompany(new Types.ObjectId(companyId), tiers),
    );

    const updateResults = await Promise.all(updatePromises);
    return updateResults.flat();
  }

  private groupByCompany(
    tiers: LoyaltyTierWithIdDto[],
  ): Map<Types.ObjectId, LoyaltyTierWithIdDto[]> {
    return tiers.reduce((map, tier) => {
      const companyId = tier.companyId.toHexString();

      if (!map.has(companyId)) map.set(companyId, []);
      map.set(companyId, [...map.get(companyId), tier]);

      return map;
    }, new Map());
  }

  private async bulkUpdateByCompany(
    companyId: Types.ObjectId,
    tiers: LoyaltyTierWithIdDto[],
  ): Promise<LoyaltyTierDocument[]> {
    const existingTiers = await this.findByCompanyId(companyId);
    this.validateTiersExist(existingTiers, tiers);
    const updatedTiers = this.applyUpdate(existingTiers, tiers);
    this.validateUniqueTierIndexes(updatedTiers);

    await this.setDummyTierIndexes(updatedTiers);
    return Promise.all(
      tiers.map(({ _id, ...tier }) => this.findOneAndUpdate({ _id }, tier)),
    );
  }

  private validateTiersExist(
    existingTiers: LoyaltyTierDocument[],
    tiersToCheck: LoyaltyTierWithIdDto[],
  ) {
    const existingTierIds = existingTiers.map((tier) => tier._id.toHexString());
    const tiersToCheckIds = tiersToCheck.map((tier) => tier._id.toHexString());

    tiersToCheckIds.forEach((tierId) => {
      if (!existingTierIds.includes(tierId))
        throw new NotFoundException(`Tier ${tierId} does not exist`);
    });
  }

  private applyUpdate(
    existingTiers: LoyaltyTierDocument[],
    updatedTiers: LoyaltyTierWithIdDto[],
  ): LoyaltyTierDocument[] {
    return existingTiers.map((existingTier) => {
      const updatedTier = updatedTiers.find(
        (updatedTier) =>
          updatedTier._id.toHexString() === existingTier._id.toHexString(),
      );

      Object.entries(updatedTier ?? {}).forEach(([key, value]) => {
        existingTier[key] = value;
      });

      return existingTier;
    });
  }

  private validateUniqueTierIndexes(tiers: LoyaltyTierDocument[]) {
    const duplicateTierIndexes = tiers
      .map((tier) => tier.tierIndex)
      .filter((tierIndex, index, array) => array.indexOf(tierIndex) !== index);

    if (duplicateTierIndexes.length > 0)
      throw new BadRequestException(
        `Duplicate tier indexes found: ${duplicateTierIndexes.join(', ')}`,
      );
  }

  private async setDummyTierIndexes(
    tiers: LoyaltyTierDocument[],
  ): Promise<void> {
    // Negate all tier indexes temporarily to avoid duplicate index errors
    const tierIds = tiers.map((tier) => tier._id);
    await this.loyaltyTierModel.updateMany(
      { _id: { $in: tierIds } },
      { $inc: { tierIndex: -Math.round(Math.random() * 1000 - 100) } },
    );
  }

  async findByEnrollmentCode(
    enrollmentCode: string,
  ): Promise<LoyaltyTierDocument> {
    return await this.loyaltyTierModel.findOne({
      enrollmentCode,
      deletedAt: null,
    });
  }

  async applyTierRequirementsUpdate(
    tiers: TierWithRequirements[],
  ): Promise<void> {
    const writes = tiers.map(({ _id, ...thresholds }) => ({
      updateOne: {
        filter: { _id },
        update: [
          { $set: thresholds },
          {
            $unset: LoyaltyTierThresholds.filter(
              (threshold) =>
                thresholds[threshold] === null ||
                thresholds[threshold] === undefined,
            ),
          },
        ],
      },
    }));

    await this.loyaltyTierModel.bulkWrite(writes, { ordered: false });
  }

  async delete(loyaltyTier: LoyaltyTierDocument): Promise<LoyaltyTierDocument> {
    const deletedAt = moment.utc().toDate();
    const oldTierIndex = loyaltyTier.tierIndex;
    const newTierIndex = -deletedAt.valueOf();

    const deletedTier = await this.findOneAndUpdate(
      { _id: loyaltyTier._id },
      { $set: { deletedAt, tierIndex: newTierIndex } },
    );

    await this.loyaltyTierModel.updateMany(
      {
        companyId: loyaltyTier.companyId,
        tierIndex: { $gte: oldTierIndex },
        deletedAt: null,
      },
      { $inc: { tierIndex: -1 } },
    );

    return deletedTier;
  }
}
