import { OrderLogReceivedObjectDto } from '@app/shared-stuff/dtos/order/order-log-received-object.dto';
import { OrderLogSentObjectDto } from '@app/shared-stuff/dtos/order/order-log-sent-object.dto';
import { OrderLogActionEnum } from '@app/shared-stuff/enums/order/order-log-action.enum';
import { InjectModel } from '@nestjs/mongoose';
import { OrderLogRepositoryInterface } from './interfaces/order-log.repository.interface';
import { Model } from 'mongoose';
import { OrderLogDocument } from '../models/order.log.model';
import { PipelineStage } from 'mongoose';
import { Injectable } from '@nestjs/common';

@Injectable()
export class OrderLogRepository implements OrderLogRepositoryInterface {
  constructor(
    @InjectModel('OrderLog') private orderLogModel: Model<OrderLogDocument>,
  ) {}

  async aggregate(pipeline: PipelineStage[]): Promise<OrderLogDocument[]> {
    return await this.orderLogModel.aggregate(pipeline);
  }
  async save(orderLog: {
    sentObject: OrderLogSentObjectDto;
    receivedObject: OrderLogReceivedObjectDto;
    logAction: OrderLogActionEnum;
    orderCode: string;
  }) {
    const newOrderLog = new this.orderLogModel(
      orderLog,
      //   sentObject:orderLog.sentObject,
      //   receivedObject: orderLog.receivedObject,
      //   logAction: orderLog.logAction,
      //   orderCode: orderLog.orderCode,
    );
    await newOrderLog.save();
  }
}
