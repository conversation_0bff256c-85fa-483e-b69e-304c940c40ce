import {
  CollectionName,
  CompanyDocument,
  ContactChannel,
  CountryDialCode,
  DocumentRedirectDocument,
  GenericTriggerModel,
  IndexCustomerDto,
  Language,
  LanguageCode,
  PusherService,
  TriggerAction,
  TriggerModule,
  WebStoreToPromote,
} from '@app/shared-stuff';
import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import { Model, Types } from 'mongoose';
import { BranchService } from '../../../branch/services/branch/branch.service';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { CompanyService } from '../../../company/services/company/company.service';
import { CustomerIndexServiceInterface } from '../../../customer/modules/customer-index/customer-index.service.interface';
import { CustomerWriteServiceInterface } from '../../../customer/modules/customer-write/customer-write.service.interface';
import { TriggerService } from '../../../notification/services/trigger/trigger.service';
import { OrderIndex } from '../../../order/dto/order.dto';
import { OrderService } from '../../../order/services/order/order.service';
import { PaymentIndex } from '../../../payment/dto/payment.dto';
import { PaymentService } from '../../../payment/services/payment/payment.service';
import { StoreServiceInterface } from '../../../store/services/store.service.interface';
import { CutomerOnCallDto } from '../../dto/customer-on-call.dto';
import { WebStoreConfigurationDto } from '../../dto/webstore-configuration.dto';
import { WebStoreReplacements } from '../../types/webstore-replacement.type';

@Injectable()
export class MainService {
  QATAR_COUNTRY_CODE = '974';

  constructor(
    private orderService: OrderService,
    @Inject(CustomerIndexServiceInterface)
    private customerIndexService: CustomerIndexServiceInterface,
    @Inject(CustomerWriteServiceInterface)
    private customerWriteService: CustomerWriteServiceInterface,
    private paymentService: PaymentService,
    private branchService: BranchService,
    private companyService: CompanyService,
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandServiceInterface,
    @Inject('StoreServiceInterface')
    private readonly storeService: StoreServiceInterface,
    private triggerService: TriggerService,
    private pusherService: PusherService,
    @InjectModel(CollectionName.DOCUMENT_REDIRECT)
    private readonly documentRedirectModel: Model<DocumentRedirectDocument>,
  ) {}

  async getDocumentUrl(name: string): Promise<string> {
    const document = await this.documentRedirectModel.findOne({ name });

    if (!document || !document.url) throw new NotFoundException();

    return document.url;
  }

  async search(search_keyword, company_id) {
    const orderIndex: OrderIndex = new OrderIndex();
    const customerIndex = new IndexCustomerDto();
    const paymentIndex = new PaymentIndex();

    orderIndex.search_key = search_keyword;
    customerIndex.search_key = search_keyword;

    paymentIndex.search_key = search_keyword;

    if (company_id) {
      orderIndex.company = company_id;
      customerIndex.company = company_id;
      paymentIndex.company = company_id;
    }

    // const orders = await this.orderService.index(orderIndex, '_id code total_amount last_contact payment_method pickup_date delivery_date status payment_status source delivery_action');
    let orders = await this.orderService.index(orderIndex);
    const customers = await this.customerIndexService.index(customerIndex);
    const payments = await this.paymentService.refactoredIndex(paymentIndex);

    orders = orders[0]['paginatedResults'];

    return { orders, customers, payments };
  }

  async webStorePromote(webStoreToPromote: WebStoreToPromote): Promise<string> {
    const branch = await this.branchService.get_details(
      webStoreToPromote.branchId,
    );
    const brand = await this.brandService.findById(
      new Types.ObjectId(webStoreToPromote.brandId),
    );
    const company = await this.companyService.get_details(
      webStoreToPromote.companyId.toHexString(),
    );
    const customer = await this.fetchingCustomerInfo(
      webStoreToPromote.phoneNumber,
      webStoreToPromote.firstName,
      webStoreToPromote.lastName,
      webStoreToPromote.countryCode,
      webStoreToPromote.language,
      company,
    );
    let store = undefined;
    if (company._id && brand._id) {
      store = await this.storeService.index({
        brandId: brand?._id,
        companyId: company?._id,
        sortType: undefined,
        provider: undefined,
      });
    }

    await this.handleWebStoreFiringTrigger({
      company: company,
      brand: brand,
      branch: branch as any,
      customer: customer,
      senderId: brand?.senderId,
      language:
        webStoreToPromote.language === Language.arabic
          ? LanguageCode.ar
          : LanguageCode.en,
      webStoreLink: store[0]['paginatedResult'][0]?.link
        ? store[0]['paginatedResult'][0].link
        : branch?.externalLinks?.webStoreLink,
    });
    return 'Done';
  }

  private async fetchingCustomerInfo(
    phoneNumber: string,
    firstName: string,
    lastName: string,
    countryCode: CountryDialCode,
    language: Language,
    company: CompanyDocument,
  ) {
    const customer = await this.customerWriteService.findOrCreate({
      first_name: firstName,
      last_name: lastName,
      phone: phoneNumber,
      country_code: countryCode,
      company: company._id,
      language: language,
      contact_channel: ContactChannel.UNKNOWN, // or WEBSTORE?
    });

    return customer;
  }

  public pushCustomerCallEvent(customerOnCallDto: CutomerOnCallDto) {
    this.pusherService.fireEvent(
      customerOnCallDto.userId,
      'customerPhoneNumber',
      customerOnCallDto,
    );
  }

  private async handleWebStoreFiringTrigger(
    webStoreConfigurationDto: WebStoreConfigurationDto,
  ) {
    const genericTriggerModel = await this.populateGenericTriggerModel(
      webStoreConfigurationDto,
    );
    await this.triggerService.fireTrigger(
      genericTriggerModel,
      TriggerAction.ON_PROMOTE_WEBSTORE,
    );
  }

  async populateGenericTriggerModel(
    webStoreConfigurationDto: WebStoreConfigurationDto,
  ): Promise<GenericTriggerModel> {
    const replacements = this.getAllWebStoreReplacements(
      webStoreConfigurationDto,
    );
    return {
      companyId: this.getCompanyId(webStoreConfigurationDto),
      branchId: webStoreConfigurationDto?.branch?._id.toHexString(),
      brandId: webStoreConfigurationDto?.brand?.['_id'],
      customerId: new Types.ObjectId(webStoreConfigurationDto.customer._id),
      countryCode: CountryDialCode.QATAR,
      createdBy: undefined,
      giftRecipientUser: undefined,
      isGift: undefined,
      senderId: webStoreConfigurationDto?.senderId,
      emailSenderId: webStoreConfigurationDto?.senderId,
      triggerModule: TriggerModule.CALL_CENTER,
      replacements: replacements,
      language: webStoreConfigurationDto.language,
      context: {
        customer: {
          ...webStoreConfigurationDto?.customer?.toJSON(),
          _id: webStoreConfigurationDto?.customer?._id,
        },
      },
    };
  }

  private getCompanyId(
    webStoreConfigurationDto: WebStoreConfigurationDto,
  ): string {
    if (webStoreConfigurationDto.company)
      return webStoreConfigurationDto.company._id.toString();
    else if (
      webStoreConfigurationDto.branch &&
      webStoreConfigurationDto.branch.company._id
    )
      return webStoreConfigurationDto.branch.company._id.toString();
    else if (
      webStoreConfigurationDto.branch &&
      !webStoreConfigurationDto.branch.company._id
    )
      return webStoreConfigurationDto.branch.company.toString();
  }

  private getAllWebStoreReplacements(
    webStoreConfigurationDto: WebStoreConfigurationDto,
  ): WebStoreReplacements {
    return {
      webStoreLink: webStoreConfigurationDto?.webStoreLink,
      firstName: webStoreConfigurationDto?.customer?.first_name,
      language: webStoreConfigurationDto?.language,
    };
  }
}
