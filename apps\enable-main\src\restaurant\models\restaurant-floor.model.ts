import { CollectionName } from '@app/shared-stuff';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { HydratedDocument, Types } from 'mongoose';

export type FloorDocument = HydratedDocument<Floor>;
@Schema({ timestamps: true })
export class Floor {
  @Prop({
    type: String,
    required: true,
  })
  name: string;

  @Prop({
    type: String,
    required: false,
  })
  internalId: string;

  @Prop({
    type: String,
    required: false,
  })
  description: string;

  @Prop({
    type: Number,
    required: false,
  })
  capacity: number;

  @Prop({
    type: String,
    required: false,
  })
  deliverectId: string;

  @Prop({
    type: String,
    required: false,
  })
  companyName: string;

  @Prop({
    type: String,
    required: false,
  })
  branchName: string;

  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: CollectionName.BRANCH,
  })
  branch: Types.ObjectId;

  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: CollectionName.COMPANY,
  })
  company: Types.ObjectId;

  @Prop({
    type: [Types.ObjectId],
    required: false,
    ref: 'Table',
  })
  tables: Types.ObjectId[];

  @Prop({
    type: {},
    required: false,
  })
  updatedBy: {};

  @Prop({
    type: {},
    required: false,
  })
  createdBy: {};

  @Prop({
    type: {},
    required: false,
  })
  assignedTo: {};

  @Prop({
    type: {},
    required: false,
  })
  deletedBy: {};

  @Prop({
    type: Date,
    required: false,
  })
  deletedAt: Date;

  @Prop({
    type: String,
    default: moment().startOf('month').format('MMMM'),
  })
  month: string;

  @Prop({
    type: Number,
    default: moment().week(),
  })
  week: number;

  @Prop({
    type: Number,
    default: moment().date(),
  })
  day: number;

  @Prop({
    type: Number,
    default: moment().year(),
  })
  year: number;
}

const FloorSchema = SchemaFactory.createForClass(Floor);

export { FloorSchema };
