import { Module } from '@nestjs/common';
import { BrandModule } from '../../../brand/brand.module';
import { CompanyModule } from '../../../company/company.module';
import { OrdableModule } from '../../../integration/webstore/ordable/ordable.module';
import { CustomerLoyaltyModule } from '../customer-loyalty/customer-loyalty.module';
import { CustomerNotificationModule } from '../customer-notification/customer-notification.module';
import { CustomerRepositoryModule } from '../customer-repository/customer-repository.module';
import { CustomerPromoteService } from './customer-promote.service';
import { CustomerPromoteServiceInterface } from './customer-promote.service.interface';

@Module({
  providers: [
    {
      provide: CustomerPromoteServiceInterface,
      useClass: CustomerPromoteService,
    },
  ],
  imports: [
    OrdableModule,
    CompanyModule,
    BrandModule,
    CustomerRepositoryModule,
    CustomerNotificationModule,
    CustomerLoyaltyModule,
  ],
  exports: [CustomerPromoteServiceInterface],
})
export class CustomerPromoteModule {}
