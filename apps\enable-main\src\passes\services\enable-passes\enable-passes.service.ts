import {
  IMAGE_URLS,
  LanguageToLanguageCode,
  PassTemplateType,
  RegisteredPass,
  WalletApp,
} from '@app/shared-stuff';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as moment from 'moment-timezone';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { CustomerPassServiceInterface } from '../../../customer/modules/customer-pass/customer-pass.service.interface';
import { CustomerReadServiceInterface } from '../../../customer/modules/customer-read/customer-read.service.interface';
import { EnableWalletPass } from '../../dto/enable-wallet-pass.dto';
import { GeneratePassDto } from '../../dto/generate-pass.dto';
import { RegisterEnablePassDto } from '../../dto/register-enable-pass.dto';
import { PassConfigService } from '../pass-config/pass-config.service';
import { PassesImageServiceInterface } from '../passes-image/passes-image.service.interface';
import { PassesService } from '../passes.service';

@Injectable()
export class EnablePassesService {
  constructor(
    @Inject(CustomerReadServiceInterface)
    private readonly customerReadService: CustomerReadServiceInterface,
    @Inject(CustomerPassServiceInterface)
    private readonly customerPassService: CustomerPassServiceInterface,
    @Inject(PassesImageServiceInterface)
    private readonly passesImageService: PassesImageServiceInterface,
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandServiceInterface,
    private readonly passConfigService: PassConfigService,
    private readonly passesService: PassesService,
    private readonly configService: ConfigService,
  ) {}

  async generateEnablePass({
    brandId,
    customerId,
  }: GeneratePassDto): Promise<EnableWalletPass> {
    const brand = await this.brandService.findById(brandId);
    const customer = await this.customerReadService.findById(customerId);

    const passFieldContext = await this.passesService.getPassFieldContext(
      customer,
      brand,
    );

    const passConfig = await this.passConfigService.getPassConfig(
      brand.companyId,
      brand._id,
      customer.loyaltyTier?._id,
    );

    // TODO: remove after deleting FILE templates
    if (passConfig?.passTemplate?.type === PassTemplateType.FILE)
      throw new BadRequestException({
        message: `Cannot generate pass for outdated pass template.`,
        statusCode: 400,
        data: {
          companyId: brand.companyId,
          brandId,
          customer: customer._id,
          tierId: customer.loyaltyTier?._id,
        },
      });

    await this.customerPassService.applyPassGenerationPreFunctions(customer);

    return {
      loyaltyCardName: { en: brand.name, ar: brand.name },
      language: LanguageToLanguageCode[customer.language],
      backgroundColor: passConfig?.passTemplate?.backgroundColor ?? '#000',
      textColor: passConfig?.passTemplate?.valueTextColor ?? '#fff',
      logo: passConfig?.passTemplate?.logo ??
        brand.image ?? { url: IMAGE_URLS.ENABLE_LOGO },
      icon: passConfig?.passTemplate?.icon ?? { url: IMAGE_URLS.ENABLE_LOGO },
      stripImage: {
        url: this.passesImageService.generateImageUrl(customer, brand),
      },
      headerFields: await this.passesService.getHeaderFields(
        passConfig,
        passFieldContext,
      ),
      primaryField: (
        await this.passesService.getPrimaryFields(passConfig, passFieldContext)
      )[0],
      secondaryFields: await this.passesService.getSecondaryFields(
        passConfig,
        passFieldContext,
      ),
      qrCode: await this.passesService.getQRCode(customer, passConfig),
    };
  }

  async registerPass(registerPassDto: RegisterEnablePassDto) {
    const customer = await this.customerReadService.findById(
      registerPassDto.customerId,
    );
    const brand = await this.brandService.findById(registerPassDto.brandId);

    const newPass: RegisteredPass = {
      deviceLibraryIdentifier: registerPassDto.deviceLibraryIdentifier ?? '',
      passTypeIdentifier:
        registerPassDto.passTypeIdentifier ||
        new URL(
          brand.loyaltyProgramConfig?.pwaBaseUrl ||
            this.configService.get<string>('DEFAULT_ENABLE_WALLET_LINK'),
        ).hostname,
      serialNumber: registerPassDto.serialNumber ?? '',
      pushToken: '',
      walletApp: WalletApp.ENABLE_WALLET,
      deviceData: customer.deviceData,
      createdAt: moment.utc().toDate(),
      updatedAt: moment.utc().toDate(),
    };
    await this.passesService.saveNewPass(customer, newPass);
  }
}
