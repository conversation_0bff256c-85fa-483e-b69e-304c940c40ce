import { SharedStuffModule } from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { forwardRef } from '@nestjs/common/utils';
import { CompanyModule } from '../../../company/company.module';
import { CustomerRepositoryModule } from '../customer-repository/customer-repository.module';
import { CustomerIndexService } from './customer-index.service';
import { CustomerIndexServiceInterface } from './customer-index.service.interface';

@Module({
  providers: [
    {
      provide: CustomerIndexServiceInterface,
      useClass: CustomerIndexService,
    },
  ],
  imports: [
    forwardRef(() => CompanyModule),
    SharedStuffModule,
    CustomerRepositoryModule,
  ],
  exports: [CustomerIndexServiceInterface],
})
export class CustomerIndexModule {}
