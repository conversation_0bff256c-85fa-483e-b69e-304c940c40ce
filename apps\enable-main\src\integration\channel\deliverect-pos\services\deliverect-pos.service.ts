import {
  CompanyDocument,
  DeliveryMethod,
  HelperSharedServiceInterface,
  IHelperSharedService,
  Language,
  LoggerService,
  MenuGroupDocument,
  MenuItemDocument,
  OrderCreationSource,
  OrderDeliveryAction,
  OrderDeliveryType,
  OrderDocument,
  OrderItem,
  OrderLogActionEnum,
  OrderPaymentMethod,
  OrderPosToCreate,
  OrderSource,
  OrderStatusEnum,
  OrderTransitionTrigger,
  TransportType,
} from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import * as randomString from 'randomstring';
import { BranchService } from '../../../../branch/services/branch/branch.service';
import { CompanyService } from '../../../../company/services/company/company.service';
import { OrderPosService } from '../../../../order/services/order-pos/order-pos.service';
import { OrderStatusService } from '../../../../order/services/order-status/order-status.service';
import { OrderService } from '../../../../order/services/order/order.service';
import { MenuCategoryService } from '../../../../restaurant/services/menu-category/menu-category.service';
import { MenuGroupService } from '../../../../restaurant/services/menu-group/menu-group.service';
import { MenuItemService } from '../../../../restaurant/services/menu-item/menu-item.service';
import { MenuService } from '../../../../restaurant/services/menu/menu.service';
import { RestaurantFloorService } from '../../../../restaurant/services/restaurant-floor/restaurant-floor.service';
import { RestaurantTableService } from '../../../../restaurant/services/restaurant-table/restaurant-table.service';

import { CompanyIntegratedChannel } from '@app/shared-stuff';
import { OrderLogServiceInterface } from '../../../../order/services/interfaces/order-log.service.interface';
import { DeliverectCategory } from '../dto/deliverect-category.dto';
import { DeliverectFloor } from '../dto/deliverect-floor.dto';
import { DeliverectOrderStatusToUpdate } from '../dto/deliverect-order-status.dto';
import { DeliverectProduct } from '../dto/deliverect-product.dto';
import { DeliverectTable } from '../dto/deliverect-table.dto';
import { RegisterNewPosDto } from '../dto/register-new-pos.dto';

@Injectable()
export class DeliverectPosService {
  private readonly loggerService = new LoggerService(DeliverectPosService.name);

  deliverectToken = '';
  deliverectTokenExpire;
  deliverectBaseUrl = '';

  constructor(
    @Inject('OrderLogServiceInterface')
    private readonly orderLogService: OrderLogServiceInterface,
    private branchService: BranchService,
    private companyService: CompanyService,
    private orderService: OrderService,
    private orderPosService: OrderPosService,
    private menuItemService: MenuItemService,
    private menuService: MenuService,
    private configService: ConfigService,
    private menuCategoryService: MenuCategoryService,
    private menuGroupService: MenuGroupService,
    private floorService: RestaurantFloorService,
    private tableService: RestaurantTableService,
    private http: HttpService,
    private eventEmitter: EventEmitter2,
    private orderStatusService: OrderStatusService,
    @Inject(IHelperSharedService)
    private helperSharedService: HelperSharedServiceInterface,
  ) {
    this.deliverectBaseUrl =
      this.configService.get('ENVIRONMENT') == 'development'
        ? this.configService.get('DELIVERECT_STAGING')
        : this.configService.get('DELIVERECT_PRODUCTION');
  }

  async getLinkedAccounts(companyId: string) {
    return new Promise(async (resolve, reject) => {
      try {
        const company = await this.companyService.get_details(companyId);
        const token = await this.getDeliverectAuthenticatedToken(company);
        if (!token) return;
        this.http
          .get(this.deliverectBaseUrl + 'accounts', {
            headers: { Authorization: `Bearer ${token}` },
          })
          .subscribe(
            (data) => {
              resolve(data.data);
            },
            (err) => {
              reject(err);
            },
          );
      } catch (err: unknown) {
        reject(err);
      }
    });
  }

  async getLocations(companyId: string) {
    return new Promise(async (resolve, reject) => {
      try {
        const company = await this.companyService.get_details(companyId);
        const token = await this.getDeliverectAuthenticatedToken(company);
        if (!token) return;
        this.http
          .get(this.deliverectBaseUrl + 'locations', {
            params: { where: { account: company.deliverectAccountId } },
            headers: { Authorization: `Bearer ${token}` },
          })
          .subscribe(
            (data) => {
              resolve(data.data);
            },
            (err) => {
              reject(err);
            },
          );
      } catch (err) {
        reject(err);
      }
    });
  }

  // Webhooks (Will Be Called From Deliverect Side)

  //When receiving a new Order From Deliverect Channel
  async onNewOrder(data: any) {
    // Checking if the order already existed before
    const orderCheck = await this.orderService.get_details(
      data['channelOrderId'],
    );

    const branch = await this.branchService.get_details(data['posLocationId']);
    if (!branch) return;
    const company = await this.companyService.get_details(
      branch.company['_id'],
    );
    if (!company) return;

    //Finding the order Channel Name
    let deliverectChannelName;
    if (
      company.deliverectIntegratedChannels &&
      company.deliverectIntegratedChannels.length
    ) {
      const channelName = company.deliverectIntegratedChannels.find(
        (x) => x.channelId == data['channel'],
      );
      deliverectChannelName = channelName?.name;
    }

    if (orderCheck && data['status'] != '100') {
      orderCheck.deliverectOrder = true;
      orderCheck.deliverectPosOrderId = data['_id'];
      orderCheck.deliverectPosOrderDisplayId = data['channelOrderId'];
      orderCheck.deliverectChannelName = deliverectChannelName;

      await orderCheck.save();
      return { data: orderCheck, status: 'updated' };
    }

    if (data['status'] == '100') {
      if (orderCheck) {
        await this.orderStatusService.changeOrderStatus(
          orderCheck,
          OrderStatusEnum.CANCELED,
          OrderTransitionTrigger.CANCEL,
          orderCheck.createdBy,
        );
      }
      return {
        posOrderId: orderCheck ? orderCheck.code : 'XXXXX',
        posReceiptId: orderCheck ? orderCheck.code : 'XXXXX',
      };
    }
    const customerInfo = this.getCustomerInfo(data['customer'], company);
    const deliveryDate = data['deliveryTime']
      ? moment.tz(data['deliveryTime'], data['timezone']).utc()
      : moment
          .tz(data['pickupTime'], data['timezone'])
          .utc()
          .add(20, 'minutes');
    const pickupDate = moment.tz(data['pickupTime'], data['timezone']).utc();
    const orderPosToCreate: OrderPosToCreate = {
      first_name: customerInfo.name,
      last_name: '',
      phone: customerInfo.phone,
      email: customerInfo.email,
      autoAssign: false,
      branch: branch._id.toString(),
      company: company._id.toString(),
      country_code: company.localization?.countryCode,
      isCustomerUpdatable: false,
      deliverectPosOrderId: data['_id'],
      // delivery_action:
      //   data['orderType'] == 1
      //     ? OrderDeliveryAction.IN_STORE_PICKUP
      //     : OrderDeliveryAction.DELIVERY_LOCATION,
      delivery_action: OrderDeliveryAction.IN_STORE_PICKUP,
      deliveryParty: undefined,
      transport_type: TransportType[TransportType.Bicycle],
      deliveryLocation: undefined,
      // deliveryLocation:
      //   data['orderType'] == 1
      //     ? undefined
      //     : {
      //         latitude: 1,
      //         longitude: 1,
      //         pinLink: 'https://maps.google.com?q=1,1',
      //         unitNumber: 0,
      //         buildingName: data['deliveryAddress']['street'] || 'Not Provided',
      //         nickname: data['deliveryAddress']['street'],
      //         city: data['deliveryAddress']['city'] || 'Not Provided',
      //         type: SavedLocationType.PIN_LOCATION,
      //         streetNumber: 0,
      //         zoneNumber: 0,
      //         buildingNumber: 0,
      //         customerId: undefined,
      //         area: 'Doha',
      //         country: 'Qatar',
      //         streetName: data['deliveryAddress']['street'] || 'Not Provided',
      //         additionalInfo:
      //           data['deliveryAddress']['extraAddressInfo'] || 'Not Provided',
      //         addressType: SavedLocationAddressType.APARTMENT,
      //         floorNumber: 0,
      //         nearestLandmark: '',
      //       },
      code: '',
      cardMessage: '',
      callback_url: '',
      current_user: undefined,
      customer: undefined,
      delivery_amount:
        data['deliveryCost'] /
        parseInt(String(1).padEnd(data['decimalDigits'] + 1, '0')),
      delivery_date: undefined,
      delivery_time: undefined,
      pickup_date: pickupDate.format('YYYY-MM-DD'),
      pickup_time: pickupDate.format('HH:mm'),
      payment_method: data['payment']['type'] == 1 ? 'cash' : 'prepaid',
      delivery_slot_from: '',
      delivery_slot_to: '',
      delivery_slot_id: '',
      delivery_type: data['deliveryIsAsap']
        ? OrderDeliveryType.urgent
        : OrderDeliveryType.scheduled,
      dispatch_type: data['deliveryIsAsap']
        ? OrderDeliveryType.urgent
        : OrderDeliveryType.scheduled,
      discount:
        (data['discountTotal'] /
          parseInt(String(1).padEnd(data['decimalDigits'] + 1, '0'))) *
        -1,
      driver: '',
      driver_id: '',
      deliveryMethod: DeliveryMethod.BRANCH_DRIVERS,
      invoice_number: data['channelOrderDisplayId'],
      status: OrderStatusEnum.UNASSIGNED,
      is_secret: false,
      is_gift: false,
      is_test: false,
      invoiced_amount: data['payment']
        ? data['payment']['amount'] /
          parseInt(String(1).padEnd(data['decimalDigits'] + 1, '0'))
        : 0,
      items: [],
      language: Language.english,
      orderType: 'restaurant',
      payment_status: '',
      paymentCode: '',
      order_remarks: data['note'],
      source: OrderSource.DELIVERECT,
      isAggregator: true,
      creationSource: OrderCreationSource.DELIVERECT_POS,
      pickupLocationId: undefined,
      recipient_country_code: '',
      recipient_name: '',
      recipient_phone: '',
      total_amount: data['payment']
        ? data['payment']['amount'] /
          parseInt(String(1).padEnd(data['decimalDigits'] + 1, '0'))
        : 0,
      total_amount_after_discount:
        (data['payment']
          ? data['payment']['amount'] /
            parseInt(String(1).padEnd(data['decimalDigits'] + 1, '0'))
          : 0) -
        (data['discountTotal']
          ? data['discountTotal'] /
            parseInt(String(1).padEnd(data['decimalDigits'] + 1, '0'))
          : 0),
      traceId: '',
      orderIsAlreadyPaid: data['orderIsAlreadyPaid'],
      brandId: '',
      brand: undefined,
    };

    // Construct Payment Method

    switch (data['payment']['type']) {
      case 0:
        orderPosToCreate['payment_method'] =
          OrderPaymentMethod['Credit Card Online'];
        break;
      case 1:
        orderPosToCreate['payment_method'] = OrderPaymentMethod.cash;
        break;
      case 2:
        orderPosToCreate['payment_method'] = OrderPaymentMethod['On Delivery'];
        break;
      case 3:
        orderPosToCreate['payment_method'] =
          OrderPaymentMethod['Credit Card Online'];
        break;
      case 4:
        orderPosToCreate['payment_method'] =
          OrderPaymentMethod['Credit Card at Door'];
        break;
      case 5:
        orderPosToCreate['payment_method'] = OrderPaymentMethod['PIN at Door'];
        break;
      case 6:
        orderPosToCreate['payment_method'] =
          OrderPaymentMethod['Voucher at Door'];
        break;
      case 7:
        orderPosToCreate['payment_method'] = OrderPaymentMethod['Cheque'];
        break;
      case 8:
        orderPosToCreate['payment_method'] = OrderPaymentMethod['Bank Contact'];
        break;
      case 9:
        orderPosToCreate['payment_method'] = OrderPaymentMethod['Other'];
        break;
      default:
    }

    const { orderItems, pluError } = await this.formattingOrderItems(
      data['items'],
      company,
    );
    orderPosToCreate.items = orderItems;

    orderPosToCreate.brandId = orderItems[0]
      ? await this.getOrderBrandFromItemPlu(orderItems[0].plu)
      : '';

    // Overriding the Customer First Name In case of Anonymous

    // Creating the Order
    const order = await this.orderPosService.create(orderPosToCreate);

    order.deliverectOrder = true;
    order.deliverectPosOrderId = data['_id'];
    order.deliverectPosOrderDisplayId = data['channelOrderId'];
    order.deliverectChannelName = deliverectChannelName;
    order.deliverectPosCourier = data['courier']?.deliveryBy;

    await order.save();
    if (pluError) {
      // order.status = OrderStatusEnum.Failed;
      await order.save();
    }
    this.eventEmitter.emit('order.status.updated', order);
    await this.orderLogService.saveOrderLog(
      order,
      { requestedObject: orderPosToCreate },
      { responseObject: data },
      OrderLogActionEnum.ON_ORDER_RECEIVED_DELIVERECT,
      order.createdBy,
    );

    return {
      posOrderId: order.code,
      posReceiptId: order.code,
    };
  }

  private async getOrderBrandFromItemPlu(itemPlu: string) {
    const item = await this.menuItemService.getDetails(itemPlu);
    if (!item) return '';

    const menu = await this.menuService.getDetails(
      item.menus[0] && item.menus[0].toString
        ? item.menus[0].toString()
        : (item.menus[0] as any),
    );

    if (!menu) return '';

    return menu.brand ? menu.brand._id.toHexString?.() : '';
  }

  private getCustomerInfo(deliverectCustomer: any, company: CompanyDocument) {
    let phoneIsRandomlyGenerated = false;
    const customer = {
      name: deliverectCustomer?.['name']
        ? deliverectCustomer['name']
        : 'Anonymous Customer',
      phone: deliverectCustomer?.['phoneNumber']
        ? deliverectCustomer['phoneNumber']
        : undefined,
      email: deliverectCustomer?.['email'],
    };

    if (!customer['phone']) {
      // Phone Number provided in the email
      if (customer['email']) {
        const phone = customer['email']?.split('@')[0] as string;
        if (/^\d+$/.test(phone)) {
          customer['phone'] = phone;
        }
      }
      // The First If Condition didn't work
      if (!customer['phone']) {
        customer['phone'] = Math.floor(********* + Math.random() * *********);
        phoneIsRandomlyGenerated = true;
      }
    }
    if (!phoneIsRandomlyGenerated) {
      const { phoneNumber, countryCode } =
        this.helperSharedService.getFormattedCountryCodeAndPhoneNumber(
          customer['phone'],
          company.localization?.countryCode,
        );
      customer['phone'] = phoneNumber;
    }
    return customer;
  }

  private async formattingOrderItems(items: any, company: CompanyDocument) {
    const orderItems: OrderItem[] = [];
    let pluError = false;
    for (let i = 0; i < items.length; i++) {
      const currentItem = items[i];
      const orderItem: OrderItem = {
        name: currentItem['name'],
        description: currentItem['name'],
        plu: currentItem['plu'].toString(),
        basePrice: parseFloat(currentItem['price']) / 100,
        itemReference: currentItem['plu'].toString(),
        price: parseFloat(currentItem['price']) / 100,
        totalAmountAfterDiscount: parseFloat(currentItem['price']) / 100,
        special_instructions: currentItem['remark'],
        quantity: currentItem['quantity'],
        discount: 0,
        modifierGroups: [],
        subProducts: [],
        totalAmount:
          (parseFloat(currentItem['quantity']) *
            parseFloat(currentItem['price'])) /
          100,
        adlerId: '',
        brandId: '',
        pickup_spot_id: '',
      };

      const pluCheck = await this.menuItemService.checkPluExistence(
        orderItem.plu,
        company._id,
      );
      if (!pluCheck) {
        pluError = true;
      }

      if (currentItem['subItems'] && currentItem['subItems'].length) {
        await this.formattingOrderSubItems(currentItem['subItems'], orderItem);
      }
      orderItems.push(orderItem);
    }
    return { orderItems, pluError };
  }

  private async formattingOrderSubItems(
    subItems: any,
    currentProduct: OrderItem,
  ) {
    for (let i = 0; i < subItems.length; i++) {
      const currentItem = subItems[i];
      const orderItem: OrderItem = {
        name: currentItem['name'],
        description: currentItem['name'],
        plu: currentItem['plu'].toString(),
        basePrice: parseFloat(currentItem['price']) / 100,
        itemReference: '',
        price: parseFloat(currentItem['price']) / 100,
        totalAmountAfterDiscount: parseFloat(currentItem['price']) / 100,
        special_instructions: currentItem['remark'],
        quantity: currentItem['quantity'],
        discount: 0,
        modifierGroups: [],
        subProducts: [],
        totalAmount:
          (currentItem['quantity'] * parseFloat(currentItem['price'])) / 100,
        adlerId: '',
        brandId: '',
        pickup_spot_id: '',
      };
      if (currentItem['subItems'] && currentItem['subItems'].length) {
        await this.formattingOrderSubItems(currentItem['subItems'], orderItem);
      } else {
        currentProduct.subProducts.push(orderItem);
      }
    }
  }

  // When Deliverect Request the list of the available products on the current location
  async onGetProducts(companyId: string, locationId: string) {
    const company = await this.companyService.get_details(companyId);
    const branch = await this.branchService.get_details(locationId);

    if (!company || !branch) {
      return;
    }

    const menus = await this.menuService.index({
      company: company._id,
      branch: branch._id,
    } as any);

    const deliverectProducts: DeliverectProduct[] = [];
    const deliverectCategories: DeliverectCategory[] = [];

    // Collect All of the Modifiers [MenuGroupItem]
    // Collect All of all ModifierGroup [MenuGroup]
    // Collect All the Product that does not have any subitems
    // Collect All the products that have subitems

    for (let i = 0; i < menus.length; i++) {
      let menuItems = await this.menuItemService.index({
        menu: menus[i]['_id'],
      } as any);

      menuItems = menuItems[0]['paginatedResult'];

      for (let j = 0; j < menuItems.length; j++) {
        const currentItem = menuItems[j];
        await this.convertEnableItemToDeliverectItem(
          currentItem,
          deliverectProducts,
          deliverectCategories,
        );
      }
    }

    return {
      accountId: company.deliverectAccountId,
      locationId: locationId,
      products: deliverectProducts,
      categories: deliverectCategories,
    };
  }

  async convertEnableItemToDeliverectItem(
    currentItem: any,
    deliverectProducts: DeliverectProduct[],
    deliverectCategories: DeliverectCategory[],
  ) {
    if (!currentItem['plu']) {
      currentItem['plu'] = randomString.generate(10);
      await this.menuItemService.updatePlu(
        currentItem['_id'],
        currentItem['plu'],
      );
    }

    let currentMenuCategory;
    if (currentItem['menuCategory']) {
      currentMenuCategory = await this.menuCategoryService.getDetails(
        currentItem['menuCategory']['_id']
          ? currentItem['menuCategory']['_id']
          : currentItem['menuCategory'],
      );
    }

    const [modifiers, modifierGroups] =
      await this.getModifiersAndModifierGroups(currentItem);

    // deliverectProducts.push(...modifiers);
    // deliverectProducts.push(...modifierGroups);

    // Check if the Modifier Exists before
    for (let i = 0; i < modifiers.length; i++) {
      const existBefore = deliverectProducts.find(
        (x) => x.plu == modifiers[i].plu,
      );
      if (!existBefore) {
        deliverectProducts.push(modifiers[i]);
      }
    }

    // Check if the one of the modifier Groups exists before or not
    for (let i = 0; i < modifierGroups.length; i++) {
      const existBefore = deliverectProducts.find(
        (x) => x.plu == modifierGroups[i].plu,
      );
      if (!existBefore) {
        deliverectProducts.push(modifierGroups[i]);
      }
    }

    // Check the item Exists Before or not

    const itemExistsBefore = deliverectProducts.find(
      (x) => x.plu == currentItem['plu'],
    );
    if (!itemExistsBefore) {
      deliverectProducts.push({
        name: currentItem['nameEn'],
        description: currentItem['descriptionEn'],
        productType: 1,
        productTags: [],
        deliveryTax: currentItem['deliveryTax'],
        takeawayTax: currentItem['takeawayTax'],
        imageUrl: currentItem['externalImage'],
        max: currentItem['max'],
        min: currentItem['min'],
        plu: currentItem['plu'],
        price: currentItem['price'] * 100,
        uniqueKey: currentItem['_id'],
        posProductId: currentItem['_id'],
        posCategoryIds: currentMenuCategory ? [currentMenuCategory['_id']] : [],
        subProducts: [
          ...modifierGroups.map((x) => x.plu.toString()),
          ...currentItem['subItems'].map((x) => x.plu.toString()),
        ],
      });
    }

    const categoryExistBefore = deliverectCategories.find(
      (x) => x.posCategoryId == currentMenuCategory?.['_id'],
    );

    if (currentMenuCategory && !categoryExistBefore) {
      deliverectCategories.push({
        name: currentMenuCategory['name'],
        imageUrl: currentMenuCategory['imageUrl'],
        posCategoryId: currentMenuCategory['_id'],
      });
    }
  }

  async getModifiersAndModifierGroups(item: MenuItemDocument) {
    const modifiers: DeliverectProduct[] = [],
      modifierGroups: DeliverectProduct[] = [];
    const currentGroupSubProducts = [];
    if (item.menuGroups && item.menuGroups.length) {
      for (let i = 0; i < item.menuGroups.length; i++) {
        const currentGroup = item.menuGroups[i] as unknown as MenuGroupDocument;

        // Fetching Sub Procuts and update it's PLU if deos not exist
        for (const modifier of currentGroup.items ?? []) {
          let plu = modifier.plu;
          if (!plu) {
            plu = randomString.generate(5);
            await this.menuGroupService.updateGroupPlu(
              new Types.ObjectId(currentGroup._id),
              plu,
            );
          }
          currentGroupSubProducts.push(plu.toString());
        }
        modifierGroups.push({
          name: currentGroup['nameEn'],
          description: currentGroup['descriptionEn'],
          price: 0,
          plu: currentGroup['plu']
            ? currentGroup['plu'].toString()
            : currentGroup['_id'].toString(),
          deliveryTax: currentGroup['deliveryTax'] ?? 0,
          takeawayTax: currentGroup['takeawayTax'] ?? 0,
          min: currentGroup['min'],
          max: currentGroup['max'],
          imageUrl: '',
          posCategoryIds: [],
          posProductId: currentGroup['_id'].toHexString(),
          productTags: [],
          productType: 3,
          subProducts: currentGroupSubProducts,
          uniqueKey: currentGroup['_id'].toHexString(),
        });
        currentGroup['items']?.map((x) => {
          let plu = x.plu;
          if (!plu) {
            plu = randomString.generate(5);
            this.menuGroupService.updateGroupItemPlu(
              new Types.ObjectId(currentGroup['_id']),
              x['name'],
              plu,
            );
          }
          modifiers.push({
            name: x['name'],
            description: '',
            price: x.price * 100,
            plu: plu.toString(),
            deliveryTax: x['deliveryTax'],
            takeawayTax: x['takeawayTax'],
            min: x['min'] || 1,
            max: x['max'] || 1,
            imageUrl: '',
            posCategoryIds: [],
            posProductId: x['deliverectRawId'] || plu.toString(),
            productTags: [],
            productType: 2,
            subProducts: [],
            uniqueKey: plu.toString(),
          });
        });
      }
    }

    return [modifiers, modifierGroups];
  }

  // When Deliverect Request the list of the available tables on the current location
  async onGetTables(locationId: string) {
    const branch = await this.branchService.get_details(locationId);

    const company = await this.companyService.get_details(
      branch['company']['_id'],
    );

    if (!branch && !company) {
      return;
    }

    const tables = await this.tableService.index({
      company: company._id,
      branch: branch._id,
    } as any);

    const deliverectTables: DeliverectTable[] = [];
    for (let i = 0; i < tables.length; i++) {
      deliverectTables.push({
        id: tables[i]._id,
        name: tables[i].name,
        floorId: tables[i].floor,
        seats: tables[i].seats,
      });
    }

    return {
      tables: deliverectTables,
    };
  }

  // When Deliverect Request the list of the available Floors on the current location
  async onGetFloors(locationId: string) {
    const branch = await this.branchService.get_details(locationId);

    const company = await this.companyService.get_details(
      branch['company']['_id'],
    );

    if (!branch && !company) {
      return;
    }

    const floors = await this.floorService.index({
      company: company._id,
      branch: branch._id,
    } as any);

    const deliverectFloors: DeliverectFloor[] = [];
    for (let i = 0; i < floors.length; i++) {
      deliverectFloors.push({
        id: floors[i]._id,
        name: floors[i].name,
        description: floors[i].description,
        capacity: floors[i].capacity,
      });
    }

    return {
      floors: deliverectFloors,
    };
  }

  // When Deliverect Request  the receipt for the specific order
  async onGetReceipt(receiptToIndex: any) {
    const order = await this.orderService.get_details(
      receiptToIndex['receiptId'] ?? receiptToIndex['tableId'],
    );

    if (!order) {
      return;
    }

    const orderItems = order.items.map((x) => {
      return { id: x.plu, amount: x.quantity, price: x.basePrice };
    });
    const orderPayments =
      order.payment_method == OrderPaymentMethod.cash
        ? [{ id: order.code, amount: order.total_amount, paymentType: 'cash' }]
        : [
            {
              id: order.code,
              amount: order.total_amount,
              paymentType: 'prePaid',
            },
          ];

    return {
      receiptId: order.code,
      floorId: order.floorCode,
      tableId: order.tableCode,
      created: order['createdAt'],
      closed:
        order.status != OrderStatusEnum.COMPLETED
          ? //  &&
            //   order.status != OrderStatusEnum.claimed
            '1970-00-00T00:00:00Z'
          : order.deliveredOn,
      customerId: order.customer['_id'],
      status: order.status,
      total: order.total_amount,
      items: orderItems,
      payments: orderPayments,
      taxes: [
        {
          total: 0,
        },
      ],
    };
  }

  // When a new location is registered on deliverect side
  async onNewLocationRegistered(data: RegisterNewPosDto) {
    const branch = await this.branchService.get_details(
      data.externalLocationId,
    );
    if (!branch) {
      throw {
        code: 500,
        message: 'Please Select A Correct External Location id',
      };
    }

    await this.branchService.setDeliverectLocationId(
      data.externalLocationId,
      data.locationId,
    );

    const companyId = branch.company['_id']
      ? branch.company['_id']
      : branch.company;
    const company = await this.companyService.get_details(companyId);
    if (!company) {
      throw {
        code: 500,
        message: 'Please Select A Correct company id',
      };
    }

    company.deliverectAccountId = data.accountId;
    await company.save();

    // This webhook should return an JSON body contains syncProductsURL, ordersWebhookURL, syncTablesURL, syncFloorsURL
    const HOST = this.configService.get('DELIVERECT_CURRENT_HOST');

    return {
      syncProductsURL: `${HOST}/pos/product/${companyId}/${branch._id}`,
      ordersWebhookURL: `${HOST}/pos/order/${companyId}`,
      syncTablesURL: `${HOST}/pos/tables/${companyId}`,
      syncFloorsURL: `${HOST}/pos/floors/${companyId}`,
    };
  }

  public async getDeliverectAuthenticatedToken(company: CompanyDocument) {
    if (
      !this.deliverectToken ||
      moment().isBefore(this.deliverectTokenExpire)
    ) {
      if (!company.deliverectPOSClientId || !company.deliverectPOSSecret) {
        return null;
      }
      const devResponse = await this.performAuthenticationRequest(
        company.deliverectPOSClientId,
        company.deliverectPOSSecret,
      );
      this.deliverectToken = devResponse['access_token'];
      this.deliverectTokenExpire = moment().add(
        devResponse['expires_in'],
        'seconds',
      );
    }
    return this.deliverectToken;
  }

  public async performAuthenticationRequest(
    clientId: string,
    clientSecret: string,
  ) {
    return new Promise((resolve, reject) => {
      this.http
        .post(this.deliverectBaseUrl + 'oauth/token', {
          client_id: clientId,
          client_secret: clientSecret,
          audience: this.deliverectBaseUrl,
          grant_type: 'client_credentials',
        })
        .subscribe(
          (data) => {
            resolve(data.data);
          },
          (err) => {
            reject(err);
          },
        );
    });
  }

  async syncMenuItem(menuItemId: string) {
    const currentItem = await this.menuItemService.getDetails(menuItemId);

    const menu = await this.menuService.getDetails(
      currentItem.menus[0].toHexString(),
    );

    const menuItems = await this.menuItemService.findByMenuId(menu._id);

    const company = await this.companyService.get_details(
      currentItem.company['_id']
        ? currentItem.company['_id'].toHexString()
        : currentItem.company.toHexString(),
    );

    if (
      !company.deliverectAccountId ||
      !company.deliverectPOSClientId ||
      !company.deliverectPOSSecret
    ) {
      return;
    }

    const branch = await this.branchService.get_details(
      menu.branches[0] ? menu.branches[0].toHexString() : '',
    );

    if (!branch) {
      return;
    }

    const deliverectProducts: DeliverectProduct[] = [];
    const deliverectCategories: DeliverectCategory[] = [];
    for (let i = 0; i < menuItems.length; i++) {
      await this.convertEnableItemToDeliverectItem(
        menuItems[i],
        deliverectProducts,
        deliverectCategories,
      );
    }

    const postData = {
      accountId: company.deliverectAccountId,
      locationId: branch ? branch['deliverectLocationId'] : '',
      products: deliverectProducts,
      categories: deliverectCategories,
    };

    const token = await this.getDeliverectAuthenticatedToken(company);
    if (!token) return;
    this.http
      .post(this.deliverectBaseUrl + 'productAndCategories', postData, {
        headers: { Authorization: `Bearer ${token}` },
      })
      .subscribe({
        next: (data) => {},
        error: (error) => {
          this.loggerService.error(error.message, error.stacktrace, postData);
        },
      });
  }

  async syncIntegratedChannels(company: CompanyDocument) {
    const integratedChannels = await this.performGetChannelRequest(company);
    if (integratedChannels && integratedChannels['length']) {
      const companyIntegratedChannel: CompanyIntegratedChannel[] = [];
      for (let i = 0; i < integratedChannels['length']; i++) {
        companyIntegratedChannel.push({
          name: integratedChannels[i]['name'],
          channelId: integratedChannels[i]['channelId'],
          sendingCustomerNumber: false,
        });
      }
      company.deliverectIntegratedChannels = companyIntegratedChannel;
      await company.save();
    }
  }

  private async performGetChannelRequest(company: CompanyDocument) {
    return new Promise(async (resolve) => {
      const token = await this.getDeliverectAuthenticatedToken(company);
      if (!token) return;
      this.http
        .get(this.deliverectBaseUrl + 'allChannels/', {
          headers: { Authorization: `Bearer ${token}` },
        })
        .subscribe(
          (data) => {
            resolve(data.data);
          },
          (err) => {
            resolve(err);
          },
        );
    });
  }

  async syncOrderStatus(order: OrderDocument) {
    try {
      const company = await this.companyService.get_details(
        order['company']['_id'] ? order['company']['_id'] : order['company'],
      );

      const deliverectOrderStatusToUpdate: DeliverectOrderStatusToUpdate = {
        status: 0,
        orderId: order.deliverectPosOrderId,
        receiptId: order.deliverectPosOrderId,
        reason: 'Order Status Updated',
        timeStamp: moment().format('YYYY-MM-DD hh:mm'),
      };

      if (order.status == OrderStatusEnum.PENDING) {
        deliverectOrderStatusToUpdate.status = 20;
      } else if (order.status == OrderStatusEnum.UNASSIGNED) {
        deliverectOrderStatusToUpdate.status = 2;
      } else if (order.status == OrderStatusEnum.PREPARING) {
        deliverectOrderStatusToUpdate.status = 50;
      } else if (order.status == OrderStatusEnum.SCHEDULED) {
        deliverectOrderStatusToUpdate.status = 25;
      } else if (order.status == OrderStatusEnum.PENDING_PICKUP) {
        deliverectOrderStatusToUpdate.status = 70;
      } else if (order.status == OrderStatusEnum.IN_ROUTE) {
        deliverectOrderStatusToUpdate.status = 80;
      } else if (order.status == OrderStatusEnum.COMPLETED) {
        deliverectOrderStatusToUpdate.status = 90;
      } else if (order.status == OrderStatusEnum.CANCELED) {
        deliverectOrderStatusToUpdate.status = 110;
      }

      await this.performOrderStatusUpdate(
        deliverectOrderStatusToUpdate,
        company,
      );
    } catch (error) {
      this.loggerService.error(error.message, error.stacktrace, order);
    }
  }

  private async performOrderStatusUpdate(
    deliverectOrderStatusToUpdate: DeliverectOrderStatusToUpdate,
    company: CompanyDocument,
  ) {
    const token = await this.getDeliverectAuthenticatedToken(company);
    if (!token) return;
    this.http
      .post(
        this.deliverectBaseUrl +
          'orderStatus/' +
          deliverectOrderStatusToUpdate.orderId,
        deliverectOrderStatusToUpdate,
        {
          headers: { Authorization: `Bearer ${token}` },
        },
      )
      .subscribe();
  }
}
