import {
  Benefit,
  BenefitAppliedTo,
  BenefitDocument,
  BenefitType,
  BenefitUsage,
  BenefitWithSourceAndId,
  CreateBenefitDto,
  Currency,
  CustomerBenefitSource,
  CustomerDocument,
  CustomerEarnedBenefit,
  GetAllBenefitDto,
  HelperSharedServiceInterface,
  IHelperSharedService,
  IndexResultDto,
  LoyaltyTierBenefit,
  omit,
  OrderDocument,
  responseCode,
  RewardMenuItem,
  UpdateBenefitDto,
} from '@app/shared-stuff';
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { plainToInstance } from 'class-transformer';
import * as moment from 'moment-timezone';
import { PipelineStage, Types } from 'mongoose';
import { CustomerRepositoryInterface } from '../../customer/modules/customer-repository/customer.repository.interface';
import { LoyaltyTransactionServiceInterface } from '../../loyalty-transaction/services/loyalty-transaction-service.interface';
import { MenuItemService } from '../../restaurant/services/menu-item/menu-item.service';
import { BenefitRepositoryInterface } from '../repositories/benefit.repository.interface';
import { BenefitServiceInterface } from './benefit-service.interface';
import { BenefitTypeFactoryService } from './types/benefit-type-factory.service';

@Injectable()
export class BenefitService implements BenefitServiceInterface {
  constructor(
    @Inject(BenefitRepositoryInterface)
    private readonly benefitRepository: BenefitRepositoryInterface,
    private readonly benefitTypeFactory: BenefitTypeFactoryService,
    private readonly eventEmitter: EventEmitter2,
    @Inject(IHelperSharedService)
    private helperSharedService: HelperSharedServiceInterface,
    @Inject(forwardRef(() => MenuItemService))
    private readonly menuItemService: MenuItemService,
    @Inject(CustomerRepositoryInterface)
    private readonly customerRepository: CustomerRepositoryInterface,
    @Inject(LoyaltyTransactionServiceInterface)
    private readonly loyaltyTransactionService: LoyaltyTransactionServiceInterface,
  ) {}

  async index(
    getAllBenefitDto: GetAllBenefitDto,
  ): Promise<IndexResultDto<Benefit>[]> {
    const pipeline: PipelineStage[] = [];

    this.addMatchStage(pipeline, getAllBenefitDto);
    this.addPaginationStage(pipeline, getAllBenefitDto);
    return await this.benefitRepository.aggregate(pipeline);
  }

  async create(createBenefitDto: CreateBenefitDto): Promise<BenefitDocument> {
    let menuItem: RewardMenuItem;
    if (createBenefitDto.appliedTo === BenefitAppliedTo.MENU_ITEM) {
      menuItem = await this.createBenefitMenuItem(
        createBenefitDto.menuItem.brandId,
        createBenefitDto.menuItem.menuId,
        createBenefitDto.menuItem.masterMenuItemId,
      );
    }
    const { titleEn, titleAr } = this.generateTitle(
      createBenefitDto.appliedTo,
      createBenefitDto.type,
      createBenefitDto.value,
      menuItem,
      createBenefitDto.currency,
    );
    const benefit = plainToInstance(Benefit, {
      ...createBenefitDto,
      menuItem,
      titleEn,
      titleAr,
    });

    return await this.benefitRepository.create(benefit);
  }

  async update(updateBenefitDto: UpdateBenefitDto): Promise<BenefitDocument> {
    const benefit = await this.findOneById(updateBenefitDto._id);

    const menuItem = await this.updateMenuItem(benefit, updateBenefitDto);

    const { titleEn, titleAr } = this.generateUpdatedTitle(
      benefit,
      updateBenefitDto,
      menuItem,
    );

    const toUpdateBenefitDto = this.createUpdatedBenefitDto(
      updateBenefitDto,
      menuItem,
      titleEn,
      titleAr,
    );
    const updatedBenefit = await this.benefitRepository.findOneAndUpdate(
      { _id: updateBenefitDto._id },
      toUpdateBenefitDto,
    );
    return updatedBenefit;
  }

  private async updateMenuItem(
    benefit: Benefit,
    updateBenefitDto: UpdateBenefitDto,
  ): Promise<RewardMenuItem> {
    if (
      updateBenefitDto.appliedTo === BenefitAppliedTo.MENU_ITEM &&
      updateBenefitDto.menuItem
    ) {
      return this.createBenefitMenuItem(
        updateBenefitDto.menuItem.brandId,
        updateBenefitDto.menuItem.menuId,
        updateBenefitDto.menuItem.masterMenuItemId,
      );
    }
    return benefit.menuItem;
  }

  private generateUpdatedTitle(
    benefit: Benefit,
    updateBenefitDto: UpdateBenefitDto,
    menuItem: RewardMenuItem,
  ): { titleEn: string; titleAr: string } {
    return this.generateTitle(
      updateBenefitDto.appliedTo ?? benefit.appliedTo,
      updateBenefitDto.type ?? benefit.type,
      updateBenefitDto.value ?? benefit.value,
      menuItem,
      updateBenefitDto.currency,
    );
  }

  private createUpdatedBenefitDto(
    updateBenefitDto: UpdateBenefitDto,
    menuItem: RewardMenuItem,
    titleEn: string,
    titleAr: string,
  ): Benefit {
    return plainToInstance(Benefit, {
      ...omit(updateBenefitDto, ['_id']),
      menuItem,
      titleEn,
      titleAr,
    });
  }

  async findByIdIn(ids: Types.ObjectId[]): Promise<BenefitDocument[]> {
    return this.benefitRepository.findAll({ _id: { $in: ids } });
  }

  async findOneById(id: Types.ObjectId): Promise<Benefit> {
    const benefit = await this.benefitRepository.findById(id);

    if (!benefit) {
      throw new NotFoundException(
        'Benefit Not Exist',
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    }
    return benefit;
  }

  async redeem(
    benefit: CustomerEarnedBenefit,
    customer: CustomerDocument,
    order: OrderDocument,
  ): Promise<OrderDocument> {
    const customerBenefit = this.findCustomerEarnedBenefit(customer, benefit);

    if (!customerBenefit) {
      throw new BadRequestException(
        'Benefit Not Exist For This Customer',
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    }

    await this.benefitTypeFactory.redeem(benefit, customer, order);
    return order;
  }

  async updateEarnedBenefits(
    benefits: BenefitWithSourceAndId[],
    customer: CustomerDocument,
  ): Promise<void> {
    const benefitsMap = new Map(
      benefits.map((benefit) => [
        `${benefit._id.toHexString()}-${benefit.source}`,
        benefit,
      ]),
    );

    customer.earnedBenefits = customer.earnedBenefits.map((benefit) => {
      if (!benefit._id) return benefit;

      const key = `${benefit._id.toHexString()}-${benefit.source}`;
      const updatedBenefit = benefitsMap.get(key);

      return updatedBenefit ? { ...benefit, ...updatedBenefit } : benefit;
    });

    await this.customerRepository.updateBenefits(
      customer._id,
      customer.earnedBenefits,
      'earnedBenefits',
    );
  }

  async replaceCustomerEarnedBenefits(
    benefits: CustomerEarnedBenefit[],
    customer: CustomerDocument,
  ): Promise<void> {
    customer.earnedBenefits = benefits;
    await this.customerRepository.updateBenefits(
      customer._id,
      customer.earnedBenefits,
      'earnedBenefits',
    );

    this.eventEmitter.emit('customer.benefits.updated', customer, benefits);
  }

  async earn(
    earnedBenefits: CustomerEarnedBenefit[],
    customer: CustomerDocument,
  ): Promise<void> {
    if (
      earnedBenefits.some((benefit) =>
        this.findCustomerEarnedBenefit(customer, benefit),
      )
    )
      throw new BadRequestException(
        'Benefit Already Earned By This Customer',
        responseCode.DUPLICATED_ENTRY.toString(),
      );

    customer.earnedBenefits = [
      ...(customer.earnedBenefits ?? []),
      ...earnedBenefits,
    ];

    await this.customerRepository.updateBenefits(
      customer._id,
      customer.earnedBenefits,
      'earnedBenefits',
    );

    await this.loyaltyTransactionService.recordEarnedBenefits(
      customer,
      earnedBenefits,
    );

    this.eventEmitter.emit(
      'customer.benefits.updated',
      customer,
      earnedBenefits,
    );
  }

  async removeEarnedBenefits(
    benefits: CustomerEarnedBenefit[],
    customer: CustomerDocument,
  ): Promise<void> {
    const benefitsSet = new Set(
      benefits.map(
        (benefit) => `${benefit._id.toHexString()}-${benefit.source}`,
      ),
    );
    customer.earnedBenefits = customer.earnedBenefits?.filter(
      (benefit) =>
        !benefitsSet.has(`${benefit._id.toHexString()}-${benefit.source}`),
    );

    customer.usedBenefits = [
      ...(customer.usedBenefits ?? []),
      ...benefits.map((benefit) => ({
        ...benefit,
        usedAt: moment.utc().toDate(),
      })),
    ];
    await this.customerRepository.updateBenefits(
      customer._id,
      customer.earnedBenefits,
      'earnedBenefits',
    );

    await this.customerRepository.updateBenefits(
      customer._id,
      customer.usedBenefits,
      'usedBenefits',
    );
    this.eventEmitter.emit('customer.benefits.updated', customer, benefits);
  }

  async onProgramBenefitUpdated(
    oldBenefits: LoyaltyTierBenefit[],
    newBenefits: LoyaltyTierBenefit[],
    source: CustomerBenefitSource,
    customer: CustomerDocument,
  ) {
    const customerBenefits = customer.earnedBenefits ?? [];
    const updatedBenefits = await this.processUpdatedBenefits(
      oldBenefits,
      newBenefits,
      source,
      customerBenefits,
    );
    await this.replaceCustomerEarnedBenefits(updatedBenefits, customer);
  }

  private async processUpdatedBenefits(
    oldBenefits: LoyaltyTierBenefit[],
    newBenefits: LoyaltyTierBenefit[],
    source: CustomerBenefitSource,
    customerBenefits: CustomerEarnedBenefit[],
  ): Promise<CustomerEarnedBenefit[]> {
    const needsDelete = this.getBenefitsToDelete(oldBenefits, newBenefits);
    const { needsCreate, needsUpdate } = this.categorizeBenefits(
      oldBenefits,
      newBenefits,
    );

    let updatedBenefits = [...customerBenefits];

    updatedBenefits = this.addNewBenefits(updatedBenefits, needsCreate, source);
    updatedBenefits = this.removeBenefits(updatedBenefits, needsDelete, source);
    updatedBenefits = this.updateExistingBenefits(
      updatedBenefits,
      needsUpdate,
      source,
    );
    if (needsDelete.length > 0) {
      await this.benefitRepository.removeMany(
        needsDelete.map((benefit) => benefit._id),
      );
    }

    return updatedBenefits;
  }

  private getBenefitsToDelete(
    oldBenefits: LoyaltyTierBenefit[],
    newBenefits: LoyaltyTierBenefit[],
  ): LoyaltyTierBenefit[] {
    return oldBenefits?.filter(
      (oldBenefit) =>
        !newBenefits.some((newBenefit) =>
          newBenefit._id.equals(oldBenefit._id),
        ),
    );
  }

  private categorizeBenefits(
    oldBenefits: LoyaltyTierBenefit[],
    newBenefits: LoyaltyTierBenefit[],
  ): {
    needsCreate: LoyaltyTierBenefit[];
    needsUpdate: LoyaltyTierBenefit[];
  } {
    return newBenefits.reduce<{
      needsCreate: LoyaltyTierBenefit[];
      needsUpdate: LoyaltyTierBenefit[];
    }>(
      (acc, benefit) => {
        const isNewBenefit = !oldBenefits.some((oldB) =>
          oldB._id.equals(benefit._id),
        );
        if (isNewBenefit) {
          acc.needsCreate.push(benefit);
        } else {
          acc.needsUpdate.push(benefit);
        }
        return acc;
      },
      { needsCreate: [], needsUpdate: [] },
    );
  }

  private addNewBenefits(
    customerBenefits: CustomerEarnedBenefit[],
    newBenefits: LoyaltyTierBenefit[],
    source: CustomerBenefitSource,
  ): CustomerEarnedBenefit[] {
    if (newBenefits.length === 0) return customerBenefits;

    return customerBenefits.concat(
      newBenefits.map((newBenefit) => ({
        ...newBenefit,
        source,
        earnedAt: moment.utc().toDate(),
        numberOfUsages: 0,
      })),
    );
  }

  private removeBenefits(
    customerBenefits: CustomerEarnedBenefit[],
    benefitsToRemove: LoyaltyTierBenefit[],
    source: CustomerBenefitSource,
  ): CustomerEarnedBenefit[] {
    if (benefitsToRemove.length === 0) return customerBenefits;

    return customerBenefits?.filter(
      (customerBenefit) =>
        customerBenefit.source !== source ||
        !benefitsToRemove.some((benefitToRemove) =>
          benefitToRemove._id.equals(customerBenefit._id),
        ),
    );
  }

  private updateExistingBenefits(
    customerBenefits: CustomerEarnedBenefit[],
    benefitsToUpdate: LoyaltyTierBenefit[],
    source: CustomerBenefitSource,
  ): CustomerEarnedBenefit[] {
    if (benefitsToUpdate.length === 0) return customerBenefits;

    return customerBenefits.map((customerBenefit) =>
      customerBenefit.source === source &&
      benefitsToUpdate.some((newBenefit) =>
        newBenefit._id.equals(customerBenefit._id),
      )
        ? {
            ...customerBenefit,
            ...benefitsToUpdate.find((benefitToUpdate) =>
              benefitToUpdate._id.equals(customerBenefit._id),
            ),
            earnedAt: customerBenefit.earnedAt,
            numberOfUsages: customerBenefit.numberOfUsages,
          }
        : customerBenefit,
    );
  }

  async createBenefitMenuItem(
    brandId: Types.ObjectId,
    menuId: Types.ObjectId,
    masterMenuItemId: Types.ObjectId,
  ): Promise<RewardMenuItem> {
    if (!masterMenuItemId) return null;

    const menuItem = await this.menuItemService.getDetails(
      masterMenuItemId.toString(),
    );

    if (!menuItem)
      throw new BadRequestException(
        `Menu item with ID ${masterMenuItemId.toHexString()} not found.`,
      );

    return {
      brandId,
      menuId,
      masterMenuItemId,
      nameEn: menuItem.nameEn,
      nameAr: menuItem.nameAr,
      images: menuItem.images,
      externalImage: menuItem.externalImage,
      eligibleMenuItemIds: (menuItem.similarMenuItems || []).concat(
        menuItem._id,
      ),
      integrationInfo: menuItem.integrationInfo,
    };
  }

  private findCustomerEarnedBenefit(
    customer: CustomerDocument,
    benefit: CustomerEarnedBenefit,
  ): CustomerEarnedBenefit | undefined {
    if (!customer.earnedBenefits) return undefined;
    return customer.earnedBenefits.find(
      (earnedBenefit) =>
        earnedBenefit._id &&
        earnedBenefit._id.equals(benefit._id) &&
        benefit.source == earnedBenefit.source,
    );
  }

  private addPaginationStage(
    pipeline: PipelineStage[],
    getAllBenefitDto: GetAllBenefitDto,
  ) {
    pipeline.push(
      this.helperSharedService.createPaginationStage(
        getAllBenefitDto.offset,
        getAllBenefitDto.limit,
      ),
    );
  }

  private addMatchStage(
    pipeline: PipelineStage[],
    getAllBenefitDto: GetAllBenefitDto,
  ) {
    const match: Partial<Record<keyof Benefit, any>> = {};
    const filterProps = ['companyId', 'type', 'appliedTo'];

    filterProps.forEach((prop) => {
      if (getAllBenefitDto[prop]) {
        match[prop] = getAllBenefitDto[prop];
      }
    });

    if (getAllBenefitDto.search_key) {
      match['$or'] = [
        { titleEn: { $regex: getAllBenefitDto.search_key, $options: 'i' } },
        { titleAr: { $regex: getAllBenefitDto.search_key, $options: 'i' } },
      ];
    }

    pipeline.push({
      $match: match,
    });
  }

  private generateTitle(
    appliedTo: BenefitAppliedTo,
    type: BenefitType,
    value: number,
    menuItem: RewardMenuItem,
    currency: Currency = Currency.QAR,
  ): {
    titleEn: string;
    titleAr: string;
  } {
    const appliedOnMapping = {
      [BenefitAppliedTo.MENU_ITEM]: {
        en: menuItem?.nameEn,
        ar: menuItem?.nameAr,
      },
      [BenefitAppliedTo.ORDER_TOTAL_AMOUNT]: {
        en: 'Order Total Amount',
        ar: 'مجموع الطلب',
      },
      [BenefitAppliedTo.ORDER_DELIVERY_AMOUNT]: {
        en: 'Order Delivery Amount',
        ar: 'خدمة التوصيل',
      },
      [BenefitAppliedTo.ORDER_INVOICED_AMOUNT]: {
        en: 'Order Invoiced Amount',
        ar: 'مجموع الفاتورة',
      },
    };
    const isFree =
      type == BenefitType.PERCENTAGE_DISCOUNT && (value == 100 || value == 1);

    if (isFree) {
      return {
        titleEn: `Free ${appliedOnMapping[appliedTo].en}`,
        titleAr: `مجانا ${appliedOnMapping[appliedTo].ar}`,
      };
    }

    return {
      titleEn: `${value < 1 ? value * 100 : value} ${
        type == BenefitType.FIXED_DISCOUNT ? currency : '%'
      } off ${appliedOnMapping[appliedTo].en}`,
      titleAr: `${value < 1 ? value * 100 : value} ${
        type == BenefitType.FIXED_DISCOUNT ? currency : '%'
      } خصم علي ${appliedOnMapping[appliedTo].ar}`,
    };
  }
  public createBenefitUsageMatcher(
    benefit: LoyaltyTierBenefit | CustomerEarnedBenefit,
    source: CustomerBenefitSource,
  ): (benefitUsage: BenefitUsage | CustomerEarnedBenefit) => boolean {
    const checkSetEquality = (a: Set<unknown>, b: Set<unknown>): boolean => {
      if (a === b) return true;
      if (a.size !== b.size) return false;
      for (const item of a) if (!b.has(item)) return false;
      return true;
    };

    const convertObjectIds = (objectIds: Types.ObjectId[]): Set<string> =>
      new Set(objectIds.map((id) => id.toString()));

    const checkItemEquality = (a: RewardMenuItem, b: RewardMenuItem): boolean =>
      checkSetEquality(
        convertObjectIds(a.eligibleMenuItemIds),
        convertObjectIds(b.eligibleMenuItemIds),
      );

    return (benefitUsage) => {
      if (benefitUsage.source !== source) return false;
      if (benefit.type !== benefitUsage.type) return false;
      if (benefit.appliedTo !== benefitUsage.appliedTo) return false;
      if (benefit.value !== benefitUsage.value) return false;
      if (benefit.appliedTo === BenefitAppliedTo.MENU_ITEM) {
        const areItemsEqual = checkItemEquality(
          benefit.menuItem,
          benefitUsage.menuItem,
        );
        if (!areItemsEqual) return false;
      }

      return true;
    };
  }
}
