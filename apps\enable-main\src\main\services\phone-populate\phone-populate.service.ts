import {
  BranchDocument,
  BranchToIndex,
  Brand,
  CompanyDocument,
  CompanyBrandBranchDto,
  IndexBrandDto,
  PhoneToPopulate,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { isValidObjectId, Types } from 'mongoose';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { BranchService } from './../../../branch/services/branch/branch.service';
import { BrandSearchType } from './../../../brand/enumerations/brand-search-type.enum';
import { BrandSortType } from './../../../brand/enumerations/brand-sort-type.enum';
import { CompanyService } from './../../../company/services/company/company.service';

@Injectable()
export class PhonePopulateService {
  constructor(
    private companyService: CompanyService,
    private branchService: BranchService,
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandServiceInterface,
  ) {}

  async populate(phoneToPopulate: PhoneToPopulate) {
    // If User Assigned to one company or companyId provided then return only one company
    // else if there is company ids provided then trying to  fetch companies using ids and phone number
    // else if there no company id provided with the phone number then return all companies

    let companyId,
      companyIds,
      brandId,
      branchId,
      companyBrandBranchDto: CompanyBrandBranchDto;

    if (phoneToPopulate.companyId) {
      companyId = new Types.ObjectId(phoneToPopulate.companyId);
    }

    if (phoneToPopulate.companies) {
      companyIds = this.convertArrayOfStringToArrayOfObjects(
        phoneToPopulate.companies,
      );
    }

    if (phoneToPopulate.brandId) {
      brandId = new Types.ObjectId(phoneToPopulate.brandId);
    }

    // Companies Fetching
    let companies = await this.companiesPopulate(
      companyId,
      companyIds,
      phoneToPopulate.phoneNumber,
    );

    // Brands Fetching
    const [brands, possibleBranchesIds] = await this.brandsPopulate(
      brandId,
      companies,
      phoneToPopulate.phoneNumber,
    );

    // Update the companies array based on One Brand
    if (
      brands &&
      brands.length == 1 &&
      companies &&
      (companies.length > 1 || !companies.length)
    ) {
      const company = await this.companyService.get_details(
        brands[0].companyId.toHexString(),
      );
      companies = [company];
    }

    // Branches Fetching
    const branches = await this.branchesPopulate(
      branchId,
      possibleBranchesIds,
      brands,
      phoneToPopulate.phoneNumber,
    );

    if (!companies.length) {
      companies = await this.companyService.index(
        { limit: 500 },
        companyIds && companyIds.length ? companyIds : undefined,
        undefined,
      );
    }

    return (companyBrandBranchDto = {
      companies: companies,
      brands: brands,
      branches: branches,
    });
  }

  private async companiesPopulate(
    companyId: Types.ObjectId,
    companyIds: Types.ObjectId[],
    phoneNumber: string,
  ): Promise<CompanyDocument[]> {
    let companies: CompanyDocument[] = [],
      fetchedCompanies = [];
    const companyToIndex = phoneNumber
      ? this.constructCompanyIndexDto(phoneNumber)
      : {};

    if (companyId) {
      fetchedCompanies = await this.companyService.index(
        {},
        undefined,
        companyId,
      );
      companies = companies.concat(fetchedCompanies);
    } else if (companyIds && companyIds.length) {
      fetchedCompanies = await this.companyService.index(
        companyToIndex,
        companyIds,
        undefined,
      );

      companies = companies.concat(fetchedCompanies);
    } else {
      fetchedCompanies = await this.companyService.index(
        companyToIndex,
        undefined,
        undefined,
      );
      companies = companies.concat(fetchedCompanies);
    }

    return companies;
  }

  private async brandsPopulate(
    brandId: Types.ObjectId,
    companies: CompanyDocument[],
    phoneNumber: string,
  ): Promise<[Brand[], Types.ObjectId[]]> {
    let brands: Brand[] = [],
      fetchedBrands = [],
      brandToIndex: IndexBrandDto,
      possibleBranchesIds: Types.ObjectId[] = [];

    // Brand On Branch Filter
    if (phoneNumber) {
      const branchesBrandsPhone = await this.branchService.index({
        brandBranchPhoneNumber: phoneNumber,
      } as any);

      if (branchesBrandsPhone && branchesBrandsPhone.length) {
        const [brandsIds, branchIds] = this.fetchingBrandsIdsFromBranches(
          branchesBrandsPhone,
          phoneNumber,
        );
        possibleBranchesIds = branchIds;
        fetchedBrands = await this.brandService.findByIdIn(brandsIds);
      }
    }

    if (!fetchedBrands.length) {
      // Normal Brand fetching Scenario
      if (brandId) {
        fetchedBrands = [await this.brandService.findById(brandId)];
      } else if (companies && companies.length) {
        brandToIndex = this.constructBrandIndexDTO(
          phoneNumber,
          companies[0]['_id'],
        );
        fetchedBrands = (
          await this.brandService.index(brandToIndex, undefined)
        )[0].paginatedResult;

        // Phone Number does not exist using phoneNumber and companies
        if (fetchedBrands.length == 0) {
          brandToIndex = this.constructBrandIndexDTO(
            undefined,
            companies[0]['_id'],
          );
          fetchedBrands = (
            await this.brandService.index(brandToIndex, undefined)
          )[0].paginatedResult;
        }
      } else {
        brandToIndex = this.constructBrandIndexDTO(phoneNumber, undefined);
        fetchedBrands = (
          await this.brandService.index(brandToIndex, undefined)
        )[0].paginatedResult;
      }
    }

    brands = brands.concat(fetchedBrands);
    return [brands, possibleBranchesIds];
  }

  private async branchesPopulate(
    branchId: Types.ObjectId,
    possibleBranches: Types.ObjectId[],
    brands: Brand[],
    phoneNumber: string,
  ) {
    let branches: BranchDocument[] = [],
      fetchedBranches: BranchDocument[] = [],
      branchToIndex: BranchToIndex = {} as any;

    if (possibleBranches && possibleBranches.length) {
      branchToIndex = this.constructBranchIndexDTO(
        phoneNumber,
        possibleBranches,
        undefined,
        undefined,
      ) as any;
      fetchedBranches = await this.branchService.index(branchToIndex);

      if (!fetchedBranches.length) {
        branchToIndex = this.constructBranchIndexDTO(
          undefined,
          possibleBranches,
          undefined,
          undefined,
        ) as any;
        fetchedBranches = await this.branchService.index(branchToIndex);
      }
    } else if (branchId) {
      fetchedBranches = [
        await this.branchService.get_details(branchId.toHexString()),
      ] as any;
    } else if (brands && brands.length) {
      branchToIndex = this.constructBranchIndexDTO(
        undefined,
        undefined,
        brands[0]['_id'],
        undefined,
      ) as any;
      fetchedBranches = await this.branchService.index(branchToIndex);
    }
    branches = branches.concat(fetchedBranches);

    return branches;
  }

  // Helper Functions
  private convertArrayOfStringToArrayOfObjects(
    arr: string[],
  ): Types.ObjectId[] {
    return arr.map((x) => new Types.ObjectId(x));
  }

  // Companies HelperFunctions
  private constructCompanyIndexDto(phoneNumber: string) {
    return {
      search_type: 'phone',
      search_key: phoneNumber,
      sort_type: '',
      offset: 0,
      limit: 1000,
      currentUser: {},
    };
  }

  // Brands Helper Functions
  private constructBrandIndexDTO(
    phoneNumber: string,
    companyId: Types.ObjectId,
  ) {
    return {
      searchType: BrandSearchType.PHONE_NUMBER,
      search_key: phoneNumber,
      companyId: companyId?.toHexString(),
      branches: '',
      sortType: BrandSortType.DATE_CREATED_ASC,
      offset: 0,
      limit: 1000,
      currentUser: {},
    };
  }

  private fetchingBrandsIdsFromBranches(
    branches: BranchDocument[],
    phoneNumber: string,
  ) {
    const brandIds: Types.ObjectId[] = [],
      branchesIds: Types.ObjectId[] = [];
    for (let i = 0; i < branches.length; i++) {
      if (branches[i].brands) {
        for (let j = 0; j < branches[i].brands.length; j++) {
          if (
            branches[i].brands[j].phoneNumber == phoneNumber &&
            branches[i].brands[j]._id &&
            isValidObjectId(branches[i].brands[j]._id)
          ) {
            branchesIds.push(new Types.ObjectId(branches[i]['_id']));
            const brandBrandObjectId =
              typeof branches[i].brands[j]._id == 'string'
                ? new Types.ObjectId(branches[i].brands[j]._id)
                : branches[i].brands[j]._id;

            const indexCheck = brandIds.findIndex((x) => {
              x.equals(brandBrandObjectId);
            });
            if (indexCheck == -1) {
              brandIds.push(brandBrandObjectId);
            }
          }
        }
      }
    }
    return [brandIds, branchesIds];
  }

  //Branch Helper Functions
  private constructBranchIndexDTO(
    phoneNumber: string,
    branches: Types.ObjectId[],
    brandId: Types.ObjectId,
    companyId: Types.ObjectId,
  ) {
    return {
      search_type: phoneNumber ? 'phone' : '',
      search_key: phoneNumber,
      sort_type: '',
      offset: 0,
      limit: 1000,
      currentUser: {},
      company: companyId,
      branches: branches,
      brandId: brandId,
      sortBy: '',
      month: '',
      deliveryArea: '',
      branch: '',
    };
  }
}
