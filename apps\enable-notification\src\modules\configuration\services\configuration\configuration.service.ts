import {
  Chat<PERSON>onfig<PERSON><PERSON>,
  Cha<PERSON><PERSON><PERSON>ider,
  Configuration<PERSON><PERSON>,
  <PERSON>ailProvider,
  PushNotificationProvider,
  SmsConfigDto,
  SmsProvider,
  TemplateOwner,
  UpdateConfigurationByOwnerDto,
} from '@app/shared-stuff';
import { HttpStatus, Injectable } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';
import { InjectModel } from '@nestjs/mongoose';
import { plainToInstance } from 'class-transformer';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import {
  chatProviderToConfigKeyMapping,
  smsProviderToConfigKeyMapping,
} from '../../constants';
import {
  Configuration,
  ConfigurationDocument,
} from '../../models/configuration.model';
import { OwnerConfigurationObject } from '../../types/dtos/config.dto';
import { ConfigurationServiceInterface } from './configuration.service.interface';

@Injectable()
export class ConfigurationService implements ConfigurationServiceInterface {
  private defaultConfiguration: ConfigurationDocument;
  private defaultConfigurationInjectedAt: Date = moment.utc().toDate();
  private defaultConfigurationPlaceholder = 'XXXX';

  //private configurationsCache: OwnerConfigurationObject[] = [];

  constructor(
    @InjectModel(Configuration.name)
    private configurationModel: Model<ConfigurationDocument, Configuration>,
  ) {}

  async findConfigurationByOwnerId(
    ownerId: Types.ObjectId,
  ): Promise<ConfigurationDocument> {
    // const cachedConfig = this.findFromCache(ownerId);
    // if (cachedConfig) return cachedConfig.configuration;

    const configuration = await this.getCurrentConfiguration(
      { _id: ownerId, name: '', smsSenderId: '', emailSenderId: '' },
      false,
    );
    return configuration;
  }

  async hasWhatsappKey(ownerId: Types.ObjectId): Promise<boolean> {
    const configuration = await this.findConfigurationByOwnerId(ownerId);

    const hasKey = Boolean(configuration?.chatConfig?.ebChatConfig?.apiKey);
    const isNotPlaceholder =
      configuration?.chatConfig?.ebChatConfig?.apiKey !==
      this.defaultConfigurationPlaceholder;

    return hasKey && isNotPlaceholder;
  }

  async findOrCreateConfiguration(
    owner: TemplateOwner | undefined,
  ): Promise<ConfigurationDocument> {
    if (!owner) {
      return await this.handleDefaultConfiguration();
    }
    // START UPDATE owner configuration
    return await this.handleOwnerConfiguration(owner);
  }

  private async handleOwnerConfiguration(owner: TemplateOwner) {
    // let ownerConfiguration: OwnerConfigurationObject;
    //   this.configurationsCache.find((conf) => conf.ownerId.equals(owner._id));

    // if (
    //   !ownerConfiguration ||
    //   moment.utc(ownerConfiguration.injectedAt).diff(moment.utc(), 'hours') >= 3
    // ) {
    // Means that the configuration is existed but need to be injected again
    // if (ownerConfiguration) {
    //   this.removeFromCache(owner._id);
    // }

    // Create New Owner Configuration OBJECT and push it to the array
    const ownerConfiguration: OwnerConfigurationObject = {
      ownerId: new Types.ObjectId(owner._id),
      configuration: await this.getCurrentConfiguration(owner),
      injectedAt: moment.utc().toDate(),
    };
    // this.configurationsCache.push(ownerConfiguration);
    //}

    return ownerConfiguration.configuration;
  }

  // private removeFromCache(ownerId: Types.ObjectId) {
  //   this.configurationsCache = this.configurationsCache.filter(
  //     (conf) => !conf.ownerId.equals(ownerId),
  //   );
  // }

  private async handleDefaultConfiguration() {
    if (
      !this.defaultConfiguration ||
      moment
        .utc(this.defaultConfigurationInjectedAt)
        .diff(moment.utc(), 'hours') >= 3
    ) {
      this.defaultConfiguration = await this.getCurrentConfiguration(undefined);
      this.defaultConfigurationInjectedAt = moment.utc().toDate();
    }
    return this.defaultConfiguration;
  }

  private async getCurrentConfiguration(
    owner: TemplateOwner | undefined,
    createIfNotExists = true,
  ): Promise<ConfigurationDocument> {
    const configDocument =
      await this.configurationModel.findOne<ConfigurationDocument>(
        owner ? { 'owner._id': owner._id } : { owner: { $exists: false } },
      );
    if (configDocument) return configDocument;

    if (!createIfNotExists) return null;

    const configuration = await this.initConfiguration(owner);
    return await this.createConfiguration(configuration);
  }

  private async initConfiguration(
    owner: TemplateOwner,
  ): Promise<Configuration> {
    const defaultConfiguration = await this.getDefaultConfiguration();
    const configuration: ConfigurationDto = {
      smsConfig: defaultConfiguration?.smsConfig ?? {
        provider: SmsProvider.MITTO,
        mittoConfig: {
          apiKey: this.defaultConfigurationPlaceholder,
          baseUrl: this.defaultConfigurationPlaceholder,
        },
      },
      otpConfig: defaultConfiguration?.otpConfig ?? {
        provider: SmsProvider.MITTO,
        mittoConfig: {
          apiKey: this.defaultConfigurationPlaceholder,
          baseUrl: this.defaultConfigurationPlaceholder,
        },
      },
      emailConfig: defaultConfiguration?.emailConfig ?? {
        provider: EmailProvider.MAIL_GUN,
        mailGunConfig: {
          domain: this.defaultConfigurationPlaceholder,
          apiKey: this.defaultConfigurationPlaceholder,
          username: this.defaultConfigurationPlaceholder,
        },
      },
      chatConfig: defaultConfiguration?.chatConfig ?? {
        provider: ChatProvider.EB_CHAT,
        ebChatConfig: {
          apiKey: this.defaultConfigurationPlaceholder,
        },
      },
      pushNotificationConfig: defaultConfiguration?.pushNotificationConfig ?? {
        provider: PushNotificationProvider.FCM,
        fcmConfig: {
          clientEmail: this.defaultConfigurationPlaceholder,
          privateKey: this.defaultConfigurationPlaceholder,
          projectId: this.defaultConfigurationPlaceholder,
          databaseURL: this.defaultConfigurationPlaceholder,
          fcmWebPushLink: this.defaultConfigurationPlaceholder,
          icon: this.defaultConfigurationPlaceholder,
          badge: this.defaultConfigurationPlaceholder,
        },
      },
      owner,
    };
    const configurationInstance = plainToInstance(Configuration, configuration);
    return configurationInstance;
  }

  private async createConfiguration(
    configuration: Configuration,
  ): Promise<ConfigurationDocument> {
    return await this.configurationModel.create(configuration);
  }

  private async getDefaultConfiguration() {
    return await this.configurationModel
      .findOne({ owner: { $exists: false } })
      .exec();
  }

  async updateConfig({
    owner,
    smsConfig,
    chatConfig,
    otpConfig,
    walletConfig,
  }: UpdateConfigurationByOwnerDto): Promise<ConfigurationDocument> {
    const templateOwner = plainToInstance(TemplateOwner, owner);
    const configuration = await this.getCurrentConfiguration(templateOwner);
    if (smsConfig) {
      const needsDefaultSmsConfig = this.getNeedsDefaultSmsConfig(
        configuration,
        smsConfig,
      );

      if (needsDefaultSmsConfig) {
        smsConfig = await this.getSmsConfigWithDefaultProviderConfig(smsConfig);
      }

      configuration.smsConfig = { ...configuration.smsConfig, ...smsConfig };
    }

    if (otpConfig) {
      const needsDefaultSmsConfig = this.getNeedsDefaultSmsConfig(
        configuration,
        otpConfig,
      );

      if (needsDefaultSmsConfig) {
        otpConfig = await this.getOtpConfigWithDefaultProviderConfig(otpConfig);
      }

      configuration.otpConfig = { ...configuration.otpConfig, ...otpConfig };
    }

    if (chatConfig) {
      const needsDefaultChatConfig = this.getNeedsDefaultChatConfig(
        configuration,
        chatConfig,
      );

      if (needsDefaultChatConfig) {
        chatConfig =
          await this.getChatConfigWithDefaultProviderConfig(chatConfig);
      }

      configuration.chatConfig = { ...configuration.chatConfig, ...chatConfig };
    }

    configuration.walletConfig = {
      ...configuration.walletConfig,
      ...walletConfig,
    };

    // this.removeFromCache(configuration.owner._id);
    await configuration.save();
    return configuration;
  }

  getNeedsDefaultSmsConfig(
    configuration: ConfigurationDocument,
    smsConfig: SmsConfigDto,
  ): boolean {
    const oldProvider = configuration.smsConfig.provider;
    const newProvider = smsConfig.provider;

    if (newProvider === SmsProvider.NO_PROVIDER) return false;

    const hasNewSmsProvider = oldProvider !== newProvider;

    const smsProviderConfigKey = smsProviderToConfigKeyMapping[newProvider];
    const hasNewSmsConfig = smsConfig.hasOwnProperty(smsProviderConfigKey);

    return hasNewSmsProvider && !hasNewSmsConfig;
  }

  async getSmsConfigWithDefaultProviderConfig(
    smsConfig: SmsConfigDto,
  ): Promise<SmsConfigDto> {
    const defaultConfiguration = await this.getDefaultConfiguration();
    const smsProviderConfigKey =
      smsProviderToConfigKeyMapping[smsConfig.provider];

    if (!defaultConfiguration?.smsConfig?.hasOwnProperty(smsProviderConfigKey))
      throw new RpcException({
        statusCode: HttpStatus.BAD_REQUEST,
        message: `A default configuration could not be found for ${smsConfig.provider}, please include smsConfig.${smsProviderConfigKey} or contact your system administrator.`,
        stackOrResponse: { defaultConfiguration },
        data: { smsConfig },
      });

    return {
      ...smsConfig,
      [smsProviderConfigKey]:
        defaultConfiguration.smsConfig[smsProviderConfigKey],
    };
  }

  async getOtpConfigWithDefaultProviderConfig(
    otpConfigDto: SmsConfigDto,
  ): Promise<SmsConfigDto> {
    const defaultConfiguration = await this.getDefaultConfiguration();
    const otpProviderConfigKey =
      smsProviderToConfigKeyMapping[otpConfigDto.provider];
    if (!defaultConfiguration?.otpConfig?.hasOwnProperty(otpProviderConfigKey))
      throw new RpcException({
        statusCode: HttpStatus.BAD_REQUEST,
        message: `A default configuration could not be found for ${otpConfigDto.provider}, please include otpConfig.${otpProviderConfigKey} or contact your system administrator.`,
        stackOrResponse: { defaultConfiguration },
        data: { otpConfigDto },
      });

    return {
      ...otpConfigDto,
      [otpProviderConfigKey]:
        defaultConfiguration.otpConfig[otpProviderConfigKey],
    };
  }

  getNeedsDefaultChatConfig(
    configuration: ConfigurationDocument,
    chatConfig: ChatConfigDto,
  ): boolean {
    const oldProvider = configuration.chatConfig?.provider;
    const newProvider = chatConfig.provider;

    if (newProvider === ChatProvider.NO_PROVIDER) return false;

    const hasNewChatProvider = newProvider !== oldProvider;

    const chatProviderConfigKey = chatProviderToConfigKeyMapping[newProvider];
    const hasNewChatConfig = chatConfig.hasOwnProperty(chatProviderConfigKey);

    return hasNewChatProvider && !hasNewChatConfig;
  }

  async getChatConfigWithDefaultProviderConfig(
    chatConfig: ChatConfigDto,
  ): Promise<ChatConfigDto> {
    const defaultConfiguration = await this.getDefaultConfiguration();
    const chatProviderConfigKey =
      chatProviderToConfigKeyMapping[chatConfig.provider];

    if (
      !defaultConfiguration?.chatConfig?.hasOwnProperty(chatProviderConfigKey)
    )
      throw new RpcException({
        statusCode: HttpStatus.BAD_REQUEST,
        message: `A default configuration could not be found for ${chatConfig.provider}, please include chatConfig.${chatProviderConfigKey} or contact your system administrator.`,
        stackOrResponse: { defaultConfiguration },
        data: { chatConfig },
      });

    return {
      ...chatConfig,
      [chatProviderConfigKey]:
        defaultConfiguration.chatConfig[chatProviderConfigKey],
    };
  }
}
