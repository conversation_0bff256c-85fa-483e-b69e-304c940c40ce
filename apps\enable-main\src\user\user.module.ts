import { SharedStuffModule } from '@app/shared-stuff';
import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { BrandModule } from '../brand/brand.module';
import { DeliveryModule } from '../delivery/delivery.module';
import { SharedModule } from '../shared/shared.module';
import { CompanyModule } from './../company/company.module';
import { RbacModule } from './../rbac/rbac.module';
import { ApikeyController } from './controllers/apikey/apikey.controller';
import { AuthController } from './controllers/auth/auth.controller';
import { UserController } from './controllers/user/user.controller';
import { ApiKeySchema } from './models/apikey.model';
import { ExpiredTokenSchema } from './models/expired_token.model';
import { UserSchema } from './models/user.model';
import { ApikeyService } from './services/apikey/apikey.service';
import { AuthService } from './services/auth/auth.service';
import { UserService } from './services/user/user.service';

@Module({
  controllers: [AuthController, UserController, ApikeyController],
  providers: [UserService, AuthService, ApikeyService],
  imports: [
    DeliveryModule,
    SharedStuffModule,
    ConfigModule,
    SharedModule,
    RbacModule,
    forwardRef(() => BrandModule),
    forwardRef(() => CompanyModule),
    MongooseModule.forFeature([
      { name: 'User', schema: UserSchema },
      { name: 'ApiKey', schema: ApiKeySchema },
      { name: 'ExpiredToken', schema: ExpiredTokenSchema },
    ]),
  ],
  exports: [AuthService, ApikeyService, UserService, MongooseModule],
})
export class UserModule {}
