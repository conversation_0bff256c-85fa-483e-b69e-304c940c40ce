import {
  CollectionName,
  CustomCacheModule,
  OrderSchema,
} from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { MongooseModule } from '@nestjs/mongoose';
import { CompanyModule } from '../company/company.module';
import { CustomerCodeModule } from '../customer/modules/customer-code/customer-code.module';
import { CustomerRepositoryModule } from '../customer/modules/customer-repository/customer-repository.module';
import { CustomerTierInfoModule } from '../customer/modules/customer-tier-info/customer-tier-info.module';
import { CustomerTierModule } from '../customer/modules/customer-tier/customer-tier.module';
import { EnableModule } from '../integration/enable/enable.module';
import {
  IntegrationLog,
  IntegrationLogSchema,
} from '../integration/integration-log/models/integration.log.model';
import { ShortenUrlModule } from '../integration/shorten-url/shorten-url.module';
import { LoyaltyPointLogModule } from '../loyalty-point-log/loyalty-point-log.module';
import { LoyaltyTierLogModule } from '../loyalty-tier-log/loyalty-tier-log.module';
import { LoyaltyTierReadModule } from '../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.module';
import { OrderInvoiceModule } from '../order/modules/order-invoice/order-invoice.module';
import { UtilityController } from './utility.controller';
import { UtilityService } from './utility.service';

@Module({
  controllers: [UtilityController],
  providers: [UtilityService],
  imports: [
    MongooseModule.forFeature([
      { name: IntegrationLog.name, schema: IntegrationLogSchema },
      { name: CollectionName.ORDER, schema: OrderSchema },
    ]),
    CustomerRepositoryModule,
    CustomerCodeModule,
    CustomerTierModule,
    CustomerTierInfoModule,
    LoyaltyTierReadModule,
    LoyaltyTierLogModule,
    LoyaltyPointLogModule,
    OrderInvoiceModule,
    ShortenUrlModule,
    CompanyModule,
    EventEmitterModule,
    CustomCacheModule,
    EnableModule,
  ],
})
export class UtilityModule {}
