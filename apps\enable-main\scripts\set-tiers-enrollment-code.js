// EBL-4967 Set the enrollment code for the tiers
// This script is used to set the enrollment code for the tiers if it's not exist

function generateRandomString(length) {
  let result = '';
  let characters = 'abcdefghijklmnopqrstuvwxyz';
  let charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

db.loyaltytiers
  .find({ enrollmentCode: { $exists: false } })
  .forEach(function (tier) {
    let newCode = generateRandomString(16);
    db.loyaltytiers.updateOne(
      { _id: tier._id },
      { $set: { enrollmentCode: newCode } },
    );
  });
