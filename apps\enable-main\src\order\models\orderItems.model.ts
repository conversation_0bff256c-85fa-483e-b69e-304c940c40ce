import { CollectionName } from '@app/shared-stuff';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

export type OrderItemDocument = HydratedDocument<OrderItem>;

@Schema({ timestamps: true })
export class OrderItem {
  @Prop({
    type: Number,
    required: false,
  })
  index?: number;

  @Prop({
    type: String,
    required: true,
  })
  name: string;

  @Prop({
    type: String,
    required: false,
  })
  description: string;

  @Prop({
    type: Number,
    default: 1,
  })
  quantity: number;

  @Prop({
    type: Number,
    required: false,
    default: 0,
  })
  discount: number;

  @Prop({
    type: Number,
    required: false,
    default: 0,
  })
  totalAmountAfterDiscount: number;

  @Prop({
    type: Number,
    required: false,
    default: 0,
  })
  totalAmount: number;

  @Prop({
    type: Number,
    default: 1,
    required: true,
  })
  price: number;

  @Prop({
    type: String,
    required: false,
  })
  special_instructions: string;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: CollectionName.BRANCH,
  })
  branchId: Types.ObjectId;

  @Prop({
    type: {},
    required: false,
  })
  updatedBy: {};

  @Prop({
    type: {},
    required: false,
  })
  createdBy: {};
  @Prop({
    type: {},
    required: false,
  })
  assignedTo: {};
  @Prop({
    type: {},
    required: false,
  })
  deletedBy: {};

  @Prop({
    type: Date,
    required: false,
  })
  deletedAt: Date;

  @Prop({
    type: Types.ObjectId,
    ref: CollectionName.COMPANY,
    required: false,
  })
  company: Types.ObjectId;
}

export const OrderItemSchema = SchemaFactory.createForClass(OrderItem);
