import { TookanJobType } from '../enums/tookan-job-type.enum';
import { TookanTaskStatus } from '../enums/tookan-task-status.enum';

export type TookanWebhookPayload = {
  job_id?: number;
  created_by?: number;
  order_id?: string;
  recurring_id?: string;
  recurring_count?: number;
  partner_order_id?: null;
  team_id?: number;
  vertical?: number;
  merchant_id?: number;
  geofence?: number;
  tags?: string;
  auto_assignment?: number;
  dispatcher_id?: null;
  job_hash?: string;
  has_pickup?: number;
  has_delivery?: number;
  is_routed?: number;
  pickup_delivery_relationship?: string;
  job_description?: string;
  job_pickup_datetime?: string;
  job_pickup_name?: string;
  job_pickup_phone?: string;
  job_delivery_datetime?: string;
  job_pickup_latitude?: string;
  job_pickup_longitude?: string;
  job_pickup_address?: string;
  job_pickup_email?: null | string;
  job_latitude?: string;
  job_longitude?: string;
  customer_username?: string;
  customer_email?: string;
  customer_phone?: string;
  job_address?: string;
  creation_datetime?: Date;
  fleet_id?: number | null;
  user_id?: number;
  fleet_rating?: number | null;
  customer_comment?: null;
  is_customer_rated?: number;
  customer_id?: number;
  arrived_datetime?: string;
  started_datetime?: string;
  completed_datetime?: string;
  acknowledged_datetime?: string;
  job_status?: TookanTaskStatus;
  is_active?: number;
  job_type?: TookanJobType;
  completed_by_admin?: number;
  open_tracking_link?: number;
  timezone?: string;
  job_time?: Date;
  job_date?: Date;
  job_time_utc?: Date;
  job_date_utc?: Date;
  total_distance_travelled?: null;
  form_id?: null;
  customer_rating?: number | null;
  driver_comment?: null;
  remarks?: null;
  barcode?: null | string;
  ride_type?: number;
  matched_pickup_delivery_relationship?: null;
  custom_field?: CustomField[];
  tracking_link?: string;
  job_vertical?: number;
  days_started?: string;
  tookan_shared_secret?: string;
  distance_in?: string;
  access_token?: string;
  domain?: string;
  agent_workflow?: number;
  is_merchant?: number;
  fleet_name?: null | string;
  fleet_email?: null | string;
  fleet_phone?: null | string;
  fleet_latitude?: number | null;
  fleet_longitude?: number | null;
  transport_type?: number | null;
  license?: null | string;
  transport_desc?: null | string;
  fleet_image?: string;
  job_details_by_fleet?: number | null;
  external_fleet_id?: null | string;
  fleet_vehicle_type?: number | null;
  fleet_vehicle_color?: null | string;
  fleet_vehicle_description?: null | string;
  mission_id?: null;
  mission_status?: null;
  task_status?: number;
  promo_used?: string;
  custom_fields?: CustomField[];
  ref_images?: any[];
  task_history?: any[];
  job_state?: string;
  task_state?: string;
  job_token?: string;
  job_time_formatted?: string;
  job_pickup_datetime_formatted?: string;
  job_delivery_datetime_formatted?: string;
  total_distance?: string;
  webhook_type?: number;
  format?: string;
  template_key?: string;
  is_internal?: number;
  full_tracking_link?: string;
};

export type CustomField = {
  label?: string;
  display_name?: string;
  data_type?: string;
  app_side?: string;
  required?: number;
  value?: number;
  data?: string;
  input?: string;
  template_id?: string;
  before_start?: number;
  dropdown?: {
    id?: number;
    value?: string;
  }[];
};
