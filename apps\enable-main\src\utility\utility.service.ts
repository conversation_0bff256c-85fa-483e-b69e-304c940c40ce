import {
  CollectionName,
  CustomerDocument,
  CustomerOrdableInfo,
  forEachAsync,
  LoggerService,
  LoyaltyPointLogAction,
  mapAsync,
  OrdableTransaction,
  Order,
  OrderSource,
  RecreateShopifyOrdersDto,
  ShopifyOrder,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import * as xlsx from 'xlsx';
import { CalendarSystemService } from '../company/services/calendar-system/calendar-system.service';
import { CompanyService } from '../company/services/company/company.service';
import { CustomerCodeServiceInterface } from '../customer/modules/customer-code/customer-code.service.interface';
import { CustomerRepositoryInterface } from '../customer/modules/customer-repository/customer.repository.interface';
import { CustomerTierInfoServiceInterface } from '../customer/modules/customer-tier-info/customer-tier-info.service.interface';
import { CustomerTierServiceInterface } from '../customer/modules/customer-tier/customer-tier.service.interface';
import { EnableService } from '../integration/enable/services/enable.service';
import {
  IntegrationLog,
  IntegrationLogDocument,
} from '../integration/integration-log/models/integration.log.model';
import { ShortenUrlServiceInterface } from '../integration/shorten-url/services/shorten-url.service.interface';
import { LoyaltyPointLogServiceInterface } from '../loyalty-point-log/services/loyalty-point-log.service.interface';
import { LoyaltyTierLogServiceInterface } from '../loyalty-tier-log/services/loyalty-tier-log.service.interface';
import { LoyaltyTierReadServiceInterface } from '../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.service.interface';

@Injectable()
export class UtilityService {
  private readonly logger = new LoggerService(UtilityService.name);

  constructor(
    @Inject(CustomerRepositoryInterface)
    private readonly customerRepository: CustomerRepositoryInterface,
    @Inject(CustomerCodeServiceInterface)
    private readonly customerCodeService: CustomerCodeServiceInterface,
    @Inject(CustomerTierInfoServiceInterface)
    private readonly customerTierInfoService: CustomerTierInfoServiceInterface,
    @Inject(CustomerTierServiceInterface)
    private readonly customerTierService: CustomerTierServiceInterface,
    @Inject(LoyaltyTierReadServiceInterface)
    private readonly loyaltyTierReadService: LoyaltyTierReadServiceInterface,
    @Inject(LoyaltyTierLogServiceInterface)
    private readonly loyaltyTierLogService: LoyaltyTierLogServiceInterface,
    @Inject(ShortenUrlServiceInterface)
    private readonly shortenUrlService: ShortenUrlServiceInterface,
    @Inject(LoyaltyPointLogServiceInterface)
    private readonly loyaltyPointLogService: LoyaltyPointLogServiceInterface,
    @InjectModel(IntegrationLog.name)
    private readonly integrationLogModel: Model<IntegrationLogDocument>,
    private readonly enableService: EnableService,
    private readonly companyService: CompanyService,
    private readonly calendarSystemService: CalendarSystemService,
    private readonly eventEmitter: EventEmitter2,
    @InjectModel(CollectionName.ORDER)
    private readonly orderModel: Model<Order>,
  ) {}

  async rectifyOrdablePoints(
    companyId: Types.ObjectId,
    file: Express.Multer.File,
    commit?: boolean,
  ) {
    const transactions = this.parseOrdableTransactions(file);
    const couponTransactions = transactions.filter((transaction) =>
      transaction['Discount Name'].includes('Coupon'),
    );

    const logs = ['Processing Transactions'];
    const deductions = new Map<string, number>();
    for (let i = 0; i < couponTransactions.length; i++) {
      const transaction = couponTransactions[i];

      const order = await this.orderModel.findOne({
        company: companyId,
        invoice_number: transaction['Transaction ID'],
      });

      if (!order) {
        logs.push(
          `    [Error] No order found for invoice number '${transaction['Transaction ID']}'`,
        );
        continue;
      }

      const pointsRedeemed = parseInt(
        transaction['Discount Name'].match(/(\d+) Points/)[0],
      );
      if (order.loyaltyProgress?.pointsRedeemed === pointsRedeemed) {
        logs.push(
          `    [${order.code}] Coupon was correct parsed for invoice number '${transaction['Transaction ID']}'`,
        );
        continue;
      }

      if (order.loyaltyProgress?.pointsRedeemed) {
        logs.push(
          `    [${order.code}] Wrong coupon was parsed for invoice number '${transaction['Transaction ID']}'`,
        );
        continue;
      }

      const customerId = order.customer.toString();
      logs.push(
        `    [${order.code}] No coupon was parsed for invoice number '${transaction['Transaction ID']}'. Need to deduct ${pointsRedeemed} Points from customer ${customerId}`,
      );
      deductions.set(
        customerId,
        pointsRedeemed + (deductions.get(customerId) ?? 0),
      );
    }

    logs.push('Processing Customers');
    for (const [customerId, pointsRedeemed] of deductions) {
      const customer = await this.customerRepository.findById(
        new Types.ObjectId(customerId),
      );
      const newBalance = Math.max(0, customer.loyaltyPoints - pointsRedeemed);
      logs.push(
        `    [${customerId}] Old Balance: ${customer.loyaltyPoints}, Points To Deduct: ${pointsRedeemed}, New Balance: ${newBalance}`,
      );

      if (commit) {
        const oldBalance = customer.loyaltyPoints;
        customer.loyaltyPoints = newBalance;

        await this.customerRepository.updateOne(
          { _id: customer._id },
          { $set: { loyaltyPoints: customer.loyaltyPoints } },
        );
        await this.loyaltyPointLogService.create({
          action: LoyaltyPointLogAction.ON_COUPON_REDEEMED,
          oldBalance,
          loyaltyPointsEarned: -pointsRedeemed,
          newBalance: customer.loyaltyPoints,
          customerId: customer._id,
          orderSource: OrderSource.WEBSTORE,
          brandId: null,
          branchId: null,
        });

        this.eventEmitter.emit('customer.points.updated', customer);
      }
    }

    return logs;
  }

  private parseOrdableTransactions(
    file: Express.Multer.File,
  ): OrdableTransaction[] {
    const csvString = file.buffer.toString('utf-8');
    const workbook = xlsx.read(csvString, { type: 'string' });
    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];
    const transactions = xlsx.utils.sheet_to_json<OrdableTransaction>(sheet);
    return transactions;
  }

  async recreateShopifyOrders({
    since,
    companyId,
    storeId,
    commit,
    limit,
  }: RecreateShopifyOrdersDto) {
    const erroredOrders = await this.integrationLogModel
      .find({
        action: 'createShopifyOrder',
        'responseBody.stackTrace': {
          $regex: 'Duplicate invoice number:',
          $options: 'i',
        },
        requestStatus: 'failure',
        createdAt: { $gt: since },
      })
      .sort({ createdAt: 1 });
    this.logger.log(
      `[RecreateShopifyOrders] Found ${erroredOrders.length} failed orders since ${since.toUTCString()}`,
    );

    for (let i = 0; i < erroredOrders.length; i++) {
      if (i > limit) return;
      const log = erroredOrders[i];

      const exists =
        log.requestBody.order_number &&
        (await this.orderModel.exists({
          company: companyId,
          invoice_number: log.requestBody.order_number,
        }));
      if (exists) {
        this.logger.log(
          `[RecreateShopifyOrders] Skipping existing log(${log._id.toString()}), order exists with invoice number ${log.requestBody.order_number}`,
        );
        continue;
      }

      this.logger.log(
        `[RecreateShopifyOrders] Processing log ${log._id.toString()}`,
      );
      if (!commit) continue;
      await this.enableService.createShopifyOrder(
        new Types.ObjectId(storeId),
        log.requestBody as ShopifyOrder,
      );
    }
  }

  public async recomputeTier(customerIds: Types.ObjectId[]) {
    const customers = await this.customerRepository.findManyById(customerIds);
    await forEachAsync(customers, async (customer) => {
      const company = await this.companyService.findById(customer.company);

      const { orderRate, amountSpent, pointsRate } =
        await this.customerTierInfoService.getLoyaltyTierProgramProgress(
          customer,
          company,
        );

      const highestEligibleTier =
        await this.loyaltyTierReadService.findHighestEligibleTierAfter(
          company._id,
          await this.loyaltyTierLogService.findHighestAssignedTierId(customer, {
            startDate: this.calendarSystemService.getStartDate(company),
            endDate: this.calendarSystemService.getEndDate(customer, company),
          }),
          orderRate,
          amountSpent,
          pointsRate,
        );

      if (
        !customer.loyaltyTier?.isVipTier &&
        customer.loyaltyTier?._id.toString() !==
          highestEligibleTier?._id.toString()
      ) {
        this.logger.log(
          `Customer ${customer._id} updated from ${customer.loyaltyTier?.nameEn ?? 'No Tier'} to ${highestEligibleTier?.nameEn ?? 'No Tier'}`,
        );
        await this.customerTierService.updateCustomerTier(
          customer,
          highestEligibleTier?._id,
          this.loyaltyTierLogService.onForceTierRecomputation,
        );
      }
    });
  }

  public async resyncCustomers(customerIds: Types.ObjectId[]) {
    const customers = await this.customerRepository.findManyById(customerIds);
    customers.forEach((customer) =>
      // Re-syncs customers on ordable and refreshes their pass
      this.eventEmitter.emit('customer.points.updated', customer),
    );
  }

  public async regenerateOrdableLinksForCustomers(
    customerIds: Types.ObjectId[],
  ) {
    const customers = await this.customerRepository.findManyById(customerIds);
    await forEachAsync(customers, (customer) =>
      this.regenerateOrdableLinkForCustomer(customer),
    );
  }

  private async regenerateOrdableLinkForCustomer(customer: CustomerDocument) {
    const newOrdableStores = Object.fromEntries(
      await mapAsync(Object.entries(customer.ordableStores), (storeEntry) =>
        this.regenerateOrdableLinkForStore(customer, storeEntry),
      ),
    );

    await customer.updateOne({ ordableStores: newOrdableStores });
  }

  private async regenerateOrdableLinkForStore(
    customer: CustomerDocument,
    [storeId, customerOrdableInfo]: [string, CustomerOrdableInfo],
  ): Promise<[string, CustomerOrdableInfo]> {
    if (!customerOrdableInfo.ordableLinkRaw)
      return [storeId, customerOrdableInfo];

    const brandName =
      customerOrdableInfo.ordableBrands.length === 1
        ? customerOrdableInfo.ordableBrands[0].name
        : await this.getCompanyName(customer.company);

    const shortCode =
      await this.customerCodeService.getOrGenerateCustomerShortCode(customer);

    const ordableLink = await this.shortenUrlService.shortenUrl({
      url: customerOrdableInfo.ordableLinkRaw,
      code: `${brandName.replaceAll(/\s/g, '')}-${shortCode}`,
      canExpire: false,
    });

    return [storeId, { ...customerOrdableInfo, ordableLink }];
  }

  private async getCompanyName(companyId: Types.ObjectId): Promise<string> {
    const company = await this.companyService.findById(companyId);
    return company.name;
  }
}
