import {
  CreateShortenUrlDto,
  ShortenUrl,
  ShortenUrlCodeDto,
} from '@app/shared-stuff';

export interface ShortenUrlServiceInterface {
  shortenUrl(createShortenUrlDto: CreateShortenUrlDto): Promise<string>;

  create(createShortenUrlDto: CreateShortenUrlDto): Promise<ShortenUrl>;

  getDetails(shortenUrlCodeDto: ShortenUrlCodeDto): Promise<unknown>;
}

export const ShortenUrlServiceInterface = Symbol('ShortenUrlServiceInterface');
