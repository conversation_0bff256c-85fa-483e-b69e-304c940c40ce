import {
  CollectionName,
  CompanyDocument,
  CustomerDocument,
  DeliveryMethod,
  DeliveryThirdPartyName,
  DriverDocument,
  LoggerService,
  OrderDocument,
  OrderLogActionEnum,
  OrderPaymentMethod,
  OrderPaymentStatus,
  OrderStatusEnum,
  OrderTransitionTrigger,
  TookanWebhookUser,
} from '@app/shared-stuff';
import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import { BranchService } from '../../../branch/services/branch/branch.service';
import { CompanyService } from '../../../company/services/company/company.service';
import { CustomerReadServiceInterface } from '../../../customer/modules/customer-read/customer-read.service.interface';
import { ThirdPartyTaskCreationDto } from '../../../delivery/dto/third-party-task-creation.dto';
import { TookanJobType } from '../../../delivery/enums/tookan-job-type.enum';
import { TookanTaskStatus } from '../../../delivery/enums/tookan-task-status.enum';
import { DeliveryConfigurationDocument } from '../../../delivery/models/delivery-configuration.model';
import { DeliveryConfigurationServiceInterface } from '../../../delivery/services/delivery-configuration/delivery-configuration.service.interface';
import { DriverService } from '../../../delivery/services/driver/driver.service';
import { TookanWebhookPayload } from '../../../delivery/types/tookan-webhook-payload.type';
import { FirstPartyTaskState } from '../../dto/order.dto';
import { OrderLogServiceInterface } from '../interfaces/order-log.service.interface';
import { OrderDeliveryService } from '../order-delivery/order-delivery.service';
import { OrderPaymentService } from '../order-payment/order-payment.service';
import { OrderStatusService } from '../order-status/order-status.service';

@Injectable()
export class TookanTaskWebhookService {
  private readonly loggerService = new LoggerService(OrderDeliveryService.name);

  constructor(
    @InjectModel(CollectionName.ORDER) private orderModel: Model<OrderDocument>,
    private companyService: CompanyService,
    private branchService: BranchService,
    private driverService: DriverService,
    private orderStatusService: OrderStatusService,
    private eventEmitter: EventEmitter2,
    @Inject(forwardRef(() => OrderPaymentService))
    private orderPaymentService: OrderPaymentService,
    @Inject(DeliveryConfigurationServiceInterface)
    private deliveryConfigurationService: DeliveryConfigurationServiceInterface,
    @Inject(CustomerReadServiceInterface)
    private customerReadService: CustomerReadServiceInterface,
    private orderDeliveryService: OrderDeliveryService,
    @Inject('OrderLogServiceInterface')
    private readonly orderLogService: OrderLogServiceInterface,
  ) {}

  async handleTookanTaskUpdated(
    taskData: TookanWebhookPayload,
    custom = false,
  ) {
    if (taskData.order_id.includes('-D-')) {
      this.eventEmitter.emit('tookan-updated-for-non-order', taskData);
      return;
    }
    try {
      const order = await this.orderModel.findOne({ code: taskData.order_id });
      if (!order) {
        throw new NotFoundException({
          message: `Order not found for order ID "${taskData.order_id}"`,
          statusCode: 404,
        });
      }

      this.loggerService.log(
        `[Tookan][Status Update] Received for ${order.code}`,
        { order, taskData, custom },
      );

      const company = await this.companyService.get_details(order.company);
      const deliveryConfiguration =
        await this.deliveryConfigurationService.findByCompanyId(company._id);
      const customer = await this.customerReadService.findOne(
        order.customer.toHexString(),
        order.company,
      );
      const branch = await this.branchService.get_details(
        typeof order.branch !== 'string'
          ? order.branch._id
          : new Types.ObjectId(order.branch),
      );
      const driver = order.driver
        ? await this.driverService.get_details(order.driver.toHexString())
        : undefined;

      const isCustomTookanTask =
        [
          DeliveryThirdPartyName.enableDelivery,
          DeliveryThirdPartyName.deliveryHub,
          DeliveryThirdPartyName.hsDelivery,
          DeliveryThirdPartyName.enableRiders,
        ].includes(order.deliveryParty) || custom;
      const isPickupTask = taskData.job_type === TookanJobType.PICKUP;
      const isDeliveryTask = taskData.job_type === TookanJobType.DELIVERY;
      const isJobUnassigned =
        taskData.job_status === TookanTaskStatus.UNASSIGNED;
      const isJobStarted = taskData.job_status === TookanTaskStatus.STARTED;
      const isJobSuccessful =
        taskData.job_status === TookanTaskStatus.SUCCESSFUL;
      const isJobFailed = taskData.job_status === TookanTaskStatus.FAILED;
      const autoAssignmentFailed =
        isPickupTask &&
        isJobUnassigned &&
        order.autoAssign &&
        taskData.template_key == 'AUTO_ASSIGN_FAILED';
      const logs: string[] = [];

      if (isJobStarted && isPickupTask) {
        logs.push('PICKUP STARTED');
        order.firstPartyState = FirstPartyTaskState.PICKUP_STARTED;
      } else if (isJobSuccessful && isPickupTask) {
        logs.push('PICKUP SUCCESSFUL');
        if (order.parentId) {
          order.is_ready = true;
          await this.orderStatusService.changeOrderStatus(
            order,
            OrderStatusEnum.COMPLETED,
            OrderTransitionTrigger.MANUAL,
            TookanWebhookUser,
          );
          logs.push('\tCHILD ORDER COMPLETED');
        }
        order.pickedUpOn = moment().utc().toDate();
        order.firstPartyState = FirstPartyTaskState.PICKUP_ENDED;
      } else if (isJobStarted && isDeliveryTask) {
        logs.push('DELIVERY STARTED');
        order.startedDeliveringOn = moment().utc().toDate();
        order.firstPartyState = FirstPartyTaskState.DELIVERY_STARTED;
        await this.orderStatusService.changeOrderStatus(
          order,
          OrderStatusEnum.IN_ROUTE,
          OrderTransitionTrigger.DELIVERY_TASK_STARTED,
          TookanWebhookUser,
        );
        logs.push('\tORDER STATUS EN ROUTE');
      } else if (isJobSuccessful && isDeliveryTask) {
        logs.push('DELIVERY SUCCESSFUL');
        logs.push(
          ...(await this.handleSuccessfulDeliveryTask(
            order,
            customer,
            branch,
            company,
            driver,
          )),
        );
      } else if (isJobFailed) {
        logs.push('TASK FAILED');
        order.is_ready = false;
        if (order.deliveryParty != DeliveryThirdPartyName.deliveryHub) {
          order.driver = undefined;
          order.assigned_driver_name = '';
        }
      } else if (autoAssignmentFailed && !isCustomTookanTask) {
        logs.push('AUTO ASSIGN FAILED');
        await this.handleAutoAssignmentFailed(
          order,
          deliveryConfiguration,
          company,
          branch,
          customer,
        );
      } else if (autoAssignmentFailed && isCustomTookanTask) {
        logs.push('AUTO ASSIGN FAILED FOR TOOKAN BASED THIRD PARTIES');
        order.assignmentMessage = 'Auto-assignment Failed';
      } else {
        logs.push('UNHANDLED TASK UPDATE');
      }

      if (taskData.fleet_id && !isCustomTookanTask) {
        logs.push('TASK ASSIGNED');
        await this.handleTaskAssignment(taskData, order);
      } else if (taskData.fleet_id) {
        logs.push('TASK ASSIGNED FOR TOOKAN BASED THIRD PARTIES');
        order.assignmentMessage = '';
      }

      await this.orderLogService.saveOrderLog(
        order,
        { requestedObject: logs.join('\n') },
        { responseObject: taskData },
        OrderLogActionEnum.ON_TOOKAN_TASK_UPDATED,
        order.createdBy,
      );
      this.loggerService.log(
        `[Tookan][Status Update] Processed for ${order.code}`,
        { logs, order, taskData },
      );

      await order.save();
      return order;
    } catch (error) {
      this.loggerService.error(
        `[Tookan][Status Update] Error processing task for ${taskData.order_id}`,
        { error },
      );
      throw error;
    }
  }

  async handleSuccessfulDeliveryTask(
    order: OrderDocument,
    customer: CustomerDocument,
    branch: any,
    company: CompanyDocument,
    driver: DriverDocument,
  ): Promise<string[]> {
    const logs: string[] = [];
    order.firstPartyState = FirstPartyTaskState.DELIVERY_ENDED;

    order.payment_status =
      order.payment_method != OrderPaymentMethod.online
        ? OrderPaymentStatus.COMPLETED
        : order.payment_status;
    order.deliveredOn = moment().utc().toDate();

    if (
      order.payment_method == OrderPaymentMethod.online &&
      order.payment_status != OrderPaymentStatus.COMPLETED
    ) {
      await this.orderPaymentService.removeOrderFromPayment(
        order,
        OrderPaymentMethod.cash,
        {
          ...TookanWebhookUser,
          name: 'Tookan Task Get Updated and the client didnt pay',
        },
      );
      logs.push('\tPAYMENT REMOVED FROM ORDER');

      await this.orderDeliveryService.tookanTaskProcessing({
        order,
        customer,
        branch,
        company,
        driver,
      });
      logs.push('\tTOOKAN TASK UPDATED');
    }

    await this.orderStatusService.changeOrderStatus(
      order,
      OrderStatusEnum.COMPLETED,
      OrderTransitionTrigger.FIRST_PARTY_DELIVERED,
      TookanWebhookUser,
    );
    logs.push('\tORDER STATUS COMPLETED');
    return logs;
  }

  private async handleAutoAssignmentFailed(
    order: OrderDocument,
    deliveryConfiguration: DeliveryConfigurationDocument,
    company: CompanyDocument,
    branch: any,
    customer: CustomerDocument,
  ) {
    order.is_ready = false;
    order.assigned_driver_name = 'Auto Allocation Failed';

    // Change The Delivery Method To ThirdParty and the DeliveryParty to be the default one
    if (
      deliveryConfiguration &&
      deliveryConfiguration?.thirdPartyConfiguration?.defaultThirdParty &&
      deliveryConfiguration?.thirdPartyConfiguration?.thirdParties.includes(
        deliveryConfiguration?.thirdPartyConfiguration?.defaultThirdParty,
      ) &&
      company.isThirdPartyFallbackEnabled === true
    ) {
      order.deliveryMethod = DeliveryMethod.THIRD_PARTY;
      order.deliveryParty =
        deliveryConfiguration?.thirdPartyConfiguration?.defaultThirdParty;
      order.deliveryTaskCreated = false;
      await order.save();
      const thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto = {
        order: order,
        company: company,
        branch: branch,
        customer: customer,
        pickupLocation: order.pickupLocation,
        currentUser: order.createdBy,
      };
      await this.orderDeliveryService.handleThirdPartyTasks(
        order.deliveryParty,
        thirdPartyTaskCreationDto,
      );
      //Cancel Current Tookan TASK
      await this.orderDeliveryService.cancelTookanTask(order);

      await this.orderLogService.saveOrderLog(
        order,
        { requestedObject: {} },
        { responseObject: order },
        OrderLogActionEnum.ON_TOOKAN_AUTO_ALLOCATION_FAILED,
        order.createdBy,
      );
    }
  }

  private async handleTaskAssignment(
    taskData: TookanWebhookPayload,
    order: OrderDocument,
  ) {
    const driver = await this.driverService.get_details(
      taskData.fleet_id.toString(),
    );

    if (
      driver &&
      !order.driverManuallyAssigned &&
      !order.autoAssign &&
      !order.parentId
    ) {
      // Self Assignment Happens
      order.is_ready = true;
      await this.orderStatusService.changeOrderStatus(
        order,
        OrderStatusEnum.PENDING_PICKUP,
        OrderTransitionTrigger.PICKUP_TASK_STARTED,
        TookanWebhookUser,
      );
    }

    if (driver) {
      order.driver = driver._id;
      order.assigned_driver_name =
        driver.first_name + ' ' + (driver.last_name ?? '');
    } else {
      order.assigned_driver_name = taskData.fleet_name;
    }
    if (order.parentId) {
      const parentOrder = await this.orderModel.findById(order.parentId);
      await this.orderModel.updateMany(
        {
          _id: {
            $in: [
              order.parentId,
              ...parentOrder.children.map((o) => o.orderId),
            ],
          },
        },
        { $set: { assigned_driver_name: order.assigned_driver_name } },
      );
    }
  }
}
