import { EmailRepository } from './repositories/email.repository';
import { Email, EmailSchema } from './models/email.model';
import { Module } from '@nestjs/common';
import { EmailService } from './services/email/email.service';
import { MongooseModule } from '@nestjs/mongoose';
import { MailgunService } from './services/integrations/mailgun/mailgun.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ schema: EmailSchema, name: Email.name }]),
  ],
  providers: [
    {
      provide: 'EmailServiceInterface',
      useClass: EmailService,
    },
    {
      provide: 'EmailRepositoryInterface',
      useClass: EmailRepository,
    },
    MailgunService,
  ],
  exports: ['EmailServiceInterface'],
})
export class EmailModule {}
