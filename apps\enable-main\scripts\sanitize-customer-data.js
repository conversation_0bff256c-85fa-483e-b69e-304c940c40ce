function main() {
  const startTime = Date.now();

  print(
    '--------------------------------- Update Users Country Codes---------------------------------',
  );
  setCountryCodeToUsersAndCompany();

  print(
    '--------------------------------- Sentize Not Dulicated Customers---------------------------------',
  );
  sanitizeNotDuplicateCustomers();

  findAndUpdateCustomersDuplications(startTime);

  updateDataInCollection(startTime);
}

function sanitizeNotDuplicateCustomers() {
  const bulkUpdate = [];
  db.customers
    .aggregate([
      {
        $match: {
          phone: {
            $regex: /^(?:\+|00)?(?:974|20|961|962)/,
          },
        },
      },
      {
        $addFields: {
          countryCode: {
            $regexFind: {
              input: '$phone',
              regex: /^(?:\+|00)?(974|20|961|962)/,
            },
          },
        },
      },
      {
        $addFields: {
          phoneNumber: {
            $regexFind: {
              input: '$phone',
              regex: /^(?:\+|00)?(?:974|20|961|962)?(\d{8,})/,
            },
          },
        },
      },
      {
        $project: {
          _id: 1,
          phone: 1,
          newCountryCode: { $arrayElemAt: ['$countryCode.captures', 0] },
          newPhoneNumber: { $arrayElemAt: ['$phoneNumber.captures', 0] },
        },
      },
    ])
    .forEach((customer) => {
      const { newCountryCode, newPhoneNumber } = customer;

      bulkUpdate.push({
        updateOne: {
          filter: { _id: customer._id },
          update: {
            $set: {
              country_code: `+${newCountryCode}`,
              phone: newPhoneNumber,
            },
          },
        },
      });
    });
  db.customers.bulkWrite(bulkUpdate, { ordered: false });
}

function findAndUpdateCustomersDuplications(startTime) {
  print(
    '--------------------------------- Customers Remove Special Characters---------------------------------',
  );
  sanitizeCustomers();

  print(`Time taken (Sanitize Customers) : ${Date.now() - startTime} ms`);

  print(
    '--------------------------------- Customers Being Formatted---------------------------------',
  );
  const newCustomers = [];
  let index = 0;
  db.customers
    .aggregate(buildAggregationPipeline())
    .forEach((customerGroup) => {
      const newCustomer = processCustomerGroup(customerGroup, index);
      newCustomers.push(newCustomer);
      index++;
    });
  print(`Result Of Duplications Removing: ${newCustomers.length}`);
  // print(newCustomers);
  print(
    `Time taken (Collect Customers duplicated) : ${Date.now() - startTime} ms`,
  );
  // Update Customers Data
  print(
    '--------------------------------- Customers Being Update ---------------------------------',
  );
  updateCustomers(newCustomers);

  print(
    '--------------------------------- Update Customer Related Logs---------------------------------',
  );
  updateCustomerLogs(newCustomers);

  print(
    `Time taken (Update Customer Information) : ${Date.now() - startTime} ms`,
  );

  print(`Time taken (Whole Process) : ${Date.now() - startTime} ms`);
}

function updateDataInCollection(startTime) {
  // Update the Payments for all of the New Customers
  print(
    '--------------------------------- Customers Payments Being Update ---------------------------------',
  );
  updateCustomerPayments();

  print(
    `Time taken (Update Customers Payments) : ${Date.now() - startTime} ms`,
  );

  print(
    '--------------------------------- Customers Orders Being Update ---------------------------------',
  );
  updateCustomersOrders();

  print(`Time taken (Update Customers Orders) : ${Date.now() - startTime} ms`);
}

function buildAggregationPipeline() {
  return [
    { $match: { deletedAt: null } },
    { $addFields: { cleanedPhoneNumber: getCleanedPhoneNumber() } },
    { $match: { cleanedPhoneNumber: { $ne: null } } },
    { $group: { _id: getGroupingId(), customers: { $push: '$$ROOT' } } },
    {
      $match: {
        'customers.1': { $exists: true },
        // '_id.phone': { $elemMatch: { $eq: '50055512' } },
      },
    },
  ];
}

function setCountryCodeToUsersAndCompany() {
  db.getCollection('users').updateMany({}, { $set: { countryCode: '+974' } });
  db.getCollection('companies').updateMany(
    {},
    { $set: { countryCode: '+974' } },
  );
}

function sanitizeCustomers() {
  const bulkWrites = db
    .getCollection('customers')
    .find({ phone: /\D/ })
    .toArray()
    .map((customer) => {
      const sanitizedPhoneNumber = customer.phone.replace(/\D/g, '');
      return {
        updateOne: {
          filter: { _id: customer._id },
          update: { $set: { phone: sanitizedPhoneNumber } },
        },
      };
    });

  if (bulkWrites.length > 0) {
    db.getCollection('customers').bulkWrite(bulkWrites, { ordered: false });
  } else {
    print('No documents found with non-digit characters in the phone field.');
  }

  print(
    '--------------------------------- Delete Customers That does not have Valid Phone ---------------------------------',
  );
  deleteCustomersThatDoesNotHavePhone();
}

function deleteCustomersThatDoesNotHavePhone() {
  const customers = db.customers.find({ phone: '' }).toArray();
  const customerIds = customers.map((customer) => customer._id);

  db.payments.deleteMany({ customer: { $in: customerIds } });
  db.orders.deleteMany({ customer: { $in: customerIds } });
  db.customers.deleteMany({ _id: { $in: customerIds } });
}

function getCleanedPhoneNumber() {
  return {
    $regexFind: {
      input: '$phone',
      regex: /^(?:\+|00)?(?:974|20|961|962)?(\d{8,})/,
      options: 'x',
    },
  };
}

function getGroupingId() {
  return {
    company: '$company',
    phone: '$cleanedPhoneNumber.captures',
  };
}

function updateCustomers(customers) {
  const bulkUpdate = [];
  const relatedCustomersIds = customers.reduce((acc, curr) => {
    acc = acc.concat(curr.relatedCustomers);
    return acc;
  }, []);

  customers.map((customer) => {
    bulkUpdate.push({
      updateOne: {
        filter: { _id: customer._id },
        update: { $set: customer },
      },
    });
  });

  bulkUpdate.push({
    deleteMany: {
      filter: {
        _id: { $in: relatedCustomersIds },
      },
    },
  });

  if (bulkUpdate.length > 0) {
    db.getCollection('customers').bulkWrite(bulkUpdate, { ordered: false });
  } else {
    print('No customers found  to update.');
  }
}

function updateCustomerLogs(customers) {
  const writes = customers.map((customer) => ({
    updateMany: {
      filter: { customerId: { $in: customer.relatedCustomers } },
      update: { $set: { customerId: customer._id } },
    },
  }));

  if (writes.length > 0) {
    db.loyaltytierlogs.bulkWrite(writes, { ordered: false });
    db.loyaltypointlogs.bulkWrite(writes, { ordered: false });
  } else {
    print('No points,tier Logs found  to update.');
  }
}

function updateCustomerDataInCollection(collectionName) {
  const bulkUpdate = db
    .getCollection('customers')
    .find(
      { 'relatedCustomers.0': { $exists: true } },
      { _id: 1, relatedCustomers: 1, full_name: 1, phone: 1, country_code: 1 },
    )
    .toArray()
    .map((customer) => ({
      updateMany: {
        filter: {
          customer: { $in: [...customer.relatedCustomers, customer._id] },
        },
        update: {
          $set: {
            customer: customer._id,
            customer_name: customer.full_name,
            customer_phone: customer.phone,
            country_code: customer.country_code,
          },
        },
      },
    }));
  if (bulkUpdate.length > 0) {
    db.getCollection(collectionName).bulkWrite(bulkUpdate, { ordered: false });
  } else {
    print(`No ${collectionName} found to update.`);
  }
}

function updateCustomerPayments() {
  updateCustomerDataInCollection('payments');
}

function updateCustomersOrders() {
  updateCustomerDataInCollection('orders');
}

function processCustomerGroup(customerGrouped, index) {
  print(`Index: ${index}`);
  const customersUpdatedAtSorted = sortCustomersByDate(
    customerGrouped.customers,
    'updatedAt',
  );
  const customerCreatedAtSorted = sortCustomersByDate(
    customerGrouped.customers,
    'createdAt',
  );
  const oldestCustomerBasedOnCreatedAt =
    customerCreatedAtSorted[customerCreatedAtSorted.length - 1];
  const newestCustomerBasedOnCreatedAt = customerCreatedAtSorted[0];

  const [firstOrder, latestOrder, ordersDocuments] = getCustomerOrders(
    customersUpdatedAtSorted.map((customer) => customer._id),
  );

  const ordersTotalAmount = getOrderTotalAmount(ordersDocuments);

  const payments = getCustomerPayments(customerCreatedAtSorted);

  const { _id } = oldestCustomerBasedOnCreatedAt;
  const { contact_channel, company, year, week, month, day, createdAt } =
    extractProperties(customerCreatedAtSorted.slice().reverse(), [
      'contact_channel',
      'company',
      'year',
      'week',
      'month',
      'day',
      'createdAt',
    ]);

  return {
    _id: new ObjectId(_id),
    ...getCustomerName(customersUpdatedAtSorted),
    ...getCustomerPhone(customerGrouped),
    ...extractProperties(customerCreatedAtSorted, [
      'title',
      'gender',
      'birthDate',
      'firstOrderDate',
      'assignedTo',
      'dibsyId',
    ]),

    createdAt,
    contact_channel,
    year,
    week,
    month,
    day,
    updatedAt: customersUpdatedAtSorted[0].updatedAt,

    company: new ObjectId(company),
    companies: customerCreatedAtSorted.reduce(concatCompanies, []),

    orders: orderDocumentsToEmbed(ordersDocuments),
    number_of_orders: ordersDocuments.length,
    total_orders_amount: ordersTotalAmount,
    average_order_value:
      ordersTotalAmount && ordersDocuments.length
        ? ordersTotalAmount / ordersDocuments.length
        : 0,
    latestContactChannel: latestOrder ? latestOrder.source : 'unknown',
    last_order_date: latestOrder ? latestOrder.createdAt : undefined,
    last_order_id: latestOrder ? latestOrder._id : undefined,
    firstBrandOrderd: ordersDocuments.find((order) => order.brand !== undefined)
      ?.brand,
    firstBranchOrderd: branchDocumentToEmbed(
      ordersDocuments.find((order) => order.branch !== undefined)?.branch,
    ),

    savedLocations: customerCreatedAtSorted.reduce(concatSavedLocations, []),

    loyaltyStatus: getCustomerLoyaltyStatus(customersUpdatedAtSorted),
    registeredPasses: customerCreatedAtSorted.reduce(concatRegisterdPasses, []),
    loyaltyPoints: getCustomerLoyaltyPoints(customerCreatedAtSorted),
    firstTierEarnedAt: getCustomerFirstTierEarnedAt(customerCreatedAtSorted),
    carryOverOrderRate: getCustomersCarryOverOrderRate(customerCreatedAtSorted),
    carryOverUpdatedAt: getCustomerCarryOverOrderRateUpdatedAt(
      customerCreatedAtSorted,
    ),
    ...extractProperties(customerCreatedAtSorted, [
      'tierUpdatedAt',
      'loyaltyRegistrationAt',
      'loyaltyRegistrationBranchId',
      'verificationContext',
      'deviceData',
      'loyaltyTier',
    ]),

    ...extractOrdableProperties(customerCreatedAtSorted),

    latestPayment: payments[0] ? payments[0]._id : undefined,
    latestPaymentDate: payments[0] ? payments[0].createdAt : undefined,

    relatedCustomers: customerCreatedAtSorted
      .map((customer) => customer._id)
      .filter((customerId) => customerId !== _id),
  };
}
function orderDocumentsToEmbed(ordersDocuments) {
  return ordersDocuments.map((order) => ({
    _id: order._id,
    source: order.source,
    status: order.status,
    pickup_date: order.pickup_date,
    delivery_date: order.delivery_date,
    total_amount_after_discount: order.total_amount_after_discount,
    total_amount: order.total_amount,
    isCartValueThresholdMet: getIsCartValueThresholdMet(order),
  }));
}

function getIsCartValueThresholdMet(order) {
  const company = db.getCollection('companies').findOne({ _id: order.company });
  return (
    company.loyaltyProgramConfig?.orderValueThreshold <=
    order.total_amount_after_discount
  );
}
function getCustomersCarryOverOrderRate(customers) {
  return customers.reduce(
    (acc, curr) => (acc += curr.carryOverOrderRate ?? 0),
    0,
  );
}

function getCustomerCarryOverOrderRateUpdatedAt(customers) {
  return sortCustomersByDate(customers, 'carryOverUpdatedAt')[0][
    'carryOverUpdatedAt'
  ];
}

function getCustomerFirstTierEarnedAt(customers) {
  const filteredCustomers = customers.filter(
    (customer) => customer.firstTierEarnedAt !== undefined,
  );
  if (filteredCustomers.length === 0) return undefined;
  return sortCustomersByDate(filteredCustomers, 'firstTierEarnedAt')[
    filteredCustomers.length - 1
  ]['firstTierEarnedAt'];
}

function branchDocumentToEmbed(branch) {
  return branch
    ? {
        _id: branch._id,
        name: branch.name,
        phoneNumber: branch.phone,
      }
    : undefined;
}

function getCustomerLoyaltyPoints(customers) {
  return customers.reduce((acc, curr) => {
    acc += curr.loyaltyPoints ?? 0;
  }, 0);
}

function getCustomerLoyaltyStatus(customers) {
  const loyaltyStatusOrder = ['member', 'enrolled', 'unenrolled'];
  const foundStatus = loyaltyStatusOrder.find((status) =>
    customers.some((customer) => customer.loyaltyStatus === status),
  );
  return foundStatus || 'unenrolled';
}

function getCustomerOrders(customerIds) {
  const ordersDocuments = db
    .getCollection('orders')
    .find({ customer: { $in: customerIds } })
    .sort({ createdAt: -1 })
    .toArray();
  return [
    ordersDocuments[ordersDocuments.length - 1],
    ordersDocuments[0],
    ordersDocuments,
  ];
}

function sortCustomersByDate(customers, dateField) {
  return customers.sort(
    (a, b) => new Date(b[dateField]) - new Date(a[dateField]),
  );
}

function concatCompanies(acc, curr) {
  return acc.concat(curr.companies || []);
}

function concatSavedLocations(acc, curr) {
  return acc.concat(curr.savedLocations || []);
}

function concatRegisterdPasses(acc, curr) {
  return acc.concat(curr.registeredPasses || []);
}

function getOrderTotalAmount(orders) {
  return orders.reduce((acc, curr) => {
    acc += curr.total_amount_after_discount
      ? curr.total_amount_after_discount
      : curr.total_amount;
    return acc;
  }, 0);
}

function getCustomerPhone(customersGrouped) {
  return {
    phone: customersGrouped._id.phone[0],
    country_code: customersGrouped._id.phone[0].length > 8 ? '+20' : '+974',
  };
}

function getCustomerPayments(customers) {
  return db
    .getCollection('payments')
    .find({ customer: { $in: customers.map((customer) => customer._id) } })
    .sort({ createdAt: -1 })
    .toArray();
}

function buildCustomerObject(customer) {
  const firstName = customer.first_name ? customer.first_name.trim() : '';
  const lastName = customer.last_name ? customer.last_name.trim() : '';

  return {
    first_name: firstName,
    last_name: lastName,
    full_name: `${firstName}${lastName ? ' ' + lastName : ''}`,
    language: customer.language,
    email: customer.email,
  };
}

function getCustomerName(customers) {
  const namePattern = /(no name|anonymous customer)/i;

  let filteredCustomers = customers.filter((customer) => {
    const fullName = `${customer.first_name.trim()}${
      customer.last_name ? ' ' + customer.last_name.trim() : ''
    }`;
    return !namePattern.test(fullName);
  });

  filteredCustomers = sortCustomersByDate(filteredCustomers, 'updatedAt');

  const customerFromLRP = filteredCustomers.find(
    (customer) => customer.contact_channel === 'loyalty_registration_page',
  );

  const selectedCustomer =
    customerFromLRP || filteredCustomers[0] || customers[0];
  return {
    ...buildCustomerObject(selectedCustomer),
    email: selectedCustomer.email
      ? selectedCustomer.email
      : filteredCustomers.find((customer) => customer.email)?.email,
  };
}
function extractProperties(customers, properties) {
  return properties.reduce((acc, prop) => {
    const foundCustomer = customers.find((customer) => customer[prop]);
    if (foundCustomer) {
      acc[prop] = foundCustomer[prop];
    }
    return acc;
  }, {});
}

function extractOrdableProperties(customers) {
  return {
    ...extractProperties(customers, [
      'ordableLink',
      'ordableId',
      'ordableTierId',
      'ordableCouponIds',
      'ordablePromotions',
      'ordableBrandIds',
    ]),
  };
}

main();
