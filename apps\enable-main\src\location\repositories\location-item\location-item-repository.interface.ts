import {
  IGenericRepository,
  LocationItem,
  LocationItemDocument,
} from '@app/shared-stuff';
import { FilterQuery, UpdateQuery } from 'mongoose';

export interface LocationItemRepositoryInterface
  extends IGenericRepository<LocationItemDocument, LocationItem> {
  updateMany(
    filterQuery: FilterQuery<LocationItemDocument>,
    update: UpdateQuery<LocationItemDocument>,
  ): Promise<any>;
}

export const LocationItemRepositoryInterface = Symbol(
  'LocationItemRepositoryInterface',
);
