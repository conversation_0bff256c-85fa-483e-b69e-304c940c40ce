import { Injectable } from '@nestjs/common';
import {
  Campaign,
  CampaignDocument,
  GenericRepository,
} from '@app/shared-stuff';
import { Model, Types } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { CampaignRepositoryInterface } from './campaign.repository.interface';

@Injectable()
export class CampaignRepository
  extends GenericRepository<CampaignDocument, Campaign>
  implements CampaignRepositoryInterface
{
  constructor(
    @InjectModel(Campaign.name)
    private campaignModel: Model<CampaignDocument, Campaign>,
  ) {
    super(campaignModel);
  }

  public async findByCompany(
    companyId: Types.ObjectId,
  ): Promise<CampaignDocument[]> {
    return await this.campaignModel.find({ companyId, deletedAt: null });
  }

  public async findByBrand(
    brandId: Types.ObjectId,
  ): Promise<CampaignDocument[]> {
    return await this.campaignModel.find({ brandId, deletedAt: null });
  }
}
