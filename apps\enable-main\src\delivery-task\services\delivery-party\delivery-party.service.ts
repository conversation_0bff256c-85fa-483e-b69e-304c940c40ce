import { LoggerService } from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { DeliveryPartyDocument } from '../../../delivery-task/models/delivery-party.model';
import { DeliveryPartyToCreate } from './../../dto/delivery-party.dto';

@Injectable()
export class DeliveryPartyService {
  private readonly loggerService = new LoggerService(DeliveryPartyService.name);

  constructor(
    @InjectModel('DeliveryParty')
    private deliveryPartyModel: Model<DeliveryPartyDocument>,
  ) {}

  async getDetails(id: string) {
    const deliveryParty = await this.deliveryPartyModel.findOne({
      _id: new Types.ObjectId(id),
    });
    return deliveryParty;
  }

  async create(deliveryPartyToCreate: DeliveryPartyToCreate) {
    const deliveryParty = new this.deliveryPartyModel(deliveryPartyToCreate);
    await deliveryParty.save();
    return deliveryParty;
  }
}
