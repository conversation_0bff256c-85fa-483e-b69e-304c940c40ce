// EBL-4968 Wallet Pass | Refresh Issue
// Migrate pass configs from brand loyalty program config to pass config entity

const passTypeIdentifiers = db.brands.distinct(
  'loyaltyProgramConfig.passTypeIdentifier',
);

const defaultPassKeyFile = './files/signerKey.pem';
const defaultPassCertFile = './files/signerCert.pem';

db.pass_config.insertOne({
  passTypeIdentifier: 'pass.tech.enable',
  passKeyFile: defaultPassKeyFile,
  passCertFile: defaultPassCertFile,
  createdAt: new Date(),
});

db.pass_config.insertMany(
  passTypeIdentifiers.map((passTypeIdentifier) => {
    const brand = db.brands.findOne({
      'loyaltyProgramConfig.passTypeIdentifier': passTypeIdentifier,
    });
    return {
      passTypeIdentifier,
      passKeyFile: brand.loyaltyProgramConfig.passKeyFile ?? defaultPassKeyFile,
      passCertFile:
        brand.loyaltyProgramConfig.passCertFile ?? defaultPassCertFile,
      createdAt: new Date(),
    };
  }),
);

db.brands.updateMany({}, [
  {
    $unset: [
      'loyaltyProgramConfig.passKeyFile',
      'loyaltyProgramConfig.passCertFile',
    ],
  },
]);
