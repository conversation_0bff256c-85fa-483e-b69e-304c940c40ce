import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsString,
} from 'class-validator';
import { OrdableProductsReferenceBy } from '../../enums/ordable-products-reference-by.enum';

export class UpdateOrdableProductsDto {
  @ApiProperty({
    type: String,
    enum: OrdableProductsReferenceBy,
    default: OrdableProductsReferenceBy.product_id,
    required: false,
  })
  reference_by?: OrdableProductsReferenceBy;

  @ApiProperty({
    type: Number,
    required: true,
    description: 'The "id" or "sku" of the product',
  })
  @IsNotEmpty()
  id: number;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsString()
  sku?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsNotEmpty()
  @IsString()
  name?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsNotEmpty()
  @IsString()
  ar_name?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsString()
  description?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsString()
  ar_description?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsString()
  short_description?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsString()
  ar_short_description?: string;

  @ApiProperty({
    type: Number,
    required: false,
  })
  @IsInt()
  sort_order?: number;

  @ApiProperty({
    type: Number,
    required: false,
  })
  @IsNumber()
  price?: number;

  @ApiProperty({
    type: String,
    required: false,
    description: 'A full URL. null will delete the photo if exists',
  })
  photo?: string;

  @ApiProperty({
    type: Number,
    required: false,
    description:
      'Use to change the category of a product. Obtain from the categories API',
  })
  @IsInt()
  category_id?: number;

  @ApiProperty({
    type: String,
    required: false,
    description:
      'Can be either the branch\'s name or id. Use to assign a product to a branch, or if used with "is_active", it will deactivate the product from the branch and not from the master list',
  })
  @IsString()
  branch_id?: string;

  @ApiProperty({
    type: Boolean,
    required: false,
    description:
      'If used without a "branch_id", it will deactivate the product from the master list in the "Products" section in the dashboard. Otherwise, it will deactivate it from the branch',
  })
  @IsBoolean()
  is_active?: boolean;
}
