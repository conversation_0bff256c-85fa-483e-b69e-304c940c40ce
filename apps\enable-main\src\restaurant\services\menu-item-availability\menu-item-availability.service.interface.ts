import {
  CreateOrUpdateMenuItemAvailabilityDto,
  CreateMenuItemAvailabilityDto,
  MenuItemAvailabilityDocument,
  UpdateMenuItemAvailabilityDto,
  WeekDays,
  MenuItemAvailabilityType,
  IndexMenuItemAvailabilityDto,
  MenuItemAvailabilityDto,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface MenuItemAvailabilityServiceInterface {
  createOrUpdate(
    createOrUpdateMenuItemAvailabilityDto: CreateOrUpdateMenuItemAvailabilityDto,
  );

  create(
    createMenuItemAvailabilityDto: CreateMenuItemAvailabilityDto,
  ): Promise<MenuItemAvailabilityDocument>;

  update(
    updateMenuItemAvailabilityDto: UpdateMenuItemAvailabilityDto,
  ): Promise<MenuItemAvailabilityDocument>;

  findOne(
    dayOfWeek: WeekDays,
    startTime: string,
    endTime: string,
    isWeekly: boolean,
    type: MenuItemAvailabilityType,
  ): Promise<MenuItemAvailabilityDocument>;

  index(indexMenuItemAvailability: IndexMenuItemAvailabilityDto): Promise<any>;

  remove(id: Types.ObjectId);

  handleDeletedMenuItems(
    availability: MenuItemAvailabilityDto,
    menuItemId: Types.ObjectId,
  );
}
