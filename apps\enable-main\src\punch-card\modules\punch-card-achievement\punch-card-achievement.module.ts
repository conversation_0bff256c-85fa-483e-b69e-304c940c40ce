import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';

import { RestaurantModule } from '../../../restaurant/restaurant.module';
import { PunchCardReadModule } from '../punch-card-read/punch-card-read.module';
import { PunchCardRepositoryModule } from '../punch-card-repository/punch-card-repository.module';
import { PunchCardAchievementService } from './punch-card-achievement.service';

@Module({
  imports: [
    PunchCardReadModule,
    PunchCardRepositoryModule,
    EventEmitterModule,
    RestaurantModule,
  ],
  providers: [PunchCardAchievementService],
  exports: [PunchCardAchievementService],
})
export class PunchCardAchievementModule {}
