import { FalconFlexAutoAssignment } from './falcon-flex-auto-assignment.type';
import { FalconFlexLocation } from './falcon-flex-location.type';
import { FalconFlexMetaDataField } from './falcon-flex-meta-data-field.type';
import { FalconFlexSkillCriteria } from './falcon-flex-skill-criteria.type';
import { FalconFlexTaskItem } from './falcon-flex-task-item.type';

export class FalconFlexAgentTask {
  Id: string;
  ShortId: string;
  ClientGeneratedId: string;
  DeliveryFee: number;
  CreatedAtUtc: string;
  UpdatedAtUtc: string;
  CreatedBy: string;
  UpdatedBy: string;
  PickupByUtc: string;
  DeliverByUtc: string;
  EstimatedPickupByUtc: string;
  EstimatedDeliveryByUtc: string;
  EstimatedDistanceRemainingToPickupKm: number;
  EstimatedDistanceRemainingToDeliverKm: number;
  EtaToCompletionMinFromUtcNow: number;
  OwningCompanyName: string;
  OwningCompanyId: string;
  ExecutingCompanyId: string;
  PaymentStatus: string;
  TaskStatus: string;
  Pickup: FalconFlexLocation;
  Delivery: FalconFlexLocation;
  CollectionAmount: number;
  TaskItems: FalconFlexTaskItem[];
  AgentTripDto: any; // Replace 'any' with the appropriate type if AgentTripDto has a specific structure
  AutoAssignmentDto: FalconFlexAutoAssignment;
  SkillCriteriaDto: FalconFlexSkillCriteria;
  MetaDataFields: FalconFlexMetaDataField[];
  TransportTypeId: number;
}
