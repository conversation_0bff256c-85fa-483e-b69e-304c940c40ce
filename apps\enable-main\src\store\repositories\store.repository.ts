import { GenericRepository } from '@app/shared-stuff';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Store, StoreDocument } from '../models/store.model';
import { StoreRepositoryInterface } from './store.repository.interface';

export class StoreRepository
  extends GenericRepository<StoreDocument, Store>
  implements StoreRepositoryInterface
{
  constructor(
    @InjectModel(Store.name)
    private readonly storeModel: Model<StoreDocument, Store>,
  ) {
    super(storeModel);
  }

  async findByBrand(brandId: Types.ObjectId): Promise<StoreDocument[]> {
    return await this.storeModel.find({ 'brands._id': brandId });
  }

  async findByCompany(companyId: Types.ObjectId): Promise<StoreDocument[]> {
    return await this.storeModel
      .find({
        companyId: companyId,
        apiBaseUrl: { $exists: true, $ne: '' },
        apiKey: { $exists: true, $ne: '' },
        'brands.0': { $exists: true },
      })
      .sort({ createdAt: 1 });
  }
}
