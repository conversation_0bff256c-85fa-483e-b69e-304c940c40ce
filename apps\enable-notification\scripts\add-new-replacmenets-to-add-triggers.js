// EBL-XXXX Add Generic Month-Related Replacements To All Triggers
// This script adds month-related replacements to all existing triggers

// Update all triggers to include generic month replacements
db.triggers.updateMany(
  {}, // Target all triggers
  {
    $addToSet: {
      replacement: {
        $each: [
          'currentMonth',
          'currentMonthName',
          'currentMonthShortName',
          'previousMonth',
          'previousMonthName',
          'previousMonthShortName',
        ],
      },
    },
  },
);

// Update all templates to include generic month replacements in their triggers
db.templates.updateMany(
  {}, // Target all templates
  {
    $addToSet: {
      'trigger.replacement': {
        $each: [
          'currentMonth',
          'currentMonthName',
          'currentMonthShortName',
          'previousMonth',
          'previousMonthName',
          'previousMonthShortName',
        ],
      },
    },
  },
);

