// regarding this task https://e-butler.atlassian.net/browse/EBL-5885
const startOf2025 = new Date('2025-01-01T00:00:00.000Z');

const orderLogs = db.orderlogs
  .find({
    logAction: 'ON_ORDER_NOTIFICATION_ACKNOWLEDGE',
    createdAt: { $gte: startOf2025 },
  })
  .toArray();

if (orderLogs.length === 0) {
  print('No relevant order logs found.');
} else {
  const orderCodeToAcknowledgedBy = {};

  orderLogs.forEach((log) => {
    if (log.createdBy && log.orderCode) {
      orderCodeToAcknowledgedBy[log.orderCode] = log.createdBy;
    }
  });

  const orderCodes = Object.keys(orderCodeToAcknowledgedBy);

  if (orderCodes.length > 0) {
    const orders = db.orders
      .find({
        code: { $in: orderCodes },
        is_test: false,
        createdAt: { $gte: startOf2025 },
      })
      .toArray();

    if (orders.length === 0) {
      print('No matching orders found.');
    } else {
      let updatedCount = 0;
      orders.forEach((order) => {
        const acknowledgedBy = orderCodeToAcknowledgedBy[order.code];
        if (acknowledgedBy) {
          const result = db.orders.updateOne(
            { _id: order._id },
            { $set: { acknowledgedBy: acknowledgedBy } },
          );
          updatedCount += result.modifiedCount;
        }
      });

      print(`${updatedCount} orders updated successfully.`);
    }
  } else {
    print('No valid order codes found for update.');
  }
}
