import {
  responseCode,
  TableSearchMapping,
  TableSortMapping,
  TableToCreate,
  TableToIndex,
  TableToRemove,
  TableToUpdate,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import { BranchService } from './../../../branch/services/branch/branch.service';
import { CompanyService } from './../../../company/services/company/company.service';
import { TableDocument } from './../../models/restaurant-table.model';
import { RestaurantFloorService } from './../restaurant-floor/restaurant-floor.service';

@Injectable()
export class RestaurantTableService {
  constructor(
    @InjectModel('Table') private tableModel: Model<TableDocument>,
    private companyService: CompanyService,
    private branchService: BranchService,
    private restaurantFloorService: RestaurantFloorService,
  ) {}

  async index(tableToIndex: TableToIndex) {
    const aggregation = [];

    if (tableToIndex.branch) {
      aggregation.push({
        $match: {
          branch: { $eq: new Types.ObjectId(tableToIndex.branch) },
        },
      });
    }

    if (tableToIndex.floor) {
      aggregation.push({
        $match: {
          floor: { $eq: new Types.ObjectId(tableToIndex.floor) },
        },
      });
    }

    if (tableToIndex.month) {
      aggregation.push({
        $match: { month: tableToIndex.month },
      });
    }

    if (tableToIndex.company) {
      aggregation.push({
        $match: { company: new Types.ObjectId(tableToIndex.company) },
      });
    }

    if (tableToIndex.sort_type) {
      aggregation.push({
        $sort: TableSortMapping[tableToIndex.sort_type],
      });
    }

    if (tableToIndex.search_key) {
      if (tableToIndex.search_type == 'all') {
        aggregation.push(
          {
            $addFields: {
              matched: {
                $or: [
                  {
                    $regexMatch: {
                      input: '$name',
                      options: 'i',
                      regex: new RegExp(`.*${tableToIndex.search_key}.*`),
                    },
                  },
                  {
                    $regexMatch: {
                      input: '$branchName',
                      options: 'i',
                      regex: new RegExp(`.*${tableToIndex.search_key}.*`),
                    },
                  },
                ],
              },
            },
          },
          { $match: { matched: true } },
        );
      } else {
        aggregation.push(
          {
            $addFields: {
              matched: {
                $regexMatch: TableSearchMapping(
                  tableToIndex.search_type || 'all',
                  tableToIndex.search_key,
                ),
              },
            },
          },
          { $match: { matched: true } },
        );
      }
    }

    aggregation.push({ $match: { deletedAt: { $eq: null } } });

    if (
      (tableToIndex.offset || tableToIndex.offset == 0) &&
      tableToIndex.limit
    ) {
      aggregation.push({
        $skip: tableToIndex.offset * tableToIndex.limit,
      });
      aggregation.push({
        $limit: tableToIndex.limit,
      });
    }

    const tables = await this.tableModel.aggregate(aggregation);
    return tables;
  }

  async getTotalNumberOfTables(tableToIndex: TableToIndex) {
    delete tableToIndex.offset;
    delete tableToIndex.limit;
    return (await this.index(tableToIndex)).length;
  }

  async getDetails(id: string) {
    const table = await this.tableModel.findOne({ _id: id });
    return table;
  }

  async create(tableToCreate: TableToCreate) {
    const branch = await this.branchService.get_details(tableToCreate.branch);
    const company = await this.companyService.get_details(
      branch.company['_id'],
    );

    tableToCreate['companyName'] = company.name;
    tableToCreate['branchName'] = branch.name;

    tableToCreate['branch'] = new Types.ObjectId(branch._id) as any;
    tableToCreate['company'] = new Types.ObjectId(company._id) as any;

    const floor = await this.restaurantFloorService.getDetailsWithoutPopulation(
      tableToCreate.floor,
    );

    if (!floor) {
      throw {
        code: responseCode.MISSING_DATA,
        statusCode: 500,
        message: 'Please Send a correct Floor Id',
      };
    }
    // setting the floor to table and add the table to floor tables

    const table = new this.tableModel(tableToCreate);

    table.floor = floor._id;

    floor.tables =
      floor.tables && floor.tables.length
        ? [...floor.tables, new Types.ObjectId(table._id)]
        : [new Types.ObjectId(table._id)];

    await floor.save();

    table.createdBy = tableToCreate.currentUser;
    await table.save();
    return table;
  }

  async update(tableToUpdate: TableToUpdate) {
    const table = await this.tableModel.findOne({ _id: tableToUpdate._id });

    const branch = await this.branchService.get_details(tableToUpdate.branch);
    const company = await this.companyService.get_details(branch.company);

    tableToUpdate['companyName'] = company.name;
    tableToUpdate['branchName'] = branch.name;

    tableToUpdate['branch'] = new Types.ObjectId(branch._id) as any;
    tableToUpdate['company'] = new Types.ObjectId(company._id) as any;

    await this.tableModel.updateOne({ _id: table._id }, tableToUpdate);

    table.updatedBy = tableToUpdate.currentUser;

    return table;
  }

  async remove(tableToRemove: TableToRemove) {
    const table = await this.tableModel.findOne({ _id: tableToRemove._id });
    table.deletedBy = tableToRemove.deletedBy;
    table.deletedAt = moment.utc().toDate();
    await table.save();
    return table;
  }
}
