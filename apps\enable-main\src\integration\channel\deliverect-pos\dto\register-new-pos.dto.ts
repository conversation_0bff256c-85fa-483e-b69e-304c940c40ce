import { ApiProperty } from '@nestjs/swagger';
import { Types } from 'mongoose';

// Based on https://developers.deliverect.com/reference/post-register-pos
export class RegisterNewPosDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Deliverect account ID',
  })
  accountId: string;

  @ApiProperty({
    type: Types.ObjectId,
    required: true,
    description: 'Deliverect location ID',
  })
  locationId: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: true,
    description: 'External location ID provided by the POS',
  })
  externalLocationId: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  locationName: string;
}
