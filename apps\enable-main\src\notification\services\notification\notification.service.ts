import {
  BranchWithId,
  LoggerService,
  OrderDocument,
  PusherService,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { Types } from 'mongoose';
import { UserService } from '../../../user/services/user/user.service';

@Injectable()
export class NotificationService {
  private readonly logger = new LoggerService(NotificationService.name);
  constructor(
    private userService: UserService,
    private pusherService: PusherService,
  ) {}

  public async fireOrderPusherTrigger(
    order: OrderDocument,
    branch: BranchWithId,
    roleName: string,
    triggerName: string,
    forAcknowledge: boolean,
  ) {
    this.logger.log(
      `Firing pusher trigger ${triggerName} for order ${order._id} for role ${roleName}`,
    );
    const users = await this.userService.index({
      company:
        order['company'] && order['company']['_id']
          ? order['company']['_id']
          : order.company,
      branch: branch && branch._id ? branch._id : branch,
      role_name: roleName,
    } as any);

    // Removing the order from previous users
    await this.userService.removeAcknowledgeOrdersFromUsers(
      order,
      'acknowledged',
    );
    this.logger.log(
      `Number Of Users Found for acknowledged Order ${order?._id} is: ${users?.length}`,
    );
    for (let i = 0; i < users.length; i++) {
      const user = users[i];
      if (forAcknowledge == true) {
        if (user.ordersToBeAcknowledged) {
          const orderIndex = user.ordersToBeAcknowledged.findIndex((x) =>
            x.orderId.equals(new Types.ObjectId(order._id)),
          );
          if (orderIndex == -1) {
            user.ordersToBeAcknowledged.push({
              orderId: new Types.ObjectId(order._id),
              orderCode: order.code,
              createdAt: new Date(),
            });
            await user.updateOne(user);
          }
        } else {
          user.ordersToBeAcknowledged = [
            {
              orderId: new Types.ObjectId(order._id),
              orderCode: order.code,
              createdAt: new Date(),
            },
          ];
          this.logger.log(
            `User ${user._id} ordersToBeAcknowledged array length is: ${user.ordersToBeAcknowledged.length}`,
            { ordersToBeAcknowledged: user.ordersToBeAcknowledged },
          );
        }
        await user.save();

        const orderIds: string[] = user.ordersToBeAcknowledged.map((order) =>
          order.orderId.toHexString
            ? order.orderId.toHexString()
            : (order.orderId as any),
        );
        this.pusherService.fireEvent(user._id.toHexString(), triggerName, {
          orders: orderIds,
        });
      } else {
        this.pusherService.fireEvent(user._id.toHexString(), triggerName, {
          orderId: order._id,
        });
      }
    }
  }
}
