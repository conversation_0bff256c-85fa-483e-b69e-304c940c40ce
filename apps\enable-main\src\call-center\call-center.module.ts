import { SharedStuffModule } from '@app/shared-stuff';
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { BrandModule } from '../brand/brand.module';
import { CustomerReadModule } from '../customer/modules/customer-read/customer-read.module';
import { CustomerReplacementsModule } from '../customer/modules/customer-replacements/customer-replacements.module';
import { CustomerWriteModule } from '../customer/modules/customer-write/customer-write.module';
import { NotificationModule } from '../notification/notification.module';
import { SharedModule } from '../shared/shared.module';
import { BranchModule } from './../branch/branch.module';
import { CompanyModule } from './../company/company.module';
import { CallCenterController } from './controllers/call-center/call-center.controller';
import { CallCenterSmsSentSchema } from './models/call-center-sms.model';
import { CallCenterService } from './services/call-center/call-center.service';

@Module({
  imports: [
    HttpModule,
    ConfigModule,
    SharedModule,
    SharedStuffModule,
    BranchModule,
    CompanyModule,
    NotificationModule,
    BrandModule,
    CustomerReadModule,
    CustomerWriteModule,
    CustomerReplacementsModule,
    MongooseModule.forFeature([
      { name: 'CallCenterSmsSent', schema: CallCenterSmsSentSchema },
    ]),
  ],
  controllers: [CallCenterController],
  providers: [CallCenterService],
})
export class CallCenterModule {}
