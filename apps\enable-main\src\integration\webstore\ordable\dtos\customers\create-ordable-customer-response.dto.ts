import { OrdableHttpResponseDto } from '../ordable-http-response.dto';
import { OrdableAddressDto } from './ordable-address.dto';

export type CreateOrdableCustomerResponseDto =
  OrdableHttpResponseDto<CreateOrdableCustomerSuccessResponsePayloadDto>;

// Based on https://ordable.stoplight.io/docs/menu-management/83f20b600630a-create-a-user
export class CreateOrdableCustomerSuccessResponsePayloadDto {
  user_id: number;
  token: string;
  email: string;
  first_name: string;
  last_name: string;
  phone: string;
  address?: OrdableAddressDto & { id: number };
  login_url: string;
}
