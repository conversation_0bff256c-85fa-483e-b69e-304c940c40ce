import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNumber, IsString } from 'class-validator';

export class UpdateOrdableProductOptionsDto {
  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsInt()
  id: number;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsString()
  value: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsString()
  ar_value: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNumber()
  price: number;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsInt()
  sort_order: number;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsString()
  deliverect_plu: string;
}
