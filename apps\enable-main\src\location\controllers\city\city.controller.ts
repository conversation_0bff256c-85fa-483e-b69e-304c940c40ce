import {
  GenericExceptionFilter,
  LocationItemType,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import {
  Api<PERSON>asicAuth,
  ApiBearerAuth,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { Types } from 'mongoose';
import {
  CityToCreate,
  CityToIndex,
  CityToUpdate,
} from '../../../location/dto/city.dto';
import { LocationItemServiceInterface } from '../../services/location-item/location-item-service.interface';

@Controller('city')
@ApiTags('city')
@SetMetadata('module', 'city')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
export class CityController {
  constructor(
    @Inject(LocationItemServiceInterface)
    private locationItemService: LocationItemServiceInterface,
  ) {}

  @Get()
  @SetMetadata('action', 'get_all')
  @ApiOperation({
    deprecated: true,
    description:
      '**Deprecated**, use `GET /location-item` with type `city` instead.',
  })
  async index(@Query() cityToIndex: CityToIndex) {
    const result = await this.locationItemService.findAll({
      type: LocationItemType.CITY,
      ...cityToIndex,
      parentId: cityToIndex.country,
    });
    return result[0].paginatedResult;
  }

  @Get('public')
  @SetMetadata('public', 'true')
  @ApiOperation({
    deprecated: true,
    description:
      '**Deprecated**, use `GET /location-item/public` with type `city` instead.',
  })
  async publicIndex(@Query() cityToIndex: CityToIndex) {
    const result = await this.locationItemService.findAll({
      type: LocationItemType.CITY,
      ...cityToIndex,
      parentId: cityToIndex.country
        ? new Types.ObjectId(cityToIndex.country)
        : undefined,
    });
    return result[0].paginatedResult;
  }

  @ApiOperation({
    deprecated: true,
    description:
      '**Deprecated**, use `POST /location-item` with type `city` instead.',
  })
  @Post()
  @SetMetadata('action', 'create')
  async create(@Body() cityToCreate: CityToCreate) {
    return await this.locationItemService.create({
      type: LocationItemType.CITY,
      ...cityToCreate,
      parentId: new Types.ObjectId(cityToCreate.country),
    });
  }

  @ApiOperation({
    deprecated: true,
    description:
      '**Deprecated**, use `PATCH /location-item` with type `city` instead.',
  })
  @Put(':id')
  @SetMetadata('action', 'update')
  async update(@Body() cityToUpdate: CityToUpdate) {
    return await this.locationItemService.update({
      type: LocationItemType.CITY,
      ...cityToUpdate,
      _id: new Types.ObjectId(cityToUpdate._id),
    });
  }

  @ApiOperation({
    deprecated: true,
    description: '**Deprecated**, use `DELETE /location-item`',
  })
  @Delete(':id')
  @SetMetadata('action', 'remove')
  async remove(@Param('id') id: string) {
    return await this.locationItemService.delete(new Types.ObjectId(id));
  }
}
