import { CurrentUser } from '@app/shared-stuff/types/general/current-user';
import { ApiExtraModels, ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';
import { IntegrationType } from '../enums/integration-type.enum';
import { IntegrationOrder } from './integration-order.dto';
import { IntegrationPayment } from './integration-payment.dto';
import { AggregatorOrderToCreate } from '@app/shared-stuff';

@ApiExtraModels(IntegrationOrder, AggregatorOrderToCreate)
export class OrderPaymentToCreate {
  @ApiProperty({
    type: String,
    enum: IntegrationType,
    required: true,
  })
  @IsNotEmpty()
  integrationType: IntegrationType;

  @ApiProperty({
    type: () => IntegrationPayment,
    required: false,
  })
  @Type(() => IntegrationPayment)
  paymentData?: IntegrationPayment;

  @ApiProperty({
    oneOf: [
      { $ref: getSchemaPath(IntegrationOrder) },
      { $ref: getSchemaPath(AggregatorOrderToCreate) },
    ],
    required: false,
  })
  @Type(({ object }) =>
    object?.integrationType === IntegrationType.ORDER_CAPTURE
      ? AggregatorOrderToCreate
      : IntegrationOrder,
  )
  orderData?: IntegrationOrder | AggregatorOrderToCreate;

  company: string;
  createdBy: CurrentUser;
  brand: any;
}
