import {
  CreateCustomerDto,
  CurrentUser,
  CustomerDocument,
  CustomerEarnedBenefit,
  DeviceData,
  EarnedReward,
  MessageCustomerDto,
  UpdateCustomerDto,
  UsedReward,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface CustomerWriteServiceInterface {
  findOrCreate(
    createCustomerDto: CreateCustomerDto,
    savedLocationId?: Types.ObjectId,
  ): Promise<CustomerDocument>;
  removeLatestPayment(id: Types.ObjectId): Promise<void>;
  remove(
    id: string,
    currentUser: CurrentUser,
    companyId: string,
  ): Promise<string>;
  update(
    updateCustomerDto: UpdateCustomerDto,
    currentUser: CurrentUser,
    companyId: string,
  ): Promise<Types.ObjectId>;
  create(
    createCustomerDto: CreateCustomerDto,
    createUser: CurrentUser,
  ): Promise<CustomerDocument>;
  updateOrCreate(
    createCustomerDto: CreateCustomerDto,
    createUser: CurrentUser,
  ): Promise<CustomerDocument>;
  setDeviceData(
    customerId: Types.ObjectId,
    deviceData: DeviceData,
  ): Promise<void>;
  updateRewards(
    customer: CustomerDocument,
    rewards: EarnedReward[] | UsedReward[],
  ): Promise<void>;
  updateBenefits(
    customer: CustomerDocument,
    benefits: CustomerEarnedBenefit[],
  ): Promise<void>;
  messageCustomer(messageCustomerDto: MessageCustomerDto): Promise<void>;
}

export const CustomerWriteServiceInterface = Symbol(
  'CustomerWriteServiceInterface',
);
