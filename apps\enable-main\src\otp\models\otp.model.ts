import { Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import {
  CountryDialCode,
  EnumField,
  Field,
  IsPhoneNumberForRegion,
} from '@app/shared-stuff';

export type OTPDocument = OTP & Document;
@Schema({ timestamps: true })
export class OTP {
  @EnumField({
    type: String,
    required: true,
    enum: CountryDialCode,

    default: CountryDialCode.QATAR,
  })
  countryCode: CountryDialCode;

  @Field({
    type: String,
    required: true,
    additionalDecorators: [IsPhoneNumberForRegion('countryCode')],
  })
  phone: string;

  @Field({
    type: Number,
    required: true,
  })
  otp: number;

  @Field({
    type: Types.ObjectId,
    required: false,
  })
  brandId?: Types.ObjectId;

  @Field({
    type: Date,
    required: true,
    modelOptions: {
      expires: 0,
      index: true,
    },
  })
  expireAt: Date;
}

export const OTPSchema = SchemaFactory.createForClass(OTP);
