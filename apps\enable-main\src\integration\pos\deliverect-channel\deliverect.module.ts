import { SharedStuffModule } from '@app/shared-stuff';
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';

import { BranchModule } from '../../../branch/branch.module';
import { CompanyModule } from '../../../company/company.module';
import { CustomerReadModule } from '../../../customer/modules/customer-read/customer-read.module';
import { LocationModule } from '../../../location/location.module';
import { OrderModule } from '../../../order/order.module';
import { RestaurantModule } from '../../../restaurant/restaurant.module';
import { SharedModule } from '../../../shared/shared.module';
import { IntegrationLogModule } from '../../integration-log/integration-log.module';
import { DeliverectController } from './controllers/deliverect.controller';
import { DeliverectChannelService } from './services/deliverect-channel.service';

@Module({
  imports: [
    SharedModule,
    BranchModule,
    CompanyModule,
    RestaurantModule,
    OrderModule,
    HttpModule,
    LocationModule,
    IntegrationLogModule,
    SharedStuffModule,
    CustomerReadModule,
  ],
  controllers: [DeliverectController],
  providers: [DeliverectChannelService],
})
export class DeliverectModule {}
