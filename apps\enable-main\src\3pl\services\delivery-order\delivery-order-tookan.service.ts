import {
  CompanyDocument,
  DeliveryOrderDocument,
  DistributionCenterDocument,
  DriverDocument,
  LoggerService,
  TempCustomerDocument,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as moment from 'moment-timezone';
import {
  AssignAgentToTookanTaskDto,
  CreateBothTookanTaskDto,
} from '../../../delivery/dto/tookan.dto';
import { TookanService } from '../../../delivery/services/tookan/tookan.service';

@Injectable()
export class DeliveryOrderTookanService {
  private readonly loggerService = new LoggerService(
    DeliveryOrderTookanService.name,
  );

  constructor(
    private tookanService: TookanService,
    private configService: ConfigService,
  ) {}

  async createTookanTask(
    deliveryOrder: DeliveryOrderDocument,
    company: CompanyDocument,
    tempCustomer: TempCustomerDocument,
    distributionCenter: DistributionCenterDocument,
  ) {
    const tookanTaskToCreate = this.constructCreateTookanTaskPayload(
      deliveryOrder,
      company,
      tempCustomer,
      distributionCenter,
    );
    this.loggerService.log(
      `delivery order ${deliveryOrder.logTracker}:  TookanTaskToCreate`,
      tookanTaskToCreate,
    );
    const createdTookanTask =
      await this.tookanService.createTookanTask(tookanTaskToCreate);
    deliveryOrder.deliveryTask.tookanTasksIds.push(createdTookanTask['job_id']);
    deliveryOrder.markModified('deliveryTask');
    await deliveryOrder.save();

    this.loggerService.log(
      `delivery order ${deliveryOrder.logTracker}:  createdTookanTask`,
      createdTookanTask,
    );
  }

  private constructCreateTookanTaskPayload(
    deliveryOrder: DeliveryOrderDocument,
    company: CompanyDocument,
    tempCustomer: TempCustomerDocument,
    distributionCenter: DistributionCenterDocument,
  ): CreateBothTookanTaskDto {
    const dateTime = moment
      .utc(deliveryOrder.deliveryDate)
      .clone()
      .tz(
        company.localization?.timezone
          ? company.localization?.timezone
          : 'Asia/Qatar',
      )
      .toString() as any;

    return {
      api_key: this.configService.get('TOOKAN_API_KEY'),
      auto_assignment: deliveryOrder.hasAutoDriverAssignment ? 1 : 0,
      order_id: deliveryOrder.code,
      barcode: deliveryOrder._id.toHexString(),
      tags: ``,
      job_description: `Delivery Order ${deliveryOrder.code}`,
      has_delivery: 1,
      has_pickup: 1,
      layout_type: 0,
      team_id: company.tookan_team_id,
      timezone: moment.tz
        .zone(
          company?.localization?.timezone
            ? company?.localization?.timezone
            : 'Asia/Qatar',
        )
        .utcOffset(moment.utc().valueOf()),
      fleet_id: '',

      // delivery data
      customer_email: tempCustomer
        ? tempCustomer.email
        : distributionCenter.name,
      customer_username: tempCustomer
        ? tempCustomer.firstName
        : distributionCenter.name,
      customer_address: deliveryOrder.deliveryLocation.address,
      customer_phone: tempCustomer
        ? (tempCustomer.countryCode ?? '') + tempCustomer.phone
        : distributionCenter.phone,
      job_delivery_datetime: dateTime,
      latitude: deliveryOrder.deliveryLocation.location?.latitude
        ? deliveryOrder.deliveryLocation.location?.latitude.toString()
        : '31.*********',
      longitude: deliveryOrder.deliveryLocation.location?.longitude
        ? deliveryOrder.deliveryLocation.location?.longitude.toString()
        : '30.*********',
      custom_field_template: 'Pickup_&_Delivery_Enable',
      meta_data: [
        {
          label: 'Cash_To_Collect',
          data: deliveryOrder.cashToCollect,
        },
      ],

      // pickup data
      job_pickup_address: deliveryOrder.pickupLocation?.address,
      job_pickup_datetime: dateTime,
      job_pickup_email: '',
      job_pickup_latitude: deliveryOrder.pickupLocation?.location?.latitude
        ? deliveryOrder.pickupLocation?.location?.latitude.toString()
        : '31.*********',
      job_pickup_longitude: deliveryOrder.pickupLocation?.location?.longitude
        ? deliveryOrder.pickupLocation?.location?.longitude?.toString()
        : '30.*********',
      job_pickup_name: '',
      job_pickup_phone: '',
      pickup_meta_data: [
        {
          label: 'Cash_To_Collect',
          data: deliveryOrder.cashToCollect,
        },
      ],
      pickup_custom_field_template: 'Pickup_&_Delivery_Enable',
    };
  }

  async assignAgentToTask(
    deliveryOrder: DeliveryOrderDocument,
    driver: DriverDocument,
    company: CompanyDocument,
  ): Promise<void> {
    for (const tookanTaskId of deliveryOrder.deliveryTask.tookanTasksIds) {
      const assignAgentToTookanTaskDto =
        this.constructAssignTookanAgentToTaskDto(
          tookanTaskId,
          parseInt(driver.tookan_driver_id),
          parseInt(company.tookan_team_id),
        );

      await this.tookanService.assignAgentToTask(assignAgentToTookanTaskDto);
      deliveryOrder.deliveryTask.assignedDriverName =
        driver.first_name + ' ' + driver.last_name;
      deliveryOrder.deliveryTask.tookanDriverId = parseInt(
        driver.tookan_driver_id,
      );
      deliveryOrder.deliveryTask.enableDriverId = driver._id;
      deliveryOrder.markModified('deliveryTask');
      await deliveryOrder.save();
    }
  }

  private constructAssignTookanAgentToTaskDto(
    tookanTaskId: number,
    tookanDriverId: number,
    tookanTeamId: number,
  ): AssignAgentToTookanTaskDto {
    return {
      fleet_id: tookanDriverId,
      job_id: tookanTaskId,
      team_id: tookanTeamId,
    };
  }
}
