import { LoyaltyProgramConfig } from '../../dtos/company/loyalty-program-config.dto';
import { LoyaltyTierProgramProgress } from '../../dtos/customer/loyalty-tier-program-progress.dto';
import { PunchCardProgress } from '../../dtos/customer/punch-card-progress.dto';
import { StripImageConfig } from '../../dtos/passes/strip-image-config.dto';
import { Currency } from '../../enums/general/currency.enum';
import { CompanyDocument } from '../../models/company.model';
import { CustomerDocument } from '../../models/customer.model';
import { PassConfig } from '../../models/pass-config.model';

export class PassImageContext {
  public punchCardProgress: PunchCardProgress[];
  public loyaltyTierId: string;
  public loyaltyTierProgramProgress: LoyaltyTierProgramProgress;
  public stripImageConfig: StripImageConfig;
  public loyaltyProgramConfig: LoyaltyProgramConfig;
  public currency: Currency;

  constructor(
    customer: CustomerDocument,
    loyaltyTierProgramProgress: LoyaltyTierProgramProgress,
    passConfig: PassConfig,
    company: CompanyDocument,
  ) {
    this.punchCardProgress = customer.punchCardProgress ?? [];
    this.loyaltyTierId = customer.loyaltyTier?._id.toString();
    this.loyaltyTierProgramProgress = loyaltyTierProgramProgress;
    this.stripImageConfig = passConfig?.stripImageConfig;
    this.loyaltyProgramConfig = company.loyaltyProgramConfig;
    this.currency = company.localization.currency ?? Currency.QAR;
  }

  public stringify() {
    return `${this.punchCardProgress.map((progress) => progress._id.toString() + progress.count)}${this.loyaltyTierId}${PassImageContext.stringifyObject(this.loyaltyTierProgramProgress)}${PassImageContext.stringifyObject(this.stripImageConfig)}${PassImageContext.stringifyObject(this.loyaltyProgramConfig)}${this.currency}`;
  }

  private static stringifyObject(object: Record<string, any>): string {
    return Object.entries(object)
      .map(([key, value]) =>
        value && typeof value === 'object'
          ? `${key}${this.stringifyObject(value)}`
          : `${key}${value}`,
      )
      .join('');
  }
}
