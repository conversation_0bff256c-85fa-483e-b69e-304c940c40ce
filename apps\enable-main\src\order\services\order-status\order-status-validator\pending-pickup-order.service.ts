import { OrderStatusServiceInterface } from '../order-status.interface';
import { BadRequestException, Inject } from '@nestjs/common';
import {
  CurrentUser,
  OrderDocument,
  OrderLogActionEnum,
  OrderPaymentMethod,
  OrderPaymentStatus,
  OrderStatusEnum,
  OrderTransitionTrigger,
  responseCode,
} from '@app/shared-stuff';
import { OrderNotificationService } from '../../order-notification/order-notification.service';
import { OrderLogServiceInterface } from '../../interfaces/order-log.service.interface';
import { AcknowledgementType } from '@app/shared-stuff/enums/company/acknowledgement-type.enum';

export class PendingPickupOrderService implements OrderStatusServiceInterface {
  transitionalStatuses = [
    OrderStatusEnum.PREPARING,
    OrderStatusEnum.PENDING,
    OrderStatusEnum.SCHEDULED,
  ];
  transitionalTrigger = [
    OrderTransitionTrigger.READY,
    OrderTransitionTrigger.PICKUP_TASK_STARTED,
    OrderTransitionTrigger.DELIVERECT_STATUS_UPDATE,
  ];

  constructor(
    @Inject('OrderLogServiceInterface')
    private readonly orderLogService: OrderLogServiceInterface,
    private orderNotificationService: OrderNotificationService,
  ) {}

  validateStatus(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
  ): boolean {
    return (
      this.ValidateTransition(order, orderTransitionTrigger) &&
      this.validatePreCondition(order)
    );
  }

  private ValidateTransition(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
  ): boolean {
    if (
      this.transitionalTrigger.includes(orderTransitionTrigger) &&
      this.transitionalStatuses.includes(order.status)
    )
      return true;

    if (
      order.company &&
      order.company['_id'] &&
      order.company['acknowledgementScreenType'] ===
        AcknowledgementType.MANUAL &&
      order.status == OrderStatusEnum.PREPARING
    ) {
      return true;
    }

    if (
      order.company &&
      order.company['_id'] &&
      order.company['acknowledgementScreenType'] !==
        AcknowledgementType.MANUAL &&
      order.status == OrderStatusEnum.PENDING
    ) {
      return true;
    }

    throw new BadRequestException(
      "Can't Change From " + order.status + ' To Pending Pick-up',
      responseCode.STATUS_NOT_VALID.toString(),
    );
  }

  private validatePreCondition(order: OrderDocument): boolean {
    if (
      order.payment_method == OrderPaymentMethod.online &&
      order.payment_status != OrderPaymentStatus.COMPLETED
    ) {
      throw new BadRequestException(
        "Can't change From " +
          order.status +
          ' to Pending Pick-up' +
          'Payment is NOT completed for online delivery order with ID ' +
          order._id,
        responseCode.STATUS_NOT_VALID.toString(),
      );
    }
    if (!order.is_ready) {
      throw new BadRequestException(
        "Can't change From " +
          order.status +
          ' to  Pending Pick-up' +
          'The order with ID ' +
          order._id +
          ' is not ready',
        responseCode.STATUS_NOT_VALID.toString(),
      );
    }

    return true;
  }

  async applyPostFunction(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
    oldStatus: OrderStatusEnum,
    user: CurrentUser,
  ) {
    await this.orderNotificationService.onOrderReadyForPickup(order);
    await this.orderLogService.saveOrderLog(
      order,
      { oldStatus: oldStatus },
      {
        newStatus: OrderStatusEnum.PENDING_PICKUP,
        trigger: orderTransitionTrigger,
      },
      OrderLogActionEnum.ON_ORDER_PENDING_PICKUP,
      user,
    );
  }
}
