import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { HydratedDocument } from 'mongoose';

export type EnableLogDocument = HydratedDocument<EnableLog>;
@Schema({ timestamps: true })
export class EnableLog {
  @Prop({
    type: String,
  })
  route: string;

  @Prop({
    type: String,
  })
  ip: string;

  @Prop({
    type: String,
  })
  url: string;
  @Prop({
    type: String,
  })
  userAgent: string;

  @Prop({
    type: {},
  })
  headers: {};

  @Prop({
    type: String,
  })
  method: string;

  @Prop({
    type: String,
    default: moment().startOf('month').format('MMMM'),
  })
  month: string;

  @Prop({
    type: Number,
    default: moment().week(),
  })
  week: number;

  @Prop({
    type: Number,
    default: moment().date(),
  })
  day: number;

  @Prop({
    type: Number,
    default: moment().year(),
  })
  year: number;
}

const EnableLogSchema = SchemaFactory.createForClass(EnableLog);

export { EnableLogSchema };
