// [EBL-6007] Add hasCreatedAt to invoices config
// This script initializes the hasCreatedAt field in the invoices config for all companies
// that do not have it. It sets the default value to false.

db.companies.updateMany(
  {
    'interfaceConfig.orderConfig.invoicesConfig.hasCreatedAt': { $exists: false },
  },
  {
    $set: {
      'interfaceConfig.orderConfig.invoicesConfig.hasCreatedAt': false,
    },
  },
);


// [EBL-6008] Add hasExternalOrderReference to invoices config
// This script initializes the hasExternalOrderReference field in the invoices config for all companies
// that do not have it. It sets the default value to false.

db.companies.updateMany(
  {
    'interfaceConfig.orderConfig.invoicesConfig.hasExternalOrderReference': {
      $exists: false,
    },
  },
  {
    $set: {
      'interfaceConfig.orderConfig.invoicesConfig.hasExternalOrderReference': false,
    },
  },
);
