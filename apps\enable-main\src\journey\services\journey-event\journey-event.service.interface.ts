import {
  CreateJourneyEventDto,
  JourneyDocument,
  UpdateJourneyEventDto,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface JourneyEventServiceInterface {
  create(
    createJourneyEventDto: CreateJourneyEventDto,
  ): Promise<JourneyDocument>;
  update(
    updateJourneyEventDto: UpdateJourneyEventDto,
  ): Promise<JourneyDocument>;
  delete(journeyEventId: Types.ObjectId): Promise<number>;
}

export const JourneyEventServiceInterface = Symbol(
  'JourneyEventServiceInterface',
);
