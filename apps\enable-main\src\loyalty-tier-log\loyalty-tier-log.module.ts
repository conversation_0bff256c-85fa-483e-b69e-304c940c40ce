import { LoyaltyTierLog, LoyaltyTierLogSchema } from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { LoyaltyTierLogRepository } from './repositories/loyalty-tier-log.repository';
import { LoyaltyTierLogRepositoryInterface } from './repositories/loyalty-tier-log.repository.interface';
import { LoyaltyTierLogService } from './services/loyalty-tier-log.service';
import { LoyaltyTierLogServiceInterface } from './services/loyalty-tier-log.service.interface';

@Module({
  providers: [
    {
      provide: LoyaltyTierLogRepositoryInterface,
      useClass: LoyaltyTierLogRepository,
    },
    {
      provide: LoyaltyTierLogServiceInterface,
      useClass: LoyaltyTierLogService,
    },
  ],
  imports: [
    MongooseModule.forFeature([
      { name: LoyaltyTierLog.name, schema: LoyaltyTierLogSchema },
    ]),
  ],
  exports: [LoyaltyTierLogServiceInterface],
})
export class LoyaltyTierLogModule {}
