import {
  IndexCampaignDto,
  Campaign,
  CampaignStatus,
  CreateCampaignDtoWithAuth,
  CampaignDocument,
  LaunchCampaignDto,
  TriggerModule,
  TriggerClient,
  TemplateFrom,
  TemplateTo,
  LanguageToLanguageCode,
  mapAsync,
  LoggerService,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { Types } from 'mongoose';
import { TemplateServiceInterface } from '../../../template/services/template/template.service.interface';
import { TriggerFireServiceInterface } from '../../../trigger/services/trigger-fire/trigger-fire.service.interface';
import { TriggerServiceInterface } from '../../../trigger/services/trigger/trigger.service.interface';
import { CampaignRepositoryInterface } from '../../repositories/campaign.repository.interface';
import { CampaignServiceInterface } from './campaign.service.interface';

@Injectable()
export class CampaignService implements CampaignServiceInterface {
  private readonly logger = new LoggerService(CampaignService.name);
  constructor(
    @Inject(CampaignRepositoryInterface)
    private readonly campaignRepository: CampaignRepositoryInterface,
    @Inject('TemplateServiceInterface')
    private readonly templateService: TemplateServiceInterface,
    @Inject('TriggerServiceInterface')
    private readonly triggerService: TriggerServiceInterface,
    @Inject('TriggerFireServiceInterface')
    private readonly triggerFireService: TriggerFireServiceInterface,
  ) {}

  public async index({
    companyId,
    brandId,
  }: IndexCampaignDto): Promise<CampaignDocument[]> {
    if (brandId) return await this.campaignRepository.findByBrand(brandId);
    return await this.campaignRepository.findByCompany(companyId);
  }

  public async create(
    createCampaignDto: CreateCampaignDtoWithAuth,
  ): Promise<CampaignDocument> {
    const newCampaign = plainToInstance(Campaign, {
      ...createCampaignDto,
      status: CampaignStatus.ACTIVE,
    });
    const campaign = await this.campaignRepository.create(newCampaign);
    return campaign;
  }

  public async launch({
    campaign,
    customers,
    replacementsMap,
    owner,
  }: LaunchCampaignDto): Promise<void> {
    const trigger = await this.triggerService.create({
      name: `Launch Campaign ${campaign.name} (${campaign._id})`,
      action: `ON_LAUNCH_CAMPAIGN_${campaign._id}`,
      module: TriggerModule.CAMPAIGN,
      replacement: Object.keys(replacementsMap),
      client: TriggerClient.ENABLE_NOTIFICATION,
    });

    await this.templateService.create({
      name: `Campaign ${campaign.name} (${campaign._id})`,
      content: {
        enContent: campaign.enContent,
        arContent: campaign.arContent,
      },
      from: TemplateFrom.COMPANY_SENDER,
      to: 'CUSTOMER',
      triggerId: trigger._id,
      type: campaign.channel,
      owner,
    });

    const results = await mapAsync(customers, async (customer) => {
      try {
        await this.triggerFireService.fire({
          action: trigger.action,
          client: trigger.client,
          module: trigger.module,
          replacements: replacementsMap[customer._id],
          users: [
            {
              id: customer._id,
              firstName: customer.first_name,
              lastName: customer.last_name,
              email: customer.email,
              phone: customer.phone,
              countryCode: customer.country_code,
              preferredLanguage: LanguageToLanguageCode[customer.language],
              role: TemplateTo.CUSTOMER,
            },
          ],
          owner,
        });
        return customer._id.toString();
      } catch (error) {
        return {
          customerId: customer._id,
          message: error.message,
          stack: error.stack,
        };
      }
    });

    await this.campaignRepository.findOneAndUpdate(
      { _id: new Types.ObjectId(campaign._id) },
      {
        $set: {
          status: CampaignStatus.ENDED,
          sentCustomerIds: results.filter(
            (result) => typeof result === 'string',
          ),
          errors: results.filter((result) => typeof result !== 'string'),
        },
      },
    );
  }
}
