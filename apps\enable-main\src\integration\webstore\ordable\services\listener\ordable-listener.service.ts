import {
  CouponDocument,
  CustomerDocument,
  EmbeddedTierDto,
  LogError,
  LoggerService,
  LoyaltyTierDocument,
  NumberOfUsesType,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { CustomerCodeServiceInterface } from '../../../../../customer/modules/customer-code/customer-code.service.interface';
import { LoyaltyTierReadServiceInterface } from '../../../../../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.service.interface';
import { StoreEvents } from '../../../../../store/enumerations/store-events.enum';
import { StoreProvider } from '../../../../../store/enumerations/store-provider.enum';
import { StoreDocument } from '../../../../../store/models/store.model';
import { OrdableCustomersServiceInterface } from '../customers/ordable-customers.service.interface';
import { OrdableFreeDeliveryServiceInterface } from '../free-delivery/ordable-free-delivery.service.interface';
import { OrdablePromotionsSyncServiceInterface } from '../promotions-sync/ordable-promotions-sync.service.interface';
import { OrdablePromotionsServiceInterface } from '../promotions/ordable-promotions.service.interface';
import { OrdableStoresServiceInterface } from '../stores/ordable-stores.service.interface';

@Injectable()
export class OrdableListenerService {
  private readonly loggerService = new LoggerService(
    OrdableListenerService.name,
  );

  constructor(
    @Inject(OrdableStoresServiceInterface)
    private readonly ordableStoresService: OrdableStoresServiceInterface,
    @Inject(OrdablePromotionsServiceInterface)
    private readonly ordablePromotionsService: OrdablePromotionsServiceInterface,
    @Inject(OrdablePromotionsSyncServiceInterface)
    private readonly ordablePromotionsSyncService: OrdablePromotionsSyncServiceInterface,
    @Inject(OrdableCustomersServiceInterface)
    private readonly ordableCustomersService: OrdableCustomersServiceInterface,
    @Inject(OrdableFreeDeliveryServiceInterface)
    private readonly ordableFreeDeliveryService: OrdableFreeDeliveryServiceInterface,
    @Inject(CustomerCodeServiceInterface)
    private readonly customerCodeService: CustomerCodeServiceInterface,
    @Inject(LoyaltyTierReadServiceInterface)
    private readonly loyaltyTierReadService: LoyaltyTierReadServiceInterface,
  ) {}

  @OnEvent('customer.tier.updated')
  @LogError()
  async handleCustomerTierUpdated(
    customer: CustomerDocument,
    previousTier?: EmbeddedTierDto,
  ) {
    await this.ordablePromotionsSyncService.syncCustomerTier(
      customer,
      previousTier,
    );
  }

  @OnEvent('customer.points.updated')
  @LogError()
  async handleCustomerPointsUpdated(customer: CustomerDocument) {
    await this.ordableCustomersService.syncLoyaltyPoints(customer);
  }

  @OnEvent('customer.rewards.updated')
  @LogError()
  async handleCustomerRewardsUpdated(customer: CustomerDocument) {
    await this.ordablePromotionsSyncService.syncCustomerRewards(customer);
  }

  @OnEvent('customer.benefits.updated')
  @LogError()
  async handleCustomerBenefitsUpdated(customer: CustomerDocument) {
    await this.ordablePromotionsSyncService.syncCustomerBenefits(customer);
  }

  @OnEvent('loyaltytier.updated')
  @LogError()
  async handleLoyaltyTierUpdated(loyaltyTier: LoyaltyTierDocument) {
    await this.ordablePromotionsService.syncPromotionUpdate(loyaltyTier);
  }

  @OnEvent('loyaltytier.freeDelivery.added')
  @LogError()
  async handleFreeDeliveryAdded(loyaltyTier: LoyaltyTierDocument) {
    await this.ordableFreeDeliveryService.handleFreeDeliveryAdded(loyaltyTier);
  }

  @OnEvent('loyaltytier.freeDelivery.removed')
  @LogError()
  async handleFreeDeliveryRemoved(loyaltyTier: LoyaltyTierDocument) {
    await this.ordableFreeDeliveryService.handleFreeDeliveryRemoved(
      loyaltyTier,
    );
  }

  @OnEvent('coupon.created')
  @LogError()
  async handleCouponCreated(coupon: CouponDocument) {
    await this.ordablePromotionsService.createCouponDiscount(coupon);
  }

  @OnEvent('coupon.updated')
  @LogError()
  async handleCouponUpdated(coupon: CouponDocument) {
    await this.ordablePromotionsService.syncPromotionUpdate(coupon);
  }

  @OnEvent('coupon.deleted')
  @LogError()
  async handleCouponDeleted(coupon: CouponDocument) {
    await this.ordablePromotionsService.deleteCouponDiscount(coupon);
  }

  @OnEvent(StoreEvents.STORE_CREATED)
  @LogError()
  async syncNewStore(store: StoreDocument) {
    if (store.provider !== StoreProvider.ORDABLE) return;
    if (!store.apiBaseUrl || !store.apiKey) return;

    const customers = await this.ordableCustomersService.initCustomers(store);

    const wasInitSuccessful = customers.some(
      (customer) =>
        customer?.ordableStores?.[store._id.toHexString()]?.ordableId,
    );
    if (!wasInitSuccessful)
      return this.loggerService.error(`Store ${store._id} init failed.`);

    await this.ordablePromotionsSyncService.initPromotions(store, customers);
    await this.ordableFreeDeliveryService.initFreeDelivery(store, customers);
    await this.ordableStoresService.handleSyncCompleted(store);
  }

  @OnEvent('store.updated')
  @LogError()
  async handleStoreUpdated(store: StoreDocument) {
    if (!store.needsSync) return;
    await this.syncNewStore(store);
  }

  @OnEvent('loyaltyTier.numberOfUses.consumed')
  @LogError()
  async onConsumedNumberOfUses(customer: CustomerDocument) {
    const loyaltyTier = await this.loyaltyTierReadService.findById(
      customer.loyaltyTier._id,
    );

    if (
      customer.loyaltyTier.percentDiscountMaximumNumberOfUsesType ===
        NumberOfUsesType.LIMITED_USAGE &&
      customer.loyaltyTier?.percentDiscountRemainingNumberOfUses === 0
    )
      await this.ordableStoresService.forEachStore(
        loyaltyTier,
        async (store) =>
          await this.ordablePromotionsService.removeCustomerFromPromotion(
            store,
            customer.ordableStores?.[store._id.toHexString()]?.ordableId,
            loyaltyTier,
          ),
      );

    if (
      customer.loyaltyTier.freeDeliveryMaximumNumberOfUsesType ===
        NumberOfUsesType.LIMITED_USAGE &&
      customer.loyaltyTier?.freeDeliveryRemainingNumberOfUses === 0
    )
      await this.ordableFreeDeliveryService.removeFreeDelivery(customer);
  }

  @OnEvent('loyaltyTier.numberOfUses.updated')
  @LogError()
  async onResetNumberOfUses(customer: CustomerDocument) {
    const loyaltyTier = await this.loyaltyTierReadService.findById(
      customer.loyaltyTier?._id,
    );
    if (
      customer.loyaltyTier.percentDiscountMaximumNumberOfUsesType ===
        NumberOfUsesType.LIMITED_USAGE &&
      customer.loyaltyTier.percentDiscountRemainingNumberOfUses > 0
    )
      await this.ordableStoresService.forEachStore(
        loyaltyTier,
        async (store) =>
          await this.ordablePromotionsService.addCustomerToPromotion(
            store,
            customer.ordableStores?.[store._id.toHexString()]?.ordableId,
            loyaltyTier,
          ),
      );

    if (
      customer.loyaltyTier.freeDeliveryMaximumNumberOfUsesType ===
        NumberOfUsesType.LIMITED_USAGE &&
      customer.loyaltyTier.freeDeliveryRemainingNumberOfUses > 0
    )
      await this.ordableFreeDeliveryService.addFreeDelivery(customer);
  }
}
