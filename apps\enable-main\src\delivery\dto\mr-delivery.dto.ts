import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class MrDeliveryConfig {
  baseUrl: string;
  apiKey: string;
  marketplaceUserId: number;
  vendorId: number;
}

export class MrDeliveryTaskToCreate {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  api_key: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  marketplace_user_id: number;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  customer_email: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  customer_phone: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  customer_name: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  customer_address: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  job_description?: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  job_delivery_latitude: number;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  job_delivery_longitude: number;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  job_pickup_datetime: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  currency_id: number;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  vendor_id: number;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  delivery_charge: number;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  payment_method: number;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  amount: number;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  order_id: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  is_custom_order: number;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  restaurant_longitude: number;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  restaurant_latitude: number;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  restaurant_address: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  restaurant_phone: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  restaurant_email: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  restaurant_name: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  job_created_from: number;
}

// export type MrDeliveryTaskStatus =  {
//   '10' : 'DeliveryStarted',
//   '12' : 'DeliveryStarted',
// };

export enum MrDeliveryTaskStatus {
  'DeliveryStarted' = 10,
  'Dispatched' = 12,
  'Delivered' = 13,
  'NotApplicable' = 14,
  'Rejected' = 15,
}

export class MrDeliveryStatusToUpdate {
  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  job_id: number;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  order_id: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  order_status: MrDeliveryTaskStatus;
}
