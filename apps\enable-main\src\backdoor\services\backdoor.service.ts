import {
  Backdoor<PERSON>ind<PERSON>ustomerD<PERSON>,
  CustomerDocument,
  LanguageToLanguageCode,
  MicroserviceCommunicationService,
} from '@app/shared-stuff';
import {
  BadRequestException,
  Inject,
  Injectable,
  OnModuleDestroy,
  OnModuleInit,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import * as moment from 'moment-timezone';
import { Types, isObjectIdOrHexString } from 'mongoose';
import { BrandServiceInterface } from '../../brand/services/brand/brand.service.interface';
import { CalendarSystemService } from '../../company/services/calendar-system/calendar-system.service';
import { CompanyService } from '../../company/services/company/company.service';
import { CustomerLoyaltyServiceInterface } from '../../customer/modules/customer-loyalty/customer-loyalty.service.interface';
import { CustomerNotificationServiceInterface } from '../../customer/modules/customer-notification/customer-notification.service.interface';
import { CustomerReadServiceInterface } from '../../customer/modules/customer-read/customer-read.service.interface';
import { CustomerReplacementsServiceInterface } from '../../customer/modules/customer-replacements/customer-replacements.service.interface';
import { CustomerRepositoryInterface } from '../../customer/modules/customer-repository/customer.repository.interface';
import { CustomerTierServiceInterface } from '../../customer/modules/customer-tier/customer-tier.service.interface';
import { ShopifyService } from '../../integration/webstore/shopify/services/shopify.service';
import { TriggerService } from '../../notification/services/trigger/trigger.service';
import { FireTriggerBackdoorDto } from '../dtos/fire-trigger.dto';
import { UpdateCustomerTierEarnDateDto } from '../dtos/update-customer-tier-earn-date.dto';

@Injectable()
export class BackdoorService implements OnModuleInit, OnModuleDestroy {
  constructor(
    @Inject('enable-main-notification-producer')
    private readonly client: ClientProxy,
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandServiceInterface,
    @Inject(CustomerRepositoryInterface)
    private readonly customerRepository: CustomerRepositoryInterface,
    @Inject(CustomerNotificationServiceInterface)
    private readonly customerNotificationService: CustomerNotificationServiceInterface,
    @Inject(CustomerReadServiceInterface)
    private readonly customerReadService: CustomerReadServiceInterface,
    @Inject(CustomerLoyaltyServiceInterface)
    private readonly customerLoyaltyService: CustomerLoyaltyServiceInterface,
    @Inject(CustomerReplacementsServiceInterface)
    private readonly customerReplacementsService: CustomerReplacementsServiceInterface,
    @Inject(CustomerTierServiceInterface)
    private readonly customerTierService: CustomerTierServiceInterface,
    private readonly triggerService: TriggerService,
    private readonly microserviceCommunicationService: MicroserviceCommunicationService,
    private readonly companyService: CompanyService,
    private readonly calendarSystemService: CalendarSystemService,
    private readonly shopifyService: ShopifyService,
  ) {}

  onModuleInit() {
    this.microserviceCommunicationService.connect(this.client);
  }

  onModuleDestroy() {
    this.microserviceCommunicationService.disconnect(this.client);
  }

  async getCustomerDetails(
    customerIdentifier: string,
    { companyId, countryCode, endDate, startDate }: BackdoorFindCustomerDto,
  ) {
    let customer: CustomerDocument;

    if (isObjectIdOrHexString(customerIdentifier)) {
      customer = await this.customerReadService.findById(
        new Types.ObjectId(customerIdentifier),
      );
    } else {
      if (!companyId) throw new BadRequestException('companyId is required');
      customer = await this.customerReadService.findOne(
        customerIdentifier,
        companyId,
        countryCode,
      );
    }

    const company = await this.companyService.findById(customer.company);

    return this.customerLoyaltyService.getCustomerWithLoyalty(
      customer,
      company,
      {
        startDate:
          startDate ?? this.calendarSystemService.getStartDate(company),
        endDate:
          endDate ?? this.calendarSystemService.getEndDate(customer, company),
      },
    );
  }

  async forceComputationCycleDay(companyId: string) {
    const customers =
      await this.customerRepository.findEligibleForTierComputation(
        [new Types.ObjectId(companyId)],
        null,
        moment.utc().startOf('month').toDate(),
      );
    if (!customers?.length)
      return `No eligible customers found. Ensure that the company has loyalty tier toggle, and try forcing tier update for some customers.`;

    customers.forEach((customer) =>
      this.customerNotificationService.fireOnComputationCycleDay(customer),
    );

    return `Fired trigger for ${customers.length} customers`;
  }

  async forceResetCustomerCalendarCycle(
    customerId: Types.ObjectId,
    startDate?: Date,
    endDate?: Date,
  ) {
    if (startDate && endDate && moment(startDate).isAfter(endDate)) {
      throw new BadRequestException('Start date must be before end date');
    }

    const customer = await this.customerReadService.findOne(
      customerId.toHexString(),
    );

    const company = await this.companyService.findById(customer.company);
    return await this.customerTierService.resetCustomerCalendarCycle(
      customer,
      company,
      {
        startDate:
          startDate ?? this.calendarSystemService.getStartDate(company),
        endDate:
          endDate ?? this.calendarSystemService.getEndDate(customer, company),
      },
    );
  }

  async fireTrigger({
    brandId,
    customerId,
    triggerModule,
    triggerAction,
  }: FireTriggerBackdoorDto) {
    const brand = await this.brandService.findById(brandId);
    const customer = await this.customerReadService.findOne(
      customerId.toHexString(),
    );
    if (!brand?.companyId.equals(customer?.company)) {
      throw new BadRequestException(
        `Brand has companyId ${brand?.companyId} but customer has companyId ${customer?.company}`,
      );
    }

    return await this.triggerService.fireTrigger(
      {
        companyId: brand.companyId.toHexString(),
        brandId: brand._id,
        senderId: brand.senderId,
        emailSenderId: brand.emailSenderId,
        customerId: customer._id,
        countryCode: customer.country_code,
        language: LanguageToLanguageCode[customer.language],
        triggerModule: triggerModule,
        replacements: {
          totalAmount: '',
          orderCode: '',
          orderSource: '',
          paymentLinkId: '',
          paymentLink: '',
          brandName: '',
          customerName: '',
          phoneNumber: '',
          email: '',
          companyName: '',
          userName: '',
          amountWithoutDelivery: '',
          paymentMethod: '',
          recipientName: '',
          deliveryAction: '',
          pickupDate: '',
          deliveryDate: '',
          locationShortenURL: '',
          locationLink: '',
          orderTrackingShortenURL: '',
          orderTrackingLink: '',
          paymentShortenURL: '',
          orderEarnedLoyaltyPoints: '',
          websiteLink: 'Dummy',
          branchName: 'Dummy',
          ...(await this.customerReplacementsService.getCustomerReplacements(
            customer,
            { brandId: brand._id },
          )),
        },
        branchId: undefined,
        isGift: false,
        giftRecipientUser: undefined,
        createdBy: undefined,
        context: {
          customer: {
            ...customer.toJSON(),
            _id: customer._id,
          },
        },
      },
      triggerAction,
    );
  }

  public async updateCustomerTierEarnDate({
    customerId,
    tierUpdatedAt,
  }: UpdateCustomerTierEarnDateDto) {
    const customer = await this.customerReadService.findOne(
      customerId.toString(),
    );

    if (!customer.loyaltyTier)
      throw new BadRequestException('Customer has no tier');

    if (customer.loyaltyTier.isVipTier)
      throw new BadRequestException(
        'Customer in VIP tier, tier earn date irrelevant.',
      );

    customer.tierUpdatedAt = tierUpdatedAt
      ? moment.utc(tierUpdatedAt).toDate()
      : moment.utc().subtract(12, 'months').toDate();
    await customer.save();

    return customer;
  }

  public async calculateHash(storeId: Types.ObjectId, rawBody: Buffer) {
    return this.shopifyService.calculateHash(storeId, rawBody);
  }
}
