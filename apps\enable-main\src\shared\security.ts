import { CorsOptions } from '@nestjs/common/interfaces/external/cors-options.interface';

const corsOptions: CorsOptions = {
  // origin: [
  //   'http://localhost:3002',
  //   'http://localhost:3000',
  //   'http://localhost:3001',
  //   'http://localhost:4200',
  //   'https://saas.e-butler.com',
  //   'https://dashboard.e-butler.com',
  //   'https://enable.e-butler.com',
  //   'https://enable.e-butler.com',
  //   'http://test.saas.e-butler.com/frontend/master',
  //   'http://test.saas.e-butler.com/frontend/saif',
  //   'http://test.saas.e-butler.com/frontend/chams',
  //   'https://test-saas.e-butler.com/frontend/master',
  //   'https://test-saas.e-butler.com/frontend/chams',
  //   'https://test-saas.e-butler.com/frontend/saif',
  //   'https://test-saas.e-butler.com/frontend/sunny',
  //   'http://test-saas.e-butler.com/frontend/sunny',
  //   'https://api.tookanapp.com',
  //   'https://notificationdemo.e-butler.com',
  //   'https://websocketdemo.e-butler.com',
  //   '*'
  // ],
  origin: '*',
  methods: 'GET,PUT,PATCH,POST,DELETE,UPDATE,OPTIONS',
  preflightContinue: false,
  credentials: true,
  allowedHeaders: [
    'Accept',
    'Content-Type',
    'Authorization',
    'x-access-token',
    'API_KEY',
    'api_key',
    'saasapikey',
  ],
};

export default corsOptions;
