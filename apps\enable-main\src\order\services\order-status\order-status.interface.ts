import {
  CurrentUser,
  OrderStatusEnum,
  OrderDocument,
  OrderTransitionTrigger,
} from '@app/shared-stuff';

export interface OrderStatusServiceInterface {
  validateStatus(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
  );
  applyPostFunction(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
    oldStatus: OrderStatusEnum,
    user: CurrentUser,
  );
}
