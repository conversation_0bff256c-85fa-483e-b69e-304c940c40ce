import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class AdminToLogin {
  @ApiProperty({
    type: String,
    required: true,
  })
  email: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  password: string;
}

export enum FeedbackCategory {
  'Complaint' = 'Complaint',
  'Report Bug' = 'Report Bug',
  'Suggestion' = 'Suggestion',
}

export class Feedback {
  @ApiProperty({
    type: String,
    required: true,
    enum: [...Object.keys(FeedbackCategory)],
  })
  @IsNotEmpty()
  category: FeedbackCategory;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  details: string;
}
