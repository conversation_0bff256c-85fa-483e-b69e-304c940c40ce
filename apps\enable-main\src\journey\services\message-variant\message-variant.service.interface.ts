import {
  CreateMessageVariantDto,
  TemplateDocument,
  UpdateMessageVariantDto,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface MessageVariantServiceInterface {
  create(
    createMessageVariantDto: CreateMessageVariantDto,
  ): Promise<TemplateDocument>;
  update(
    updateMessageVariantDto: UpdateMessageVariantDto,
  ): Promise<TemplateDocument>;
  delete(messageVariantId: Types.ObjectId): Promise<number>;
}

export const MessageVariantServiceInterface = Symbol(
  'MessageVariantServiceInterface',
);
