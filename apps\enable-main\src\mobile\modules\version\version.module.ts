import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CollectionName, VersionSchema } from '@app/shared-stuff';
import { VersionService } from './services/version.service';
import { VersionRepository } from './repositories/version.repository';

@Module({
  providers: [VersionService, VersionRepository],
  imports: [
    MongooseModule.forFeature([
      { name: CollectionName.VERSIONS, schema: VersionSchema },
    ]),
  ],
  exports: [VersionService],
})
export class VersionModule {}
