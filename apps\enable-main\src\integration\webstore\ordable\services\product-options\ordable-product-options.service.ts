import { Inject, Injectable } from '@nestjs/common';

import { Store } from '../../../../../store/models/store.model';
import { CreateOrdableProductOptionsDto } from '../../dtos/product-options/create-ordable-product-options.dto';
import { UpdateOrdableProductOptionsDto } from '../../dtos/product-options/update-ordable-product-options.dto';
import { OrdableHttpRequestsServiceInterface } from '../ordable-http-requests.service.interface';
import { OrdableProductOptionsServiceInterface } from './ordable-product-options.service.interface';

@Injectable()
export class OrdableProductOptionsService
  implements OrdableProductOptionsServiceInterface
{
  PRODUCT_OPTIONS_URI = '/api/product_options/';
  constructor(
    @Inject('OrdableHttpRequestsServiceInterface')
    private readonly ordableHttpRequestsService: OrdableHttpRequestsServiceInterface,
  ) {}

  async create(
    createOrdableProductOptionsDto: CreateOrdableProductOptionsDto,
    store: Store,
  ): Promise<any> {
    const URL = store.apiBaseUrl + this.PRODUCT_OPTIONS_URI;
    const API_KEY = store.apiKey;
    return this.ordableHttpRequestsService.createOrdablePostRequest(
      URL,
      API_KEY,
      createOrdableProductOptionsDto,
    );
  }

  update(
    updateOrdableProductOptionsDto: UpdateOrdableProductOptionsDto,
    store: Store,
  ): Promise<any> {
    const URL = store.apiBaseUrl + this.PRODUCT_OPTIONS_URI;
    const API_KEY = store.apiKey;
    return this.ordableHttpRequestsService.createOrdablePatchRequest(
      URL,
      API_KEY,
      updateOrdableProductOptionsDto,
    );
  }
}
