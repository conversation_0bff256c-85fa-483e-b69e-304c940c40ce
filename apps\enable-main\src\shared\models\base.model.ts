import { Prop, Schema } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model, Document } from 'mongoose';

export class BaseModel {
  @Prop({
    type: String,
    default: moment().startOf('month').format('MMMM'),
  })
  month: string;

  @Prop({
    type: Number,
    default: moment().week(),
  })
  week: number;

  @Prop({
    type: Number,
    default: moment().date(),
  })
  day: number;

  @Prop({
    type: Number,
    default: moment().year(),
  })
  year: number;

  // @Prop({
  //   type: Date,
  //   default: undefined,
  //   required: false,
  // })
  // deletedAt: Date;
}
