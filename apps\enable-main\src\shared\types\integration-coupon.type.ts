import { Coupon } from '@app/shared-stuff';
import { ObjectIdTransform } from '@app/shared-stuff';
import { ApiProperty, PickType } from '@nestjs/swagger';
import { Types } from 'mongoose';

export class IntegrationCoupon extends PickType(Coupon, [
  'loyaltyPointCost',
  'microsDiscountId',
]) {
  @ObjectIdTransform()
  _id: Types.ObjectId;

  @ApiProperty({ type: Number, required: false })
  flatDiscountAmount?: number;
}
