import { PassDeliveryLocationDto } from '../dtos/pass-delivery-location.dto';
import { PassDeliveryOrderDeliveryStatus } from '../enums/pass-delivery-order-delivery-status.enum';
import { PassDeliveryPaymentType } from '../enums/pass-delivery-payment-type.enum';
import { PassDeliveryDriver } from './pass-delivery-driver.type';

export class PassDeliveryGetTaskDetailsResponse {
  driver: PassDeliveryDriver;
  pickup: PassDeliveryLocationDto;
  dropoffs: PassDeliveryLocationDto[];
  price: number;
  in_progress: boolean;
  date: string;
  payment_type: PassDeliveryPaymentType;
  order_status: PassDeliveryOrderDeliveryStatus;
  share_url: string;
}
