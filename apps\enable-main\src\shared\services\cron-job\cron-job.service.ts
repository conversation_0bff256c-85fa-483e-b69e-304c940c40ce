import { LoggerService } from '@app/shared-stuff';
import { BadRequestException, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CronExpressionEnum } from '../../enums/cron-expression.enum';

@Injectable()
export class CronJobService {
  private readonly loggerService = new LoggerService(CronJobService.name);

  constructor(private readonly eventEmitter: EventEmitter2) {}

  run(expression: CronExpressionEnum): { message: string } {
    this.loggerService.log('[Cron Job Schedular] main cron job is running');
    if (!expression) throw new BadRequestException('Expression is required.');

    this.eventEmitter.emit(`cron.${expression}`);

    return {
      message: `Triggered cron job with expression '${expression}' successfully.`,
    };
  }
}
