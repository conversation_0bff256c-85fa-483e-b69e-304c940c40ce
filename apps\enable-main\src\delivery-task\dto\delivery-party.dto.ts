import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsMongoId, IsNotEmpty, IsOptional } from 'class-validator';
import { Types } from 'mongoose';

export enum DeliveryPartyThirdParty {
  'TOOKAN' = 'TOOKAN',
  'BEE' = 'BEE',
}

export class DeliveryPartyDriversFilter {
  @ApiProperty({
    type: String,
    required: true,
  })
  company: string;
}

export class DeliveryPartyThirdPartyConfig {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  API_KEY: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  TEAM_ID: string;
}

export class DeliveryPartyToCreate {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    type: Boolean,
    required: true,
  })
  @IsBoolean()
  @IsNotEmpty()
  isOwnDrivers: boolean;

  @ApiProperty({
    type: String,
    enum: [...Object.keys(DeliveryPartyThirdParty)],
    required: true,
  })
  @IsNotEmpty()
  thirdParty: DeliveryPartyThirdParty;

  @ApiProperty({
    type: Boolean,
    required: true,
  })
  @IsBoolean()
  @IsNotEmpty()
  showDriverList: boolean;

  @ApiProperty({
    type: () => DeliveryPartyDriversFilter,
    required: true,
  })
  driversFilter: DeliveryPartyDriversFilter;

  @ApiProperty({
    type: () => DeliveryPartyThirdPartyConfig,
    required: true,
  })
  thirdPartyConfig: DeliveryPartyThirdPartyConfig;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsMongoId()
  @IsOptional()
  company: Types.ObjectId;
}
