import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Brand, BrandDocument } from '@app/shared-stuff/models/brand.model';
import { BrandRepositoryInterface } from './brand.repository.interface';
import { Model, Types, UpdateQuery, UpdateWriteOpResult } from 'mongoose';
import {
  CollectionName,
  EmbeddedBranchDto,
  GenericRepository,
} from '@app/shared-stuff';

@Injectable()
export class BrandRepository
  extends GenericRepository<BrandDocument, Brand>
  implements BrandRepositoryInterface
{
  constructor(
    @InjectModel(CollectionName.BRAND)
    private brandModel: Model<BrandDocument, Brand>,
  ) {
    super(brandModel);
  }

  async findByCompanyId(companyId: Types.ObjectId): Promise<BrandDocument[]> {
    return await this.brandModel.find({
      companyId,
    });
  }

  async findByBranchId(branchId: Types.ObjectId): Promise<BrandDocument[]> {
    return await this.brandModel.find({
      branches: { $elemMatch: { _id: branchId } },
    });
  }

  async findByIdIn(ids: Types.ObjectId[]): Promise<BrandDocument[]> {
    return await this.brandModel.find({ _id: { $in: ids } });
  }

  async findByNameAndCompanyId(
    name: string,
    companyId: Types.ObjectId,
  ): Promise<BrandDocument> {
    return await this.brandModel.findOne({
      companyId: companyId,
      name: { $regex: name, $options: 'i' },
    });
  }

  async updateByBrandIds(
    brandIds: string[],
    updateQuery: UpdateQuery<BrandDocument>,
  ): Promise<UpdateWriteOpResult> {
    return await this.brandModel.updateMany(
      {
        _id: { $in: brandIds },
      },
      updateQuery,
    );
  }

  async findBrandsWithLoyalty(
    companyId: Types.ObjectId,
  ): Promise<BrandDocument[]> {
    return await this.brandModel.find({
      companyId: companyId,
      passPreviewImage: { $exists: true },
    });
  }

  async dropBranchFromBrands(
    branchId: Types.ObjectId,
    existingBrandIds: Types.ObjectId[],
  ) {
    await this.brandModel.updateMany(
      {
        $and: [
          { 'branches._id': branchId },
          { _id: { $nin: existingBrandIds } },
        ],
      },
      { $pull: { branches: { _id: branchId } } },
    );
  }

  async addBranchToBrands(
    embeddedBranchDto: EmbeddedBranchDto,
    existingBrandIds: Types.ObjectId[],
  ) {
    await this.brandModel.updateMany(
      {
        $and: [
          { _id: { $in: existingBrandIds } },
          { 'branches._id': { $ne: embeddedBranchDto._id } },
        ],
      },
      { $push: { branches: embeddedBranchDto } },
    );
  }

  async updateBranchOnBrands(embeddedBranchDto: EmbeddedBranchDto) {
    await this.brandModel.updateMany(
      { 'branches._id': embeddedBranchDto._id },
      {
        $set: {
          'branches.$.name': embeddedBranchDto.name,
        },
      },
    );
  }

  async updatePwaBaseUrl(
    brandId: Types.ObjectId,
    pwaBaseUrl?: string,
  ): Promise<void> {
    const update = pwaBaseUrl
      ? { $set: { 'loyaltyProgramConfig.pwaBaseUrl': pwaBaseUrl } }
      : { $unset: { 'loyaltyProgramConfig.pwaBaseUrl': '' } };

    await this.brandModel.updateOne({ _id: brandId }, update);
  }
}
