import { GenericRepository } from '@app/shared-stuff';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  DeliveryConfiguration,
  DeliveryConfigurationDocument,
} from '../models/delivery-configuration.model';
import { DeliveryConfigurationRepositoryInterface } from './delivery-configuration.repository.interface';

export class DeliveryConfigurationRepository
  extends GenericRepository<
    DeliveryConfigurationDocument,
    DeliveryConfiguration
  >
  implements DeliveryConfigurationRepositoryInterface
{
  constructor(
    @InjectModel(DeliveryConfiguration.name)
    private deliveryConfigurationModel: Model<
      DeliveryConfigurationDocument,
      DeliveryConfiguration
    >,
  ) {
    super(deliveryConfigurationModel);
  }

  async getByBrandIdAndBranchId(
    branchId: Types.ObjectId,
    brandId: Types.ObjectId,
  ): Promise<DeliveryConfigurationDocument> {
    return await this.deliveryConfigurationModel.findOne({
      branchId: branchId,
      brandId: brandId,
    });
  }
  async getByBrandId(
    brandId: Types.ObjectId,
  ): Promise<DeliveryConfigurationDocument> {
    return await this.deliveryConfigurationModel.findOne({ brandId: brandId });
  }
  async getByBranchId(
    branchId: Types.ObjectId,
  ): Promise<DeliveryConfigurationDocument> {
    return await this.deliveryConfigurationModel.findOne({
      branchId: branchId,
    });
  }
  async getByCompanyId(
    companyId: Types.ObjectId,
  ): Promise<DeliveryConfigurationDocument> {
    return await this.deliveryConfigurationModel.findOne({
      companyId: companyId,
    });
  }
  async findByBranchId(
    branchId: Types.ObjectId,
  ): Promise<DeliveryConfigurationDocument> {
    return await this.deliveryConfigurationModel.findOne({
      branchId: branchId,
    });
  }

  async deleteDefaultDeliveryMethod(_id: Types.ObjectId): Promise<void> {
    await this.deliveryConfigurationModel.updateOne(
      {
        _id: _id,
      },
      { $unset: { defaultDeliveryMethod: '' } },
    );
  }

  async deleteThirdPartyConfiguration(_id: Types.ObjectId): Promise<void> {
    await this.deliveryConfigurationModel.updateOne(
      {
        _id: _id,
      },
      { $unset: { thirdPartyConfiguration: '' } },
    );
  }
}
