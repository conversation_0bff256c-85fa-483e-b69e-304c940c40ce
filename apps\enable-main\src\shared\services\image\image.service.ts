import {
  CollectionName,
  Image,
  ImageDocument,
  ImageToCreate,
} from '@app/shared-stuff';
import * as BwipJs from '@bwip-js/node';
import { HttpService } from '@nestjs/axios';
import { Injectable, NotFoundException, StreamableFile } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Document, Model } from 'mongoose';
import { generate } from 'randomstring';
import * as sharp from 'sharp';
import { GoogleCloudStorageService } from '../../../storage/google-cloud-storage.service';

@Injectable()
export class ImageService {
  constructor(
    private readonly googleCloudStorageService: GoogleCloudStorageService,
    private readonly http: HttpService,
    @InjectModel(CollectionName.IMAGE)
    private readonly imageModel: Model<ImageDocument>,
  ) {}

  async generateBarcodeImage(text: string) {
    const qrCode = await BwipJs.toBuffer({
      bcid: 'code128',
      text: text,
      scale: 3,
      height: 15,
      includetext: true,
      textxalign: 'center',
    });

    const qrCodeImage = await sharp(qrCode).resize(268, 100).toBuffer();

    return new StreamableFile(qrCodeImage, { type: 'image/png' });
  }

  async getImageUrl(folder: string, name: string): Promise<string> {
    const image = await this.imageModel.findOne({ name, for: folder });

    if (!image || !image.url) throw new NotFoundException();

    return image.url;
  }

  async uploadImage(imageToCreate: ImageToCreate | Image): Promise<Image> {
    if (typeof imageToCreate !== 'object') return imageToCreate;
    if (!('base64' in imageToCreate)) return imageToCreate;

    imageToCreate.name = imageToCreate.name + generate(5) + '.png';
    imageToCreate.base64 = imageToCreate.base64.split(';base64,').pop();

    const uploadedImage =
      await this.googleCloudStorageService.uploadImage(imageToCreate);

    await this.imageModel.create({
      name: uploadedImage.name,
      for: uploadedImage.for,
      url: uploadedImage.url,
    } satisfies Omit<ImageDocument, keyof Document>);

    return uploadedImage;
  }

  public async readImageData(image: Image): Promise<Buffer> {
    if (!image.url) return Buffer.from('');

    const response = await this.http.axiosRef.get(image.url, {
      responseType: 'arraybuffer',
    });
    return Buffer.from(response.data);
  }
}
