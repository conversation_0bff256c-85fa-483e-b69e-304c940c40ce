import {
  CalendarCycle,
  CalendarSystem,
  CompanyDocument,
  CustomerDocument,
  CycleOffset,
  EmbeddedOrderDto,
  EmbeddedTierDto,
  IsLoyaltyOrder,
  LoggerService,
  loyaltyOrderSources,
  LoyaltyTierDocument,
  LoyaltyTierMilestone,
  LoyaltyTierProgramProgress,
  OrderStatusEnum,
  TierLevellingUpMethod,
  TierStatus,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';

import { plainToInstance } from 'class-transformer';
import * as moment from 'moment-timezone';
import { CalendarSystemService } from '../../../company/services/calendar-system/calendar-system.service';
import { CompanyService } from '../../../company/services/company/company.service';
import { LoyaltyTierReadServiceInterface } from '../../../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.service.interface';
import { CustomerPointsService } from '../customer-points/customer-points.service';
import { CustomerTierInfoServiceInterface } from './customer-tier-info.service.interface';

@Injectable()
export class CustomerTierInfoService
  implements CustomerTierInfoServiceInterface
{
  private readonly logger = new LoggerService(CustomerTierInfoService.name);

  constructor(
    private readonly companyService: CompanyService,
    private readonly calendarSystemService: CalendarSystemService,
    private readonly customerPointsService: CustomerPointsService,
    @Inject(LoyaltyTierReadServiceInterface)
    private readonly loyaltyTierService: LoyaltyTierReadServiceInterface,
  ) {}

  public createIsLoyaltyOrder(
    customer: CustomerDocument,
    company: CompanyDocument,
    calendarCycle?: CalendarCycle,
  ): IsLoyaltyOrder {
    const calendarStartDate =
      calendarCycle?.startDate ??
      this.calendarSystemService.getStartDate(company);
    const calendarEndDate =
      calendarCycle?.endDate ??
      this.calendarSystemService.getEndDate(customer, company);

    const timezone = company.localization.timezone || 'Asia/Qatar';

    const calendarStartDateInLocalTime = moment
      .tz(calendarStartDate, timezone)
      .utc(true)
      .toDate();

    const calendarEndDateInLocalTime = moment
      .tz(calendarEndDate, timezone)
      .utc(true)
      .toDate();
    return (order: EmbeddedOrderDto): boolean => {
      if (!order) return false;

      const isCompleted = order.status === OrderStatusEnum.COMPLETED;

      if (!isCompleted || order.deletedAt) return false;

      const isAfterStartDate = moment
        .utc(order.pickup_date) // pickup date is actually in local time but parsed as UTC
        .isAfter(calendarStartDateInLocalTime);
      if (!isAfterStartDate) return false;

      const isBeforeEndDate = moment
        .utc(order.pickup_date)
        .isBefore(calendarEndDateInLocalTime);
      if (!isBeforeEndDate) return false;

      const isDirectOrder = loyaltyOrderSources.includes(order.source);
      if (!isDirectOrder) return false;

      return order.isOrderCounted;
    };
  }

  private async getLoyaltyOrders(
    customer: CustomerDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<EmbeddedOrderDto[]> {
    if (!customer || !customer.orders || customer.orders.length === 0)
      return [];

    const company = await this.companyService.findById(customer.company);
    if (!company?.hasLoyaltyProgram || !company?.loyaltyProgramConfig)
      return [];

    const isLoyaltyOrder = this.createIsLoyaltyOrder(
      customer,
      company,
      calendarCycle,
    );

    const matchedOrders = customer.orders.filter(isLoyaltyOrder);
    return matchedOrders;
  }

  public async getLoyaltyOrderRate(
    customer: CustomerDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<number> {
    const loyaltyOrders = await this.getLoyaltyOrders(customer, calendarCycle);
    const tierRateOrders = loyaltyOrders.filter(
      (order: EmbeddedOrderDto) => order.isCartValueThresholdMet,
    );
    return tierRateOrders.length;
  }

  public async getTotalLoyaltyOrderRate(
    customer: CustomerDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<number> {
    const loyaltyOrderRate = await this.getLoyaltyOrderRate(
      customer,
      calendarCycle,
    );
    return loyaltyOrderRate + (customer.carryOverOrderRate || 0);
  }

  public async getLoyaltyAmountSpent(
    customer: CustomerDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<number> {
    const loyaltyOrders = await this.getLoyaltyOrders(customer, calendarCycle);
    return loyaltyOrders
      .map((order) => order.total_amount || 0)
      .reduce((partialSum, amount) => partialSum + amount, 0);
  }

  public async getTotalLoyaltyAmountSpent(
    customer: CustomerDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<number> {
    const loyaltyAmountSpent = await this.getLoyaltyAmountSpent(
      customer,
      calendarCycle,
    );
    return loyaltyAmountSpent + (customer.carryOverAmountSpent || 0);
  }

  public async getLoyaltyPointsRate(
    customer: CustomerDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<number> {
    const loyaltyOrders = await this.getLoyaltyOrders(customer, calendarCycle);
    return loyaltyOrders
      .map((order) => order?.loyaltyProgress?.pointsEarned || 0)
      .reduce((partialSum, amount) => partialSum + amount, 0);
  }

  public async getTotalLoyaltyPointsRate(
    customer: CustomerDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<number> {
    const loyaltyPointsRate = await this.getLoyaltyPointsRate(
      customer,
      calendarCycle,
    );
    return loyaltyPointsRate + (customer.carryOverPointsRate || 0);
  }

  async getRemainingOrdersCurrentTier(
    customer: CustomerDocument,
    totalLoyaltyOrderRate?: number,
  ): Promise<number> {
    return this.computeRemainingOrders(
      customer.loyaltyTier,
      totalLoyaltyOrderRate ?? (await this.getTotalLoyaltyOrderRate(customer)),
    );
  }

  async getRemainingOrdersUpperTier(
    customer: CustomerDocument,
    upperTier: LoyaltyTierDocument,
    totalLoyaltyOrderRate?: number,
  ): Promise<number> {
    if (customer.loyaltyTier?.isVipTier) return 0;

    return this.computeRemainingOrders(
      upperTier,
      totalLoyaltyOrderRate ?? (await this.getTotalLoyaltyOrderRate(customer)),
    );
  }

  public computeRemainingOrders(
    loyaltyTier: LoyaltyTierDocument | EmbeddedTierDto,
    totalLoyaltyOrderRate: number,
  ): number {
    if (!loyaltyTier) return 0;
    if (loyaltyTier?.isVipTier) return 0;
    const threshold = loyaltyTier?.orderRateThreshold ?? 0;
    return Math.max(threshold - totalLoyaltyOrderRate, 0);
  }

  async getRemainingAmountSpentCurrentTier(
    customer: CustomerDocument,
    loyaltyAmountSpent?: number,
  ): Promise<number> {
    if (!customer.loyaltyTier) return undefined;
    const loyaltyTier = await this.loyaltyTierService.findById(
      customer.loyaltyTier._id,
    );

    return this.computeRemainingAmount(
      loyaltyTier,
      loyaltyAmountSpent ?? (await this.getTotalLoyaltyAmountSpent(customer)),
    );
  }

  async getRemainingAmountSpentUpperTier(
    customer: CustomerDocument,
    upperTier: LoyaltyTierDocument,
    loyaltyAmountSpent?: number,
  ): Promise<number> {
    return this.computeRemainingAmount(
      upperTier,
      loyaltyAmountSpent ?? (await this.getTotalLoyaltyAmountSpent(customer)),
    );
  }

  public computeRemainingAmount(
    loyaltyTier: LoyaltyTierDocument | EmbeddedTierDto,
    totalAmountSpent: number,
  ): number {
    if (loyaltyTier?.isVipTier) return undefined;
    if (!loyaltyTier?.amountSpentThreshold) return undefined;
    return Math.max(loyaltyTier.amountSpentThreshold - totalAmountSpent, 0);
  }

  public computeRemainingPoints(
    loyaltyTier: LoyaltyTierDocument | EmbeddedTierDto,
    loyaltyPointsRate: number,
  ): number {
    if (loyaltyTier?.isVipTier) return 0;
    if (!loyaltyTier?.pointsRateThreshold) return undefined;
    return Math.max(loyaltyTier.pointsRateThreshold - loyaltyPointsRate, 0);
  }

  async getTierValidTill(
    customer: CustomerDocument,
    company: CompanyDocument,
    useReminderDateAsValidTill?: boolean,
  ): Promise<Date | null> {
    const hasLoyaltyTiers = company?.loyaltyProgramConfig?.hasLoyaltyTiers;
    const calendarSystem = company?.loyaltyProgramConfig?.calendarSystem;
    if (!hasLoyaltyTiers || !calendarSystem) return null;

    if (!customer.loyaltyTier || customer.loyaltyTier.isVipTier) return null;

    if (
      useReminderDateAsValidTill ??
      company.loyaltyProgramConfig.useReminderDateAsValidTill
    ) {
      const reminderDate = await this.getGracePeriodReminderDate(
        customer,
        company,
      );
      if (reminderDate) return reminderDate;
    }

    const isRelativeCalendarSystem =
      this.calendarSystemService.isRelativeCalendarSystem(
        company.loyaltyProgramConfig.calendarSystem,
      );
    let hasMaintainedTier: boolean;
    if (isRelativeCalendarSystem)
      hasMaintainedTier = await this.hasCustomerMaintainedTier(customer, {
        startDate: customer.tierUpdatedAt,
        endDate: this.calendarSystemService.getEndDate(customer, company),
      });
    else hasMaintainedTier = await this.hasCustomerMaintainedTier(customer);

    if (!hasMaintainedTier)
      return this.calendarSystemService.getEndDate(customer, company);

    const hasMaintainedTierNextCycle =
      await this.hasCustomerMaintainedTierNextCycle(customer, company);
    const cycleOffset = hasMaintainedTierNextCycle
      ? CycleOffset.CYCLE_AFTER_NEXT_CYCLE
      : CycleOffset.NEXT_CYCLE;
    return this.calendarSystemService.getEndDate(
      customer,
      company,
      cycleOffset,
    );
  }

  async getGracePeriodReminderDate(
    customer: CustomerDocument,
    company: CompanyDocument,
  ) {
    const hasLoyaltyTiers = company?.loyaltyProgramConfig?.hasLoyaltyTiers;
    const calendarSystem = company?.loyaltyProgramConfig?.calendarSystem;
    if (!hasLoyaltyTiers || !calendarSystem) return null;

    if (
      !customer.loyaltyTier ||
      customer.loyaltyTier.isVipTier ||
      !customer.loyaltyRegistrationAt
    )
      return null;

    const dateUnit =
      calendarSystem === CalendarSystem.QUARTERLY ? 'quarter' : 'month';
    const reminderDate = moment
      .utc(customer.tierUpdatedAt)
      .add(1, dateUnit)
      .toDate();

    const isInGracePeriod = await this.isInGracePeriod(customer, company);
    if (!isInGracePeriod) return null;

    return reminderDate;
  }

  async isInGracePeriod(
    customer: CustomerDocument,
    company: CompanyDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<boolean> {
    if (!customer.loyaltyRegistrationAt) return false;

    const calendarSystem = company.loyaltyProgramConfig.calendarSystem;
    const calendarSystemHasNoGracePeriod =
      this.calendarSystemService.isRelativeCalendarSystem(calendarSystem);
    if (calendarSystemHasNoGracePeriod) return false;

    const gracePeriodStartDate =
      this.calendarSystemService.getGracePeriodStartDate(
        company,
        calendarCycle?.startDate ??
          this.calendarSystemService.getStartDate(company),
      );
    const gracePeriodEndDate = calendarCycle?.endDate
      ? calendarCycle.endDate
      : this.calendarSystemService.getGracePeriodEndDate(calendarSystem);

    return moment
      .utc(customer.loyaltyRegistrationAt)
      .isBetween(gracePeriodStartDate, gracePeriodEndDate);
  }

  async hasCustomerMaintainedTier(
    customer: CustomerDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<boolean> {
    if (!customer.loyaltyTier) return true;
    if (customer.loyaltyTier.isVipTier) return true;
    if (customer.tierStatus === TierStatus.MAINTAINED) return true;

    const company = await this.companyService.findById(customer.company);
    const isFixedCalendarSystem =
      this.calendarSystemService.isFixedCalendarSystem(
        company.loyaltyProgramConfig.calendarSystem,
      );
    if (isFixedCalendarSystem && customer.tierStatus === TierStatus.UPGRADED)
      return true;

    if (await this.isInGracePeriod(customer, company, calendarCycle))
      return true;
    return await this.hasCustomerMetTierRequirements(
      customer,
      company,
      calendarCycle,
    );
  }

  async hasCustomerMaintainedTierNextCycle(
    customer: CustomerDocument,
    company: CompanyDocument,
  ): Promise<boolean> {
    const tier = customer.loyaltyTier;
    if (!tier) return true;
    if (tier.isVipTier) return true;

    const hasCarryOver = this.companyService.hasCarryOver(company);
    if (!hasCarryOver) return false;

    return await this.companyService.evaluateTierLevellingUpMethod(company, {
      orderRate: () => this.willCarryOverMeetOrderRateThreshold(customer, tier),
      amountSpent: () =>
        this.willCarryOverMeetAmountSpentThreshold(customer, tier),
      pointsRate: () =>
        this.willCarryOverMeetPointsRateThreshold(customer, tier),
      fallback: false,
    });
  }

  private async willCarryOverMeetOrderRateThreshold(
    customer: CustomerDocument,
    tier: EmbeddedTierDto,
  ) {
    const loyaltyOrderRate = await this.getLoyaltyOrderRate(customer);
    const totalLoyaltyOrderRate = await this.getTotalLoyaltyOrderRate(customer);
    const carryOverOrderRate = Math.min(
      totalLoyaltyOrderRate - tier.orderRateThreshold,
      loyaltyOrderRate,
    );
    return carryOverOrderRate >= tier.orderRateThreshold;
  }

  private async willCarryOverMeetAmountSpentThreshold(
    customer: CustomerDocument,
    tier: EmbeddedTierDto,
  ) {
    const loyaltyAmountSpent = await this.getLoyaltyAmountSpent(customer);
    const totalLoyaltyAmountSpent =
      await this.getTotalLoyaltyAmountSpent(customer);
    const carryOverAmountSpent = Math.min(
      totalLoyaltyAmountSpent - tier.amountSpentThreshold,
      loyaltyAmountSpent,
    );
    return carryOverAmountSpent >= tier.amountSpentThreshold;
  }

  private async willCarryOverMeetPointsRateThreshold(
    customer: CustomerDocument,
    tier: EmbeddedTierDto,
  ) {
    const totalLoyaltyPointsRate =
      await this.getTotalLoyaltyPointsRate(customer);
    const loyaltyPointsRate = await this.getLoyaltyPointsRate(customer);
    const carryOverPointsRate = Math.min(
      totalLoyaltyPointsRate - tier.pointsRateThreshold,
      loyaltyPointsRate ?? 0,
    );
    return carryOverPointsRate >= tier.pointsRateThreshold;
  }

  async hasCustomerMetTierRequirements(
    customer: CustomerDocument,
    company: CompanyDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<boolean> {
    if (!customer.loyaltyTier) return false;

    return await this.companyService.evaluateTierLevellingUpMethod(company, {
      orderRate: () => this.isOrderRateThresholdMet(customer, calendarCycle),
      amountSpent: () =>
        this.isAmountSpentThresholdMet(customer, calendarCycle),
      pointsRate: () => this.isPointsRateThresholdMet(customer, calendarCycle),
      fallback: false,
    });
  }

  private async isOrderRateThresholdMet(
    customer: CustomerDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<boolean> {
    if (!customer.loyaltyTier?.orderRateThreshold) return false;
    const orderRate = await this.getTotalLoyaltyOrderRate(
      customer,
      calendarCycle,
    );
    return orderRate >= customer.loyaltyTier.orderRateThreshold;
  }

  private async isAmountSpentThresholdMet(
    customer: CustomerDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<boolean> {
    if (!customer.loyaltyTier?.amountSpentThreshold) return false;
    const amountSpent = await this.getTotalLoyaltyAmountSpent(
      customer,
      calendarCycle,
    );
    return amountSpent >= customer.loyaltyTier.amountSpentThreshold;
  }

  private async isPointsRateThresholdMet(
    customer: CustomerDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<boolean> {
    if (!customer.loyaltyTier?.pointsRateThreshold) return false;
    const pointsRate = await this.getTotalLoyaltyPointsRate(
      customer,
      calendarCycle,
    );
    return pointsRate >= customer.loyaltyTier.pointsRateThreshold;
  }

  async willTierBeMaintained(
    company: CompanyDocument,
    order: EmbeddedOrderDto,
    customer: CustomerDocument,
  ): Promise<boolean> {
    const isTierMaintained = await this.hasCustomerMaintainedTier(customer);
    if (isTierMaintained) return false;

    return await this.willTierLevellingUpMethodBeMet(
      company,
      customer,
      order,
      customer.loyaltyTier,
    );
  }

  private async willTierLevellingUpMethodBeMet(
    company: CompanyDocument,
    customer: CustomerDocument,
    order: EmbeddedOrderDto,
    loyaltyTier: EmbeddedTierDto,
  ) {
    return await this.companyService.evaluateTierLevellingUpMethod(company, {
      orderRate: () =>
        this.willOrderRateThresholdBeMet(customer, order, loyaltyTier),
      amountSpent: () =>
        this.willAmountSpentThresholdBeMet(customer, order, loyaltyTier),
      pointsRate: () =>
        this.willPointsRateThresholdBeMet(
          customer,
          order,
          loyaltyTier,
          company,
        ),
    });
  }

  private async willOrderRateThresholdBeMet(
    customer: CustomerDocument,
    order: EmbeddedOrderDto,
    loyaltyTier: EmbeddedTierDto,
  ): Promise<boolean> {
    if (!order.isCartValueThresholdMet) return false;

    const totalLoyaltyOrderRate = await this.getTotalLoyaltyOrderRate(customer);
    const newOrderRate = totalLoyaltyOrderRate + 1;

    const isThresholdMet = newOrderRate >= loyaltyTier.orderRateThreshold;
    this.logger.log(`Will order rate threshold be met: ${isThresholdMet}`, {
      totalLoyaltyOrderRate,
      newOrderRate,
      threshold: loyaltyTier.orderRateThreshold,
    });
    return isThresholdMet;
  }

  private async willAmountSpentThresholdBeMet(
    customer: CustomerDocument,
    order: EmbeddedOrderDto,
    loyaltyTier: EmbeddedTierDto,
  ): Promise<boolean> {
    const totalLoyaltyAmountSpent =
      await this.getTotalLoyaltyAmountSpent(customer);
    const newAmountSpent = totalLoyaltyAmountSpent + order.total_amount;

    const isThresholdMet = newAmountSpent >= loyaltyTier.amountSpentThreshold;
    this.logger.log(`Will order rate threshold be met: ${isThresholdMet}`, {
      totalLoyaltyAmountSpent,
      newAmountSpent,
      threshold: loyaltyTier.amountSpentThreshold,
    });
    return isThresholdMet;
  }

  private async willPointsRateThresholdBeMet(
    customer: CustomerDocument,
    order: EmbeddedOrderDto,
    loyaltyTier: EmbeddedTierDto,
    company: CompanyDocument,
  ): Promise<boolean> {
    const pointsRate = await this.getTotalLoyaltyPointsRate(customer);
    const pointsEarned =
      this.customerPointsService.calculatePointsEarnedOnOrder(
        order,
        customer,
        company,
      );
    const newPointsRate = pointsRate + pointsEarned;

    return newPointsRate >= loyaltyTier.pointsRateThreshold;
  }

  async willTierBeUpgraded(
    company: CompanyDocument,
    order: EmbeddedOrderDto,
    customer: CustomerDocument,
  ): Promise<boolean> {
    const currentTier = customer.loyaltyTier
      ? await this.loyaltyTierService.findById(customer.loyaltyTier._id)
      : null;
    const nextTier = await this.loyaltyTierService.findNextTier(
      customer.company,
      currentTier?.tierIndex,
    );
    if (!nextTier) return false;

    return await this.willTierLevellingUpMethodBeMet(
      company,
      customer,
      order,
      nextTier,
    );
  }

  async getThresholdPercentage(
    customer: CustomerDocument,
    company: CompanyDocument,
    tier?: LoyaltyTierDocument | EmbeddedTierDto,
  ): Promise<number> {
    if (!tier && !customer.loyaltyTier) return 100;
    if (!tier && customer.tierStatus === TierStatus.MAINTAINED) return 100;
    tier = tier ?? customer.loyaltyTier;

    return await this.companyService.evaluateTierLevellingUpMethod(company, {
      orderRate: () => this.getOrderRateThresholdPercentage(customer, tier),
      amountSpent: () => this.getAmountSpentThresholdPercentage(customer, tier),
      pointsRate: () => this.getPointsRateThresholdPercentage(customer, tier),
      fallback: 100,
    });
  }

  private async getOrderRateThresholdPercentage(
    customer: CustomerDocument,
    tier: LoyaltyTierDocument | EmbeddedTierDto,
  ): Promise<number> {
    const remainingOrders = this.computeRemainingOrders(
      tier,
      await this.getTotalLoyaltyOrderRate(customer),
    );
    const remainingOrdersPercent = remainingOrders / tier.orderRateThreshold;
    return (1 - remainingOrdersPercent) * 100;
  }

  private async getAmountSpentThresholdPercentage(
    customer: CustomerDocument,
    tier: LoyaltyTierDocument | EmbeddedTierDto,
  ): Promise<number> {
    const remainingAmount = this.computeRemainingOrders(
      tier,
      await this.getTotalLoyaltyAmountSpent(customer),
    );
    const remainingAmountPercent = remainingAmount / tier.amountSpentThreshold;
    return (1 - remainingAmountPercent) * 100;
  }

  private async getPointsRateThresholdPercentage(
    customer: CustomerDocument,
    tier: LoyaltyTierDocument | EmbeddedTierDto,
  ): Promise<number> {
    const remainingPoints = this.computeRemainingPoints(
      tier,
      await this.getTotalLoyaltyPointsRate(customer),
    );
    const remainingPointsPercent = remainingPoints / tier.pointsRateThreshold;
    return (1 - remainingPointsPercent) * 100;
  }

  async getLastMilestone(
    customer: CustomerDocument,
    company: CompanyDocument,
  ): Promise<LoyaltyTierMilestone> {
    if (!customer.loyaltyTier) return null;

    if (
      company.loyaltyProgramConfig?.tierLevellingUpMethod ===
      TierLevellingUpMethod.POINTS_RATE
    ) {
      return {
        ...customer.loyaltyTier,
        threshold: customer.loyaltyTier.pointsRateThreshold,
        distanceToThreshold: 0,
        currencySymbol: company.loyaltyProgramConfig.calendarPointsTitleEn,
      };
    } else if (
      company.loyaltyProgramConfig?.tierLevellingUpMethod ===
      TierLevellingUpMethod.AMOUNT_SPENT
    ) {
      return {
        ...customer.loyaltyTier,
        threshold: customer.loyaltyTier.amountSpentThreshold,
        distanceToThreshold: 0,
        currencySymbol: this.companyService.getCurrencySymbol(company),
      };
    } else {
      return await this.getPreviousAmountSpentMilestone(customer, company);
    }
  }

  async getPreviousAmountSpentMilestone(
    customer: CustomerDocument,
    company: CompanyDocument,
  ): Promise<LoyaltyTierMilestone> {
    const currentTierHasAmountSpentThreshold =
      customer.loyaltyTier?.amountSpentThreshold;
    if (currentTierHasAmountSpentThreshold)
      return {
        ...customer.loyaltyTier,
        threshold: customer.loyaltyTier.amountSpentThreshold,
        distanceToThreshold: 0,
        currencySymbol: this.companyService.getCurrencySymbol(company),
      };

    const previousTier = await this.loyaltyTierService.findPreviousTier(
      customer.company,
      customer.loyaltyTier?._id,
      true,
    );
    if (!previousTier) return null;
    return {
      ...previousTier.toObject(),
      threshold: previousTier.amountSpentThreshold,
      distanceToThreshold: 0,
      currencySymbol: this.companyService.getCurrencySymbol(company),
    };
  }

  async getNextMilestone(
    customer: CustomerDocument,
    company: CompanyDocument,
  ): Promise<LoyaltyTierMilestone> {
    if (customer?.loyaltyTier?.isHighestTier) return null;
    if (
      company.loyaltyProgramConfig?.tierLevellingUpMethod ===
      TierLevellingUpMethod.POINTS_RATE
    ) {
      return await this.getNextPointsRateMilestone(customer, company);
    } else {
      return await this.getNextAmountSpentMilestone(customer, company);
    }
  }

  async getNextPointsRateMilestone(
    customer: CustomerDocument,
    company: CompanyDocument,
  ): Promise<LoyaltyTierMilestone> {
    const currentTier = customer.loyaltyTier
      ? await this.loyaltyTierService.findById(customer.loyaltyTier._id)
      : null;
    const nextTier = await this.loyaltyTierService.findNextTier(
      customer.company,
      currentTier?.tierIndex,
    );
    if (!nextTier) return null;

    const pointsRate = await this.getTotalLoyaltyPointsRate(customer);
    const distanceToThreshold = Math.max(
      0,
      nextTier.pointsRateThreshold - pointsRate,
    );
    return {
      ...nextTier.toObject(),
      threshold: nextTier.pointsRateThreshold,
      distanceToThreshold,
      currencySymbol: company.loyaltyProgramConfig.calendarPointsTitleEn,
    };
  }

  async getNextAmountSpentMilestone(
    customer: CustomerDocument,
    company: CompanyDocument,
  ): Promise<LoyaltyTierMilestone> {
    const currentTier = customer.loyaltyTier
      ? await this.loyaltyTierService.findById(customer.loyaltyTier._id)
      : null;
    const nextTier = await this.loyaltyTierService.findNextTier(
      customer.company,
      currentTier?.tierIndex,
      true,
    );
    if (!nextTier) return null;

    const totalAmountSpent = await this.getTotalLoyaltyAmountSpent(customer);
    const distanceToThreshold = Math.max(
      0,
      nextTier.amountSpentThreshold - totalAmountSpent,
    );
    return {
      ...nextTier.toObject(),
      threshold: nextTier.amountSpentThreshold,
      distanceToThreshold,
      currencySymbol: this.companyService.getCurrencySymbol(company),
    };
  }

  async calculateRemainingRequirements(
    customer: CustomerDocument,
    upperTier: LoyaltyTierDocument,
  ): Promise<string> {
    const company = await this.companyService.findById(customer.company);
    switch (customer.tierStatus) {
      case TierStatus.NOT_MAINTAINED:
        return await this.calculateRequirementsNotMaintained(customer, company);
      case TierStatus.MAINTAINED:
      case TierStatus.UPGRADED:
        return await this.calculateRequirementsMaintainedOrUpgraded(
          customer,
          company,
          upperTier,
        );
      default:
        return '';
    }
  }

  private async calculateRequirementsNotMaintained(
    customer: CustomerDocument,
    company: CompanyDocument,
  ) {
    return await this.companyService.evaluateTierLevellingUpMethod(company, {
      orderRate: async () => {
        const remainingOrdersCurrentTier =
          await this.getRemainingOrdersCurrentTier(customer);
        return `${remainingOrdersCurrentTier} orders to maintain ${customer.loyaltyTier.nameEn}`;
      },
      amountSpent: async () => {
        const remainingAmountSpentCurrentTier =
          await this.getRemainingAmountSpentCurrentTier(customer);

        return `${remainingAmountSpentCurrentTier} ${
          company.localization?.currency ?? 'QAR'
        } to maintain ${customer.loyaltyTier.nameEn}`;
      },
      both: async () => {
        const remainingOrdersCurrentTier =
          await this.getRemainingOrdersCurrentTier(customer);
        const remainingAmountSpentCurrentTier =
          await this.getRemainingAmountSpentCurrentTier(customer);

        return `${remainingOrdersCurrentTier} orders or ${remainingAmountSpentCurrentTier} ${
          company.localization?.currency ?? 'QAR'
        } to maintain ${customer.loyaltyTier.nameEn}`;
      },
      pointsRate: async () => {
        const remainingPointsCurrentTier = this.computeRemainingPoints(
          customer.loyaltyTier,
          await this.getTotalLoyaltyPointsRate(customer),
        );
        return `${remainingPointsCurrentTier} points to maintain ${customer.loyaltyTier.nameEn}`;
      },
    });
  }

  private async calculateRequirementsMaintainedOrUpgraded(
    customer: CustomerDocument,
    company: CompanyDocument,
    upperTier: LoyaltyTierDocument,
  ) {
    if (customer.loyaltyTier.isVipTier || !upperTier)
      return 'Customer Already on Highest Tier';

    return await this.companyService.evaluateTierLevellingUpMethod(company, {
      orderRate: async () => {
        const remainingOrdersUpperTier = this.computeRemainingOrders(
          upperTier,
          await this.getTotalLoyaltyOrderRate(customer),
        );
        return `${remainingOrdersUpperTier} orders to upgrade to ${upperTier.nameEn}`;
      },
      amountSpent: async () => {
        const remainingAmountSpentUpperTier = this.computeRemainingAmount(
          upperTier,
          await this.getTotalLoyaltyAmountSpent(customer),
        );

        return `${remainingAmountSpentUpperTier} ${
          company.localization.currency ?? 'QAR'
        } to upgrade to ${upperTier.nameEn}`;
      },
      both: async () => {
        const remainingOrdersUpperTier = this.computeRemainingOrders(
          upperTier,
          await this.getTotalLoyaltyOrderRate(customer),
        );
        const remainingAmountSpentUpperTier = this.computeRemainingAmount(
          upperTier,
          await this.getTotalLoyaltyAmountSpent(customer),
        );
        return `${remainingOrdersUpperTier} orders or ${remainingAmountSpentUpperTier} ${
          company.localization.currency ?? 'QAR'
        } to upgrade to ${upperTier.nameEn}`;
      },
      pointsRate: async () => {
        const remainingPointsCurrentTier = this.computeRemainingPoints(
          upperTier,
          await this.getTotalLoyaltyPointsRate(customer),
        );
        return `${remainingPointsCurrentTier} points to upgrade to ${upperTier.nameEn}`;
      },
    });
  }

  public async getLoyaltyTierProgramProgress(
    customer: CustomerDocument,
    company: CompanyDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<LoyaltyTierProgramProgress> {
    const amountSpent = async () => ({
      amountSpent: await this.getTotalLoyaltyAmountSpent(
        customer,
        calendarCycle,
      ),
    });

    const orderRate = async () => ({
      orderRate: await this.getTotalLoyaltyOrderRate(customer, calendarCycle),
    });

    const both = async () => ({
      amountSpent: await this.getTotalLoyaltyAmountSpent(
        customer,
        calendarCycle,
      ),
      orderRate: await this.getTotalLoyaltyOrderRate(customer, calendarCycle),
    });

    const pointsRate = async () => ({
      pointsRate: await this.getTotalLoyaltyPointsRate(customer, calendarCycle),
    });

    const plainTierLoyaltyProgress =
      await this.companyService.evaluateTierLevellingUpMethod<LoyaltyTierProgramProgress>(
        company,
        { amountSpent, orderRate, both, pointsRate, fallback: {} },
      );
    return plainToInstance(
      LoyaltyTierProgramProgress,
      plainTierLoyaltyProgress,
    );
  }
}
