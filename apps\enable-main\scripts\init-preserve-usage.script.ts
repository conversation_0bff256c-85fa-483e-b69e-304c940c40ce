// EBL-5645 Tier Benefits Configuration | Preserve Usage Progress in Calendar Cycle
// Init preserveUsage for existing benefits

db.benefits.updateMany(
  {
    'config.maximumUsageType': 'limited',
    'config.preserveUsage': { $exists: false },
  },
  { $set: { 'config.preserveUsage': false } },
);

db.loyaltytiers.updateMany(
  {
    benefits: {
      $elemMatch: {
        'config.maximumUsageType': 'limited',
        'config.preserveUsage': { $exists: false },
      },
    },
  },
  { $set: { 'benefits.$[needsUpdate].config.preserveUsage': false } },
  {
    arrayFilters: [
      {
        'needsUpdate.config.maximumUsageType': 'limited',
        'needsUpdate.config.preserveUsage': { $exists: false },
      },
    ],
  },
);

db.customers.updateMany(
  {
    earnedBenefits: {
      $elemMatch: {
        'config.maximumUsageType': 'limited',
        'config.preserveUsage': { $exists: false },
      },
    },
  },
  { $set: { 'earnedBenefits.$[needsUpdate].config.preserveUsage': false } },
  {
    arrayFilters: [
      {
        'needsUpdate.config.maximumUsageType': 'limited',
        'needsUpdate.config.preserveUsage': { $exists: false },
      },
    ],
  },
);

db.customers.updateMany(
  {
    usedBenefits: {
      $elemMatch: {
        'config.maximumUsageType': 'limited',
        'config.preserveUsage': { $exists: false },
      },
    },
  },
  { $set: { 'usedBenefits.$[needsUpdate].config.preserveUsage': false } },
  {
    arrayFilters: [
      {
        'needsUpdate.config.maximumUsageType': 'limited',
        'needsUpdate.config.preserveUsage': { $exists: false },
      },
    ],
  },
);
