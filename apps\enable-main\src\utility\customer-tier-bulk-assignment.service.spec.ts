import { Test, TestingModule } from '@nestjs/testing';
import { Types } from 'mongoose';
import { CustomerRepositoryInterface } from '../customer/modules/customer-repository/customer.repository.interface';
import { CustomerTierServiceInterface } from '../customer/modules/customer-tier/customer-tier.service.interface';
import { LoyaltyTierReadServiceInterface } from '../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.service.interface';
import { CustomerTierBulkAssignmentService } from './customer-tier-bulk-assignment.service';

describe('CustomerTierBulkAssignmentService', () => {
  let service: CustomerTierBulkAssignmentService;
  let customerRepository: jest.Mocked<CustomerRepositoryInterface>;
  let customerTierService: jest.Mocked<CustomerTierServiceInterface>;
  let loyaltyTierReadService: jest.Mocked<LoyaltyTierReadServiceInterface>;

  beforeEach(async () => {
    const mockCustomerRepository = {
      findByPhoneAndCompanyId: jest.fn(),
    };

    const mockCustomerTierService = {
      bulkTierAssignment: jest.fn(),
    };

    const mockLoyaltyTierReadService = {
      findByCompanyId: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CustomerTierBulkAssignmentService,
        {
          provide: CustomerRepositoryInterface,
          useValue: mockCustomerRepository,
        },
        {
          provide: CustomerTierServiceInterface,
          useValue: mockCustomerTierService,
        },
        {
          provide: LoyaltyTierReadServiceInterface,
          useValue: mockLoyaltyTierReadService,
        },
      ],
    }).compile();

    service = module.get<CustomerTierBulkAssignmentService>(
      CustomerTierBulkAssignmentService,
    );
    customerRepository = module.get(CustomerRepositoryInterface);
    customerTierService = module.get(CustomerTierServiceInterface);
    loyaltyTierReadService = module.get(LoyaltyTierReadServiceInterface);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('processCustomerTierBulkAssignment', () => {
    it('should process Excel file and assign tiers to customers', async () => {
      const companyId = new Types.ObjectId();
      const customerId = new Types.ObjectId();
      const tierId = new Types.ObjectId();

      // Mock data
      const mockTiers = [
        {
          _id: tierId,
          nameEn: 'Gold Tier',
          nameAr: 'المستوى الذهبي',
        },
      ];

      const mockCustomer = {
        _id: customerId,
        phone: '+97412345678',
        company: companyId,
      };

      // Setup mocks
      loyaltyTierReadService.findByCompanyId.mockResolvedValue(mockTiers as any);
      customerRepository.findByPhoneAndCompanyId.mockResolvedValue(mockCustomer as any);
      customerTierService.bulkTierAssignment.mockResolvedValue({
        success: 1,
        failed: 0,
        unchanged: 0,
      });

      // Mock Excel file reading (this would need to be mocked properly in a real test)
      jest.spyOn(service as any, 'readExcelFile').mockReturnValue([
        {
          'Phone Number': '+97412345678',
          'Loyalty Tier Name': 'Gold Tier',
        },
      ]);

      const result = await service.processCustomerTierBulkAssignment(
        companyId,
        'test-file.xlsx',
      );

      expect(result).toEqual({
        success: 1,
        failed: 0,
        unchanged: 0,
      });

      expect(loyaltyTierReadService.findByCompanyId).toHaveBeenCalledWith(companyId);
      expect(customerRepository.findByPhoneAndCompanyId).toHaveBeenCalledWith(
        '+97412345678',
        companyId,
      );
      expect(customerTierService.bulkTierAssignment).toHaveBeenCalledWith({
        tierId,
        customerIds: [customerId],
        notifyCustomers: false,
      });
    });

    it('should handle errors gracefully', async () => {
      const companyId = new Types.ObjectId();

      // Mock error in reading Excel file
      jest.spyOn(service as any, 'readExcelFile').mockImplementation(() => {
        throw new Error('File not found');
      });

      await expect(
        service.processCustomerTierBulkAssignment(companyId, 'invalid-file.xlsx'),
      ).rejects.toThrow('File not found');
    });
  });
});
