import { CompanyService } from './../../../../company/services/company/company.service';
import { PaymentHelpersService } from './../../payment-helpers/payment-helpers.service';
import {
  CollectionName,
  OrderDocument,
  PaymentDocument,
  PaymentGatewayType,
  PaymentStatusEnum,
} from '@app/shared-stuff';
import { CyberSourcePaymentToProcess } from './../../../dto/payment-cyber-source.dto';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createHmac } from 'crypto';
import * as moment from 'moment-timezone';
import * as uuidApiKey from 'uuid-apikey';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { PaymentRepositoryInterface } from '../../../repositories/interfaces/payment.repository.interface';

@Injectable()
export class PaymentCyberSourceService {
  secureAcceptanceConfig = {} as any;
  constructor(
    private configService: ConfigService,
    private paymentHelperService: PaymentHelpersService,
    private companyService: CompanyService,
    @InjectModel(CollectionName.ORDER)
    private paymentOrderModel: Model<OrderDocument>,
    @Inject('PaymentRepositoryInterface')
    private paymentRepository: PaymentRepositoryInterface,
  ) {
    this.secureAcceptanceConfig['BASE_URL'] = this.configService.get(
      'SECURE_ACCEPTANCE_BASE_URL',
    );
    this.secureAcceptanceConfig['ACCESS_KEY'] = this.configService.get(
      'SECURE_ACCEPTANCE_ACCESS_KEY',
    );
    this.secureAcceptanceConfig['PROFILE_ID'] = this.configService.get(
      'SECURE_ACCEPTANCE_PROFILE_ID',
    );
    this.secureAcceptanceConfig['SINGED_FILED'] = this.configService.get(
      'SECURE_ACCEPTANCE_SINGED_FILED',
    );
    this.secureAcceptanceConfig['LOCALE'] = this.configService.get(
      'SECURE_ACCEPTANCE_LOCALE',
    );
    this.secureAcceptanceConfig['SECRET_KEY'] = this.configService.get(
      'SECURE_ACCEPTANCE_SECRET_KEY',
    );
  }

  async processPayment(payment: PaymentDocument) {
    const todayDate = moment().utc().format('YYYY-MM-DDTHH:mm:ss');
    const REF_NUMBER = Math.floor(100000000 + Math.random() * 900000000);
    const uuid = (uuidApiKey as any).create().uuid;

    payment.gatewayType = PaymentGatewayType.CYBER_SOURCE;
    payment.externalId = uuid;

    await payment.save();

    return await this.onProcessPayment({
      access_key: this.secureAcceptanceConfig['ACCESS_KEY'],
      profile_id: this.secureAcceptanceConfig['PROFILE_ID'],
      transaction_uuid: uuid,
      signed_field_names: this.secureAcceptanceConfig['SINGED_FILED'],
      unsigned_field_names: '',
      signed_date_time: todayDate + 'Z',
      locale: 'en',
      transaction_type: 'sale',
      reference_number: REF_NUMBER,
      amount: payment.amount,
      currency: 'QAR',
    });
  }

  async onProcessPayment(
    cyberSourcePaymentToProcess: CyberSourcePaymentToProcess,
  ) {
    cyberSourcePaymentToProcess['signature'] = await this.hashTheValues(
      cyberSourcePaymentToProcess,
      this.secureAcceptanceConfig['SECRET_KEY'],
    );

    return cyberSourcePaymentToProcess;
  }

  private async hashTheValues(
    cyberSourceToProcess: CyberSourcePaymentToProcess,
    secretKey: string,
  ) {
    const hash = createHmac('sha256', secretKey);

    let commaSeparatedFiled = '';

    // Start Creating the Values of the Hashing
    for (const key in cyberSourceToProcess) {
      commaSeparatedFiled +=
        key.toString() + '=' + cyberSourceToProcess[key].toString() + ',';
    }

    commaSeparatedFiled = commaSeparatedFiled.slice(0, -1);

    // start Hashing The values
    hash.update(commaSeparatedFiled);
    // Return the Hash value
    return hash.digest('base64');
  }

  // performTheCyberSourceRequest git revert --no-commit e7f498cd2a23ba2299e1cfde579286d4ea03b485

  async afterPaymentDone(data: any) {
    // Getting The Payment Details using the transaction UUID
    const payment = await this.paymentRepository.findByExternalId(
      data['req_transaction_uuid'],
    );
    if (!payment) return;

    // Fetching the company details
    const company = await this.companyService.get_details(payment.company);

    // Getting The Order Details
    let order: OrderDocument;
    if (payment.order_code) {
      order = await this.paymentOrderModel.findOne({
        code: payment.order_code,
      });
    }

    // Handle THe Payment Default Data like the transction date and payment tries
    await this.paymentHelperService.saveTransaction(
      payment,
      data['transaction_id'],
      data,
      PaymentGatewayType.CYBER_SOURCE,
    );

    // Handle The Payment Status Changes and The Success and Un Success logic
    const paymentStatus =
      data['reason_code'] == '100'
        ? PaymentStatusEnum.TRANSACTION_COMPLETED
        : PaymentStatusEnum.UNSUCCESSFUL;
    await this.paymentHelperService.handlePaymentStatusLogic(
      payment,
      paymentStatus,
      order,
      company,
    );

    // Generate the CallbackURL
    const callbackURL = this.paymentHelperService.generateCallBackUrl(
      payment,
      company,
      data['reason_code'],
      order,
    );

    return callbackURL;
  }
}
