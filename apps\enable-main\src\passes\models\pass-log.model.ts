import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type PassLogDocument = HydratedDocument<PassLog>;

@Schema({ timestamps: true })
export class PassLog {
  @Prop({
    type: String,
    required: false,
  })
  requestUrl?: string;

  @Prop({
    type: {},
    required: false,
  })
  requestBody?: Record<string, unknown>;

  @Prop({
    type: {},
    required: false,
  })
  requestHeaders?: Record<string, unknown>;

  @Prop({
    type: {},
    required: false,
  })
  responseBody?: Record<string, unknown>;

  @Prop({
    type: {},
    required: false,
  })
  responseHeaders?: Record<string, unknown>;

  @Prop({
    type: {},
    required: false,
  })
  responseError?: Record<string, unknown>;
}

export const PassLogSchema = SchemaFactory.createForClass(PassLog);
