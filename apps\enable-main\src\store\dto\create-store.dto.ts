import { ApiProperty } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { IsNotEmpty, ValidateNested } from 'class-validator';
import { StoreProvider } from '../enumerations/store-provider.enum';
import { Field, ShopifyConfig } from '../../../../../libs/shared-stuff/src';

export class CreateStoreDto {
  @ApiProperty({
    type: String,
    required: false,
  })
  link: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  apiKey: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  apiBaseUrl: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    type: String,
    enum: Object.keys(StoreProvider),
    required: true,
  })
  @IsNotEmpty()
  provider: StoreProvider;

  @ApiProperty({
    type: Types.ObjectId,
    required: true,
  })
  @IsNotEmpty()
  companyId: Types.ObjectId;

  @ApiProperty({
    type: [Types.ObjectId],
    required: true,
  })
  @IsNotEmpty()
  brandIds: Types.ObjectId[];

  @Field({
    type: ShopifyConfig,
    required: (dto) => dto.provider === StoreProvider.SHOPIFY,
  })
  shopifyConfig?: ShopifyConfig;
}
