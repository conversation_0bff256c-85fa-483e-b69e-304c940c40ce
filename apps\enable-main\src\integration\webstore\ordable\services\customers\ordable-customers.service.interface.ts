import { CustomerDocument, CustomerOrdableInfo } from '@app/shared-stuff';
import { Types } from 'mongoose';
import { StoreDocument } from '../../../../../store/models/store.model';

export interface OrdableCustomersServiceInterface {
  initCustomers(store: StoreDocument): Promise<CustomerDocument[]>;
  syncLoyaltyPoints(customer: CustomerDocument): Promise<CustomerDocument>;
  regenerateOrdableLink(customer: CustomerDocument): Promise<CustomerDocument>;
  shortenOrdableLink(
    store: StoreDocument,
    ordableInfo: CustomerOrdableInfo,
    companyId: Types.ObjectId,
    customerCode: string,
  ): Promise<CustomerOrdableInfo>;
}

export const OrdableCustomersServiceInterface = Symbol(
  'OrdableCustomersServiceInterface',
);
