// implemented this script while implementing https://e-butler.atlassian.net/browse/EBL-6252
// However we could use it whenever requested adding new privilege
const newPrivilege = {
  module: "orders",
  label: "Created By Me",
  value: "Created By Me",
  parentKey: ObjectId("5fcdde01f958911b6d4e5ce0")
};

const result = db.privileges.updateOne(
  {
    module: newPrivilege.module,
    label: newPrivilege.label,
    value: newPrivilege.value,
    parentKey: newPrivilege.parentKey
  },
  {
    $setOnInsert: {
      ...newPrivilege,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  },
  { upsert: true }
);


if (result.upsertedCount) {
  print(`✅ Inserted new privilege.`);
} else {
  print("ℹ️ Privilege already exists; no changes made.");
}