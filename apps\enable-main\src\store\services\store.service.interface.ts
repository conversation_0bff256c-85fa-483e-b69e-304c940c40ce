import { Types } from 'mongoose';
import { CreateStoreDto } from '../dto/create-store.dto';
import { IndexStoreDto } from '../dto/index-store.dto';
import { UpdateStoreDto } from '../dto/update-store.dto';
import { StoreDocument } from '../models/store.model';

export interface StoreServiceInterface {
  index(indexStoreDto: IndexStoreDto): Promise<any[]>;
  update(updateStoreDto: UpdateStoreDto): Promise<StoreDocument>;
  create(createStoreDto: CreateStoreDto): Promise<StoreDocument>;
  findById(id: Types.ObjectId): Promise<StoreDocument>;
  delete(id: Types.ObjectId): Promise<void>;
  findByBrand(brandId: Types.ObjectId): Promise<StoreDocument[]>;
  findByCompanyId(companyId: Types.ObjectId): Promise<StoreDocument[]>;
}
