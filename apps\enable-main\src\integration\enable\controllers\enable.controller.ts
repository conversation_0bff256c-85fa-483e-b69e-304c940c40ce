import {
  ChangeShipmentStatusDto,
  CountryDialCode,
  CreateShipmentDto,
  GenericExceptionFilter,
  IntegrationOrderCaptureDto,
  IntegrationUpdateTempCustomerDto,
  LoggerService,
  MenuItemToIndex,
  MenuToIndex,
  PostMicrosOrderDto,
  RegisterLoyaltyCustomerIntegrationDto,
  responseCode,
  ShipmentDocument,
  ShipmentStatus,
  ShopifyOrder,
  ShopifyProduct,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Get,
  Headers,
  HttpCode,
  Param,
  Patch,
  Post,
  Query,
  RawBodyRequest,
  Req,
  SetMetadata,
  UseFilters,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';

import { Request } from 'express';
import { Types } from 'mongoose';
import { OrderToCancel } from '../../../order/dto/order.dto';
import { GetCustomerIntegrationDetailsDto } from '../../../shared/dto/get-customer-integration-details.dto';
import { CurrentUserService } from '../../../shared/services/current-user/current-user.service';
import { IntegrationLogInterceptor } from '../../integration-log/interceptors/integration-log.interceptor';
import { ShopifyService } from '../../webstore/shopify/services/shopify.service';
import { OrderPaymentToCreate } from '../dto/order-payment-to-create.dto';
import { UpdateIntegrationOrderPaymentDto } from '../dto/update-integration-order-payment.dto';
import { ExternalOrderGuard } from '../guards/external-order.guard';
import { EnableService } from '../services/enable.service';

@Controller('integration')
@UseInterceptors(IntegrationLogInterceptor, TransformInterceptor)
@UseFilters(GenericExceptionFilter)
@SetMetadata('module', 'integration')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@ApiTags('Integration')
export class EnableController {
  private readonly loggerService = new LoggerService(EnableController.name);

  constructor(
    private readonly enableService: EnableService,
    private readonly currentUserService: CurrentUserService,
    private readonly shopifyService: ShopifyService,
  ) {}

  @Get('customer/details/:countryCode/:phoneNumber')
  @SetMetadata('action', 'get-customer-details')
  async getCustomerDetails(
    @Param('countryCode') countryCode: CountryDialCode,
    @Param('phoneNumber') phoneNumber: string,
    @Req() req: Request,
  ) {
    return await this.enableService.getCustomerDetails({
      uniqueIdentifier: phoneNumber,
      countryCode: countryCode,
      companyId: req['company_id'],
    });
  }

  @Get('customer/details')
  @SetMetadata('action', 'lookup-customer-details')
  async getCustomerByUniqueIdentifier(
    @Query() { uniqueIdentifier }: GetCustomerIntegrationDetailsDto,
    @Req() req: Request,
  ) {
    return await this.enableService.getCustomerDetails({
      uniqueIdentifier: uniqueIdentifier,
      companyId: req['company_id'],
    });
  }

  @Get('menu')
  @SetMetadata('action', 'get-menu')
  async getMenu(@Query() menuToIndex: MenuToIndex, @Req() req: Request) {
    menuToIndex.company = req['company_id']
      ? req['company_id']
      : menuToIndex.company;
    return await this.enableService.getMenus(menuToIndex);
  }

  @Get('menu-items')
  @SetMetadata('action', 'get-menu')
  async getMenuItems(@Query() menuItemToIndex: MenuItemToIndex) {
    const result = await this.enableService.getMenuItems(menuItemToIndex);
    return result[0]['paginatedResult'];
  }

  @Get('order/details/:uniqueIdentifier')
  @SetMetadata('action', 'get-order-details')
  @ApiQuery({
    name: 'uniqueIdentifier',
    type: String,
    description: 'Enable Order ID or Enable code',
  })
  async getOrderDetails(@Param('uniqueIdentifier') uniqueIdentifier: string) {
    return await this.enableService.getOrderDetails(
      uniqueIdentifier,
      this.currentUserService.getCurrentCompanyId(),
    );
  }

  @Post('order_payment')
  @SetMetadata('action', 'create_order_payment')
  @HttpCode(responseCode.SUCCESS_TO_CREATE)
  @UseGuards(ExternalOrderGuard)
  async createOrderPayment(
    @Body() orderPaymentToCreate: OrderPaymentToCreate,
    @Req() req: Request,
  ) {
    orderPaymentToCreate.createdBy = req['current'];
    orderPaymentToCreate.company = req['company_id'] || '';

    // Setting the brand id with the id of the Api Key
    if (orderPaymentToCreate.orderData) {
      orderPaymentToCreate.orderData.brandId = !orderPaymentToCreate.orderData
        .brandId
        ? req['brand']
          ? req['brand']['_id']
          : undefined
        : orderPaymentToCreate.orderData.brandId;
    }

    if (orderPaymentToCreate.paymentData) {
      orderPaymentToCreate.paymentData.brandId = !orderPaymentToCreate
        .paymentData.brandId
        ? req['brand']
          ? req['brand']['_id']
          : undefined
        : orderPaymentToCreate.paymentData.brandId;
    }

    return await this.enableService.orderPaymentIntegration(
      orderPaymentToCreate,
    );
  }

  @Patch('order_payment')
  @SetMetadata('action', 'update_order_payment')
  @HttpCode(responseCode.SUCCESS_TO_CREATE)
  async updateOrderPayment(
    @Body() updateIntegrationOrderPaymentDto: UpdateIntegrationOrderPaymentDto,
  ): Promise<void> {
    return await this.enableService.updateIntegrationOrderPayment(
      updateIntegrationOrderPaymentDto,
    );
  }

  @Post('3pl/shipment')
  @SetMetadata('action', 'post-shipment')
  @HttpCode(responseCode.SUCCESS_TO_CREATE)
  async postShipment(
    @Body() createShipmentDto: CreateShipmentDto,
    @Req() req: Request,
  ): Promise<ShipmentDocument> {
    createShipmentDto.companyId = req['company_id'];
    createShipmentDto.eblBrandId = req['brand']
      ? req['brand']._id
      : createShipmentDto.eblBrandId;
    return await this.enableService.createShipment(createShipmentDto);
  }

  @Get('3pl/shipment/tracking-number/:trackingNumber')
  @SetMetadata('action', 'post-shipment')
  @HttpCode(responseCode.SUCCESS_TO_CREATE)
  async getShipmentByTrackingNumber(
    @Param('trackingNumber') trackingNumber: string,
  ): Promise<ShipmentDocument> {
    return await this.enableService.getShipmentByTrackingNumber(trackingNumber);
  }

  @Post('3pl/shipment/packed')
  @SetMetadata('action', 'change-shipment-status-to-packed')
  async changeShipmentStatusToPacked(
    @Body() { shipmentRef }: ChangeShipmentStatusDto,
  ) {
    await this.enableService.changeShipmentStatus(
      shipmentRef,
      ShipmentStatus.PACKED,
    );
  }

  @Post('3pl/shipment/canceled')
  @SetMetadata('action', 'change-shipment-status-to-canceled')
  async changeShipmentStatusToCanceled(
    @Body() { shipmentRef }: ChangeShipmentStatusDto,
  ) {
    await this.enableService.changeShipmentStatus(
      shipmentRef,
      ShipmentStatus.CANCELED,
    );
  }

  @Patch('3pl/customer')
  @SetMetadata('action', 'update-temp-customer')
  async updateTempCustomer(
    @Body() integrationUpdateTempCustomerDto: IntegrationUpdateTempCustomerDto,
  ) {
    return await this.enableService.updateTempCustomer(
      integrationUpdateTempCustomerDto,
    );
  }

  @Get('branches')
  @SetMetadata('action', 'get-loyalty-branches')
  async getLoyaltyBranches(@Req() req: Request) {
    return await this.enableService.getBranches(
      req['company_id'],
      req['branches'],
      req['brand'] ? req['brand']['_id'] : undefined,
    );
  }

  @Get('loyalty/config')
  @SetMetadata('action', 'get-loyalty-config')
  async getLoyaltyConfig(@Req() req: Request) {
    return await this.enableService.getLoyaltyConfiguration(
      req['company_id'],
      req['brand'] ? req['brand']['_id'] : undefined,
    );
  }

  @Post('loyalty/register')
  @SetMetadata('action', 'register-loyalty')
  async registerLoyalty(
    @Body()
    registerLoyaltyCustomerIntegrationDto: RegisterLoyaltyCustomerIntegrationDto,
    @Req() req: Request,
  ) {
    return await this.enableService.loyaltyRegister(
      registerLoyaltyCustomerIntegrationDto,
      req['brand']['_id'],
    );
  }

  @Post('order/capture')
  @SetMetadata('action', 'order-capture')
  async captureOrder(
    @Body()
    integrationOrderCaptureDto: IntegrationOrderCaptureDto,
    @Req() req: Request,
  ) {
    return await this.enableService.captureOrder(
      integrationOrderCaptureDto,
      req['company_id'],
      req['brand']?.['_id'],
      req['current'],
    );
  }

  @Post('micros/check-sync')
  @SetMetadata('action', 'micros-check-sync')
  async syncMicrosCheck(
    @Body()
    postMicrosOrderDto: PostMicrosOrderDto,
    @Req() req: Request,
  ) {
    return await this.enableService.syncMicrosCheck(
      postMicrosOrderDto,
      req['company_id'],
      req['brand']?._id,
      req['current'],
    );
  }

  @Post('order/cancel')
  @SetMetadata('action', 'cancel-order')
  async cancelOrder(@Body() orderToCancel: OrderToCancel, @Req() req: Request) {
    orderToCancel.currentUser = req['current'];

    await this.enableService.cancelOrder(orderToCancel);
  }

  @Post('store/:storeId/shopify/order/create')
  @SetMetadata('public', 'true')
  async createShopifyOrder(
    @Param('storeId') storeId: string,
    @Headers('X-Shopify-Hmac-SHA256') hash: string,
    @Headers('X-Shopify-Event-Id') requestId: string,
    @Req() req: RawBodyRequest<ShopifyOrder>,
    @Body() createShopifyOrderDto: ShopifyOrder,
  ) {
    const validationIssue = await this.shopifyService.validateWebhook(
      new Types.ObjectId(storeId),
      hash,
      req.rawBody,
      requestId,
    );
    if (validationIssue) return validationIssue;

    return await this.enableService.createShopifyOrder(
      new Types.ObjectId(storeId),
      createShopifyOrderDto,
    );
  }

  @Post('store/:storeId/shopify/order/capture')
  @SetMetadata('public', 'true')
  async captureShopifyOrder(
    @Param('storeId') storeId: string,
    @Headers('X-Shopify-Hmac-SHA256') hash: string,
    @Headers('X-Shopify-Event-Id') requestId: string,
    @Req() req: RawBodyRequest<ShopifyOrder>,
    @Body() createShopifyOrderDto: ShopifyOrder,
  ) {
    const validationIssue = await this.shopifyService.validateWebhook(
      new Types.ObjectId(storeId),
      hash,
      req.rawBody,
      requestId,
    );
    if (validationIssue) return validationIssue;

    return await this.enableService.captureShopifyOrder(
      new Types.ObjectId(storeId),
      createShopifyOrderDto,
    );
  }

  @Post([
    'store/:storeId/shopify/menu-item/create',
    'store/:storeId/shopify/menu-item/update',
  ])
  @SetMetadata('public', 'true')
  async createShopifyMenuItem(
    @Param('storeId') storeId: string,
    @Headers('X-Shopify-Hmac-SHA256') hash: string,
    @Headers('X-Shopify-Event-Id') requestId: string,
    @Req() req: RawBodyRequest<ShopifyProduct>,
    @Body() shopifyProduct: ShopifyProduct,
  ) {
    const validationIssue = await this.shopifyService.validateWebhook(
      new Types.ObjectId(storeId),
      hash,
      req.rawBody,
      requestId,
    );
    if (validationIssue) return validationIssue;

    return await this.enableService.createOrUpdateShopifyProduct(
      new Types.ObjectId(storeId),
      shopifyProduct,
    );
  }
}
