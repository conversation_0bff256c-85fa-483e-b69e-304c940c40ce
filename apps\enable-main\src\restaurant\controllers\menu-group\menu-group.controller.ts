import { MenuGroupToIndex, responseCode } from '@app/shared-stuff';
import { Controller, Get, Query, Req, Res, SetMetadata } from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { HelperService } from './../../../shared/services/helper/helper.service';
import { MenuGroupService } from './../../services/menu-group/menu-group.service';

@Controller('menu-group')
@ApiTags('Restaurant Menu Groups')
@SetMetadata('module', 'menu-group')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class MenuGroupController {
  constructor(
    private helperService: HelperService,
    private menuGroupService: MenuGroupService,
  ) {}

  @Get()
  @SetMetadata('action', 'get_all')
  async index(
    @Query() menuGroupToIndex: MenuGroupToIndex,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      const groups = await this.menuGroupService.index(menuGroupToIndex);
      const totalNumberOfGroups =
        await this.menuGroupService.getTotalNumberOfGroups(menuGroupToIndex);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to get all Menus categories',
        { menuGroups: groups, totalMenuGroups: totalNumberOfGroups },
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
}
