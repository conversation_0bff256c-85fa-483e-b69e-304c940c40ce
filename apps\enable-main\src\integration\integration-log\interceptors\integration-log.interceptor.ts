import { LoggerService, Response } from '@app/shared-stuff';
import {
  CallHandler,
  ExecutionContext,
  Inject,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { ServerResponse } from 'http';
import { Document } from 'mongoose';
import { Observable, tap } from 'rxjs';
import { IntegrationLogRepositoryInterface } from '../repositories/interfaces/integration-log.repository.interface';

// Assumes that:
//  1. Standard responses are used, not library-specific (No @Res in Controller)
//  2. TransformInterceptor has already transformed the data into a Response object.
//  3. The data in the Response object is a Document, and thus has an _id property.
@Injectable()
export class IntegrationLogInterceptor implements NestInterceptor {
  private readonly logger = new LoggerService(IntegrationLogInterceptor.name);

  constructor(
    @Inject('IntegrationLogRepositoryInterface')
    private integrationLogRepository: IntegrationLogRepositoryInterface,
    private reflector: Reflector,
  ) {}

  intercept(context: ExecutionContext, next: <PERSON><PERSON><PERSON><PERSON>): Observable<any> {
    const action = this.reflector.get<string>('action', context.getHandler());

    return next.handle().pipe(
      tap({
        next: async (responseData: Response<Document> | ServerResponse) => {
          // Controller sent response directly instead of passing through the
          // TransformInterceptor. Once a response has been sent its json cannot
          // be accessed. In this case the controller is responsible for
          // creating an integration log.
          if (responseData instanceof ServerResponse) return;

          const request = context.switchToHttp().getRequest<Request>();

          await this.integrationLogRepository.logSuccess(
            action ?? context.getHandler().name,
            request.body,
            responseData,
            responseData?.data && responseData?.data._id,
            request.headers,
          );
        },
        error: async (err: Error) => {
          const request = context.switchToHttp().getRequest<Request>();

          await this.integrationLogRepository.logError(
            action ?? context.getHandler().name,
            request.body ?? {},
            // We do not have access to response body here since it is
            // constructed by the exception filters, and exception filters are
            // executed later than interceptors
            undefined,
            {
              name: err.name,
              message: err.message,
              stackTrace: err.stack,
            },
            request.headers,
          );
        },
      }),
    );
  }
}
