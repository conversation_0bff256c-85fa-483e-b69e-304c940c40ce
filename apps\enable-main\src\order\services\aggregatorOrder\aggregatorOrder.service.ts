import {
  Aggregator<PERSON>rder<PERSON>ust<PERSON>,
  aggregatorOrderSources,
  AggregatorOrderToCreate,
  areObjectIdsEqual,
  AutoRegisterMode,
  BrandDocument,
  CapturedOrderSource,
  CollectionName,
  CustomerDocument,
  CustomerEarnedBenefit,
  Discount,
  Language,
  LookupCustomerBenefitDto,
  LoyaltyStatus,
  LRPSource,
  mapAsync,
  OrderDeliveryAction,
  OrderDocument,
  OrderLogActionEnum,
  OrderPaymentMethod,
  OrderPaymentStatus,
  OrderSource,
  OrderStatusEnum,
} from '@app/shared-stuff';
import { CompanyDocument } from '@app/shared-stuff/models/company.model';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import { BranchService } from '../../../branch/services/branch/branch.service';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { CompanyService } from '../../../company/services/company/company.service';
import { CustomerLoyaltyMemberServiceInterface } from '../../../customer/modules/customer-loyalty-member/customer-loyalty-member.service.interface';
import { CustomerOrderServiceInterface } from '../../../customer/modules/customer-order/customer-order.service.interface';
import { CustomerWriteServiceInterface } from '../../../customer/modules/customer-write/customer-write.service.interface';
import { MenuItemService } from '../../../restaurant/services/menu-item/menu-item.service';
import { OrderInvoiceService } from '../../modules/order-invoice/order-invoice.service';
import { OrderRedemptionService } from '../../modules/order-redemption/order-redemption.service';
import { OrderLogServiceInterface } from '../interfaces/order-log.service.interface';
import { OrderBenefitService } from '../order-benefit/order-benefit.service';
import { OrderNotificationService } from '../order-notification/order-notification.service';
import { FirstPartyTaskState } from './../../dto/order.dto';
import { OrderService } from '../order/order.service';

@Injectable()
export class AggregatorOrderService {
  constructor(
    @InjectModel(CollectionName.ORDER) private orderModel: Model<OrderDocument>,
    private companyService: CompanyService,
    private branchService: BranchService,
    @Inject('BrandServiceInterface')
    private brandService: BrandServiceInterface,
    @Inject(CustomerOrderServiceInterface)
    private customerOrderService: CustomerOrderServiceInterface,
    @Inject(CustomerWriteServiceInterface)
    private customerWriteService: CustomerWriteServiceInterface,
    @Inject('OrderLogServiceInterface')
    private readonly orderLogService: OrderLogServiceInterface,
    private orderNotificationService: OrderNotificationService,
    private readonly orderInvoiceService: OrderInvoiceService,
    private readonly menuItemService: MenuItemService,
    private orderBenefitService: OrderBenefitService,
    @Inject(CustomerLoyaltyMemberServiceInterface)
    private customerLoyaltyMemberService: CustomerLoyaltyMemberServiceInterface,
    private readonly orderRedemptionService: OrderRedemptionService,
    private readonly orderService: OrderService,
  ) {}

  async create(aggregatorOrderToCreate: AggregatorOrderToCreate) {
    const company = await this.companyService.get_details(
      aggregatorOrderToCreate.companyId.toString(),
    );

    const customer = await this.getCustomerDetails(
      aggregatorOrderToCreate.customer,
      company,
      aggregatorOrderToCreate.source,
    );

    const requestedObject = { ...aggregatorOrderToCreate };

    await this.validateOrderDetails(aggregatorOrderToCreate, company, customer);

    const order = await this.initOrderDetails(
      aggregatorOrderToCreate,
      company,
      customer,
    );

    order.loyaltyProgress =
      await this.customerOrderService.computeLoyaltyProgress(
        company,
        order,
        customer,
      );

    order.companyName = company.name;

    await order.save();

    await this.applyOrderCapturePostFunctions(order, customer, requestedObject);

    return order;
  }

  private async saveOrderLog(
    order: OrderDocument,
    requestedObject: AggregatorOrderToCreate,
  ) {
    await this.orderLogService.saveOrderLog(
      order,
      { requestedObject },
      { responseObject: order },
      OrderLogActionEnum.ON_ORDER_MANUALLY_CAPTURED,
      order.createdBy,
    );
  }

  private async validateOrderDetails(
    aggregatorOrderToCreate: AggregatorOrderToCreate,
    company: CompanyDocument,
    customer: CustomerDocument,
  ) {
    if (
      company.loyaltyProgramConfig?.isInvoiceNumberMandatory &&
      !aggregatorOrderToCreate.invoice_number
    )
      throw new BadRequestException(
        "You can't capture an order without an invoice number.",
      );

    if (
      company.loyaltyProgramConfig?.canNonMembersOrderCapture === false &&
      customer.loyaltyStatus !== LoyaltyStatus.MEMBER
    )
      throw new BadRequestException(
        "You can't capture an order for non loyalty members, please let them register in loyalty first",
      );

    this.orderBenefitService.validateBenefits(
      aggregatorOrderToCreate.benefits,
      customer,
    );

    aggregatorOrderToCreate.discounts =
      await this.orderRedemptionService.validateLoyaltyRedemptions(
        company,
        aggregatorOrderToCreate,
        customer,
      );
  }

  private async initOrderDetails(
    aggregatorOrderToCreate: AggregatorOrderToCreate,
    company: CompanyDocument,
    customer: CustomerDocument,
  ) {
    const { discounts: couponDiscounts, benefits: earnedCouponBenefits } =
      await this.orderService.applyCouponBenefitToOrder(
        aggregatorOrderToCreate.couponId,
        customer,
      );
    aggregatorOrderToCreate = this.mapBenefitsToDiscounts(
      aggregatorOrderToCreate,
      customer,
      couponDiscounts,
    );
    aggregatorOrderToCreate = this.computeInvoiceDetails(
      aggregatorOrderToCreate,
    );

    const brand = await this.brandService.findById(
      aggregatorOrderToCreate.brandId,
    );

    if (
      customer.loyaltyStatus !== LoyaltyStatus.MEMBER &&
      company?.loyaltyProgramConfig?.autoRegisterMode ===
        AutoRegisterMode.GLOBAL
    )
      await this.customerLoyaltyMemberService.registerLoyaltyCustomer(
        customer,
        {
          brandId: aggregatorOrderToCreate.brandId,
          source: LRPSource.AUTOMATIC_LR,
          rawSource: `[${LRPSource.AUTOMATIC_LR.toUpperCase()}] ${aggregatorOrderToCreate?.source}`,
        },
        true,
      );

    const branch = await this.branchService.findById(
      aggregatorOrderToCreate.branchId ?? brand.branches[0]._id,
    );

    const order = await this.createOrderDto(
      aggregatorOrderToCreate,
      branch,
      brand,
      customer,
      company,
    );

    order.benefits = [...(order.benefits ?? []), ...earnedCouponBenefits];
    await order.save();

    return order;
  }

  private mapBenefitsToDiscounts(
    aggregatorOrderToCreate: AggregatorOrderToCreate,
    customer: CustomerDocument,
    couponDiscounts: Discount[],
  ): AggregatorOrderToCreate {
    const { benefits: rawBenefits = [], discounts: existingDiscounts = [] } =
      aggregatorOrderToCreate;

    const customerBenefits = rawBenefits.length
      ? this.lookupCustomerBenefits(rawBenefits, customer)
      : [];

    const customerDiscounts = customerBenefits.map((customerBenefit) =>
      Discount.fromBenefit(customerBenefit),
    );

    return {
      ...aggregatorOrderToCreate,
      discounts: [
        ...existingDiscounts,
        ...couponDiscounts,
        ...customerDiscounts,
      ],
    };
  }

  private lookupCustomerBenefits(
    benefits: LookupCustomerBenefitDto[],
    customer: CustomerDocument,
  ): CustomerEarnedBenefit[] {
    if (!benefits?.length) return [];
    return benefits.map((benefit: LookupCustomerBenefitDto) =>
      customer.earnedBenefits.find(
        (earnedBenefit) =>
          areObjectIdsEqual(benefit.benefitId, earnedBenefit._id) &&
          benefit.source === earnedBenefit.source,
      ),
    );
  }

  private computeInvoiceDetails(
    aggregatorOrderToCreate: AggregatorOrderToCreate,
  ): AggregatorOrderToCreate {
    if (aggregatorOrderToCreate.microsOrderId) return aggregatorOrderToCreate;
    else if (aggregatorOrderToCreate.amount != null)
      return {
        ...aggregatorOrderToCreate,
        ...this.orderInvoiceService.computeFromInvoicedAmount(
          aggregatorOrderToCreate.amount,
          aggregatorOrderToCreate.delivery_amount,
          aggregatorOrderToCreate.discounts,
        ),
      };
    else if (aggregatorOrderToCreate.paidAmount != null)
      return {
        ...aggregatorOrderToCreate,
        ...this.orderInvoiceService.computeFromPaidAmount(
          aggregatorOrderToCreate.paidAmount,
          aggregatorOrderToCreate.delivery_amount,
          aggregatorOrderToCreate.discounts,
        ),
      };
    else
      throw new BadRequestException(
        'Either `amount` or `paidAmount` must be defined.',
      );
  }

  private async applyOrderCapturePostFunctions(
    order: OrderDocument,
    customer: CustomerDocument,
    requestedObject: AggregatorOrderToCreate,
  ) {
    await this.customerOrderService.assignOrderToCustomer(order, customer);
    await this.customerOrderService.completeCustomerOrder(order);
    await this.orderNotificationService.onCapturedOrder(order, customer);
    await this.saveOrderLog(order, requestedObject);
  }

  private async createOrderDto(
    aggregatorOrderToCreate: AggregatorOrderToCreate,
    branch: any,
    brand: BrandDocument,
    customer: CustomerDocument,
    company: CompanyDocument,
  ) {
    const source = aggregatorOrderToCreate.source as unknown as OrderSource;
    const payment_method =
      aggregatorOrderToCreate.payment_method || OrderPaymentMethod.prepaid;
    const date = moment.utc(
      aggregatorOrderToCreate.date || new Date(),
      'YYYY-MM-DD HH:mm',
    );
    return new this.orderModel({
      code: await this.orderCodeConstruct(company),
      order_items: [],
      items: await mapAsync(aggregatorOrderToCreate.items, (item) =>
        this.menuItemService.convertCapturedItem(item),
      ),
      isAcknowledged: true,
      invoice_number: aggregatorOrderToCreate.invoice_number,
      invoiced_amount: aggregatorOrderToCreate.invoiced_amount,
      order_remarks: '',
      total_amount_after_discount:
        aggregatorOrderToCreate.total_amount_after_discount,
      customer_name: customer.full_name,
      customer_phone: customer.phone,
      country_code: customer.country_code,
      customer: customer._id,
      recipient_name: '',
      recipient_country_code: '',
      recipient_phone: '',
      is_gift: false,
      autoAssign: false,
      delivery_action: this.getDeliveryAction(aggregatorOrderToCreate),
      deliveryLocationAction: undefined,
      pickup_date: date.toDate(),
      delivery_date: date.toDate(),
      delivery_time: date.format('HH:mm'),
      barCode: this.generateOrderBarCode(),
      tookan_job_id: undefined,
      tookan_delivery_track_url: undefined,
      deliveryJobId: undefined,
      pickupJobId: undefined,
      tookan_pickup_track_url: undefined,
      assigned_driver_name: 'Manually Capture',
      driver: undefined,
      driverManuallyAssigned: false,
      delivery_amount: 0,
      deliveryMethod: undefined,
      deliveryTaskCreated: false,
      firstPartyState: FirstPartyTaskState.PENDING,
      shortenUrl: undefined,
      payment_method,
      prepaidBy:
        payment_method === OrderPaymentMethod.prepaid
          ? (aggregatorOrderToCreate.prepaidBy ?? 'customer')
          : undefined,
      total_amount: aggregatorOrderToCreate.total_amount,
      total_discount: aggregatorOrderToCreate.total_discount,
      discounts: aggregatorOrderToCreate.discounts,
      benefits: this.lookupCustomerBenefits(
        aggregatorOrderToCreate.benefits,
        customer,
      ),
      is_ready: true,
      source: source,
      creationSource: aggregatorOrderToCreate.creationSource,
      createdBy: aggregatorOrderToCreate.currentUser,
      orderType: 'restaurant',
      couponId: aggregatorOrderToCreate?.couponId,
      company: company._id,
      branch: branch,
      branchName: branch.name,
      branch_object: branch._id,
      brand: {
        _id: brand._id,
        name: brand.name,
        senderId: brand.senderId,
        emailSenderId: brand.emailSenderId,
        countryCode: brand.countryCode,
        phoneNumber: brand.phoneNumber,
        image: brand.image,
      },
      deliveryLocation: undefined,
      pickupLocationId: branch.locationId,
      isAggregator: aggregatorOrderSources.includes(source),
      isManuallyCaptured: true,
      language: customer.language || Language.english,
      status: OrderStatusEnum.COMPLETED,
      payment_status: OrderPaymentStatus.COMPLETED,
      microsOrderId: aggregatorOrderToCreate?.microsOrderId,
      integrationChannel: aggregatorOrderToCreate.integrationChannel,
    });
  }

  private getDeliveryAction({
    delivery_action,
    source,
  }: AggregatorOrderToCreate): OrderDeliveryAction {
    if (delivery_action) return delivery_action;

    const orderSourcetoDeliveryActionMap: Partial<
      Record<OrderSource, OrderDeliveryAction>
    > = {
      [OrderSource.DINE_IN]: OrderDeliveryAction.DINE_IN,
      [OrderSource.WALK_IN]: OrderDeliveryAction.WALK_IN,
      [OrderSource.DRIVE_THRU]: OrderDeliveryAction.DRIVE_THRU,
    };

    if (source in orderSourcetoDeliveryActionMap)
      return orderSourcetoDeliveryActionMap[source];

    return OrderDeliveryAction.IN_STORE_PICKUP;
  }

  private generateOrderBarCode() {
    return Math.floor(1000000000000 + Math.random() * 9000000000000).toString();
  }

  private async orderCodeConstruct(company: CompanyDocument) {
    company = await this.companyService.incrementNumberOfOrders(company._id);
    const code =
      company.acronym +
      '-O-' +
      company.number_of_orders.toString().padStart(5, '0');

    return code;
  }

  private async getCustomerDetails(
    aggregatorCustomer: AggregatorOrderCustomer,
    company: CompanyDocument,
    orderSource: CapturedOrderSource,
  ) {
    const customer = await this.customerWriteService.findOrCreate({
      full_name:
        aggregatorCustomer.firstName + ' ' + aggregatorCustomer.lastName,
      first_name: aggregatorCustomer.firstName,
      last_name: aggregatorCustomer.lastName,
      phone: aggregatorCustomer.phoneNumber,
      country_code: aggregatorCustomer.countryCode,
      gender: aggregatorCustomer.gender,
      email: '',
      language: Language.english,
      company: company._id,
      // CapturedOrderSource enum is subset of OrderSource enum
      contact_channel: orderSource as any,
      firstBranchOrderdId: undefined,
      firstBrandOrderdId: undefined,
    });

    return customer;
  }
}
