import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CustomerPunchCardModule } from '../customer-punch-card/customer-punch-card.module';
import { CustomerRepositoryModule } from '../customer-repository/customer-repository.module';
import { CustomerPassService } from './customer-pass.service';
import { CustomerPassServiceInterface } from './customer-pass.service.interface';

@Module({
  providers: [
    { provide: CustomerPassServiceInterface, useClass: CustomerPassService },
  ],
  imports: [ConfigModule, CustomerPunchCardModule, CustomerRepositoryModule],
  exports: [CustomerPassServiceInterface],
})
export class CustomerPassModule {}
