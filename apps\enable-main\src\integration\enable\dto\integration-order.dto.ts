import {
  Discount,
  Language,
  LookupCustomerBenefitDto,
  ObjectIdTransform,
  OrderCreationSource,
  OrderDeliveryAction,
  OrderDeliveryType,
  OrderItem,
  OrderPaymentMethod,
  OrderSource,
  SavedLocationToCreate,
} from '@app/shared-stuff';
import { CurrentUser } from '@app/shared-stuff/types/general/current-user';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { Types } from 'mongoose';
import * as randomstring from 'randomstring';

export class IntegrationOrder {
  @ApiProperty({
    required: true,
    type: String,
    description: 'Customer First Name',
  })
  first_name: string;

  @ApiProperty({
    required: false,
    type: String,
    default: ' ',
    description: 'Customer Last Name',
  })
  last_name = ' ';

  @ApiProperty({
    required: true,
    type: String,
    description: 'Customer Phone Number',
  })
  @IsNotEmpty()
  phone: string;

  @ApiProperty({
    required: true,
    type: String,
    description: 'Phone Number Country Code',
  })
  country_code: string;

  @ApiProperty({
    type: Boolean,
    default: false,
    required: false,
    description: 'if the order is gift it will be true otherwise false',
  })
  @Type(() => Boolean)
  is_gift?: boolean;

  @ApiProperty({
    type: String,
    required: false,
    description:
      'if is_gift = true this is the gift recipient name (it will be required in this case)',
  })
  recipient_name?: string;

  @ApiProperty({
    type: String,
    required: false,
    description:
      'if is_gift = true this is the gift recipient Phone Number Country Code (it will be required in this case)',
    default: '+974',
  })
  recipient_country_code? = '+974';

  @ApiProperty({
    type: String,
    required: false,
    description:
      'if is_gift = true this is the gift recipient Phone number (it will be required in this case)',
  })
  recipient_phone?: string;

  @ApiProperty({
    required: false,
    type: String,
    default: 'default' + randomstring.generate(5) + '@e-butler.com',
    description: 'Customer email',
  })
  email: string = 'default' + randomstring.generate(5) + '@e-butler.com';

  @ApiProperty({
    required: false,
    type: String,
    format: 'YYYY-DD-MM',
    description: 'the order pickup date => Example : 2020-12-15',
  })
  pickup_date: string;

  @ApiProperty({
    required: false,
    type: String,
    format: 'HH:mm',
    description: 'the order pickup Time => Example : 23:20',
  })
  pickup_time: string;

  @ApiProperty({
    required: true,
    type: String,
    format: 'YYYY-DD-MM',
    description: 'the order delivery date => Example : 2020-12-15',
  })
  delivery_date: string;

  @ApiProperty({
    required: false,
    type: String,
    format: 'HH:mm',
    description:
      'the order delivery date (can be replaced by the slot from and to) => Example : 13:20',
  })
  delivery_time: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'The number of the invoice in your system',
  })
  @Type(() => String)
  invoice_number?: string;

  @ApiProperty({
    required: false,
    type: Number,
    default: 0,
    description:
      'The Invoiced amount of the order, in case there is order items it will be recalculated automatically based on the items price',
  })
  @Type(() => Number)
  invoiced_amount = 0;

  @ApiProperty({
    required: true,
    type: Number,
    default: 0,
    description: 'The value of the delivery',
  })
  @Type(() => Number)
  delivery_amount = 0;

  @ApiProperty({
    required: true,
    type: Number,
    description:
      'is the Summation of the invoiced_amount + delivery_amount (also will be recalculated automatically if not provided)',
  })
  total_amount = 0;

  @ApiProperty({
    required: true,
    type: Number,
    description:
      'is the Summation of the invoiced_amount + delivery_amount (also will be recalculated automatically if not provided)',
  })
  total_amount_after_discount = 0;

  @ApiProperty({
    type: Number,
    required: false,
    default: 0,
    deprecated: true,
    description: '**Deprecated** use `discounts` instead.',
  })
  discount: number;

  @ApiProperty({
    type: Discount,
    isArray: true,
    required: false,
    default: [],
  })
  @IsOptional()
  @Type(() => Discount)
  discounts?: Discount[];

  @ApiProperty({
    type: [LookupCustomerBenefitDto],
    required: false,
  })
  @IsOptional()
  @Type(() => LookupCustomerBenefitDto)
  @ValidateNested({ each: true })
  benefits?: LookupCustomerBenefitDto[];

  @ApiProperty({
    type: String,
    required: false,
    description:
      'ID of the coupon that must be sent together with the `discounts` applied when redeeming the coupon.',
  })
  @ObjectIdTransform({ optional: true })
  couponId?: Types.ObjectId;

  @ApiProperty({
    type: String,
    description: 'Order Extra Notes',
  })
  order_remarks: string;

  @ApiProperty({
    required: true,
    type: String,
    enum: Object.values(OrderPaymentMethod),
    description:
      'if The integrationType Is orderWithPayment So it will convert to online automatically',
  })
  payment_method: string;

  @ApiProperty({
    required: false,
    type: String,
    description: 'If payment_method is prepaid, who was it prepaid by?',
  })
  prepaidBy?: string;

  @ApiProperty({
    required: true,
    type: String,
    enum: OrderSource,
    default: OrderSource.WHATSAPP,
    description:
      'The Order Source it will let you know where this order is came from',
  })
  source: OrderSource = OrderSource.WHATSAPP;

  @ApiProperty({
    type: String,
    enum: Object.values(OrderCreationSource),
    default: OrderCreationSource.ORDER_PAYMENT_INTEGRATION_API,
  })
  @IsOptional()
  @IsEnum(OrderCreationSource)
  creationSource?: OrderCreationSource =
    OrderCreationSource.ORDER_PAYMENT_INTEGRATION_API;

  @ApiProperty({
    required: false,
    type: String,
    enum: Language,
    default: Language.english,
    description:
      'The Order Language it will let you know what language you should support during the order creation',
  })
  language?: Language = Language.english;

  @ApiProperty({
    required: false,
    type: String,
    enum: ['restaurant', 'simple'],
    default: 'simple',
  })
  orderType = 'restaurant';

  @ApiProperty({
    type: Boolean,
    required: false,
    default: false,
  })
  is_secret? = false;

  @ApiProperty({
    type: String,
    required: false,
  })
  cardMessage? = '';

  @ApiProperty({
    type: () => SavedLocationToCreate,
    required: false,
    description:
      'The Address of the Customer can be skipped if the delivery_action is send_sms',
  })
  @Type(() => SavedLocationToCreate)
  deliveryLocation: SavedLocationToCreate;

  @ApiProperty({
    type: 'object',
    additionalProperties: true,
    description:
      'The Address of the Customer can be skipped if the delivery_action is send_sms',
  })
  delivery_address?: any;

  @ApiProperty({
    type: String,
    required: false,
    description: `Detect how the order delivered if delivery_location => you must provide the delivery_address,
          send_sms => will sending a sms to customer with link to provide the delivery address automatically `,
    enum: OrderDeliveryAction,
  })
  delivery_action: OrderDeliveryAction;

  @ApiProperty({
    type: String,
    required: false,
    enum: OrderDeliveryType,
    description: 'detect if the delivery will be urgent or scheduled',
  })
  delivery_type: OrderDeliveryType;

  @ApiProperty({
    type: String,
    required: false,
    format: 'HH:mm',
    description: 'Order Delivery Time Range (From) => Example 15:36',
  })
  delivery_slot_from?: string;

  @ApiProperty({
    type: String,
    required: false,
    format: 'HH:mm',
    description: 'Order Delivery Time Range (To) => Example 15:36',
  })
  delivery_slot_to?: string;

  @ApiProperty({
    type: String,
    required: false,
    format: 'HH:mm',
    description: 'Order Delivery Time Range (To) => Example 15:36',
  })
  @IsOptional()
  timezone?: string;

  @ApiProperty({
    type: () => [OrderItem],
    required: false,
    description: 'Order Items (Products, Menus)',
  })
  @Type(() => OrderItem)
  items: OrderItem[];

  @ApiProperty({
    type: String,
    required: false,
  })
  brandId = '';

  @ApiProperty({
    type: Boolean,
    required: false,
    default: false,
  })
  @Type(() => Boolean)
  is_test?: boolean;

  @ApiProperty({
    type: String,
    required: false,
  })
  paymentCode?: string;

  company: string;
  createdBy: CurrentUser;
  enable_branch_id?: string;

  @ApiProperty({
    type: Number,
    required: false,
  })
  @IsOptional()
  bigcommerceOrderId?: number;
}
