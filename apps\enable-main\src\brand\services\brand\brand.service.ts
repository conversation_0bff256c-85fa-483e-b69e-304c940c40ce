import {
  BranchDocument,
  Brand,
  BrandDocument,
  CompanyDocument,
  CountryDialCode,
  CreateBrandDto,
  EmbeddedBranchDto,
  EmbeddedBrandDto,
  ImageToCreate,
  IndexBrandDto,
  LogError,
  omit,
  PatchBrandConfigDto,
  pick,
  responseCode,
  UpdateBrandDto,
} from '@app/shared-stuff';
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  UnprocessableEntityException,
} from '@nestjs/common';
import { NotFoundException } from '@nestjs/common/exceptions';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { plainToInstance } from 'class-transformer';
import { Types } from 'mongoose';
import randomstring from 'randomstring';
import { CompanyService } from '../../../company/services/company/company.service';
import { LoyaltyTierReadServiceInterface } from '../../../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.service.interface';
import { ImageService } from '../../../shared/services/image/image.service';
import { BrandSortMapping } from '../../constants';
import { BrandRepositoryInterface } from '../../repositories/brand.repository.interface';
import { BrandDetails } from '../../types/brand-details.type';
import { BrandServiceInterface } from './brand.service.interface';

@Injectable()
export class BrandService implements BrandServiceInterface {
  constructor(
    @Inject('BrandRepositoryInterface')
    private readonly brandRepository: BrandRepositoryInterface,
    @Inject(forwardRef(() => CompanyService))
    private readonly companyService: CompanyService,
    @Inject(LoyaltyTierReadServiceInterface)
    private readonly loyaltyTierReadService: LoyaltyTierReadServiceInterface,
    private imageService: ImageService,
    private eventEmitter: EventEmitter2,
  ) {}

  async findByCompanyId(companyId: Types.ObjectId): Promise<BrandDocument[]> {
    const brands = await this.brandRepository.findByCompanyId(companyId);
    if (!brands)
      throw new BadRequestException(
        'There is no brand attached to this company',
      );
    return brands;
  }

  async findByBranchId(branchId: Types.ObjectId): Promise<BrandDocument[]> {
    return await this.brandRepository.findByBranchId(branchId);
  }

  async index(
    indexBrandDto: IndexBrandDto,
    requestBranches: Types.ObjectId[],
  ): Promise<any> {
    const pipeline = [],
      match = { deletedAt: null };
    this.addProjectStage(pipeline);
    await this.addMatchStage(pipeline, match, indexBrandDto, requestBranches);
    this.addSortStage(pipeline, indexBrandDto);
    this.addPaginationStage(pipeline, indexBrandDto);
    return await this.brandRepository.aggregate(pipeline);
  }

  async create(createBrandDto: CreateBrandDto): Promise<BrandDocument> {
    const brand = plainToInstance(Brand, createBrandDto);
    brand.companyId = createBrandDto.companyId;
    if (
      createBrandDto.name &&
      !(await this.isNameUnique(createBrandDto.name, createBrandDto.companyId))
    )
      throw new BadRequestException(
        'Please Provide a Unique Brand Name ',
        responseCode.DUPLICATED_ENTRY.toString(),
      );

    if (!createBrandDto.phoneNumber) {
      throw new BadRequestException(
        'Please Provide a Brand Phone Number ',
        responseCode.MISSING_DATA.toString(),
      );
    }

    if (createBrandDto.image) {
      createBrandDto.image.for = Brand.name;
      brand.image = (await this.imageService.uploadImage(
        createBrandDto.image,
      )) as any;
    }

    await this.companyService.increaseNumberOfBrands(brand.companyId);

    return await this.brandRepository.create(brand);
  }

  async update(updateBrandDto: UpdateBrandDto): Promise<BrandDocument | null> {
    if (updateBrandDto.image && 'base64' in updateBrandDto.image) {
      updateBrandDto.image = await this.updateImage(updateBrandDto);
    }

    const existedBrand = await this.brandRepository.findById(
      updateBrandDto._id,
    );
    if (!existedBrand) throw new NotFoundException('Brand Not Found');
    updateBrandDto.companyId = existedBrand.companyId;

    if (
      existedBrand &&
      existedBrand.name != updateBrandDto.name &&
      !(await this.isNameUnique(updateBrandDto.name, updateBrandDto.companyId))
    ) {
      throw new BadRequestException(
        'Please Provide a Unique Brand Name ',
        responseCode.DUPLICATED_ENTRY.toString(),
      );
    }

    if (!updateBrandDto.phoneNumber || updateBrandDto.phoneNumber == '') {
      throw new BadRequestException(
        'Please Provide a Brand Phone Number ',
        responseCode.MISSING_DATA.toString(),
      );
    }

    const { _id, ...changes } = updateBrandDto;
    const updatedBrand = await this.brandRepository.findOneAndUpdate(
      { _id },
      changes,
    );

    this.eventEmitter.emit('on-Brand-Update', updatedBrand, _id);

    return updatedBrand;
  }

  async findById(id: Types.ObjectId): Promise<BrandDocument> {
    const brand = await this.brandRepository.findById(id);
    if (!brand)
      throw new BadRequestException(
        "Brand Doesn't Exist",
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    return brand;
  }

  async findByIdForPublicPages(id: Types.ObjectId): Promise<BrandDetails> {
    const brand = await this.findById(id);
    const company = await this.companyService.findById(brand.companyId);

    const sanitizedCompany: BrandDetails['company'] = {
      ...company.toObject(),
    };

    if (sanitizedCompany.loyaltyProgramConfig?.baseTierEnrollmentCode) {
      sanitizedCompany.loyaltyProgramConfig['baseTier'] =
        await this.loyaltyTierReadService.findByEnrollmentCode(
          sanitizedCompany.loyaltyProgramConfig.baseTierEnrollmentCode,
        );
      sanitizedCompany.loyaltyProgramConfig = omit(
        sanitizedCompany.loyaltyProgramConfig,
        ['baseTierEnrollmentCode'],
      );
    }

    return {
      ...brand.toObject(),
      company: pick(sanitizedCompany, [
        '_id',
        'name',
        'loyaltyProgramConfig',
        'hasLoyaltyProgram',
        'localization',
      ]),
    };
  }

  async findByIdIn(ids: Types.ObjectId[]): Promise<BrandDocument[]> {
    return await this.brandRepository.findByIdIn(ids);
  }

  async patchConfig({
    brandIds,
    orderAveragePreparationTime,
  }: PatchBrandConfigDto): Promise<void> {
    await this.brandRepository.updateByBrandIds(brandIds, {
      orderAveragePreparationTime,
    });
  }

  async findLoyaltyBrand(
    companyId: Types.ObjectId,
  ): Promise<BrandDocument | null> {
    const brandsWithLoyalty =
      await this.brandRepository.findBrandsWithLoyalty(companyId);

    if (!brandsWithLoyalty || brandsWithLoyalty.length === 0) return null;

    return brandsWithLoyalty[0];
  }

  toEmbeddedBrandDto(brand: BrandDocument): EmbeddedBrandDto {
    return {
      _id: brand._id,
      name: brand.name,
      phoneNumber: brand.phoneNumber,
      senderId: brand.senderId,
      image: brand.image,
      emailSenderId: brand.emailSenderId,
      preferredLanguage: brand.preferredLanguage,
    };
  }

  // Events

  @OnEvent('company.createDefaultBrand')
  @LogError()
  async createDefaultBrand(
    createdCompany: CompanyDocument,
    defaultBranch: BranchDocument,
  ) {
    const createBrandDto: CreateBrandDto = {
      name: createdCompany.name,
      phoneNumber: createdCompany.phone,
      image: null,
      description: '',
      companyId: new Types.ObjectId(createdCompany._id),
      senderId: createdCompany.senderId,
      emailSenderId: createdCompany.email,
      orderAveragePreparationTime: 15,
      countryCode: CountryDialCode.QATAR,
    };
    const brand = plainToInstance(Brand, createBrandDto);
    const defaultBrand = await this.assignDefaultBranchToDefaultBrand(
      defaultBranch,
      brand,
    );
    await this.assignDefaultBrandToDefaultBranch(defaultBrand, defaultBranch);
  }

  // Helper Functions

  async findOneByNameAndCompanyId(
    name: string,
    companyId: Types.ObjectId,
  ): Promise<BrandDocument | null> {
    return await this.brandRepository.findByNameAndCompanyId(name, companyId);
  }

  private addProjectStage(pipeline: any[]) {
    pipeline.push({
      $project: {
        name: 1,
        image: 1,
        description: 1,
        branches: 1,
        companyId: 1,
        phoneNumber: 1,
        senderId: 1,
        whatsappNumber: 1,
        whatsappCountryCode: 1,
        localName: 1,
        preferredLanguage: 1,
        lowerName: {
          $toLower: '$name',
        },
        passPreviewImage: 1,
        loyaltyProgramConfig: 1,
      },
    });
  }

  private async addMatchStage(
    pipeline: any,
    match: any,
    indexBrandDto: IndexBrandDto,
    requestBranches: Types.ObjectId[],
  ) {
    if (indexBrandDto.companyId) {
      await this.matchCompanyId(indexBrandDto.companyId, match);
    }
    if (indexBrandDto.branchId)
      this.matchBranchId(indexBrandDto.branchId, match);
    if (indexBrandDto.branches) {
      const branchIdsList = this.convertToArray(indexBrandDto.branches);
      const branchObjectIds = this.convertToObjectIds(branchIdsList);
      this.matchRequestedBranches(branchObjectIds, match);
    } else if (requestBranches) {
      this.matchBranches(requestBranches, match);
    }
    if (indexBrandDto.search_key) {
      match[indexBrandDto.searchType] = {
        $regex: indexBrandDto.search_key,
        $options: 'i',
      };
    }

    if (indexBrandDto.phoneNumber) {
      const or = [
        {
          phoneNumber: indexBrandDto.phoneNumber,
        },
        {
          branches: {
            $elemMatch: {
              phoneNumber: indexBrandDto.phoneNumber,
            },
          },
        },
      ];
      match['$or'] = match['$or'] ? [...match['$or'], ...or] : or;
    }

    pipeline.push({ $match: match });
  }

  private addSortStage(pipeline: any, indexBrandDto: IndexBrandDto) {
    if (indexBrandDto.sortType) {
      pipeline.push({
        $sort: BrandSortMapping[indexBrandDto.sortType]
          ? BrandSortMapping[indexBrandDto.sortType]
          : { createdAt: -1 },
      });
    } else
      pipeline.push({
        $sort: { createdAt: -1 },
      });
  }

  private addPaginationStage(pipeline: any, indexBrandDto: IndexBrandDto) {
    pipeline.push({
      $facet: {
        paginatedResult: [
          ...(Number(indexBrandDto.offset)
            ? [
                {
                  $skip:
                    Number(indexBrandDto.offset) * Number(indexBrandDto.limit),
                },
              ]
            : [
                {
                  $skip: 0,
                },
              ]),
          ...(Number(indexBrandDto.limit)
            ? [
                {
                  $limit: Number(indexBrandDto.limit),
                },
              ]
            : []),
        ],
        totalCount: [
          {
            $count: 'createdAt',
          },
        ],
      },
    });
  }

  private matchRequestedBranches(
    branchObjectIds: Types.ObjectId[],
    match: any,
  ) {
    match['branches'] = {
      $elemMatch: {
        _id: { $in: branchObjectIds },
      },
    };
  }

  private matchBranches(branchObjectIds: Types.ObjectId[], match: any) {
    match['$or'] = [
      { branches: { $exists: true, $eq: [] } },
      {
        branches: {
          $elemMatch: {
            _id: { $in: branchObjectIds },
          },
        },
      },
    ];
  }

  private async updateImage(updateBrandDto: UpdateBrandDto) {
    const imageToCreate: ImageToCreate = {
      name: updateBrandDto.name || randomstring.generate(10),
      alt: updateBrandDto.image.alt,
      description: updateBrandDto.image.description,
      for: Brand.name,
      base64: updateBrandDto.image.base64,
    };
    return (await this.imageService.uploadImage(imageToCreate)) as any;
  }

  private convertToObjectIds(branchIdsList: string[]): Types.ObjectId[] {
    const branchObjectIds = [];
    for (let i = 0; i < branchIdsList.length; i++) {
      branchObjectIds.push(new Types.ObjectId(branchIdsList[i]));
    }
    return branchObjectIds;
  }

  private convertToArray(branches: string) {
    return branches.split(',');
  }

  private async matchCompanyId(companyId: string, match: any) {
    const company = await this.companyService.get_details(companyId);
    if (!company)
      throw new UnprocessableEntityException(
        'Company not found!',
        responseCode.MISSING_DATA.toString(),
      );
    match['companyId'] = new Types.ObjectId(companyId);
  }

  private matchBranchId(branchId: Types.ObjectId, match: any) {
    match['branches'] = {
      $elemMatch: {
        _id: { $eq: new Types.ObjectId(branchId) },
      },
    };
  }

  private async assignDefaultBranchToDefaultBrand(
    defaultBranch: BranchDocument,
    brand: Brand,
  ) {
    const embeddedBranch: EmbeddedBranchDto = {
      _id: new Types.ObjectId(defaultBranch._id),
      name: defaultBranch.name,
    };
    brand.branches.push(embeddedBranch);
    return await this.brandRepository.create(brand);
  }

  private async assignDefaultBrandToDefaultBranch(
    defaultBrand: BrandDocument,
    defaultBranch: BranchDocument,
  ) {
    const embeddedBrand: EmbeddedBrandDto = {
      _id: new Types.ObjectId(defaultBrand._id),
      name: defaultBrand.name,
      phoneNumber: defaultBrand.phoneNumber,
      senderId: defaultBrand.senderId,
      image: defaultBrand.image,
      emailSenderId: defaultBrand.emailSenderId,
    };
    defaultBranch.brands.push(embeddedBrand);
    this.eventEmitter.emit('brands-updated', defaultBranch);
  }

  private async isNameUnique(
    name: string,
    companyId: Types.ObjectId,
  ): Promise<boolean> {
    if (name && companyId)
      return !(await this.brandRepository.findByNameAndCompanyId(
        name,
        companyId,
      ));
  }

  async updatePwaBaseUrl(
    brandId: Types.ObjectId,
    pwaBaseUrl?: string,
  ): Promise<void> {
    this.brandRepository.updatePwaBaseUrl(brandId, pwaBaseUrl);
  }
}
