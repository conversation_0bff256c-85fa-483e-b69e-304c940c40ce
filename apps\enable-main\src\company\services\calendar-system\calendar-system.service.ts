import {
  CalendarCycle,
  CalendarSystem,
  CompanyDocument,
  CustomerDocument,
  CycleOffset,
  FixedCalendarSystem,
  fixedCalendarSystems,
  RelativeCalendarSystem,
  relativeCalendarSystems,
  UnreachableError,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import * as moment from 'moment-timezone';

@Injectable()
export class CalendarSystemService {
  public getCalendarCycle(
    customer: CustomerDocument,
    company: CompanyDocument,
    cycleOffSet = CycleOffset.CURRENT_CYCLE,
  ): CalendarCycle {
    return {
      startDate: this.getStartDate(company, cycleOffSet),
      endDate: this.getEndDate(customer, company, cycleOffSet),
    };
  }

  public getFixedCalendarCycle(
    calendarSystem: FixedCalendarSystem,
    cycleOffset = CycleOffset.CURRENT_CYCLE,
  ): CalendarCycle {
    return {
      startDate: this.getFixedStartDate(calendarSystem, cycleOffset),
      endDate: this.getFixedEndDate(calendarSystem, cycleOffset),
    };
  }

  public isFixedCalendarSystem(
    calendarSystem: CalendarSystem,
  ): calendarSystem is FixedCalendarSystem {
    return fixedCalendarSystems.includes(calendarSystem as FixedCalendarSystem);
  }

  public isRelativeCalendarSystem(
    calendarSystem: CalendarSystem,
  ): calendarSystem is RelativeCalendarSystem {
    return relativeCalendarSystems.includes(
      calendarSystem as RelativeCalendarSystem,
    );
  }

  public getFixedStartDate(
    calendarSystem: FixedCalendarSystem,
    cycleOffset = CycleOffset.CURRENT_CYCLE,
  ): Date {
    switch (calendarSystem) {
      case CalendarSystem.QUARTERLY:
        return moment
          .utc()
          .add(cycleOffset, 'quarter')
          .startOf('quarter')
          .toDate();
      case CalendarSystem.MONTHLY:
        return moment.utc().add(cycleOffset, 'month').startOf('month').toDate();
      default:
        throw new UnreachableError(calendarSystem);
    }
  }

  public getRelativeStartDate(calendarSystem: RelativeCalendarSystem) {
    switch (calendarSystem) {
      case CalendarSystem.LAST_12_MONTHS:
        return moment.utc().subtract(12, 'months').toDate();
      default:
        throw new UnreachableError(calendarSystem);
    }
  }

  public getStartDate(
    company: CompanyDocument,
    cycleOffSet = CycleOffset.CURRENT_CYCLE,
  ): Date {
    if (!company.hasLoyaltyProgram) return moment.utc('2000-01-01').toDate();

    const calendarSystem = company.loyaltyProgramConfig?.calendarSystem;
    if (this.isFixedCalendarSystem(calendarSystem))
      return this.getFixedStartDate(calendarSystem, cycleOffSet);
    else if (this.isRelativeCalendarSystem(calendarSystem))
      return this.getRelativeStartDate(calendarSystem);
    else throw new UnreachableError(calendarSystem);
  }

  public getGracePeriodStartDate(
    company: CompanyDocument,
    calendarCycleStartDate: Date,
  ): Date {
    const calendarSystem = company.loyaltyProgramConfig.calendarSystem;
    if (this.isRelativeCalendarSystem(calendarSystem)) return null;

    const defaultGracePeriodStartDay =
      company.loyaltyProgramConfig.calendarSystem === CalendarSystem.QUARTERLY
        ? 14
        : 3;
    const gracePeriodStartDay =
      company.loyaltyProgramConfig.gracePeriodStartDay ??
      defaultGracePeriodStartDay;

    return moment
      .utc(calendarCycleStartDate)
      .add(gracePeriodStartDay, 'days')
      .toDate();
  }

  public getFixedEndDate(
    calendarSystem: FixedCalendarSystem,
    cycleOffset = CycleOffset.CURRENT_CYCLE,
  ): Date {
    switch (calendarSystem) {
      case CalendarSystem.QUARTERLY:
        return moment
          .utc()
          .add(cycleOffset, 'quarter')
          .endOf('quarter')
          .toDate();
      case CalendarSystem.MONTHLY:
        return moment.utc().add(cycleOffset, 'month').endOf('month').toDate();
      default:
        throw new UnreachableError(calendarSystem);
    }
  }

  public getRelativeEndDate(
    customer: CustomerDocument,
    calendarSystem: RelativeCalendarSystem,
    cycleOffset = CycleOffset.CURRENT_CYCLE,
  ) {
    switch (calendarSystem) {
      case CalendarSystem.LAST_12_MONTHS:
        return moment
          .utc(customer.tierUpdatedAt)
          .add(12 * (cycleOffset + 1), 'months')
          .toDate();
      default:
        throw new UnreachableError(calendarSystem);
    }
  }

  public getEndDate(
    customer: CustomerDocument,
    company: CompanyDocument,
    cycleOffset = CycleOffset.CURRENT_CYCLE,
  ): Date {
    if (!company.hasLoyaltyProgram) return moment.utc().toDate();

    const calendarSystem = company.loyaltyProgramConfig?.calendarSystem;
    if (this.isFixedCalendarSystem(calendarSystem))
      return this.getFixedEndDate(calendarSystem, cycleOffset);
    else if (this.isRelativeCalendarSystem(calendarSystem))
      return this.getRelativeEndDate(customer, calendarSystem, cycleOffset);
    else throw new UnreachableError(calendarSystem);
  }

  public getGracePeriodEndDate(
    calendarSystem: FixedCalendarSystem,
    cycleOffset = CycleOffset.CURRENT_CYCLE,
  ): Date {
    return this.getFixedEndDate(calendarSystem, cycleOffset);
  }
}
