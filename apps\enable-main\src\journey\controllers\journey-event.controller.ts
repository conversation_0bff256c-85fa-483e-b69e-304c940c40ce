import {
  CreateJourneyEventDto,
  GenericExceptionFilter,
  Journey,
  JourneyDocument,
  JourneyEventIdDto,
  TransformInterceptor,
  UpdateJourneyEventDto,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Inject,
  Param,
  Patch,
  Post,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { JourneyEventServiceInterface } from '../services/journey-event/journey-event.service.interface';

@Controller('journey-event')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags('JourneyEvent')
@SetMetadata('module', 'journey-event')
export class JourneyEventController {
  constructor(
    @Inject(JourneyEventServiceInterface)
    private readonly journeyEventService: JourneyEventServiceInterface,
  ) {}

  @Post()
  @ApiOkResponse({ type: Journey })
  @SetMetadata('action', 'create')
  async create(
    @Body() createJourneyEventDto: CreateJourneyEventDto,
  ): Promise<JourneyDocument> {
    return await this.journeyEventService.create(createJourneyEventDto);
  }

  @Patch(':eventId')
  @ApiOkResponse({ type: Journey })
  @SetMetadata('action', 'update')
  async update(
    @Param() { eventId }: JourneyEventIdDto,
    @Body() updateJourneyEventDto: Omit<UpdateJourneyEventDto, 'eventId'>,
  ): Promise<JourneyDocument> {
    return await this.journeyEventService.update({
      eventId,
      ...updateJourneyEventDto,
    });
  }

  @Delete(':eventId')
  @ApiOkResponse({ type: Journey })
  @SetMetadata('action', 'delete')
  async delete(@Param() { eventId }: JourneyEventIdDto) {
    return await this.journeyEventService.delete(eventId);
  }
}
