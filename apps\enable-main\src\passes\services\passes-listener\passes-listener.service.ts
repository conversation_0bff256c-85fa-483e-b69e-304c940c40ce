import {
  CompanyDocument,
  CouponDocument,
  CustomerDocument,
  LogError,
  LoggerService,
  LoyaltyTierDocument,
  PassConfig,
  PassOwnerType,
  PunchCardDocument,
  RegisteredPass,
  UnreachableError,
  WalletApp,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Types } from 'mongoose';
import { CustomerReadServiceInterface } from '../../../customer/modules/customer-read/customer-read.service.interface';
import { PassesCronService } from '../passes-cron/passes-cron.service';

@Injectable()
export class PassesListenerService {
  private readonly loggerService = new LoggerService(
    PassesListenerService.name,
  );

  private readonly UPDATE_DEBOUNCE_DELAY = 5000;
  private readonly timeouts = new Map<Types.ObjectId, NodeJS.Timeout>();

  constructor(
    @Inject(CustomerReadServiceInterface)
    private readonly customerService: CustomerReadServiceInterface,
    private readonly passesCronService: PassesCronService,
  ) {}

  @OnEvent('passconfig.updated')
  @LogError()
  async refreshPassesForPassConfoig(passConfig: PassConfig) {
    switch (passConfig.owner.type) {
      case PassOwnerType.COMPANY: {
        const customers = await this.customerService.findByCompanyId(
          passConfig.owner._id,
          true,
        );
        await this.updateCustomersPasses(customers);
        break;
      }
      case PassOwnerType.BRAND: {
        const customers = await this.customerService.findByBrandId(
          passConfig.owner._id,
          true,
        );
        await this.updateCustomersPasses(customers);
        break;
      }
      case PassOwnerType.TIER: {
        const customers = await this.customerService.findByLoyaltyTier(
          [passConfig.owner._id],
          undefined,
          true,
        );
        await this.updateCustomersPasses(customers);
        break;
      }
      default:
        throw new UnreachableError(passConfig.owner.type);
    }
  }

  @OnEvent('passes.forceRefresh')
  @LogError()
  async handleForceRefresh(passTypeIdentifier: string) {
    const customers = await this.customerService.findByRegisteredPasses({
      passTypeIdentifier,
    });

    this.loggerService.log(
      `Force refreshing passes of type '${passTypeIdentifier}' for ${customers.length} customers`,
    );

    this.updateCustomersPasses(customers);
  }

  @OnEvent('customer.embeddedTier.updated')
  @OnEvent('loyaltytier.upperTier.changed')
  @LogError()
  async handleTierUpdated(loyaltyTier: LoyaltyTierDocument) {
    const customers = await this.customerService.findByLoyaltyTier(
      [loyaltyTier._id],
      loyaltyTier.companyId,
      true,
    );
    this.updateCustomersPasses(customers);
  }

  @OnEvent('loyaltytier.deleted')
  @LogError()
  async handleTierDeleted(loyaltyTier: LoyaltyTierDocument) {
    const customers = await this.customerService.findByCompanyId(
      loyaltyTier.companyId,
      true,
    );
    this.updateCustomersPasses(customers);
  }

  @OnEvent('punchcard.achievement.created')
  @OnEvent('punchcard.achievement.updated')
  @OnEvent('punchcard.achievement.deleted')
  async handleAchievementUpdated(punchCard: PunchCardDocument) {
    const customers = await this.customerService.findByPunchCard(punchCard._id);
    this.updateCustomersPasses(customers);
  }

  @OnEvent('coupon.created')
  @OnEvent('coupon.deleted')
  async handleCouponListUpdated(coupon: CouponDocument) {
    const customers = await this.customerService.findByCompanyId(
      coupon.companyId,
      true,
    );
    this.updateCustomersPasses(customers);
  }

  @OnEvent('company.passBarcodePayload.updated')
  async handleCompanyPassBarcodePayloadUpdated(company: CompanyDocument) {
    const customers = await this.customerService.findByCompanyId(
      company._id,
      true,
    );
    this.updateCustomersPasses(customers);
  }

  @OnEvent('customer.tier.updated')
  @OnEvent('customer.points.updated')
  @OnEvent('customer.orders.updated')
  @OnEvent('customer.stamps.updated')
  @OnEvent('customer.rewards.updated')
  @OnEvent('customer.messages.updated')
  @OnEvent('customer.loyaltyStatus.registered')
  @OnEvent('customer.firstGracePeriodReminder.sent')
  updateCustomerPasses(customer: CustomerDocument) {
    if (this.timeouts.has(customer._id)) {
      this.timeouts.get(customer._id).refresh();
    } else {
      this.timeouts.set(
        customer._id,
        setTimeout(() => {
          void this.passesCronService.queuePassRefreshes(
            customer.registeredPasses,
          );
        }, this.UPDATE_DEBOUNCE_DELAY),
      );
    }
  }

  private updateCustomersPasses(customers: CustomerDocument[]) {
    const customersWithPasses = customers.filter(
      (customer) => customer.registeredPasses?.length > 0,
    );

    const passes = customersWithPasses.flatMap(
      (customer) => customer.registeredPasses,
    );

    this.passesCronService.queuePassRefreshes(passes);
  }
}
