import { BadRequestException, Injectable } from '@nestjs/common';
import { VersionRepository } from '../repositories/version.repository';
import {
  CaptureVersionDto,
  Platform,
  VersionDocument,
  CheckVersionResponse,
  Version,
  ForVersion,
  responseCode,
  VersionViewModel,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

@Injectable()
export class VersionService {
  constructor(private readonly versionRepository: VersionRepository) {}

  async checkVersion(
    platform: Platform,
    version: string,
  ): Promise<CheckVersionResponse> {
    const forVersion = ForVersion.REWARDS_APP;
    const latestVersion =
      await this.versionRepository.findLatestVersionForPlatform(
        platform,
        forVersion,
      );

    const existingVersion =
      await this.versionRepository.findByVersionAndPlatform(
        version,
        platform,
        forVersion,
      );

    return {
      latestVersion: latestVersion ? latestVersion.version : null,
      forceUpdate: existingVersion ? existingVersion.forceUpdate : false,
    };
  }

  private async createVersion(
    version: string,
    platform: Platform,
    deviceId: string,
  ): Promise<void> {
    const deviceIds: string[] = [];
    if (deviceId) deviceIds.push(deviceId);
    const newVersion: Version = {
      version,
      platform,
      deviceIds: deviceIds,
      firstEnrollmentAt: new Date(),
      for: ForVersion.REWARDS_APP,
      forceUpdate: false,
    };
    await this.versionRepository.create(newVersion);
  }

  async captureVersion({
    deviceId,
    version,
    platform,
  }: CaptureVersionDto): Promise<void> {
    const existingVersion =
      await this.versionRepository.findByVersionAndPlatform(
        version,
        platform,
        ForVersion.REWARDS_APP,
      );

    if (!existingVersion) await this.createVersion(version, platform, deviceId);
    else {
      await this.removeDeviceId(deviceId, version, platform);
      await this.addDeviceId(deviceId, version, platform);
    }
  }

  private async removeDeviceId(
    deviceId: string,
    version: string,
    platform: string,
  ): Promise<void> {
    await this.versionRepository.findOneAndUpdate(
      { deviceIds: deviceId, version, platform },
      { $pull: { deviceIds: deviceId } },
    );
  }

  private async addDeviceId(
    deviceId: string,
    version: string,
    platform: string,
  ): Promise<void> {
    await this.versionRepository.findOneAndUpdate(
      { version, platform },
      { $addToSet: { deviceIds: deviceId } },
      { upsert: true },
    );
  }

  async forceUpdateVersion(
    versionId: Types.ObjectId,
  ): Promise<VersionViewModel> {
    const version = await this.versionRepository.findById(versionId);
    if (!version)
      throw new BadRequestException(
        `Version Doesn't Exist with id : ${versionId}`,
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    version.forceUpdate = !version.forceUpdate;
    await version.save();
    return this.mapToVersionViewModel(version);
  }

  async findAll(forVersion: ForVersion): Promise<VersionViewModel[]> {
    const versions = await this.versionRepository.findByForVersion(forVersion);
    if (versions.length != 0) {
      return versions
        .filter((version) => version.for === forVersion)
        .map(this.mapToVersionViewModel);
    } else
      throw new BadRequestException(
        `Can not find any versions for ${forVersion}`,
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
  }

  private mapToVersionViewModel(version: VersionDocument): VersionViewModel {
    return {
      _id: version._id,
      version: 'v' + version.version,
      platform: version.platform,
      devicesCount: version?.deviceIds?.length,
      forceUpdate: version.forceUpdate,
      firstEnrollmentAt: version.firstEnrollmentAt,
    };
  }
}
