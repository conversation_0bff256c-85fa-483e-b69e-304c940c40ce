// [EBL-5954] Notification Conditions | Tier Status
// Init `customer.tier.hasDowngradedToNoTier` based on loyalty tier logs

const current = new Date();
const startOfMonth = new Date(current.getFullYear(), current.getMonth(), 1);
db.customers.updateMany(
  {
    _id: {
      $in: db.loyaltytierlogs.distinct('customerId', {
        trigger: 'MONTHLY_TIER_COMPUTATION',
        action: 'DOWNGRADE',
        currentTierId: null,
        createdAt: { $gt: startOfMonth },
      }),
    },
  },
  { $set: { 'tier.hasDowngradedToNoTier': true } },
);
