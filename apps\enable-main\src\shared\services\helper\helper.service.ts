import {
  CountryDialCode,
  GenericTriggerModel,
  InternalErrorLog,
  LoggerService,
  responseCode,
  SavedLocation,
  SavedLocationDocument,
  SavedLocationType,
  TemplateTo,
  TriggerUserDto,
} from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { BadRequestException } from '@nestjs/common/exceptions';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { AxiosError } from 'axios';
import { plainToInstance, Transform } from 'class-transformer';
import { Request, Response } from 'express';
import { verify } from 'jsonwebtoken';
import { Model } from 'mongoose';
import { firstValueFrom } from 'rxjs';

import { CustomerTokenPayload } from '../../dto/customer-token-payload.dto';
import { LocationType } from '../../enums/location-type.enum';
import { BaladiaApiResponse, BaladiaResponse } from '../../types/baladia.type';

@Injectable()
export class HelperService {
  private readonly logger = new LoggerService(HelperService.name);
  constructor(
    private http: HttpService,
    private configService: ConfigService,
    @InjectModel(InternalErrorLog.name)
    private internalErrorLogModel: Model<InternalErrorLog>,
  ) {}

  public handelError(error, res: Response) {
    const status = error['statusCode'] || 500;
    const customCode = error['code'] || responseCode.GENERAL_ERROR;
    const message = error.message;

    const request = res.req as Request;
    const method = request.method;
    const url = request.url;

    this.internalErrorLogModel.create({
      requestUrl: `${method} ${url}`,
      requestBody: request.body,
      error: error,
    });

    return res
      .status(status)
      .json({ message: message, code: customCode, error: error });
  }

  public handelSuccessResponse(code, message, data, res: Response) {
    return res.status(200).send({ message, code, data });
  }

  public checkDeliveryOptionExist(
    usingBranchDrivers: boolean,
    usingCompanyDrivers: boolean,
    usingEbDelivery: boolean,
    usingThirdParty: boolean,
  ) {
    if (
      !usingBranchDrivers &&
      !usingCompanyDrivers &&
      !usingEbDelivery &&
      !usingThirdParty
    )
      throw new BadRequestException(
        'You Must Enable at least one delivery option ( Method ) ',
        responseCode.MISSING_DATA.toString(),
      );
  }

  public convertLocationToString(
    location: SavedLocation | SavedLocationDocument,
    locationType: LocationType,
  ) {
    let address = '';
    if (
      !location ||
      !location.latitude ||
      !location.longitude ||
      location.latitude == 1 ||
      location.longitude == 1
    ) {
      address = 'Not available';
    } else {
      address = this.convertAvailableLocationToString(location, locationType);
    }
    return address;
  }

  public async convertQatarNationalAddressToMapsCoordinate(
    zone: string | number,
    street: string | number,
    building: string | number,
  ): Promise<BaladiaResponse> {
    const finalURL = this.build_baladia_link(zone, street, building);
    this.logger.log(
      `Baladia request: Zone: ${zone}, Street: ${street}, Building: ${building}`,
      finalURL,
    );
    try {
      const response = await firstValueFrom(
        this.http.get<BaladiaApiResponse>(finalURL, {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
        }),
      );
      this.logger.log(
        `Baladia response: Zone: ${zone}, Street: ${street}, Building: ${building}`,
        response.data,
      );
      if (response.data?.features?.[0]) {
        const { Y_COORD: lat, X_COORD: lng } =
          response.data.features[0].attributes;
        const google_link = `http://maps.google.com/?q=${lat},${lng}`;
        return { lat: lat ?? 0, lng: lng ?? 0, google_link };
      }
    } catch (error) {
      this.logger.error('Error fetching Baladia data', error);
    }

    return {
      lat: 0,
      lng: 0,
      google_link: 'https://maps.google.com/q?0,0',
    };
  }

  public getProperty(prop_paths: any[], obj: any) {
    let value = obj,
      failed = false;
    for (let i = 0; i < prop_paths.length; i++) {
      if (prop_paths[i]['is_array_of_object']) {
        const arr = value[prop_paths[i]['array_name']];
        if (!arr) {
          continue;
        }
        for (let j = 0; j < arr.length; j++) {
          if (
            arr[j][prop_paths[i]['search_key']] ==
            prop_paths[i]['search_key_value']
          ) {
            value = arr[j][prop_paths[i]['result_key']];
            failed = false;
          }
        }
      } else {
        const path_split = prop_paths[i].split('.');
        for (let j = 0; j < path_split.length; j++) {
          const path_after_split = path_split[j];

          if (value.hasOwnProperty(path_after_split)) {
            value = value[path_after_split];
            failed = false;
          } else {
            failed = true;
          }
        }
      }
    }
    return failed ? '' : value;
  }

  public checkifObjectHasProberty(obj) {
    for (const prop in obj) {
      if (obj.hasOwnProperty(prop)) {
        return true;
      }
    }
    return false;
  }

  mapToTriggerUser(
    user: any,
    genericTriggerModel: GenericTriggerModel,
    role: TemplateTo,
  ): TriggerUserDto {
    if (!user) return null;
    return {
      id: user._id,
      firstName: this.getUserName(user, role),
      lastName: '',
      email: this.getUserEmail(user, role),
      phone: this.getUserPhone(user, role),
      countryCode: this.getUserCountryCode(genericTriggerModel, user, role),
      notificationToken: user.fcm_token,
      preferredLanguage: genericTriggerModel.language,
      role: role,
      walletApp: user.loyaltyCardStatus,
    };
  }

  public transformError(error: Error): {
    success: false;
    status: 'error';
    message: string;
    stackOrResponse: string | Record<string, any>;
  } {
    const isAxiosError = (err: any): err is AxiosError => err.isAxiosError;

    if (isAxiosError(error)) {
      return {
        success: false,
        status: 'error',
        message: error.message,
        stackOrResponse: error.response?.data,
      };
    }

    return {
      success: false,
      status: 'error',
      message: error.message,
      stackOrResponse: error.stack,
    };
  }

  public parseCustomerTokenPayload(authHeader: string): CustomerTokenPayload {
    if (!authHeader)
      throw new UnauthorizedException('Please provide an auth token.');
    try {
      const [, token] = authHeader.split(' ');
      const jwtPayload = verify(
        token,
        this.configService.get('MAIN_JWT_SECRET'),
      );

      if (!this.isPayloadValid(jwtPayload)) {
        throw new UnauthorizedException('Invalid payload');
      }

      return plainToInstance(CustomerTokenPayload, jwtPayload);
    } catch {
      throw new UnauthorizedException('Invalid token');
    }
  }

  private convertAvailableLocationToString(
    location: SavedLocation,
    locationType: LocationType,
  ) {
    if (locationType == LocationType.PICKUP)
      return `${location.area}, ${location.additionalInfo}`;

    const createPart = (value: string | number, label: string) =>
      value ? `${label}: ${value.toString()}` : '';

    const joinParts = (separator: string, parts: string[]) =>
      parts.filter(Boolean).join(separator);

    const parts =
      location.type == SavedLocationType.PIN_LOCATION
        ? [
            location.area,
            createPart(location.streetName, 'Street Name'),
            createPart(location.buildingName, 'Building Name'),
            createPart(location.floorNumber, 'Floor'),
            createPart(location.unitNumber, 'Unit Number'),
          ]
        : [
            location.area,
            createPart(location.streetNumber, 'Street'),
            joinParts(' - ', [
              createPart(location.buildingName, 'Building Name'),
              createPart(location.buildingNumber, 'Building Number'),
            ]),
            createPart(location.unitNumber, 'Unit Number'),
          ];

    return joinParts(', ', parts);
  }

  private build_baladia_link(zone, street, building) {
    const baseURL = this.configService.get('BALADIA_API_URL');

    return (
      baseURL +
      `&where=zone_no%3D${zone}+and+street_no%3D${street}+and+building_no%3D${building}`
    );
  }

  private getUserName(user: any, role: TemplateTo): string {
    if (role == TemplateTo.CUSTOMER) return user.first_name;
    else if (role == TemplateTo.GIFT_RECIPIENT) return user.recipientName;
    else return user?.name ? user.name : '';
  }

  private getUserEmail(user: any, role: TemplateTo): string {
    if (role == TemplateTo.GIFT_RECIPIENT || role == TemplateTo.ITEM_CREATOR)
      return '';
    else return user?.email ? user.email : '';
  }

  private getUserPhone(user: any, role: TemplateTo): string {
    if (role == TemplateTo.GIFT_RECIPIENT) return user.recipientPhone;
    else return user?.phone ? user.phone : '';
  }

  private getUserCountryCode(
    genericTriggerModel: GenericTriggerModel,
    user: any,
    role: TemplateTo,
  ): CountryDialCode {
    if (role == TemplateTo.GIFT_RECIPIENT) return user.recipientCountryCode;
    else if (role == TemplateTo.CUSTOMER) return user.country_code;
    else return genericTriggerModel?.countryCode ?? CountryDialCode.UNDEFINED;
  }

  private isPayloadValid(payload: any): payload is CustomerTokenPayload {
    return (
      payload &&
      typeof payload === 'object' &&
      'customerId' in payload &&
      'companyId' in payload &&
      'brandId' in payload
    );
  }
}

export const capitalizeFirstLetter = (s: string) => {
  if (typeof s !== 'string') return '';
  return s.charAt(0).toUpperCase() + s.slice(1).toLowerCase();
};

export const capitalizeFirstLetterOfEachWord = (s: string) => {
  if (typeof s !== 'string') return '';
  return s.split(' ').map(capitalizeFirstLetter).join(' ');
};

export const addSpaces = (s: string) => {
  if (typeof s !== 'string') return '';

  // underscores_as_spaces
  if (s.includes('_')) return s.replaceAll('_', ' ');

  // camelCaseNoSpaces
  if (/^([a-z]+)(([A-Z]([a-z]+))+)$/.test(s))
    return s.replace(/([A-Z])/g, ' $1');

  return s;
};

export const formatText = (s: string) => {
  if (typeof s !== 'string') return '';
  return capitalizeFirstLetterOfEachWord(addSpaces(s));
};

export function Default(defaultValue: any) {
  return Transform((value: any) =>
    value !== null && value !== undefined ? value : defaultValue,
  );
}

export const parsePhoneNumber = (
  phone: string,
  countryCode?: string,
): string => {
  // remove spaces, dashes, and parentheses from phone number
  const phoneCompacted = phone.replace(/[\s\(\)\-]/g, '');
  const shouldIncludeCountryCode = phoneCompacted.length <= 10;
  const phoneNumber = shouldIncludeCountryCode
    ? (countryCode || '+974') + phoneCompacted
    : phoneCompacted;
  return phoneNumber;
};

export const parseArabicNumber = (numberString: string): number => {
  const arabicNumbers = '٠١٢٣٤٥٦٧٨٩';
  const englishNumbers = '0123456789';

  let parsedNumber = '';
  for (const char of numberString) {
    const index = arabicNumbers.indexOf(char);

    if (index >= 0) parsedNumber += englishNumbers[index];
    else parsedNumber += char;
  }

  return parseInt(parsedNumber);
};

export const pluralize = (
  n: number,
  strings: { one?: string; two?: string; few?: string; many: string },
): string => {
  if (n === 1 && strings.one) return strings.one;
  if (n === 2 && strings.two) return strings.two;
  if (n <= 10 && strings.few) return strings.few;
  return strings.many;
};
