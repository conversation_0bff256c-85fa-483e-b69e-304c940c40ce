import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsNotEmpty, ValidateIf } from 'class-validator';
import { IntegrationType } from '../enums/integration-type.enum';
import { UpdateIntegrationOrderDto } from './update-integration-order.dto';

export class UpdateIntegrationOrderPaymentDto {
  @ApiProperty({
    type: String,
    required: true,
    enum: IntegrationType,
  })
  @IsEnum(IntegrationType)
  @IsNotEmpty()
  integrationType: IntegrationType;

  @ApiProperty({
    type: () => UpdateIntegrationOrderDto,
    required: false,
  })
  @ValidateIf(
    (o) =>
      o.integrationType === IntegrationType.ORDER ||
      o.integrationType === IntegrationType.ORDER_WITH_PAYMENT,
  )
  @IsNotEmpty()
  @Type(() => UpdateIntegrationOrderDto)
  order?: UpdateIntegrationOrderDto;
}
