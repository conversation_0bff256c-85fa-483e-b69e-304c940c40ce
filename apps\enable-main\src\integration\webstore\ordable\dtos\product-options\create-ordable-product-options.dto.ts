import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsString,
} from 'class-validator';

export class CreateOrdableProductOptionsDto {
  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsInt()
  product_id: number;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsInt()
  option_id: number;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  value: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  ar_value: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNumber()
  price: number;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsInt()
  sort_order: number;

  @ApiProperty({
    type: Boolean,
    required: true,
  })
  @IsBoolean()
  is_active: boolean;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsString()
  deliverect_plu: string;
}
