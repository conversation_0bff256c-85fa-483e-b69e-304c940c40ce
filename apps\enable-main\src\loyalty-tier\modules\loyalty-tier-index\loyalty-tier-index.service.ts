import {
  IndexLoyaltyTierDto,
  LoyaltyTierDocument,
  LoyaltyTierIndexOutput,
  LoyaltyTierSearchType,
  NoTier,
  omit,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { CompanyService } from '../../../company/services/company/company.service';
import { LoyaltyTierRepositoryInterface } from '../loyalty-tier-repository/loyalty-tier.repository.interface';

@Injectable()
export class LoyaltyTierIndexService {
  constructor(
    @Inject(LoyaltyTierRepositoryInterface)
    private readonly loyaltyTierRepository: LoyaltyTierRepositoryInterface,
    private readonly companyService: CompanyService,
  ) {}

  async index(
    indexLoyaltyTierDto: IndexLoyaltyTierDto & { withNoTier: true },
  ): Promise<[NoTier, ...LoyaltyTierDocument[]]>;
  async index(
    indexLoyaltyTierDto: IndexLoyaltyTierDto,
  ): Promise<LoyaltyTierDocument[]>;
  async index(
    indexLoyaltyTierDto: IndexLoyaltyTierDto,
  ): Promise<LoyaltyTierDocument[] | [NoTier, ...LoyaltyTierDocument[]]> {
    if (indexLoyaltyTierDto.type === LoyaltyTierSearchType.VIP) {
      indexLoyaltyTierDto.isVipTier = true;
    } else if (indexLoyaltyTierDto.type === LoyaltyTierSearchType.NORMAL) {
      indexLoyaltyTierDto.isVipTier = false;
    } else {
      delete indexLoyaltyTierDto.isVipTier;
    }

    const loyaltyTiers = await this.loyaltyTierRepository.find(
      omit(indexLoyaltyTierDto, ['type', 'withNoTier', 'output']),
    );

    const populatedTiers = await this.populateExchangeRates(
      indexLoyaltyTierDto,
      loyaltyTiers,
    );

    if (indexLoyaltyTierDto.withNoTier) {
      return [NoTier, ...populatedTiers];
    }

    return populatedTiers;
  }

  private async populateExchangeRates(
    indexLoyaltyTierDto: IndexLoyaltyTierDto,
    loyaltyTiers: LoyaltyTierDocument[],
  ): Promise<LoyaltyTierDocument[]> {
    if (indexLoyaltyTierDto.output !== LoyaltyTierIndexOutput.EXCHANGE_RATES)
      return loyaltyTiers;

    const company = await this.companyService.findById(
      indexLoyaltyTierDto.companyId,
    );
    const tierExchangeRates =
      company.loyaltyProgramConfig?.earningExchangeRates?.tierExchangeRates;
    if (!tierExchangeRates) return loyaltyTiers;

    return loyaltyTiers.map((loyaltyTier) =>
      loyaltyTier.set(
        'pointsExchangeRate',
        loyaltyTier._id.toString() in tierExchangeRates
          ? tierExchangeRates[loyaltyTier._id.toString()]
          : null,
        { strict: false },
      ),
    );
  }
}
