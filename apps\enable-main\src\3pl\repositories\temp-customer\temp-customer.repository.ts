import { Injectable } from '@nestjs/common';
import {
  CollectionName,
  GenericRepository,
  TempCustomer,
  TempCustomerDocument,
} from '@app/shared-stuff';
import { TempCustomerRepositoryInterface } from './temp-customer-repository.interface';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@Injectable()
export class TempCustomerRepository
  extends GenericRepository<TempCustomerDocument, TempCustomer>
  implements TempCustomerRepositoryInterface
{
  constructor(
    @InjectModel(CollectionName.TEMP_CUSTOMER)
    private tempCustomerModel: Model<TempCustomerDocument, TempCustomer>,
  ) {
    super(tempCustomerModel);
  }

  async findOneBySalesOrderId(
    salesOrderId: string,
  ): Promise<TempCustomerDocument> {
    return this.tempCustomerModel
      .findOne({ salesOrderId: salesOrderId })
      .exec();
  }
}
