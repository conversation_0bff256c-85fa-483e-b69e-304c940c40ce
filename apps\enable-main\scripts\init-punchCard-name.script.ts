// EBL-4953 Multi-Tracks | Punch Card
// Init punch card "nameEn" and "nameAr"

db.punchcards.updateMany(
  { $or: [{ nameEn: { $exists: false } }, { nameAr: { $exists: false } }] },
  { $set: { nameEn: 'Punch', nameAr: 'Punch' } },
);

db.customers.updateMany(
  {
    'punchCardProgress.punchCard': { $exists: true },
    'punchCardProgress.punchCard.nameEn': { $exists: false },
  },
  {
    $set: {
      'punchCardProgress.$[progress].punchCard.nameEn': 'Punch',
      'punchCardProgress.$[progress].punchCard.nameAr': 'Punch',
    },
  },
  { arrayFilters: [{ 'progress.punchCard.nameEn': { $exists: false } }] },
);
