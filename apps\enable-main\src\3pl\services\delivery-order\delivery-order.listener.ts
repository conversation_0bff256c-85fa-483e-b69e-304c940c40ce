import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { TookanWebhookPayload } from '../../../delivery/types/tookan-webhook-payload.type';
import { DeliveryOrderServiceInterface } from './delivery-order-service.interface';
import { LogError } from '@app/shared-stuff';

@Injectable()
export class DeliveryOrderListener {
  constructor(
    @Inject(DeliveryOrderServiceInterface)
    private deliveryOrderService: DeliveryOrderServiceInterface,
  ) {}

  @OnEvent('tookan-updated-for-non-order')
  @LogError()
  async handleDeliveryOrderCreatedEvent(payload: TookanWebhookPayload) {
    const deliveryOrder = await this.deliveryOrderService.findOneByCode(
      payload.order_id,
    );
    if (deliveryOrder) {
      await this.deliveryOrderService.deliveryTaskUpdated(
        deliveryOrder,
        payload,
      );
    }
  }
}
