import { SharedStuffModule } from '@app/shared-stuff';
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { CompanyModule } from '../../../company/company.module';
import { CustomerReadModule } from '../../../customer/modules/customer-read/customer-read.module';
import { SharedModule } from '../../../shared/shared.module';
import { IntegrationLogModule } from '../../integration-log/integration-log.module';
import { SolutionERPListener } from './listeners/solution-erp.listener';
import { SolutionERPService } from './services/solution-erp.service';

@Module({
  imports: [
    SharedModule,
    SharedStuffModule,
    CompanyModule,
    HttpModule,
    ConfigModule,
    IntegrationLogModule,
    CustomerReadModule,
  ],
  providers: [
    SolutionERPListener,
    {
      provide: 'SolutionERPServiceInterface',
      useClass: SolutionERPService,
    },
  ],
  controllers: [],
  exports: [],
})
export class SolutionERPModule {}
