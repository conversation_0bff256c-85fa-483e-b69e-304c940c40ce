import {
  CaptureVersionDto,
  CheckVersionResponse,
  GenericExceptionFilter,
  Platform,
  TransformInterceptor,
  Version,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  SetMetadata,
  UseFilters,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';
import { VersionService } from '../modules/version/services/version.service';

@Controller('mobile')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@UsePipes(
  new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidUnknownValues: true,
  }),
)
@ApiTags('Mobile')
@SetMetadata('module', 'mobile')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class MobileController {
  constructor(private readonly versionService: VersionService) {}

  @Get('version')
  @SetMetadata('public', 'true')
  async checkVersion(
    @Query('platform') platform: Platform,
    @Query('version') version: string,
  ): Promise<CheckVersionResponse> {
    return this.versionService.checkVersion(platform, version);
  }

  @Post('version')
  @SetMetadata('public', 'true')
  @ApiOkResponse({ type: Version })
  async captureVersion(
    @Body()
    captureVersionDto: CaptureVersionDto,
  ): Promise<void> {
    this.versionService.captureVersion(captureVersionDto);
  }
}
