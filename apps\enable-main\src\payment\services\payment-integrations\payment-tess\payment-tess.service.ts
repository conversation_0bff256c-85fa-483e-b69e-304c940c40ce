import {
  CompanyDocument,
  Currency,
  Customer,
  OrderDocument,
  Payment,
  PaymentDocument,
  PaymentGatewayType,
  PaymentMethodUsed,
  PaymentStatusEnum,
} from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createHash } from 'crypto';
import { CompanyService } from '../../../../company/services/company/company.service';
import { CustomerReadServiceInterface } from '../../../../customer/modules/customer-read/customer-read.service.interface';
import { OrderService } from '../../../../order/services/order/order.service';
import { ENABLE_TECH_URL } from '../../../constants/payment-callback.const';
import { paymentMethodToTESSMapping } from '../../../constants/payment-method-to-tess-mapping.const';
import { CreateTESSPaymentIntegrationDto } from '../../../dto/tess/create-tess-payment-integration.dto';
import { PaymentLogAction } from '../../../enums/payment-log-action.enum';
import { PaymentCallbackResult } from '../../../enums/tess/payment-callback-result.enum';
import { TESSPaymentMethod } from '../../../enums/tess/tess-payment-methods.enum';
import { UrlTargetTESSPaymentEnum } from '../../../enums/tess/url-target-tess-payment.enum';
import { PaymentLogRepositoryInterface } from '../../../repositories/interfaces/payment.log.repository.interface';
import { PaymentRepositoryInterface } from '../../../repositories/interfaces/payment.repository.interface';
import { OrderTESSPaymentIntegration } from '../../../types/tess/order-tess-payment-integration.type';
import { PaymentHelpersService } from '../../payment-helpers/payment-helpers.service';
import { PaymentIntegrationServiceInterface } from '../payment-integration.service.interface';

@Injectable()
export class PaymentTESSService implements PaymentIntegrationServiceInterface {
  constructor(
    private httpService: HttpService,
    @Inject('PaymentLogRepositoryInterface')
    private paymentLogRepository: PaymentLogRepositoryInterface,
    private configService: ConfigService,
    private orderService: OrderService,
    @Inject(CustomerReadServiceInterface)
    private customerReadService: CustomerReadServiceInterface,
    private paymentHelperService: PaymentHelpersService,
    @Inject('PaymentRepositoryInterface')
    private paymentRepository: PaymentRepositoryInterface,
    private companyService: CompanyService,
  ) {}

  async processPayment(
    payment: PaymentDocument,
    company: CompanyDocument,
  ): Promise<string> {
    const customer = await this.customerReadService.findOne(
      payment.customer._id.toHexString(),
      company._id,
    );
    const orderTESSPaymentIntegration =
      this.constructOrderTESSPaymentIntegration(payment, company);
    const signature = this.generateAuthenticationSignature(
      orderTESSPaymentIntegration,
    );
    const createTESSPaymentIntegrationDto =
      this.constructCreateTESSPaymentIntegrationDto(
        orderTESSPaymentIntegration,
        signature,
        customer,
        payment,
      );
    const response = await this.create(
      createTESSPaymentIntegrationDto,
      signature,
    );
    await this.applyCreatePostFunction(
      response,
      createTESSPaymentIntegrationDto,
      payment,
    );
    return response['data']['redirect_url'];
  }

  private async applyCreatePostFunction(
    response: Record<string, any>,
    createTESSPaymentIntegrationDto: CreateTESSPaymentIntegrationDto,
    payment: PaymentDocument,
  ): Promise<void> {
    if (response['data']['redirect_url']) {
      payment.status = PaymentStatusEnum.GATEWAY_LINK_GENERATED;
      payment.payment_method_used = PaymentMethodUsed.TESS;
      payment.gateway_link = response['data']['redirect_url'];
      await payment.save();
    }
    await this.paymentLogRepository.create({
      sentObject: createTESSPaymentIntegrationDto,
      receivedObject: response,
      logAction: PaymentLogAction.TessPaymentProcessed,
      paymentCode: payment.code,
      paymentId: payment._id,
    });
  }

  private constructCreateTESSPaymentIntegrationDto(
    orderTESSPaymentIntegration: OrderTESSPaymentIntegration,
    signature: string,
    customer: Customer,
    payment: Payment,
  ): CreateTESSPaymentIntegrationDto {
    return {
      merchant_key: this.configService.get('TESS_MERCHANT_KEY'),
      operation: 'purchase',
      success_url: `${this.configService.get(
        'HOST_URL',
      )}/payment-tess/public/callback/success/${payment.code}`,
      cancel_url: `${this.configService.get(
        'HOST_URL',
      )}/payment-tess/public/callback/cancel/${payment.code}`,
      url_target: UrlTargetTESSPaymentEnum._parent,
      hash: signature,
      order: orderTESSPaymentIntegration,
      methods:
        payment.payment_method in paymentMethodToTESSMapping
          ? paymentMethodToTESSMapping[payment.payment_method]
          : [TESSPaymentMethod.CARD, TESSPaymentMethod.NAPS],
      customer: {
        name: customer?.full_name,
        email: this.getTessCustomerEmail(customer.phone),
      },
    };
  }

  private getTessCustomerEmail(phone: string): string {
    if (phone.startsWith('+')) phone = phone.slice(1);

    return `${phone}@enable.tech`;
  }

  private constructOrderTESSPaymentIntegration(
    payment: PaymentDocument,
    company: CompanyDocument,
  ): OrderTESSPaymentIntegration {
    return {
      number: payment.code,
      amount: parseFloat(payment?.amount.toString()).toFixed(2),
      currency: company?.localization?.currency
        ? company?.localization?.currency
        : Currency.QAR,
      description: payment?.comments
        ? payment.comments
        : 'this is a dummy description',
    };
  }

  private async create(
    createPaymentIntegrationData: CreateTESSPaymentIntegrationDto,
    signature: string,
  ): Promise<any> {
    const URL = this.configService.get('TESS_BASE_URL') + '/api/v1/session';
    return new Promise((resolve) => {
      this.httpService
        .post(URL, createPaymentIntegrationData, {
          headers: { Authorization: signature },
        })
        .subscribe(
          (data) => {
            resolve({ status: 'success', data: data.data });
          },
          (err) => {
            resolve({ status: 'error', data: err.response.data });
          },
        );
    });
  }

  private generateAuthenticationSignature(
    orderTESSPaymentIntegration: OrderTESSPaymentIntegration,
  ): string {
    const to_md5 =
      orderTESSPaymentIntegration.number +
      orderTESSPaymentIntegration.amount +
      orderTESSPaymentIntegration.currency +
      orderTESSPaymentIntegration.description +
      this.configService.get('TESS_MERCHANT_PASS');
    const md5 = createHash('md5').update(to_md5.toUpperCase()).digest('hex');

    return createHash('sha1').update(md5).digest('hex');
  }

  async callback(
    paymentCallbackResult: PaymentCallbackResult,
    paymentCode: string,
  ): Promise<string> {
    const payment =
      await this.paymentRepository.findByCodeWithBranch(paymentCode);
    if (!payment) return;

    const company = await this.companyService.get_details(payment.company);
    let order: OrderDocument;
    if (payment.order_code)
      order = await this.orderService.get_details(payment.order_code);

    const updatedCallbackPaymentIntegrationDto =
      await this.paymentHelperService.saveTransaction(
        payment,
        payment.transaction_id ? payment.transaction_id : 'NOT_FOUND',
        {
          paymentCode: paymentCode,
          paymentCallbackResult: paymentCallbackResult,
        },
        PaymentGatewayType.TESS,
      );
    if (updatedCallbackPaymentIntegrationDto['ENABLE_NOTE'])
      return ENABLE_TECH_URL;

    const paymentStatus =
      paymentCallbackResult === PaymentCallbackResult.SUCCESS
        ? PaymentStatusEnum.TRANSACTION_COMPLETED
        : PaymentStatusEnum.UNSUCCESSFUL;
    await this.paymentHelperService.handlePaymentStatusLogic(
      payment,
      paymentStatus,
      order,
      company,
    );
    const callbackURL = this.paymentHelperService.generateCallBackUrl(
      payment,
      company,
      paymentCallbackResult == PaymentCallbackResult.SUCCESS ? '000' : '152',
      order,
    );
    await this.paymentLogRepository.create({
      sentObject: { callbackURL },
      receivedObject: {
        paymentCode: paymentCode,
        paymentCallbackResult: paymentCallbackResult,
      },
      logAction: PaymentLogAction.TessPaymentWebhookFired,
      paymentCode: payment.code,
      paymentId: payment._id,
    });
    return callbackURL;
  }
}
