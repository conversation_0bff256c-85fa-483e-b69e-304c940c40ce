import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class EmailSendDto {
  @ApiProperty({
    type: String,
    required: true,
    description:
      'Comma Separated String contains the emails for who we need to sent to',
  })
  @IsNotEmpty()
  to: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  from: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  subject: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  content: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsOptional()
  cc: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsOptional()
  bcc: string;
}
