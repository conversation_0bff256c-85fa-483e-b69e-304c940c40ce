// Add microsDiscountId to embedded customer loyalty tiers

db.customers.bulkWrite(
  db.customers
    .find(
      {
        loyaltyTier: { $ne: null },
        'loyaltyTier.microsDiscountId': { $exists: false },
      },
      { _id: 1, loyaltyTier: 1 },
    )
    .toArray()
    .map(({ _id, loyaltyTier }) => {
      const tier = db.loyaltytiers.findOne(
        { _id: loyaltyTier._id },
        { microsDiscountId: 1 },
      );
      return {
        updateOne: {
          filter: { _id },
          update: {
            $set: {
              'loyaltyTier.microsDiscountId': tier.microsDiscountId,
            },
          },
        },
      };
    }),
  { ordered: false },
);
