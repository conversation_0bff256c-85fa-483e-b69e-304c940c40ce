import {
  BrandDocument,
  CacheServiceInterface,
  EmbeddedBrandDto,
} from '@app/shared-stuff';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { StoreEvents } from 'apps/enable-main/src/store/enumerations/store-events.enum';
import { plainToClass } from 'class-transformer';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { BrandServiceInterface } from '../../brand/services/brand/brand.service.interface';
import { ApiKeyDocument } from '../../user/models/apikey.model';
import { ApikeyService } from '../../user/services/apikey/apikey.service';
import { StoreSortMapping } from '../constants';
import { CreateStoreDto } from '../dto/create-store.dto';
import { IndexStoreDto } from '../dto/index-store.dto';
import { UpdateStoreDto } from '../dto/update-store.dto';
import { Store, StoreDocument } from '../models/store.model';
import { StoreRepositoryInterface } from '../repositories/store.repository.interface';
import { StoreServiceInterface } from './store.service.interface';

@Injectable()
export class StoreService implements StoreServiceInterface {
  availableFields = [
    'link',
    'apiKey',
    'apiBaseUrl',
    'name',
    'provider',
    'enableApiKey',
  ];

  constructor(
    @Inject('StoreRepositoryInterface')
    private readonly storeRepository: StoreRepositoryInterface,
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandServiceInterface,
    private apiKeyService: ApikeyService,
    private readonly eventEmitter: EventEmitter2,
    @Inject(CacheServiceInterface)
    private readonly cacheService: CacheServiceInterface,
  ) {}

  async create(createStoreDto: CreateStoreDto): Promise<StoreDocument> {
    const embeddedBrands = await this.mapBrandsToEmbeddedBrands(
      createStoreDto?.brandIds,
    );
    const store = plainToClass(Store, createStoreDto);
    store.companyId = new Types.ObjectId(createStoreDto.companyId);
    store.brands = embeddedBrands;

    const apiKey = await this.createApiKey(store);
    store.enableApiKey = apiKey;
    const newStore = await this.storeRepository.create(store);
    this.eventEmitter.emit(StoreEvents.STORE_CREATED, newStore);
    return newStore;
  }

  private async createApiKey(store: Store): Promise<ApiKeyDocument> {
    return await this.apiKeyService.create({
      name: store.provider.toString() + '-' + store.name,
      expire_date: moment(moment(), 'DD-MM-YYYY').add(5, 'years'),
      company: store.companyId.toHexString(),
      brand: store.brands[0],
    } as any);
  }

  private async mapBrandsToEmbeddedBrands(
    brandIds: Types.ObjectId[],
  ): Promise<EmbeddedBrandDto[]> {
    const brands = await this.getBrandsByIds(brandIds);
    const embeddedBrands = [];
    for (let i = 0; i < brands.length; i++) {
      const embeddedBrand: EmbeddedBrandDto = {
        _id: brands[i]._id,
        name: brands[i].name,
        senderId: brands[i].senderId,
        emailSenderId: brands[i].emailSenderId,
        phoneNumber: brands[i].phoneNumber,
      };
      embeddedBrands.push(embeddedBrand);
    }
    return embeddedBrands;
  }

  private async getBrandsByIds(
    brandIds: Types.ObjectId[],
  ): Promise<BrandDocument[]> {
    const objectBrandIds = [];
    for (let i = 0; i < brandIds.length; i++) {
      objectBrandIds.push(new Types.ObjectId(brandIds[i]));
    }
    return await this.brandService.findByIdIn(objectBrandIds);
  }

  async index(indexStoreDto: IndexStoreDto): Promise<any[]> {
    const match = { deletedAt: null };
    const pipeline = [];
    this.addMatchStage(match, pipeline, indexStoreDto);
    this.addSortStage(pipeline, indexStoreDto);
    this.addPaginationStage(pipeline, indexStoreDto);
    return await this.storeRepository.aggregate(pipeline);
  }

  async update(updateStoreDto: UpdateStoreDto): Promise<StoreDocument> {
    const embeddedBrands = await this.mapBrandsToEmbeddedBrands(
      updateStoreDto?.brandIds || [],
    );
    const store = plainToClass(Store, updateStoreDto);
    store.companyId = new Types.ObjectId(updateStoreDto.companyId);
    store.brands = embeddedBrands;
    const updatedStore = await this.storeRepository.findOneAndUpdate(
      { _id: updateStoreDto.id },
      store,
    );
    this.eventEmitter.emit('store.updated', updatedStore);
    return updatedStore;
  }

  async findById(id: Types.ObjectId): Promise<StoreDocument> {
    const store = await this.storeRepository.findById(id);
    if (!store) throw new BadRequestException("Store Doesn't Exist");
    return store;
  }

  async delete(id: Types.ObjectId): Promise<void> {
    await this.storeRepository.remove(id);
  }

  async findByBrand(brandId: Types.ObjectId): Promise<StoreDocument[]> {
    return await this.storeRepository.findByBrand(brandId);
  }

  async findByCompanyId(companyId: Types.ObjectId): Promise<StoreDocument[]> {
    return await this.storeRepository.findByCompany(companyId);
  }

  private addSortStage(pipeline: any, indexStoreDto: IndexStoreDto) {
    if (indexStoreDto.sortType) {
      pipeline.push({
        $sort: StoreSortMapping[indexStoreDto.sortType],
      });
    } else
      pipeline.push({
        $sort: { createdAt: -1 },
      });
  }

  private addMatchStage(
    match: any,
    pipeline: any,
    indexStoreDto: IndexStoreDto,
  ) {
    if (indexStoreDto.brandId) {
      match['brands'] = {
        $elemMatch: { _id: new Types.ObjectId(indexStoreDto.brandId) },
      };
    }

    if (indexStoreDto.companyId) {
      match['companyId'] = new Types.ObjectId(indexStoreDto.companyId);
    }

    if (indexStoreDto.provider) {
      match['provider'] = indexStoreDto.provider;
    }

    if (indexStoreDto.search_key) {
      match['$or'] = this.availableFields.map((field) => {
        return {
          [field]: {
            $regex: indexStoreDto.search_key,
            $options: 'i',
          },
        };
      });
    }
    pipeline.push({
      $match: match,
    });
  }

  private addPaginationStage(pipeline: any, indexStoreDto: IndexStoreDto) {
    pipeline.push({
      $facet: {
        paginatedResult: [
          ...(Number(indexStoreDto.offset)
            ? [
                {
                  $skip:
                    Number(indexStoreDto.offset) * Number(indexStoreDto.limit),
                },
              ]
            : [
                {
                  $skip: 0,
                },
              ]),
          ...(Number(indexStoreDto.limit)
            ? [
                {
                  $limit: Number(indexStoreDto.limit),
                },
              ]
            : []),
        ],
        totalCount: [
          {
            $count: 'createdAt',
          },
        ],
      },
    });
  }
}
