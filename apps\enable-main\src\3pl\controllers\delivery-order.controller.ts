import {
  Assign<PERSON>riverDto,
  CreateDeliveryOrderDto,
  GenericExceptionFilter,
  GetAllDeliveryOrderDto,
  SingleIdDto,
  TransformInterceptor,
  UpdateDeliveryOrderStatusDto,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Get,
  Inject,
  Param,
  Post,
  Query,
  Req,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { CurrentUserService } from '../../shared/services/current-user/current-user.service';
import { DeliveryOrderServiceInterface } from '../services/delivery-order/delivery-order-service.interface';

@Controller('delivery-order')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags('Third Party Logistics')
@SetMetadata('module', 'delivery-order')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class DeliveryOrderController {
  constructor(
    @Inject(DeliveryOrderServiceInterface)
    private deliveryOrderService: DeliveryOrderServiceInterface,
    private currentUserService: CurrentUserService,
  ) {}

  @Get()
  @SetMetadata('action', 'index')
  async findAll(
    @Query() getAllDeliveryOrderDto: GetAllDeliveryOrderDto,
    @Req() req: Request,
  ) {
    getAllDeliveryOrderDto.companyId = req['company_id']
      ? req['company_id']
      : getAllDeliveryOrderDto.companyId;

    const result = await this.deliveryOrderService.findAll(
      getAllDeliveryOrderDto,
    );
    return {
      deliveryOrders: result[0].paginatedResult,
      totalDeliveryOrders: result[0].totalCount[0]?.createdAt,
    };
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  async findOne(@Param() { id }: SingleIdDto) {
    return this.deliveryOrderService.findOne(id);
  }

  @Get('sales-related/:salesOrderId')
  @SetMetadata('action', 'get_details')
  async findSalesRelated(@Param('salesOrderId') id: string) {
    return this.deliveryOrderService.getSalesRelated(id);
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() createDeliveryOrderDto: CreateDeliveryOrderDto,
    @Req() req: Request,
  ) {
    createDeliveryOrderDto.companyId = req['company_id']
      ? req['company_id']
      : createDeliveryOrderDto.companyId;
    return this.deliveryOrderService.create(createDeliveryOrderDto);
  }

  @Post('status')
  @SetMetadata('action', 'change_status')
  async updateStatus(
    @Body() updateDeliveryOrderStatusDto: UpdateDeliveryOrderStatusDto,
  ) {
    return this.deliveryOrderService.updateStatus(updateDeliveryOrderStatusDto);
  }

  @Post('assign-driver')
  @SetMetadata('action', 'assign-driver')
  async assignDriverToTask(@Body() assignDriverDto: AssignDriverDto) {
    assignDriverDto.companyId = this.currentUserService.getCurrentCompanyId();
    await this.deliveryOrderService.assignDriverToTask(assignDriverDto);
  }
}
