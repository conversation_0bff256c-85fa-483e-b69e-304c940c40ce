import { CustomerDocument, EmbeddedTierDto } from '@app/shared-stuff';
import { StoreDocument } from '../../../../../store/models/store.model';

export interface OrdablePromotionsSyncServiceInterface {
  initPromotions(
    store: StoreDocument,
    customers: CustomerDocument[],
  ): Promise<void>;
  syncCustomerCoupons(customer: CustomerDocument): Promise<void>;
  syncCustomerTier(
    customer: CustomerDocument,
    previousTier?: EmbeddedTierDto,
  ): Promise<void>;
  syncCustomerRewards(customer: CustomerDocument): Promise<CustomerDocument>;
  syncCustomerBenefits(customer: CustomerDocument): Promise<CustomerDocument>;
}

export const OrdablePromotionsSyncServiceInterface = Symbol(
  'OrdablePromotionsSyncServiceInterface',
);
