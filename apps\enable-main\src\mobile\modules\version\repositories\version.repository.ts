import {
  CollectionName,
  ForVersion,
  GenericRepository,
  Platform,
  Version,
  VersionDocument,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@Injectable()
export class VersionRepository extends GenericRepository<
  VersionDocument,
  Version
> {
  constructor(
    @InjectModel(CollectionName.VERSIONS)
    private versionModel: Model<VersionDocument, Version>,
  ) {
    super(versionModel);
  }

  async findByVersionAndPlatform(
    version: string,
    platform: Platform,
    forVersion: ForVersion,
  ): Promise<VersionDocument> {
    return await this.versionModel.findOne({
      version,
      platform,
      for: forVersion,
    });
  }

  async findByForVersion(forVersion: ForVersion): Promise<VersionDocument[]> {
    return await this.versionModel.find({ for: forVersion });
  }

  async findLatestVersionForPlatform(
    platform: Platform,
    forVersion: ForVersion,
  ): Promise<VersionDocument> {
    return await this.versionModel
      .findOne({ platform, for: forVersion })
      .sort({ version: -1 })
      .exec();
  }
}
