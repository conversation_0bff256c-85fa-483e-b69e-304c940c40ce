import { Request, Response } from 'express';
import { DeliveryPartyToCreate } from './../../dto/delivery-party.dto';
import { DeliveryPartyService } from './../../services/delivery-party/delivery-party.service';
import { HelperService } from './../../../shared/services/helper/helper.service';
import { Body, Controller, Post, Req, Res, SetMetadata } from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { responseCode } from '@app/shared-stuff';

@Controller('delivery-party')
@ApiTags('Delivery Party')
@SetMetadata('module', 'delivery-party')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class DeliveryPartyController {
  constructor(
    private helperService: HelperService,
    private deliveryPartyService: DeliveryPartyService,
  ) {}

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() deliveryToCreate: DeliveryPartyToCreate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const deliveryParty =
        await this.deliveryPartyService.create(deliveryToCreate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success to create a new Delivery Party',
        deliveryParty,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
}
