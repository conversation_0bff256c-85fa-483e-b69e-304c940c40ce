import {
  CreateMessageVariantDto,
  MicroserviceCommunicationService,
  TemplateDocument,
  UpdateMessageVariantDto,
} from '@app/shared-stuff';
import {
  Inject,
  Injectable,
  OnModuleDestroy,
  OnModuleInit,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { Types } from 'mongoose';
import { MessageVariantServiceInterface } from './message-variant.service.interface';

@Injectable()
export class MessageVariantService
  implements MessageVariantServiceInterface, OnModuleInit, OnModuleDestroy
{
  constructor(
    @Inject('enable-main-notification-producer')
    private client: ClientProxy,
    private microserviceCommunicationService: MicroserviceCommunicationService,
  ) {}

  onModuleInit() {
    this.microserviceCommunicationService.connect(this.client);
  }

  onModuleDestroy() {
    this.microserviceCommunicationService.disconnect(this.client);
  }

  async create(
    createMessageVariantDto: CreateMessageVariantDto,
  ): Promise<TemplateDocument> {
    return await this.microserviceCommunicationService.produceAndWaitForResponse(
      this.client,
      'message-variant.create.request',
      createMessageVariantDto,
    );
  }

  async update(
    updateMessageVariantDto: UpdateMessageVariantDto,
  ): Promise<TemplateDocument> {
    return await this.microserviceCommunicationService.produceAndWaitForResponse(
      this.client,
      'message-variant.update.request',
      updateMessageVariantDto,
    );
  }

  async delete(messageVariantId: Types.ObjectId): Promise<number> {
    return await this.microserviceCommunicationService.produceAndWaitForResponse(
      this.client,
      'message-variant.delete.request',
      { messageVariantId },
    );
  }
}
