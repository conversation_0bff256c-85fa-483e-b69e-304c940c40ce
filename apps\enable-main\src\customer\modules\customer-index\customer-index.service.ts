import {
  CompanyDocument,
  Customer,
  CustomerDocument,
  CustomerSearchType,
  CustomerSortMapping,
  FetchEligibleCustomerForBulkActionDto,
  IndexCustomerDto,
  loyaltyOrderSources,
  OrderStatusEnum,
  parseCommaSeparatedString,
  responseCode,
  StringCustomerSearchTypes,
  YesOrNo,
} from '@app/shared-stuff';
import {
  BadRequestException,
  Inject,
  Injectable,
  UnprocessableEntityException,
} from '@nestjs/common';

import * as moment from 'moment-timezone';
import {
  FilterQuery,
  isObjectIdOrHexString,
  PipelineStage,
  Types,
} from 'mongoose';
import { CalendarSystemService } from '../../../company/services/calendar-system/calendar-system.service';
import { CompanyService } from '../../../company/services/company/company.service';
import { CustomerRepositoryInterface } from '../customer-repository/customer.repository.interface';
import { CustomerIndexServiceInterface } from './customer-index.service.interface';

@Injectable()
export class CustomerIndexService implements CustomerIndexServiceInterface {
  constructor(
    private readonly companyService: CompanyService,
    private readonly calendarSystemService: CalendarSystemService,
    @Inject(CustomerRepositoryInterface)
    private readonly customerRepository: CustomerRepositoryInterface,
  ) {}

  async index(indexCustomerDto: IndexCustomerDto): Promise<unknown[]> {
    const pipeline: PipelineStage[] = [];
    await this.addOrderRateStage(pipeline, indexCustomerDto);
    pipeline.push({ $match: await this.getMatchStage(indexCustomerDto) });
    this.addPaginationStage(pipeline, indexCustomerDto);
    return await this.customerRepository.aggregate(pipeline);
  }

  private async getMatchStage(
    indexCustomerDto: IndexCustomerDto,
  ): Promise<FilterQuery<Customer>> {
    const match: FilterQuery<Customer> = { deletedAt: null };
    if (indexCustomerDto.company)
      await this.matchCompanyId(indexCustomerDto.company, match);

    if (indexCustomerDto.month) match['month'] = indexCustomerDto.month;

    if (indexCustomerDto?.havingLatestPayment == YesOrNo.YES)
      match['latestPayment'] = { $exists: true };

    if (
      indexCustomerDto.contactChannels &&
      indexCustomerDto.contactChannels.length > 0
    )
      match['contact_channel'] = { $in: indexCustomerDto.contactChannels };

    if (
      indexCustomerDto.latestContactChannels &&
      indexCustomerDto.latestContactChannels.length > 0
    )
      match['latestContactChannel'] = {
        $in: indexCustomerDto.latestContactChannels,
      };

    if (
      indexCustomerDto.loyaltyCardStatuses &&
      indexCustomerDto.loyaltyCardStatuses.length > 0
    )
      match['loyaltyCardStatus'] = {
        $in: indexCustomerDto.loyaltyCardStatuses,
      };

    if (
      indexCustomerDto.loyaltyRegistrationBranchIds &&
      indexCustomerDto.loyaltyRegistrationBranchIds.length > 0
    )
      match['loyaltyRegistrationBranchId'] = {
        $in: indexCustomerDto.loyaltyRegistrationBranchIds,
      };

    if (
      indexCustomerDto.lastOrderBefore &&
      moment.utc(indexCustomerDto.lastOrderBefore, 'YYYY-MM-DD', true).isValid()
    )
      match['last_order_date'] = {
        $lt: moment.utc(indexCustomerDto.lastOrderBefore).toDate(),
      };

    const types = parseCommaSeparatedString(indexCustomerDto.search_type);
    const keys = parseCommaSeparatedString(indexCustomerDto.search_key);

    if (types.length > keys.length)
      throw new BadRequestException(
        'Please specify search key for each search type',
      );

    keys.forEach((searchKey, index) => {
      const type = types[index];
      const searchType = this.isSearchType(type)
        ? type
        : CustomerSearchType.ALL;

      if (searchType === CustomerSearchType.ALL && searchKey === '') return;

      const filterQuery = this.parseFilterFor(searchType, searchKey);
      match.$and = [...(match.$and || []), filterQuery];
    });
    return match;
  }

  private isSearchType(type: any): type is CustomerSearchType {
    return Object.values(CustomerSearchType).includes(type);
  }

  private parseFilterFor(
    searchType: CustomerSearchType,
    searchKey: string,
  ): FilterQuery<Customer> {
    const filterMap: Record<
      Exclude<CustomerSearchType, StringCustomerSearchTypes>,
      FilterQuery<Customer>
    > = {
      [CustomerSearchType.ALL]: {
        $or: Object.values(CustomerSearchType).map((attribute) => ({
          [attribute]: {
            $regex: searchKey,
            $options: 'i',
          },
        })),
      },
      [CustomerSearchType.YEAR]: {
        year: { $eq: Number(searchKey) },
      },
      [CustomerSearchType.LOYALTY_TIER]: (() => {
        if (searchKey === 'no_tier') {
          return { loyaltyTier: null };
        } else {
          return {
            'loyaltyTier._id': isObjectIdOrHexString(searchKey)
              ? new Types.ObjectId(searchKey)
              : searchKey,
          };
        }
      })(),
      [CustomerSearchType.LOYALTY_POINTS]: { loyaltyPoints: Number(searchKey) },
      [CustomerSearchType.LOYALTY_POINTS_GT]: {
        loyaltyPoints: { $gte: Number(searchKey) },
      },
      [CustomerSearchType.LOYALTY_POINTS_LT]: {
        loyaltyPoints: { $lte: Number(searchKey) },
      },
      [CustomerSearchType.ORDER_RATE]: { orderRate: Number(searchKey) },
      [CustomerSearchType.ORDER_RATE_GT]: {
        orderRate: { $gte: Number(searchKey) },
      },
      [CustomerSearchType.ORDER_RATE_LT]: {
        orderRate: { $lte: Number(searchKey) },
      },
      [CustomerSearchType.LOYALTY_ORDER_RATE]: {
        loyaltyOrderRate: Number(searchKey),
      },
      [CustomerSearchType.LOYALTY_ORDER_RATE_GT]: {
        loyaltyOrderRate: { $gte: Number(searchKey) },
      },
      [CustomerSearchType.LOYALTY_ORDER_RATE_LT]: {
        loyaltyOrderRate: { $lte: Number(searchKey) },
      },
      [CustomerSearchType.LOYALTY_STATUS]: {
        loyaltyStatus: { $regex: searchKey, $options: 'i' },
      },
    };

    if (searchType in filterMap) return filterMap[searchType];

    return { [searchType]: { $regex: searchKey, $options: 'i' } };
  }

  private async matchCompanyId(
    companyId: Types.ObjectId,
    match: any,
  ): Promise<void> {
    const company = await this.companyService.get_details(
      companyId instanceof Types.ObjectId ? companyId.toHexString() : companyId,
    );
    if (!company)
      throw new UnprocessableEntityException(
        'Company not found!',
        responseCode.MISSING_DATA.toString(),
      );
    match['company'] = new Types.ObjectId(companyId);
  }

  private async addOrderRateStage(
    pipeline: any[],
    { company: companyId }: IndexCustomerDto,
  ) {
    if (!companyId || !isObjectIdOrHexString(companyId)) return;

    const company = await this.companyService.findById(
      new Types.ObjectId(companyId),
    );

    if (
      !company ||
      !company.hasLoyaltyProgram ||
      !company.loyaltyProgramConfig.hasLoyaltyTiers
    )
      return;

    const cutOffDate = this.calendarSystemService.getStartDate(company);

    const orderRateFilters = [
      { $eq: ['$$order.status', OrderStatusEnum.COMPLETED] },
      { $gte: ['$$order.pickup_date', this.toLocalTime(cutOffDate, company)] },
      {
        $gte: [
          '$$order.pickup_date',
          this.toLocalTime('$carryOverUpdatedAt', company),
        ],
      },
    ];

    const loyaltyOrderFilters = [
      ...orderRateFilters,
      { $in: ['$$order.source', loyaltyOrderSources] },
      { $eq: ['$$order.isOrderCounted', true] },
    ];

    const loyaltyOrderRateFilters = [
      ...loyaltyOrderFilters,
      { $eq: ['$$order.isCartValueThresholdMet', true] },
    ];

    const addOrderRates = {
      $addFields: {
        orderRate: this.getFilteredOrdersLength(orderRateFilters),
        loyaltyOrderRate: this.getFilteredOrdersLength(loyaltyOrderRateFilters),
        amountSpent: this.getFilteredOrdersAmountSpent(loyaltyOrderRateFilters),
        pointsRate: this.getFilteredOrdersPointsRate(loyaltyOrderFilters),
      },
    };

    pipeline.push(addOrderRates);
  }

  private toLocalTime(date: string | Date, company: CompanyDocument) {
    return {
      $dateAdd: {
        startDate: date,
        unit: 'minute',
        amount: moment
          .tz(company.localization.timezone || 'Asia/Qatar')
          .utcOffset(),
      },
    };
  }

  private getFilteredOrdersLength(filters) {
    return {
      $size: {
        $ifNull: [
          {
            $filter: {
              input: '$orders',
              as: 'order',
              cond: { $and: filters },
            },
          },
          [],
        ],
      },
    };
  }

  private getFilteredOrdersAmountSpent(loyaltyOrderRateFilters: any[]) {
    return {
      $sum: {
        $map: {
          input: {
            $filter: {
              input: '$orders',
              as: 'order',
              cond: { $and: loyaltyOrderRateFilters },
            },
          },
          as: 'order',
          in: '$$order.total_amount',
        },
      },
    };
  }

  private getFilteredOrdersPointsRate(loyaltyOrderRateFilters: any[]) {
    return {
      $sum: {
        $map: {
          input: {
            $filter: {
              input: '$orders',
              as: 'order',
              cond: { $and: loyaltyOrderRateFilters },
            },
          },
          as: 'order',
          in: '$$order.loyaltyProgress.pointsEarned',
        },
      },
    };
  }

  private getSortStage(indexCustomerDto: IndexCustomerDto): {
    $sort: Record<string, 1 | -1>;
  } {
    if (indexCustomerDto.sort_type in CustomerSortMapping) {
      return { $sort: CustomerSortMapping[indexCustomerDto.sort_type] };
    }

    const defaultSort = { $sort: { createdAt: -1 } } as const;
    return defaultSort;
  }

  private addPaginationStage(
    pipeline: any[],
    indexCustomerDto: IndexCustomerDto,
  ): void {
    pipeline.push({
      $facet: {
        paginatedResult: [
          this.getSortStage(indexCustomerDto),
          ...(Number(indexCustomerDto.offset)
            ? [
                {
                  $skip:
                    Number(indexCustomerDto.offset) *
                    Number(indexCustomerDto.limit),
                },
              ]
            : [
                {
                  $skip: 0,
                },
              ]),
          ...(Number(indexCustomerDto.limit)
            ? [
                {
                  $limit: Number(indexCustomerDto.limit),
                },
              ]
            : []),
        ],
        totalCount: [
          {
            $count: 'createdAt',
          },
        ],
      },
    });
  }

  async indexAsDocuments(
    indexCustomerDto: IndexCustomerDto,
  ): Promise<CustomerDocument[]> {
    return this.customerRepository.findAll(
      await this.getMatchStage(indexCustomerDto),
    );
  }

  async count(indexCustomerDto: IndexCustomerDto): Promise<number> {
    return await this.customerRepository.countDocuments(
      await this.getMatchStage(indexCustomerDto),
    );
  }

  async fetchEligibleCustomersForBulkAction({
    customerSegment,
    customerIds,
    deselectedCustomerIds,
  }: FetchEligibleCustomerForBulkActionDto): Promise<CustomerDocument[]> {
    let customers: CustomerDocument[];
    if (customerIds)
      customers = await this.customerRepository.findManyById(customerIds);
    else if (customerSegment) {
      customers = await this.indexAsDocuments(customerSegment);
      if (deselectedCustomerIds) {
        customers = customers.filter(
          (customer) =>
            !deselectedCustomerIds.includes(customer._id.toHexString()),
        );
      }
    }

    return customers;
  }
}
