import {
  <PERSON><PERSON><PERSON><PERSON>s,
  CacheServiceInterface,
  CapturedItem,
  ContactChannel,
  CountryDialCode,
  CreateCustomerDto,
  CurrentUser,
  CustomerDocument,
  Discount,
  DiscountApplyTo,
  DiscountSource,
  DiscountType,
  fullCacheKeys,
  LookupCustomerBenefitDto,
  mapAsync,
  MenuItemBulkWriteDto,
  MenuItemToCreate,
  MenuWithId,
  OrderItem,
  OrderPaymentMethod,
  sanitizePhoneNumber,
  SavedLocationAddressType,
  SavedLocationToCreate,
  SavedLocationType,
  ShopifyAddress,
  ShopifyCustomer,
  ShopifyDiscountApplicationTargetType,
  ShopifyDiscountApplicationValueType,
  ShopifyDiscountCodeType,
  ShopifyLineItem,
  ShopifyOrder,
  ShopifyPaymentGatewayName,
  ShopifyProduct,
} from '@app/shared-stuff';
import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { createHmac, timingSafeEqual } from 'crypto';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { CouponServiceInterface } from '../../../../coupon/services/coupon.service.interface';
import { CustomerReadServiceInterface } from '../../../../customer/modules/customer-read/customer-read.service.interface';
import { CustomerWriteServiceInterface } from '../../../../customer/modules/customer-write/customer-write.service.interface';
import { MenuItemService } from '../../../../restaurant/services/menu-item/menu-item.service';
import { StoreWithId } from '../../../../store/dto/store-with-id.dto';
import { StoreProvider } from '../../../../store/enumerations/store-provider.enum';
import { Store } from '../../../../store/models/store.model';
import { StoreServiceInterface } from '../../../../store/services/store.service.interface';
import { IntegrationOrder } from '../../../enable/dto/integration-order.dto';
import { ShopifyRequestRepository } from '../repositories/shopify-request.repository';
import parsePhoneNumberFromString from 'libphonenumber-js';

@Injectable()
export class ShopifyService {
  private readonly MISSING_PHONE_COUNTRY_CODE = CountryDialCode.NAURU;

  constructor(
    @Inject('StoreServiceInterface')
    private readonly storeService: StoreServiceInterface,
    @Inject(CustomerReadServiceInterface)
    private readonly customerReadService: CustomerReadServiceInterface,
    @Inject(CustomerWriteServiceInterface)
    private readonly customerWriteService: CustomerWriteServiceInterface,
    @Inject(CouponServiceInterface)
    private readonly couponService: CouponServiceInterface,
    private readonly menuItemService: MenuItemService,
    private readonly shopifyRequestRepository: ShopifyRequestRepository,
    @Inject(CacheServiceInterface)
    private readonly cacheService: CacheServiceInterface,
  ) {}

  public parseGiftDetails(
    store: Store,
    createShopifyOrderDto: ShopifyOrder,
  ): Partial<IntegrationOrder> {
    const notGift = { is_gift: false };
    if (!store.shopifyConfig?.useGiftOrders) return notGift;
    if (!createShopifyOrderDto.customer?.phone) return notGift;
    if (!createShopifyOrderDto.shipping_address?.phone) return notGift;

    const parsedCustomerPhone = parsePhoneNumberFromString(
      createShopifyOrderDto.customer?.phone,
    );
    if (!parsedCustomerPhone) return notGift;
    if (!parsedCustomerPhone.nationalNumber) return notGift;

    const parsedShippingPhone = parsePhoneNumberFromString(
      createShopifyOrderDto.shipping_address?.phone,
      createShopifyOrderDto.shipping_address?.country_code,
    );
    if (!parsedShippingPhone) return notGift;
    if (!parsedShippingPhone.nationalNumber) return notGift;
    if (
      parsedCustomerPhone.nationalNumber === parsedShippingPhone.nationalNumber
    )
      return notGift;

    return {
      is_gift: true,
      recipient_country_code: parsedShippingPhone.countryCallingCode,
      recipient_phone: parsedShippingPhone.nationalNumber,
      recipient_name: createShopifyOrderDto.shipping_address.name,
    };
  }

  public mapPaymentGatewayNames(
    payment_gateway_names: ShopifyPaymentGatewayName[],
  ): { payment_method: OrderPaymentMethod; prepaidBy?: string } {
    const gateway = payment_gateway_names?.length
      ? payment_gateway_names[0]
      : null;

    switch (gateway) {
      case ShopifyPaymentGatewayName.CASH_ON_DELIVERY:
      case ShopifyPaymentGatewayName.CASH_ON_DELIVERY_COD:
        return {
          payment_method: OrderPaymentMethod.cash,
        };
      case ShopifyPaymentGatewayName.CARD_ON_DELIVERY:
        return {
          payment_method: OrderPaymentMethod.card_machine,
        };
      case ShopifyPaymentGatewayName.PAY_WITH_INSURANCE:
        return {
          payment_method: OrderPaymentMethod.prepaid,
          prepaidBy: 'insurance',
        };
      default:
        return {
          payment_method: OrderPaymentMethod.prepaid,
          prepaidBy: 'customer',
        };
    }
  }

  public parseCreateMenuItemDtos(
    product: ShopifyProduct,
    currentUser: CurrentUser,
    menu: MenuWithId,
    category: Types.ObjectId,
  ): MenuItemToCreate[] {
    if (!product.variants?.length)
      throw new BadRequestException(
        'Cannot create shopify product without variants',
      );

    const description = product.body_html
      ? product.body_html.replaceAll(/<[^>]+>/g, '')
      : '';

    return product.variants
      .filter((variant) => variant.sku)
      .map((variant) => ({
        nameAr: product.title,
        nameEn: product.title,
        descriptionEn: description,
        descriptionAr: description,
        code: variant.sku,
        menuCategory: category ? category._id : null,
        plu: variant.sku,
        reference: variant.sku,
        price: +variant.price,
        available: true,
        showOnWeb: true,
        showInPos: true,
        calories: 0,
        tags: product.tags ? product.tags.split(',') : [],
        type: 'regular',
        menu: menu._id,
        menuGroups: [],
        subItems: [],
        images: [],
        externalImage: product.image?.src,
        company: menu.company._id,
        createdBy: currentUser,
        availabilities: [],
        isScheduledAvailabilityActive: false,
        integrationInfo: {
          shopify: {
            productId: product.id,
            variantId: variant.id,
          },
        },
      }));
  }

  public async parseMenuItemBulkWriteDto(
    product: ShopifyProduct,
    currentUser: CurrentUser,
    menu: MenuWithId,
    category: Types.ObjectId,
  ): Promise<MenuItemBulkWriteDto> {
    if (!product.variants?.length)
      throw new BadRequestException(
        'Cannot update shopify product without variants',
      );

    const createMenuItemDtos = this.parseCreateMenuItemDtos(
      product,
      currentUser,
      menu,
      category,
    );

    const skuToIdMap = await this.menuItemService.getSkuToIdMap(
      menu._id,
      category?.toString(),
      product.variants.map((variant) => variant.sku),
    );

    const menuItemBulkWriteDto: MenuItemBulkWriteDto = {
      createMenuItemDtos: [],
      updateMenuItemDtos: [],
    };

    for (let i = 0; i < createMenuItemDtos.length; i++) {
      const menuItem = createMenuItemDtos[i];

      if (menuItem.plu in skuToIdMap)
        menuItemBulkWriteDto.updateMenuItemDtos.push({
          ...menuItem,
          _id: skuToIdMap[menuItem.plu],
          menuGroups: [],
          images: [],
          updatedBy: currentUser,
        });
      else menuItemBulkWriteDto.createMenuItemDtos.push(menuItem);
    }

    return menuItemBulkWriteDto;
  }

  public async findShopifyStore(storeId: Types.ObjectId) {
    const key = fullCacheKeys[CacheKeys.STORE](storeId);
    const storeCache = await this.cacheService.getCache(key, StoreWithId);
    const store = storeCache || (await this.storeService.findById(storeId));

    if (store.provider !== StoreProvider.SHOPIFY)
      throw new BadRequestException('Store not configured for Shopify.');
    if (!store.shopifyConfig?.webhookSigningKey)
      throw new BadRequestException(
        'Store not configured with webhook signing key.',
      );

    if (!storeCache) await this.cacheService.setCache(key, store);

    return store;
  }

  public async calculateHash(storeId: Types.ObjectId, rawBody: Buffer) {
    const store = await this.findShopifyStore(storeId);

    const hash = createHmac('sha256', store.shopifyConfig.webhookSigningKey)
      .update(rawBody)
      .digest('base64');

    return hash;
  }

  async validateWebhook(
    storeId: Types.ObjectId,
    hash: string,
    rawBody: Buffer,
    requestId: string,
  ) {
    if (!hash) return new BadRequestException('Payload hash must be provided.');

    const calculatedHash = await this.calculateHash(storeId, rawBody);

    if (!timingSafeEqual(Buffer.from(calculatedHash), Buffer.from(hash)))
      return new UnauthorizedException(
        `Digest does not match. Please ensure correct webhook signing key is configured for this store.`,
      );

    const cacheKey = fullCacheKeys[CacheKeys.SHOPIFY_REQUEST](requestId);
    const cachedRequest = await this.cacheService.getCache(cacheKey, Date);

    if (cachedRequest) {
      return { statusCode: 200, message: 'Duplicate webhook' };
    }

    try {
      await this.shopifyRequestRepository.create({
        requestId,
        createdAt: moment.utc().toDate(),
      });

      await this.cacheService.setCache(cacheKey, new Date(), 60 * 60 * 24);
    } catch (error) {
      if (error && 'code' in error && error.code === 11000)
        return { statusCode: 200, message: 'Duplicate webhook' };
      return error;
    }
  }

  public getShopifyCustomerPhone(
    createShopifyOrderDto: ShopifyOrder,
    isCustomerPhonePriority: boolean,
  ) {
    const customerPhone = createShopifyOrderDto.customer?.phone;
    const shippingPhone = createShopifyOrderDto.shipping_address?.phone;

    if (isCustomerPhonePriority && customerPhone) return customerPhone;

    return shippingPhone ?? customerPhone ?? createShopifyOrderDto.phone;
  }

  public async getShopifyCustomer(
    companyId: Types.ObjectId,
    shopifyCustomer: ShopifyCustomer,
    currentUser: CurrentUser,
  ): Promise<CustomerDocument> {
    if (shopifyCustomer.phone) {
      const { phone, countryCode } = sanitizePhoneNumber(shopifyCustomer.phone);
      try {
        const customer = await this.customerReadService.findByPhoneAndCompanyId(
          phone,
          companyId,
          countryCode,
        );
        return customer;
      } catch (exception: unknown) {
        if (exception instanceof NotFoundException) {
          return await this.customerWriteService.create(
            this.getCustomerCreateDto(
              shopifyCustomer,
              countryCode,
              phone,
              companyId,
              currentUser,
            ),
            currentUser,
          );
        }
      }
    } else {
      if (shopifyCustomer.email) {
        const customer = await this.customerReadService.findByEmail(
          companyId,
          shopifyCustomer.email,
        );
        if (customer) return customer;
      }

      const countryCode = this.MISSING_PHONE_COUNTRY_CODE;
      const phone = await this.generateRandomPhoneNumber(companyId);

      return await this.customerWriteService.create(
        this.getCustomerCreateDto(
          shopifyCustomer,
          countryCode,
          phone,
          companyId,
          currentUser,
        ),
        currentUser,
      );
    }
  }

  private async generateRandomPhoneNumber(
    companyId: Types.ObjectId,
  ): Promise<string> {
    let phone: string;
    do {
      phone =
        '8' +
        Math.floor(Math.random() * 999999)
          .toString()
          .padStart(6, '0');
    } while (await this.isPhoneNumberTaken(phone, companyId));

    return phone;
  }

  private async isPhoneNumberTaken(
    phone: string,
    companyId: Types.ObjectId,
  ): Promise<boolean> {
    try {
      const customer = await this.customerReadService.findByPhoneAndCompanyId(
        phone,
        companyId,
        this.MISSING_PHONE_COUNTRY_CODE,
        { phone: 1 },
      );

      return !!customer;
    } catch (error) {
      if (error instanceof NotFoundException) return false;
      throw error;
    }
  }

  public createSavedLocationToCreate(
    address: ShopifyAddress,
  ): SavedLocationToCreate {
    return {
      type: SavedLocationType.PIN_LOCATION,
      addressType: SavedLocationAddressType.HOUSE,
      nickname: address.name,
      latitude: address.latitude,
      longitude: address.longitude,
      country: address.country,
      city: address.city,
      area: address.province,
      buildingName: (address.address1 + ' ' + address.address2).trim(),
    };
  }

  public convertShopifyLineItemToOrderItem(
    lineItem: ShopifyLineItem,
  ): OrderItem {
    const totalAmount = +lineItem.price * lineItem.quantity;
    const discount = lineItem.discount_allocations.reduce(
      (sum, discount) => sum + +discount.amount,
      0,
    );
    const totalAmountAfterDiscount = totalAmount - discount;
    return {
      name: lineItem.name,
      basePrice: +lineItem.price,
      price: +lineItem.price,
      quantity: lineItem.quantity,
      plu: lineItem.sku,
      itemReference: lineItem.sku,
      totalAmount: totalAmount,
      discount,
      totalAmountAfterDiscount,
    };
  }

  public async convertShopifyLineItemsToCapturedItems(
    lineItems: ShopifyLineItem[],
    companyId: Types.ObjectId,
  ): Promise<CapturedItem[]> {
    const capturedItems: CapturedItem[] = [];

    for (let i = 0; i < lineItems.length; i++) {
      const lineItem = lineItems[i];
      const realItem = await this.menuItemService.getDetails(
        lineItem.sku,
        undefined,
        companyId,
      );

      if (realItem)
        capturedItems.push({ _id: realItem._id, quantity: lineItem.quantity });
    }

    return capturedItems;
  }

  public async convertDiscounts(
    shopifyOrderCreateDto: ShopifyOrder,
    customer: CustomerDocument,
  ): Promise<Pick<IntegrationOrder, 'discounts' | 'benefits'>> {
    const deliveryRedemptions = await this.convertDeliveryDiscounts(
      shopifyOrderCreateDto,
      customer,
    );

    const itemRedemptions = await this.convertItemDiscounts(
      shopifyOrderCreateDto,
      customer,
    );

    const redemptions = [...deliveryRedemptions, ...itemRedemptions];

    const discounts = [];
    const benefits = [];

    for (let i = 0; i < redemptions.length; i++) {
      const redemption = redemptions[i];

      if ('benefitId' in redemption) benefits.push(redemption);
      else discounts.push(redemption);
    }

    return { discounts, benefits };
  }

  private async convertDeliveryDiscounts(
    shopifyOrderCreateDto: ShopifyOrder,
    customer: CustomerDocument,
  ): Promise<(Discount | LookupCustomerBenefitDto)[]> {
    if (!shopifyOrderCreateDto.discount_applications?.length) return [];

    const deliveryDiscounts =
      shopifyOrderCreateDto.discount_applications.filter(
        ({ target_type }) =>
          target_type === ShopifyDiscountApplicationTargetType.SHIPPING_LINE,
      );

    return mapAsync(deliveryDiscounts, ({ value_type, value, code }) =>
      this.matchDiscountCode(code, customer, {
        type:
          value_type === ShopifyDiscountApplicationValueType.FIXED_AMOUNT
            ? DiscountType.FLAT
            : DiscountType.PERCENTAGE,
        amount: +value,
        source: DiscountSource.SHOPIFY,
        applyTo: DiscountApplyTo.DELIVERY,
      }),
    );
  }

  private async convertItemDiscounts(
    shopifyOrderCreateDto: ShopifyOrder,
    customer: CustomerDocument,
  ): Promise<(Discount | LookupCustomerBenefitDto)[]> {
    if (!shopifyOrderCreateDto.line_items?.length) return [];

    const redemptions = await mapAsync(
      shopifyOrderCreateDto.line_items,
      (lineItem) =>
        mapAsync(lineItem.discount_allocations, async ({ amount }) =>
          this.matchDiscountCode(
            shopifyOrderCreateDto.discount_codes.find(
              (discountCode) =>
                (discountCode.type === ShopifyDiscountCodeType.FIXED_AMOUNT &&
                  discountCode.amount === amount) ||
                (discountCode.type === ShopifyDiscountCodeType.PERCENTAGE &&
                  +discountCode.amount ===
                    (+lineItem.total_discount / +lineItem.price) * 100),
            )?.code,
            customer,
            {
              type: DiscountType.FLAT,
              amount: +amount,
              source: DiscountSource.SHOPIFY,
              applyTo: DiscountApplyTo.MENU_ITEM,
              menuItemId: lineItem.sku,
            },
          ),
        ),
    );
    return redemptions.flat();
  }

  private async matchDiscountCode(
    code: string,
    customer: CustomerDocument,
    defaultDiscount: Discount | LookupCustomerBenefitDto,
  ): Promise<Discount | LookupCustomerBenefitDto> {
    if (!code) return defaultDiscount;

    const benefit = (customer.earnedBenefits || []).find(
      (benefit) => benefit.config.shopifyDiscountCode === code,
    );
    if (benefit) return { benefitId: benefit._id, source: benefit.source };

    if (customer.loyaltyTier?.freeDeliveryShopifyDiscountCode === code)
      return Discount.fromTierFreeDelivery(customer.loyaltyTier);

    if (customer.loyaltyTier?.percentDiscountShopifyDiscountCode === code)
      return Discount.fromTierPercentDiscount(customer.loyaltyTier);

    const reward = (customer.rewards || []).find(
      (reward) => reward.shopifyDiscountCode === code,
    );
    if (reward) return Discount.fromReward(reward);

    const coupon =
      await this.couponService.findByShopifyDiscountCodeForCustomer(
        code,
        customer,
      );
    if (coupon) return Discount.fromCoupon(coupon);

    return defaultDiscount;
  }

  private getCustomerCreateDto(
    customer: ShopifyCustomer,
    country_code: CountryDialCode,
    phone: string,
    companyId: Types.ObjectId,
    currentUser: CurrentUser,
  ): CreateCustomerDto {
    return {
      contact_channel: ContactChannel.SHOPIFY,
      country_code,
      phone,
      email: customer.email,
      first_name: customer.first_name,
      last_name: customer.last_name,
      company: companyId,
      createdBy: currentUser,
    };
  }

  public getCurrentUserForStore(store: Store): CurrentUser {
    return {
      id: store.enableApiKey._id,
      name: 'Shopify Store API Key',
      phone: '',
      role: store.enableApiKey.roles[0].toString(),
      type: 'ApiKey',
    };
  }
}
