import {
  CollectionName,
  GenericRepository,
  LoggerService,
  PassConfig,
  PassConfigDocument,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';

@Injectable()
export class PassConfigRepository extends GenericRepository<
  PassConfigDocument,
  PassConfig
> {
  private readonly logger = new LoggerService(PassConfigRepository.name);
  constructor(
    @InjectModel(CollectionName.PASS_CONFIG)
    private readonly passConfigModel: Model<PassConfigDocument, PassConfig>,
  ) {
    super(passConfigModel);
  }

  public async findByOwnerId(
    ownerId: Types.ObjectId,
  ): Promise<PassConfigDocument> {
    if (!ownerId) return null;
    return this.passConfigModel.findOne({ 'owner._id': ownerId });
  }

  public async isExistingOwnerId(ownerId: Types.ObjectId): Promise<boolean> {
    const passConfigId = await this.passConfigModel.exists({
      'owner._id': ownerId,
      deletedAt: null,
    });
    return !!passConfigId;
  }
}
