import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BrandModule } from '../brand/brand.module';
import { CompanyModule } from '../company/company.module';
import { CustomerTokenModule } from '../customer/modules/customer-token/customer-token.module';
import { CustomerWriteModule } from '../customer/modules/customer-write/customer-write.module';
import { OrdableModule } from '../integration/webstore/ordable/ordable.module';
import { NotificationModule } from '../notification/notification.module';
import { OTPController } from './controllers/otp.controller';
import { OTP, OTPSchema } from './models/otp.model';
import { OTPRepository } from './repositories/otp.repository';
import { OTPRepositoryInterface } from './repositories/otp.repository.interface';
import { OTPService } from './services/otp.service';
import { OTPServiceInterface } from './services/otp.service.interface';

@Module({
  imports: [
    NotificationModule,
    BrandModule,
    MongooseModule.forFeature([{ name: OTP.name, schema: OTPSchema }]),
    OrdableModule,
    CompanyModule,
    CustomerWriteModule,
    CustomerTokenModule,
  ],
  controllers: [OTPController],
  providers: [
    {
      provide: OTPRepositoryInterface,
      useClass: OTPRepository,
    },
    {
      provide: OTPServiceInterface,
      useClass: OTPService,
    },
  ],
  exports: [],
})
export class OtpModule {}
