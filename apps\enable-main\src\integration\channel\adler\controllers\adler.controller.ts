import {
  Body,
  Controller,
  HttpCode,
  Post,
  Req,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';

import {
  GenericExceptionFilter,
  responseCode,
  TransformInterceptor,
} from '@app/shared-stuff';
import { IntegrationLogInterceptor } from '../../../integration-log/interceptors/integration-log.interceptor';
import { AdlerBranchToSync, AdlerMenuToSync } from '../dto/adler.dto';
import { AdlerService } from '../services/adler.service';

@Controller('adler')
@UseInterceptors(IntegrationLogInterceptor, TransformInterceptor)
@UseFilters(GenericExceptionFilter)
@ApiTags('Adler Controller')
@SetMetadata('module', 'adler')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class AdlerController {
  constructor(private adlerService: AdlerService) {}

  @Post('outlet')
  @SetMetadata('action', 'synBranchesWithAdler')
  @HttpCode(responseCode.SUCCESS_TO_SYNC)
  async syncBranches(
    @Body() adlerBranchToSync: AdlerBranchToSync,
    @Req() req: Request,
  ) {
    adlerBranchToSync.companyId = req['company_id']
      ? req['company_id']
      : adlerBranchToSync.companyId;

    return await this.adlerService.syncBranchesWithAdlerOutlet(
      adlerBranchToSync,
    );
  }

  @Post('menu')
  @SetMetadata('action', 'syndMenuItemsWithAdler')
  @HttpCode(responseCode.SUCCESS_TO_SYNC)
  async syncMenuItems(
    @Body() adlerMenuToSync: AdlerMenuToSync,
    @Req() req: Request,
  ) {
    adlerMenuToSync.companyId = req['company_id']
      ? req['company_id']
      : adlerMenuToSync.companyId;

    return await this.adlerService.syncMenuItemsWithAdler(adlerMenuToSync);
  }
}
