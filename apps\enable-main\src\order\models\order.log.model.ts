import { OrderLogReceivedObjectDto } from '@app/shared-stuff/dtos/order/order-log-received-object.dto';
import { OrderLogSentObjectDto } from '@app/shared-stuff/dtos/order/order-log-sent-object.dto';
import { OrderLogActionEnum } from '@app/shared-stuff/enums/order/order-log-action.enum';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type OrderLogDocument = HydratedDocument<OrderLog>;
@Schema({ timestamps: true })
export class OrderLog {
  @Prop({
    type: {},
    required: false,
  })
  sentObject: OrderLogSentObjectDto;

  @Prop({
    type: {},
    required: false,
  })
  receivedObject: OrderLogReceivedObjectDto;

  @Prop({
    type: String,
    required: false,
    enum: OrderLogActionEnum,
    index: true,
  })
  logAction: OrderLogActionEnum;

  @Prop({
    type: String,
    required: false,
    index: true,
  })
  orderCode: string;

  @Prop({
    type: {},
    required: false,
  })
  createdBy: {};

  @Prop({
    type: Date,
    required: false,
  })
  deletedAt: Date;
}

export const OrderLogSchema = SchemaFactory.createForClass(OrderLog);
