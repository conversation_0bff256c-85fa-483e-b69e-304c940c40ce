import { LogError, OrderDocument } from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { CompanyDocument } from '@app/shared-stuff/models/company.model';
import { DeliverectPosService } from '../services/deliverect-pos.service';

@Injectable()
export class DeliverectPosListener {
  constructor(private deliverectPosService: DeliverectPosService) {}

  @OnEvent('menuItem.updatedOrCreated')
  @LogError()
  async onMenuItemCreatedOrUpdated(menuItemId: string) {
    await this.deliverectPosService.syncMenuItem(menuItemId);
  }

  @OnEvent('company.deliverect.updated')
  @LogError()
  async onCompanyUpdated(company: CompanyDocument) {
    await this.deliverectPosService.syncIntegratedChannels(company);
  }

  @OnEvent('order.status.updated')
  @LogError()
  async onOrderStatusUpdated(order: OrderDocument) {
    if (!order.deliverectOrder || !order.deliverectPosOrderId) return;

    this.deliverectPosService.syncOrderStatus(order);
  }
}
