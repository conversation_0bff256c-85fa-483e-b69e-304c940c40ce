import { LogError, LoggerService, RegisteredPass } from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { PassesRefreshService } from '../passes-refresh/passes-refresh.service';
import { OnEvent } from '@nestjs/event-emitter';

@Injectable()
export class PassesCronService {
  private readonly loggerService = new LoggerService(PassesCronService.name);
  private passesToRefresh: RegisteredPass[] = [];
  private PASSES_PER_BATCH = 150;

  constructor(private readonly passesRefreshService: PassesRefreshService) {}

  public queuePassRefreshes(passes: RegisteredPass[]) {
    this.passesToRefresh.push(...(passes || []));
  }

  @OnEvent('cron.every30secs')
  @LogError()
  public async runRefreshBatch() {
    const processBatch = async () => {
      const passes = this.passesToRefresh.splice(0, this.PASSES_PER_BATCH);
      this.loggerService.log(
        `[PassesCron] Refreshing ${passes.length}/${this.passesToRefresh.length + passes.length} passes`,
      );
      await this.passesRefreshService.updatePasses(passes);
    };

    await processBatch();

    setTimeout(() => {
      void processBatch();
    }, 30_000);
  }
}
