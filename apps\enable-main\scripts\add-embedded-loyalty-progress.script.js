// EBL-5088 Fixed & Relative Calendar Systems | Tiers Program
// Add `loyaltyProgress.pointsEarned` property to customer's embedded order dto

const writes = db.loyaltypointlogs
  .find(
    { action: 'ON_ORDER_COMPLETED' },
    { orderId: 1, loyaltyPointsEarned: 1 },
  )
  .toArray()
  .map(({ orderId, loyaltyPointsEarned }) => ({
    updateOne: {
      filter: { 'orders._id': orderId },
      update: {
        $set: {
          'orders.$.loyaltyProgress.pointsEarned': loyaltyPointsEarned,
        },
      },
    },
  }));

db.customers.bulkWrite(writes, { ordered: false });
