// EBL-4953 Multi-Tracks | Punch Card
// Migrate existing completed punch cards progress into their own collection

db.customers
  .find({ 'punchCardProgress.completedAt': { $exists: true } })
  .forEach((customer) => {
    db.completedpunchcards.insertMany(
      customer.punchCardProgress
        .filter((p) => p.completedAt)
        .map((p) => ({
          customerId: customer._id,
          companyId: customer.company,
          ...p,
        })),
    );
    db.customers.updateOne(
      { _id: customer._id },
      { $pull: { punchCardProgress: { completedAt: { $exists: true } } } },
    );
  });
