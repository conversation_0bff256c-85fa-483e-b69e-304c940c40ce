// EBL-4519 Punch Card v1.1 - Support Menu Items
// Migrate old `punchCard.counterType` to `punchCard.counter.type`

db.punchcards.updateMany({ counterType: { $exists: true } }, [
  { $set: { 'counter.type': '$counterType' } },
  { $unset: ['counterType'] },
]);

db.customers.bulkWrite(
  db.customers
    .find(
      { 'punchCardProgress.punchCard.counterType': { $exists: true } },
      { punchCardProgress: 1 },
    )
    .toArray()
    .map(({ _id, punchCardProgress }) => ({
      updateOne: {
        filter: { _id },
        update: {
          $set: {
            punchCardProgress: punchCardProgress.map((progress) => ({
              ...progress,
              punchCard: {
                _id: progress.punchCard._id,
                achievements: progress.punchCard.achievements,
                counter: { type: progress.punchCard.counterType },
              },
            })),
          },
        },
      },
    })),
  { ordered: false },
);
