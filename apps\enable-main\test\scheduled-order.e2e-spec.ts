import {
  Customer,
  OrderDeliveryType,
  OrderDocument,
  OrderStatusEnum,
  responseCode,
} from '@app/shared-stuff';
import { CompanyDocument } from '@app/shared-stuff/models/company.model';
import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import * as moment from 'moment-timezone';
import { DeleteResult } from 'mongodb';
import * as request from 'supertest';
import TestAgent from 'supertest/lib/agent';
import { EnableMainModule } from '../src/enable-main.module';
import { OrderCronJobService } from '../src/order/services/order-cron-job/order-cron-job.service';
import {
  generateRandomCompany,
  generateRandomCustomer,
  generateRandomOrder,
} from './generators';
import { mockClientKafka } from './mocks/client-kafka.mock';

jest.mock('puppeteer', () => {
  const launch = jest.fn().mockResolvedValue({
    newPage: jest.fn().mockResolvedValue({
      setContent: jest.fn(),
      pdf: jest.fn().mockResolvedValue(Buffer.from('')),
      close: jest.fn(),
    }),
    close: jest.fn(),
  });

  return { launch };
});
// app init takes around 10 seconds, app close takes around 8 seconds
// so the default timeout of 5 seconds needs to be increased
jest.setTimeout(15 * 1000);
describe('Scheduled Restaurant Order Scenario (e2e)', () => {
  let app: INestApplication;
  let agent: TestAgent<request.Test>;
  let token: string;
  let company: CompanyDocument;
  let customer: Customer;
  let order: OrderDocument;

  const post = (url) =>
    agent
      .post(url)
      .set('Authorization', `Bearer ${token}`)
      .set('x-access-token', `Bearer ${token}`)
      .set('Accept', 'application/json');

  const get = (url) =>
    agent
      .get(url)
      .set('Authorization', `Bearer ${token}`)
      .set('x-access-token', `Bearer ${token}`)
      .set('Accept', 'application/json');

  const del = (url) =>
    agent
      .delete(url)
      .set('Authorization', `Bearer ${token}`)
      .set('x-access-token', `Bearer ${token}`)
      .set('Accept', 'application/json');

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [EnableMainModule],
    })
      .overrideProvider('enable-main-notification-producer')
      .useValue(mockClientKafka)
      .overrideProvider('enable-main-reporting-producer')
      .useValue(mockClientKafka)
      .overrideProvider('enable-main-shorten-producer')
      .useValue(mockClientKafka)
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    agent = request(app.getHttpServer());
  });

  afterAll(async () => {
    await app.close();
  });

  it('logs in as super admin', () =>
    post('/auth/login')
      .send({
        email: process.env.ENABLE_TEST_USER_EMAIL,
        password: process.env.ENABLE_TEST_USER_PASSWORD,
      })
      .expect(200)
      .then((res) => {
        const { code, data } = res.body;
        expect(code).toBe(responseCode.SUCCESS_TO_LOGIN);
        expect(data.token).toBeDefined();
        token = data.token;
      }));

  it('creates a company', () =>
    post('/company')
      .send(generateRandomCompany())
      .expect(200)
      .then((res) => {
        const { code, data }: { code: number; data: CompanyDocument } =
          res.body;
        expect(code).toBe(responseCode.SUCCESS_TO_CREATE);
        expect(data).toBeDefined();
        expect(data._id).toBeDefined();
        company = data;
      }));

  it('logs out as super admin', () =>
    post('/auth/logout')
      .expect(200)
      .then((res) =>
        expect(res.body.code).toBe(responseCode.SUCCESS_TO_LOGOUT),
      ));

  it('logs in as company default user', () =>
    post('/auth/login')
      .send({
        email: company.email,
        password: process.env.ENABLE_TEST_DEFAULT_USER_PASSWORD,
      })
      .expect(200)
      .then((res) => {
        const { code, data } = res.body;
        expect(code).toBe(responseCode.SUCCESS_TO_LOGIN);
        expect(data.token).toBeDefined();
        token = data.token;
      }));

  it('creates a customer', () =>
    post('/customer')
      .send(generateRandomCustomer(company._id))
      .expect(200)
      .then((res) => {
        const { code, data } = res.body;
        expect(code).toBe(responseCode.SUCCESS_TO_CREATE);
        expect(data).toBeDefined();
        expect(data._id).toBeDefined();
        customer = data;
      }));

  it('creates an order', () =>
    post('/order')
      .send(
        generateRandomOrder(customer, {
          delivery_type: OrderDeliveryType.scheduled,
          delivery_date: moment.utc().format('YYYY-MM-DD'),
          delivery_time: moment.utc().add(1, 'hour').format('HH:mm'),
        }),
      )
      .expect(200)
      .then((res) => {
        const { code, data } = res.body;
        expect(code).toBe(responseCode.SUCCESS_TO_CREATE);
        expect(data).toBeDefined();
        expect(data._id).toBeDefined();
        expect(data.status).toBe(OrderStatusEnum.PENDING);
        order = data;
      }));

  it('acknowledges the order', () =>
    post('/order/notification/acknowledge')
      .send({
        orderId: order._id,
      })
      .expect(200)
      .then((res) => {
        const { code, data } = res.body;
        expect(code).toBe(200);
        expect(data).toBeDefined();
        expect(data.order).toBeDefined();
        expect(data.order.status).toBe(OrderStatusEnum.SCHEDULED);
        expect(data.order.isOperable).toBeFalsy();
      }));

  it('makes the order operable', async () => {
    const prevInstance = process.env.NODE_APP_INSTANCE;
    process.env.NODE_APP_INSTANCE = '3';
    await app.get(OrderCronJobService).handleScheduledOrders();
    process.env.NODE_APP_INSTANCE = prevInstance;

    await get(`/order/${order._id}`)
      .expect(200)
      .then((res) => {
        const { code, data } = res.body;
        expect(code).toBe(responseCode.SUCCESS_TO_GETDETAILS);
        expect(data).toBeDefined();
        expect(data.status).toBe(OrderStatusEnum.PREPARING);
        expect(data.isOperable).toBeTruthy();
      });
  });

  it('marks the order as ready', () =>
    post('/order/ready')
      .send({
        order_id: order._id,
      })
      .expect(200)
      .then((res) => {
        const { code, data } = res.body;
        expect(code).toBe(responseCode.SUCCESS_TO_UPDATE);
        expect(data).toBeDefined();
        expect(data.status).toBe(OrderStatusEnum.PENDING_PICKUP);
      }));

  it('marks order as completed', () =>
    post('/order/status')
      .send({
        _id: order._id,
        status: OrderStatusEnum.COMPLETED,
        comment: 'e2etest',
      })
      .expect(200)
      .then((res) => {
        const { code, data } = res.body;
        expect(code).toBe(responseCode.SUCCESS_TO_UPDATE);
        expect(data).toBeDefined();
        expect(data.status).toBe(OrderStatusEnum.COMPLETED);
      }));

  it('cleans up all documents', () =>
    del(`/admin/${company._id}/hardDelete`)
      .send({ code: process.env.HARD_DELETE_CODE })
      .expect(200)
      .then((res) => {
        const {
          code,
          data: deleteResults,
        }: { code: number; data: DeleteResult[] } = res.body;

        const [
          companiesDeleted,
          configsDeleted,
          rolesDeleted,
          branchesDeleted,
          brandsDeleted,
          customersDeleted,
          savedLocationsDeleted,
          orderLogsDeleted,
          ordersDeleted,
          orderItemsDeleted,
          usersDeleted,
        ]: number[] = deleteResults.map(
          (deleteResult: DeleteResult) => deleteResult.deletedCount,
        );

        deleteResults.forEach((deleteResult: DeleteResult) =>
          expect(deleteResult.acknowledged).toBe(true),
        );
        expect(companiesDeleted).toBe(1);
        expect(configsDeleted).toBe(1);
        expect(rolesDeleted).toBeGreaterThan(1);
        expect(branchesDeleted).toBe(1);
        expect(brandsDeleted).toBe(1);
        expect(customersDeleted).toBe(1);
        expect(savedLocationsDeleted).toBe(0);
        expect(orderLogsDeleted).toBeGreaterThan(1);
        expect(ordersDeleted).toBe(1);
        expect(orderItemsDeleted).toBe(1);
        expect(usersDeleted).toBe(1);
        expect(code).toBe(responseCode.SUCCESS_TO_REMOVE);
      }));
});
