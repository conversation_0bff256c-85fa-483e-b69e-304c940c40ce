import { Module } from '@nestjs/common';
import { CustomerRepositoryModule } from '../customer-repository/customer-repository.module';
import { CustomerReadService } from './customer-read.service';
import { CustomerReadServiceInterface } from './customer-read.service.interface';

@Module({
  providers: [
    {
      provide: CustomerReadServiceInterface,
      useClass: CustomerReadService,
    },
  ],
  imports: [CustomerRepositoryModule],
  exports: [CustomerReadServiceInterface],
})
export class CustomerReadModule {}
