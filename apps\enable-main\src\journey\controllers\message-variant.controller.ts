import {
  CreateMessageVariantDto,
  GenericExceptionFilter,
  Journey,
  MessageVariantIdDto,
  TemplateDocument,
  TransformInterceptor,
  UpdateMessageVariantDto,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Inject,
  Param,
  Patch,
  Post,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { MessageVariantServiceInterface } from '../services/message-variant/message-variant.service.interface';

@Controller('message-variant')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags('MessageVariant')
@SetMetadata('module', 'messagevariant')
export class MessageVariantController {
  constructor(
    @Inject(MessageVariantServiceInterface)
    private readonly messageVariantService: MessageVariantServiceInterface,
  ) {}

  @Post()
  @ApiOkResponse({ type: Journey })
  @SetMetadata('action', 'create')
  async create(
    @Body() createMessageVariantDto: CreateMessageVariantDto,
  ): Promise<TemplateDocument> {
    return await this.messageVariantService.create(createMessageVariantDto);
  }

  @Patch(':messageVariantId')
  @ApiOkResponse({ type: Journey })
  @SetMetadata('action', 'update')
  async update(
    @Param() { messageVariantId }: MessageVariantIdDto,
    @Body()
    updateMessageVariantDto: Omit<UpdateMessageVariantDto, 'messageVariantId'>,
  ): Promise<TemplateDocument> {
    return await this.messageVariantService.update({
      messageVariantId,
      ...updateMessageVariantDto,
    });
  }

  @Delete(':messageVariantId')
  @ApiOkResponse({ type: Journey })
  @SetMetadata('action', 'delete')
  async delete(@Param() { messageVariantId }: MessageVariantIdDto) {
    return await this.messageVariantService.delete(messageVariantId);
  }
}
