import { faker } from '@faker-js/faker';
import {
  CreateCompanyDto,
  CompanyType,
  AcknowledgementType,
  CountryDialCode,
} from '@app/shared-stuff';

export const generateRandomCompany = (
  override: Partial<CreateCompanyDto> = {},
): Partial<CreateCompanyDto> => {
  const companyAcronym = faker.string.alpha(5);
  const companyName = 'testSuite' + companyAcronym;
  return {
    name: companyName,
    acronym: companyAcronym,
    email: `${companyName}@testmail.com`,
    phone: faker.string.numeric('########'),
    countryCode: CountryDialCode.QATAR,
    website: `https://www.${companyName}.com`,
    type: CompanyType.RESTAURANT,
    has_delivery_system: true,
    acknowledgementScreenType: AcknowledgementType.MANUAL,
    defaultUserPassword: process.env.ENABLE_TEST_DEFAULT_USER_PASSWORD,
    ...override,
  };
};
