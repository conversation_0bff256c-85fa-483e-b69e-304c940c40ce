import {
  CollectionName,
  CompanyDocument,
  LocationItem,
  LocationItemDocumentToEmbedded,
  LocationItemType,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@Injectable()
export class CompanyListener {
  constructor(
    @InjectModel(CollectionName.COMPANY)
    private companyModel: Model<CompanyDocument>,
  ) {}

  @OnEvent('locationItem.updated')
  async updateEmbeddedLocationItem(locationItem: LocationItem) {
    if (locationItem.type !== LocationItemType.COUNTRY) return;

    const embeddedLocationitem = LocationItemDocumentToEmbedded([
      locationItem,
    ])[0];

    await this.companyModel.updateMany(
      { 'localization.country': locationItem.name },
      { $set: { 'localization.countryItem': embeddedLocationitem } },
    );
  }
}
