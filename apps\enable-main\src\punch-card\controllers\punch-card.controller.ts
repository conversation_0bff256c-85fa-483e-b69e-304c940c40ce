import {
  <PERSON><PERSON>rl<PERSON><PERSON><PERSON>,
  CreatePunchCardAchievementDto,
  CreatePunchCardDto,
  GenericExceptionFilter,
  IndexPunchCardsDto,
  PunchCardIdDto,
  TransformInterceptor,
  UpdatePunchCardAchievementDto,
  UpdatePunchCardDto,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  SetMetadata,
  UnauthorizedException,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { CurrentUserService } from '../../shared/services/current-user/current-user.service';
import { PunchCardAchievementService } from '../modules/punch-card-achievement/punch-card-achievement.service';
import { PunchCardReadService } from '../modules/punch-card-read/punch-card-read.service';
import { PunchCardWriteService } from '../modules/punch-card-write/punch-card-write.service';

@Controller('punch-card')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags('PunchCard')
@SetMetadata('module', 'punch-card')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class PunchCardController {
  constructor(
    private readonly readService: PunchCardReadService,
    private readonly writeService: PunchCardWriteService,
    private readonly achievementSerice: PunchCardAchievementService,
    private readonly currentUserService: CurrentUserService,
  ) {}

  @Post()
  @SetMetadata('action', 'create')
  async create(@Body() createPunchCardDto: CreatePunchCardDto) {
    const companyId = this.currentUserService.getCurrentCompanyId();
    if (!companyId)
      throw new UnauthorizedException('Current user does not have a company.');
    return await this.writeService.create({
      ...createPunchCardDto,
      companyId,
    });
  }

  @Patch(':punchCardId')
  @SetMetadata('action', 'update')
  async update(
    @Body() updatePunchCardDto: UpdatePunchCardDto,
    @Param() { punchCardId }: PunchCardIdDto,
  ) {
    return await this.writeService.update(punchCardId, updatePunchCardDto);
  }

  @Get()
  @SetMetadata('action', 'get_all')
  async index(@Query() indexPunchCardsDto: IndexPunchCardsDto) {
    const companyId =
      indexPunchCardsDto.companyId ??
      this.currentUserService.getCurrentCompanyId();

    if (!companyId)
      throw new UnauthorizedException('Current user does not have a company.');

    return await this.readService.index({
      ...indexPunchCardsDto,
      companyId,
    });
  }

  @Get(':punchCardId')
  @SetMetadata('action', 'get_details')
  async getDetails(@Param() { punchCardId }: PunchCardIdDto) {
    return await this.readService.getDetails(punchCardId);
  }

  @Post(':punchCardId/achievement')
  @SetMetadata('action', 'create_achievement')
  async createAchievement(
    @Param() { punchCardId }: PunchCardIdDto,
    @Body() createPunchCardAchievementDto: CreatePunchCardAchievementDto,
  ) {
    return await this.achievementSerice.create(
      punchCardId,
      createPunchCardAchievementDto,
    );
  }

  @Get(':companyId/achievement')
  @SetMetadata('action', 'get_all_achievements')
  async getAllAchievements(@Param('companyId') companyId: string) {
    return await this.achievementSerice.findAllAchievements(
      new Types.ObjectId(companyId),
    );
  }

  @Patch(':punchCardId/achievement/:achievementId')
  @SetMetadata('action', 'update_achievement')
  async updateAchievement(
    @Param() achievementUrlParams: AchievementUrlParams,
    @Body() updatePunchCardAchievementDto: UpdatePunchCardAchievementDto,
  ) {
    return await this.achievementSerice.update(
      achievementUrlParams,
      updatePunchCardAchievementDto,
    );
  }

  @Delete(':punchCardId/achievement/:achievementId')
  @SetMetadata('action', 'delete_achievement')
  async deleteAchievement(
    @Param() { punchCardId, achievementId }: AchievementUrlParams,
  ) {
    return await this.achievementSerice.delete(punchCardId, achievementId);
  }
}
