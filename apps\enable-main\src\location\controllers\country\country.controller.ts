import {
  GenericExceptionFilter,
  LocationItemType,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { Types } from 'mongoose';
import {
  CountryToCreate,
  CountryToIndex,
  CountryToUpdate,
} from '../../../location/dto/country.dto';
import { LocationItemServiceInterface } from '../../services/location-item/location-item-service.interface';

@Controller('country')
@ApiTags('Country')
@SetMetadata('module', 'country')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
export class CountryController {
  constructor(
    @Inject(LocationItemServiceInterface)
    private locationItemService: LocationItemServiceInterface,
  ) {}

  @Get()
  @SetMetadata('action', 'get_all')
  @ApiOperation({
    deprecated: true,
    description:
      '**Deprecated**, use `GET /location-item` with type `country` instead.',
  })
  async index(@Query() countryToIndex: CountryToIndex) {
    const result = await this.locationItemService.findAll({
      type: LocationItemType.COUNTRY,
      ...countryToIndex,
    });
    return result[0].paginatedResult;
  }

  @ApiOperation({
    deprecated: true,
    description:
      '**Deprecated**, use `GET /location-item/public` with type `country` instead.',
  })
  @Get('public')
  @SetMetadata('public', 'true')
  async publicIndex(@Query() countryToIndex: CountryToIndex) {
    const result = await this.locationItemService.findAll({
      type: LocationItemType.COUNTRY,
      ...countryToIndex,
    });
    return result[0].paginatedResult;
  }

  @ApiOperation({
    deprecated: true,
    description:
      '**Deprecated**, use `POST /location-item` with type `country` instead.',
  })
  @Post()
  @SetMetadata('action', 'create')
  async create(@Body() countryToCreate: CountryToCreate) {
    return await this.locationItemService.create({
      type: LocationItemType.COUNTRY,
      ...countryToCreate,
    });
  }

  @ApiOperation({
    deprecated: true,
    description:
      '**Deprecated**, use `PATCH /location-item` with type `country` instead.',
  })
  @Put(':id')
  @SetMetadata('action', 'update')
  async update(@Body() countryToUpdate: CountryToUpdate) {
    return await this.locationItemService.update({
      type: LocationItemType.COUNTRY,
      ...countryToUpdate,
      _id: new Types.ObjectId(countryToUpdate._id),
    });
  }

  @ApiOperation({
    deprecated: true,
    description: '**Deprecated**, use `DELETE /location-item`',
  })
  @Delete(':id')
  @SetMetadata('action', 'remove')
  async remove(@Param('id') id: string) {
    return await this.locationItemService.delete(new Types.ObjectId(id));
  }
}
