import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { HydratedDocument } from 'mongoose';

export type SettingsModelDocument = HydratedDocument<SettingsModel>;
@Schema({ timestamps: true })
export class SettingsModel {
  @Prop({
    type: String,
    required: false,
  })
  paymentPluginVersion: string;

  @Prop({
    type: String,
    required: false,
  })
  deliveryPluginVersion: string;

  @Prop({
    type: String,
    default: moment().startOf('month').format('MMMM'),
  })
  month: string;

  @Prop({
    type: Number,
    default: moment().week(),
  })
  week: number;

  @Prop({
    type: Number,
    default: moment().date(),
  })
  day: number;

  @Prop({
    type: Number,
    default: moment().year(),
  })
  year: number;
}

export const SettingSchema = SchemaFactory.createForClass(SettingsModel);
