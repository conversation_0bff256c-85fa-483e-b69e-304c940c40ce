import {
  GenericExceptionFilter,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { <PERSON>pi<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>pi<PERSON><PERSON>erAuth, ApiTags } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { CreateStoreDto } from '../dto/create-store.dto';
import { IndexStoreDto } from '../dto/index-store.dto';
import { UpdateStoreDto } from '../dto/update-store.dto';
import { StoreServiceInterface } from '../services/store.service.interface';

@Controller('store')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags('Store')
@SetMetadata('module', 'store')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class StoreController {
  constructor(
    @Inject('StoreServiceInterface')
    private readonly storeService: StoreServiceInterface,
  ) {}

  @Post()
  @SetMetadata('action', 'create')
  async create(@Body() createStoreDto: CreateStoreDto) {
    return await this.storeService.create(createStoreDto);
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  async findById(@Param('id') id: Types.ObjectId) {
    return await this.storeService.findById(id);
  }
  @Delete(':id')
  @SetMetadata('action', 'delete')
  async delete(@Param('id') id: Types.ObjectId) {
    await this.storeService.delete(id);
  }

  @Put()
  @SetMetadata('action', 'update')
  async update(@Body() updateStoreDto: UpdateStoreDto) {
    return await this.storeService.update(updateStoreDto);
  }

  @Get()
  @SetMetadata('action', 'index')
  async index(@Query() indexBrandDto: IndexStoreDto) {
    const selectedStores = await this.storeService.index(indexBrandDto);
    const res = {
      stores: selectedStores[0]['paginatedResult'],
      totalCount: selectedStores[0]['totalCount'][0]
        ? selectedStores[0]['totalCount'][0]['createdAt']
        : 0,
    };
    return res;
  }
}
