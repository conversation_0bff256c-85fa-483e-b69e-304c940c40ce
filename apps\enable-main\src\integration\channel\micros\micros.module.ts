import { SharedStuffModule } from '@app/shared-stuff';
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';

import { BranchModule } from '../../../branch/branch.module';
import { BrandModule } from '../../../brand/brand.module';
import { CompanyModule } from '../../../company/company.module';
import { CouponModule } from '../../../coupon/coupon.module';
import { CustomerReadModule } from '../../../customer/modules/customer-read/customer-read.module';
import { OrderModule } from '../../../order/order.module';
import { RestaurantModule } from '../../../restaurant/restaurant.module';
import { IntegrationLogModule } from '../../integration-log/integration-log.module';
import { MicrosListener } from './listeners/micros.listener';
import { MicrosAuthHttpService } from './services/micros-auth-http.service';
import { MicrosAuthService } from './services/micros-auth.service';
import { MicrosCheckHttpService } from './services/micros-check-http.service';
import { MicrosService } from './services/micros.service';

@Module({
  imports: [
    OrderModule,
    SharedStuffModule,
    IntegrationLogModule,
    HttpModule,
    CompanyModule,
    ScheduleModule,
    CustomerReadModule,
    BranchModule,
    BrandModule,
    RestaurantModule,
    CouponModule,
  ],
  providers: [
    MicrosService,
    MicrosCheckHttpService,
    MicrosAuthService,
    MicrosAuthHttpService,
    MicrosListener,
  ],
  exports: [MicrosService],
})
export class MicrosModule {}
