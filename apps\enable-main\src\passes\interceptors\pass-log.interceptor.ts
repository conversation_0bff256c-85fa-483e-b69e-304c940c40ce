import {
  CallH<PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
  StreamableFile,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Request, Response } from 'express';
import { ServerResponse } from 'http';
import { Model } from 'mongoose';
import { Observable, tap } from 'rxjs';
import { PassLog } from '../models/pass-log.model';

@Injectable()
export class PassLogInterceptor implements NestInterceptor {
  constructor(
    @InjectModel(PassLog.name)
    private passLogModel: Model<PassLog>,
  ) {}

  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const httpContext = context.switchToHttp();
    const request = httpContext.getRequest<Request>();

    const log = await this.passLogModel.create({
      requestBody: request.body,
      requestHeaders: request.headers,
      requestUrl: `${request.method} ${request.url}`,
    });
    return next.handle().pipe(
      tap({
        next: async (responseBody) => {
          const response = httpContext.getResponse<Response>();
          await log.updateOne({
            responseHeaders: response.getHeaders(),
            responseBody:
              responseBody instanceof StreamableFile
                ? 'StreamableFile'
                : responseBody instanceof ServerResponse
                  ? `${responseBody.statusCode} ${responseBody.statusMessage}`
                  : responseBody,
          });
        },
        error: async (err) => {
          const response = httpContext.getResponse<Response>();
          await log.updateOne({
            responseHeaders: response.getHeaders(),
            responseError: err,
          });
        },
      }),
    );
  }
}
