name: Enable Services (Main)

on:
  push:
    branches:
      - main
  pull_request:
    types:
      - closed
    branches:
      - main
  workflow_dispatch:

jobs:
  build:
    if: (github.event.pull_request && github.event.pull_request.merged == 'true') || (github.event.pusher)
    environment: Production
    runs-on: ubuntu-latest
    outputs:
      build_success: ${{ steps.build.outputs.success }}
    steps:
      - name: Check Out Repository
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '22'

      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install Dependencies
        run: npm ci

      - name: Build Services
        id: build
        run: |
          set -e
          # Run builds in parallel and capture their exit codes
          npm run build enable-main > build-main.log 2>&1 & pid1=$!
          npm run build enable-notification > build-notification.log 2>&1 & pid2=$!
          npm run build enable-reporting > build-reporting.log 2>&1 & pid3=$!
          npm run build enable-shorten > build-shorten.log 2>&1 & pid4=$!

          # Wait for all processes and capture exit codes
          wait $pid1 || export BUILD_FAILED=1
          wait $pid2 || export BUILD_FAILED=1
          wait $pid3 || export BUILD_FAILED=1
          wait $pid4 || export BUILD_FAILED=1

          # Display logs regardless of success/failure
          echo "=== enable-main build log ==="
          cat build-main.log
          echo "=== enable-notification build log ==="
          cat build-notification.log
          echo "=== enable-reporting build log ==="
          cat build-reporting.log
          echo "=== enable-shorten build log ==="
          cat build-shorten.log

          # Exit with error if any build failed
          if [ "${BUILD_FAILED}" = "1" ]; then
            echo "One or more builds failed"
            exit 1
          fi

          echo "success=true" >> $GITHUB_OUTPUT

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: dist

  deploy-main:
    needs: build
    if: needs.build.outputs.build_success == 'true'
    environment: Production
    runs-on: ubuntu-latest
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: dist

      - name: Copy enable-main to first Production machine
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.HOSTING_URL_MAIN }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SECRET_KEY }}
          passphrase: ${{ secrets.KEY_PASSPHRASE }}
          port: ${{ secrets.PORT }}
          source: 'dist'
          target: '~/enable-backend'
          strip_components: 0

      - name: Install Dependencies and Reload enable-main service
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOSTING_URL_MAIN }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SECRET_KEY }}
          passphrase: ${{ secrets.KEY_PASSPHRASE }}
          port: ${{ secrets.PORT }}
          script: |
            export NVM_DIR="$HOME/.nvm"
            [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
            cd ~/enable-backend
            git pull
            npm ci --omit=dev
            set -e
            pm2 reload all --wait-ready --listen-timeout 10000 --kill-timeout 10000 --update-env

  deploy-microservices:
    needs: build
    if: needs.build.outputs.build_success == 'true'
    environment: Production
    runs-on: ubuntu-latest
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: dist

      - name: Copy microservices to second Production machine
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.HOSTING_URL_SERVICES }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SECRET_KEY }}
          passphrase: ${{ secrets.KEY_PASSPHRASE }}
          port: ${{ secrets.PORT }}
          source: 'dist'
          target: '~/enable-backend'
          strip_components: 0

      - name: Install Dependencies and Reload microservices
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOSTING_URL_SERVICES }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SECRET_KEY }}
          passphrase: ${{ secrets.KEY_PASSPHRASE }}
          port: ${{ secrets.PORT }}
          script: |
            export NVM_DIR="$HOME/.nvm"
            [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
            cd ~/enable-backend
            git pull
            npm ci --omit=dev
            set -e
            pm2 reload all --wait-ready --listen-timeout 10000 --kill-timeout 10000 --update-env
