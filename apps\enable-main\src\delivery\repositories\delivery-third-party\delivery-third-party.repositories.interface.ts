import { Types } from 'mongoose';
import { DeliveryThirdPartyName, IGenericRepository } from '@app/shared-stuff';
import {
  DeliveryThirdPartyDocument,
  DeliveryThirdParty,
} from '../../models/delivery-third-party.model';

export interface DeliveryThirdPartyRepositoryInterface
  extends IGenericRepository<DeliveryThirdPartyDocument, DeliveryThirdParty> {
  findByNameIn(
    thirdParties: DeliveryThirdPartyName[],
  ): Promise<DeliveryThirdPartyDocument[]>;
  findByName(
    defaultThirdParty: DeliveryThirdPartyName,
  ): Promise<DeliveryThirdPartyDocument[]>;
  findByCompanyId(
    companyId: Types.ObjectId,
  ): Promise<DeliveryThirdPartyDocument[]>;
}

export const DeliveryThirdPartyRepositoryInterface = Symbol(
  'DeliveryThirdPartyRepositoryInterface',
);
