import {
  CollectionName,
  EmbeddedBrandDto,
  ObjectIdTransform,
} from '@app/shared-stuff';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { HydratedDocument, Types } from 'mongoose';

function getMomentDefault() {
  return moment().add({ days: 365 }).toISOString();
}

export type ApiKeyDocument = HydratedDocument<ApiKey>;
@Schema({ timestamps: true })
export class ApiKey {
  @Prop({
    required: true,
  })
  name: string;

  @Prop({
    required: true,
  })
  code: string;

  @Prop({
    required: false,
  })
  secret: string;

  @Prop({
    required: true,
  })
  expire_date: Date;

  @Prop({
    type: [{ type: Types.ObjectId, ref: CollectionName.ROLES }],
    required: true,
  })
  roles: Types.ObjectId[];

  @Prop({
    type: {},
    required: false,
  })
  updatedBy: {};

  @Prop({
    type: {},
    required: false,
  })
  createdBy: {};

  @Prop({
    type: {},
    required: false,
  })
  assignedTo: {};

  @Prop({
    type: {},
    required: false,
  })
  deletedBy: {};

  @Prop({
    type: Date,
    required: false,
  })
  deletedAt: Date;

  @Prop({
    type: String,
    enum: ['active', 'not_active'],
    default: 'active',
  })
  status: string;

  @Prop({
    type: String,
    default: moment().startOf('month').format('MMMM'),
  })
  month: string;

  @Prop({
    type: Number,
    default: moment().week(),
  })
  week: number;

  @Prop({
    type: Number,
    default: moment().date(),
  })
  day: number;

  @Prop({
    type: EmbeddedBrandDto,
  })
  brand: EmbeddedBrandDto;

  @Prop({
    type: Number,
    default: moment().year(),
  })
  year: number;

  @Prop({
    type: Date,
    default: getMomentDefault,
  })
  last_active: Date;

  @Prop({
    type: [String],
    required: false,
  })
  domains: string[];

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: CollectionName.COMPANY,
  })
  company: string;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: CollectionName.BRANCH,
  })
  branch: string;

  @Prop({
    type: [Types.ObjectId],
    ref: CollectionName.BRANCH,
    required: false,
    default: [],
  })
  branches: Types.ObjectId[];
}

export class ApiKeyWithId extends ApiKey {
  @ObjectIdTransform()
  _id: Types.ObjectId;
}

export const ApiKeySchema = SchemaFactory.createForClass(ApiKey);
