import {
  MicroserviceCommunicationModule,
  SharedStuffModule,
} from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { ShortenUrlController } from './controllers/shorten-url.controller';
import { ShortenUrlService } from './services/shorten-url.service';
import { ShortenUrlServiceInterface } from './services/shorten-url.service.interface';

@Module({
  controllers: [ShortenUrlController],
  imports: [
    SharedStuffModule,
    MicroserviceCommunicationModule.forChild('enable-main-shorten-producer'),
  ],
  providers: [
    { provide: ShortenUrlServiceInterface, useClass: ShortenUrlService },
  ],
  exports: [ShortenUrlServiceInterface],
})
export class ShortenUrlModule {}
