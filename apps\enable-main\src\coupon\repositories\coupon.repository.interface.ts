import {
  Coupon,
  CouponDocument,
  CouponIndexDto,
  CustomerDocument,
  IGenericRepository,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface CouponRepositoryInterface
  extends IGenericRepository<CouponDocument, Coupon> {
  index(couponIndexDto: CouponIndexDto): Promise<CouponDocument[]>;
  findByCompanyId(companyId: Types.ObjectId): Promise<CouponDocument[]>;
  findByOrdableId(ordableId: number): Promise<CouponDocument | null>;
  findByShopifyDiscountCodeForCustomer(
    code: string,
    customer: CustomerDocument,
  ): Promise<CouponDocument | null>;
  findByCost(
    companyId: Types.ObjectId,
    lowerBound: number,
    upperBound: number,
  ): Promise<CouponDocument[]>;
  findHighestCoupon(
    companyId: Types.ObjectId,
    upperBound: number,
  ): Promise<CouponDocument>;
}

export const CouponRepositoryInterface = Symbol('CouponRepositoryInterface');
