// EBL-3716 [Loyalty Program] Wallet Pass - Loyalty Card Status
// Initialize registeredPasses.createdAt as registeredPasses.updatedAt
db.customers
  .find(
    { 'registeredPasses.0': { $exists: true } },
    { _id: 1, registeredPasses: 1 },
  )
  .forEach((customer) => {
    const registeredPasses = customer.registeredPasses.map((pass) => ({
      ...pass,
      createdAt: pass.updatedAt,
    }));

    db.customers.updateOne(
      { _id: customer._id },
      { $set: { registeredPasses } },
    );
  });
