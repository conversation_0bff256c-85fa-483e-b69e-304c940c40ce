import {
  AggregatorOrderToCreate,
  areObjectIdsEqual,
  Company,
  CustomerDocument,
  DEFAULT_DISCOUNT_LABEL,
  Discount,
  DiscountApplyTo,
  DiscountSource,
  DiscountType,
  LoyaltyPointLogAction,
  LoyaltyStatus,
  mapAsync,
  NumberOfUsesType,
  OrderDocument,
  OrderPosToCreate,
  PunchCardBenefit,
  Reward,
  UnreachableError,
  ValidDiscount,
} from '@app/shared-stuff';
import {
  BadRequestException,
  Inject,
  Injectable,
  Logger,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CouponServiceInterface } from 'apps/enable-main/src/coupon/services/coupon.service.interface';
import { CustomerNotificationServiceInterface } from 'apps/enable-main/src/customer/modules/customer-notification/customer-notification.service.interface';
import { CustomerTierServiceInterface } from 'apps/enable-main/src/customer/modules/customer-tier/customer-tier.service.interface';
import { Types } from 'mongoose';
import { BenefitTypeFactoryService } from '../../../benefit/services/types/benefit-type-factory.service';
import { LoyaltyPointLogServiceInterface } from '../../../loyalty-point-log/services/loyalty-point-log.service.interface';
import { CompanyService } from 'apps/enable-main/src/company/services/company/company.service';

@Injectable()
export class OrderRedemptionService {
  private readonly loggerService = new Logger(OrderRedemptionService.name);

  constructor(
    private readonly eventEmitter: EventEmitter2,
    @Inject(CustomerTierServiceInterface)
    private readonly customerTierService: CustomerTierServiceInterface,
    @Inject(CustomerNotificationServiceInterface)
    private readonly customerNotificationService: CustomerNotificationServiceInterface,
    @Inject(CouponServiceInterface)
    private readonly couponService: CouponServiceInterface,
    @Inject(LoyaltyPointLogServiceInterface)
    private readonly loyaltyPointLogService: LoyaltyPointLogServiceInterface,
    private readonly benefitTypeFactoryService: BenefitTypeFactoryService,
    private readonly companyService: CompanyService,
  ) {}

  async updateCustomerLoyaltyUsage(
    customer: CustomerDocument,
    orderHasTierPercentDiscount: boolean,
    orderHasTierFreeDelivery: boolean,
  ) {
    if (
      customer.loyaltyTier &&
      customer.loyaltyTier.percentDiscountMaximumNumberOfUsesType ===
        NumberOfUsesType.LIMITED_USAGE &&
      !customer.loyaltyTier.percentDiscountRemainingNumberOfUses &&
      customer.loyaltyTier.percentDiscountRemainingNumberOfUses !== 0
    ) {
      await this.resetRemainingUses(customer, 'PERCENT_DISCOUNT');
    }

    if (
      customer.loyaltyTier &&
      customer.loyaltyTier.freeDeliveryMaximumNumberOfUsesType ===
        NumberOfUsesType.LIMITED_USAGE &&
      !customer.loyaltyTier.freeDeliveryRemainingNumberOfUses &&
      customer.loyaltyTier.freeDeliveryRemainingNumberOfUses !== 0
    ) {
      await this.resetRemainingUses(customer, 'FREE_DELIVERY');
    }

    if (
      orderHasTierPercentDiscount &&
      customer?.loyaltyTier?.percentDiscountMaximumNumberOfUsesType ===
        NumberOfUsesType.LIMITED_USAGE
    ) {
      await this.customerTierService.reduceNumberOfUses(
        customer,
        'PERCENT_DISCOUNT',
      );
    }

    if (
      orderHasTierFreeDelivery &&
      customer?.loyaltyTier?.freeDeliveryMaximumNumberOfUsesType ===
        NumberOfUsesType.LIMITED_USAGE
    ) {
      await this.customerTierService.reduceNumberOfUses(
        customer,
        'FREE_DELIVERY',
      );
    }
  }

  async applyCouponRedemptionPostFunctions(
    { couponId, branch, brand, code, source }: OrderDocument,
    customer: CustomerDocument,
  ): Promise<void> {
    const oldBalance = customer.loyaltyPoints;
    if (couponId) {
      const coupon = await this.couponService.findById(couponId);
      customer.loyaltyPoints -= coupon.loyaltyPointCost;
      await customer.save();

      await this.loyaltyPointLogService.create({
        action: LoyaltyPointLogAction.ON_COUPON_REDEEMED,
        oldBalance,
        loyaltyPointsEarned: -coupon.loyaltyPointCost,
        newBalance: customer.loyaltyPoints,
        customerId: customer._id,
        orderCode: code,
        orderSource: source,
        branchId:
          typeof branch === 'object' ? branch._id : new Types.ObjectId(branch),
        brandId: brand?._id,
      });

      this.eventEmitter.emit('customer.points.updated', customer);
      await this.customerNotificationService.fireOnCouponRedeemedTrigger(
        customer,
      );
    }
  }

  async applyFlexiblePointsRedemptionPostFunctions(
    order: OrderDocument,
    customer: CustomerDocument,
    company: Company,
  ): Promise<void> {
    const pointsRedeemed = this.calculatePointsRedeemedOnFlexibleRedemptions(
      order,
      company,
    );

    const oldBalance = customer.loyaltyPoints;
    customer.loyaltyPoints = customer.loyaltyPoints - pointsRedeemed;
    await customer.save();
    await this.loyaltyPointLogService.create({
      action: LoyaltyPointLogAction.ON_FLEXIBLE_POINTS_REDEMPTION,
      oldBalance,
      loyaltyPointsEarned: -pointsRedeemed,
      newBalance: customer.loyaltyPoints,
      flatDiscountAmount: order.discounts
        .filter(({ source }) => source === DiscountSource.POINTS)
        .map(({ amount }) => amount)
        .reduce((a, b) => a + b, 0),
      currency: order.localization?.currency ?? company.localization.currency,
      customerId: customer._id,
      orderCode: order.code,
      orderSource: order.source,
      branchId:
        typeof order.branch === 'object'
          ? order.branch._id
          : new Types.ObjectId(order.branch),
      brandId: order.brand?._id,
    });

    this.eventEmitter.emit('customer.points.updated', customer);
  }

  async validateLoyaltyRedemptions(
    company: Company,
    order: OrderDocument | OrderPosToCreate | AggregatorOrderToCreate,
    customer: CustomerDocument,
  ): Promise<ValidDiscount[]> {
    this.validateCombinationRedemption(company, order);
    this.validateCouponRedemption(company, order, customer);
    this.validateFlexiblePointsRedemption(company, order, customer);
    this.validateTierDiscountRedemption(company, order, customer);
    this.validateRewardRedemption(company, order);

    return mapAsync(order.discounts || [], (discount: Discount) =>
      Promise.resolve(
        this.validateDiscount(discount, company, order, customer),
      ),
    );
  }

  public calculatePointsRedeemedOnFlexibleRedemptions(
    order: OrderDocument,
    company: Company,
  ) {
    return (order.discounts || [])
      .filter(({ source }) => source === DiscountSource.POINTS)
      .map(
        ({ amount }) =>
          amount * company.loyaltyProgramConfig.redemptionExchangeRate,
      )
      .reduce((a, b) => a + b, 0);
  }

  private async resetRemainingUses(
    customer: CustomerDocument,
    benefitType: 'PERCENT_DISCOUNT' | 'FREE_DELIVERY',
  ) {
    if (benefitType === 'PERCENT_DISCOUNT') {
      customer.loyaltyTier.percentDiscountRemainingNumberOfUses =
        customer.loyaltyTier.percentDiscountMaximumNumberOfUses;
    } else if (benefitType === 'FREE_DELIVERY') {
      customer.loyaltyTier.freeDeliveryRemainingNumberOfUses =
        customer.loyaltyTier.freeDeliveryMaximumNumberOfUses;
    }

    customer.markModified('loyaltyTier');
    await customer.save();
    this.eventEmitter.emit('loyaltyTier.numberOfUses.updated', customer);
  }

  private validateCombinationRedemption(
    company: Company,
    { discounts }: OrderPosToCreate | OrderDocument | AggregatorOrderToCreate,
  ) {
    if (
      company.loyaltyProgramConfig?.canCombineProgramsDiscounts ||
      !discounts ||
      discounts.length < 2
    )
      return;

    const sources = discounts.map((discount: Discount) => discount.source);
    const uniqueSources = Array.from(new Set(sources));
    const loyaltySources = [
      DiscountSource.COUPON,
      DiscountSource.LOYALTY_TIER,
      DiscountSource.REWARD,
    ];
    const uniqueLoyaltySources = uniqueSources.filter((source) =>
      loyaltySources.includes(source),
    );

    const isCombiningProgramsDiscounts = uniqueLoyaltySources.length > 1;
    if (isCombiningProgramsDiscounts)
      this.throwError(
        'User should be able to apply only one loyalty program discount on the same order',
      );
  }

  private validateCouponRedemption(
    company: Company,
    { discounts }: OrderPosToCreate | OrderDocument | AggregatorOrderToCreate,
    customer: CustomerDocument,
  ) {
    const couponDiscounts = (discounts || []).filter(
      (discount) => discount.source === DiscountSource.COUPON,
    );
    if (!couponDiscounts || couponDiscounts.length === 0) return;

    if (
      !company.hasLoyaltyProgram ||
      !company.loyaltyProgramConfig.hasLoyaltyPoints
    ) {
      this.throwError(
        "Cannot redeem coupon, company doesn't have a loyalty program",
      );
    }

    if (customer.loyaltyStatus !== LoyaltyStatus.MEMBER) {
      this.throwError('Only Loyalty Members can redeem coupons');
    }
  }

  private validateTierDiscountRedemption(
    company: Company,
    { discounts }: OrderPosToCreate | OrderDocument | AggregatorOrderToCreate,
    customer: CustomerDocument,
  ) {
    const hasTierDiscount = discounts?.some(
      (discount) => discount.source === DiscountSource.LOYALTY_TIER,
    );
    if (!discounts || !hasTierDiscount) return;

    if (
      !company.hasLoyaltyProgram ||
      !company.loyaltyProgramConfig.hasLoyaltyTiers
    ) {
      this.throwError(
        "Cannot redeem tier discount, company doesn't have loyalty tiers",
      );
    }

    if (!customer.loyaltyTier) {
      this.throwError(
        'Customer does not have a loyalty tier to redeem tier discount',
      );
    }

    const isPercentDiscountUsed = discounts.some(
      (discount) =>
        discount.source === DiscountSource.LOYALTY_TIER &&
        discount.applyTo === DiscountApplyTo.CART,
    );

    const isFreeDeliveryUsed = discounts.some(
      (discount) =>
        discount.source === DiscountSource.LOYALTY_TIER &&
        discount.applyTo === DiscountApplyTo.DELIVERY,
    );

    if (
      isPercentDiscountUsed &&
      customer.loyaltyTier &&
      customer.loyaltyTier.percentDiscountMaximumNumberOfUsesType ===
        NumberOfUsesType.LIMITED_USAGE &&
      customer.loyaltyTier.percentDiscountRemainingNumberOfUses === 0
    ) {
      this.throwError(
        'Customer does have a loyalty tier but there is no number of uses remaining for percent discount benefit ',
      );
    }

    if (
      isFreeDeliveryUsed &&
      customer.loyaltyTier &&
      customer.loyaltyTier.freeDeliveryMaximumNumberOfUsesType ===
        NumberOfUsesType.LIMITED_USAGE &&
      customer.loyaltyTier.freeDeliveryRemainingNumberOfUses === 0
    ) {
      this.throwError(
        'Customer does have a loyalty tier but there is no number of uses remaining for free delivery benefit ',
      );
    }

    if (customer.loyaltyStatus !== LoyaltyStatus.MEMBER) {
      this.throwError('Only Loyalty Members can redeem tier discounts');
    }
  }

  private throwError(message: string): void {
    this.loggerService.error(message);
    throw new BadRequestException(message);
  }

  private validateRewardRedemption(
    company: Company,
    { discounts }: OrderPosToCreate | OrderDocument | AggregatorOrderToCreate,
  ) {
    const rewardDiscounts = discounts?.filter(
      (discount) => discount.source === DiscountSource.REWARD,
    );
    if (!rewardDiscounts || rewardDiscounts.length === 0) return;

    if (
      !company.hasLoyaltyProgram ||
      !company.loyaltyProgramConfig.hasLoyaltyPunchCards
    ) {
      this.throwError(
        "Cannot redeem reward, company doesn't have a loyalty punch card system",
      );
    }
  }

  private validateRewardForDiscount(
    discount: Discount,
    customer: CustomerDocument,
  ): ValidDiscount {
    if (!discount.rewardId) {
      this.throwError('rewardId is required to redeem reward discount');
    }

    const reward = (customer.rewards || []).find((reward) =>
      reward._id.equals(discount.rewardId),
    );
    if (!reward) {
      this.throwError(
        `Customer does not have reward with ID ${discount.rewardId.toString()}`,
      );
    }

    if (
      reward.benefit === PunchCardBenefit.PERCENT_DISCOUNT &&
      discount.type !== DiscountType.PERCENTAGE
    ) {
      this.throwError(
        `Reward with ID ${discount.rewardId.toString()} is a percent discount, but discount type is not percent`,
      );
    }

    if (
      reward.benefit === PunchCardBenefit.PERCENT_DISCOUNT &&
      reward.amount !== discount.amount
    )
      this.throwError(
        `Reward with ID ${discount.rewardId.toString()} has amount ${reward.amount}, but discount amount is ${discount.amount}`,
      );

    if (reward.benefit === PunchCardBenefit.MENU_ITEM && !discount.menuItemId)
      this.throwError(
        `Reward with ID ${discount.rewardId.toString()} is applied to a menu item, but discount has no menuItemId`,
      );

    if (
      reward.benefit === PunchCardBenefit.MENU_ITEM &&
      !reward.menuItem.eligibleMenuItemIds
        .map((id) => id.toString())
        .includes(discount.menuItemId.toString())
    )
      this.throwError(
        `Menu Item with ID ${discount.menuItemId.toString()} is not eligible for reward with ID ${discount.rewardId.toString()}`,
      );

    return {
      ...discount,
      titleEn: this.getRewardDiscountLabel(reward),
    };
  }

  private validateDiscount(
    discount: Discount,
    company: Company,
    order: OrderDocument | OrderPosToCreate | AggregatorOrderToCreate,
    customer: CustomerDocument,
  ): ValidDiscount {
    if (discount.source === DiscountSource.POINTS) {
      const loyaltyPointCost =
        discount.amount * company.loyaltyProgramConfig.redemptionExchangeRate;
      return {
        ...discount,
        titleEn: `${loyaltyPointCost} ${company.loyaltyProgramConfig.pointsBalanceTitleEn} Redemption`,
        loyaltyPointCost,
      };
    } else if (discount.source === DiscountSource.REWARD)
      return this.validateRewardForDiscount(discount, customer);
    else if (discount.source === DiscountSource.LOYALTY_TIER) {
      return {
        ...discount,
        titleEn:
          discount.applyTo === DiscountApplyTo.DELIVERY
            ? 'Free Delivery'
            : `${discount.amount}${discount.type == DiscountType.PERCENTAGE ? '%' : (company.localization?.currency ?? 'QAR')}`,
      };
    } else if (discount.source === DiscountSource.BENEFIT) {
      if (customer.earnedBenefits) {
        const benefit = customer.earnedBenefits.find((earnedBenefit) =>
          areObjectIdsEqual(earnedBenefit._id, discount.benefitId),
        );
        if (!benefit)
          this.throwError(
            `Benefit with id ${discount.benefitId.toString()} not found.`,
          );

        const { isValid, validationReasons } =
          this.benefitTypeFactoryService.validate(benefit, order);
        if (!isValid) this.throwError(validationReasons.join(', '));

        return { ...discount, titleEn: benefit?.titleEn };
      }
    }

    return {
      ...discount,
      titleEn: DEFAULT_DISCOUNT_LABEL,
    };
  }

  private getRewardDiscountLabel(reward: Reward) {
    if (reward.benefit === PunchCardBenefit.PERCENT_DISCOUNT)
      return `Loyalty Reward ${reward.amount}%`;
    else if (reward.benefit === PunchCardBenefit.MENU_ITEM)
      return `Loyalty Reward Free ${reward.menuItem.nameEn}`;
    else throw new UnreachableError(reward.benefit);
  }

  private validateFlexiblePointsRedemption(
    company: Company,
    order: OrderPosToCreate | OrderDocument | AggregatorOrderToCreate,
    customer: CustomerDocument,
  ) {
    const flexiblePointsRedemptions = (order.discounts || []).filter(
      (discount) => discount.source === DiscountSource.POINTS,
    );
    if (!flexiblePointsRedemptions || flexiblePointsRedemptions.length === 0)
      return;

    if (
      !company.loyaltyProgramConfig.hasLoyaltyPoints ||
      !company.loyaltyProgramConfig.hasFlexiblePointsRedemption
    )
      this.throwError(
        'This company does not have flexible points redemption enabled.',
      );

    flexiblePointsRedemptions.forEach((discount) => {
      if (discount.type !== DiscountType.FLAT)
        this.throwError(
          `Flexible point redemption discount type should be "${DiscountType.FLAT}" not "${discount.type}"`,
        );

      if (discount.applyTo !== DiscountApplyTo.CART)
        this.throwError(
          `Flexible point redemption discount should be appled to  "${DiscountApplyTo.CART}" not "${discount.applyTo}"`,
        );
    });

    const totalDiscountAmount = flexiblePointsRedemptions
      .map((discount) => discount.amount)
      .reduce((a, b) => a + b, 0);

    if (
      order.invoiced_amount != null &&
      totalDiscountAmount > order.invoiced_amount
    )
      this.throwError(
        'Flexible points redemption discount amount cannot exceed invoiced amount.',
      );

    const pointsCost =
      totalDiscountAmount * company.loyaltyProgramConfig.redemptionExchangeRate;

    if (!customer.loyaltyPoints || pointsCost > customer.loyaltyPoints)
      this.throwError(
        `Customer's points balance of ${customer.loyaltyPoints} is insufficient to cover the ${pointsCost} loyalty points cost to redeem ${totalDiscountAmount} ${company.localization.currency} discount`,
      );
  }
}
