import { CollectionName } from '@app/shared-stuff';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { HydratedDocument, Schema as MongooseSchema, Types } from 'mongoose';
import {
  DeliveryTaskPoint,
  DeliveryTaskStatus,
  DeliveryTaskType,
} from '../dto/delivery-task.dto';

const DeliveryPointSchema = new MongooseSchema({
  date: {
    type: Date,
    required: true,
  },
  completeDate: {
    type: Date,
    required: false,
  },
  startDate: {
    type: Date,
    required: false,
  },
  locationName: {
    type: String,
    required: false,
  },
  location: {
    type: Types.ObjectId,
    ref: 'SavedLocation',
    required: true,
  },
  branch: {
    type: Types.ObjectId,
    ref: CollectionName.BRANCH,
    required: false,
  },
  taskId: {
    type: Number,
    required: false,
  },
  pinLocation: {
    type: String,
    required: false,
  },
  barCode: {
    type: String,
    required: false,
  },
});

export type DeliveryTaskDocument = HydratedDocument<DeliveryTask>;
@Schema({ timestamps: true })
export class DeliveryTask {
  @Prop({
    type: String,
    required: true,
    unique: true,
  })
  code: string;

  @Prop({
    type: String,
  })
  externalId: string;

  @Prop({
    type: String,
    required: true,
    enum: [...Object.keys(DeliveryTaskStatus)],
    default: DeliveryTaskStatus.Pending,
  })
  status: DeliveryTaskStatus;

  @Prop({
    type: String,
    required: true,
    enum: [...Object.keys(DeliveryTaskType)],
  })
  type: DeliveryTaskType;

  @Prop({
    type: Number,
    required: true,
  })
  deliveryFees: number;

  @Prop({
    type: String,
    required: false,
  })
  orderCode: string;

  @Prop({
    type: String,
    required: false,
  })
  barCode: string;

  @Prop({
    type: Boolean,
    required: false,
    default: false,
  })
  autoAssign: boolean;

  @Prop({
    type: Boolean,
    required: false,
    default: true,
  })
  assignDriverLater: boolean;

  @Prop({
    type: String,
    required: false,
  })
  deliveryPartyName: string;

  @Prop({
    type: String,
    required: false,
  })
  driverName: string;

  @Prop({
    type: String,
    required: false,
  })
  customerName: string;

  @Prop({
    type: String,
    required: false,
  })
  customerPhone: string;

  @Prop({
    type: String,
    required: false,
  })
  customerCountryCode: string;

  @Prop({
    type: [DeliveryPointSchema],
    required: false,
  })
  pickupPoints: DeliveryTaskPoint[];

  @Prop({
    type: [DeliveryPointSchema],
    required: false,
  })
  deliveryPoints: DeliveryTaskPoint[];

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    required: false,
    ref: CollectionName.ORDER,
  })
  order: Types.ObjectId;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    required: true,
    ref: 'DeliveryParty',
  })
  deliveryParty: Types.ObjectId;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    required: false,
    ref: 'Driver',
  })
  driver: Types.ObjectId;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    required: true,
    ref: CollectionName.CUSTOMER,
  })
  customer: Types.ObjectId;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    required: true,
    ref: CollectionName.COMPANY,
  })
  company: Types.ObjectId;

  @Prop({
    type: {},
    required: false,
  })
  updatedBy: {};

  @Prop({
    type: {},
    required: false,
  })
  createdBy: {};

  @Prop({
    type: {},
    required: false,
  })
  deletedBy: {};

  @Prop({
    type: Date,
    required: false,
  })
  deletedAt: Date;

  @Prop({
    type: String,
    default: moment().startOf('month').format('MMMM'),
  })
  month: string;

  @Prop({
    type: Number,
    default: moment().week(),
  })
  week: number;

  @Prop({
    type: Number,
    default: moment().date(),
  })
  day: number;

  @Prop({
    type: Number,
    default: moment().year(),
  })
  year: number;
}

const DeliveryTaskSchema = SchemaFactory.createForClass(DeliveryTask);

export { DeliveryTaskSchema };
