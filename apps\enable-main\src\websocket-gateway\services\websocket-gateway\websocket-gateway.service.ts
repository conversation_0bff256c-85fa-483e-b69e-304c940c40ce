import {
  LogError,
  OrderDocument,
  PaymentDocument,
  PusherService,
} from '@app/shared-stuff';
import { OnEvent } from '@nestjs/event-emitter';
import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { CompanyService } from './../../../company/services/company/company.service';
import { AuthService } from './../../../user/services/auth/auth.service';
import { UserService } from './../../../user/services/user/user.service';
import { UserToConnect } from './../../dto/websocket.dto';

@WebSocketGateway({
  transports: ['polling', 'websocket'],
  upgrade: false,
  pingInterval: 25000,
  pingTimeout: 5000,
  cookie: false,
  cors: {
    origin: '*',
    methods: ['GET', 'POST', 'DELETE', 'PUT'],
  },
})
export class WebsocketGatewayService
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;

  constructor(
    private userService: UserService,
    private authService: AuthService,
    private companyService: CompanyService,
    private pusherService: PusherService,
  ) {}

  afterInit() {}

  handleConnection(client) {}

  async handleDisconnect(client) {
    const user = await this.userService.get_details(client.id);
    if (user) {
      user.activeNow = false;
      user.socketIoId = '';
      await user.save();
    }
  }

  @SubscribeMessage('EnableConnection')
  @LogError()
  async onEnableConnection(
    @MessageBody() userToConnect: UserToConnect,
    @ConnectedSocket() client: Socket,
  ) {
    const user = await this.authService.get_me(userToConnect.user);

    const company = await this.companyService.get_details(
      (user.company['_id'] ? user.company['_id'] : user.company) as any,
    );
    if (company) {
      // client.join(company._id);
    }
    user.socketIoId = client.id;
    user.activeNow = true;
    await user.save();
  }

  @OnEvent('order.updated')
  @LogError()
  async handleOrderUpdatedEvent(order: OrderDocument) {
    const companyName = order.companyName
      ? order.companyName
      : (await this.companyService.get_details(order.company)).name;
    const channelName = this.pusherService.formatCompanyName(companyName);

    const payload = {
      companyId: order.company,
      orderCode: order.code,
      isNew: order.isNew,
    };

    await this.pusherService.fireEvent(channelName, 'orderUpdated', payload);

    if (order.isNew) {
      await this.pusherService.fireEvent(
        order.company.toHexString(),
        'onOrderCreated',
        payload,
      );
      await this.pusherService.fireEvent(
        'AllCompaniesOrderCreated',
        'onOrderCreated',
        payload,
      );
    }

    await this.pusherService.fireEvent(
      order.code.replace(/ /g, '_'),
      'singleOrderUpdated',
      payload,
    );
    await this.pusherService.fireEvent(
      'AllCompaniesOrderUpdates',
      'orderUpdated',
      payload,
    );
  }

  @OnEvent('payment.updated')
  @LogError()
  async handlePaymentUpdatedEvent(payment: PaymentDocument) {
    await this.pusherService.fireEvent(
      this.pusherService.formatCompanyName(payment.company_name),
      'paymentUpdated',
      {
        companyId: payment.company,
        orderCode: payment.order_code,
      },
    );
  }
}
