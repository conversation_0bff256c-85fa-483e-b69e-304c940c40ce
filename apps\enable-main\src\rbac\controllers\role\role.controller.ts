import { Types } from 'mongoose';
import { RoleExcelToImport, RoleToIndex } from './../../dto/role.dto';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  SetMetadata,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { RoleService } from '../../services/role/role.service';
import { Request, Response } from 'express';
import { DataIndex, responseCode } from '@app/shared-stuff';
import { RoleToCreate, RoleToUpdate } from '../../dto/role.dto';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiTags,
} from '@nestjs/swagger';
import { HelperService } from '../../../shared/services/helper/helper.service';
import { FileInterceptor } from '@nestjs/platform-express';

@ApiTags('Role')
@Controller('role')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@SetMetadata('module', 'role')
export class RoleController {
  constructor(
    private roleservice: RoleService,
    private helperService: HelperService,
  ) {}

  @Get()
  @SetMetadata('action', 'get_all')
  async findAll(
    @Query() dataIndex: DataIndex,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const selectedRoles = await this.roleservice.index(
        dataIndex,
        req['company_id'],
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success for getting role details',
        selectedRoles,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get('admin')
  @SetMetadata('action', 'get_all')
  async admin_index(
    @Query() roleToIndex: RoleToIndex,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      roleToIndex.company = req['company_id'];
      const selectedRoles = await this.roleservice.admin_index(roleToIndex);
      const count = await this.roleservice.get_total_roles(roleToIndex);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success for getting role details',
        { roles: selectedRoles, totalRoles: count },
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  async findDetails(@Param('id') id: string, @Res() res: Response) {
    try {
      const selectedRoles = await this.roleservice.get_details(id);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'success for getting all roles',
        selectedRoles,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() roleToCreate: RoleToCreate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      roleToCreate.company = req['company_id']
        ? new Types.ObjectId(req['company_id'])
        : roleToCreate.company;
      const createdRole = await this.roleservice.create(roleToCreate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success for creating role',
        createdRole,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Put(':id')
  @SetMetadata('action', 'update')
  async update(
    @Body() roleToUpdate: RoleToUpdate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      roleToUpdate.company = req['company_id']
        ? new Types.ObjectId(req['company_id'])
        : roleToUpdate.company;
      const updatedRole = await this.roleservice.update(roleToUpdate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'success for update role',
        updatedRole,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Delete(':id')
  @SetMetadata('action', 'remove')
  async remove(@Param('id') id: string, @Res() res: Response) {
    try {
      const removedRole = await this.roleservice.remove(id);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_REMOVE,
        'success for remove role',
        removedRole,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('import/excel')
  @SetMetadata('action', 'import_excel')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: 'multipart/form-data',
    required: true,
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile() file,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const roleExcelToImport: RoleExcelToImport = {
        file_path: file['path'],
        company: new Types.ObjectId(req['company_id']),
      } as any;

      const data = await this.roleservice.importExcel(roleExcelToImport);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'Success to import  file',
        {},
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
}
