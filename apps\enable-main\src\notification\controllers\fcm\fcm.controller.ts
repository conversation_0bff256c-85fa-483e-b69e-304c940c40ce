import {
  FcmSubscribeDto,
  FcmUnsubscribeDto,
  GenericExceptionFilter,
  MicroserviceCommunicationService,
  PopulatedFcmSubscribeDto,
  PopulatedFcmUnsubscribeDto,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Inject,
  NotFoundException,
  OnModuleDestroy,
  OnModuleInit,
  Post,
  SetMetadata,
  UnauthorizedException,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { CurrentUserService } from '../../../shared/services/current-user/current-user.service';
import { CustomerReadServiceInterface } from '../../../customer/modules/customer-read/customer-read.service.interface';

@Controller('notification/fcm')
@ApiTags('Notification-FCM')
@SetMetadata('module', 'fcm')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
export class FcmController implements OnModuleInit, OnModuleDestroy {
  constructor(
    @Inject('enable-main-notification-producer')
    private readonly client: ClientProxy,
    private readonly microserviceCommunicationService: MicroserviceCommunicationService,
    @Inject(CustomerReadServiceInterface)
    private readonly customerReadService: CustomerReadServiceInterface,
  ) {}

  public async onModuleDestroy() {
    await this.microserviceCommunicationService.disconnect(this.client);
  }

  public async onModuleInit() {
    await this.microserviceCommunicationService.connect(this.client);
  }

  @Post('subscribe')
  @SetMetadata('public', 'true')
  public async subscribe(
    @Body() fcmSubscribeDto: FcmSubscribeDto,
  ): Promise<any> {
    const companyId = await this.customerReadService.getCompanyIdForCustomerId(
      fcmSubscribeDto.customerId,
    );
    if (!companyId) throw new NotFoundException();

    return this.microserviceCommunicationService.produceAndWaitForResponse<
      PopulatedFcmSubscribeDto,
      any
    >(this.client, 'fcm.subscribe.request', {
      ownerId: companyId,
      ...fcmSubscribeDto,
    });
  }

  @Post('unsubscribe')
  @SetMetadata('public', 'true')
  public async unsubscribe(
    @Body() fcmUnsubscribeDto: FcmUnsubscribeDto,
  ): Promise<any> {
    const companyId = await this.customerReadService.getCompanyIdForCustomerId(
      fcmUnsubscribeDto.customerId,
    );
    if (!companyId) throw new NotFoundException();

    return this.microserviceCommunicationService.produceAndWaitForResponse<
      PopulatedFcmUnsubscribeDto,
      any
    >(this.client, 'fcm.unsubscribe.request', {
      ownerId: companyId,
      ...fcmUnsubscribeDto,
    });
  }
}
