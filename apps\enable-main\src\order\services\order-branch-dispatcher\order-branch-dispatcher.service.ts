import {
  BranchDocument,
  CompanyDocument,
  CurrentUser,
  DeliveryMethod,
  DispatchedInfo,
  LoggerService,
  OrderChildren,
  OrderCreationSource,
  OrderDeliveryAction,
  OrderDocument,
  OrderItem,
  OrderLogActionEnum,
  OrderPaymentMethod,
  OrderPaymentStatus,
  OrderPosToCreate,
  SavedLocation,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OnEvent } from '@nestjs/event-emitter';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { BranchService } from '../../../branch/services/branch/branch.service';
import { CompanyService } from '../../../company/services/company/company.service';
import { OrderPosService } from '../order-pos/order-pos.service';
import {
  TookanMultipleTasksToCreate,
  TookanTask,
} from '../../../delivery/dto/tookan.dto';
import { TookanService } from '../../../delivery/services/tookan/tookan.service';
import { SavedLocationService } from '../../../location/services/saved-location/saved-location.service';
import { LocationType } from '../../../shared/enums/location-type.enum';
import { HelperService } from '../../../shared/services/helper/helper.service';
import { OrderItemDocument } from '../../models/orderItems.model';
import { OrderLogServiceInterface } from '../interfaces/order-log.service.interface';
import { OrderItemService } from '../order-item/order-item.service';
import * as randomestring from 'randomstring';

@Injectable()
export class OrderBranchDispatcherService {
  private readonly loggerService = new LoggerService(
    OrderBranchDispatcherService.name,
  );

  private readonly TOOKAN_TEMPLATE_NAME = 'Pickup_&_Delivery_Enable';
  private readonly TOOKAN_SPECIAL_INSTRUCTION_LABEL = 'Special_Instruction';
  private readonly TOOKAN_PAYMENT_AMOUNT_LABEL = 'Payment_Amount';
  private readonly TOOKAN_PAYMENT_METHOD_LABEL = 'Payment_Method';
  constructor(
    @Inject('OrderLogServiceInterface')
    private readonly orderLogService: OrderLogServiceInterface,
    private readonly orderItemsService: OrderItemService,
    private branchService: BranchService,
    private orderPosService: OrderPosService,
    private tookanService: TookanService,
    private configService: ConfigService,
    private savedLocationService: SavedLocationService,
    private helperService: HelperService,
    private companyService: CompanyService,
  ) {}

  @OnEvent('order.created')
  async handleOrderCreatedEvent(
    order: OrderDocument,
    company: CompanyDocument,
  ) {
    try {
      const canBeDispatched: boolean = this.canBeDispatched(order, company);
      this.loggerService.log(
        `Order ${order.code} can be dispatched from branch dispatcher: ${canBeDispatched}`,
      );
      if (canBeDispatched) {
        const dispatchedInfo = await this.dispatchOrder(order, company);

        await this.orderLogService.saveOrderLog(
          order,
          { requestedObject: {} },
          { responseObject: dispatchedInfo },
          OrderLogActionEnum.ORDER_DISPATCHED_TO_MULTIPLE_BRANCHES,
          {} as CurrentUser,
        );
      }
    } catch (error: any) {
      await this.orderLogService.saveOrderLog(
        order,
        { requestedObject: { order: order._id, code: order.code } },
        { responseObject: { error: JSON.stringify(error) } },
        OrderLogActionEnum.ORDER_DISPATCHED_TO_MULTIPLE_BRANCHES,
        {} as CurrentUser,
      );
    }
  }

  private async dispatchOrder(order: OrderDocument, company: CompanyDocument) {
    const orderItems: OrderItemDocument[] = await this.fetchOrderItems(order),
      branches: BranchDocument[] = await this.getBranches(orderItems),
      items = this.groupItemsByBranch(orderItems),
      childrenOrders: OrderDocument[] = [],
      dispatchedInfo: DispatchedInfo = {
        companyIds: [company._id],
        brandIds: [],
        parentId: order._id,
        childrenIds: [],
      };

    for (const branch of branches) {
      const orderPosToCreate: OrderPosToCreate =
        this.constructOrderBasedOnBranch(
          order,
          branch,
          company,
          items[branch._id.toString()],
        );

      const children = await this.orderPosService.create(orderPosToCreate);

      childrenOrders.push(children);

      dispatchedInfo.childrenIds.push(children._id);
    }

    await this.saveOrderChildren(order, childrenOrders);

    const parentCompany: CompanyDocument =
      await this.companyService.get_details(
        order.company['_id'] ? order.company['_id'] : order.company,
      );

    await this.createTookanTasks(
      order,
      parentCompany,
      childrenOrders,
      branches,
    );
  }

  private async getBranches(
    orderItems: OrderItemDocument[],
  ): Promise<BranchDocument[]> {
    const branchesIds: Types.ObjectId[] = orderItems.map(
      (item: OrderItemDocument) => item.branchId,
    );
    return await this.branchService.findAllByIds(branchesIds);
  }

  private async fetchOrderItems(
    order: OrderDocument,
  ): Promise<OrderItemDocument[]> {
    return order.populated('order_items')
      ? (order.order_items as any)
      : await this.orderItemsService.getAllByIds(order.order_items);
  }

  private canBeDispatched(order: OrderDocument, company: CompanyDocument) {
    const isNotChildOrder = !order.parentId;
    const isBranchDispatchingCompany = company?.isBranchDispatchingCompany;
    return isNotChildOrder && isBranchDispatchingCompany;
  }

  private calculateChildrenSubInvoicedAmount(items: OrderItemDocument[]) {
    return items.reduce(
      (totalAmount: number, orderItem: OrderItemDocument) =>
        totalAmount +
        (orderItem.totalAmountAfterDiscount ?? orderItem.totalAmount),
      0,
    );
  }

  private async saveOrderChildren(
    order: OrderDocument,
    childrenOrders: OrderDocument[],
  ) {
    order.children = this.mapToChildrenArray(childrenOrders);
    await order.save();
  }

  private mapToChildrenArray(orders: OrderDocument[]) {
    return orders.map<OrderChildren>((order: OrderDocument) => ({
      orderId: new Types.ObjectId(order._id),
      status: order.status,
    }));
  }

  private groupItemsByBranch(items: OrderItemDocument[]) {
    return items.reduce((acc: any, item: OrderItemDocument) => {
      const branchId = item.branchId.toHexString();
      if (!acc[branchId]) {
        acc[branchId] = [];
      }
      acc[branchId].push(item);
      return acc;
    }, {});
  }
  private constructOrderBasedOnBranch(
    parentOrder: OrderDocument,
    branch: BranchDocument,
    company: CompanyDocument,
    items: OrderItemDocument[],
  ): OrderPosToCreate {
    const invoicedAmount = this.calculateChildrenSubInvoicedAmount(items);
    const deliveryAmount = 0;
    return {
      items: this.mapOrderItemDocumentToOrderItem(items),
      first_name: parentOrder.customer_name,
      last_name: '',
      country_code: parentOrder.country_code,
      phone: parentOrder.customer_phone,
      is_gift: parentOrder.is_gift,
      recipient_name: parentOrder.recipient_name,
      recipient_phone: parentOrder.recipient_phone,
      company: company._id.toHexString(),
      brandId: parentOrder.brand._id.toHexString(),
      autoAssign: false,
      branch: branch._id.toString(),
      code: '',
      deliveryLocation: parentOrder.deliveryLocation as any,
      delivery_date: moment
        .utc(parentOrder.delivery_date)
        .add(5, 'minutes')
        .format('YYYY-MM-DD HH:mm'),
      pickup_date: moment
        .utc(parentOrder.pickup_date)
        .add(5, 'minutes')
        .format('YYYY-MM-DD HH:mm'),
      pickup_time: '',
      brand: undefined,
      callback_url: '',
      payment_method: OrderPaymentMethod.prepaid,
      cardMessage: parentOrder.cardMessage,
      paymentCode: '',
      delivery_action: OrderDeliveryAction.IN_STORE_PICKUP,
      dispatch_type: parentOrder.delivery_type,
      current_user: parentOrder.createdBy,
      deliverectPosOrderId: '',
      customer: undefined,
      delivery_slot_from: parentOrder.delivery_slot_from,
      delivery_slot_id: undefined,
      deliveryParty: undefined,
      delivery_slot_to: parentOrder.delivery_slot_to,
      delivery_time: undefined,
      delivery_type: parentOrder.delivery_type,
      deliveryLocationId: parentOrder.deliveryLocationId,
      discount: 0,
      driver: undefined,
      driver_id: undefined,
      deliveryMethod: undefined,
      invoice_number: parentOrder.invoice_number,
      invoiced_amount: invoicedAmount,
      total_amount: invoicedAmount + deliveryAmount,
      total_amount_after_discount: invoicedAmount,
      delivery_amount: deliveryAmount, // Need TO Be Validated
      order_remarks: parentOrder.order_remarks,
      email: '',
      is_secret: parentOrder.is_secret,
      is_test: parentOrder.is_test,
      isCustomerUpdatable: false,
      orderType: parentOrder.orderType,
      language: parentOrder.language,
      orderIsAlreadyPaid: true,
      payment_status: OrderPaymentStatus.COMPLETED,
      pickupLocationId: undefined,
      source: parentOrder.source,
      creationSource: OrderCreationSource.DISPATCHER,
      status: undefined,
      recipient_country_code: parentOrder.recipient_country_code,
      traceId: '',
      transport_type: parentOrder.transport_type,
      parentId: parentOrder._id,
    };
  }

  private mapOrderItemDocumentToOrderItem(
    items: OrderItemDocument[],
  ): OrderItem[] {
    return items.map((item: OrderItemDocument): OrderItem => {
      return {
        pickup_spot_id: item.branchId.toHexString(),
        adlerId: '',
        basePrice: item.price,
        brandId: '',
        itemReference: item._id.toHexString(),
        price: item.price,
        discount: item.discount,
        name: item.name,
        description: item.description,
        plu: item._id.toHexString(),
        modifierGroups: [],
        quantity: item.quantity,
        solutionErpId: '',
        special_instructions: item.special_instructions,
        subProducts: [],
        totalAmountAfterDiscount: item.totalAmountAfterDiscount,
        totalAmount: item.totalAmount,
      };
    });
  }

  private async createTookanTasks(
    parentOrder: OrderDocument,
    parentCompany: CompanyDocument,
    subOrders: OrderDocument[],
    branches: BranchDocument[],
  ) {
    const tookanMultipleTasksToCreate: TookanMultipleTasksToCreate =
      this.initTookanMultipleTaskDTO(parentOrder, parentCompany);
    try {
      tookanMultipleTasksToCreate.deliveries.push(
        await this.constructDeliveryTask(parentOrder),
      );

      // Filling The Pickup Tasks BASED on BRANDS and SUB_ORDERs
      for (const [index, branch] of branches.entries()) {
        tookanMultipleTasksToCreate.pickups.push(
          await this.constructPickupTask(branch, parentOrder, subOrders[index]),
        );
      }

      const createdTookanTaskPickups =
        await this.tookanService.createMultipleTask({
          ...tookanMultipleTasksToCreate,
          deliveries: [],
          has_delivery: 0,
        });

      const createdTookanTaskDelivers =
        await this.tookanService.createMultipleTask({
          ...tookanMultipleTasksToCreate,
          has_pickup: 0,
          auto_assignment: 0,
          pickups: [],
        });

      await this.updateOrderTookanDetails(
        parentOrder,
        createdTookanTaskPickups,
        createdTookanTaskDelivers,
      );

      await this.orderLogService.saveOrderLog(
        parentOrder,
        { requestedObject: tookanMultipleTasksToCreate },
        {
          responseObject: {
            createdTookanTaskPickups,
            createdTookanTaskDelivers,
          },
        },
        OrderLogActionEnum.MULTIPLE_TOOKAN_TASK_TO_CREATE,
        parentOrder.createdBy,
      );

      return parentOrder;
    } catch (e) {
      await this.orderLogService.saveOrderLog(
        parentOrder,
        { requestedObject: tookanMultipleTasksToCreate },
        { responseObject: JSON.stringify(e) },
        OrderLogActionEnum.MULTIPLE_TOOKAN_TASK_TO_CREATE,
        parentOrder.createdBy,
      );
    }
  }

  private initTookanMultipleTaskDTO(
    parentOrder: OrderDocument,
    company: CompanyDocument,
  ): TookanMultipleTasksToCreate {
    return {
      api_key: this.configService.get('TOOKAN_API_KEY'),
      tags: `${company._id.toHexString()}`,
      timezone: moment.tz
        .zone(
          parentOrder?.localization?.timezone
            ? parentOrder?.localization?.timezone
            : 'Asia/Qatar',
        )
        .utcOffset(moment.utc().valueOf()),
      auto_assignment: 1,
      team_id: company.tookan_team_id,
      fleet_id: undefined,
      geofence: 0,
      layout_type: 0,
      has_delivery: 1,
      has_pickup: 1,
      pickups: [],
      deliveries: [],
    };
  }

  private async constructDeliveryTask(
    order: OrderDocument,
  ): Promise<TookanTask> {
    let deliveryLocation: SavedLocation;
    if (order.deliveryLocationId)
      deliveryLocation = await this.savedLocationService.getDetails(
        order.deliveryLocationId.toHexString(),
      );
    return {
      address: `${this.helperService.convertLocationToString(
        deliveryLocation,
        LocationType.DELIVERY,
      )} - ${order.code}`,
      email: 'default' + randomestring.generate(5) + '@e-butler.com',
      job_description: order.order_remarks,
      name: order.customer_name,
      order_id: order.code,
      time: moment(order.pickup_date).add(5, 'minutes').format('HH:mm'),
      latitude: deliveryLocation ? deliveryLocation.latitude : 30.0444,
      longitude: deliveryLocation ? deliveryLocation.longitude : 31.2357,
      ref_images: [],
      barcode: order.barCode.toString(),
      template_data: [
        {
          label: this.TOOKAN_PAYMENT_AMOUNT_LABEL,
          data:
            order.payment_method == OrderPaymentMethod.online
              ? 'Paid Already'
              : order.total_amount.toString(),
        },
        {
          label: this.TOOKAN_PAYMENT_METHOD_LABEL,
          data:
            (order.payment_method == OrderPaymentMethod.online &&
              order.payment_status != OrderPaymentStatus.COMPLETED) ||
            order.payment_method == OrderPaymentMethod.cash
              ? 'cash'
              : 'online',
        },
        {
          label: this.TOOKAN_SPECIAL_INSTRUCTION_LABEL,
          data: order.order_remarks,
        },
      ],
      template_name: this.TOOKAN_TEMPLATE_NAME,
    };
  }

  private async constructPickupTask(
    branch: BranchDocument,
    parentOrder: OrderDocument,
    subOrder: OrderDocument,
  ): Promise<TookanTask> {
    const branchLocation = await this.savedLocationService.getDetails(
      branch.locationId.toHexString(),
    );
    return {
      address: `${this.helperService.convertLocationToString(
        branchLocation,
        LocationType.PICKUP,
      )} - ${subOrder.code}`,
      email: 'default' + randomestring.generate(5) + '@e-butler.com',
      job_description: subOrder?.order_remarks,
      name: subOrder?.order_remarks,
      order_id: subOrder?.code,
      time: moment(subOrder?.pickup_date).add(5, 'minutes').format('HH:mm'),
      latitude: branchLocation.latitude,
      longitude: branchLocation.longitude,
      barcode: subOrder?.barCode.toString(),
      ref_images: [],
      template_data: [
        {
          label: this.TOOKAN_PAYMENT_AMOUNT_LABEL,
          data:
            subOrder?.payment_method == OrderPaymentMethod.online
              ? 'Paid Already'
              : subOrder?.total_amount.toString(),
        },
        {
          label: this.TOOKAN_PAYMENT_METHOD_LABEL,
          data:
            (subOrder?.payment_method == OrderPaymentMethod.online &&
              subOrder?.payment_status != OrderPaymentStatus.COMPLETED) ||
            subOrder?.payment_method == OrderPaymentMethod.cash
              ? 'cash'
              : 'online',
        },
        {
          label: this.TOOKAN_SPECIAL_INSTRUCTION_LABEL,
          data: `Brand Name: ${subOrder?.code} - Code: ${subOrder?.code} - Remarks: ${subOrder?.order_remarks}`,
        },
      ],
      template_name: this.TOOKAN_TEMPLATE_NAME,
    };
  }

  private getPickupTaskIds(tookanTask: any) {
    const pickupsIds: number[] = [];
    for (let i = 0; i < tookanTask.pickups.length; i++) {
      pickupsIds.push(tookanTask['pickups'][i]['job_id']);
    }
    return pickupsIds;
  }

  private async updateOrderTookanDetails(
    order: OrderDocument,
    createdPickupTasks: any,
    createdDeliveryTasks: any,
  ) {
    order.assigned_driver_name = 'Branch Dispatcher';
    order.tookan_job_id = createdDeliveryTasks['deliveries'][0]['job_id'];
    order.tookanDeliveryJobId = createdDeliveryTasks['deliveries'][0]['job_id'];
    order.tookan_delivery_track_url =
      createdDeliveryTasks['deliveries'][0]['result_tracking_link'];
    order.tookanPickupJobIds = this.getPickupTaskIds(createdPickupTasks);
    order.deliveryMethod = DeliveryMethod.BRANCH_DRIVERS;
    await order.save();
  }
}
