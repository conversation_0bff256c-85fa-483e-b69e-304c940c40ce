// EBL-4696 Punch card v1.3 - Rewarding Scheme
// Rename punch card achievement borderDisplay to stampOnEarn

// As of mongodb 7.0, "$rename does not work on embedded documents in arrays."
// https://www.mongodb.com/docs/manual/reference/operator/update/rename/#rename-embedded-document-fields

db.punchcards
  .find(
    { 'achievements.borderDisplay': { $exists: true } },
    { achievements: 1 },
  )
  .forEach(({ _id, achievements }) => {
    const newAchievements = achievements.map(({ borderDisplay, ...rest }) => ({
      ...rest,
      ...(borderDisplay && { stampOnEarn: borderDisplay }),
    }));

    db.punchcards.updateOne(
      { _id },
      { $set: { achievements: newAchievements } },
    );
  });

db.customers
  .find(
    {
      'punchCardProgress.punchCard.achievements.borderDisplay': {
        $exists: true,
      },
    },
    { punchCardProgress: 1 },
  )
  .forEach(({ _id, punchCardProgress }) => {
    const newPunchCardProgress = punchCardProgress.map(
      ({ punchCard, ...rest }) => ({
        ...rest,
        punchCard: {
          ...punchCard,
          achievements: punchCard.achievements.map(
            ({ borderDisplay, ...rest }) => ({
              ...rest,
              ...(borderDisplay && { stampOnEarn: borderDisplay }),
            }),
          ),
        },
      }),
    );
    db.customers.updateOne(
      { _id },
      { $set: { punchCardProgress: newPunchCardProgress } },
    );
  });
