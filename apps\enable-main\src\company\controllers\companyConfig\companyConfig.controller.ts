import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  SetMetadata,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { HelperService } from '../../../shared/services/helper/helper.service';
import {
  CompanyConfigBillingToUpdate,
  CompanyConfigIDParam,
  CompanyConfigIntegrationToUpdate,
  CompanyConfigPaymentToUpdate,
  CompanyConfigQuery,
  CompanyConfigSetupToUpdate,
  CompanyConfigToCreate,
  CompanyConfigToIndex,
  CompanyDeliveryConfigToUpdate,
} from '@app/shared-stuff';
import { CompanyConfigService } from '../../services/companyConfig/companyConfig.service';

@Controller('companyconfig')
@ApiTags('Company Config')
@SetMetadata('module', 'companyconfig')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@UsePipes(
  new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidUnknownValues: true,
  }),
)
export class CompanyConfigController {
  constructor(
    private companyConfigService: CompanyConfigService,
    private helperService: HelperService,
  ) {}

  @Get()
  @SetMetadata('action', 'get_all')
  async index(
    @Query() companyConfigToIndex: CompanyConfigToIndex,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      if (req['company_id']) {
        companyConfigToIndex.company = req['company_id'];
      }
      const companyConfigs =
        await this.companyConfigService.index(companyConfigToIndex);
      const totalCompanyConfigs = [];
      return this.helperService.handelSuccessResponse(
        HttpStatus.OK,
        'success to get company configs',
        {
          companyConfigs,
          totalCompanyConfigs,
        },
        res,
      );
    } catch (error) {
      return this.helperService.handelError(error, res);
    }
  }

  @Get('payment/methods')
  @SetMetadata('action', 'index_available_payment_methods')
  async indexAvailablePaymentMethods(
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const paymentMethods =
        await this.companyConfigService.getPaymentMethods();
      return this.helperService.handelSuccessResponse(
        HttpStatus.OK,
        'success to get Payment Method Available',
        {
          paymentMethods,
        },
        res,
      );
    } catch (error) {
      return this.helperService.handelError(error, res);
    }
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() companyConfigToCreate: CompanyConfigToCreate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const currentUser = req['current'];
      if (req['company_id']) {
        companyConfigToCreate.company = req['company_id'];
      }
      const createdCompanyConfig = await this.companyConfigService.create(
        companyConfigToCreate,
        currentUser,
      );
      return this.helperService.handelSuccessResponse(
        HttpStatus.CREATED,
        'success to create company config',
        { createdCompanyConfig },
        res,
      );
    } catch (error) {
      return this.helperService.handelError(error, res);
    }
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  async getDetails(
    @Param() { id }: CompanyConfigIDParam,
    @Query() { pathToFetch }: CompanyConfigQuery,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      let companyConfig: any;
      // if the clients requested a specific part of the company config
      if (pathToFetch) {
        companyConfig = await this.companyConfigService.getPathDetails(
          id,
          pathToFetch,
          req['company_id'],
        );
      } else {
        // if the client requested the whole compnay company config
        companyConfig = await this.companyConfigService.getDetails(
          id,
          req['company_id'],
        );
      }
      return this.helperService.handelSuccessResponse(
        HttpStatus.OK,
        'success to get company config details',
        { companyConfig },
        res,
      );
    } catch (error) {
      return this.helperService.handelError(error, res);
    }
  }

  @Put('setup')
  @SetMetadata('action', 'update')
  async updateSetup(
    @Body() companyConfigSetupToUpdate: CompanyConfigSetupToUpdate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const currentUser = req['current'];
      if (req['company_id']) {
        companyConfigSetupToUpdate.company = req['company_id'];
      }
      const updatedCompanyConfig = await this.companyConfigService.updateSetup(
        companyConfigSetupToUpdate,
        currentUser,
      );
      return this.helperService.handelSuccessResponse(
        HttpStatus.OK,
        'success to update company config',
        { updatedCompanyConfig },
        res,
      );
    } catch (error) {
      return this.helperService.handelError(error, res);
    }
  }

  @Put('integration')
  @SetMetadata('action', 'update')
  async updateIntegration(
    @Body() companyConfigIntegrationToUpdate: CompanyConfigIntegrationToUpdate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const currentUser = req['current'];
      if (req['company_id']) {
        companyConfigIntegrationToUpdate.company = req['company_id'];
      }
      const updatedCompanyConfig =
        await this.companyConfigService.updateIntegration(
          companyConfigIntegrationToUpdate,
          currentUser,
        );
      return this.helperService.handelSuccessResponse(
        HttpStatus.OK,
        'success to update company config',
        { updatedCompanyConfig },
        res,
      );
    } catch (error) {
      return this.helperService.handelError(error, res);
    }
  }

  @Put('delivery')
  @SetMetadata('action', 'update')
  async updateDeliveryConfig(
    @Body() companyDeliveryConfigToUpdate: CompanyDeliveryConfigToUpdate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const currentUser = req['current'];
      if (req['company_id']) {
        companyDeliveryConfigToUpdate.company = req['company_id'];
      }
      const updatedCompanyConfig =
        await this.companyConfigService.updateDeliveryConfig(
          companyDeliveryConfigToUpdate,
          currentUser,
        );
      return this.helperService.handelSuccessResponse(
        HttpStatus.OK,
        'success to update company config',
        { updatedCompanyConfig },
        res,
      );
    } catch (error) {
      return this.helperService.handelError(error, res);
    }
  }

  @Put('billing')
  @SetMetadata('action', 'update')
  async updateBilling(
    @Body() companyConfigBillingToUpdate: CompanyConfigBillingToUpdate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const currentUser = req['current'];
      if (req['company_id']) {
        companyConfigBillingToUpdate.company = req['company_id'];
      }
      const updatedCompanyConfig =
        await this.companyConfigService.updateBilling(
          companyConfigBillingToUpdate,
          currentUser,
        );
      return this.helperService.handelSuccessResponse(
        HttpStatus.OK,
        'success to update company config',
        { updatedCompanyConfig },
        res,
      );
    } catch (error) {
      return this.helperService.handelError(error, res);
    }
  }

  @Put('payment')
  @SetMetadata('action', 'update')
  async updatePayment(
    @Body() companyConfigPaymentToUpdate: CompanyConfigPaymentToUpdate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const currentUser = req['current'];
      if (req['company_id']) {
        companyConfigPaymentToUpdate.company = req['company_id'];
      }
      const updatedCompanyConfig =
        await this.companyConfigService.updatePayment(
          companyConfigPaymentToUpdate,
          currentUser,
        );
      return this.helperService.handelSuccessResponse(
        HttpStatus.OK,
        'success to update company config',
        { updatedCompanyConfig },
        res,
      );
    } catch (error) {
      return this.helperService.handelError(error, res);
    }
  }

  @Delete(':id')
  @SetMetadata('action', 'delete')
  async delete(
    @Param() { id }: CompanyConfigIDParam,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const currentUser = req['current'];

      const deletedCategory = await this.companyConfigService.delete(
        id,
        req['company_id'],
        currentUser,
      );
      return this.helperService.handelSuccessResponse(
        HttpStatus.OK,
        'success to delete company config',
        { deletedCategory },
        res,
      );
    } catch (error) {
      return this.helperService.handelError(error, res);
    }
  }
}
