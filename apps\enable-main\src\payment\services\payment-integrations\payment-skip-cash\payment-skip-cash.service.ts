import {
  CollectionName,
  LanguageToLanguageCode,
  LoggerService,
  OrderDocument,
  OrderStatusEnum,
  PaymentDocument,
  PaymentGatewayType,
  PaymentMethodUsed,
  PaymentStatusEnum,
  responseCode,
  SkipCashConfiguration,
  SkipCashPaymentStatus,
  SkipCashWebhookDto,
} from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import * as crypto from 'crypto';
import { createHmac } from 'crypto';
import { Model } from 'mongoose';
import { lastValueFrom } from 'rxjs';
import * as uuidApiKey from 'uuid-apikey';
import { CompanyService } from '../../../../company/services/company/company.service';
import { ENABLE_TECH_URL } from '../../../constants/payment-callback.const';
import { PaymentLogAction } from '../../../enums/payment-log-action.enum';
import { PaymentLogRepositoryInterface } from '../../../repositories/interfaces/payment.log.repository.interface';
import { PaymentHelpersService } from '../../payment-helpers/payment-helpers.service';
import { SkipCashPaymentToCreate } from './../../../dto/payment-skip-cash.dto';

@Injectable()
export class PaymentSkipCashService {
  private readonly logger = new LoggerService(PaymentSkipCashService.name);

  secretKey = '';
  BaseUrl = '';
  keyId = '';

  constructor(
    private httpService: HttpService,
    private configService: ConfigService,
    @InjectModel(CollectionName.ORDER)
    private paymentOrderModel: Model<OrderDocument>,
    private companyService: CompanyService,
    private paymentHelperService: PaymentHelpersService,
    @Inject('PaymentLogRepositoryInterface')
    private paymentLogRepository: PaymentLogRepositoryInterface,
  ) {
    this.BaseUrl = this.configService.get('SKIP_CASH_BASE_URL');
  }

  async processPayment(
    payment: PaymentDocument,
    config: SkipCashConfiguration,
  ) {
    const order = payment.order_code
      ? await this.paymentOrderModel.findOne({ code: payment.order_code })
      : undefined;

    if (
      order &&
      (order.status === OrderStatusEnum.CANCELED ||
        order.status === OrderStatusEnum.FAILED)
    )
      throw new BadRequestException(
        'The order is canceled or failed',
        responseCode.ORDER_IS_CANCELED_OR_FAILED.toString(),
      );

    if (payment.gateway_link) return payment.gateway_link;

    this.secretKey = config.skipCashKeySecret;
    this.keyId = config.skipCashKeyId;

    const uuid = (uuidApiKey as any).create().uuid;
    const skipCashPaymentToCreate: SkipCashPaymentToCreate = {
      Uid: uuid,
      KeyId: this.keyId,
      Amount: payment.amount.toFixed(2).toString(),
      FirstName: payment.customer_name,
      LastName: 'enable',
      Phone: payment.country_code + payment.customer_phone,
      Email: payment.customer_phone + '@enable.tech',
      Street: 'Doha',
      City: 'DH',
      State: 'DH',
      Country: 'QA',
      PostalCode: '00000',
      TransactionId: payment.code,
      Custom1: payment._id.toHexString(),
    };

    const signature = this.calculateCreatePaymentSignature(
      skipCashPaymentToCreate,
    );
    const response = await this.create(skipCashPaymentToCreate, signature);

    if (response['status'] === 'success') {
      payment.gateway_link = response['data']['resultObj']['payUrl'];
      payment.status = PaymentStatusEnum.GATEWAY_LINK_GENERATED;
      payment.payment_method_used = PaymentMethodUsed.SKIPCASH;
      await payment.save();
    }

    await this.paymentLogRepository.create({
      sentObject: skipCashPaymentToCreate,
      receivedObject: response,
      logAction: PaymentLogAction.PaymentProcessedFromSkipCash,
      paymentCode: payment.code,
      paymentId: payment._id,
    });

    return payment.gateway_link;
  }

  private calculateCreatePaymentSignature(
    skipCashPaymentToCreate: SkipCashPaymentToCreate,
  ) {
    let request = '';
    // Generate the comma-separated string from the properties
    Object.keys(skipCashPaymentToCreate).map((key) => {
      if (skipCashPaymentToCreate[key]) {
        request += `${key}=${skipCashPaymentToCreate[key]},`;
      }
    });
    // Removing the last comma
    request = request.slice(0, -1);

    // StartCompute Hash using the key-secret
    const hash = createHmac('sha256', this.secretKey);
    hash.update(request);

    // Return the Hash value in BASE64
    return hash.digest('base64');
  }

  async fireSkipCashWebhook(
    skipCashWebhookDto: SkipCashWebhookDto,
    authorization?: string,
  ) {
    this.logger.log('skipCashWebhookDto', skipCashWebhookDto);

    const { PaymentId, Amount, StatusId, TransactionId, Custom1, VisaId } =
      skipCashWebhookDto;

    const { gatewayType, gatewayConfig, payment } =
      await this.paymentHelperService.getPaymentAndConfiguration(TransactionId);

    this.logger.log('all payment details', {
      gatewayType,
      gatewayConfig,
      payment,
    });

    if (payment.status === PaymentStatusEnum.TRANSACTION_COMPLETED) {
      await this.paymentLogRepository.create({
        sentObject: {
          note: 'The Payment is Successful and webhook is ignored',
        },
        receivedObject: skipCashWebhookDto,
        logAction: PaymentLogAction.SKIP_CASH_WEBHOOK_FIRED,
        paymentCode: payment.code,
        paymentId: payment._id,
      });
      return;
    }

    try {
      const company = await this.companyService.get_details(
        payment.company['_id'],
      );

      if (gatewayType !== PaymentGatewayType.SKIP_CASH) return;

      const skipCashWebHookKey = (
        gatewayConfig.configuration as SkipCashConfiguration
      )?.skipCashWebHookKey;

      this.verifySignature(
        { PaymentId, Amount, StatusId, TransactionId, Custom1, VisaId },
        authorization,
        skipCashWebHookKey,
      );

      const order = payment.order_code
        ? await this.paymentOrderModel.findOne({ code: payment.order_code })
        : undefined;

      const { newPaymentStatus, responseCode } =
        this.determinePaymentStatus(StatusId);

      if (newPaymentStatus !== PaymentStatusEnum.PENDING) {
        await this.paymentHelperService.saveTransaction(
          payment,
          PaymentId,
          skipCashWebhookDto,
          PaymentGatewayType.SKIP_CASH,
        );

        await this.paymentHelperService.handlePaymentStatusLogic(
          payment,
          newPaymentStatus,
          order,
          company,
        );

        const callBackURL = this.paymentHelperService.generateCallBackUrl(
          payment,
          company,
          responseCode,
          order,
        );

        await this.paymentLogRepository.create({
          sentObject: { callBackURL },
          receivedObject: skipCashWebhookDto,
          logAction: PaymentLogAction.SKIP_CASH_WEBHOOK_FIRED,
          paymentCode: payment.code,
          paymentId: payment._id,
        });

        this.paymentHelperService.firePusherEvent(
          callBackURL,
          payment,
          newPaymentStatus,
        );

        payment.integrationInfo = {
          ...(payment.integrationInfo ?? {}),
          callbackUrl: callBackURL,
        };
        await payment.save();

        return callBackURL;
      } else {
        payment.status = newPaymentStatus;
        await payment.save();

        await this.paymentLogRepository.create({
          sentObject: { paymentStatus: newPaymentStatus },
          receivedObject: skipCashWebhookDto,
          logAction: PaymentLogAction.SKIP_CASH_WEBHOOK_PENDING,
          paymentCode: payment.code,
          paymentId: payment._id,
        });
      }
    } catch (exception) {
      this.logger.error('skipcash payment exception', exception);
      payment.status = PaymentStatusEnum.INTERNAL_FAILED;
      await payment.save();
      await this.paymentLogRepository.create({
        sentObject: { exception },
        receivedObject: {},
        logAction: PaymentLogAction.SKIP_CASH_WEBHOOK_FAILED,
        paymentCode: payment.code,
        paymentId: payment._id,
      });
    }
  }

  private verifySignature(
    fields: Record<string, any>,
    authorization: string,
    skipCashWebHookKey: string,
  ) {
    const signatureString = this.createSignatureString(fields);
    const calculatedSignature = this.calculateHmacSignature(
      signatureString,
      skipCashWebHookKey,
    );

    if (authorization !== calculatedSignature) {
      throw new HttpException(
        'Unauthorized: Invalid Signature',
        HttpStatus.UNAUTHORIZED,
      );
    }
  }

  private createSignatureString(fields: Record<string, any>) {
    return Object.entries(fields)
      .filter(([, value]) => value !== null && value !== undefined)
      .map(([key, value]) => `${this.capitalizeFirstLetter(key)}=${value}`)
      .join(',');
  }

  private capitalizeFirstLetter(string: string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
  }

  private calculateHmacSignature(
    signature: string,
    skipCashWebHookKey: string,
  ): string {
    return crypto
      .createHmac('sha256', skipCashWebHookKey)
      .update(signature)
      .digest('base64');
  }

  private determinePaymentStatus(statusId: number) {
    const statusMap = new Map<
      number,
      { status: PaymentStatusEnum; code?: string }
    >([
      [SkipCashPaymentStatus.PENDING, { status: PaymentStatusEnum.PENDING }],
      [
        SkipCashPaymentStatus.PAID,
        { status: PaymentStatusEnum.TRANSACTION_COMPLETED, code: '000' },
      ],
    ]);

    const defaultStatus = {
      status: PaymentStatusEnum.UNSUCCESSFUL,
      code: '152',
    };

    const { status: newPaymentStatus, code: responseCode } =
      statusMap.get(statusId) || defaultStatus;

    return { newPaymentStatus, responseCode };
  }

  async afterPaymentReturned(data: any) {
    const SECURE_PAYMENT_ROUTE = this.configService.get<string>(
      'SECURE_PAYMENT_ROUTE',
    );
    const { gatewayType, payment } =
      await this.paymentHelperService.getPaymentAndConfiguration(
        data['transId'],
      );

    if (!payment || gatewayType !== PaymentGatewayType.SKIP_CASH) {
      return ENABLE_TECH_URL;
    }

    const callbackURL =
      SECURE_PAYMENT_ROUTE +
      `?code=${payment.code}&&lang=${LanguageToLanguageCode[payment.language]}`;

    return callbackURL;
  }

  private async create(
    skipCashPaymentToCreate: SkipCashPaymentToCreate,
    signature: string,
  ) {
    const URL = this.BaseUrl + '/api/v1/payments';

    return lastValueFrom(
      this.httpService.post(URL, skipCashPaymentToCreate, {
        headers: { Authorization: signature },
      }),
    )
      .then((data) => {
        return { status: 'success', data: data.data };
      })
      .catch((err) => {
        return { status: 'error', data: err.response.data };
      });
  }

  private async fetchPaymentDetails(paymentId: string, clientId: string) {
    return new Promise((resolve) => {
      const URL = this.BaseUrl + '/api/v1/payments/' + paymentId;
      this.httpService
        .get(URL, {
          headers: { Authorization: clientId },
        })
        .subscribe(
          (data) => {
            resolve({ status: 'success', data: data.data });
          },
          (err) => {
            resolve({ status: 'error', data: err.toJSON() });
          },
        );
    });
  }
}
