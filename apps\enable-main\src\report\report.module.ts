import { MicroserviceCommunicationModule } from '@app/shared-stuff/modules/microservice-communication/microservice-communication.module';
import { Module } from '@nestjs/common';
import { SharedModule } from './../shared/shared.module';
import { ReportV1Controller } from './controllers/report-v1/report-v1.controller';
import { ReportController } from './controllers/report/report.controller';

@Module({
  imports: [
    SharedModule,
    MicroserviceCommunicationModule.forChild('enable-main-reporting-producer'),
  ],
  controllers: [ReportController, ReportV1Controller],
  providers: [],
})
export class ReportModule {}
