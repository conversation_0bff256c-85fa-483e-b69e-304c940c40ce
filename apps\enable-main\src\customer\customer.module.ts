import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { BrandModule } from '../brand/brand.module';
import { CompanyModule } from '../company/company.module';
import { LoyaltyTierLogModule } from '../loyalty-tier-log/loyalty-tier-log.module';
import { NotificationModule } from '../notification/notification.module';
import { PunchCardReadModule } from '../punch-card/modules/punch-card-read/punch-card-read.module';
import { CustomerController } from './controllers/customer.controller';
import { CustomerCronJobService } from './cronjobs/customer-cron-job.service';
import { CustomerListener } from './listeners/customer.listener';
import { CustomerCodeModule } from './modules/customer-code/customer-code.module';
import { CustomerEmitterModule } from './modules/customer-emitter/customer-emitter.module';
import { CustomerIndexModule } from './modules/customer-index/customer-index.module';
import { CustomerIoModule } from './modules/customer-io/customer-io.module';
import { CustomerLoyaltyMemberModule } from './modules/customer-loyalty-member/customer-loyalty-member.module';
import { CustomerLoyaltyModule } from './modules/customer-loyalty/customer-loyalty.module';
import { CustomerNotificationModule } from './modules/customer-notification/customer-notification.module';
import { CustomerOrderModule } from './modules/customer-order/customer-order.module';
import { CustomerPassModule } from './modules/customer-pass/customer-pass.module';
import { CustomerPromoteModule } from './modules/customer-promote/customer-promote.module';
import { CustomerPunchCardModule } from './modules/customer-punch-card/customer-punch-card.module';
import { CustomerReadModule } from './modules/customer-read/customer-read.module';
import { CustomerReplacementsModule } from './modules/customer-replacements/customer-replacements.module';
import { CustomerRepositoryModule } from './modules/customer-repository/customer-repository.module';
import { CustomerTierInfoModule } from './modules/customer-tier-info/customer-tier-info.module';
import { CustomerTierModule } from './modules/customer-tier/customer-tier.module';
import { CustomerTokenModule } from './modules/customer-token/customer-token.module';
import { CustomerUserModule } from './modules/customer-user/customer-user.module';
import { CustomerWebstoreModule } from './modules/customer-webstore/customer-webstore.module';
import { CustomerWriteModule } from './modules/customer-write/customer-write.module';

@Module({
  controllers: [CustomerController],
  providers: [CustomerCronJobService, CustomerListener],
  imports: [
    CompanyModule,
    BrandModule,
    EventEmitterModule,
    PunchCardReadModule,
    NotificationModule,
    LoyaltyTierLogModule,
    CustomerRepositoryModule,
    CustomerCodeModule,
    CustomerIoModule,
    CustomerLoyaltyModule,
    CustomerPunchCardModule,
    CustomerNotificationModule,
    CustomerOrderModule,
    CustomerReplacementsModule,
    CustomerUserModule,
    CustomerTierModule,
    CustomerTierInfoModule,
    CustomerReadModule,
    CustomerWriteModule,
    CustomerPassModule,
    CustomerWebstoreModule,
    CustomerLoyaltyMemberModule,
    CustomerTokenModule,
    CustomerPromoteModule,
    CustomerIndexModule,
    CustomerEmitterModule,
  ],
  exports: [CustomerCronJobService],
})
export class CustomerModule {}
