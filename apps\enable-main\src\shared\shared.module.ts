import {
  CollectionName,
  ImageSchema,
  InternalErrorInterceptor,
  InternalErrorLog,
  InternalErrorLogSchema,
} from '@app/shared-stuff';
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { MongooseModule } from '@nestjs/mongoose';
import { ScheduleModule } from '@nestjs/schedule';
import { OrderLogSchema } from '../order/models/order.log.model';
import { StorageModule } from '../storage/storage.module';
import { ImageController } from './controller/image.controller';
import { SettingSchema } from './models/settings.model';
import { CurrentUserService } from './services/current-user/current-user.service';
import { HelperService } from './services/helper/helper.service';
import { ImageService } from './services/image/image.service';
import { SettingsService } from './services/settings/settings.service';
import { CronJobController } from './controller/cron-job.controller';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { CronJobService } from './services/cron-job/cron-job.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'SettingsModel', schema: SettingSchema },
      { name: 'OrderLog', schema: OrderLogSchema },
      { name: InternalErrorLog.name, schema: InternalErrorLogSchema },
      { name: CollectionName.IMAGE, schema: ImageSchema },
    ]),
    ScheduleModule,
    ConfigModule,
    HttpModule,
    EventEmitterModule,
    StorageModule,
  ],
  controllers: [ImageController, CronJobController],
  providers: [
    ImageService,
    HelperService,
    SettingsService,
    CronJobService,
    CurrentUserService,
    {
      provide: APP_INTERCEPTOR,
      useClass: InternalErrorInterceptor,
    },
  ],
  exports: [
    HelperService,
    ImageService,
    SettingsService,
    CurrentUserService,
    MongooseModule,
  ],
})
export class SharedModule {}
