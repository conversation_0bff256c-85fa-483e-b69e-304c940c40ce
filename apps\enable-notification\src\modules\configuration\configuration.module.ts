import {
  Configuration,
  ConfigurationSchema,
} from './models/configuration.model';
import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';
import { ConfigurationService } from './services/configuration/configuration.service';
import { ConfigurationController } from './controllers/configuration/configuration.controller';

@Module({
  imports: [
    MongooseModule.forFeature([
      { schema: ConfigurationSchema, name: Configuration.name },
    ]),
  ],
  providers: [
    {
      provide: 'ConfigurationServiceInterface',
      useClass: ConfigurationService,
    },
  ],
  exports: ['ConfigurationServiceInterface'],
  controllers: [ConfigurationController],
})
export class ConfigurationModule {}
