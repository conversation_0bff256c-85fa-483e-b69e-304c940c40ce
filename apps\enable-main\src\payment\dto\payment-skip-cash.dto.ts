import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

// Remember the SORTING-ORDER is very important
export class SkipCashPaymentToCreate {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  Uid: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  KeyId: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  Amount: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  FirstName: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  LastName: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  Phone: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  Email: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  Street: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  City: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  State: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  Country: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  PostalCode: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  TransactionId: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  Custom1?: string;
}
