import {
  CollectionName,
  CountryDialCode,
  Image,
  IsPhoneNumberForRegion,
} from '@app/shared-stuff';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { hash } from 'bcrypt';
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import * as moment from 'moment-timezone';
import { HydratedDocument, Types } from 'mongoose';
import { UserOrderToAcknowledge } from '../dto/user.dto';
import { CallCenterProvider } from '../enums/call-center-provider.enum';

function getMomentDefault() {
  return moment().toISOString();
}

export type UserDocument = HydratedDocument<User>;
@Schema({ timestamps: true })
export class User {
  @Prop({
    required: true,
    index: true,
    lowercase: true,
  })
  name: string;

  @Prop({
    type: String,
    default: 'employee',
  })
  position: string;

  @Prop({
    required: true,
    unique: true,
    lowercase: true,
  })
  email: string;

  @Prop({
    required: true,
    unique: true,
  })
  @IsNotEmpty()
  @IsPhoneNumberForRegion('countryCode')
  phone: string;

  @Prop({
    type: String,
    enum: CountryDialCode,
    required: true,
  })
  @IsOptional()
  @IsEnum(CountryDialCode)
  countryCode: CountryDialCode;

  @Prop({
    required: true,
  })
  password: string;

  @Prop({
    type: String,
    enum: ['active', 'not_active'],
    default: 'active',
  })
  status: string;

  @Prop({
    type: Image,
  })
  image: Image;

  @Prop({
    type: [{ type: Types.ObjectId, ref: CollectionName.ROLES }],
    required: true,
  })
  roles: Types.ObjectId[];

  @Prop({
    type: () => [UserOrderToAcknowledge],
    required: false,
  })
  ordersToBeAcknowledged: UserOrderToAcknowledge[];

  @Prop({
    type: Number,
    default: 0,
  })
  new_notification_count: number;

  @Prop({
    type: String,
  })
  auth_token: string;

  @Prop({
    type: String,
    default: moment().startOf('month').format('MMMM'),
  })
  month: string;

  @Prop({
    type: String,
    required: false,
  })
  socketIoId: string;

  @Prop({
    type: Boolean,
    required: false,
    default: false,
  })
  activeNow: boolean;

  @Prop({
    type: Number,
    default: moment().week(),
  })
  week: number;

  @Prop({
    type: Number,
    default: moment().date(),
  })
  day: number;

  @Prop({
    type: Number,
    default: moment().year(),
  })
  year: number;

  @Prop({
    type: Date,
    default: getMomentDefault,
  })
  last_active: Date;

  @Prop({
    type: String,
    required: false,
  })
  fcm_token: string;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: CollectionName.COMPANY,
  })
  company: Types.ObjectId;

  @Prop({
    type: [Types.ObjectId],
    required: false,
  })
  companies: Types.ObjectId[];

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: CollectionName.BRANCH,
  })
  branch: string;

  @Prop({
    type: [Types.ObjectId],
    ref: CollectionName.BRANCH,
    required: false,
    default: [],
  })
  branches: Types.ObjectId[];

  @Prop({
    type: String,
    default: 'male',
    enum: ['male', 'female'],
  })
  gender: string;

  @Prop({
    type: {},
    required: false,
  })
  updatedBy: {};

  @Prop({
    type: {},
    required: false,
  })
  createdBy: {};

  @Prop({
    type: {},
    required: false,
  })
  assignedTo: {};

  @Prop({
    type: {},
    required: false,
  })
  deletedBy: {};

  @Prop({
    type: Date,
    required: false,
  })
  deletedAt: Date;

  @Prop({
    type: String,
    enum: CallCenterProvider,
    required: false,
    default: CallCenterProvider.MAQSAM,
  })
  callCenterProvider = CallCenterProvider.MAQSAM;
}
const UserSchema = SchemaFactory.createForClass(User);

UserSchema.index({ createdAt: 1 });
// User Pre-saved MiddelWare
UserSchema.pre('save', async function () {
  if (!this.isNew) {
    return;
  }
  const CurrentUser = this;
  const hashedPassword = await hash(CurrentUser.get('password'), 10);
  CurrentUser.set('password', hashedPassword);
});

export { UserSchema };
