import {
  GenericExceptionFilter,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Logger,
  Post,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { CallbackFalconFlexDto } from '../dtos/callback-falcon-flex.dto';
import { FalconFlexService } from '../services/falcon-flex.service';

@Controller('falconFlex')
@UseInterceptors(TransformInterceptor)
@UseFilters(GenericExceptionFilter)
@ApiTags('falconFlex Controller')
@SetMetadata('module', 'falconFlex')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class FalconFlexController {
  private readonly logger = new Logger(FalconFlexController.name);

  constructor(private readonly falconFlexService: FalconFlexService) {}

  @Post('public/falconFlex/updateTaskStatus')
  @SetMetadata('public', true)
  async updateFalconFlexDeliveryStatus(
    @Body() callbackFalconFlexDto: CallbackFalconFlexDto,
  ) {
    this.logger.log(
      'Falcon Flex Webhook',
      JSON.stringify(callbackFalconFlexDto),
    );
    await this.falconFlexService.updateFalconFlexDeliveryTaskStatus(
      callbackFalconFlexDto,
    );
  }
}
