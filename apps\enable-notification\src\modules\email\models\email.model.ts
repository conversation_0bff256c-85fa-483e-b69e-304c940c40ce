import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type EmailDocument = Email & Document;

@Schema({
  timestamps: true,
  collection: 'email_logs',
})
export class Email {
  @Prop({
    type: String,
    required: true,
  })
  to: string;

  @Prop({
    type: String,
    required: true,
  })
  from: string;

  @Prop({
    type: String,
    required: true,
  })
  subject: string;

  @Prop({
    type: String,
    required: false,
  })
  cc: string;

  @Prop({
    type: String,
    required: false,
  })
  bcc: string;

  @Prop({
    type: String,
    required: true,
  })
  content: string;

  @Prop({
    type: {},
    required: true,
  })
  rawResponse: {};
}

export const EmailSchema = SchemaFactory.createForClass(Email);
