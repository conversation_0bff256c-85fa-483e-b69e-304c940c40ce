import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class DeliverectOrderStatusToUpdate {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  orderId: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  receiptId: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  reason: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  timeStamp: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  status: number;
}
