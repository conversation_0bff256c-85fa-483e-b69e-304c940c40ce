// EBL-4227 Introduce new automation journeys customer filters / conditions
// init tier status

db.customers.updateMany(
  { firstTierEarnedAt: null, loyaltyTier: null, tierStatus: null },
  { $set: { tierStatus: 'NO_TIER' } },
);

const lastComputation = new Date();
lastComputation.setDate(1);
lastComputation.setHours(0, 0, 0, 0);
const afterLastComputation = new Date(lastComputation);
afterLastComputation.setHours(1);
db.customers.updateMany(
  {
    loyaltyTier: { $ne: null },
    tierUpdatedAt: { $gt: lastComputation },
    tierStatus: null,
  },
  { $set: { tierStatus: 'UPGRADED' } },
);

const maintainedCustomerIds = db.customers
  .aggregate([
    { $match: { tierStatus: null, loyaltyTier: { $ne: null } } },
    {
      $addFields: {
        loyaltyOrderRate: {
          $size: {
            $ifNull: [
              {
                $filter: {
                  input: '$orders',
                  as: 'order',
                  cond: {
                    $and: [
                      { $eq: ['$$order.status', 'COMPLETED'] },
                      {
                        $gte: ['$$order.pickup_date', new Date(2023, 7)],
                      },
                      {
                        $eq: ['$$order.isCartValueThresholdMet', true],
                      },
                      {
                        $in: [
                          '$$order.source',
                          [
                            'dine_in',
                            'walk_in',
                            'drive_thru',
                            'direct_call',
                            'whatsapp',
                            'webstore',
                            'pos',
                          ],
                        ],
                      },
                      {
                        $gte: ['$$order.pickup_date', '$carryOverUpdatedAt'],
                      },
                    ],
                  },
                },
              },
              [],
            ],
          },
        },
      },
    },
  ])
  .toArray()
  .filter(
    (customer) =>
      customer.loyaltyOrderRate + (customer.carryOverOrderRate || 0) >=
      customer.loyaltyTier.orderRateThreshold,
  )
  .map((customer) => customer._id);

db.customers.updateMany(
  { _id: { $in: maintainedCustomerIds } },
  { $set: { tierStatus: 'MAINTAINED' } },
);

db.customers.updateMany(
  { loyaltyTier: { $ne: null }, tierStatus: null },
  { $set: { tierStatus: 'NOT_MAINTAINED' } },
);
