// EBL-4099 [CRM] Create new triggers for non loyalty members
// Add ON_ORDER_CREATED_NON_LOYALTY_MEMBERS, ON_WEBSTORE_ORDER_CREATED_NON_LOYALTY_MEMBERS and ON_WHATSAPP_ORDER_NON_LOYALTY_MEMBERS
db.triggers.insertMany([
  {
    name: '[ORDER] ON_ORDER_CREATED_NON_LOYALTY_MEMBERS',
    client: 'ENABLE_MAIN',
    action: 'ON_ORDER_CREATED_NON_LOYALTY_MEMBERS',
    module: 'ORDER',
    replacement: [
      'firstName',
      'fullName',
      'loyaltyPointBalance',
      'loyaltyTier',
      'remainingOrdersCurrentTier',
      'remainingOrdersUpperTier',
      'upperTierDiscountValue',
      'tierDiscountValue',
      'tierDiscountOrderValueThreshold',
      'loyaltyRegistrationPageLink',
      'walletPassAccessPageLink',
      'walletPassLink',
      'ordableLink',
      'totalAmount',
      'orderCode',
      'orderSource',
      'paymentLinkId',
      'paymentLink',
      'brandName',
      'customerName',
      'phoneNumber',
      'email',
      'companyName',
      'userName',
      'amountWithoutDelivery',
      'paymentMethod',
      'recipientName',
      'deliveryAction',
      'pickupDate',
      'deliveryDate',
      'locationShortenURL',
      'locationLink',
      'orderTrackingShortenURL',
      'orderTrackingLink',
      'paymentShortenURL',
      'orderEarnedLoyaltyPoints',
      'upperLoyaltyTier',
    ],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    name: '[ORDER] ON_WEBSTORE_ORDER_CREATED_NON_LOYALTY_MEMBERS',
    client: 'ENABLE_MAIN',
    action: 'ON_WEBSTORE_ORDER_CREATED_NON_LOYALTY_MEMBERS',
    module: 'ORDER',
    replacement: [
      'firstName',
      'fullName',
      'loyaltyPointBalance',
      'loyaltyTier',
      'remainingOrdersCurrentTier',
      'remainingOrdersUpperTier',
      'upperTierDiscountValue',
      'tierDiscountValue',
      'tierDiscountOrderValueThreshold',
      'loyaltyRegistrationPageLink',
      'walletPassAccessPageLink',
      'walletPassLink',
      'ordableLink',
      'totalAmount',
      'orderCode',
      'orderSource',
      'paymentLinkId',
      'paymentLink',
      'brandName',
      'customerName',
      'phoneNumber',
      'email',
      'companyName',
      'userName',
      'amountWithoutDelivery',
      'paymentMethod',
      'recipientName',
      'deliveryAction',
      'pickupDate',
      'deliveryDate',
      'locationShortenURL',
      'locationLink',
      'orderTrackingShortenURL',
      'orderTrackingLink',
      'paymentShortenURL',
      'orderEarnedLoyaltyPoints',
      'upperLoyaltyTier',
    ],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    name: '[ORDER] ON_WHATSAPP_ORDER_NON_LOYALTY_MEMBERS',
    client: 'ENABLE_MAIN',
    action: 'ON_WHATSAPP_ORDER_NON_LOYALTY_MEMBERS',
    module: 'ORDER',
    replacement: [
      'firstName',
      'fullName',
      'loyaltyPointBalance',
      'loyaltyTier',
      'remainingOrdersCurrentTier',
      'remainingOrdersUpperTier',
      'upperTierDiscountValue',
      'tierDiscountValue',
      'tierDiscountOrderValueThreshold',
      'loyaltyRegistrationPageLink',
      'walletPassAccessPageLink',
      'walletPassLink',
      'ordableLink',
      'totalAmount',
      'orderCode',
      'orderSource',
      'paymentLinkId',
      'paymentLink',
      'brandName',
      'customerName',
      'phoneNumber',
      'email',
      'companyName',
      'userName',
      'amountWithoutDelivery',
      'paymentMethod',
      'recipientName',
      'deliveryAction',
      'pickupDate',
      'deliveryDate',
      'locationShortenURL',
      'locationLink',
      'orderTrackingShortenURL',
      'orderTrackingLink',
      'paymentShortenURL',
      'orderEarnedLoyaltyPoints',
      'upperLoyaltyTier',
    ],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
]);
