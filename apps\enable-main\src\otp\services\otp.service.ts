import {
  HttpStatus,
  Inject,
  Injectable,
  InternalServerErrorException,
  UnauthorizedException,
} from '@nestjs/common';
import * as moment from 'moment-timezone';

import {
  ContactChannel,
  fillAnonymousTriggerUserDto,
  GenericTriggerModel,
  LanguageCode,
  LanguageCodeToLanguage,
  NotificationRecommendedIntegration,
  TemplateType,
  TriggerAction,
  TriggerModule,
} from '@app/shared-stuff';
import { BrandDocument } from '@app/shared-stuff/models/brand.model';
import { BrandServiceInterface } from '../../brand/services/brand/brand.service.interface';
import { CustomerTokenServiceInterface } from '../../customer/modules/customer-token/customer-token.service.interface';
import { CustomerWriteServiceInterface } from '../../customer/modules/customer-write/customer-write.service.interface';
import { TriggerService } from '../../notification/services/trigger/trigger.service';
import { parseArabicNumber } from '../../shared/services/helper/helper.service';
import { GenerateOTPDto } from '../dto/generate-otp.dto';
import { VerifyOTPResponseDTO } from '../dto/verify-otp-response.dto';
import { VerifyOTPDto } from '../dto/verify-otp.dto';
import { OTP, OTPDocument } from '../models/otp.model';
import { OTPRepositoryInterface } from '../repositories/otp.repository.interface';
import { OTPServiceInterface } from './otp.service.interface';

@Injectable()
export class OTPService implements OTPServiceInterface {
  constructor(
    @Inject(OTPRepositoryInterface)
    private readonly otpRepository: OTPRepositoryInterface,
    private readonly triggerService: TriggerService,
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandServiceInterface,
    @Inject(CustomerWriteServiceInterface)
    private readonly customerWriteService: CustomerWriteServiceInterface,
    @Inject(CustomerTokenServiceInterface)
    private readonly customerTokenService: CustomerTokenServiceInterface,
  ) {}

  async generateOTP(generateOTPDto: GenerateOTPDto): Promise<HttpStatus> {
    const brand = await this.brandService.findById(generateOTPDto.brandId);
    const expireAt =
      generateOTPDto.expiresIn &&
      moment.utc().add(generateOTPDto.expiresIn).isValid() &&
      moment.utc().add(generateOTPDto.expiresIn).isAfter(moment.utc())
        ? moment.utc().add(generateOTPDto.expiresIn).toDate()
        : moment.utc().add(1, 'hour').toDate();
    const otp = await this.findOrCreate(<OTP>{
      countryCode: generateOTPDto.countryCode,
      phone: generateOTPDto.phone,
      otp: Math.floor(Math.random() * 10000),
      brandId: brand._id,
      expireAt,
    });

    await this.triggerService.fireTrigger(
      await this.fillTriggerModel(brand, otp, generateOTPDto.language),
      generateOTPDto.templateType === TemplateType.SMS
        ? TriggerAction.ON_OTP_GENERATED
        : TriggerAction.ON_OTP_RESEND_AS_CHAT,
      [
        fillAnonymousTriggerUserDto(
          otp.phone,
          otp.countryCode,
          generateOTPDto.language,
        ),
      ],
      NotificationRecommendedIntegration.OTP,
    );

    return HttpStatus.CREATED;
  }

  private async findOrCreate(otp: OTP): Promise<OTPDocument> {
    const existingOtp = await this.otpRepository.findOne({
      phone: otp.phone,
      countryCode: otp.countryCode,
      brandId: otp.brandId,
      expireAt: { $gt: moment.utc().toDate() },
    });

    if (existingOtp) return existingOtp;
    return await this.otpRepository.create(otp);
  }

  private async fillTriggerModel(
    brand: BrandDocument,
    otpDocument: OTPDocument,
    lang: LanguageCode,
  ): Promise<GenericTriggerModel> {
    return {
      companyId: brand.companyId.toHexString(),
      senderId: brand.senderId,
      emailSenderId: brand.emailSenderId,
      countryCode: otpDocument.countryCode,
      triggerModule: TriggerModule.AUTH,
      language: lang,
      replacements: {
        otp: otpDocument.otp.toString().padStart(4, '0'),
      },
      branchId: '',
      brandId: brand._id.toHexString(),
      customerId: null,
      isGift: false,
      giftRecipientUser: null,
      createdBy: null,
      context: {},
    };
  }

  async verifyOTP(verifyOTPDto: VerifyOTPDto): Promise<VerifyOTPResponseDTO> {
    const otp = await this.otpRepository.findOne({
      countryCode: verifyOTPDto.countryCode,
      phone: verifyOTPDto.phone,
      otp: parseArabicNumber(verifyOTPDto.otp),
      brandId: verifyOTPDto.brandId,
    });

    if (!otp) throw new UnauthorizedException('Invalid OTP');
    if (otp.expireAt < moment.utc().toDate())
      throw new UnauthorizedException('Expired OTP. Please request a new one.');

    const brand = await this.brandService.findById(verifyOTPDto.brandId);
    const customer = await this.customerWriteService.updateOrCreate(
      {
        company: brand.companyId,
        country_code: verifyOTPDto.countryCode,
        phone: verifyOTPDto.phone,
        language: LanguageCodeToLanguage[verifyOTPDto.language],
        contact_channel: ContactChannel.LOYALTY_REGISTRATION_PAGE,
        verificationContext: {
          isVerified: true,
          brandId: brand._id,
          verifiedAt: moment.utc().toDate(),
        },
      },
      null,
    );
    if (!customer)
      throw new InternalServerErrorException(
        'Could not update or create customer',
      );

    const token = this.customerTokenService.generateCustomerToken({
      customerId: customer._id,
      companyId: customer.company,
      brandId: brand._id,
    });

    return { token, customer };
  }
}
