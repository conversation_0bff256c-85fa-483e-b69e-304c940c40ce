export enum CronExpressionEnum {
  EVERY_1ST_DAY_AT_11_30 = 'every1stDayAt1130',
  EVERY_DAY_AT_11_45_PM = 'everyDayAt1145PM',
  EVERY_DAY_AT_11_45_AM = 'everyDayAt1145AM',
  EVERY_DAY_AT_11_30_AM = 'everyDayAt1130AM',

  EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT = 'every1stDayOfMonthAtMidnight',
  EVERY_1ST_DAY_OF_QUARTER_AT_30_MIN_AFTER_MIDNIGHT = 'every1stDayOfQuarterAt30MinAfterMidnight',

  EVERY_FEBRUARY_18_AT_11_30 = 'everyFebruary18At1130',
  EVERY_30DAY_MONTH_20TH_AT_11_30 = 'every30DayMonth20thAt1130',
  EVERY_31DAY_MONTH_21ST_AT_11_30 = 'every31DayMonth21stAt1130',
  EVERY_QUARTER_30DAY_5TH_AT_11_30 = 'everyQuarter30Day5thAt1130',
  EVERY_QUARTER_31DAY_6TH_AT_11_30 = 'everyQuarter31Day6thAt1130',

  EVERY_30_SECONDS = 'every30secs',
  EVERY_MINUTE = 'every1min',
  EVERY_5_MINUTES = 'every5mins',
  EVERY_10_MINUTES = 'every10mins',
}
