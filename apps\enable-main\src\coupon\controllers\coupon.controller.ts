import {
  Coupon,
  CouponDocument,
  CouponIdDto,
  CouponIndexDto,
  CreateCouponDto,
  GenericExceptionFilter,
  TransformInterceptor,
  UpdateCouponDto,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  Param,
  Patch,
  Post,
  Query,
  SetMetadata,
  UseFilters,
  UseInterceptors,
  Version,
} from '@nestjs/common';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';
import { CurrentUserService } from '../../shared/services/current-user/current-user.service';
import { CouponServiceInterface } from '../services/coupon.service.interface';

@Controller('coupon')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags('Coupon')
@SetMetadata('module', 'coupon')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class CouponController {
  constructor(
    @Inject(CouponServiceInterface)
    private readonly couponService: CouponServiceInterface,
    private readonly currentUserService: CurrentUserService,
  ) {}

  @Get()
  @Version('2')
  @ApiOkResponse({ type: Coupon, isArray: true })
  @SetMetadata('public', 'true')
  async indexV2(
    @Query() couponIndexDto: CouponIndexDto,
  ): Promise<CouponDocument[]> {
    return await this.couponService.index(couponIndexDto);
  }

  @Get(':couponId')
  @ApiOkResponse({ type: Coupon })
  @SetMetadata('public', 'true')
  async getById(@Param() { couponId }: CouponIdDto): Promise<CouponDocument> {
    return await this.couponService.findById(couponId);
  }

  @Post()
  @ApiOkResponse({ type: Coupon })
  @SetMetadata('action', 'create')
  async create(
    @Body() createCouponDto: CreateCouponDto,
  ): Promise<CouponDocument> {
    if (!createCouponDto.companyId)
      createCouponDto.companyId = this.currentUserService.getCurrentCompanyId();
    this.currentUserService.validateAccessToCompany(createCouponDto.companyId);
    return await this.couponService.create(createCouponDto);
  }

  @Patch(':couponId')
  @ApiOkResponse({ type: Coupon })
  @SetMetadata('action', 'update')
  async update(
    @Param() { couponId }: CouponIdDto,
    @Body() updateCouponDto: Omit<UpdateCouponDto, 'couponId'>,
  ): Promise<CouponDocument> {
    if (!updateCouponDto.companyId)
      updateCouponDto.companyId = this.currentUserService.getCurrentCompanyId();
    this.currentUserService.validateAccessToCompany(updateCouponDto.companyId);
    const coupon = await this.couponService.update({
      ...updateCouponDto,
      couponId,
    });
    return coupon;
  }

  @Delete(':couponId')
  @ApiOkResponse({ type: Coupon })
  @SetMetadata('action', 'delete')
  async delete(@Param() { couponId }: CouponIdDto): Promise<string> {
    const currentUser = this.currentUserService.getCurrentUser();
    const companyId = this.currentUserService.getCurrentCompanyId();
    return await this.couponService.delete(couponId, companyId, currentUser);
  }
}
