import { LogError, OrderDocument } from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { AdlerService } from '../services/adler.service';

@Injectable()
export class AdlerListener {
  constructor(private adlerService: AdlerService) {}

  @OnEvent('order.acknowledged')
  @LogError()
  async onNewOrder(order: OrderDocument) {
    if (!order.branch || !order.branch['adlerOutletId']) {
      // order does not have a branch or the branch does not have integration with adler
      return;
    }
    if (order.adlerPosOrderId) return;
    await this.adlerService.createAdlerOrder(order);
  }

  @OnEvent('order.cancelled')
  @LogError()
  async cancelAdlerOrderStatus(order: OrderDocument) {
    if (!order.adlerPosOrderId) return;

    await this.adlerService.cancelAdlerOrder(order);
  }
}
