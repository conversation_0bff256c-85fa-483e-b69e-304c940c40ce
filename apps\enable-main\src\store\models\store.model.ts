import {
  CollectionName,
  EmbeddedBrandDto,
  ObjectIdTransform,
} from '@app/shared-stuff';
import { ShopifyConfig } from '@app/shared-stuff/dtos/webstore/shopify/shopify-config.dto';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Transform } from 'class-transformer';
import { HydratedDocument, Types } from 'mongoose';
import { ApiKeyWithId } from '../../user/models/apikey.model';
import { OrdableConfig } from '../dto/ordable-config.dto';
import { StoreProvider } from '../enumerations/store-provider.enum';

export type StoreDocument = HydratedDocument<Store>;

@Schema({ timestamps: true })
export class Store {
  @Prop({ required: false })
  link: string;

  @Prop({ required: false })
  apiKey: string;

  @Prop({ required: false })
  @Transform(({ value }) => value?.replace(/\/$/, ''))
  apiBaseUrl: string;

  @Prop({ type: Boolean, required: false, default: true })
  needsSync: boolean;

  @Prop({ required: true })
  name: string;

  @Prop({
    type: String,
    required: true,
    enum: Object.keys(StoreProvider),
    default: StoreProvider.ORDABLE,
  })
  provider: StoreProvider;

  @Prop({ type: Types.ObjectId, required: true, ref: CollectionName.COMPANY })
  @ObjectIdTransform()
  companyId: Types.ObjectId;

  @Prop({
    type: () => [EmbeddedBrandDto],
    required: true,
  })
  brands: EmbeddedBrandDto[];

  @Prop({
    type: () => ApiKeyWithId,
    required: false,
  })
  enableApiKey: ApiKeyWithId;

  @Prop({
    type: () => OrdableConfig,
    required: false,
  })
  ordableConfig?: OrdableConfig;

  @Prop({ type: ShopifyConfig, required: false })
  shopifyConfig?: ShopifyConfig;
}

export const StoreSchema = SchemaFactory.createForClass(Store);
