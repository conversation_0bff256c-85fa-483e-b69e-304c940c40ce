import { ApiProperty } from '@nestjs/swagger';

export class SolutionERPCustomer {
  @ApiProperty({
    type: String,
    required: false,
  })
  customer_code: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  name: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  address: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  phone: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  phone2: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  fax: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  email: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  notes: string;
}
