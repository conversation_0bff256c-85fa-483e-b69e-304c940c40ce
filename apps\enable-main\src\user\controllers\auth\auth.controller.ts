import { responseCode } from '@app/shared-stuff';
import {
  Body,
  Controller,
  Get,
  Headers,
  Inject,
  Post,
  Req,
  Res,
  SetMetadata,
} from '@nestjs/common';
import { ApiHeader, ApiTags } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { Types } from 'mongoose';
import { CompanyService } from '../../../company/services/company/company.service';
import { DeliveryConfigurationServiceInterface } from '../../../delivery/services/delivery-configuration/delivery-configuration.service.interface';
import { HelperService } from '../../../shared/services/helper/helper.service';
import { UserToLogin, UserToRegister } from '../../dto/user.dto';
import { AuthService } from '../../services/auth/auth.service';
import { PasswordToCompare } from './../../dto/user.dto';

@Controller('auth')
@ApiTags('Authentication')
export class AuthController {
  constructor(
    private authService: AuthService,
    private helperService: HelperService,
    private companyService: CompanyService,
    @Inject(DeliveryConfigurationServiceInterface)
    private readonly deliveryConfigurationService: DeliveryConfigurationServiceInterface,
  ) {}

  @Post('register')
  @SetMetadata('public', true)
  async register(@Body() userToRegister: UserToRegister, @Res() res: Response) {
    try {
      const createdUser = await this.authService.register(userToRegister);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_SIGNUP,
        'Success to Singup',
        {},
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('login')
  @SetMetadata('public', true)
  async login(@Body() userToLogin: UserToLogin, @Res() res: Response) {
    try {
      const token = await this.authService.login(userToLogin);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_LOGIN,
        'Success to Login',
        { token },
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get('me')
  @SetMetadata('public', true)
  @ApiHeader({
    name: 'x-access-token',
  })
  async getMe(
    @Headers('x-access-token') barrerToken: string,
    @Res() res: Response,
  ) {
    try {
      const token = barrerToken.split(' ')[1];
      const selectedUSer = await this.authService.get_me(token);
      const userAsJson = selectedUSer.toJSON();
      let deliveryConfiguration;
      if (selectedUSer.company) {
        deliveryConfiguration =
          await this.deliveryConfigurationService.findByCompanyId(
            new Types.ObjectId(selectedUSer.company._id),
          );
        userAsJson['company']['deliveryConfiguration'] = deliveryConfiguration;
      }
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'Success For getting user data',
        userAsJson,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('logout')
  @SetMetadata('public', true)
  @ApiHeader({ name: 'x-access-token' })
  async logout(
    @Headers('x-access-token') barrerToken: string,
    @Res() res: Response,
  ) {
    try {
      const token = barrerToken.split(' ')[1];
      const expiredToken = await this.authService.logout(token);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_LOGOUT,
        'Success to logout',
        expiredToken,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
  @Post('password/compare')
  @SetMetadata('public', true)
  async compare_password(
    @Body() passwordToCompare: PasswordToCompare,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      passwordToCompare.user_id =
        passwordToCompare.user_id ?? req['current']['id'];
      const password_check =
        await this.authService.compare_password(passwordToCompare);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'Success to compare password',
        { match: password_check },
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
}
