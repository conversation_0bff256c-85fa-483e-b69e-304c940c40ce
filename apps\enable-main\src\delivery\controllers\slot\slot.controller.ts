import { Request, Response } from 'express';
import { SlotToCreate, SlotToIndex } from './../../dto/slot.dto';
import { SlotService } from '../../services/slot/slot.service';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  SetMetadata,
} from '@nestjs/common';
import { HelperService } from '../../../shared/services/helper/helper.service';
import { responseCode } from '@app/shared-stuff';

@Controller('slot')
@ApiTags('Slot')
@SetMetadata('module', 'slot')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class SlotController {
  constructor(
    private slotService: SlotService,
    private helperService: HelperService,
  ) {}

  @Get()
  @SetMetadata('action', 'get_all')
  async index(
    @Query() slotToIndex: SlotToIndex,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      slotToIndex.company = req['company_id']
        ? req['company_id']
        : slotToIndex.company;
      const slots = await this.slotService.index(slotToIndex);
      const counts = [];
      const totalSlots = 0;
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to get all slots',
        { slots: slots, counts, totalSlots },
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get('public')
  @SetMetadata('public', 'true')
  async public_index(
    @Query() slotToIndex: SlotToIndex,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      slotToIndex.company = req['company_id']
        ? req['company_id']
        : slotToIndex.company;
      const slots = await this.slotService.index(slotToIndex);
      const counts = [];
      const totalSlots = 0;
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to get all slots',
        { slots: slots, counts, totalSlots },
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() slotCreate: SlotToCreate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      slotCreate.current_user = req['current'];
      slotCreate.company = req['company_id']
        ? req['company_id']
        : slotCreate.company;
      const createdSlot = await this.slotService.create(slotCreate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success to create slot',
        createdSlot,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
  @Put(':id')
  @SetMetadata('action', 'update')
  async update(
    @Body() slotToUpdate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      slotToUpdate.current_user = req['current'];
      const updatedSlot = await this.slotService.update(slotToUpdate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'success to update slot',
        updatedSlot,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  async get_details(@Param('id') id: string, @Res() res: Response) {
    try {
      const slot = await this.slotService.get_details(id);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'success to get slot details',
        slot,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Delete(':id')
  @SetMetadata('action', 'remove')
  async remove(
    @Param('id') id: string,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const removedSlot = await this.slotService.remove(id, req['current']);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_REMOVE,
        'success to remove slot',
        removedSlot,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
}
