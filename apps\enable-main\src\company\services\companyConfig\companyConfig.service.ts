import {
  PaymentCreditCompanies,
  PaymentDebitCompanies,
  PaymentMobileBankingCompanies,
  DeliveryThirdPartyName,
  responseCode,
  CompanyConfigBillingToUpdate,
  CompanyConfigIntegrationToUpdate,
  CompanyConfigPaymentToUpdate,
  CompanyConfigSetupToUpdate,
  CompanyConfigToCreate,
  CompanyConfigToIndex,
  CompanyDeliveryConfigToUpdate,
  AcknowledgementType,
  CompanyConfigDocument,
  CurrentUser,
  CollectionName,
} from '@app/shared-stuff';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';

@Injectable()
export class CompanyConfigService {
  constructor(
    @InjectModel(CollectionName.COMPANY_CONFIG)
    private companyConfigModel: Model<CompanyConfigDocument>,
  ) {}

  async index(companyConfigToIndex: CompanyConfigToIndex) {
    const configs = await this.companyConfigModel.find({
      ...companyConfigToIndex,
      deletedAt: { $exists: false },
    });
    return configs;
  }

  async getDetails(configID: Types.ObjectId, companyID?: Types.ObjectId) {
    const match = {
      _id: configID,
      deletedAt: { $exists: false },
    };
    if (companyID) {
      match['company'] = companyID;
    }
    const config = await this.companyConfigModel.findOne({ ...match });

    if (!config) {
      throw {
        code: responseCode.BAD_REQUEST,
        statusCode: 400,
        message: 'You need to provide a valid and existing config id',
      };
    }

    return config;
  }

  async getPathDetails(
    configID: Types.ObjectId,
    pathToFetch: string,
    companyID?: Types.ObjectId,
  ) {
    const match = {
      _id: configID,
      deletedAt: { $exists: false },
    };
    if (companyID) {
      match['company'] = companyID;
    }

    const config = await this.companyConfigModel.findOne({ ...match });

    if (!config) {
      throw {
        code: responseCode.BAD_REQUEST,
        statusCode: 400,
        message: 'You need to provide a valid and existing config id',
      };
    }
    const pathArray = pathToFetch.split('-');
    return {
      [pathArray[0]]: config[pathArray[0]][pathArray[1]]
        ? {
            [pathArray[1]]: config[pathArray[0]][pathArray[1]] ?? null,
          }
        : (config[pathArray[0]] ?? null),
    };
  }

  async create(
    companyConfigToCreate: CompanyConfigToCreate,
    currentUser: CurrentUser,
  ) {
    if (!companyConfigToCreate.company) {
      throw new HttpException(
        'You need to provide a company ID',
        HttpStatus.BAD_REQUEST,
      );
    }
    const createdConfig = new this.companyConfigModel(companyConfigToCreate);
    createdConfig.createdBy = currentUser;
    await createdConfig.save();
    return createdConfig;
  }

  async updateSetup(
    companyConfigSetupToUpdate: CompanyConfigSetupToUpdate,
    currentUser: CurrentUser,
  ) {
    const { _id, ...updates } = companyConfigSetupToUpdate;

    const config = await this.companyConfigModel.findOne({
      _id,
      deletedAt: { $exists: false },
    });

    if (!config) {
      throw {
        code: responseCode.BAD_REQUEST,
        statusCode: 400,
        message: 'You need to provide a valid and existing config id',
      };
    }

    for (const key in updates) {
      config.setup[key] = updates[key];
    }

    config.updatedBy = currentUser;
    await config.save();
    return config;
  }

  async updateIntegration(
    companyConfigIntegrationToUpdate: CompanyConfigIntegrationToUpdate,
    currentUser: CurrentUser,
  ) {
    const { _id, ...updates } = companyConfigIntegrationToUpdate;

    const config = await this.companyConfigModel.findOne({
      _id,
      deletedAt: { $exists: false },
    });

    if (!config) {
      throw {
        code: responseCode.BAD_REQUEST,
        statusCode: 400,
        message: 'You need to provide a valid and existing config id',
      };
    }

    for (const key in updates) {
      config.integration[key] = updates[key];
    }

    config.updatedBy = currentUser;
    await config.save();
    return config;
  }

  async updateDeliveryConfig(
    companyDeliveryConfigToUpdate: CompanyDeliveryConfigToUpdate,
    currentUser: CurrentUser,
  ) {
    const { _id, ...updates } = companyDeliveryConfigToUpdate;

    const config = await this.companyConfigModel.findOne({
      _id,
      deletedAt: { $exists: false },
    });

    if (!config) {
      throw {
        code: responseCode.BAD_REQUEST,
        statusCode: 400,
        message: 'You need to provide a valid and existing config id',
      };
    }

    for (const key in updates) {
      config.delivery[key] = updates[key];
    }

    config.updatedBy = currentUser;
    await config.save();
    return config;
  }

  async updateBilling(
    companyConfigBillingToUpdate: CompanyConfigBillingToUpdate,
    currentUser: CurrentUser,
  ) {
    const { _id, ...updates } = companyConfigBillingToUpdate;

    const config = await this.companyConfigModel.findOne({
      _id,
      deletedAt: { $exists: false },
    });

    if (!config) {
      throw {
        code: responseCode.BAD_REQUEST,
        statusCode: 400,
        message: 'You need to provide a valid and existing config id',
      };
    }

    for (const key in updates) {
      config.billing[key] = updates[key];
    }

    config.updatedBy = currentUser;
    await config.save();
    return config;
  }

  async updatePayment(
    companyConfigPaymentToUpdate: CompanyConfigPaymentToUpdate,
    currentUser: CurrentUser,
  ) {
    const { _id, ...updates } = companyConfigPaymentToUpdate;

    const config = await this.companyConfigModel.findOne({
      _id,
      deletedAt: { $exists: false },
    });

    if (!config) {
      throw {
        code: responseCode.BAD_REQUEST,
        statusCode: 400,
        message: 'You need to provide a valid and existing config id',
      };
    }

    for (const key in updates) {
      config.payment[key] = updates[key];
    }

    config.updatedBy = currentUser;
    await config.save();
    return config;
  }

  async delete(
    configID: Types.ObjectId,
    companyID: Types.ObjectId,
    currentUser: CurrentUser,
  ) {
    const match = {
      _id: configID,
      deletedAt: { $exists: false },
    };
    if (companyID) {
      match['company'] = companyID;
    }

    const deletedConfig = await this.companyConfigModel.findOne({ ...match });
    if (!deletedConfig) {
      throw {
        code: responseCode.BAD_REQUEST,
        statusCode: 400,
        message: 'You need to provide a valid and existing config id',
      };
    }

    deletedConfig.deletedAt = moment().utc().toDate();
    deletedConfig.deletedBy = currentUser;
    await deletedConfig.save();

    return deletedConfig;
  }

  async getPaymentMethods() {
    const paymentMethods = {
      credit: ['vistaMoney', 'cyberSource'],
      debit: ['vistaMoney', 'commercialBank'],
      mobileBanking: ['cbPay'],
    };

    return paymentMethods;
  }

  async createDefaultOne(companyId: Types.ObjectId) {
    this.create(
      {
        company: companyId,
        billing: {
          paymentsComission: {
            creditCard: 10,
            debitCard: 100,
            cbPay: 10,
          },
          webStore: {
            setup: 100,
            monthlyFees: 100,
            customization: 100,
          },
          delivery: {
            companyTasks: { feesPerTask: 500 },
            ebTasks: { feesPerTask: 500, taskType: 'perCity' },
          },
          enable: {
            setup: 100,
            monthlyFees: 100,
          },
          notifications: {
            customSenderID: {
              feesPerMonth: 100,
            },
            smsCost: {
              feesPerSms: 100,
            },
            bulkSmsCost: {
              feesPer1k: 1000,
            },
          },
        },
        payment: {
          credit: PaymentCreditCompanies.vistaMoney,
          debit: PaymentDebitCompanies.vistaMoney,
          mobileBanking: [PaymentMobileBankingCompanies.cbPay],
        },
        integration: {
          webhook: [],
        },
        delivery: {
          usingDeliveryModule: false,
          usingEbDelivery: true,
          usingThirdPartyDelivery: true,
          autoAssignOwnDerivers: true,
          allowedThirdPartyDeliveries: [DeliveryThirdPartyName.bee],
          autoAssingOrdersDelivery: true,
          hasOwnDrivers: false,
          defaultThirdPartyDelivery: DeliveryThirdPartyName.bee,
          acknowledgementType: AcknowledgementType.MANUAL,
        },
        setup: {
          notifications: {
            sms: {
              status: true,
              custom: true,
              senderName: 'ENABLE',
            },
            email: {
              status: true,
              custom: true,
              senderName: 'ENABLE',
            },
          },
        },
      },
      {
        name: '',
        id: companyId,
        phone: '',
        type: '',
        role: '',
      },
    );
  }
}
