import {
  CollectionName,
  CompanyDocument,
  LoggerService,
  OrderDocument,
  PaymentDocument,
  PaymentGatewayType,
  PaymentStatusEnum,
  TapCharge,
  TapChargeStatus,
  TapConfiguration,
  TapCreateChargeDto,
  TapCreateChargeResponseDto,
  TapGatewayConfiguration,
} from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import {
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { AxiosError } from 'axios';
import { createHmac } from 'crypto';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import { firstValueFrom } from 'rxjs';
import { CompanyService } from '../../../../company/services/company/company.service';
import { PaymentLogAction } from '../../../enums/payment-log-action.enum';
import { PaymentRepositoryInterface } from '../../../repositories/interfaces/payment.repository.interface';
import { PaymentLogRepository } from '../../../repositories/payment.log.repository';
import { PaymentConfigurationServiceInterface } from '../../payment-configuration/payment-configuration.service.interface';
import { PaymentHelpersService } from '../../payment-helpers/payment-helpers.service';

@Injectable()
export class PaymentTapService {
  private readonly BASE_URL = 'https://api.tap.company/v2/charges';
  private readonly logger = new LoggerService(PaymentTapService.name);
  constructor(
    private readonly http: HttpService,
    private readonly configService: ConfigService,
    private readonly companyService: CompanyService,
    @InjectModel(CollectionName.ORDER)
    private readonly paymentOrderModel: Model<OrderDocument>,
    private readonly paymentHelperService: PaymentHelpersService,
    @Inject(PaymentConfigurationServiceInterface)
    private readonly paymentConfigurationService: PaymentConfigurationServiceInterface,
    @Inject('PaymentRepositoryInterface')
    private readonly paymentRepository: PaymentRepositoryInterface,
    @Inject('PaymentLogRepositoryInterface')
    private readonly paymentLogRepository: PaymentLogRepository,
  ) {}

  public async updateStatus(
    paymentId: string,
    charge: TapCharge,
    hash: string,
  ) {
    const payment = await this.paymentRepository.findById(
      new Types.ObjectId(paymentId),
    );

    const company = await this.companyService.findById(
      new Types.ObjectId(payment.company),
    );

    const tapConfiguration = await this.getTapConfiguration(payment, company);

    this.validateWebhook(charge, tapConfiguration.tapSecretApiKey, hash);

    const order = payment.order
      ? await this.paymentOrderModel.findById(payment.order)
      : null;

    await this.paymentHelperService.saveTransaction(
      payment,
      charge.id,
      charge,
      PaymentGatewayType.TAP,
    );

    const previousPaymentStatus = payment.status;
    const newPaymentStatus = this.parsePaymentStatus(charge.response.code);

    await this.paymentHelperService.handlePaymentStatusLogic(
      payment,
      newPaymentStatus,
      order,
      company,
    );

    await this.paymentLogRepository.create({
      logAction: PaymentLogAction.TAP_UPDATE_STATE,
      paymentCode: payment.code,
      paymentId: payment._id,
      receivedObject: { paymentId, charge },
      sentObject: { payment, previousPaymentStatus, newPaymentStatus },
    });
  }

  private validateWebhook(
    charge: TapCharge,
    secretAPIKey: string,
    hash: string,
  ) {
    const toBeHashedString = `x_id${charge.id}x_amount${charge.amount}x_currency${charge.currency}x_gateway_reference${charge.reference.gateway}x_payment_reference${charge.reference.payment}x_status${charge.status}x_created${charge.transaction.created}`;

    const computedHash = createHmac('sha256', secretAPIKey)
      .update(toBeHashedString)
      .digest('hex');

    if (computedHash !== hash) {
      this.logger.error('[Tap Webhook] Hash strings did not match');
      // When using the global api key in testing, the hash strings never matched
      // I'm guessing the hash strings will match once we switch to our api key
      // TODO: retest with our API key
      // throw new UnauthorizedException();
    }
  }

  public async afterPaymentDoneRedirection(
    paymentId: string,
    tapId: string,
  ): Promise<string> {
    const payment = await this.paymentRepository.findById(
      new Types.ObjectId(paymentId),
    );

    const company = await this.companyService.findById(
      new Types.ObjectId(payment.company),
    );

    const order = payment.order_code
      ? await this.paymentOrderModel.findOne({ code: payment.order_code })
      : null;

    if (payment.status === PaymentStatusEnum.TRANSACTION_COMPLETED) {
      await this.paymentRepository.findOneAndUpdate(
        { _id: payment._id },
        {
          $push: {
            paymentTries: {
              message: 'After Done action triggered for complete payment.',
              timestamp: moment.utc(),
            },
          },
        },
      );

      return await this.paymentHelperService.generateCallBackUrl(
        payment,
        company,
        '000',
        order,
      );
    }

    const tapConfiguration = await this.getTapConfiguration(payment, company);
    const charge = await this.getCharge(
      tapId,
      tapConfiguration?.tapSecretApiKey,
    );

    payment.transaction_date = moment().toDate();
    payment.transaction_id = tapId;
    const callback = await this.paymentHelperService.generateCallBackUrl(
      payment,
      company,
      charge.response.code,
      order,
    );

    await this.paymentLogRepository.create({
      logAction: PaymentLogAction.TAP_AFTER_DONE,
      paymentCode: payment.code,
      paymentId: payment._id,
      receivedObject: { paymentId, tapId, charge },
      sentObject: { payment, callback },
    });

    return callback;
  }

  private async getTapConfiguration(
    payment: PaymentDocument,
    company: CompanyDocument,
  ): Promise<TapConfiguration> {
    const paymentConfiguration =
      await this.paymentConfigurationService.findPaymentConfig(
        payment.branch,
        payment.branch,
        company._id,
      );

    const gatewayConfiguration: TapGatewayConfiguration =
      paymentConfiguration.configuration[payment.payment_method];

    return gatewayConfiguration.configuration;
  }

  private parsePaymentStatus(code: TapChargeStatus): PaymentStatusEnum {
    if (code === TapChargeStatus.CAPTURED)
      return PaymentStatusEnum.TRANSACTION_COMPLETED;

    if (
      [
        TapChargeStatus.AUTHORIZED,
        TapChargeStatus.INITIATED,
        TapChargeStatus.IN_PROGRESS,
      ].includes(code)
    )
      return PaymentStatusEnum.PROCESSING;

    return PaymentStatusEnum.UNSUCCESSFUL;
  }

  public async createPaymentLink(
    payment: PaymentDocument,
    { tapSecretApiKey }: TapConfiguration,
  ): Promise<string> {
    if (payment.gateway_link) return payment.gateway_link;

    const createChargeDto = this.buildCreateChargeDto(payment);

    try {
      const createChargeResponse = await this.createCharge(
        createChargeDto,
        tapSecretApiKey,
      );

      await this.paymentLogRepository.create({
        logAction: PaymentLogAction.TAP_CREATE_PAYMENT_LINK,
        paymentCode: payment.code,
        paymentId: payment._id,
        sentObject: createChargeDto,
        receivedObject: createChargeResponse,
      });

      if (!createChargeResponse?.transaction?.url)
        throw new InternalServerErrorException(`Failed to create charge`);

      await this.paymentRepository.findOneAndUpdate(
        { _id: payment._id },
        { gateway_link: createChargeResponse.transaction.url },
      );

      return createChargeResponse.transaction.url;
    } catch (error) {
      const errorBody =
        error instanceof AxiosError ? error.response.data : error;

      this.paymentLogRepository.create({
        logAction: PaymentLogAction.TAP_CREATE_PAYMENT_LINK_ERROR,
        paymentCode: payment.code,
        paymentId: payment._id,
        sentObject: createChargeDto,
        receivedObject: errorBody,
      });

      throw errorBody;
    }
  }

  private async createCharge(
    createChargeDto: TapCreateChargeDto,
    tapSecretApiKey: string,
  ): Promise<TapCreateChargeResponseDto> {
    const response = await firstValueFrom(
      this.http.post(this.BASE_URL, createChargeDto, {
        headers: this.getHeaders(tapSecretApiKey),
      }),
    );

    return response.data as TapCreateChargeResponseDto;
  }

  private buildCreateChargeDto(payment: PaymentDocument): TapCreateChargeDto {
    return {
      amount: payment.amount,
      currency: payment.localization.currency,
      customer: {
        first_name: payment.customer_name,
        phone: {
          country_code: parseInt(
            payment.localization.countryCode.replace('+', '').replace('-', ''),
          ),
          number: parseInt(payment.customer_phone),
        },
      },
      source: { id: 'src_card' },
      redirect: {
        url: `${this.configService.get('HOST_URL')}/payment/action/tap/after_done/${payment._id}`,
      },
      post: {
        url: `${this.configService.get('HOST_URL')}/payment/action/tap/update_status/${payment._id}`,
      },
    };
  }

  private async getCharge(
    chargeId: string,
    tapSecretApiKey: string,
  ): Promise<TapCharge> {
    const response = await firstValueFrom(
      this.http.get(`${this.BASE_URL}/${chargeId}`, {
        headers: this.getHeaders(tapSecretApiKey),
      }),
    );

    return response.data;
  }

  private getHeaders(tapSecretApiKey: string) {
    return {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${tapSecretApiKey}`,
    };
  }
}
