import { Module } from '@nestjs/common';
import { CompanyModule } from '../../../company/company.module';
import { LoyaltyTierReadModule } from '../../../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.module';
import { CustomerPointsModule } from '../customer-points/customer.points.module';
import { CustomerTierInfoService } from './customer-tier-info.service';
import { CustomerTierInfoServiceInterface } from './customer-tier-info.service.interface';

@Module({
  providers: [
    {
      provide: CustomerTierInfoServiceInterface,
      useClass: CustomerTierInfoService,
    },
  ],
  imports: [CompanyModule, LoyaltyTierReadModule, CustomerPointsModule],
  exports: [CustomerTierInfoServiceInterface],
})
export class CustomerTierInfoModule {}
