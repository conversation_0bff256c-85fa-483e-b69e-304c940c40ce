import { ApiProperty } from '@nestjs/swagger';
import {
  IsAlpha,
  IsAlphanumeric,
  IsOptional,
  IsPhoneNumber,
  Matches,
  MaxLength,
  MinLength,
} from 'class-validator';

export class BillingAddressTESSPaymentIntegration {
  @ApiProperty({
    type: String,
    required: false,
    maxLength: 2,
    minLength: 2,
  })
  @IsOptional()
  @Matches('A-Z')
  @MaxLength(2)
  @MinLength(2)
  country?: string;

  @ApiProperty({
    type: String,
    required: false,
    maxLength: 32,
    minLength: 2,
  })
  @IsOptional()
  @IsAlpha()
  @MaxLength(32)
  @MinLength(2)
  state?: string;

  @ApiProperty({
    type: String,
    required: false,
    maxLength: 32,
    minLength: 2,
  })
  @IsOptional()
  @IsAlpha()
  @MaxLength(32)
  @MinLength(2)
  city?: string;

  @ApiProperty({
    type: String,
    required: false,
    maxLength: 32,
    minLength: 2,
  })
  @IsOptional()
  @IsAlphanumeric()
  @MaxLength(32)
  @MinLength(2)
  address?: string;

  @ApiProperty({
    type: String,
    required: false,
    maxLength: 10,
    minLength: 2,
  })
  @IsOptional()
  @IsAlphanumeric()
  @MaxLength(10)
  @MinLength(2)
  zip?: string;

  @ApiProperty({
    type: String,
    required: false,
    maxLength: 32,
    minLength: 0,
  })
  @IsOptional()
  @Matches('0-9+()-')
  @IsPhoneNumber()
  @MaxLength(32)
  @MinLength(0)
  phone?: string;
}
