import { GenericRepository } from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model } from 'mongoose';
import { Job, JobDocument } from '../../models/job.model';
import { JobRepositoryInterface } from './job.repository.interface';

@Injectable()
export class JobRepository
  extends GenericRepository<JobDocument, Job>
  implements JobRepositoryInterface
{
  constructor(
    @InjectModel(Job.name)
    private jobModel: Model<JobDocument, Job>,
  ) {
    super(jobModel);
  }

  async getReadyJobs(): Promise<JobDocument[]> {
    return await this.jobModel.find({
      scheduledAt: { $lte: moment.utc().toDate() },
      deletedAt: null,
    });
  }
}
