import {
  CountryDialCode,
  CouponDocument,
  Customer,
  CustomerDocument,
  CustomerProjection,
  LoyaltyTierDocument,
  RegisteredPass,
  responseCode,
} from '@app/shared-stuff';
import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { isValidPhoneNumber } from 'libphonenumber-js';
import { FilterQuery, isObjectIdOrHexString, Types } from 'mongoose';
import { StoreDocument } from '../../../store/models/store.model';
import { CustomerRepositoryInterface } from '../customer-repository/customer.repository.interface';
import { CustomerReadServiceInterface } from './customer-read.service.interface';

@Injectable()
export class CustomerReadService implements CustomerReadServiceInterface {
  constructor(
    @Inject(CustomerRepositoryInterface)
    private readonly customerRepository: CustomerRepositoryInterface,
  ) {}

  async findById(customerId: Types.ObjectId): Promise<CustomerDocument> {
    const customer = await this.customerRepository.findById(customerId);
    if (!customer)
      throw new NotFoundException(`Customer with ID ${customerId} not found.`);
    return customer;
  }

  async findOne(
    uniqueIdentifier: string,
    companyId?: Types.ObjectId,
    countryCode?: CountryDialCode,
  ): Promise<CustomerDocument> {
    const filterObj = this.constructFilterObject(
      uniqueIdentifier,
      companyId,
      countryCode,
    );
    const customer = await this.customerRepository.findOne(filterObj);
    if (!customer)
      throw new BadRequestException(
        'Customer Not Found',
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    return customer;
  }

  async getCustomer(
    uniqueIdentifier: string,
    companyId: Types.ObjectId,
  ): Promise<CustomerDocument | undefined> {
    const filterObj = this.constructFilterObject(uniqueIdentifier, companyId);
    return await this.customerRepository.findOne(filterObj);
  }

  getCompanyIdForCustomerId(
    customerId: Types.ObjectId,
  ): Promise<Types.ObjectId> {
    return this.customerRepository.getCompanyIdForCustomerId(customerId);
  }

  async findByRegisteredPasses(
    registeredPass: Partial<RegisteredPass>,
    updatedSince?: Date,
  ): Promise<CustomerDocument[]> {
    return await this.customerRepository.findByRegisteredPasses(
      registeredPass,
      updatedSince,
    );
  }

  async findEligibleForTierDiscount(
    loyaltyTier: LoyaltyTierDocument,
  ): Promise<CustomerDocument[]> {
    return await this.customerRepository.findEligibleForTierDiscount(
      loyaltyTier,
    );
  }

  async findEligibleForCoupon(
    coupon: CouponDocument,
  ): Promise<CustomerDocument[]> {
    return await this.customerRepository.findEligibleForCoupon(coupon);
  }

  async findByPhoneAndCompanyId(
    phone: string,
    companyId: Types.ObjectId,
    countryCode?: string,
    projection?: CustomerProjection,
  ): Promise<CustomerDocument> {
    const customer = await this.customerRepository.findByPhoneAndCompanyId(
      phone,
      companyId,
      { countryCode, projection },
    );

    if (!customer) throw new NotFoundException('Customer Not Found');
    return customer;
  }

  findByLoyaltyTier(
    loyaltyTierIds: Types.ObjectId[],
    companyId?: Types.ObjectId,
    hasPasses?: boolean,
  ): Promise<CustomerDocument[]> {
    return this.customerRepository.findByLoyaltyTier(
      loyaltyTierIds,
      companyId,
      hasPasses,
    );
  }

  findByBrandId(
    brandId: Types.ObjectId,
    hasPasses?: boolean,
  ): Promise<CustomerDocument[]> {
    return this.customerRepository.findByBrandId(brandId, hasPasses);
  }

  async findSyncableForOrdableStore(
    store: StoreDocument,
  ): Promise<CustomerDocument[]> {
    return await this.customerRepository.findSyncableForOrdableStore(store);
  }

  async findByPunchCard(
    punchCardId: Types.ObjectId,
  ): Promise<CustomerDocument[]> {
    return await this.customerRepository.findByPunchCardId(punchCardId);
  }

  async findByCompanyId(
    companyId: Types.ObjectId | Types.ObjectId[],
    hasPasses?: boolean,
  ): Promise<CustomerDocument[]> {
    return await this.customerRepository.findByCompanyId(companyId, hasPasses);
  }

  async findByEmail(
    companyId: Types.ObjectId,
    email: string,
  ): Promise<CustomerDocument> {
    return this.customerRepository.findByEmail(companyId, email);
  }

  private constructFilterObject(
    uniqueIdentifier: string,
    companyId?: Types.ObjectId,
    countryCode?: CountryDialCode,
  ): any {
    const filter: FilterQuery<Customer> = {};

    if (isObjectIdOrHexString(companyId))
      filter.company = new Types.ObjectId(companyId);

    const isJSON =
      typeof uniqueIdentifier === 'string' &&
      uniqueIdentifier.startsWith('{') &&
      uniqueIdentifier.endsWith('}');
    const isPhoneNumber = isValidPhoneNumber(uniqueIdentifier, {
      defaultCallingCode: countryCode?.replace('+', ''),
    });
    if (isObjectIdOrHexString(uniqueIdentifier))
      filter._id = new Types.ObjectId(uniqueIdentifier);
    else if (isJSON) {
      const payload = JSON.parse(uniqueIdentifier);
      filter._id = new Types.ObjectId(payload.id);
    } else if (countryCode && isPhoneNumber) {
      filter.country_code = countryCode;
      filter.phone = uniqueIdentifier;
    } else {
      filter.shortCode = uniqueIdentifier;
    }

    return filter;
  }
}
