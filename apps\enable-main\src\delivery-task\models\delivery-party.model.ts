import { CollectionName } from '@app/shared-stuff';
import { Prop, Schema, SchemaFactory, raw } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { HydratedDocument, Types } from 'mongoose';
import { DeliveryPartyThirdParty } from '../dto/delivery-party.dto';
import {
  DeliveryPartyDriversFilter,
  DeliveryPartyThirdPartyConfig,
} from './../dto/delivery-party.dto';

export type DeliveryPartyDocument = HydratedDocument<DeliveryParty>;
@Schema({ timestamps: true })
export class DeliveryParty {
  @Prop({
    type: String,
    required: true,
  })
  name: string;

  @Prop({
    type: Boolean,
    required: true,
  })
  isOwnDrivers: boolean;

  @Prop({
    type: String,
    enum: [...Object.keys(DeliveryPartyThirdParty)],
    required: true,
  })
  thirdParty: DeliveryPartyThirdParty;

  @Prop({
    type: Boolean,
    required: false,
    default: false,
  })
  showDriverList: boolean;

  @Prop(
    raw({
      company: {
        type: Types.ObjectId,
        required: false,
        ref: CollectionName.COMPANY,
      },
    }),
  )
  driversFilter: DeliveryPartyDriversFilter;

  @Prop(
    raw({
      API_KEY: {
        type: String,
        required: true,
      },
      TEAM_ID: {
        type: String,
        required: false,
      },
    }),
  )
  thirdPartyConfig: DeliveryPartyThirdPartyConfig;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: CollectionName.COMPANY,
  })
  company: Types.ObjectId;

  @Prop({
    type: {},
    required: false,
  })
  updatedBy: {};

  @Prop({
    type: {},
    required: false,
  })
  createdBy: {};

  @Prop({
    type: {},
    required: false,
  })
  deletedBy: {};

  @Prop({
    type: Date,
    required: false,
  })
  deletedAt: Date;

  @Prop({
    type: String,
    default: moment().startOf('month').format('MMMM'),
  })
  month: string;

  @Prop({
    type: Number,
    default: moment().week(),
  })
  week: number;

  @Prop({
    type: Number,
    default: moment().date(),
  })
  day: number;

  @Prop({
    type: Number,
    default: moment().year(),
  })
  year: number;
}

const DeliveryPartySchema = SchemaFactory.createForClass(DeliveryParty);

export { DeliveryPartySchema };
