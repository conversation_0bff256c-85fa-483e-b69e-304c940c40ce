import {
  BulkActionResponseDto,
  BulkLoyaltyRegistrationDto,
  BulkTierAssignmentDto,
  CollectionName,
  CreateCustomerDto,
  FindCustomerDto,
  GenericExceptionFilter,
  ImportLoyaltyCustomersDto,
  IndexCustomerDto,
  LoggerService,
  LookupCallCenterCustomerDto,
  MessageCustomerDto,
  PromoteForBrandDto,
  ********************,
  RegisterLoyaltyCustomerDto,
  responseCode,
  SendOrdableLinkDto,
  SetDeviceDataDto,
  TransformInterceptor,
  UpdateCustomerDto,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Get,
  Headers,
  HttpCode,
  HttpStatus,
  Inject,
  Param,
  Post,
  Put,
  Query,
  Req,
  SetMetadata,
  UploadedFile,
  UseFilters,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  <PERSON>pi<PERSON>asic<PERSON><PERSON>,
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Request } from 'express';
import { Types } from 'mongoose';
import { CustomerIOServiceInterface } from '../modules/customer-io/customer-io.service.interface';
import { CustomerLoyaltyMemberServiceInterface } from '../modules/customer-loyalty-member/customer-loyalty-member.service.interface';
import { CustomerLoyaltyServiceInterface } from '../modules/customer-loyalty/customer-loyalty.service.interface';
import { CustomerPromoteServiceInterface } from '../modules/customer-promote/customer-promote.service.interface';
import { CustomerPunchCardServiceInterface } from '../modules/customer-punch-card/customer-punch-card.service.interface';
import { CustomerTierServiceInterface } from '../modules/customer-tier/customer-tier.service.interface';
import { CustomerWriteServiceInterface } from '../modules/customer-write/customer-write.service.interface';
import { MessagePattern } from '@nestjs/microservices';
import { CustomerCronJobService } from '../cronjobs/customer-cron-job.service';

@Controller('customer')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags(CollectionName.CUSTOMER)
@SetMetadata('module', 'customer')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class CustomerController {
  private readonly loggerService = new LoggerService(CustomerController.name);

  constructor(
    @Inject(CustomerWriteServiceInterface)
    private readonly customerWriteService: CustomerWriteServiceInterface,
    @Inject(CustomerIOServiceInterface)
    private readonly customerIOService: CustomerIOServiceInterface,
    @Inject(CustomerLoyaltyServiceInterface)
    private readonly customerLoyaltyService: CustomerLoyaltyServiceInterface,
    @Inject(CustomerTierServiceInterface)
    private readonly customerTierService: CustomerTierServiceInterface,
    @Inject(CustomerLoyaltyMemberServiceInterface)
    private readonly customerLoyaltyMemberService: CustomerLoyaltyMemberServiceInterface,
    @Inject(CustomerPromoteServiceInterface)
    private readonly customerPromoteService: CustomerPromoteServiceInterface,
    @Inject(CustomerPunchCardServiceInterface)
    private readonly customerPunchCardService: CustomerPunchCardServiceInterface,
  ) {}

  //This is a dummy function
  @Get('cards')
  @SetMetadata('action', 'customer_cards')
  async indexCards() {
    return [];
  }

  @Get()
  @SetMetadata('action', 'get_all')
  async index(
    @Query() indexCustomerDto: IndexCustomerDto,
    @Req() req: Request,
  ) {
    indexCustomerDto.company = req['company_id']
      ? req['company_id']
      : new Types.ObjectId(indexCustomerDto.company);
    const selectedCustomers =
      await this.customerLoyaltyService.indexLoyaltyCustomers(indexCustomerDto);
    return {
      customers: selectedCustomers[0]['paginatedResult'],
      totalCustomers: selectedCustomers[0]['totalCount'][0]
        ? selectedCustomers[0]['totalCount'][0]['createdAt']
        : 0,
      counts: [
        {
          name: '# Total Customers',
          count: selectedCustomers[0]['totalCount'][0]
            ? selectedCustomers[0]['totalCount'][0]['createdAt']
            : 0,
          change: 75,
          change_direction: 'down',
          icon: '',
          from: '2022-03-19T21:00:00.000Z',
          to: '2022-04-02T20:59:59.999Z',
        },
      ],
    };
  }

  @Get('me')
  @SetMetadata('public', 'true')
  async getCustomerByAuthToken(@Headers('authorization') authHeader: string) {
    return await this.customerLoyaltyService.getCustomerDetailsForFrontendByAuthToken(
      authHeader,
    );
  }

  @Get('punchCardCounters')
  @SetMetadata('action', 'punchCardCounters')
  async getPunchCardCounters(
    @Query() { companyId, customerId }: ********************,
  ) {
    return await this.customerPunchCardService.getPunchCardCounters(
      companyId,
      customerId,
    );
  }

  @Get(':id')
  @SetMetadata('action', 'findOne')
  async findOne(
    @Param('id') id: string,
    @Query() { countryCode, companyId }: FindCustomerDto,
    @Req() req: Request,
  ) {
    return await this.customerLoyaltyService.getCustomerDetailsForFrontend(
      id,
      req['company_id'] ?? companyId ?? '',
      countryCode,
    );
  }

  @Get('public/:phoneNumber')
  @SetMetadata('public', 'true')
  async findOnePublic(
    @Param('phoneNumber') phoneNumber: string,
    @Query() { countryCode, companyId }: FindCustomerDto,
    @Req() req: Request,
  ) {
    return await this.customerLoyaltyService.getSimplifiedCustomerDetails(
      phoneNumber,
      req['company_id'] ?? companyId ?? '',
      countryCode,
    );
  }

  @Post()
  @SetMetadata('action', 'create')
  @HttpCode(responseCode.SUCCESS_TO_CREATE)
  async create(
    @Body() createCustomerDto: CreateCustomerDto,
    @Req() req: Request,
  ) {
    createCustomerDto.company = req['company_id']
      ? req['company_id']
      : new Types.ObjectId(createCustomerDto.company);
    return await this.customerWriteService.create(
      createCustomerDto,
      req['current'],
    );
  }

  @Post('create-update')
  @SetMetadata('action', 'create')
  @HttpCode(responseCode.SUCCESS_TO_CREATE)
  async updateOrCreate(
    @Body() createCustomerDto: CreateCustomerDto,
    @Req() req: Request,
  ) {
    createCustomerDto.company = req['company_id']
      ? req['company_id']
      : new Types.ObjectId(createCustomerDto.company);

    const customer = await this.customerWriteService.updateOrCreate(
      createCustomerDto,
      req['current'],
    );

    return customer._id;
  }

  @Put()
  @SetMetadata('action', 'update')
  @UsePipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidUnknownValues: true,
    }),
  )
  @HttpCode(responseCode.SUCCESS_TO_UPDATE)
  async update(
    @Body() updateCustomerDto: UpdateCustomerDto,
    @Req() req: Request,
  ) {
    const isCustomerRequest = Boolean(req['customer']);
    if (isCustomerRequest) updateCustomerDto._id = req['customer']['_id'];

    return await this.customerWriteService.update(
      updateCustomerDto,
      req['current'],
      req['company_id'],
    );
  }

  @Put(':id')
  @SetMetadata('action', 'update')
  @ApiOperation({
    deprecated: true,
    description:
      '**Deprecated**, use `PUT /customer` instead, and pass customerId as `_id` in request body.',
  })
  @UsePipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidUnknownValues: true,
    }),
  )
  @HttpCode(responseCode.SUCCESS_TO_UPDATE)
  async updateById(
    @Body() updateCustomerDto: UpdateCustomerDto,
    @Req() req: Request,
  ) {
    return await this.customerWriteService.update(
      updateCustomerDto,
      req['current'],
      req['company_id'],
    );
  }

  @Delete(':id')
  @SetMetadata('action', 'remove')
  @HttpCode(responseCode.SUCCESS_TO_REMOVE)
  async remove(@Param('id') id: string, @Req() req: Request) {
    return await this.customerWriteService.remove(
      id,
      req['current'],
      req['company_id'],
    );
  }

  @Get('download/excel')
  @SetMetadata('action', 'downloadFile')
  async downloadFile(
    @Query() indexCustomerDto: IndexCustomerDto,
    @Req() req: Request,
  ) {
    indexCustomerDto.company = req['company_id']
      ? req['company_id']
      : new Types.ObjectId(indexCustomerDto.company);
    return await this.customerIOService.downloadFile(indexCustomerDto);
  }

  @Get('export/excel')
  @SetMetadata('action', 'downloadFile')
  @ApiOperation({
    deprecated: true,
    description: '**Deprecated**, use `GET /customer/download/excel` instead.',
  })
  async exportToExcel(
    @Query() indexCustomerDto: IndexCustomerDto,
    @Req() req: Request,
  ) {
    indexCustomerDto.company = req['company_id']
      ? req['company_id']
      : new Types.ObjectId(indexCustomerDto.company);
    return await this.customerIOService.downloadFile(indexCustomerDto);
  }

  @Post('import/excel')
  @SetMetadata('action', 'import_excel')
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(@UploadedFile() file, @Req() req: Request) {
    return await this.customerIOService.uploadFile({
      filePath: file['path'],
      companyId: req['company_id'],
    });
  }

  @Post('import/loopy')
  @SetMetadata('action', 'import_excel')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      required: ['brandId', 'companyId', 'file'],
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        brandId: {
          type: 'string',
        },
        companyId: {
          type: 'string',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async importLoopyCustomers(
    @UploadedFile('file') file: any,
    @Body()
    importLoopyCustomersDto: Omit<ImportLoyaltyCustomersDto, 'filePath'>,
  ) {
    await this.customerIOService.importLoopyCustomers({
      file: file.buffer,
      brandId: new Types.ObjectId(importLoopyCustomersDto.brandId),
      companyId: new Types.ObjectId(importLoopyCustomersDto.companyId),
    });
    return 'DONE';
  }

  @Post('import/brandWallet')
  @SetMetadata('action', 'import_excel')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      required: ['brandId', 'companyId', 'file'],
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        brandId: {
          type: 'string',
        },
        companyId: {
          type: 'string',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async importBrandWalletCustomers(
    @UploadedFile('file') file: any,
    @Body()
    importBrandWalletCustomersDto: Omit<ImportLoyaltyCustomersDto, 'filePath'>,
  ) {
    await this.customerIOService.importBrandWalletCustomers({
      file: file.buffer,
      brandId: new Types.ObjectId(importBrandWalletCustomersDto.brandId),
      companyId: new Types.ObjectId(importBrandWalletCustomersDto.companyId),
    });
    return 'DONE';
  }

  @Post('sendOrdableLink')
  @SetMetadata('action', 'sendOrdableLink')
  async sendOrdableLink(@Body() sendOrdableLinkDto: SendOrdableLinkDto) {
    await this.customerPromoteService.sendOrdableLink(sendOrdableLinkDto);
    return 'Success';
  }

  @Get('populate/info')
  @SetMetadata('action', 'populate-infoF')
  async lookupCallCenterCustomer(
    @Query() lookupCallCenterCustomerDto: LookupCallCenterCustomerDto,
  ) {
    return await this.customerLoyaltyService.lookupCallCenterCustomer(
      lookupCallCenterCustomerDto,
    );
  }

  @Post('promoteLoyaltyRegistration')
  @SetMetadata('action', 'promoteLoyaltyRegistration')
  async promoteLoyaltyRegistration(
    @Body() promoteLoyaltyRegistrationDto: PromoteForBrandDto,
  ) {
    await this.customerPromoteService.promoteLoyaltyForBrand(
      promoteLoyaltyRegistrationDto,
      'Registration',
    );
    return 'Success';
  }

  @Post('promoteLoyaltyCard')
  @SetMetadata('action', 'promoteLoyaltyCard')
  async promoteLoyaltyCardS(
    @Body() promoteLoyaltyRegistrationDto: PromoteForBrandDto,
  ) {
    await this.customerPromoteService.promoteLoyaltyForBrand(
      promoteLoyaltyRegistrationDto,
      'LoyaltyCard',
    );
    return 'Success';
  }

  @Post('manualTierAssignment')
  @SetMetadata('action', 'manualTierAssignment')
  @ApiResponse({ type: BulkActionResponseDto })
  async manualTierAssignment(
    @Body() bulkTierAssignmentDto: BulkTierAssignmentDto,
    @Req() req: Request,
  ) {
    if (bulkTierAssignmentDto.customerSegment && req['company_id'])
      bulkTierAssignmentDto.customerSegment.company = req['company_id'];
    bulkTierAssignmentDto.currentUser = req['current'];
    return await this.customerTierService.bulkTierAssignment(
      bulkTierAssignmentDto,
    );
  }

  @Post('manualLoyaltyRegistration')
  @SetMetadata('action', 'manualLoyaltyRegistration')
  @ApiResponse({ type: BulkActionResponseDto })
  async manualLoyaltyRegistration(
    @Body()
    bulkLoyaltyRegistrationDto: BulkLoyaltyRegistrationDto,
    @Req() req: Request,
  ) {
    if (bulkLoyaltyRegistrationDto.customerSegment && req['company_id'])
      bulkLoyaltyRegistrationDto.customerSegment.company = req['company_id'];
    return await this.customerLoyaltyMemberService.bulkLoyaltyRegistration(
      bulkLoyaltyRegistrationDto,
    );
  }

  @Post('deviceData')
  @SetMetadata('public', 'true')
  async setDeviceData(@Body() { customerId, deviceData }: SetDeviceDataDto) {
    await this.customerWriteService.setDeviceData(customerId, deviceData);
    return 'Success';
  }

  @Post('loyaltyRegistration')
  @SetMetadata('public', 'true')
  async customerLoyaltyRegistration(
    @Body() registerLoyaltyCustomerDto: RegisterLoyaltyCustomerDto,
  ) {
    return await this.customerLoyaltyMemberService.registerCustomerInLoyaltyProgram(
      registerLoyaltyCustomerDto,
    );
  }

  @MessagePattern('customer.message.request')
  @SetMetadata('public', 'true')
  public messageCustomer(@Body() messageCustomerDto: MessageCustomerDto) {
    return this.customerWriteService.messageCustomer(messageCustomerDto);
  }
}
