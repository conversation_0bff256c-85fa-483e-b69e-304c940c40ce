import {
  <PERSON>ache<PERSON><PERSON>s,
  CacheServiceInterface,
  fullCacheKeys,
  Menu,
  MenuDocument,
  MenuEvents,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';

@Injectable()
export class MenuListener {
  constructor(
    @Inject(CacheServiceInterface)
    private readonly cacheService: CacheServiceInterface,
  ) {}

  @OnEvent(MenuEvents.MENU_UPDATED)
  async handleMenuUpdated(menu: MenuDocument) {
    const key = fullCacheKeys[CacheKeys.MENU](menu._id);
    const keyExists = await this.cacheService.getCache<Menu>(key, Menu);

    if (keyExists) {
      await this.cacheService.setCache(key, menu);
    }
  }
}
