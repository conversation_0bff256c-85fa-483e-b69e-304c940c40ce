import {
  GenericExceptionFilter,
  Payment,
  SkipCashWebhookDto,
  TapCharge,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Get,
  Headers,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Query,
  Req,
  Res,
  SetMetadata,
  UseFilters,
  UseInterceptors,
  Version,
} from '@nestjs/common';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiExcludeEndpoint,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Request, Response } from 'express';
import { PaymentIndex } from '../../../payment/dto/payment.dto';
import { PaymentStripeService } from '../../../payment/services/payment-integrations/payment-stripe/payment-stripe.service';
import { PaymentService } from '../../../payment/services/payment/payment.service';
import { HelperService } from '../../../shared/services/helper/helper.service';
import { CbPayTokenToRequest } from '../../dto/cbpay/cbpay-token-to-request.dto';
import { ClonePaymentDto } from '../../dto/clone-payment.dto';
import { CreateTessSessionDto } from '../../dto/create-tss-session.dto';
import { DibsyMerchantValidatedToRequest } from '../../dto/dibsy/dibsy-merchant-validation-request.dto';
import { PaymentToCreate } from '../../dto/payment-to-create.dto';
import { PaymentToProcess } from '../../dto/payment-to-process.dto';
import { PaymentToBeRefunded } from '../../dto/payment-to-refund.dto';
import { PaymentToResend } from '../../dto/payment-to-resend.dto';
import { PaymentMyFatoorahService } from '../../services/payment-integrations/payment-myfatoorah/payment-myfatoorah.service';
import { PaymentSkipCashService } from '../../services/payment-integrations/payment-skip-cash/payment-skip-cash.service';
import { PaymentTapService } from '../../services/payment-integrations/payment-tap/payment-tap.service';
import { PaymentVistamoneyService } from '../../services/payment-integrations/payment-vistamoney/payment-vistamoney.service';
import { PaymentCbpayService } from './../../services/payment-integrations/payment-cbpay/payment-cbpay.service';
import { PaymentCyberSourceService } from './../../services/payment-integrations/payment-cyber-source/payment-cyber-source.service';
import { PaymentDibsyService } from './../../services/payment-integrations/payment-dibsy/payment-dibsy.service';

@Controller('payment')
@ApiTags('Payment Link')
@SetMetadata('module', 'payment')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
export class PaymentController {
  constructor(
    private paymentService: PaymentService,
    private helperService: HelperService,
    private cbPayService: PaymentCbpayService,
    private vistaMoneyService: PaymentVistamoneyService,
    private cyberSourceService: PaymentCyberSourceService,
    private paymentStripeService: PaymentStripeService,
    private skipCashService: PaymentSkipCashService,
    private dibsyPaymentService: PaymentDibsyService,
    private myFatoorahPaymentService: PaymentMyFatoorahService,
    private tapService: PaymentTapService,
  ) {}

  @Get()
  @SetMetadata('action', 'get_all')
  async index(@Query() paymentIndex: PaymentIndex, @Req() req: Request) {
    paymentIndex.company = req['company_id']
      ? req['company_id']
      : paymentIndex.company;
    paymentIndex.branches = req['branches'] ? req['branches'] : '';
    paymentIndex.companyIds = req['companies']
      ? req['companies'].toString()
      : '';

    const payments = await this.paymentService.refactoredIndex(paymentIndex);
    const responseBody = {
      payments: payments[0]['paginatedResult'],
      totalPayments: payments[0]['totalCount'][0]
        ? payments[0]['totalCount'][0]['createdAt']
        : 0,
      totalPaymentsWithoutFilters: payments[0]['totalCount'][0]
        ? payments[0]['totalCount'][0]['createdAt']
        : 0,
    };
    return responseBody;
  }

  @Get(':code')
  @SetMetadata('action', 'get_payment_details')
  @ApiResponse({ status: 200, type: Payment })
  async get_details(@Param('code') code: string, @Req() req: Request) {
    return await this.paymentService.get_details_front(code, req['company_id']);
  }

  @Get('/page/public/:uniqueIdentifier')
  @ApiResponse({ status: 200, type: Payment })
  @SetMetadata('public', 'true')
  async findOne(@Param('uniqueIdentifier') uniqueIdentifier: string) {
    return await this.paymentService.findOne(uniqueIdentifier);
  }

  @Get('/public/:code')
  @ApiResponse({ status: 200, type: Payment })
  @SetMetadata('public', 'true')
  async get_public_details(@Param('code') code: string) {
    return await this.paymentService.get_details_front(code, null);
  }

  @Post()
  @ApiResponse({ status: 201, type: Payment })
  @SetMetadata('action', 'create')
  async create(@Body() paymentToCreate: PaymentToCreate, @Req() req: Request) {
    paymentToCreate.company = req['company_id'] ? req['company_id'] : '';
    return await this.paymentService.create(paymentToCreate, req['current']);
  }

  @Post('clone')
  @SetMetadata('public', 'true')
  async clone(@Body() clonePaymentDto: ClonePaymentDto) {
    return this.paymentService.clonePaymentWithCustomerDetails(clonePaymentDto);
  }

  @Post('refund')
  @ApiResponse({ status: 201, type: Payment })
  @SetMetadata('action', 'refund')
  async refund(@Body() paymentToBeRefunded: PaymentToBeRefunded) {
    return await this.paymentService.changePaymentRefundedStatus(
      paymentToBeRefunded,
    );
  }

  // POST status git revert --no-commit 4dc7693ef8a627d614bc2accbc87e8e420d7c5d9

  @Get('export/excel')
  @SetMetadata('action', 'export_excel')
  async export_excel(
    @Req() req: Request,
    @Query() paymentToIndex: PaymentIndex,
  ) {
    paymentToIndex.company = req['company_id'] ? req['company_id'] : '';
    paymentToIndex.branches = req['branches'] ? req['branches'] : '';
    const filePath = await this.paymentService.exportExcel(paymentToIndex);
    return filePath;
  }

  @Version('1')
  @Post('process')
  @SetMetadata('public', 'true')
  async process_payment(@Body() paymentToProcess: PaymentToProcess) {
    return await this.paymentService.processPayment(paymentToProcess);
  }

  @Post('tess/session')
  @SetMetadata('public', 'true')
  async createTessCreditSession(
    @Body() createTessSessionDto: CreateTessSessionDto,
  ) {
    return await this.vistaMoneyService.createCreditCardSession(
      createTessSessionDto.paymentCode,
    );
  }

  @Get('receipt/:code')
  @SetMetadata('public', 'true')
  async get_receipt(@Param('code') code: string) {
    return await this.paymentService.get_details(code);
  }

  @Get('action/after_done')
  @SetMetadata('public', 'true')
  async vistaMoneyAfterPaymentDone(@Query() data, @Res() res: Response) {
    const callback_url = await this.vistaMoneyService.afterPaymentDone(data);
    return res.redirect(callback_url);
  }

  @Get('tess/callback/:paymentCode')
  @SetMetadata('public', 'true')
  async tessCreditAfterPaymentDone(
    @Query() data,
    @Param('paymentCode') paymentCode: string,
    @Res() res: Response,
  ) {
    const callback_url = await this.vistaMoneyService.creditCardCallback(
      paymentCode,
      data,
    );
    return res.redirect(callback_url);
  }

  @Post('action/skipcash/after_done')
  @SetMetadata('public', 'true')
  async fireSkipCashWebhook(
    @Headers('Authorization') authorization: string,
    @Body() skipCashWebhookDto: SkipCashWebhookDto,
    @Res() res: Response,
  ) {
    const callbackUrl = await this.skipCashService.fireSkipCashWebhook(
      skipCashWebhookDto,
      authorization,
    );
    return res.redirect(callbackUrl);
  }

  @Get('action/skipcash/returned')
  @SetMetadata('public', 'true')
  async skipCashAfterPaymentReturned(@Query() data, @Res() res: Response) {
    const callback_url = await this.skipCashService.afterPaymentReturned(data);
    return res.redirect(callback_url);
  }

  @Get('fatoorah/execute/:sessionId')
  @SetMetadata('public', 'true')
  async executeFatoorahPayment(
    @Param('sessionId') sessionId: string,
    @Res() res: Response,
  ) {
    const redirectURL =
      await this.myFatoorahPaymentService.executePayment(sessionId);
    return res.redirect(redirectURL);
  }

  @Get('fatoorah/callback/:paymentCode')
  @SetMetadata('public', 'true')
  async myFatoorahAfterPaymentReturned(
    @Param('paymentCode') paymentCode: string,
    @Query() data: any,
    @Res() res: Response,
  ) {
    const redirectURL = await this.myFatoorahPaymentService.redirectCustomer(
      paymentCode,
      data,
    );
    return res.redirect(redirectURL);
  }

  @Post('fatoorah/webhook')
  @SetMetadata('public', 'true')
  async myFatoorahAfterPaymentDone(
    @Req() req: Request,
    @Res() res: Response,
    @Headers('MyFatoorah-Signature') signature: string,
  ) {
    await this.myFatoorahPaymentService.afterPaymentDone(req.body, signature);

    return res.status(HttpStatus.OK).send('Webhook received');
  }

  @Get('action/cbpay/update_payment_status')
  @SetMetadata('action', 'cbpay_after_payment_done')
  async cbPayAfterPaymentDone(@Query() data) {
    return await this.cbPayService.afterPaymentDone(data);
  }

  @Post('action/cybersource/after_done')
  @SetMetadata('public', 'true')
  async cyberSourceAfterPaymentDone(@Body() data, @Res() res: Response) {
    const callback = await this.cyberSourceService.afterPaymentDone(data);
    return res.redirect(callback);
  }

  @Post('action/stripe/after_done')
  @SetMetadata('public', 'true')
  async stripeOnPaymentUpdated(@Body() data) {
    return await this.paymentStripeService.onStripeUpdated(data);
  }

  @Post('action/dibsy/after_done/:paymentId')
  @SetMetadata('public', 'true')
  async dibsyOnPaymentUpdated(
    @Param('paymentId') paymentId: string,
    @Body() data,
  ) {
    return await this.dibsyPaymentService.afterPaymentDone(paymentId, data);
  }

  @Get('action/dibsy/after_done/:paymentId')
  @SetMetadata('public', 'true')
  async dibsyAfterPaymentDoneRedirection(
    @Param('paymentId') paymentId: string,
    @Res() res: Response,
  ) {
    const callbackUrl =
      await this.dibsyPaymentService.afterPaymentDoneRedirection(paymentId);
    return res.redirect(callbackUrl);
  }

  @Get('action/tap/after_done/:paymentId')
  @SetMetadata('public', 'true')
  async tapAfterPaymentDoneRedirection(
    @Param('paymentId') paymentId: string,
    @Query('tap_id') tapId: string,
    @Res() res: Response,
  ) {
    const callbackUrl = await this.tapService.afterPaymentDoneRedirection(
      paymentId,
      tapId,
    );
    return res.redirect(callbackUrl);
  }

  @Post('action/tap/update_status/:paymentId')
  @SetMetadata('public', 'true')
  async tapUpdateStatus(
    @Param('paymentId') paymentId: string,
    @Body() charge: TapCharge,
    @Headers('hashstring') hash: string,
  ) {
    await this.tapService.updateStatus(paymentId, charge, hash);
  }

  @Post('action/dibsy/merchant_validation')
  @SetMetadata('public', 'true')
  async dibsyMerchantValidation(
    @Body() dibsyMerchantValidationToRequest: DibsyMerchantValidatedToRequest,
  ) {
    return await this.dibsyPaymentService.merchantValidationRequest(
      dibsyMerchantValidationToRequest,
    );
  }

  @Post('resend')
  @SetMetadata('action', 'resend_payment_link')
  async resend_payment_link(@Body() paymentToResend: PaymentToResend) {
    await this.paymentService.resendPaymentLink(paymentToResend.code);
  }

  @Post('cbpay/requestToken')
  @SetMetadata('public', 'true')
  async requestCbPayToken(
    @Body() cbPayTokenToRequest: CbPayTokenToRequest,
    @Res() res: Response,
  ) {
    // let token = await this.cbPayService.requestToken(cbPayTokenToRequest);
    // return this.helperService.handelSuccessResponse(
    //   responseCode.SUCCESS_TO_GETDETAILS,
    //   'Success to request a token from cpbay',
    //   token,
    //   res,
    // );
    return this.helperService.handelError(
      {
        data: null,
        errorCode: 9,
        opMessage: 'Mobile number not registered with CBQ',
        opStatus: false,
      },
      res,
    );
  }

  @Delete(':id')
  @SetMetadata('action', 'delete')
  async removePayment(@Param('id') id: string, @Req() req: Request) {
    return await this.paymentService.remove(id, req['current']);
  }
}
