import {
  CurrentUser,
  OrderDeliveryAction,
  OrderDocument,
  OrderLogActionEnum,
  OrderStatusEnum,
  OrderTransitionTrigger,
  responseCode,
} from '@app/shared-stuff';
import { BadRequestException, Inject } from '@nestjs/common';
import { OrderLogServiceInterface } from '../../interfaces/order-log.service.interface';
import { OrderNotificationService } from '../../order-notification/order-notification.service';
import { OrderStatusServiceInterface } from '../order-status.interface';

export class InRouteOrderService implements OrderStatusServiceInterface {
  transitionalStatuses = [
    OrderStatusEnum.PREPARING,
    OrderStatusEnum.PENDING_PICKUP,
  ];
  transitionalTrigger = [
    OrderTransitionTrigger.DELIVERY_TASK_STARTED,
    OrderTransitionTrigger.DELIVERECT_STATUS_UPDATE,
  ];

  constructor(
    @Inject('OrderLogServiceInterface')
    private readonly orderLogService: OrderLogServiceInterface,
    private orderNotificationService: OrderNotificationService,
  ) {}

  validateStatus(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
  ): boolean {
    return (
      this.ValidateTransition(order, orderTransitionTrigger) ||
      this.validatePreCondition(order)
    );
  }

  private ValidateTransition(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
  ): boolean {
    if (
      this.transitionalTrigger.includes(orderTransitionTrigger) &&
      this.transitionalStatuses.includes(order.status)
    )
      return true;

    throw new BadRequestException(
      "Can't Change From " + order.status + ' To IN_ROUTE',
      responseCode.STATUS_NOT_VALID.toString(),
    );
  }

  private validatePreCondition(order: OrderDocument): boolean {
    if (order.delivery_action === OrderDeliveryAction.DELIVERY_LOCATION)
      return true;

    throw new BadRequestException(
      "Can't change From " +
        order.status +
        ' to  In-Route' +
        'The order with ID ' +
        order._id +
        ' is driver did not complete pickup task',
      responseCode.STATUS_NOT_VALID.toString(),
    );
  }

  async applyPostFunction(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
    oldStatus: OrderStatusEnum,
    user: CurrentUser,
  ) {
    if (
      oldStatus == OrderStatusEnum.PREPARING &&
      order.status == OrderStatusEnum.IN_ROUTE
    ) {
      order.is_ready = true;
      await order.save();
    }
    await this.orderNotificationService.onOrderInRoute(order);
    await this.orderLogService.saveOrderLog(
      order,
      { oldStatus: oldStatus },
      { newStatus: OrderStatusEnum.IN_ROUTE, trigger: orderTransitionTrigger },
      OrderLogActionEnum.ON_ORDER_IN_ROUTE,
      user,
    );
  }
}
