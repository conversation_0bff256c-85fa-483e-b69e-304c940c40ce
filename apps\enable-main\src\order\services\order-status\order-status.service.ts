import {
  <PERSON><PERSON><PERSON>,
  CurrentUser,
  LoggerService,
  Order<PERSON><PERSON>dren,
  OrderDocument,
  OrderStatusEnum,
  OrderTransitionTrigger,
  responseCode,
} from '@app/shared-stuff';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CompanyService } from '../../../company/services/company/company.service';
import { CustomerOrderServiceInterface } from '../../../customer/modules/customer-order/customer-order.service.interface';
import { UserService } from '../../../user/services/user/user.service';
import { OrderLogServiceInterface } from '../interfaces/order-log.service.interface';
import { OrderReversionService } from '../order-reversion/order-reversion.service';
import { OrderNotificationService } from './../order-notification/order-notification.service';
import { CanceledOrderService } from './order-status-validator/canceled-order.service';
import { CompletedOrderService } from './order-status-validator/completed-order.service';
import { InRouteOrderService } from './order-status-validator/in-route-order.service';
import { PendingOrderService } from './order-status-validator/pending-order.service';
import { PendingPickupOrderService } from './order-status-validator/pending-pickup-order.service';
import { PreparingOrderService } from './order-status-validator/preparing-order.service';
import { ScheduledOrderService } from './order-status-validator/scheduled-order.service';
import { UnAssignedOrderService } from './order-status-validator/un-assigned-order.service';
import { OrderStatusServiceInterface } from './order-status.interface';

@Injectable()
export class OrderStatusService {
  private readonly loggerService = new LoggerService(OrderStatusService.name);
  private statusMap: Map<string, OrderStatusServiceInterface>;

  constructor(
    private eventEmitter: EventEmitter2,
    @Inject('OrderLogServiceInterface')
    private readonly orderLogService: OrderLogServiceInterface,
    private orderNotificationService: OrderNotificationService,
    private userService: UserService,
    @Inject(CustomerOrderServiceInterface)
    private customerOrderService: CustomerOrderServiceInterface,
    @InjectModel(CollectionName.ORDER) private orderModel: Model<OrderDocument>,
    private companyService: CompanyService,
    private readonly orderReversionService: OrderReversionService,
  ) {
    this.initStatusMap();
  }

  async changeOrderStatus(
    order: OrderDocument,
    status: OrderStatusEnum,
    orderTransitionTrigger: OrderTransitionTrigger,
    user: CurrentUser,
  ) {
    if (!order)
      throw new BadRequestException(
        "Can't change status to " + status + ' the Order is Not Created',
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    const statusService = this.statusMap.get(status);
    if (statusService) {
      statusService.validateStatus(order, orderTransitionTrigger);
      const oldStatus = order.status;
      await this.saveNewOrderStatus(order, status);
      this.loggerService.log(
        'after saving new order status , order details :',
        order,
      );

      await statusService.applyPostFunction(
        order,
        orderTransitionTrigger,
        oldStatus,
        user,
      );

      await this.orderNotificationService.onOrderStatusUpdated(order);
      // UPDATE the order STATUS logic
      await this.parentStatusLogic(order, orderTransitionTrigger, user);
      this.eventEmitter.emit('order.status.updated', order);
    } else {
      throw new BadRequestException({
        message: `${status} is not a valid order status, please provide a correct one`,
      });
    }
  }

  private async saveNewOrderStatus(
    order: OrderDocument,
    status: OrderStatusEnum,
  ) {
    order.status = status;
    await order.save();
  }

  private initStatusMap() {
    this.statusMap = new Map<string, OrderStatusServiceInterface>([
      [
        OrderStatusEnum.UNASSIGNED,
        new UnAssignedOrderService(
          this.orderLogService,
          this.orderNotificationService,
          this.eventEmitter,
        ),
      ],
      [
        OrderStatusEnum.PENDING,
        new PendingOrderService(
          this.orderLogService,
          this.orderNotificationService,
          this,
          this.eventEmitter,
        ),
      ],
      [
        OrderStatusEnum.SCHEDULED,
        new ScheduledOrderService(this.orderLogService, this.userService),
      ],
      [
        OrderStatusEnum.PREPARING,
        new PreparingOrderService(
          this.orderLogService,
          this.userService,
          this.orderNotificationService,
          this.eventEmitter,
        ),
      ],
      [
        OrderStatusEnum.PENDING_PICKUP,
        new PendingPickupOrderService(
          this.orderLogService,
          this.orderNotificationService,
        ),
      ],
      [
        OrderStatusEnum.IN_ROUTE,
        new InRouteOrderService(
          this.orderLogService,
          this.orderNotificationService,
        ),
      ],
      [
        OrderStatusEnum.COMPLETED,
        new CompletedOrderService(
          this.orderLogService,
          this.customerOrderService,
          this.orderNotificationService,
          this.companyService,
        ),
      ],
      [
        OrderStatusEnum.CANCELED,
        new CanceledOrderService(
          this.orderLogService,
          this.orderNotificationService,
          this.eventEmitter,
          this.orderReversionService,
        ),
      ],
    ]);
  }

  private async parentStatusLogic(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
    user: CurrentUser,
  ) {
    if (order.parentId) {
      const parentOrder = await this.orderModel
        .findOne({
          _id: order.parentId,
        })
        .exec();

      if (parentOrder.children && parentOrder.children.length) {
        await this.updateChildrenInsideParentStatus(parentOrder, order);
        if (
          order.status == OrderStatusEnum.PREPARING &&
          this.parentStatusNeedsUpdate(parentOrder)
        ) {
          await this.changeOrderStatus(
            parentOrder,
            OrderStatusEnum.COMPLETED,
            orderTransitionTrigger,
            user,
          );
        }
      }
    }
  }

  private async updateChildrenInsideParentStatus(
    parentOrder: OrderDocument,
    childrenOrder: OrderDocument,
  ) {
    const childOrder: OrderChildren = parentOrder.children.find((x) =>
      x.orderId.equals(childrenOrder._id),
    );
    childOrder && (childOrder.status = childrenOrder.status);

    await parentOrder.save();
  }

  private parentStatusNeedsUpdate(parentOrder: OrderDocument): boolean {
    return parentOrder.children.every(
      (children: OrderChildren) => children.status == OrderStatusEnum.PREPARING,
    );
  }
}
