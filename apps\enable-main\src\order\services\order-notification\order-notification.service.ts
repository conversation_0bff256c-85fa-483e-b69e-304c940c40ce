import {
  AcknowledgementType,
  BranchWithId,
  capturedLoyaltyOrderSources,
  CompanyDocument,
  CustomerDocument,
  GenericTriggerModel,
  GiftRecipientUser,
  Language,
  LanguageCode,
  LoggerService,
  LoyaltyStatus,
  OrderDeliveryAction,
  OrderDeliveryType,
  OrderDocument,
  OrderPaymentMethod,
  OrderPaymentStatus,
  OrderSource,
  TriggerAction,
  TriggerModule,
} from '@app/shared-stuff';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CompanyService } from '../../../company/services/company/company.service';
import { CustomerPointsService } from '../../../customer/modules/customer-points/customer-points.service';
import { CustomerReadServiceInterface } from '../../../customer/modules/customer-read/customer-read.service.interface';
import { CustomerReplacementsServiceInterface } from '../../../customer/modules/customer-replacements/customer-replacements.service.interface';
import { NotificationService } from '../../../notification/services/notification/notification.service';
import { TriggerService } from '../../../notification/services/trigger/trigger.service';
import { capitalizeFirstLetter } from '../../../shared/services/helper/helper.service';
import { UserService } from '../../../user/services/user/user.service';
import { AcknowledgeOrderDto } from '../../dto/order.dto';
import { OrderPaymentService } from '../order-payment/order-payment.service';

@Injectable()
export class OrderNotificationService {
  private readonly logger = new LoggerService(OrderNotificationService.name);

  constructor(
    private notificationService: NotificationService,
    private configService: ConfigService,
    private triggerService: TriggerService,
    private companyService: CompanyService,
    private userService: UserService,
    @Inject(CustomerReadServiceInterface)
    private customerReadService: CustomerReadServiceInterface,
    @Inject(CustomerReplacementsServiceInterface)
    private customerReplacementsService: CustomerReplacementsServiceInterface,
    private eventEmitter: EventEmitter2,
    @Inject(forwardRef(() => OrderPaymentService))
    private orderPaymentService: OrderPaymentService,
    private customerPointsService: CustomerPointsService,
  ) {}

  public async onOrderIsCreated(
    order: OrderDocument,
    company: CompanyDocument,
  ) {
    if (!company) {
      company = await this.companyService.get_details(
        order.company['_id'] ? order.company['_id'] : order.company,
      );
    }
    //send notification here

    if (company.acknowledgementScreenType === AcknowledgementType.MANUAL)
      await this.notificationService.fireOrderPusherTrigger(
        order,
        undefined,
        'Dispatcher',
        'orderAcknowledgeReceived',
        true,
      );
  }

  public async onOrderCreatedWithBranch(
    order: OrderDocument,
    branch: BranchWithId,
    currentUser,
  ) {
    //send notification here

    const company = await this.companyService.get_details(
      order.company['_id'] ? order.company['_id'] : order.company,
    );
    if (
      company.acknowledgementScreenType === AcknowledgementType.MANUAL &&
      !order.isAcknowledged
    ) {
      this.logger.log(
        'onOrderCreatedWithBranch , with branch name :',
        branch?.name,
      );
      await this.notificationService.fireOrderPusherTrigger(
        order,
        branch,
        'Branch Manager',
        'orderAcknowledgeReceived',
        true,
      );
    }
  }
  public async handleDeliveryAndPaymentNotification(
    order: OrderDocument,
    genericTriggerModel?: GenericTriggerModel,
  ) {
    if (
      !order.deliveryLocationId &&
      order.delivery_action == OrderDeliveryAction.SEND_SMS &&
      order.payment_method != OrderPaymentMethod.online &&
      !order.is_gift
    ) {
      await this.onSendDeliveryLocationSMS(order, genericTriggerModel);
    } else if (
      !order.deliveryLocationId &&
      order.delivery_action == OrderDeliveryAction.SEND_SMS &&
      order.payment_method == OrderPaymentMethod.online &&
      !order.is_gift
    ) {
      await this.OnSendDeliveryLocationAndPaymentRequestSMS(
        order,
        genericTriggerModel,
      );
    } else if (
      order.is_gift &&
      order.delivery_type === OrderDeliveryType.urgent &&
      !order.deliveryLocationId &&
      order.delivery_action == OrderDeliveryAction.SEND_SMS
    ) {
      await this.fireGiftRecipientLocationTrigger(order, genericTriggerModel);
    } else if (
      order.payment_method === OrderPaymentMethod.online &&
      order.payment_status !== OrderPaymentStatus.COMPLETED
    ) {
      await this.OnOnlinePayment(order, genericTriggerModel);
    }
  }

  async onOrderAcknowledged(
    order: OrderDocument,
    acknowledgeOrderDto: AcknowledgeOrderDto,
  ) {
    await this.fireOrderNotificationTrigger(
      order,
      TriggerAction.ON_ORDER_ACKNOWLEDGED,
    );
  }

  async onOrderInRoute(order: OrderDocument) {
    await this.fireOrderNotificationTrigger(
      order,
      TriggerAction.ON_ORDER_IN_ROUTE,
    );
  }

  async onOrderCompleted(order: OrderDocument) {
    const genericTriggerModel = await this.populateGenericTriggerModel(
      order,
      TriggerAction.ON_ORDER_COMPLETED,
    );

    await this.fireOrderNotificationTrigger(
      order,
      TriggerAction.ON_ORDER_COMPLETED,
      genericTriggerModel,
    );

    if (order.source === OrderSource.DELIVERECT) {
      await this.fireOrderNotificationTrigger(
        order,
        TriggerAction.ON_ORDER_COMPLETED_DELIVERECT,
        genericTriggerModel,
      );
    }
  }

  async onGiftOrderCompleted(order: OrderDocument): Promise<void> {
    await this.fireOrderNotificationTrigger(
      order,
      TriggerAction.ON_GIFT_ORDER_COMPLETED,
    );
  }

  async onCapturedOrder(order: OrderDocument, customer: CustomerDocument) {
    const isLoyaltyOrder = capturedLoyaltyOrderSources.includes(order.source);
    const isLoyaltyMember = customer.loyaltyStatus === LoyaltyStatus.MEMBER;

    const fire = async (action: TriggerAction) =>
      await this.fireOrderNotificationTrigger(order, action);

    if (isLoyaltyOrder)
      await fire(
        isLoyaltyMember
          ? TriggerAction.ON_LOYALTY_ORDER_LOYALTY_MEMBERS
          : TriggerAction.ON_LOYALTY_ORDER_NON_LOYALTY_MEMBERS,
      );

    if (order.source === OrderSource.DINE_IN)
      await fire(TriggerAction.ON_DINEIN_ORDER);
    else if (order.source === OrderSource.WALK_IN)
      await fire(TriggerAction.ON_WALK_IN_ORDER);
    else if (!isLoyaltyOrder) {
      await fire(TriggerAction.ON_AGGREGATOR_ORDER);
      if (!isLoyaltyMember)
        await fire(TriggerAction.ON_AGGREGATOR_ORDER_NON_LOYALTY_MEMBERS);
    }
  }

  public async onOrderAssigned(order: OrderDocument, branch: BranchWithId) {
    const company = await this.companyService.get_details(
      order.company['_id'] ? order.company['_id'] : order.company,
    );
    if (company.acknowledgementScreenType === AcknowledgementType.MANUAL)
      await this.notificationService.fireOrderPusherTrigger(
        order,
        branch,
        'Branch Manager',
        'orderAcknowledgeReceived',
        true,
      );

    await this.fireOrderNotificationTrigger(
      order,
      TriggerAction.ON_ORDER_ASSIGNED_TO_BRANCH,
      undefined,
    );
  }

  public async onOrderReadyForPickup(order: OrderDocument) {
    await this.fireOrderNotificationTrigger(
      order,
      TriggerAction.ON_ORDER_READY,
    );
  }

  public async onOrderRejected(order: OrderDocument) {
    await this.fireOrderNotificationTrigger(
      order,
      TriggerAction.ON_ORDER_REJECTED,
    );

    await this.notificationService.fireOrderPusherTrigger(
      order,
      undefined,
      'Dispatcher',
      'orderAcknowledgeReceived',
      true,
    );
  }

  public async onOrderCancelled(order: OrderDocument) {
    await this.fireOrderNotificationTrigger(
      order,
      TriggerAction.ON_ORDER_CANCELED,
    );
    await this.handleOrderPaymentStatus(order);

    // Removing the Order From the Acknowledge List for all users
    await this.userService.removeAcknowledgeOrdersFromUsers(order, 'canceled');
  }

  private async handleOrderPaymentStatus(order: OrderDocument): Promise<void> {
    if (order.payment_status == OrderPaymentStatus.COMPLETED)
      order.payment_status = OrderPaymentStatus.NEEDS_REFUND;
    else order.payment_status = OrderPaymentStatus.CANCELLED;
    await order.save();
  }

  public async onDeliveryDateHasBeenUpdated(order: OrderDocument) {
    await this.fireOrderNotificationTrigger(
      order,
      TriggerAction.ON_ORDER_DELIVERY_DATE_UPDATED,
    );
  }

  public async onRemainingXTimeOnDelivery(order: OrderDocument) {
    // const branch = await this.branchService.get_details(
    //   order.branch,
    //   undefined,
    // );
    if (order.branch && typeof order.branch !== 'string') {
      await this.notificationService.fireOrderPusherTrigger(
        order,
        order.branch,
        'Branch Manager',
        'orderAcknowledgeReceived',
        true,
      );
    }
  }

  public async onOrderDeleted(order: OrderDocument) {
    // Firebase Notification

    // this.pusher.trigger(order._id.toHexString(), 'orderCanceledOrDeleted', {
    //   id: order._id,
    //   code: order.code,
    //   status: 'deleted',
    //   timestamp: moment.utc().toDate(),
    // });
    // Removing the Order From the Acknowledge List for all users
    await this.userService.removeAcknowledgeOrdersFromUsers(order, 'deleted');
    this.eventEmitter.emit('order.deleted', order);
  }

  public async onOrderDelayed(order: OrderDocument) {}

  public async onOrderDeliveredFromTookan(order: OrderDocument) {}

  async fireGiftRecipientLocationTrigger(
    order: OrderDocument,
    genericTriggerModel?: GenericTriggerModel,
  ) {
    if (
      order.is_gift &&
      order.delivery_action === OrderDeliveryAction.SEND_SMS
    ) {
      await this.fireOrderNotificationTrigger(
        order,
        TriggerAction.ON_SEND_DELIVERY_LOCATION_SMS_GIFT,
        genericTriggerModel,
      );

      order.firedTriggers.push(
        TriggerAction.ON_SEND_DELIVERY_LOCATION_SMS_GIFT,
      );
      await order.save();
    }
  }

  // General Status updated
  public async onOrderStatusUpdated(order: OrderDocument) {
    await this.fireOrderNotificationTrigger(
      order,
      TriggerAction.ON_ORDER_STATUS_UPDATED,
    );
  }

  public async onOrderNotificationAcknowledge(order: OrderDocument) {}

  async onDeliveryLocationUpdateSuccess(order: OrderDocument) {
    await this.fireOrderNotificationTrigger(
      order,
      TriggerAction.ON_DELIVERY_LOCATION_SUBMITTED,
      undefined,
    );
  }

  async onSendDeliveryLocationSMS(
    order: OrderDocument,
    genericTriggerModel: GenericTriggerModel,
  ) {
    genericTriggerModel = await this.populateGenericTriggerModel(
      order,
      TriggerAction.ON_SEND_DELIVERY_LOCATION_SMS,
    );
    await this.fireOrderNotificationTrigger(
      order,
      TriggerAction.ON_SEND_DELIVERY_LOCATION_SMS,
      genericTriggerModel,
    );
  }

  async OnSendDeliveryLocationAndPaymentRequestSMS(
    order: OrderDocument,
    genericTriggerModel: GenericTriggerModel,
  ) {
    genericTriggerModel = await this.populateGenericTriggerModel(
      order,
      TriggerAction.ON_DELIVERY_LOCATION_AND_PAYMENT_REQUEST_SMS,
    );
    await this.fireOrderNotificationTrigger(
      order,
      TriggerAction.ON_DELIVERY_LOCATION_AND_PAYMENT_REQUEST_SMS,
      genericTriggerModel,
    );
  }

  async OnOnlinePayment(
    order: OrderDocument,
    genericTriggerModel: GenericTriggerModel,
  ) {
    if (
      ![OrderSource.WEBSTORE, OrderSource.MOBILE_APP].includes(order.source)
    ) {
      await this.fireOrderNotificationTrigger(
        order,
        TriggerAction.ON_ONLINE_PAYMENT,
        genericTriggerModel,
      );
    }
  }

  async fireOnLoyaltyTransactionReversionTrigger(
    order: OrderDocument,
  ): Promise<void> {
    await this.fireOrderNotificationTrigger(
      order,
      TriggerAction.ON_TRANSACTION_REVERSION,
    );
  }

  async fireOrderNotificationTrigger(
    order: OrderDocument,
    triggerAction: TriggerAction,
    genericTriggerModel?: GenericTriggerModel,
  ) {
    if (order.parentId) {
      return;
    }

    if (!genericTriggerModel) {
      genericTriggerModel = await this.populateGenericTriggerModel(
        order,
        triggerAction,
      );
    }

    await this.triggerService.fireTrigger(genericTriggerModel, triggerAction);
  }

  async populateGenericTriggerModel(
    order: OrderDocument,
    triggerAction: TriggerAction,
  ): Promise<GenericTriggerModel> {
    const customer = await this.customerReadService.findOne(
      order.customer?.toString(),
    );
    const companyId = order.company['_id']
      ? order.company['_id']
      : order?.company;
    const company = companyId
      ? await this.companyService.get_details(companyId)
      : null;

    const replacements = await this.getAllOrderReplacements(
      order,
      triggerAction,
      customer,
      company,
    );
    const giftRecipientUser: GiftRecipientUser = {
      recipientName: order.recipient_name,
      recipientCountryCode: order.recipient_country_code,
      recipientPhone: order.recipient_phone,
    };

    const branchId =
      typeof order.branch !== 'string'
        ? order?.branch?._id?.toString()
        : order?.branch;

    const brandId = order?.brand?._id?.toHexString();

    return {
      companyId: companyId.toString(),
      branchId: branchId,
      brandId: brandId,
      customerId: order.customer,
      countryCode: order.country_code,
      createdBy: order.createdBy,
      giftRecipientUser: giftRecipientUser,
      isGift: order.is_gift,
      senderId: order.brand ? order.brand.senderId : company?.senderId,
      emailSenderId: order.brand ? order.brand.emailSenderId : undefined,
      triggerModule: TriggerModule.ORDER,
      replacements: replacements,
      language:
        order.language === Language.english ? LanguageCode.en : LanguageCode.ar,
      context: {
        customer: {
          ...customer.toJSON(),
          _id: customer._id,
        },
        order,
      },
    };
  }

  private async getAllOrderReplacements(
    order: OrderDocument,
    triggerAction: TriggerAction,
    customer?: CustomerDocument,
    company?: CompanyDocument,
  ) {
    if (!customer) {
      customer = await this.customerReadService.findOne(
        order['customer']['_id']
          ? order['customer']['_id'].toHexString()
          : order['customer'].toHexString(),
        order.company['_id'] ? order.company['_id'] : order?.company,
      );
    }

    if (!company) {
      company = await this.companyService.get_details(
        order.company['_id'] ? order.company['_id'] : order.company,
      );
    }
    const khuludBranchId = order.branch
      ? (order.branch as BranchWithId).integrationInfo?.khuludBranchId
      : undefined;

    const orderEarnedLoyaltyPoints =
      this.customerPointsService.calculatePointsEarnedOnOrder(
        order,
        customer,
        company,
      );
    return {
      totalAmount: Number.isInteger(order.total_amount)
        ? order.total_amount.toString()
        : order.total_amount.toFixed(2),
      orderCode: order.code,
      orderSource: order.source,
      paymentLinkId: order.payment_code ?? '',
      paymentLink: order.payment_code
        ? this.configService.get('PAY_PAGE') + '/' + order.payment_code
        : '',
      brandName: order.brand ? order.brand.name : '',
      customerName:
        triggerAction == TriggerAction.ON_SEND_DELIVERY_LOCATION_SMS_GIFT &&
        order.is_gift &&
        order.is_secret
          ? 'A friend'
          : capitalizeFirstLetter(order.customer_name),
      orderStatus: order.status,
      phoneNumber: order.customer_phone,
      email: customer.email ?? '',
      companyName: capitalizeFirstLetter(company.name),
      userName: order.createdBy ? order.createdBy.name : '',
      amountWithoutDelivery: Number.isInteger(order.invoiced_amount)
        ? order.invoiced_amount.toString()
        : order.invoiced_amount.toFixed(2),
      paymentMethod: order.payment_method,
      recipientName: capitalizeFirstLetter(order.recipient_name) ?? '',
      revertedPoints: Number.isInteger(order.loyaltyProgress?.pointsUnearned)
        ? order.loyaltyProgress?.pointsUnearned.toString()
        : order.loyaltyProgress?.pointsUnearned?.toFixed(2),
      stampsRemovedFirstTrack: order.loyaltyProgress?.stampsRemovedFirstTrack,
      stampsRemovedSecondTrack: order.loyaltyProgress?.stampsRemovedSecondTrack,
      deliveryAction: order.delivery_action,
      pickupDate: order.pickup_date ? order.pickup_date.toString() : '',
      deliveryDate: order.delivery_date ? order.delivery_date.toString() : '',
      locationShortenURL: order.shortenUrl ? order.shortenUrl : '',
      locationLink: this.configService.get('LOCATION_PAGE') + '/' + order.code,
      orderTrackingShortenURL: order.shortenTrackingLink
        ? order.shortenTrackingLink
        : '',
      orderTrackingLink:
        this.configService.get('ORDER_TRACKING_BASE') + '/' + order.code,
      paymentShortenURL:
        order.payment_method == OrderPaymentMethod.online
          ? order.paymentShortenURL
            ? order.paymentShortenURL
            : `${this.configService.get('PAY_PAGE')}/${order.payment_code}`
          : '',
      orderEarnedLoyaltyPoints: Number.isInteger(orderEarnedLoyaltyPoints)
        ? orderEarnedLoyaltyPoints.toString()
        : orderEarnedLoyaltyPoints?.toFixed(2),
      currentOrderUnlockedCouponBenefit:
        order.loyaltyProgress?.highestCouponUnlocked?.titleEn,
      invoiceNumber: order.invoice_number ?? '',
      ...(await this.customerReplacementsService.getCustomerReplacements(
        customer,
        {
          brandId: order.brand?._id,
          preferredLanguage: order.language,
        },
      )),
      khuludBranchId,
    };
  }
}
