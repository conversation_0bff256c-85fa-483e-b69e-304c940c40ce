import {
  BulkUpdateLoyaltyTiersDto,
  CreateLoyaltyTierDto,
  LoyaltyTier,
  LoyaltyTierDocument,
  LoyaltyTierThresholds,
  LoyaltyTierWithIdDto,
  ThresholdMap,
  TierLevellingUpMethod,
  TierWithRequirements,
  UpdateLoyaltyTierDto,
} from '@app/shared-stuff';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { forwardRef } from '@nestjs/common/utils';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { plainToInstance } from 'class-transformer';
import { Types } from 'mongoose';
import * as randomstring from 'randomstring';
import { CompanyService } from '../../../company/services/company/company.service';
import { ImageService } from '../../../shared/services/image/image.service';
import { LoyaltyTierReadServiceInterface } from '../loyalty-tier-read/loyalty-tier-read.service.interface';
import { LoyaltyTierRepositoryInterface } from '../loyalty-tier-repository/loyalty-tier.repository.interface';
import { LoyaltyTierWriteServiceInterface } from './loyalty-tier-write.service.interface';
import { BenefitUtilityService } from 'apps/enable-main/src/benefit/benefit-utility/benefit-utility.service';

@Injectable()
export class LoyaltyTierWriteService
  implements LoyaltyTierWriteServiceInterface
{
  constructor(
    @Inject(LoyaltyTierRepositoryInterface)
    private readonly loyaltyTierRepository: LoyaltyTierRepositoryInterface,
    @Inject(LoyaltyTierReadServiceInterface)
    private readonly loyaltyTierReadService: LoyaltyTierReadServiceInterface,
    private readonly eventEmitter: EventEmitter2,
    private readonly imageService: ImageService,
    @Inject(forwardRef(() => CompanyService))
    private readonly companyService: CompanyService,
    private readonly benefitUtilityService: BenefitUtilityService,
  ) {}

  async create(
    createLoyaltyTierDto: CreateLoyaltyTierDto,
  ): Promise<LoyaltyTierDocument> {
    const highestTier = await this.loyaltyTierRepository.findHighestTier(
      createLoyaltyTierDto.companyId,
    );

    const tierIndex = highestTier ? highestTier.tierIndex + 1 : 0;

    const newLoyaltyTier = plainToInstance(LoyaltyTier, {
      ...createLoyaltyTierDto,
      tierIndex,
      enrollmentCode: randomstring.generate({ length: 16 }),
    });

    await this.validateHasUniqueQualifiers(newLoyaltyTier);
    const company = await this.companyService.findById(
      createLoyaltyTierDto.companyId,
    );

    if (createLoyaltyTierDto.benefits)
      newLoyaltyTier.benefits = await this.benefitUtilityService.upsertEmbedded(
        company,
        createLoyaltyTierDto.benefits,
      );

    this.validateThresholds(
      newLoyaltyTier,
      company?.loyaltyProgramConfig.tierLevellingUpMethod,
    );

    const validThresholds =
      ThresholdMap[company.loyaltyProgramConfig.tierLevellingUpMethod];
    const thresholdsToDelete = LoyaltyTierThresholds.filter(
      (threshold) => !validThresholds.includes(threshold),
    );
    thresholdsToDelete.forEach((threshold) => delete newLoyaltyTier[threshold]);

    const loyaltyTier = await this.loyaltyTierRepository.create(newLoyaltyTier);
    const updatedTiers = await this.recomputeTierIndexes(loyaltyTier.companyId);
    const updatedTier = updatedTiers.find(
      (tier) => tier._id.toHexString() === loyaltyTier._id.toHexString(),
    );
    this.emitTierCreatedEvents(updatedTier, updatedTiers);

    return updatedTier ?? loyaltyTier;
  }

  async update({
    loyaltyTierId,
    ...updateLoyaltyTierDto
  }: UpdateLoyaltyTierDto): Promise<LoyaltyTierDocument> {
    const existingTier =
      await this.loyaltyTierReadService.findById(loyaltyTierId);
    const existingFreeDelivery = existingTier.freeDelivery;

    const tierBeforeUpdate: LoyaltyTierWithIdDto = existingTier.toObject();
    existingTier.set(updateLoyaltyTierDto);
    const company = await this.companyService.findById(existingTier.companyId);
    this.validateThresholds(
      existingTier,
      company?.loyaltyProgramConfig.tierLevellingUpMethod,
    );
    await this.validateHasUniqueQualifiers(existingTier);
    const loyaltyTier = await this.loyaltyTierRepository.findOneAndUpdate(
      { _id: existingTier._id },
      [
        { $set: updateLoyaltyTierDto },
        {
          $unset: LoyaltyTierThresholds.filter(
            (threshold) =>
              !ThresholdMap[
                company.loyaltyProgramConfig.tierLevellingUpMethod
              ].includes(threshold),
          ),
        },
      ],
    );

    if (updateLoyaltyTierDto.benefits) {
      loyaltyTier.benefits = await this.benefitUtilityService.upsertEmbedded(
        company,
        updateLoyaltyTierDto.benefits,
      );
      await loyaltyTier.save();
    }

    if (!existingFreeDelivery && loyaltyTier.freeDelivery)
      this.eventEmitter.emit('loyaltytier.freeDelivery.added', loyaltyTier);

    if (existingFreeDelivery && !loyaltyTier.freeDelivery)
      this.eventEmitter.emit('loyaltytier.freeDelivery.removed', loyaltyTier);

    const updatedTiers = await this.recomputeTierIndexes(loyaltyTier.companyId);
    const updatedTier = updatedTiers.find(
      (tier) => tier._id.toHexString() === loyaltyTier._id.toHexString(),
    );
    this.emitTierUpdatedEvents(tierBeforeUpdate, updatedTier, updatedTiers);
    return updatedTier ?? loyaltyTier;
  }

  async bulkUpdate({
    loyaltyTiers,
  }: BulkUpdateLoyaltyTiersDto): Promise<LoyaltyTierDocument[]> {
    return await this.loyaltyTierRepository.bulkUpdate(loyaltyTiers);
  }

  async generateEnrollmentCode(loyaltyTierId: Types.ObjectId): Promise<string> {
    const loyaltyTier =
      await this.loyaltyTierReadService.findById(loyaltyTierId);

    if (loyaltyTier.enrollmentCode) return loyaltyTier.enrollmentCode;

    const enrollmentCode = randomstring.generate({ length: 16 });
    await loyaltyTier.updateOne({ enrollmentCode });
    return enrollmentCode;
  }

  async updateTierLevellingUpMethod(
    companyId: Types.ObjectId,
    tiers: TierWithRequirements[],
  ): Promise<void> {
    await this.loyaltyTierRepository.applyTierRequirementsUpdate(tiers);
    const updatedTiers = await this.recomputeTierIndexes(companyId);
    updatedTiers.forEach((tier) =>
      this.eventEmitter.emit('loyaltytier.updated', tier),
    );
  }

  private validateThresholds(
    loyaltyTier: Omit<TierWithRequirements, '_id'>,
    tierLevellingUpMethod: TierLevellingUpMethod,
  ) {
    if (!tierLevellingUpMethod)
      throw new BadRequestException(
        'Please configure tierLevellingUpMethod before creating tiers.',
      );

    const thresholds = ThresholdMap[tierLevellingUpMethod];
    const missingThresholds = thresholds.filter(
      (threshold) => !loyaltyTier[threshold],
    );

    if (missingThresholds.length > 0)
      throw new BadRequestException(
        `Since tierLevellingUpMethod is ${tierLevellingUpMethod}, ${missingThresholds.join(
          ' and ',
        )} must be specified`,
      );
  }

  private async recomputeTierIndexes(
    companyId: Types.ObjectId,
  ): Promise<LoyaltyTierDocument[]> {
    const loyaltyTiers =
      await this.loyaltyTierRepository.findByCompanyId(companyId);
    if (!loyaltyTiers || loyaltyTiers.length === 0) return [];
    const sortedTiers = await this.getSortedTiers(loyaltyTiers, companyId);

    return await this.bulkUpdate({ loyaltyTiers: sortedTiers });
  }

  private async getSortedTiers(
    loyaltyTiers: LoyaltyTierDocument[],
    companyId: Types.ObjectId,
  ): Promise<LoyaltyTierWithIdDto[]> {
    const company = await this.companyService.findById(companyId);

    return loyaltyTiers
      .map((tier) => tier.toJSON() as LoyaltyTierWithIdDto)
      .sort(
        this.getTierComparator(
          company.loyaltyProgramConfig?.tierLevellingUpMethod,
        ),
      )
      .map((tier, index, tiers) => ({
        ...tier,
        tierIndex: index,
        isHighestTier: index === tiers.length - 1,
      }));
  }

  private getTierComparator(
    tierLevellingUpMethod: TierLevellingUpMethod,
  ): (
    firstTierToCompare: TierWithRequirements,
    secondTierToCompare: TierWithRequirements,
  ) => number {
    switch (tierLevellingUpMethod) {
      case TierLevellingUpMethod.AMOUNT_SPENT:
        return (a, b) => a.amountSpentThreshold - b.amountSpentThreshold;
      case TierLevellingUpMethod.ORDER_RATE:
        return (a, b) => a.orderRateThreshold - b.orderRateThreshold;
      case TierLevellingUpMethod.POINTS_RATE:
        return (a, b) => a.pointsRateThreshold - b.pointsRateThreshold;
      case TierLevellingUpMethod.BOTH:
      default:
        return (a, b) =>
          a.orderRateThreshold === b.orderRateThreshold
            ? a.amountSpentThreshold - b.amountSpentThreshold
            : a.orderRateThreshold - b.orderRateThreshold;
    }
  }

  private async validateHasUniqueQualifiers(
    tierToCheck: LoyaltyTier | LoyaltyTierWithIdDto,
  ) {
    let existingTiers = await this.loyaltyTierRepository.findByCompanyId(
      tierToCheck.companyId,
    );

    if ('_id' in tierToCheck) {
      const tierId = tierToCheck._id.toHexString();
      existingTiers = existingTiers.filter(
        (existingTier) => existingTier._id.toHexString() !== tierId,
      );
    }

    if (!existingTiers || existingTiers.length === 0) return;

    const haveSameQualifiers = (existingTier: LoyaltyTier) =>
      LoyaltyTierThresholds.every(
        (qualifier) => existingTier[qualifier] === tierToCheck[qualifier],
      );

    if (existingTiers.some(haveSameQualifiers)) {
      const match = existingTiers.find(haveSameQualifiers);
      throw new BadRequestException(
        `Could not ${
          '_id' in tierToCheck ? 'update' : 'create'
        } loyalty tier '${
          tierToCheck.nameEn
        }' as it has the same tier qualifiers as existing tier '${
          match.nameEn
        }'`,
      );
    }
  }

  private emitTierCreatedEvents(
    createdTier: LoyaltyTierDocument,
    allTiers: LoyaltyTierDocument[],
  ) {
    this.eventEmitter.emit('loyaltytier.created', createdTier);
    this.emitUpperTierChanged(createdTier, allTiers);
    this.eventEmitter.emit(
      'loyaltytier.requirements.lowered',
      createdTier,
      allTiers.filter((tier) => tier.tierIndex < createdTier.tierIndex),
    );
  }

  private emitTierUpdatedEvents(
    tierBeforeUpdate: LoyaltyTierWithIdDto,
    tierAfterUpdate: LoyaltyTierDocument,
    updatedTiers: LoyaltyTierDocument[],
  ) {
    this.eventEmitter.emit(
      'loyaltytier.updated',
      tierAfterUpdate,
      tierBeforeUpdate,
    );
    this.emitUpperTierChanged(tierAfterUpdate, updatedTiers);

    const isTierRequirementsLowered = LoyaltyTierThresholds.some(
      (threshold) => {
        const isThresholdAdded =
          !tierBeforeUpdate[threshold] && tierAfterUpdate[threshold];

        const isThresholdLowered =
          tierBeforeUpdate[threshold] > tierAfterUpdate[threshold];
        return isThresholdAdded || isThresholdLowered;
      },
    );

    if (!isTierRequirementsLowered) return;

    const tiersEligibleForUpgrades = updatedTiers.filter(
      (tier) => tier.tierIndex > tierAfterUpdate.tierIndex,
    );
    this.eventEmitter.emit(
      'loyaltytier.requirements.lowered',
      tierAfterUpdate,
      tiersEligibleForUpgrades,
    );
  }

  private emitUpperTierChanged(
    changedTier: LoyaltyTierDocument,
    allTiers: LoyaltyTierDocument[],
  ) {
    if (changedTier.tierIndex === 0) return;

    const lowerTier = allTiers.find(
      (tier) => tier.tierIndex === changedTier.tierIndex - 1,
    );
    this.eventEmitter.emit('loyaltytier.upperTier.changed', lowerTier);
  }
}
