import {
  CapturedItem,
  CollectionName,
  CurrentUser,
  ImportMenuItemsDto,
  LogError,
  LoggerService,
  mapAsync,
  MENU_ITEM_IMPORT_MAPPINGS,
  MenuCategoryWithId,
  MenuItemAvailabilityAction,
  MenuItemAvailabilityDto,
  MenuItemBulkWriteDto,
  MenuItemDocument,
  MenuItemIndexOutput,
  MenuItemRow,
  MenuItemSearchType,
  MenuItemSortMapping,
  MenuItemToCreate,
  MenuItemToIndex,
  MenuItemToPromote,
  MenuItemToRemove,
  MenuItemToUpdate,
  MenuWithId,
  OrderItem,
  responseCode,
  TriggerAction,
} from '@app/shared-stuff';
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { plainToInstance } from 'class-transformer';
import * as moment from 'moment-timezone';
import { isValidObjectId, Model, Types } from 'mongoose';
import * as randomString from 'randomstring';
import * as xlsx from 'xlsx';
import { BranchService } from '../../../branch/services/branch/branch.service';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { PunchCardReadService } from '../../../punch-card/modules/punch-card-read/punch-card-read.service';
import { ISelectionUI } from '../../types/selection-ui';
import { MenuCategoryService } from '../menu-category/menu-category.service';
import { MenuItemAvailabilityServiceInterface } from '../menu-item-availability/menu-item-availability.service.interface';
import { MenuItemNotificationService } from '../menu-item-notification/menu-item-notification.service';
import { CompanyService } from './../../../company/services/company/company.service';
import { TriggerService } from './../../../notification/services/trigger/trigger.service';
import { ImageService } from './../../../shared/services/image/image.service';
import { MenuGroupService } from './../menu-group/menu-group.service';
import { MenuService } from './../menu/menu.service';

@Injectable()
export class MenuItemService {
  private readonly logger = new LoggerService(MenuItemService.name);

  constructor(
    @InjectModel('MenuItem') private menuItemModel: Model<MenuItemDocument>,
    private menuGroupService: MenuGroupService,
    private imageService: ImageService,
    private menuItemNotificationService: MenuItemNotificationService,
    @Inject(forwardRef(() => MenuService)) private menuService: MenuService,
    private companyService: CompanyService,
    private triggerService: TriggerService,
    private eventEmitter: EventEmitter2,
    private branchService: BranchService,
    @Inject('BrandServiceInterface')
    private brandService: BrandServiceInterface,
    @Inject('MenuItemAvailabilityServiceInterface')
    private menuItemAvailabilityService: MenuItemAvailabilityServiceInterface,
    private readonly punchCardReadService: PunchCardReadService,
    private readonly menuCategoryService: MenuCategoryService,
  ) {}

  public async importMenuItems(
    importDto: ImportMenuItemsDto,
    file: Express.Multer.File,
  ) {
    this.logger.log(
      `Starting menu items import for company: ${importDto.companyId}, menu: ${importDto.menuId}`,
    );

    const { companyId, menuId } = importDto;
    const workbook = xlsx.read(file.buffer, { type: 'buffer' });
    const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
    const menuItemRows: MenuItemRow[] = xlsx.utils.sheet_to_json(firstSheet);

    this.logger.log(
      `Parsed ${menuItemRows.length} rows from uploaded Excel file`,
    );

    // Get the key mapping based on preference or custom mapping
    const keyMapping = this.getKeyMapping(importDto);

    // Use the SKU field from the mapping for filtering
    const skuField = keyMapping.plu || 'Variant SKU';
    const menuItemRowsWithSku = this.filterMenuItemsWithSku(
      menuItemRows,
      skuField,
    );

    this.logger.log(
      `Found ${menuItemRowsWithSku.length} menu items with valid SKU values`,
    );

    const menu = await this.menuService.getDetails(menuId.toString());
    const currentUser = {
      id: new Types.ObjectId(),
      name: 'Import Menu Items Excel',
      phone: 'Import Menu Items Excel',
      type: 'Import Menu Items Excel',
      role: 'Import Menu Items Excel',
    };
    const getCategory = this.createGetCategory(menu, currentUser);

    this.logger.log('Transforming menu item rows to DTOs');
    const menuItemDtos = await mapAsync(menuItemRowsWithSku, (row) =>
      this.transformMenuItemRowToCreateMenuItemDto(
        row,
        menu._id,
        companyId,
        getCategory,
        currentUser,
        keyMapping,
      ),
    );
    this.logger.log(`Transformed ${menuItemDtos.length} menu items for import`);

    const skuToIdMap = await this.getSkuToIdMap(menuId);
    this.logger.log(
      `Found ${Object.keys(skuToIdMap).length} existing menu items with SKUs`,
    );

    const bulkWriteDto = this.createMenuItemBulkWriteDto(
      menuItemDtos,
      skuToIdMap,
    );

    this.logger.log(
      `Prepared bulk write operation with ${bulkWriteDto.createMenuItemDtos?.length || 0} items to create and ${bulkWriteDto.updateMenuItemDtos?.length || 0} items to update`,
    );

    const result = await this.bulkWrite(companyId, bulkWriteDto);

    this.logger.log(
      `Successfully completed menu items import for company: ${companyId}, menu: ${menuId}`,
    );

    return result;
  }

  public async getSkuToIdMap(
    menuId: Types.ObjectId,
    menuCategory?: string,
    skus?: string[],
  ) {
    const indexResult = await this.index({
      menu: menuId.toString(),
      output: MenuItemIndexOutput.SKU,
      menuCategory,
      skus,
    });
    const existingMenuItems = indexResult[0].paginatedResult;

    const skuToId: Record<string, Types.ObjectId> = {};

    for (let i = 0; i < existingMenuItems.length; i++) {
      const { plu, _id } = existingMenuItems[i];
      skuToId[plu] = _id;
    }

    return skuToId;
  }

  public async bulkWrite(
    companyId: Types.ObjectId,
    { createMenuItemDtos, updateMenuItemDtos }: MenuItemBulkWriteDto,
  ) {
    const processedCreateMenuItemDtos = await mapAsync(
      createMenuItemDtos,
      (createMenuItemDto) => this.processMenuItemToCreate(createMenuItemDto),
    );
    const inserts = processedCreateMenuItemDtos.map((createMenuItemDto) => ({
      insertOne: { document: { ...createMenuItemDto, company: companyId } },
    }));

    const processedUpdateMenuItemDtos = await mapAsync(
      updateMenuItemDtos,
      (updateMenuItemDto) => this.processMenuItemToUpdate(updateMenuItemDto),
    );
    const updates = processedUpdateMenuItemDtos.map(({ _id, ...update }) => ({
      updateOne: {
        filter: { _id },
        update,
      },
    }));

    const result = await this.menuItemModel.bulkWrite(
      [...inserts, ...updates],
      { ordered: false },
    );

    this.logger.log(
      `Wrote ${result.insertedCount} creates and ${result.modifiedCount} updates`,
    );
    return { created: result.insertedCount, updated: result.modifiedCount };
  }

  async index(menuItemToIndex: MenuItemToIndex) {
    const aggregation = [];
    const match = { deletedAt: { $eq: null } };

    if (menuItemToIndex.menu) {
      match['menus'] = new Types.ObjectId(menuItemToIndex.menu);
    }

    if (menuItemToIndex.month) {
      match['month'] = { month: menuItemToIndex.month };
    }

    if (menuItemToIndex.sort_type) {
      aggregation.push({
        $sort: MenuItemSortMapping[menuItemToIndex.sort_type],
      });
    } else {
      aggregation.push({
        $sort: { sortOrder: 1 },
      });
    }

    const unionSearchConditions =
      this.buildUnionSearchConditions(menuItemToIndex);

    if (unionSearchConditions.length > 0) match['$and'] = unionSearchConditions;

    if (menuItemToIndex.ids) {
      const ids = menuItemToIndex.ids.split(',').map((x) => {
        return new Types.ObjectId(x);
      });
      match['_id'] = { $in: ids };
    }

    if (menuItemToIndex.menuCategory) {
      match['menuCategory'] = new Types.ObjectId(menuItemToIndex.menuCategory);
    }

    if (menuItemToIndex.type) {
      match['type'] = menuItemToIndex.type;
    }

    aggregation.push({ $match: match });

    if (menuItemToIndex.search_key) {
      aggregation.push({
        $match: this.buildSearchTypeFilter(
          menuItemToIndex.search_type,
          menuItemToIndex.search_key,
        ),
      });
    }

    if (menuItemToIndex.output === MenuItemIndexOutput.OPTIONS) {
      aggregation.push({ $project: { _id: 1, nameEn: 1 } });
    } else if (menuItemToIndex.output === MenuItemIndexOutput.SKU) {
      aggregation.push({ $project: { plu: 1 } });
    }

    aggregation.push({
      $facet: {
        paginatedResult: [
          ...(Number(menuItemToIndex.offset) ||
          Number(menuItemToIndex.offset) == 0
            ? [
                {
                  $skip:
                    Number(menuItemToIndex.offset) *
                    Number(menuItemToIndex.limit),
                },
              ]
            : [{ $skip: 0 }]),
          ...(Number(menuItemToIndex.limit)
            ? [{ $limit: Number(menuItemToIndex.limit) }]
            : []),
        ],
        totalCount: [{ $count: 'createdAt' }],
      },
    });

    return await this.menuItemModel.aggregate(aggregation);
  }

  async findByMenuId(menuId: Types.ObjectId): Promise<MenuItemDocument[]> {
    return await this.menuItemModel.find({
      menus: {
        $elemMatch: { $eq: new Types.ObjectId(menuId) },
      },
      deletedAt: null,
    });
  }

  async getDetails(id: string, menuId?: string, companyId?: Types.ObjectId) {
    const filters = {
      $or: [{ plu: id }, { deliverectId: id }, { code: id }],
      deletedAt: null,
    } as any;
    if (isValidObjectId(id)) {
      filters.$or.push({ _id: id });
    }
    if (companyId) {
      filters['company'] = companyId;
    }
    if (menuId) {
      filters['menus'] = {
        $elemMatch: { $eq: new Types.ObjectId(menuId) },
      };
    }
    return await this.menuItemModel
      .findOne(filters)
      .populate({
        path: 'subItems',
        model: 'MenuItem',
        match: { deletedAt: null },
        populate: [
          { path: 'menuGroups', model: 'MenuGroup' },
          {
            path: 'subItems',
            model: 'MenuItem',
            match: { deletedAt: null },
            populate: [{ path: 'menuGroups', model: 'MenuGroup' }],
          },
        ],
      })
      .populate({ path: 'menuGroups', model: 'MenuGroup' })
      .populate({ path: 'menuCategory', model: 'MenuCategory' })
      .populate({ path: 'company', model: CollectionName.COMPANY })
      .exec();
  }

  async updateCategoryIdForManyDeliverect(
    productIds: string[],
    categoryId: Types.ObjectId,
    menuId: Types.ObjectId,
  ) {
    await this.menuItemModel.updateMany(
      {
        deliverectId: { $in: productIds },
        menus: { $elemMatch: { $eq: menuId } },
      },
      { $set: { menuCategory: categoryId } },
    );
  }

  async getDetailsForFrontEnd(
    id: string,
    menuId?: string,
    companyId?: Types.ObjectId,
  ) {
    const filters = {
      $or: [{ plu: id }, { deliverectId: id }, { code: id }],
      deletedAt: null,
    } as any;
    if (isValidObjectId(id)) {
      filters.$or.push({ _id: id });
    }
    if (companyId) {
      filters['company'] = companyId;
    }
    if (menuId) {
      filters['menus'] = {
        $elemMatch: { $eq: new Types.ObjectId(menuId) },
      };
    }
    const menuItem = await this.menuItemModel
      .findOne(filters)
      .populate({
        path: 'subItems',
        model: 'MenuItem',
        match: { deletedAt: null },
        populate: [
          { path: 'menuGroups', model: 'MenuGroup' },
          {
            path: 'subItems',
            model: 'MenuItem',
            match: { deletedAt: null },
            populate: [{ path: 'menuGroups', model: 'MenuGroup' }],
          },
        ],
      })
      .populate({ path: 'menuGroups', model: 'MenuGroup' })
      .populate({ path: 'menuCategory', model: 'MenuCategory' })
      .populate({
        path: 'company',
        model: CollectionName.COMPANY,
        select: '-deliverectIntegratedChannels',
      });

    const menuItemObject = menuItem.toObject();

    menuItemObject['availabilities'] =
      await this.menuItemAvailabilityService.index({
        startTime: undefined,
        endTime: undefined,
        menuItemId: menuItem._id,
        type: undefined,
      });
    menuItemObject['availabilityStep'] = 5;

    //Just A dummy thing to help the frontend to accomplish the task
    // adding this property just to avoid populate the similarMenuItems in get all menuItems
    // And this will be used in the frontend side to display a preSelected list that can be
    // use to show the user what is the similar item for this item
    menuItemObject['selectionListUI'] = await this.menuItemModel
      .find(
        {
          _id: { $in: menuItem.similarMenuItems ?? [] },
          deletedAt: null,
        },
        { _id: 1, nameEn: 1, nameAr: 1 },
      )
      .populate('menus', 'name');
    menuItemObject['selectionListUI'] = this.mapMenusArrayToMenuObject(
      menuItemObject['selectionListUI'],
    );
    return menuItemObject;
  }

  getImageUrl(menuItem: MenuItemDocument): string {
    const image = menuItem.images?.find(
      (image) => !/\s/g.test(image.name) && !/\s/g.test(image.for),
    );
    if (!image) return menuItem.externalImage;
    return image.url;
  }

  async getMenuItemWithBranchAndBrandDetails(id: string) {
    const menuItem = await this.getDetails(id, undefined);

    let branch, brand;
    if (menuItem) {
      const menu = await this.menuService.getDetails(
        menuItem.menus[0] ? menuItem.menus[0].toHexString() : undefined,
      );
      if (menu) {
        branch = menu.branches[0]
          ? await this.branchService.get_details(menu.branches[0].toHexString())
          : undefined;
        brand = menu.brand
          ? await this.brandService.findById(menu.brand._id)
          : undefined;
      }
    }
    return { menuItem, branch, brand };
  }

  async create(menuItemToCreate: MenuItemToCreate) {
    menuItemToCreate = await this.processMenuItemToCreate(menuItemToCreate);
    const company = await this.companyService.get_details(
      menuItemToCreate['company']?.toString(),
    );
    menuItemToCreate['company'] = new Types.ObjectId(company._id) as any;

    const menuItem = new this.menuItemModel(menuItemToCreate);
    const createdMenuItem = await menuItem.save();

    if (
      company.deliverectAccountId &&
      company.deliverectPOSClientId &&
      company.deliverectPOSSecret
    ) {
      this.eventEmitter.emit('menuItem.updatedOrCreated', menuItem._id);
    }
    if (menuItemToCreate.availabilities)
      await this.handleMenuItemsAvailabilitiesOnMenuItemCreated(
        menuItemToCreate.availabilities,
        createdMenuItem._id,
      );

    return menuItem;
  }

  async update(menuItemToUpdate: MenuItemToUpdate) {
    const menuItem = await this.menuItemModel.findOne({
      _id: menuItemToUpdate._id,
    });
    if (menuItemToUpdate.parentMenuItemId)
      await this.addSlaveMenuItemToMaster(
        menuItemToUpdate.parentMenuItemId,
        menuItem,
      );

    menuItemToUpdate = await this.processMenuItemToUpdate(menuItemToUpdate);
    await this.menuItemModel.updateOne({ _id: menuItem._id }, menuItemToUpdate);
    const updatedMenuItem = await menuItem.save();

    const company = await this.companyService.get_details(
      menuItem.company.toHexString(),
    );

    if (
      company.deliverectAccountId &&
      company.deliverectPOSClientId &&
      company.deliverectPOSSecret
    ) {
      this.eventEmitter.emit('menuItem.updatedOrCreated', menuItem._id);
    }
    if (menuItemToUpdate.availabilities)
      await this.handleMenuItemsAvailabilitiesOnMenuItemUpdated(
        menuItemToUpdate.availabilities,
        updatedMenuItem._id,
      );

    return updatedMenuItem;
  }

  async remove(menuItemToRemove: MenuItemToRemove) {
    const menuItem = await this.menuItemModel.findOne({
      _id: menuItemToRemove._id,
      deletedAt: null,
    });
    menuItem.deletedAt = moment.utc().toDate();
    menuItem.deletedBy = menuItemToRemove.deletedBy;
    await menuItem.save();
    return menuItem;
  }

  async getTags() {
    const tags = [
      { label: 'celery', value: 'celery' },
      { label: 'gluten', value: 'gluten' },
      { label: 'crustaceans', value: 'crustaceans' },
      { label: 'fish', value: 'fish' },
      { label: 'eggs', value: 'eggs' },
      { label: 'lupin', value: 'lupin' },
      { label: 'milk', value: 'milk' },
      { label: 'molluscs', value: 'molluscs' },
      { label: 'mustard', value: 'mustard' },
      { label: 'nuts', value: 'nuts' },
      { label: 'peanuts', value: 'peanuts' },
      { label: 'sesame', value: 'sesame' },
      { label: 'soya', value: 'soya' },
      { label: 'sulphites', value: 'sulphites' },
      { label: 'almonds', value: 'almonds' },
      { label: 'barley', value: 'barley' },
      { label: 'brazil nuts', value: 'brazil nuts' },
      { label: 'cashew', value: 'cashew' },
      { label: 'hazelnuts', value: 'hazelnuts' },
      { label: 'kamut', value: 'kamut' },
      { label: 'macadamia', value: 'macadamia' },
      { label: 'oats', value: 'oats' },
      { label: 'pecan', value: 'pecan' },
      { label: 'pistachios', value: 'pistachios' },
      { label: 'rye', value: 'rye' },
      { label: 'spelt', value: 'spelt' },
      { label: 'walnuts', value: 'walnuts' },
      { label: 'wheat', value: 'wheat' },
      { label: 'alcohol', value: 'alcohol' },
      { label: 'halal', value: 'halal' },
      { label: 'kosher', value: 'kosher' },
      { label: 'vegan', value: 'vegan' },
      { label: 'vegetarian', value: 'vegetarian' },
    ];
    return tags;
  }

  async deleteItemInsideMenu(menu: MenuWithId) {
    const menuItems = await this.menuItemModel.find({ menus: [menu._id] });
    let menuGroups: Types.ObjectId[] = [];
    const itemsIds: Types.ObjectId[] = [];

    menuItems.map((x) => {
      itemsIds.push(x._id);
      menuGroups = menuGroups.concat(x.menuGroups);
    });
    await this.menuItemModel.deleteMany({ _id: { $in: menuItems } });
    await this.menuGroupService.removeMany(menuGroups);
  }

  async promoteMenuItem(menuItemToPromote: MenuItemToPromote) {
    const menuItem = await this.getDetails(
      menuItemToPromote.menuItemId.toString(),
    );
    if (!menuItem)
      throw new BadRequestException(
        'Please Provide a Correct menu Item id',
        responseCode.MISSING_DATA.toString(),
      );

    const genericTriggerModel =
      await this.menuItemNotificationService.populateGenericTriggerModel(
        menuItem,
        menuItemToPromote,
      );
    return await this.triggerService.fireTrigger(
      genericTriggerModel,
      TriggerAction.ON_MENU_ITEM_PROMOTED,
    );
  }

  async checkPluExistence(plug: string, companyId: Types.ObjectId) {
    const menuItem = await this.menuItemModel.findOne({
      plu: plug,
      company: new Types.ObjectId(companyId),
      deletedAt: null,
    });

    if (menuItem) {
      return true;
    } else {
      return false;
    }
  }

  public async findManyById(
    menuItemIds: Types.ObjectId[],
  ): Promise<MenuItemDocument[]> {
    return await this.menuItemModel.find({ _id: { $in: menuItemIds } });
  }

  async findMenuItemForBranch(
    menuItemIds: Types.ObjectId[],
    branchId: Types.ObjectId,
  ): Promise<MenuItemDocument | undefined> {
    type MenuItemWithPopulatedMenus = Omit<MenuItemDocument, 'menus'> & {
      menus: Pick<MenuWithId, '_id' | 'branches' | 'deletedAt'>[];
    };

    const menuItems: MenuItemWithPopulatedMenus[] = await this.menuItemModel
      .find({
        _id: { $in: menuItemIds },
        deletedAt: null,
      })
      .populate('menus', '_id branches deletedAt');

    const menuItemForBranch = menuItems.find((menuItem) =>
      menuItem.menus.some(
        (menu) => menu.branches.includes(branchId) && menu.deletedAt == null,
      ),
    );

    const unpopulateMenuItem = (
      populatedMenuItem: MenuItemWithPopulatedMenus,
    ): MenuItemDocument => {
      populatedMenuItem.menus = populatedMenuItem.menus.map(
        (menu) => menu._id,
      ) as any;
      return populatedMenuItem as any;
    };

    return menuItemForBranch !== undefined
      ? unpopulateMenuItem(menuItemForBranch)
      : undefined;
  }

  async updatePlu(menuItemId: string, plu: string) {
    await this.menuItemModel.updateOne(
      { _id: new Types.ObjectId(menuItemId) },
      { $set: { plu } },
    );
  }

  async convertCapturedItem({
    _id,
    quantity,
  }: CapturedItem): Promise<OrderItem> {
    const item = await this.menuItemModel.findById(_id);
    if (!item) {
      const punchCard = await this.punchCardReadService.findById(_id);
      if (!punchCard)
        throw new NotFoundException(`Item with ID ${_id} not found`);

      const punchCardOrderItem: OrderItem = {
        itemReference: _id.toString(),
        plu: _id.toString(),
        name: 'Punch',
        quantity,
        basePrice: 0,
        price: 0,
        totalAmountAfterDiscount: 0,
        discount: 0,
        microsMenuItemId: item?.microsMenuItemId,
        totalAmount: 0,
      };
      return plainToInstance(OrderItem, punchCardOrderItem);
    }
    return {
      name: item.nameEn,
      description: item.descriptionEn,
      plu: item.plu,
      basePrice: item.price,
      itemReference: item._id.toString(),
      price: item.price,
      totalAmountAfterDiscount: item.price * quantity,
      special_instructions: '',
      quantity,
      discount: 0,
      modifierGroups: [],
      subProducts: [],
      totalAmount: item.price * quantity,
      adlerId: item.adlerId,
      brandId: item.externalBrandId,
      pickup_spot_id: '',
      microsMenuItemId: item?.microsMenuItemId,
    };
  }

  @OnEvent('cron.every5mins')
  @LogError()
  async activeMenuItems() {
    const currentTime = moment().utc().format('HH:mm');
    const menuItemAvailability = await this.menuItemAvailabilityService.index({
      startTime: undefined,
      endTime: currentTime,
      menuItemId: undefined,
      type: undefined,
    });
    if (menuItemAvailability) {
      for (let j = 0; j < menuItemAvailability.length; j++) {
        await this.menuItemModel.updateMany(
          {
            _id: { $in: menuItemAvailability[j].menuItemIds },
            isScheduledAvailabilityActive: true,
          },
          {
            available:
              menuItemAvailability[j].type == 'AVAILABLE' ? false : true,
          },
        );
      }
    }
  }

  @OnEvent('cron.every5mins')
  @LogError()
  async deActiveMenuItems() {
    const currentTime = moment().utc().format('HH:mm');
    const menuItemAvailabilities = await this.menuItemAvailabilityService.index(
      {
        startTime: currentTime,
        endTime: undefined,
        menuItemId: undefined,
        type: undefined,
      },
    );
    if (menuItemAvailabilities) {
      for (let i = 0; i < menuItemAvailabilities.length; i++) {
        await this.menuItemModel.updateMany(
          {
            _id: { $in: menuItemAvailabilities[i].menuItemIds },
            isScheduledAvailabilityActive: true,
          },
          {
            available:
              menuItemAvailabilities[i].type == 'AVAILABLE' ? true : false,
          },
        );
      }
    }
  }

  async findByMicrosMenuItemIdAndCompanyId(
    microsMenuItemId: number,
    companyId: Types.ObjectId,
  ) {
    return await this.menuItemModel.findOne({
      microsMenuItemId,
      company: companyId,
    });
  }

  private createMenuItemBulkWriteDto(
    menuItemDtos: MenuItemToCreate[],
    skuToIdMap: Record<string, Types.ObjectId>,
  ) {
    const createMenuItemDtos: MenuItemToCreate[] = [],
      updateMenuItemDtos: MenuItemToUpdate[] = [];

    for (let i = 0; i < menuItemDtos.length; i++) {
      const dto = menuItemDtos[i];
      if (dto.plu in skuToIdMap) {
        updateMenuItemDtos.push({
          ...dto,
          _id: skuToIdMap[dto.plu],
          menuGroups: [],
          images: [],
          updatedBy: dto.createdBy,
        });
      } else if (dto.plu) {
        createMenuItemDtos.push(dto);
      }
    }

    this.logger.log(
      `Writing ${createMenuItemDtos.length} creates and ${updateMenuItemDtos.length} updates`,
    );

    return { createMenuItemDtos, updateMenuItemDtos };
  }

  private getKeyMapping(importDto: ImportMenuItemsDto): Record<string, string> {
    // If custom keyMapping is provided, use it
    if (importDto.keyMapping) {
      return importDto.keyMapping;
    }

    // If preference is provided, use the predefined mapping
    if (
      importDto.preference &&
      importDto.preference in MENU_ITEM_IMPORT_MAPPINGS
    ) {
      return MENU_ITEM_IMPORT_MAPPINGS[importDto.preference];
    }

    // Default to the current mapping
    return MENU_ITEM_IMPORT_MAPPINGS.Default;
  }

  private filterMenuItemsWithSku(
    menuItemRows: MenuItemRow[],
    skuField: string = 'Variant SKU',
  ): MenuItemRow[] {
    const menuItemRowsWithSku = [];

    for (let i = 0; i < menuItemRows.length; i++) {
      const row = menuItemRows[i];
      if (!row[skuField]) continue;
      row[skuField] = row[skuField].toString().replace("'", '');
      menuItemRowsWithSku.push(row);
    }

    return menuItemRowsWithSku;
  }

  private createGetCategory(menu: MenuWithId, currentUser: CurrentUser) {
    const categoryCache = new Map<string, Promise<MenuCategoryWithId>>();
    const getCategory = async (
      categoryName: string,
    ): Promise<MenuCategoryWithId> => {
      if (!categoryCache.has(categoryName)) {
        categoryCache.set(
          categoryName,
          this.menuCategoryService.findOrCreateWithCache(
            categoryName,
            menu._id,
            menu.name,
            currentUser,
          ),
        );
      }
      return categoryCache.get(categoryName);
    };
    return getCategory;
  }

  private async transformMenuItemRowToCreateMenuItemDto(
    row: MenuItemRow,
    menuId: Types.ObjectId,
    companyId: Types.ObjectId,
    getCategory: (categoryName: string) => Promise<MenuCategoryWithId>,
    currentUser: CurrentUser,
    keyMapping: Record<string, string> = MENU_ITEM_IMPORT_MAPPINGS.Default,
  ): Promise<MenuItemToCreate> {
    // Get field values using the mapping
    const nameField = keyMapping.nameEn || 'Title';
    const descriptionField = keyMapping.descriptionEn || 'Body (HTML)';
    const codeField = keyMapping.code || 'Variant SKU';
    const priceField = keyMapping.price || 'Variant Price';
    const tagsField = keyMapping.tags || 'Tags';
    const imageField = keyMapping.externalImage || 'Image Src';
    const categoryField = keyMapping.menuCategory || 'Product Category';

    const description = row[descriptionField]
      ? row[descriptionField].replaceAll(/<[^>]+>/g, '')
      : '';
    const category = row[categoryField]
      ? row[categoryField].split('>').reverse()[0].trim()
      : null;

    return {
      nameAr: row[nameField],
      nameEn: row[nameField],
      descriptionEn: description,
      descriptionAr: description,
      code: row[codeField],
      menuCategory: category ? (await getCategory(category))._id : null,
      plu: row[codeField],
      reference: row[codeField],
      price: row[priceField],
      available: true,
      showOnWeb: true,
      showInPos: true,
      calories: 0,
      tags: row[tagsField] ? row[tagsField].split(',') : [],
      type: 'regular',
      menu: menuId,
      menuGroups: [],
      subItems: [],
      images: [],
      externalImage: row[imageField]?.includes(',')
        ? row[imageField].split(',')[0]
        : row[imageField],
      company: companyId,
      createdBy: currentUser,
      availabilities: [],
      isScheduledAvailabilityActive: false,
    };
  }

  private buildSearchTypeFilter(
    search_type: MenuItemSearchType,
    search_key: string,
  ) {
    const regex = { $regex: search_key, $options: 'i' };

    switch (search_type) {
      case MenuItemSearchType.NAME:
        return { $or: [{ nameEn: regex, nameAr: regex }] };
      case MenuItemSearchType.TYPE:
        return { $or: [{ type: regex }] };
      case MenuItemSearchType.PRICE:
        return { $or: [{ price: regex }] };
      case MenuItemSearchType.ALL:
      default:
        return {
          $or: [
            { nameEn: regex },
            { nameAr: regex },
            { nameEn: regex },
            { descriptionEn: regex },
            { descriptionAr: regex },
            { type: regex },
          ],
        };
    }
  }

  private buildUnionSearchConditions(menuItemToIndex: MenuItemToIndex) {
    const searchConditions = [];

    if (menuItemToIndex.name) {
      let preprocessedQuery = menuItemToIndex.name;

      preprocessedQuery = preprocessedQuery.replace(/%25/g, '%20');

      preprocessedQuery = preprocessedQuery.replace(
        /%(?![0-9A-Fa-f]{2})/g,
        '%20',
      );

      let decodedSearchQuery;
      try {
        decodedSearchQuery = decodeURIComponent(preprocessedQuery);
      } catch (error) {
        decodedSearchQuery = preprocessedQuery;
      }

      const searchTerms = decodedSearchQuery.split(/\s+/).filter(Boolean);

      const nameRegex = new RegExp(
        searchTerms
          .map((term) => term.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'))
          .join('.*'),
        'i',
      );

      searchConditions.push({
        $or: [
          { nameEn: { $regex: nameRegex } },
          { nameAr: { $regex: nameRegex } },
        ],
      });
    }

    if (menuItemToIndex.sku) {
      searchConditions.push({
        $or: [
          { plu: menuItemToIndex.sku },
          { code: menuItemToIndex.sku },
          { reference: menuItemToIndex.sku },
        ],
      });
    }

    if (menuItemToIndex.price) {
      searchConditions.push({ price: menuItemToIndex.price });
    }

    return searchConditions;
  }

  private mapMenusArrayToMenuObject(selectionListUI: ISelectionUI[]) {
    return selectionListUI.map(
      (item) =>
        <ISelectionUI>{
          _id: item._id,
          nameEn: item.nameEn,
          menu: item.menus[0],
          menus: undefined,
        },
    );
  }

  private async processMenuItemToCreate(
    menuItemToCreate: MenuItemToCreate,
  ): Promise<MenuItemToCreate> {
    if (!menuItemToCreate.plu && !menuItemToCreate.code) {
      menuItemToCreate.plu = randomString.generate(10);
    } else if (!menuItemToCreate.plu && menuItemToCreate.code) {
      menuItemToCreate.plu = menuItemToCreate.code;
    }
    // MenuGroups
    const groups = [];
    for (let i = 0; i < menuItemToCreate.menuGroups.length; i++) {
      const group = await this.menuGroupService.create(
        menuItemToCreate.menuGroups[i],
      );
      groups.push(group._id);
    }
    menuItemToCreate.menuGroups = groups as any;

    //images
    let images = [];
    for (let i = 0; i < menuItemToCreate.images.length; i++) {
      const image = await this.imageService.uploadImage({
        ...menuItemToCreate.images[i],
        name: menuItemToCreate.nameEn.replace(/\s/g, ''),
        for: 'menuItem',
      });
      images.push(image);
    }
    if (menuItemToCreate.readyToUseImages) {
      images = menuItemToCreate.readyToUseImages;
    }
    menuItemToCreate.images = images as any;

    const subItems = [];
    for (let i = 0; i < menuItemToCreate.subItems.length; i++) {
      subItems.push(new Types.ObjectId(menuItemToCreate.subItems[i]));
    }
    menuItemToCreate.subItems = subItems;

    menuItemToCreate['menus'] = [new Types.ObjectId(menuItemToCreate.menu)];

    if (menuItemToCreate.menuCategory) {
      menuItemToCreate['menuCategory'] = new Types.ObjectId(
        menuItemToCreate.menuCategory,
      ) as any;
    }

    if (menuItemToCreate['company']) {
      menuItemToCreate['company'] = new Types.ObjectId(
        menuItemToCreate['company'],
      ) as any;
    }

    return menuItemToCreate;
  }

  private async processMenuItemToUpdate(
    menuItemToUpdate: MenuItemToUpdate,
  ): Promise<MenuItemToUpdate> {
    if (menuItemToUpdate.menuGroups) {
      const menuGroups = [];

      for (let i = 0; i < menuItemToUpdate.menuGroups.length; i++) {
        const menuGroup = menuItemToUpdate.menuGroups[i];

        if (typeof menuGroup === 'string')
          menuGroups.push(new Types.ObjectId(menuGroup));
        else if ('_id' in menuGroup) {
          const updatedMenuGroup =
            await this.menuGroupService.update(menuGroup);
          menuGroups.push(updatedMenuGroup._id);
        } else {
          const createdMenuGroup =
            await this.menuGroupService.create(menuGroup);
          menuGroups.push(createdMenuGroup._id);
        }
      }

      menuItemToUpdate.menuGroups = menuGroups;
    }

    if (menuItemToUpdate.subItems) {
      const subItems = [];
      for (let i = 0; i < menuItemToUpdate.subItems.length; i++) {
        subItems.push(new Types.ObjectId(menuItemToUpdate.subItems[i]));
      }
      menuItemToUpdate.subItems = subItems;
    }

    if (menuItemToUpdate.images) {
      const images = [];
      for (let i = 0; i < menuItemToUpdate.images.length; i++) {
        const currentImage = menuItemToUpdate.images[i];
        if (currentImage.base64) {
          const image = await this.imageService.uploadImage({
            alt: currentImage.alt,
            name: menuItemToUpdate.nameEn.replace(/\s/g, ''),
            description: currentImage.description,
            base64: currentImage.base64,
            for: 'menuItem',
          });
          images.push(image);
        } else {
          images.push(currentImage);
        }
      }
      menuItemToUpdate.images = images;
    }

    if (menuItemToUpdate.menuCategory) {
      menuItemToUpdate['menuCategory'] = new Types.ObjectId(
        menuItemToUpdate.menuCategory,
      ) as any;
    }

    if (!menuItemToUpdate.plu && !menuItemToUpdate.code) {
      menuItemToUpdate.plu = randomString.generate(10);
    } else if (!menuItemToUpdate.plu && menuItemToUpdate.code) {
      menuItemToUpdate.plu = menuItemToUpdate.code;
    }

    if (menuItemToUpdate['company']) {
      menuItemToUpdate['company'] = new Types.ObjectId(
        menuItemToUpdate['company'],
      ) as any;
    }

    return menuItemToUpdate;
  }

  private async addSlaveMenuItemToMaster(
    parentMenuItemId: Types.ObjectId,
    menuItem: MenuItemDocument,
  ): Promise<void> {
    const updatedMenuItem = await this.menuItemModel.updateOne(
      { _id: parentMenuItemId },
      { $addToSet: { similarMenuItems: menuItem._id } },
    );
    if (updatedMenuItem.modifiedCount === 0) {
      throw new NotFoundException(
        `Parent menu item with ID ${parentMenuItemId} not found`,
      );
    }
  }

  private async handleMenuItemsAvailabilitiesOnMenuItemCreated(
    availabilities: MenuItemAvailabilityDto[],
    menuItemId: Types.ObjectId,
  ) {
    for (let i = 0; i < availabilities.length; i++) {
      await this.menuItemAvailabilityService.createOrUpdate({
        availability: availabilities[i],
        menuItemId: menuItemId,
      });
    }
  }

  private async handleMenuItemsAvailabilitiesOnMenuItemUpdated(
    availabilities: MenuItemAvailabilityDto[],
    menuItemId: Types.ObjectId,
  ) {
    for (let i = 0; i < availabilities.length; i++) {
      if (availabilities[i].action == MenuItemAvailabilityAction.CREATION)
        await this.menuItemAvailabilityService.createOrUpdate({
          availability: availabilities[i],
          menuItemId: menuItemId,
        });
      else if (
        availabilities[i].action == MenuItemAvailabilityAction.DELETION
      ) {
        await this.menuItemAvailabilityService.handleDeletedMenuItems(
          availabilities[i],
          menuItemId,
        );
      }
    }
  }
}
