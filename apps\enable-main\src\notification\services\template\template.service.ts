import {
  ContactChannel,
  CustomerAgeGroup,
  CustomerTitle,
  Datatype,
  DeliveryOrderStatus,
  ListConditionsDto,
  ListConditionsResponse,
  ListRolesDto,
  LRPSource,
  MicroserviceCommunicationService,
  NoTier,
  Operator,
  OrderDeliveryAction,
  OrderPaymentMethod,
  OrderSource,
  OrderStatusEnum,
  Reference,
  SyncTemplateWhatsappDto,
  TemplateExistsDto,
  TemplateFilterEntity,
  TemplateTo,
  TemplateType,
  TierStatus,
  TypedOption,
  Variable,
  VariableName,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { BranchService } from '../../../branch/services/branch/branch.service';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { CompanyService } from '../../../company/services/company/company.service';
import { LoyaltyTierReadServiceInterface } from '../../../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.service.interface';
import { TriggerService } from '../trigger/trigger.service';

@Injectable()
export class TemplateService {
  constructor(
    @Inject('BrandServiceInterface')
    private brandService: BrandServiceInterface,
    private readonly branchService: BranchService,
    private readonly companyService: CompanyService,
    private readonly triggerService: TriggerService,
    @Inject(LoyaltyTierReadServiceInterface)
    private readonly loyaltyTierService: LoyaltyTierReadServiceInterface,
    @Inject('enable-main-notification-producer') private client: ClientProxy,
    private microserviceCommunicationService: MicroserviceCommunicationService,
  ) {}

  async onModuleDestroy() {
    await this.microserviceCommunicationService.disconnect(this.client);
  }

  async onModuleInit() {
    await this.microserviceCommunicationService.connect(this.client);
  }

  async checkTemplateExists(
    templateExistsDto: TemplateExistsDto,
  ): Promise<boolean> {
    return this.microserviceCommunicationService.produceAndWaitForResponse(
      this.client,
      'exists.template.request',
      templateExistsDto,
    );
  }

  async listRoles(listRolesDto: ListRolesDto): Promise<string[]> {
    const giftRecipientTriggers: string[] = [
      '[ORDER] ON_SEND_DELIVERY_LOCATION_SMS_GIFT',
      '[ORDER] ON_SEND_DELIVERY_LOCATION_SMS',
      '[ORDER] ON_DELIVERY_LOCATION_AND_PAYMENT_REQUEST_SMS',
      '[ORDER] ON_DELIVERY_DELAY',
      '[ORDER] THREE_HOURS_AFTER_ORDER_COMPLETION',
      '[ORDER] ON_ORDER_IN_ROUTE',
      '[ORDER] ON_ORDER_COMPLETED',
      '[ORDER] ON_GIFT_ORDER_COMPLETED',
    ];
    if (listRolesDto.type === TemplateType.PUSH_NOTIFICATION) {
      const roles = Object.keys(TemplateTo);
      if (listRolesDto.triggerId) {
        const trigger = await this.triggerService.findById(
          listRolesDto.triggerId,
        );
        if (!giftRecipientTriggers.includes(trigger.name))
          return roles.filter(
            (key) =>
              key !==
                Object.keys(TemplateTo).at(
                  Object.values(TemplateTo).findIndex(
                    (val) => val === TemplateTo.CUSTOM,
                  ),
                ) &&
              key !==
                Object.keys(TemplateTo).at(
                  Object.values(TemplateTo).findIndex(
                    (val) => val === TemplateTo.GIFT_RECIPIENT,
                  ),
                ),
          );
      }
      return roles.filter(
        (key) =>
          key !==
          Object.keys(TemplateTo).at(
            Object.values(TemplateTo).findIndex(
              (val) => val === TemplateTo.CUSTOM,
            ),
          ),
      );
    } else if (listRolesDto.type === TemplateType.CHAT) {
      if (listRolesDto.triggerId) {
        const trigger = await this.triggerService.findById(
          listRolesDto.triggerId,
        );
        if (!giftRecipientTriggers.includes(trigger.name))
          return [
            Object.keys(TemplateTo).at(
              Object.values(TemplateTo).findIndex(
                (val) => val === TemplateTo.CUSTOMER,
              ),
            ),
          ];
      }
      return [
        Object.keys(TemplateTo).at(
          Object.values(TemplateTo).findIndex(
            (val) => val === TemplateTo.CUSTOMER,
          ),
        ),
        Object.keys(TemplateTo).at(
          Object.values(TemplateTo).findIndex(
            (val) => val === TemplateTo.GIFT_RECIPIENT,
          ),
        ),
      ];
    } else {
      if (listRolesDto.triggerId) {
        const trigger = await this.triggerService.findById(
          listRolesDto.triggerId,
        );
        if (!giftRecipientTriggers.includes(trigger.name))
          return Object.keys(TemplateTo).filter(
            (key) =>
              key !==
              Object.keys(TemplateTo).at(
                Object.values(TemplateTo).findIndex(
                  (val) => val === TemplateTo.GIFT_RECIPIENT,
                ),
              ),
          );
      }
    }

    return Object.keys(TemplateTo);
  }

  async assignBrandOwner(syncWhatsAppTemplate: SyncTemplateWhatsappDto) {
    const brand = await this.brandService.findById(
      syncWhatsAppTemplate.brandId,
    );
    syncWhatsAppTemplate.owner = {
      _id: brand._id,
      name: brand.name,
      smsSenderId: brand.senderId,
      emailSenderId: brand.emailSenderId,
    };
  }

  async listConditions({
    companyId,
    brandId,
  }: ListConditionsDto): Promise<ListConditionsResponse> {
    const company = await this.companyService.findById(companyId);
    const loyaltyTiers =
      await this.loyaltyTierService.findByCompanyId(companyId);
    const branches = await this.branchService.index({
      company: companyId.toHexString(),
      brandId,
      limit: 999999,
    });
    return {
      [TemplateFilterEntity.CUSTOMER]: [
        {
          variable: VariableName.NUMBER_OF_ORDERS,
          datatype: Datatype.NUMBER,
          operators: this.getOperatorOptions(Datatype.NUMBER),
          allowCustom: true,
        },
        {
          variable: VariableName.LAST_ORDER_DAY,
          datatype: Datatype.DATE,
          operators: this.getOperatorOptions(Datatype.DATE),
          allowCustom: true,
          references: this.getReferences(Datatype.DATE),
        },
        {
          variable: VariableName.ACQUISITION_DAY,
          datatype: Datatype.DATE,
          operators: this.getOperatorOptions(Datatype.DATE),
          allowCustom: true,
          references: this.getReferences(Datatype.DATE),
        },
        {
          variable: VariableName.LAST_AGGREGATOR_ORDER,
          datatype: Datatype.DATE,
          operators: this.getOperatorOptions(Datatype.DATE),
          allowCustom: true,
          references: this.getReferences(Datatype.DATE),
        },
        {
          variable: VariableName.TIER_STATUS,
          datatype: Datatype.STRING,
          operators: this.getOperatorOptions(Datatype.STRING),
          allowCustom: false,
          references: [
            {
              label: 'No Tier',
              value: TierStatus.NO_TIER,
              datatype: Datatype.STRING,
            },
            {
              label: 'Maintained',
              value: TierStatus.MAINTAINED,
              datatype: Datatype.STRING,
            },
            {
              label: 'Unmaintained',
              value: TierStatus.NOT_MAINTAINED,
              datatype: Datatype.STRING,
            },
            {
              label: 'Upgraded',
              value: TierStatus.UPGRADED,
              datatype: Datatype.STRING,
            },
          ],
        },
        {
          variable: VariableName.LOYALTY_ORDERS_RATE,
          datatype: Datatype.NUMBER,
          operators: this.getOperatorOptions(Datatype.NUMBER),
          allowCustom: true,
        },
        {
          variable: VariableName.LOYALTY_AMOUNT_SPENT,
          datatype: Datatype.NUMBER,
          operators: this.getOperatorOptions(Datatype.NUMBER),
          allowCustom: true,
        },
        {
          variable: VariableName.LOYALTY_POINTS_BALANCE,
          datatype: Datatype.NUMBER,
          operators: this.getOperatorOptions(Datatype.NUMBER),
          allowCustom: true,
        },
        ...this.createBooleanVariable(
          VariableName.IS_LOYALTY_MEMBER,
          VariableName.IS_IN_VIP_TIER,
          VariableName.LOYALTY_CARD_ADDED,
          VariableName.HAS_COMPLETE_LOCATION,
          VariableName.HAS_PUNCH_CARD_REWARDS,
          VariableName.NO_TIER_FOR_ONE_CYCLE,
          VariableName.NO_TIER_FOR_MULTIPLE_CYCLES,
          VariableName.WAS_AUTOMATICALLY_REGISTERED,
        ),
        {
          variable: VariableName.DEVICE_OS,
          datatype: Datatype.STRING,
          operators: this.getOperatorOptions(Datatype.STRING),
          allowCustom: false,
          references: [
            {
              label: 'iOS',
              value: 'iOS',
              datatype: Datatype.STRING,
            },
            {
              label: 'Android',
              value: 'Android',
              datatype: Datatype.STRING,
            },
          ],
        },
        {
          variable: VariableName.LOYALTY_TIER,
          datatype: Datatype.STRING,
          operators: this.getOperatorOptions(Datatype.STRING),
          allowCustom: false,
          references: [
            {
              label: NoTier.getName(company),
              value: NoTier._id,
              datatype: Datatype.STRING,
            },
            ...loyaltyTiers.map((loyaltyTier) => ({
              label: loyaltyTier.nameEn,
              value: loyaltyTier._id.toHexString(),
              datatype: Datatype.STRING,
            })),
          ],
        },
        {
          variable: VariableName.LAST_ORDER_CHANNEL,
          datatype: Datatype.STRING,
          operators: this.getOperatorOptions(Datatype.STRING),
          allowCustom: false,
          references: Object.entries(ContactChannel).map(([key, value]) => ({
            label: key,
            value,
            datatype: Datatype.STRING,
          })),
        },
        {
          variable: VariableName.ACQUISITION_CHANNEL,
          datatype: Datatype.STRING,
          operators: this.getOperatorOptions(Datatype.STRING),
          allowCustom: false,
          references: Object.entries(ContactChannel).map(([key, value]) => ({
            label: key,
            value,
            datatype: Datatype.STRING,
          })),
        },
        {
          variable: VariableName.LOYALTY_REGISTRATION_BRANCH,
          datatype: Datatype.STRING,
          operators: this.getOperatorOptions(Datatype.STRING),
          allowCustom: false,
          references: branches.map((branch) => ({
            label: branch.name,
            value: branch._id.toHexString(),
            datatype: Datatype.STRING,
          })),
        },
        {
          variable: VariableName.TITLE,
          allowCustom: false,
          datatype: Datatype.STRING,
          operators: this.getOperatorOptions(Datatype.STRING),
          references: [
            {
              label: 'Mr',
              value: CustomerTitle.MR,
              datatype: Datatype.STRING,
            },
            {
              label: 'Ms',
              value: CustomerTitle.MS,
              datatype: Datatype.STRING,
            },
            {
              label: 'Mrs',
              value: CustomerTitle.MRS,
              datatype: Datatype.STRING,
            },
          ],
        },
        {
          variable: VariableName.AGE_GROUP,
          allowCustom: false,
          datatype: Datatype.STRING,
          operators: this.getOperatorOptions(Datatype.STRING),
          references: Object.values(CustomerAgeGroup).map((value) => ({
            label: value,
            value,
            datatype: Datatype.STRING,
          })),
        },
        {
          variable: VariableName.FAVORITE_ORDER_CHANNELS,
          operators: this.getOperatorOptions(Datatype.LIST),
          datatype: Datatype.LIST,
          allowCustom: false,
          references: Object.entries(OrderSource).map(([key, value]) => ({
            label: key,
            value,
            datatype: Datatype.LIST,
          })),
        },
        {
          variable: VariableName.LOYALTY_REGISTRATION_SOURCE,
          datatype: Datatype.STRING,
          operators: this.getOperatorOptions(Datatype.STRING),
          allowCustom: false,
          references: Object.entries(LRPSource).map(([key, value]) => ({
            label: key,
            value,
            datatype: Datatype.STRING,
          })),
        },
        {
          variable: VariableName.EXCEEDED_THRESHOLD_OF_UPPER_TIER,
          datatype: Datatype.PERCENTAGE,
          operators: this.getOperatorOptions(Datatype.PERCENTAGE),
          allowCustom: true,
        },
        {
          variable: VariableName.EXCEEDED_THRESHOLD_OF_CURRENT_TIER,
          datatype: Datatype.PERCENTAGE,
          operators: this.getOperatorOptions(Datatype.PERCENTAGE),
          allowCustom: true,
        },
        {
          variable: VariableName.LOYALTY_REGISTRATION_AT,
          datatype: Datatype.DATE,
          operators: this.getOperatorOptions(Datatype.DATE),
          allowCustom: true,
          references: this.getReferences(Datatype.DATE),
        },
        {
          variable: VariableName.LAST_COMMUNICATION_DAY,
          datatype: Datatype.DATE,
          operators: this.getOperatorOptions(Datatype.DATE),
          allowCustom: true,
          references: this.getReferences(Datatype.DATE),
        },
        {
          variable: VariableName.POINTS_PROGRESS,
          datatype: Datatype.NUMBER,
          operators: this.getOperatorOptions(Datatype.NUMBER),
          allowCustom: true,
        },
      ],
      [TemplateFilterEntity.DELIVERY_ORDER]: [
        {
          variable: VariableName.DELIVERY_ORDER_STATUS,
          datatype: Datatype.STRING,
          operators: this.getOperatorOptions(Datatype.STRING),
          allowCustom: false,
          references: Object.values(DeliveryOrderStatus).map((value) => ({
            label: value,
            value,
            datatype: Datatype.STRING,
          })),
        },
      ],
      [TemplateFilterEntity.ORDER]: [
        {
          variable: VariableName.ORDER_DELIVERY_TYPE,
          datatype: Datatype.STRING,
          operators: this.getOperatorOptions(Datatype.STRING),
          allowCustom: false,
          references: Object.values(OrderDeliveryAction).map((value) => ({
            label: value,
            value,
            datatype: Datatype.STRING,
          })),
        },
        {
          variable: VariableName.SOURCE,
          datatype: Datatype.STRING,
          operators: this.getOperatorOptions(Datatype.STRING),
          allowCustom: false,
          references: Object.values(OrderSource).map((value) => ({
            label: value,
            value,
            datatype: Datatype.STRING,
          })),
        },
        {
          variable: VariableName.PAYMENT_METHOD,
          datatype: Datatype.STRING,
          operators: this.getOperatorOptions(Datatype.STRING),
          allowCustom: false,
          references: Object.values(OrderPaymentMethod).map((value) => ({
            label: value,
            value,
            datatype: Datatype.STRING,
          })),
        },
        {
          variable: VariableName.ORDER_STATUS,
          datatype: Datatype.STRING,
          operators: this.getOperatorOptions(Datatype.STRING),
          allowCustom: false,
          references: Object.values(OrderStatusEnum).map((value) => ({
            label: value,
            value,
            datatype: Datatype.STRING,
          })),
        },
        ...this.createBooleanVariable(
          VariableName.ORDER_IS_GIFT,
          VariableName.UNLOCKED_COUPONS,
          VariableName.WILL_UNLOCK_COUPONS,
          VariableName.MAINTAINED_TIER,
          VariableName.WILL_MAINTAIN_TIER,
          VariableName.UPGRADED_TIER,
          VariableName.WILL_UPGRADE_TIER,
          VariableName.ORDER_HAS_PIN_LOCATION,
          VariableName.WILL_UNLOCK_REWARDS,
          VariableName.UNLOCKED_REWARDS,
          VariableName.WILL_UNLOCK_FIRST_TRACK_REWARDS,
          VariableName.UNLOCKED_FIRST_TRACK_REWARDS,
          VariableName.WILL_UNLOCK_SECOND_TRACK_REWARDS,
          VariableName.UNLOCKED_SECOND_TRACK_REWARDS,
          VariableName.TIER_UNMAINTAINED_ON_THIS_REVERSION,
          VariableName.TIER_DOWNGRADED_ON_THIS_REVERSION,
          VariableName.REWARD_LOST_ON_THIS_REVERSION,
          VariableName.COUPON_LOST_ON_THIS_REVERSION,
        ),
      ],
    };
  }

  private createBooleanVariable(...names: VariableName[]): Variable[] {
    return names.map((name) => ({
      variable: name,
      datatype: Datatype.BOOLEAN,
      operators: this.getOperatorOptions(Datatype.BOOLEAN),
      allowCustom: false,
      references: this.getReferences(Datatype.BOOLEAN),
    }));
  }

  private getReferences(
    datatype: Datatype.DATE | Datatype.BOOLEAN,
  ): TypedOption[] {
    return {
      [Datatype.DATE]: [
        {
          label: 'Registration Day',
          value: Reference.REGISTRATION_DAY,
          datatype: Datatype.DATE,
        },
        {
          label: 'End of Cycle Day',
          value: Reference.END_OF_CYCLE_DAY,
          datatype: Datatype.DATE,
        },
        {
          label: 'Last Communication Day',
          value: Reference.LAST_COMMUNICATION_DAY,
          datatype: Datatype.DATE,
        },
        {
          label: 'Last Aggregator Order',
          value: Reference.LAST_AGGREGATOR_ORDER,
          datatype: Datatype.DATE,
        },
        {
          label: 'Acquisition Day',
          value: Reference.ACQUISITION_DAY,
          datatype: Datatype.DATE,
        },
        {
          label: 'Last 7 Days',
          value: Reference.LAST_SEVEN_DAYS,
          datatype: Datatype.INTERVAL,
        },
        {
          label: 'Last 14 Days',
          value: Reference.LAST_FOURTEEN_DAYS,
          datatype: Datatype.INTERVAL,
        },
        {
          label: 'Last 30 Days',
          value: Reference.LAST_THIRTY_DAYS,
          datatype: Datatype.INTERVAL,
        },
        {
          label: 'Last 3 Months',
          value: Reference.LAST_THREE_MONTHS,
          datatype: Datatype.INTERVAL,
        },
        {
          label: 'Last 6 Months',
          value: Reference.LAST_SIX_MONTHS,
          datatype: Datatype.INTERVAL,
        },
      ],
      [Datatype.BOOLEAN]: [
        {
          label: 'True',
          value: 'true',
          datatype: Datatype.BOOLEAN,
        },
        {
          label: 'False',
          value: 'false',
          datatype: Datatype.BOOLEAN,
        },
      ],
    }[datatype];
  }

  private getOperatorOptions(datatype: Datatype): TypedOption[] {
    const operators = this.getOperators(datatype);
    const operatorOptions = operators.map(
      (operator: Operator): TypedOption => ({
        label: operator.replaceAll('_', ' '),
        value: operator,
        datatype,
      }),
    );

    if (datatype === Datatype.DATE) {
      const intervalOperators = this.getOperators(Datatype.INTERVAL);
      return intervalOperators
        .map(
          (operator: Operator): TypedOption => ({
            label: operator.replaceAll('_', ' '),
            value: operator,
            datatype: Datatype.INTERVAL,
          }),
        )
        .concat(operatorOptions);
    }

    return operatorOptions;
  }

  private getOperators(datatype: Datatype): Operator[] {
    return {
      [Datatype.STRING]: [Operator.IS, Operator.IS_NOT],
      [Datatype.NUMBER]: [
        Operator.GREATER_THAN,
        Operator.GREATER_THAN_OR_EQUAL,
        Operator.EQUAL,
        Operator.LESS_THAN,
        Operator.LESS_THAN_OR_EQUAL,
      ],
      [Datatype.DATE]: [
        Operator.LESS_THAN,
        Operator.LESS_THAN_OR_EQUAL,
        Operator.EQUAL,
        Operator.GREATER_THAN,
        Operator.GREATER_THAN_OR_EQUAL,
      ],
      [Datatype.INTERVAL]: [
        Operator.AFTER,
        Operator.WITHIN,
        Operator.NOT_WITHIN,
        Operator.BEFORE,
      ],
      [Datatype.LIST]: [Operator.INCLUDES, Operator.DOES_NOT_INCLUDE],
      [Datatype.PERCENTAGE]: [
        Operator.GREATER_THAN,
        Operator.GREATER_THAN_OR_EQUAL,
        Operator.EQUAL,
        Operator.LESS_THAN,
        Operator.LESS_THAN_OR_EQUAL,
      ],
      [Datatype.BOOLEAN]: [Operator.IS, Operator.IS_NOT],
    }[datatype];
  }
}
