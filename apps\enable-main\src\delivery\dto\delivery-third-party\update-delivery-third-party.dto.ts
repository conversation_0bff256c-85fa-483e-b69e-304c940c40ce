import { ApiProperty, PickType } from '@nestjs/swagger';
import { DeliveryThirdParty } from '../../models/delivery-third-party.model';
import { Types } from 'mongoose';
import { ObjectIdTransform } from '@app/shared-stuff';

export class UpdateDeliveryThirdPartyDto extends PickType(DeliveryThirdParty, [
  'internalName',
  'settlementParty',
  'settlementSchema',
]) {
  @ApiProperty({ type: String, required: true })
  @ObjectIdTransform()
  _id: Types.ObjectId;
}
