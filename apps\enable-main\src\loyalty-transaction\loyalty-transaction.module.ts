import { CollectionName, LoyaltyTransactionSchema } from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CustomerReadModule } from '../customer/modules/customer-read/customer-read.module';
import { CustomerWriteModule } from '../customer/modules/customer-write/customer-write.module';
import { PunchCardAchievementModule } from '../punch-card/modules/punch-card-achievement/punch-card-achievement.module';
import { LoyaltyTransactionController } from './controllers/loyalty-transaction.controller';
import { LoyaltyTransactionRepositoryInterface } from './repositories/loyalty-transaction-repository.interface';
import { LoyaltyTransactionRepository } from './repositories/loyalty-transaction.repository';
import { LoyaltyTransactionServiceInterface } from './services/loyalty-transaction-service.interface';
import { LoyaltyTransactionService } from './services/loyalty-transaction.service';
import { CompanyModule } from '../company/company.module';

@Module({
  controllers: [LoyaltyTransactionController],
  imports: [
    CustomerWriteModule,
    CustomerReadModule,
    CompanyModule,
    PunchCardAchievementModule,
    MongooseModule.forFeature([
      {
        name: CollectionName.LOYALTY_TRANSACTION,
        schema: LoyaltyTransactionSchema,
      },
    ]),
  ],
  providers: [
    {
      provide: LoyaltyTransactionRepositoryInterface,
      useClass: LoyaltyTransactionRepository,
    },
    {
      provide: LoyaltyTransactionServiceInterface,
      useClass: LoyaltyTransactionService,
    },
  ],
  exports: [LoyaltyTransactionServiceInterface],
})
export class LoyaltyTransactionModule {}
