// EBL-3553 - [Loyalty Program] Promote LRP
// Create Send Loyalty Registration trigger and global template
db.triggers.insertOne({
  name: '[CUSTOMER] ON_SEND_LOYALTY_REGISTRATION',
  client: 'ENABLE_MAIN',
  action: 'ON_SEND_LOYALTY_REGISTRATION',
  module: 'CUSTOMER',
  replacement: ['loyaltyRegistrationPageLink'],
  createdAt: new Date(),
  updatedAt: new Date(),
});

db.templates.insertOne({
  name: 'Global Promote Loyalty Registration Tempalate',
  content: {
    enContent:
      'Use the following link to complete your registration and join our Loyalty Program 🎁 $loyaltyRegistrationPageLink',
    arContent:
      'Use the following link to complete your registration and join our Loyalty Program 🎁 $loyaltyRegistrationPageLink',
  },
  to: 'CUSTOMER',
  from: 'COMPANY_SENDER',
  type: 'CHAT',
  trigger: db.triggers.findOne({
    name: '[CUSTOMER] ON_SEND_LOYALTY_REGISTRATION',
  }),
  createdAt: new Date(),
  updatedAt: new Date(),
});
