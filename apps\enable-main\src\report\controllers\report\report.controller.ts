import {
  CustomerReportFilterDto,
  GenericExceptionFilter,
  OrderReportFilterDto,
  PaymentReportFilterDto,
  TransformInterceptor,
} from '@app/shared-stuff';
import { MicroserviceCommunicationService } from '@app/shared-stuff/services/microservice-communication.service';
import {
  Controller,
  Get,
  Inject,
  OnModuleDestroy,
  OnModuleInit,
  Query,
  Req,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { Request } from 'express';

@Controller('report')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags('Report')
@SetMetadata('module', 'report')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class ReportController implements OnModuleInit, OnModuleDestroy {
  constructor(
    @Inject('enable-main-reporting-producer') private client: ClientProxy,
    private microserviceCommunicationService: MicroserviceCommunicationService,
  ) {}

  async onModuleDestroy() {
    await this.microserviceCommunicationService.disconnect(this.client);
  }

  async onModuleInit() {
    await this.microserviceCommunicationService.connect(this.client);
  }

  @Get('order')
  @ApiOperation({
    deprecated: true,
    description: '**Deprecated**, use `GET /analytics` instead',
  })
  @SetMetadata('action', 'order-report')
  public async generateOrderReports(
    @Query() orderReportFilterDto: OrderReportFilterDto,
    @Req() req: Request,
  ) {
    orderReportFilterDto.companyId = req['company_id']
      ? req['company_id']
      : orderReportFilterDto.companyId;
    return this.microserviceCommunicationService.produceAndWaitForResponse<
      OrderReportFilterDto,
      any
    >(this.client, 'order.report.request', orderReportFilterDto);
  }

  @Get('modes/order')
  @SetMetadata('action', 'order-report')
  public async generateOrderReportsBasedOnModes(
    @Query() orderReportFilterDto: OrderReportFilterDto,
    @Req() req: Request,
  ) {
    orderReportFilterDto.companyId = req['company_id']
      ? req['company_id']
      : orderReportFilterDto.companyId;
    return this.microserviceCommunicationService.produceAndWaitForResponse<
      OrderReportFilterDto,
      any
    >(this.client, 'order.report.modes.request', orderReportFilterDto);
  }

  @Get('payment')
  @SetMetadata('action', 'payment-report')
  public async generatePaymentReports(
    @Query() paymentReportFilterDto: PaymentReportFilterDto,
    @Req() req: Request,
  ) {
    paymentReportFilterDto.companyId = req['company_id']
      ? req['company_id']
      : paymentReportFilterDto.companyId;
    return this.microserviceCommunicationService.produceAndWaitForResponse<
      PaymentReportFilterDto,
      any
    >(this.client, 'payment.report.request', paymentReportFilterDto);
  }

  @Get('customer')
  @SetMetadata('action', 'customer-report')
  public async generateCustomerReports(
    @Query() customerReportFilterDto: CustomerReportFilterDto,
    @Req() req: Request,
  ) {
    customerReportFilterDto.companyId = req['company_id']
      ? req['company_id']
      : customerReportFilterDto.companyId;

    return this.microserviceCommunicationService.produceAndWaitForResponse<
      CustomerReportFilterDto,
      any
    >(this.client, 'customer.report.request', customerReportFilterDto);
  }
}
