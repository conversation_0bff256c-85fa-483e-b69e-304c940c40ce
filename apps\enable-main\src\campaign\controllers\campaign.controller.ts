import {
  Controller,
  SetMetadata,
  Inject,
  Post,
  Body,
  Get,
  Query,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import {
  CampaignDocument,
  CreateCampaignDto,
  IndexCampaignDto,
} from '@app/shared-stuff';
import { CurrentUserService } from '../../shared/services/current-user/current-user.service';
import { CampaignServiceInterface } from '../services/campaign.service.interface';

@ApiTags('Campaign')
@SetMetadata('module', 'campaign')
@Controller('campaign')
export class CampaignController {
  constructor(
    private currentUserService: CurrentUserService,
    @Inject(CampaignServiceInterface)
    private campaignService: CampaignServiceInterface,
  ) {}

  @Get()
  @SetMetadata('action', 'index-campaign')
  async index(
    @Query() indexCampaignDto: IndexCampaignDto,
  ): Promise<CampaignDocument[]> {
    const companyId =
      indexCampaignDto.companyId ??
      this.currentUserService.getCurrentCompanyId();
    this.currentUserService.validateAccessToCompany(companyId);
    return await this.campaignService.index(indexCampaignDto);
  }

  @Post()
  @SetMetadata('action', 'create-campaign')
  async create(
    @Body() createCampaignDto: CreateCampaignDto,
  ): Promise<CampaignDocument> {
    return await this.campaignService.create({
      ...createCampaignDto,
      companyId: this.currentUserService.getCurrentCompanyId(),
      createdBy: this.currentUserService.getCurrentUser(),
    });
  }
}
