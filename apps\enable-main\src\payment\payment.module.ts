import {
  CollectionName,
  OrderSchema,
  PaymentDocument,
  PaymentSchema,
  SharedStuffModule,
} from '@app/shared-stuff';
import { HttpModule } from '@nestjs/axios';
import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { MongooseModule } from '@nestjs/mongoose';
import { ScheduleModule } from '@nestjs/schedule';
import { CompanyModule } from '../company/company.module';
import { CustomerIndexModule } from '../customer/modules/customer-index/customer-index.module';
import { CustomerReadModule } from '../customer/modules/customer-read/customer-read.module';
import { CustomerWriteModule } from '../customer/modules/customer-write/customer-write.module';
import { ShortenUrlModule } from '../integration/shorten-url/shorten-url.module';
import { NotificationModule } from '../notification/notification.module';
import { SharedModule } from '../shared/shared.module';
import { StorageModule } from '../storage/storage.module';
import { BranchModule } from './../branch/branch.module';
import { BrandModule } from './../brand/brand.module';
import { DeliveryModule } from './../delivery/delivery.module';
import { OrderModule } from './../order/order.module';
import { PaymentConfigurationController } from './controllers/payment-configuration/payment-configuration.controller';
import { PaymentTESSController } from './controllers/payment-tess/payment-tess.controller';
import { PaymentController } from './controllers/payment/payment.controller';
import { PaymentListener } from './listeners/payment.listener';
import {
  PaymentConfiguration,
  PaymentConfigurationSchema,
} from '../../../../libs/shared-stuff/src/models/payment.configuration.model';
import { PaymentLog, PaymentLogSchema } from './models/payment.log.model';
import {
  PaymentStatus,
  PaymentStatusSchema,
} from './models/payment.status.model';
import { PaymentConfigurationRepositoryInterface } from './repositories/interfaces/payment.configuration.repository.interface';
import { PaymentConfigurationRepository } from './repositories/payment.configuration.repository';
import { PaymentLogRepository } from './repositories/payment.log.repository';
import { PaymentRepository } from './repositories/payment.repository';
import { PaymentStatusRepository } from './repositories/payment.status.repository';
import { PaymentConfigurationService } from './services/payment-configuration/payment-configuration.service';
import { PaymentConfigurationServiceInterface } from './services/payment-configuration/payment-configuration.service.interface';
import { PaymentHelpersService } from './services/payment-helpers/payment-helpers.service';
import { PaymentCbpayService } from './services/payment-integrations/payment-cbpay/payment-cbpay.service';
import { PaymentCyberSourceService } from './services/payment-integrations/payment-cyber-source/payment-cyber-source.service';
import { PaymentDibsyService } from './services/payment-integrations/payment-dibsy/payment-dibsy.service';
import { PaymentMyFatoorahService } from './services/payment-integrations/payment-myfatoorah/payment-myfatoorah.service';
import { PaymentSkipCashService } from './services/payment-integrations/payment-skip-cash/payment-skip-cash.service';
import { PaymentStripeService } from './services/payment-integrations/payment-stripe/payment-stripe.service';
import { PaymentTapService } from './services/payment-integrations/payment-tap/payment-tap.service';
import { PaymentTESSService } from './services/payment-integrations/payment-tess/payment-tess.service';
import { PaymentVistamoneyService } from './services/payment-integrations/payment-vistamoney/payment-vistamoney.service';
import { PaymentNotificationService } from './services/payment-notification/payment-notification.service';
import { PaymentService } from './services/payment/payment.service';

@Module({
  imports: [
    CustomerReadModule,
    CustomerIndexModule,
    CustomerWriteModule,
    SharedStuffModule,
    ConfigModule,
    SharedModule,
    CompanyModule,
    HttpModule,
    BranchModule,
    forwardRef(() => OrderModule),
    NotificationModule,
    ScheduleModule,
    DeliveryModule,
    BrandModule,
    ShortenUrlModule,
    StorageModule,
    MongooseModule.forFeatureAsync([
      {
        name: CollectionName.PAYMENT,
        useFactory: (eventEmitter: EventEmitter2) => {
          const schema = PaymentSchema;
          schema.post('save', async function (payment: PaymentDocument, next) {
            eventEmitter.emit('payment.updated', payment);
            next();
          });
          return PaymentSchema;
        },
        inject: [EventEmitter2],
      },
      { name: PaymentStatus.name, useFactory: () => PaymentStatusSchema },
      { name: CollectionName.ORDER, useFactory: () => OrderSchema },
      { name: PaymentLog.name, useFactory: () => PaymentLogSchema },
      {
        name: PaymentConfiguration.name,
        useFactory: () => PaymentConfigurationSchema,
      },
    ]),
  ],
  controllers: [
    PaymentController,
    PaymentTESSController,
    PaymentConfigurationController,
  ],
  providers: [
    {
      provide: PaymentConfigurationRepositoryInterface,
      useClass: PaymentConfigurationRepository,
    },
    {
      provide: PaymentConfigurationServiceInterface,
      useClass: PaymentConfigurationService,
    },
    {
      provide: 'PaymentRepositoryInterface',
      useClass: PaymentRepository,
    },
    {
      provide: 'PaymentLogRepositoryInterface',
      useClass: PaymentLogRepository,
    },
    {
      provide: 'PaymentStatusRepositoryInterface',
      useClass: PaymentStatusRepository,
    },
    {
      provide: 'PaymentTESSService',
      useClass: PaymentTESSService,
    },
    PaymentService,
    PaymentVistamoneyService,
    PaymentNotificationService,
    PaymentCbpayService,
    PaymentCyberSourceService,
    PaymentHelpersService,
    PaymentStripeService,
    PaymentSkipCashService,
    PaymentDibsyService,
    PaymentMyFatoorahService,
    PaymentTapService,
    PaymentListener,
  ],
  exports: [
    PaymentService,
    PaymentNotificationService,
    'PaymentRepositoryInterface',
  ],
})
export class PaymentModule {}
