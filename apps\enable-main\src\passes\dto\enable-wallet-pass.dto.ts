import { Barcode } from 'passkit-generator';
import { Image, LanguageCode, LocalizedString, PassField } from '@app/shared-stuff';

export class EnableWalletPass {
  loyaltyCardName: LocalizedString;
  language: LanguageCode;
  backgroundColor: string;
  textColor: string;
  logo: Image;
  icon: Image;
  stripImage: Image;
  headerFields: PassField[];
  primaryField: PassField | null;
  secondaryFields: PassField[];
  qrCode: Barcode;
}
