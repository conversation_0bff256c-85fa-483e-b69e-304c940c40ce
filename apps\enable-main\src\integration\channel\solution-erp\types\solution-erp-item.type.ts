import { ApiProperty } from '@nestjs/swagger';

export class SolutionERPItem {
  @ApiProperty({
    type: String,
    required: false,
  })
  stock_id: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  description: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  uom: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  quantity: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  price: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  discount: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  total: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  notes: string;
}
