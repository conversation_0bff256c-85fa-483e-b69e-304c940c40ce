// EBL-3716 [Loyalty Program] Wallet Pass - Loyalty Card Status
// Initialize customer loyaltyCardStatus as not_added
db.customers.updateMany(
  {
    loyaltyCardStatus: null,
    $or: [{ registeredPasses: null }, { registeredPasses: [] }],
  },
  { $set: { loyaltyCardStatus: 'not_added' } },
);

db.customers.updateMany(
  {
    loyaltyCardStatus: null,
    'registeredPasses.walletApp': 'unknown',
  },
  { $set: { loyaltyCardStatus: 'unknown' } },
);

db.customers.updateMany(
  {
    loyaltyCardStatus: null,
    'registeredPasses.walletApp': 'wallet_passes',
  },
  { $set: { loyaltyCardStatus: 'wallet_passes' } },
);

db.customers.updateMany(
  {
    loyaltyCardStatus: null,
    'registeredPasses.walletApp': 'apple_wallet',
  },
  { $set: { loyaltyCardStatus: 'apple_wallet' } },
);
