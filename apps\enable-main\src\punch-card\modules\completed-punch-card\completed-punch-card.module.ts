import { CollectionName, CompletedPunchCardSchema } from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CompletedPunchCardRepository } from './completed-punch-card.repository';
import { CompletedPunchCardService } from './completed-punch-card.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: CollectionName.COMPLETED_PUNCH_CARD,
        schema: CompletedPunchCardSchema,
      },
    ]),
  ],
  providers: [CompletedPunchCardRepository, CompletedPunchCardService],
  exports: [CompletedPunchCardService],
})
export class CompletedPunchCardModule {}
