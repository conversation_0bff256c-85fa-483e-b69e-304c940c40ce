declare const db: any;

// EBL-3559 - Change ON_MAQSAM_CALL_CAME to ON_CALL_CAME
// Rename triggers
const renameMap = {
  ON_MAQSAM_CALL_CAME: 'ON_CALL_CAME',
  ON_MAQSAM_CALL_DROPPED: 'ON_CALL_DROPPED',
  ON_MAQSAM_CALL_ABANDONED: 'ON_CALL_ABANDONED',
};

Object.entries(renameMap).map(([key, value]) => {
  db.triggers.updateOne(
    { action: key },
    { $set: { action: value, name: `[CALL_CENTER] ${value}` } },
  );
  db.templates.updateMany(
    { 'trigger.action': key },
    {
      $set: {
        'trigger.action': value,
        'trigger.name': `[CALL_CENTER] ${value}`,
      },
    },
  );
});
