import { Type } from 'class-transformer';
import { IsNotEmpty, ValidateNested } from 'class-validator';
import { PassDeliveryLocationDto } from './pass-delivery-location.dto';

export class PassDeliveryAddressesDto {
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => PassDeliveryLocationDto)
  pickup: PassDeliveryLocationDto;

  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => PassDeliveryLocationDto)
  dropoffs: PassDeliveryLocationDto[];
}
