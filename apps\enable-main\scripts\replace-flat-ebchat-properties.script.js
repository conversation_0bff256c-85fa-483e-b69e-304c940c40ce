const ebChatTemplate = {
  status: 'approved',
  replacementMapping: [],
  numberOfRequiredReplacement: 0,
  buttonsReference: [],
  externalReference: '0',
  actionNeeded: false,
};
db.getCollection('templates').updateMany(
  {},
  {
    $set: { ebChatTemplate: ebChatTemplate },
    $unset: {
      status: '',
      replacementMapping: '',
      numberOfRequiredReplacement: '',
      buttonsReference: '',
      externalReference: '',
      actionNeeded: '',
    },
  },
);
