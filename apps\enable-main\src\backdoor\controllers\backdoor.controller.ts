import {
  BackdoorFindCustomerDto,
  GenericExceptionFilter,
  ShopifyOrder,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  RawBodyRequest,
  Req,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { FireTriggerBackdoorDto } from '../dtos/fire-trigger.dto';
import { forceResetCustomerCalendarCycle } from '../dtos/force-reset-customer-calendar-cycle.dto';
import { UpdateCustomerTierEarnDateDto } from '../dtos/update-customer-tier-earn-date.dto';
import { BackdoorService } from '../services/backdoor.service';
@Controller('backdoor')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
export class BackdoorController {
  constructor(private readonly backdoorService: BackdoorService) {}

  @Post('fireTrigger')
  @SetMetadata('public', 'true')
  async fireTrigger(@Body() fireTriggerDto: FireTriggerBackdoorDto) {
    return await this.backdoorService.fireTrigger(fireTriggerDto);
  }

  @Post('updateCustomerTierEarnDate')
  @SetMetadata('public', 'true')
  async updateCustomerTierEarnDate(
    @Body() updateCustomerTierEarnDateDto: UpdateCustomerTierEarnDateDto,
  ) {
    return await this.backdoorService.updateCustomerTierEarnDate(
      updateCustomerTierEarnDateDto,
    );
  }

  @Post(['forceCustomerTierUpdate', 'forceResetCustomerCalendarCycle'])
  @SetMetadata('public', 'true')
  async forceResetCustomerCalendarCycle(
    @Body() { customerId, startDate, endDate }: forceResetCustomerCalendarCycle,
  ) {
    return await this.backdoorService.forceResetCustomerCalendarCycle(
      customerId,
      startDate ? moment.utc(startDate).toDate() : undefined,
      endDate ? moment.utc(endDate).toDate() : undefined,
    );
  }

  @Post('forceComputationCycleDay')
  @SetMetadata('public', 'true')
  async forceComputationCycleDay(@Body() { companyId }: { companyId: string }) {
    return await this.backdoorService.forceComputationCycleDay(companyId);
  }

  @Get('customer/:id')
  @SetMetadata('public', 'true')
  async getCustomerDetails(
    @Param('id') id: string,
    @Query() backdoorFindCustomerDto: BackdoorFindCustomerDto,
  ) {
    return this.backdoorService.getCustomerDetails(id, backdoorFindCustomerDto);
  }

  @Post('store/:storeId/shopify/calculateHash')
  @SetMetadata('public', 'true')
  async calculateShopifyHash(
    @Param('storeId') storeId: string,
    @Req() req: RawBodyRequest<ShopifyOrder>,
  ) {
    const hash = await this.backdoorService.calculateHash(
      new Types.ObjectId(storeId),
      req.rawBody,
    );

    return `Attach header 'X-Shopify-Hmac-SHA256' with value '${hash}'`;
  }
}
