import {
  CreateDeliveryConfigurationDto,
  GetDeliveryConfigurationDto,
  IndexDeliveryConfigurationDto,
  UpdateDeliveryConfigurationDto,
} from '@app/shared-stuff';
import { Types } from 'mongoose';
import {
  DeliveryConfigurationDocument,
  DeliveryConfigurationWithId,
} from '../../models/delivery-configuration.model';

export interface DeliveryConfigurationServiceInterface {
  deleteByBranchId(branchId: Types.ObjectId): Promise<string>;
  create(
    createDeliveryConfigurationDto: CreateDeliveryConfigurationDto,
  ): Promise<DeliveryConfigurationDocument>;
  update(
    updateDeliveryConfigurationDto: UpdateDeliveryConfigurationDto,
  ): Promise<DeliveryConfigurationDocument>;
  index(
    indexDeliveryConfigurationDto: IndexDeliveryConfigurationDto,
  ): Promise<any>;
  findById(id: Types.ObjectId): Promise<DeliveryConfigurationDocument>;
  delete(id: Types.ObjectId): Promise<string>;
  getDeliveryConfiguration(
    getDeliveryConfigurationDto: GetDeliveryConfigurationDto,
  ): Promise<DeliveryConfigurationWithId>;
  findByCompanyId(
    companyId: Types.ObjectId,
  ): Promise<DeliveryConfigurationDocument>;
  findByBranchId(
    branchId: Types.ObjectId,
  ): Promise<DeliveryConfigurationDocument>;
}
export const DeliveryConfigurationServiceInterface = Symbol(
  'DeliveryConfigurationServiceInterface',
);
