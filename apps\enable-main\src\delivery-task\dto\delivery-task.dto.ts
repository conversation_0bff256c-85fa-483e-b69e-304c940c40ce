import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDefined,
  IsInt,
  IsMongoId,
  IsNotEmpty,
  IsOptional,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
import { Types } from 'mongoose';
import { CreateCustomerDto } from '@app/shared-stuff';

export class DeliveryTaskPoint {
  @ApiProperty({
    type: Date,
    required: true,
  })
  @IsNotEmpty()
  date: Date;

  @ApiProperty({
    type: Date,
    required: true,
  })
  completeDate: Date;

  @ApiProperty({
    type: Date,
    required: true,
  })
  startDate: Date;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsMongoId()
  location: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  locationName: string;

  @ApiProperty({
    type: Types.ObjectId,
    required: true,
  })
  @IsMongoId()
  @IsNotEmpty()
  taskId: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: true,
  })
  pinLocation: string;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsMongoId()
  branch: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: false,
  })
  barCode: string;
}

export enum DeliveryTaskStatus {
  'Pending' = 'Pending',
  'Fired To Party' = 'Fired To Party',
  'Pending Pickup' = 'Pending Pickup',
  'In Route' = 'In Route',
  'Picked Up' = 'Pickups Done',
  'Delivered' = ' Delivered',
  'Canceled' = 'canceled',
  'Delayed' = 'Delayed',
}

export enum DeliveryTaskType {
  'Pickup' = 'Pickup',
  'Delivery' = 'Delivery',
  'Pickup & Delivery' = 'Pickup & Delivery',
}

export class DeliveryTaskToIndex {
  @ApiProperty({
    required: false,
    type: String,
    description:
      "it takes one 'key:value' pair, where the key is the key to sort by and the value is the direction to sort the key with. e.g [name:asc, name:desc]",
  })
  sortBy: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  searchKey: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  searchValue: string;

  @ApiProperty({
    type: String,
    required: false,
    enum: [...Object.keys(DeliveryTaskStatus)],
  })
  @IsOptional()
  status: DeliveryTaskStatus;

  @ApiProperty({
    required: false,
    type: Number,
    default: 0,
  })
  @IsOptional()
  @IsNotEmpty()
  @IsDefined()
  @IsInt()
  @Min(0)
  offset?: number = 0;

  @ApiProperty({
    required: false,
    type: Number,
    minimum: 1,
    default: 10,
  })
  @IsOptional()
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  limit?: number = 10;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsOptional()
  @IsNotEmpty()
  @IsMongoId()
  customer?: Types.ObjectId;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsOptional()
  @IsNotEmpty()
  @IsMongoId()
  driver?: Types.ObjectId;

  company?: Types.ObjectId;
}

export class DeliveryTaskToCreate {
  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsOptional()
  @IsMongoId()
  customerId?: Types.ObjectId;

  @ApiProperty({
    type: () => CreateCustomerDto,
    required: false,
  })
  @IsOptional()
  @Type(() => CreateCustomerDto)
  customer?: CreateCustomerDto;

  @ApiProperty({
    type: Boolean,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  autoAssign = false;

  @ApiProperty({
    type: () => [DeliveryTaskPoint],
    required: false,
    default: [],
  })
  @IsOptional()
  @Type(() => DeliveryTaskPoint)
  pickupPoints: DeliveryTaskPoint[];

  @ApiProperty({
    type: () => [DeliveryTaskPoint],
    default: [],
    required: false,
  })
  @IsOptional()
  @Type(() => DeliveryTaskPoint)
  deliveryPoints: DeliveryTaskPoint[];

  @ApiProperty({
    type: String,
    enum: [...Object.keys(DeliveryTaskType)],
    required: true,
  })
  @IsNotEmpty()
  type: DeliveryTaskType;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsMongoId()
  @IsNotEmpty()
  deliveryParty: Types.ObjectId;

  @ApiProperty({
    type: String,
    required: false,
  })
  barCode: string;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @Type(() => Types.ObjectId)
  @IsMongoId()
  @IsNotEmpty()
  company: Types.ObjectId;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsMongoId()
  @IsOptional()
  order: Types.ObjectId;

  @ApiProperty({
    type: Number,
    required: false,
    default: 0,
  })
  @IsOptional()
  deliveryFees = 0;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsMongoId()
  @IsOptional()
  driver: Types.ObjectId;
}

export class DeliveryTaskStatusToUpdate {
  @ApiProperty({
    type: String,
    required: false,
    enum: [...Object.keys(DeliveryTaskStatus)],
  })
  @IsNotEmpty()
  status: DeliveryTaskStatus;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsNotEmpty()
  @IsMongoId()
  deliveryTaskId: Types.ObjectId;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsMongoId()
  companyId: Types.ObjectId;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsMongoId()
  deliveryPartyId: Types.ObjectId;
}

export class DeliveryTaskPointToDelete {
  @ApiProperty({
    type: String,
    required: false,
  })
  @IsNotEmpty()
  barCode: string;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsNotEmpty()
  @IsMongoId()
  deliveryTaskId: Types.ObjectId;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsMongoId()
  companyId: Types.ObjectId;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsMongoId()
  deliveryPartyId: Types.ObjectId;
}

export class DeliveryTaskDriverToAssign {
  @ApiProperty({
    type: Types.ObjectId,
    required: true,
  })
  @IsNotEmpty()
  @IsMongoId()
  driver: Types.ObjectId;

  @ApiProperty({
    type: Types.ObjectId,
    required: true,
  })
  @IsNotEmpty()
  @IsMongoId()
  deliveryTaskId: Types.ObjectId;
}

export class DeliveryTaskDetailsToGet {
  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsNotEmpty()
  @IsMongoId()
  deliveryTaskId: Types.ObjectId;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsMongoId()
  companyId: Types.ObjectId;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsMongoId()
  deliveryPartyId: Types.ObjectId;
}

export class DeliveryTaskToRemove {
  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsNotEmpty()
  @IsMongoId()
  deliveryTaskId: Types.ObjectId;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsMongoId()
  companyId: Types.ObjectId;

  @ApiProperty({
    type: Types.ObjectId,
    required: false,
  })
  @IsMongoId()
  deliveryPartyId: Types.ObjectId;

  currentUser: {};
}
