import { OrderStatusServiceInterface } from '../order-status.interface';
import {
  CurrentUser,
  OrderDocument,
  OrderLogActionEnum,
  OrderStatusEnum,
  OrderTransitionTrigger,
  responseCode,
} from '@app/shared-stuff';
import { BadRequestException, Inject } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { OrderNotificationService } from '../../order-notification/order-notification.service';
import { OrderLogServiceInterface } from '../../interfaces/order-log.service.interface';

export class UnAssignedOrderService implements OrderStatusServiceInterface {
  transitionalStatuses = [OrderStatusEnum.PENDING, OrderStatusEnum.UNASSIGNED];
  transitionalTrigger = [
    OrderTransitionTrigger.CREATED,
    OrderTransitionTrigger.REJECTED,
    OrderTransitionTrigger.DELIVERECT_STATUS_UPDATE,
  ];

  constructor(
    @Inject('OrderLogServiceInterface')
    private readonly orderLogService: OrderLogServiceInterface,
    private orderNotificationService: OrderNotificationService,
    private eventEmitter: EventEmitter2,
  ) {}

  validateStatus(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
  ): boolean {
    return (
      this.ValidateTransition(order, orderTransitionTrigger) &&
      this.validatePreCondition(order)
    );
  }

  private ValidateTransition(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
  ): boolean {
    if (
      this.transitionalTrigger.includes(orderTransitionTrigger) &&
      this.transitionalStatuses.includes(order.status)
    )
      return true;

    throw new BadRequestException(
      "Can't Change From " + order.status + ' To UnAssigned',
      responseCode.STATUS_NOT_VALID.toString(),
    );
  }

  private validatePreCondition(order: OrderDocument): boolean {
    if (!order.branch) return true;

    throw new BadRequestException(
      "Can't change From " +
        order.status +
        ' to  UnAssigned' +
        'The order with ID ' +
        order._id.toHexString() +
        ' is assigned to a branch',
      responseCode.MISSING_DATA.toString(),
    );
  }

  async applyPostFunction(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
    oldStatus: OrderStatusEnum,
    user: CurrentUser,
  ) {
    if (orderTransitionTrigger == OrderTransitionTrigger.REJECTED) {
      await this.orderNotificationService.onOrderRejected(order);
      await this.orderLogService.saveOrderLog(
        order,
        { oldStatus: oldStatus },
        {
          newStatus: OrderStatusEnum.UNASSIGNED,
          trigger: orderTransitionTrigger,
        },
        OrderLogActionEnum.ON_ORDER_REJECTED,
        user,
      );
    } else {
      await this.orderNotificationService.onOrderIsCreated(order, undefined);
      this.eventEmitter.emit('order.created', order);
      await this.orderLogService.saveOrderLog(
        order,
        { oldStatus: oldStatus },
        {
          newStatus: OrderStatusEnum.UNASSIGNED,
          trigger: orderTransitionTrigger,
        },
        OrderLogActionEnum.ON_ORDER_UNASSIGNED,
        user,
      );
    }
  }
}
