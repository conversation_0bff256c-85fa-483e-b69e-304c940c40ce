import {
  BaseCustomerData,
  BrandWalletExcelRow,
  Customer,
  CustomerDocument,
  DeviceType,
  EXCEL_HEADERS,
  EXCEL_HEADERS_LOWER,
  EXCEL_HEADERS_MAPPING,
  excelRow,
  HelperSharedServiceInterface,
  IHelperSharedService,
  ImportExcelCustomerDto,
  ImportLoyaltyCustomersDto,
  IndexCustomerDto,
  Language,
  LoopyExcelRow,
  LRPSource,
  OrderItem,
  OrderToComplete,
  RequirementType,
} from '@app/shared-stuff';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as Excel from 'excel4node';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import * as xlsx from 'xlsx';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { CompanyService } from '../../../company/services/company/company.service';
import { PunchCardReadService } from '../../../punch-card/modules/punch-card-read/punch-card-read.service';
import { GoogleCloudStorageService } from '../../../storage/google-cloud-storage.service';
import { CustomerIndexServiceInterface } from '../customer-index/customer-index.service.interface';
import { CustomerLoyaltyMemberServiceInterface } from '../customer-loyalty-member/customer-loyalty-member.service.interface';
import { CustomerPunchCardServiceInterface } from '../customer-punch-card/customer-punch-card.service.interface';
import { CustomerRepositoryInterface } from '../customer-repository/customer.repository.interface';
import { CustomerIOServiceInterface } from './customer-io.service.interface';

type LoyaltyCustomerRow = LoopyExcelRow | BrandWalletExcelRow;

@Injectable()
export class CustomerIOService implements CustomerIOServiceInterface {
  constructor(
    @Inject(CustomerRepositoryInterface)
    private readonly customerRepository: CustomerRepositoryInterface,
    @Inject(CustomerIndexServiceInterface)
    private readonly customerIndexService: CustomerIndexServiceInterface,
    private configService: ConfigService,
    private companyService: CompanyService,
    @Inject('BrandServiceInterface')
    private brandService: BrandServiceInterface,
    @Inject(CustomerLoyaltyMemberServiceInterface)
    private customerLoyaltyMemberService: CustomerLoyaltyMemberServiceInterface,
    @Inject(CustomerPunchCardServiceInterface)
    private customerPunchCardService: CustomerPunchCardServiceInterface,
    private punchCardReadService: PunchCardReadService,
    @Inject(IHelperSharedService)
    private helperSharedService: HelperSharedServiceInterface,
    private googleCloudStorageService: GoogleCloudStorageService,
  ) {}

  async downloadFile(indexCustomerDto: IndexCustomerDto): Promise<string> {
    const SHEET_NAME = 'Customers';
    const workbook = new Excel.Workbook(),
      worksheet = workbook.addWorksheet(SHEET_NAME);
    const customers = await this.fetchingCustomersAsPatches(indexCustomerDto);
    const company = await this.companyService.get_details(
      indexCustomerDto.company instanceof Types.ObjectId
        ? indexCustomerDto.company.toHexString()
        : indexCustomerDto.company,
    );
    this.constructStyleSheet(workbook, worksheet);
    this.addCustomersDataIntoCells(
      customers,
      worksheet,
      company.localization?.timezone,
    );
    const fileName = await this.saveWorkbook(workbook, company.name);
    return `/documents/${fileName}`;
  }

  async importBrandWalletCustomers(params: ImportLoyaltyCustomersDto) {
    const customers = this.parseExcelFile(params, 'brandWallet');
    return this.processLoyaltyCustomers(customers);
  }

  async importLoopyCustomers(params: ImportLoyaltyCustomersDto) {
    const customers = this.parseExcelFile(params, 'loopy');
    return this.processLoyaltyCustomers(customers);
  }

  private parseExcelFile(
    { brandId, companyId, file }: ImportLoyaltyCustomersDto,
    type: 'loopy' | 'brandWallet',
  ): LoyaltyCustomerRow[] {
    const workbook = xlsx.read(file);
    const sheets = Object.values(workbook.Sheets);

    return sheets
      .map((sheet: xlsx.WorkSheet) =>
        xlsx.utils.sheet_to_json(sheet).map((row: LoyaltyCustomerRow) => ({
          ...row,
          companyId,
          brandId,
          type,
        })),
      )
      .flat() as LoyaltyCustomerRow[];
  }

  private async processLoyaltyCustomers(customers: LoyaltyCustomerRow[]) {
    await Promise.all(
      customers.map((customer) => this.registerLoyaltyCustomer(customer)),
    );

    // Increment punch cards
    const incrementedCustomers = await Promise.all(
      customers.map((customer) =>
        this.incrementPunchCardCountersForCustomer(customer),
      ),
    );

    const redeemedCustomers = await Promise.all(
      customers.map((customer) =>
        this.redeemRewardsForLoyaltyCustomer(customer),
      ),
    );

    return redeemedCustomers.length > 0
      ? redeemedCustomers
      : incrementedCustomers;
  }

  private async registerLoyaltyCustomer(customer: LoyaltyCustomerRow) {
    const customerDoc = await this.findOrCreateCustomer(
      this.mapToBaseCustomerData(customer),
      customer.type,
    );

    return this.customerLoyaltyMemberService.registerLoyaltyCustomer(
      customerDoc,
      {
        source:
          customer.type === 'loopy'
            ? LRPSource.LOOPY_LOYALTY
            : LRPSource.BRAND_WALLET_LOYALTY,
        brandId: customer.brandId,
      },
      false,
      true,
    );
  }

  private mapToBaseCustomerData(data: LoyaltyCustomerRow): BaseCustomerData {
    if (data.type === 'loopy') {
      return {
        phone: data['Mobile Number'],
        companyId: data.companyId,
        brandId: data.brandId,
        fullName: data['Your Name'],
        memberSince: moment.utc(data['Card Created']).toDate(),
        customerId: data['Customer Id'],
      };
    } else {
      return {
        phone: data['Phone'],
        companyId: data.companyId,
        brandId: data.brandId,
        fullName: data['Subscriber'],
        memberSince: moment.utc(data['Member Since']).toDate(),
        customerId: data['Id'],
        language:
          data.Language === 'Arabic' ? Language.arabic : Language.english,
        barcode: data.Barcode,
        deviceData: {
          deviceType: DeviceType.MOBILE,
          operatingSystem: {
            name: data['Device Type'] === 'iPhone' ? 'iOS' : 'Android',
            version: '',
          },
          userAgent: '',
          browser: { name: '', version: '' },
        },
      };
    }
  }

  private async incrementPunchCardCountersForCustomer(
    customer: LoyaltyCustomerRow,
  ): Promise<CustomerDocument> {
    const order: Partial<OrderToComplete> = {
      isLoyaltyOrder: true,
      company: customer.companyId,
    };

    const customerDoc = await this.findOrCreateCustomer(
      this.mapToBaseCustomerData(customer),
      customer.type,
    );

    const punchCard = (
      await this.punchCardReadService.index({
        companyId: customer.companyId,
      })
    )[0];

    const totalStampsEarned =
      customer.type == 'brandWallet'
        ? parseInt(customer.GIFT) *
            this.punchCardReadService.getCompletionThreshold(punchCard) +
          parseInt(customer.Stamps)
        : parseInt(customer['Total Stamps Earned']);

    if (punchCard.counter.type == RequirementType.NUMBER_OF_MENU_ITEMS) {
      await this.customerPunchCardService.incrementPunchCardCounters(
        customerDoc,
        {
          ...order,
          items: [
            {
              itemReference:
                punchCard.counter.menuItem.masterMenuItemId.toHexString(),
              quantity: totalStampsEarned,
            } as OrderItem,
          ],
        } as OrderToComplete,
      );
    } else if (
      punchCard.counter.type == RequirementType.NUMBER_OF_LOYALTY_ORDERS
    ) {
      for (let i = 0; i < totalStampsEarned; i++) {
        await this.customerPunchCardService.incrementPunchCardCounters(
          customerDoc,
          order as OrderToComplete,
        );
      }
    }
    return customerDoc;
  }

  private async redeemRewardsForLoyaltyCustomer(
    loyaltyCustomer: LoyaltyCustomerRow,
  ): Promise<CustomerDocument> {
    const customer = await this.findOrCreateCustomer(
      this.mapToBaseCustomerData(loyaltyCustomer),
      loyaltyCustomer.type,
    );

    if (!customer.rewards) return customer;

    const numberOfRedeemedRewards =
      loyaltyCustomer.type === 'brandWallet'
        ? Math.max(0, customer.rewards.length - parseInt(loyaltyCustomer.GIFT))
        : parseInt(loyaltyCustomer['Total Rewards Redeemed']);

    if (!numberOfRedeemedRewards) return customer;

    await this.customerRepository.redeemRewards(
      customer,
      customer.rewards.slice(0, numberOfRedeemedRewards),
    );
    return customer;
  }

  private constructStyleSheet(workBook: any, workSheet: any): void {
    const STYLE = workBook.createStyle({
      font: {
        color: '#FF0800',
        size: 12,
      },
      numberFormat: '$#,##0.00; ($#,##0.00); -',
    });
    for (let i = 1; i <= EXCEL_HEADERS.length; i++) {
      workSheet
        .cell(1, i)
        .string(EXCEL_HEADERS[i - 1])
        .style(STYLE);
    }
  }

  private async fetchingCustomersAsPatches(
    indexCustomerDto: IndexCustomerDto,
  ): Promise<CustomerDocument[]> {
    indexCustomerDto.offset = 0;
    indexCustomerDto.limit = 10000;

    let customers: CustomerDocument[] = [];

    let length = 0;
    do {
      const response = await this.customerIndexService.index(indexCustomerDto);
      length = response[0]['totalCount'][0]['createdAt'];
      customers = customers.concat(response[0]['paginatedResult']);
      indexCustomerDto.offset += 1;
    } while (length > customers.length);

    return customers;
  }

  private addCustomersDataIntoCells(
    customers: CustomerDocument[],
    workSheet: any,
    timezone: string,
  ): void {
    for (let i = 0; i < customers.length; i++) {
      workSheet.cell(i + 2, 1).string(customers[i]['full_name']?.toString());
      workSheet
        .cell(i + 2, 2)
        .string(customers[i]['email'] ? customers[i]['email'] : '');
      workSheet.cell(i + 2, 3).string(customers[i]['phone']?.toString());
      workSheet
        .cell(i + 2, 4)
        .string(
          customers[i]['contact_channel']
            ? customers[i]['contact_channel'].toString()
            : '',
        );
      if (customers[i]['last_order_date']) {
        workSheet.cell(i + 2, 5).string(
          moment(customers[i]['last_order_date'])
            .tz(timezone ?? 'Asia/Qatar')
            .format('YYYY-MM-DD hh:mm a'),
        );
      }

      workSheet
        .cell(i + 2, 6)
        .string(customers[i]['total_orders_amount']?.toString());
      workSheet
        .cell(i + 2, 7)
        .string(customers[i]['number_of_orders']?.toString());
      workSheet
        .cell(i + 2, 8)
        .string(
          customers[i]['gender'] ? customers[i]['gender'].toString() : '',
        );
      workSheet.cell(i + 2, 9).string(customers[i]['createdAt']?.toString());

      workSheet
        .cell(i + 2, 10)
        .string(customers[i]['deviceData']?.['deviceType']?.toString());

      const operatingSystem = customers[i]['deviceData']?.['operatingSystem'];
      const operationSystemString = operatingSystem
        ? `${operatingSystem?.name} ${operatingSystem?.version}`
        : '';
      workSheet.cell(i + 2, 11).string(operationSystemString);

      const browser = customers[i]['deviceData']?.['browser'];
      const browserString = browser
        ? `${browser?.name} ${browser?.version}`
        : '';
      workSheet.cell(i + 2, 12).string(browserString);
    }
  }

  private async saveWorkbook(
    workbook: any,
    companyName: string,
  ): Promise<string> {
    const fileName =
      companyName +
      '-Customers-' +
      moment.utc().format('DD-MM-YYYY HH-mm-ss').toString() +
      '.xlsx';

    await this.googleCloudStorageService.uploadDocument(
      await workbook.writeToBuffer(),
      fileName,
    );

    return fileName;
  }

  async uploadFile({ filePath, companyId }: ImportExcelCustomerDto) {
    const parseSheet = async (sheet: xlsx.WorkSheet) => {
      const customerData: excelRow[] = xlsx.utils.sheet_to_json(sheet);

      this.validateExcelColumnHeaders(customerData);

      const addMissingFields = (customer: Customer) => ({
        ...customer,
        company: companyId,
      });

      const customers: Customer[] = customerData
        .map(this.excelRowToCustomer)
        .map(addMissingFields);

      const newCustomers: Customer[] =
        await this.removeExistingCustomers(customers);

      await Promise.all(
        newCustomers.map((customer: Customer) =>
          this.customerRepository.create(customer),
        ),
      );
    };

    const workbook = xlsx.readFile(filePath);
    const sheets = Object.values(workbook.Sheets);
    await Promise.all(sheets.map(parseSheet));
  }

  private validateExcelColumnHeaders(customerData: excelRow[]): boolean {
    if (!customerData || customerData.length === 0) {
      return true;
    }

    const headers = Object.keys(customerData[0]);

    const invalidHeaders = headers.filter(
      (header) => !EXCEL_HEADERS_LOWER.includes(header.toLowerCase()),
    );

    if (invalidHeaders.length > 0) {
      throw new BadRequestException(
        `Invalid column header: "${invalidHeaders.join('", ')}".`,
      );
    }

    return true;
  }

  private excelRowToCustomer(customerRow: excelRow): Customer {
    return Object.entries(customerRow).reduce((customer, [key, value]) => {
      if (value === '') return customer;

      if (key === 'Customer Name') {
        const [first_name, ...last_name] = value.split(' ');
        customer.first_name = first_name;
        customer.last_name = last_name.join(' ');
      }

      const targetKey: string = EXCEL_HEADERS_MAPPING[key.toLowerCase()];
      if (targetKey === null) return customer;

      if (!targetKey.includes('.')) {
        customer[targetKey] = value;
        return customer;
      }
      const [deviceDataObj, deviceDataKey] = targetKey.split('.');
      customer[deviceDataObj] = customer[deviceDataObj] || {};

      const versionedFields = ['operatingSystem', 'browser'];
      if (versionedFields.includes(deviceDataKey)) {
        const [name, version] = value.split(' ');
        customer[deviceDataObj][deviceDataKey] = { name, version };
      } else {
        customer[deviceDataObj][deviceDataKey] = value;
      }

      return customer;
    }, {} as Partial<Customer>) as Customer;
  }

  private async removeExistingCustomers(
    customers: Customer[],
  ): Promise<Customer[]> {
    const newCustomers: Customer[] = [];
    for (const customer of customers) {
      const existingCustomer: Customer | null = customer.email
        ? await this.customerRepository.findByPhoneOrEmail(
            customer.phone,
            customer.email,
            customer.company,
          )
        : await this.customerRepository.findByPhoneAndCompanyId(
            customer.phone,
            customer.company,
          );
      if (existingCustomer === null) {
        newCustomers.push(customer);
      }
    }
    return newCustomers;
  }

  private async findOrCreateCustomer(
    data: BaseCustomerData,
    customerType: 'loopy' | 'brandWallet',
  ): Promise<CustomerDocument> {
    const phoneParts =
      this.helperSharedService.getFormattedCountryCodeAndPhoneNumber(
        data.phone.toString(),
        '',
      );

    const existingCustomer =
      await this.customerRepository.findByPhoneAndCompanyId(
        phoneParts.phoneNumber,
        data.companyId,
      );

    if (existingCustomer) {
      const updateField =
        customerType === 'loopy' ? 'loopyCustomerId' : 'brandWalletCustomerId';

      await this.customerRepository.updateOne(
        { _id: existingCustomer._id },
        {
          $set: {
            isMigrated: true,
            [updateField]: data.customerId,
          },
        },
      );
      return existingCustomer;
    }

    const brand = await this.brandService.findById(data.brandId);
    const customer: Partial<Customer> = {
      country_code: phoneParts.countryCode,
      phone: phoneParts.phoneNumber,
      ...this.helperSharedService.getFirstAndLastName(data.fullName),
      company: data.companyId,
      activeBrand: this.brandService.toEmbeddedBrandDto(brand),
      createdAt: data.memberSince,
      loyaltyRegistrationAt: data.memberSince,
      language: data.language,
      isMigrated: true,
      ...(customerType === 'loopy'
        ? { loopyCustomerId: data.customerId }
        : {
            brandWalletCustomerId: data.customerId,
            brandWalletPassBarcode: data.barcode,
          }),
    };

    return await this.customerRepository.create(customer as Customer);
  }
}
