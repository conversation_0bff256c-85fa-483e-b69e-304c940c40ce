// EBL-3626 - [Loyalty Program] Rename $loyaltyEnrollmentPageLink replacement
// Rename $loyaltyEnrollmentPageLink replacement to $loyaltyRegistrationPageLink

db.triggers.updateMany(
  { replacement: 'loyaltyEnrollmentPageLink' },
  { $addToSet: { replacement: 'loyaltyRegistrationPageLink' } },
);

db.triggers.updateMany(
  { replacement: 'loyaltyEnrollmentPageLink' },
  { $pull: { replacement: 'loyaltyEnrollmentPageLink' } },
);

db.templates.updateMany(
  {
    $or: [
      { 'content.arContent': /loyaltyEnrollmentPageLink/ },
      { 'content.enContent': /loyaltyEnrollmentPageLink/ },
    ],
  },
  [
    {
      $set: {
        'content.arContent': {
          $replaceOne: {
            input: '$content.arContent',
            find: 'loyaltyEnrollmentPageLink',
            replacement: 'loyaltyRegistrationPageLink',
          },
        },
        'content.enContent': {
          $replaceOne: {
            input: '$content.enContent',
            find: 'loyaltyEnrollmentPageLink',
            replacement: 'loyaltyRegistrationPageLink',
          },
        },
      },
    },
  ],
);
