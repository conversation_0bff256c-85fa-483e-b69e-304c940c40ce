import { Store } from '../../../../../store/models/store.model';
import { CreateOrdableProductsDto } from '../../dtos/products/create-ordable-products.dto';
import { UpdateOrdableProductsDto } from '../../dtos/products/update-ordable-products.dto';

export interface OrdableProductsServiceInterface {
  create(
    createOrdableProductsDto: CreateOrdableProductsDto,
    store: Store,
  ): Promise<any>;

  update(
    updateOrdableProductsDto: UpdateOrdableProductsDto,
    store: Store,
  ): Promise<any>;

  findAll(store: Store): Promise<any>;
}
