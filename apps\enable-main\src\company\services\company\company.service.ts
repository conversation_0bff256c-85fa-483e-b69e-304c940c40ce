import {
  AcknowledgementType,
  BranchDocument,
  CollectionName,
  CompanyDeliveryTeamToCreate,
  CompanyDeliveryTeamToEdit,
  CompanyDocument,
  CompanyIntegratorChannelEnum,
  CompanyStatusToUpdate,
  CompanyToIndex,
  CompanyToUpdate,
  CompanyType,
  CompanyWebhook,
  countries,
  CountriesNameToIsoCodeMapping,
  CountryDialCode,
  CreateCompanyDto,
  CreateLoyaltyProgramConfig,
  Currency,
  CurrencySymbol,
  DeliveryMethod,
  ImageToCreate,
  LocationItemDocumentToEmbedded,
  LocationItemType,
  LoggerService,
  LoyaltyProgramConfig,
  LoyaltyTier,
  LoyaltyTierDocument,
  OrderDocument,
  PusherService,
  responseCode,
  RestaurantBusyToBeSet,
  TeamBatteryUsage,
  ThresholdMap,
  TierLevellingUpMethod,
  TierLevellingUpMethodActionMap,
  TierWithRequirements,
  TransactionScreenScheme,
  UnreachableError,
  UpdateCompanyConfigDto,
  UpdateCompanyLocalizationDto,
  UpdateLoyaltyProgramConfigDto,
  BranchAssignmentScheme,
} from '@app/shared-stuff';
import {
  BadRequestException,
  forwardRef,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import * as jwt from 'jsonwebtoken';
import * as moment from 'moment-timezone';
import { FilterQuery, isObjectIdOrHexString, Model, Types } from 'mongoose';
import * as randomstring from 'randomstring';
import { DeliveryConfigurationServiceInterface } from '../../../delivery/services/delivery-configuration/delivery-configuration.service.interface';
import { LocationItemServiceInterface } from '../../../location/services/location-item/location-item-service.interface';
import { LoyaltyTierReadServiceInterface } from '../../../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.service.interface';
import { CallCenterProvider } from '../../../user/enums/call-center-provider.enum';
import { CalendarSystemService } from '../calendar-system/calendar-system.service';
import {
  TookanTeamToCreate,
  TookanTeamToEdit,
} from './../../../delivery/dto/tookan.dto';
import { TookanService } from './../../../delivery/services/tookan/tookan.service';
import { ImageService } from './../../../shared/services/image/image.service';
import { UserService } from './../../../user/services/user/user.service';
import { CompanyConfigService } from './../companyConfig/companyConfig.service';

@Injectable()
export class CompanyService {
  private readonly logger = new LoggerService(CompanyService.name);

  constructor(
    @InjectModel(CollectionName.COMPANY)
    private companyModel: Model<CompanyDocument>,
    private imageService: ImageService,
    private configService: ConfigService,
    private tookanService: TookanService,
    private userService: UserService,
    private eventEmitter: EventEmitter2,
    private companyConfig: CompanyConfigService,
    @Inject(DeliveryConfigurationServiceInterface)
    private readonly deliveryConfigurationService: DeliveryConfigurationServiceInterface,
    @InjectModel(CollectionName.BRANCH)
    private branchModel: Model<BranchDocument>,
    @Inject(LocationItemServiceInterface)
    private readonly locationItemService: LocationItemServiceInterface,
    private readonly pusherService: PusherService,
    @Inject(forwardRef(() => LoyaltyTierReadServiceInterface))
    private readonly loyaltyTierReadService: LoyaltyTierReadServiceInterface,
    private readonly calendarSystemService: CalendarSystemService,
  ) {}

  async index(
    companyToIndex: CompanyToIndex,
    companyIds?: Types.ObjectId[],
    companyId?: Types.ObjectId,
  ) {
    const match: unknown = {};
    /**
     * Available sortings:
     *    [ "status", "year", "day", "week",
     *      "number_of_users", "name", "acronym",
     *      "email", "phone", "total_roles", "total_users",
     *      "in_opreation_since", "hasOwnDrivers",
     *      "using3rdPartyDelivery",
     *      "usingEbDelivery", "deliveryParty"
     *      "total_revenue" "total_orders", "total_branches",
     *      "ordersUpdates", "number_of_tookan_tasks_own",
     *      "number_of_tookan_tasks_ebutler", "number_of_payments",
     *      "number_of_orders", "number_of_drivers", "numberOfEmployee",
     *      "new_orders", "new_notifications", "new_leads",
     *      "hasNotificationTemplates",  "usingSMSNotifications",
     *      "smsFlow",  "call_back_url" "createdAt", "updatedAt"]
     *
     * sorting takes place in this form "key:value"
     * for example "name:asc" or "name:desc"
     * if value is omitted or have any value that is not "desc",
     * will be considered "asc"
     */
    const sort: unknown = {};
    this.matchById(match, companyId, companyIds);

    if (companyToIndex.search_type) {
      /**
       * Available search fields:
       *    ["name", "email", "phone", "month", "acronym", 'in_opreation_since', 'description']
       * special search key is "all".
       */
      const availableFields = [
        'name',
        'email',
        'phone',
        'month',
        'acronym',
        'in_opreation_since',
        'description',
      ];
      if (
        availableFields.includes(companyToIndex.search_type) &&
        companyToIndex.search_key
      ) {
        match[companyToIndex.search_type] = {
          $regex: companyToIndex.search_key,
          $options: 'i',
        };
      } else if (companyToIndex.search_type === 'all') {
        match['$or'] = availableFields.map((field) => {
          return {
            [field]: {
              $regex: companyToIndex.search_key,
              $options: 'i',
            },
          };
        });
      }
    }

    // if client is using old sorting api
    if (companyToIndex.sort_type) {
      const sort_types = {
        name_desc: { name: -1 },
        name_asc: { name: 1 },
        revenue_asc: { total_revenue: 1 },
        revenue_desc: { total_revenue: -1 },
      };

      const key = Object.keys(sort_types[companyToIndex.sort_type])[0];
      const value = sort_types[companyToIndex.sort_type][key];
      if (value) {
        sort[key] = value;
      }

      // if client is using new sorting api
    } else if (companyToIndex['sortBy']) {
      const parts = companyToIndex['sortBy'].split(':');
      sort[parts[0]] = parts[1] === 'desc' ? -1 : 1;
    } else {
      // default sorting
      // if user is not providing a sorting type
      sort['createdAt'] = -1;
    }

    match['deletedAt'] = { $eq: null };
    const pipeline = [
      { $match: match },
      { $sort: { _id: 1 } },
      { $sort: sort },
      ...(Number(companyToIndex.offset) || Number(companyToIndex.offset) == 0
        ? [
            {
              $skip:
                Number(companyToIndex.offset) * Number(companyToIndex.limit),
            },
          ]
        : []),
      ...(Number(companyToIndex.limit)
        ? [
            {
              $limit: Number(companyToIndex.limit),
            },
          ]
        : []),
    ];

    const companies = await this.companyModel.aggregate(pipeline as any);
    return companies;
  }

  async create(createCompanyDto: CreateCompanyDto) {
    createCompanyDto['smsFlow'] = 'newFlow';
    this.ensureValidCompanyData(createCompanyDto);

    if (createCompanyDto.type !== CompanyType.RESTAURANT)
      // NOTE : this only a quick fix shall we uncomment the following logic after FE refactor
      // if (
      //   companyToCreate.type === CompanyType.RETAIL &&
      //   companyToCreate.acknowledgementScreenType !==
      //     AcknowledgementType.AUTOMATIC
      // ) {
      //   throw new BadRequestException(
      //     'acknowledgementScreenType for Retail companies must be AUTOMATIC.',
      //     responseCode.IN_VALID_INPUT.toString(),
      //   );
      // }

      createCompanyDto.acknowledgementScreenType =
        AcknowledgementType.AUTOMATIC;

    await this.userService.validateUserData({
      email: createCompanyDto.email,
      phone: createCompanyDto.phone,
    });
    const createdCompany = new this.companyModel(createCompanyDto);
    if (createCompanyDto.image) {
      createCompanyDto.image.for = 'company';
      createdCompany.image = await this.imageService.uploadImage(
        createCompanyDto.image,
      );
    }
    createdCompany.interfaceConfig = {
      orderConfig: {
        paymentMethodsConfig: {
          prepaidOptions: ['customer', 'company', 'enable'],
        },
        invoicesConfig: { template: 'a4' },
      },
      callCenterConfig: {
        invoicesConfig: { template: 'a4' },
      },
      mobileAppConfig: {
        transactionScreenScheme: TransactionScreenScheme.PLACING_AN_ORDER,
      },
    };
    createdCompany.createdBy = createCompanyDto.current_user;
    createdCompany.branchAssignmentConfig = {
      branchAssignmentScheme: BranchAssignmentScheme.MANUAL,
    };
    await createdCompany.save();

    const user = await this.createDefaultUser(
      createdCompany,
      createCompanyDto.defaultUserPassword,
    );
    this.eventEmitter.emit('company.createDefaultBranch', {
      company: createdCompany,
      companyToCreate: createCompanyDto,
      user,
    });
    this.eventEmitter.emit('company.created', createdCompany);

    if (createCompanyDto.has_delivery_system) {
      const tookanTeamToCreate: CompanyDeliveryTeamToCreate = {
        team_name: createdCompany.name + ' ' + randomstring.generate(5),
        company: createdCompany._id,
        tags: `${createdCompany._id},${createdCompany.name}`,
        battery_usage: 'Medium',
      };
      await this.createTookanTeam(tookanTeamToCreate).catch((error) =>
        this.logger.error(
          `Tookan Team failed to create for company ${createdCompany._id}: ${error.message}`,
          error.stack,
        ),
      );
    }

    await this.companyConfig.createDefaultOne(createdCompany._id);
    await this.deliveryConfigurationService.create({
      defaultDeliveryMethod: DeliveryMethod.BRANCH_DRIVERS,
      usingBranchDrivers: true,
      companyId: createdCompany._id,
    });

    return createdCompany;
  }

  async createDefaultUser(
    createdCompany: CompanyDocument,
    password = '123456',
  ) {
    //create user from userToCreate
    const createdUser = await this.userService.create(
      {
        name: 'admin',
        email: createdCompany.email,
        gender: 'male',
        phone: createdCompany.phone,
        password: password,
        position: 'Company Administrator',
        status: 'active',
        company: new Types.ObjectId(createdCompany._id) as any,
        image_to_create: null,
        companies: [],
        isEnableUser: false,
        role: undefined,
        callCenterProvider: CallCenterProvider.MAQSAM,
        countryCode: createdCompany.localization?.countryCode
          ? createdCompany.localization?.countryCode
          : CountryDialCode.QATAR,
      },
      null,
    );
    // assign super admin to user TODO:[revisit this task][NOTE: ROLE_ID hard coded]
    await this.userService.add_role_to_user(
      createdUser._id,
      new Types.ObjectId('5f172ce60564f853cc1eca86'),
      null,
    );
    return createdUser;
  }

  async update(companyToUpdate: CompanyToUpdate) {
    if (companyToUpdate.image_to_update) {
      const imageToCreate: ImageToCreate = {
        name: companyToUpdate.name || randomstring.generate(10),
        alt: companyToUpdate.image_to_update.alt,
        description: companyToUpdate.image_to_update.description,
        for: companyToUpdate.image_to_update.for,
        base64: companyToUpdate.image_to_update.base64,
      };
      companyToUpdate['image'] =
        await this.imageService.uploadImage(imageToCreate);
    }
    const updatedCompany = await this.companyModel.findById(
      companyToUpdate._id,
    );
    for (const key in companyToUpdate) {
      updatedCompany[key] = companyToUpdate[key];
    }
    updatedCompany.updatedBy = companyToUpdate.current_user;
    await updatedCompany.save();
    return updatedCompany;
  }

  async updateCompanyConfig(updateCompanyConfigDto: UpdateCompanyConfigDto) {
    const company = await this.findById(updateCompanyConfigDto._id);
    this.ensureValidCompanyData(updateCompanyConfigDto);

    const fieldsToUpdate = [
      'name',
      'email',
      'phone',
      'countryCode',
      'acronym',
      'website',
      'hasNotificationTemplates',
      'has_delivery_system',
      'updatedBy',
      'senderId',
      'acknowledgementScreenType',
      'usingApplePay',
      'type',
      'followBranchMechanism',
      'usingScheduleLater',
      'usingDeliverectPos',
      'deliverectPOSSecret',
      'deliverectPOSClientId',
      'deliverectAccountId',
      'deliverectPosOrderSnooze',
      'deliverectIntegratedChannels',
      'senderId',
      'defaultDeliveryTax',
      'defaultTakeawayTax',
      'isAdlerCompany',
      'adlerCompanyId',
      'adlerSecretAccessKey',
      'isDeliveryMethodChangeable',
      'usingReminderScreen',
      'usingSolutionErpPos',
      'solutionErpApiKey',
      'hasLoyaltyProgram',
      'isThirdPartyFallbackEnabled',
      'analyticsVersion',
      'isBranchRedirectCallEnabled',
      'hardPhoneNumbers',
      'hasRetailModule',
      'hasMicros',
      'hasManualCampaigns',
      'hasRestaurantManagerPos',
      'metabaseDashboardId',
      'interfaceConfig',
      'termsAndConditionsUrl',
    ];

    fieldsToUpdate.forEach((field) => {
      if (updateCompanyConfigDto[field] !== undefined) {
        company[field] = updateCompanyConfigDto[field];
      }
    });

    if (updateCompanyConfigDto.webhook) {
      company.webhooks = company.webhooks || new CompanyWebhook();
      const webhookTypes = [
        'orderStatusUpdated',
        'paymentStatusUpdated',
        'orderCreated',
      ];
      webhookTypes.forEach((type) => {
        if (updateCompanyConfigDto.webhook.key === type) {
          company.webhooks[type] = updateCompanyConfigDto.webhook.value;
        }
      });
      this.updateCalendarSystem(company, updateCompanyConfigDto);
      await company.save();
    }

    company.integratorChannelConfig =
      updateCompanyConfigDto.integratorChannelConfig;
    if (updateCompanyConfigDto.branchAssignmentConfig) {
      company.branchAssignmentConfig =
        updateCompanyConfigDto.branchAssignmentConfig;
    }

    if (company.hasLoyaltyProgram) {
      company.loyaltyProgramConfig = {
        ...new CreateLoyaltyProgramConfig(),
        ...(updateCompanyConfigDto.loyaltyProgramConfig ?? {}),
        ...(company.loyaltyProgramConfig ?? {}),
      };
    }

    if (
      company.usingDeliverectPos &&
      company.deliverectPOSClientId &&
      company.deliverectPOSSecret
    ) {
      this.eventEmitter.emit('company.deliverect.updated', company);
    }

    if (updateCompanyConfigDto.defaultUserPassword) {
      const user = await this.userService.findOneByEmail(
        company.email,
        company._id,
      );
      if (!user) {
        throw new NotFoundException(
          'User Not Found',
          responseCode.ENTITY_NOT_FOUND.toString(),
        );
      }
      await this.userService.updatePassword(
        user._id,
        updateCompanyConfigDto.defaultUserPassword,
      );
    }

    return company.save();
  }

  async get_details(id: Types.ObjectId | string) {
    const filterOBJ = {
      $or: [{ ebutler_provider_id: id }, { phone: id }],
    } as any;
    if (isObjectIdOrHexString(id)) {
      filterOBJ.$or.push({ _id: new Types.ObjectId(id) });
      filterOBJ.$or.push({ deliverectAccountId: id });
    }
    return await this.companyModel
      .findOne(filterOBJ)
      .select({
        customers: 0,
      })
      .exec();
  }

  async findById(id: Types.ObjectId): Promise<CompanyDocument> {
    const company = await this.companyModel.findById(id).exec();
    if (!company)
      throw new NotFoundException(
        'Company Not Exist',
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    return company;
  }

  async remove(id: string, deletedBy: Record<any, any>) {
    const removedCompany = await this.companyModel.findOne({ _id: id });
    removedCompany.deletedAt = moment().toDate();
    removedCompany.deletedBy = deletedBy;
    await removedCompany.save();
    return removedCompany;
  }

  async change_status(companyStatusToUpdate: CompanyStatusToUpdate) {
    const updatedCompnay = await this.companyModel.findOneAndUpdate(
      { _id: companyStatusToUpdate._id },
      { status: companyStatusToUpdate.status },
    );
    return updatedCompnay;
  }

  async add_branch_to_company(
    branch_id: Types.ObjectId,
    company_id: Types.ObjectId,
  ) {
    const selectedCompany = await this.companyModel.findOne({
      _id: company_id,
    });
    selectedCompany.branches.push(branch_id);
    selectedCompany.total_branches = selectedCompany.total_branches + 1;
    await selectedCompany.save();
    return selectedCompany;
  }

  async assignOrderToCompany(order: OrderDocument, company: CompanyDocument) {
    company.total_orders = company.total_orders + 1;
    company.total_revenue = company.total_revenue + (order.total_amount || 0);

    await company.save();
    return company;
  }

  async createTookanTeam(deliveryTeamToCreate: CompanyDeliveryTeamToCreate) {
    const company = await this.companyModel.findOne({
      _id: deliveryTeamToCreate.company,
    });
    const tookanTeamToCreate: TookanTeamToCreate = {
      api_key: this.configService.get<string>('TOOKAN_API_KEY'),
      battery_usage: TeamBatteryUsage[deliveryTeamToCreate.battery_usage],
      tags: deliveryTeamToCreate.tags,
      team_name: company.name,
    };
    const tookanTeamData =
      await this.tookanService.createTookanTeam(tookanTeamToCreate);
    company.tookan_team_id = tookanTeamData['team_id'];
    company.tookan_team = {
      battery_usage: TeamBatteryUsage[deliveryTeamToCreate.battery_usage],
      battery_usage_name: deliveryTeamToCreate.battery_usage,
      tags: deliveryTeamToCreate.tags,
      team_name: company.name,
    };
    await company.save();
    return tookanTeamData;
  }

  async editTookanTeam(teamToEdit: CompanyDeliveryTeamToEdit) {
    const company = await this.companyModel.findOne({
      _id: teamToEdit.company,
    });
    const tookanTeamToCreate: TookanTeamToEdit = {
      api_key: this.configService.get<string>('TOOKAN_API_KEY'),
      battery_usage: TeamBatteryUsage[teamToEdit.battery_usage],
      tags: teamToEdit.tags,
      team_name: teamToEdit.team_name,
      team_id: parseInt(company.tookan_team_id),
    };
    const tookanTeamData =
      await this.tookanService.updateTookanTeam(tookanTeamToCreate);
    company.tookan_team = {
      battery_usage: TeamBatteryUsage[teamToEdit.battery_usage],
      tags: teamToEdit.tags,
      team_name: teamToEdit.team_name,
      battery_usage_name: teamToEdit.battery_usage,
    };
    // company.tookan_team_id = tookanTeamData['team_id'];
    await company.save();
    return tookanTeamData;
  }

  async setCompanyBusy(companyBusyToBeSet: RestaurantBusyToBeSet) {
    const company = await this.get_details(companyBusyToBeSet.companyId);
    if (!company) {
      return;
    }

    if (
      companyBusyToBeSet.currentUser['role'] != 'Branch Manager' ||
      company.branchAssignmentConfig.branchAssignmentScheme ===
        BranchAssignmentScheme.MANUAL
    ) {
      company.isRestaurantBusy = companyBusyToBeSet.isRestaurantBusy;
      await company.save();
    } else {
      // Setting All The branches of the Branch Manager As Busy
      for (let i = 0; i < companyBusyToBeSet.branches.length; i++) {
        const branch = await this.branchModel.findOne({
          _id: companyBusyToBeSet.branches[i],
        });
        if (branch) {
          branch.isRestaurantBusy = companyBusyToBeSet.isRestaurantBusy;
          await branch.save();
        }
      }
    }
  }

  async getCompanyConfig(companyId: string) {
    const _id = new Types.ObjectId(companyId);
    const companyConfigs = await this.companyConfig.index({ company: _id });
    return companyConfigs[0];
  }

  async increaseNumberOfBrands(companyId: Types.ObjectId) {
    await this.companyModel.updateOne(
      {
        _id: companyId,
      },
      { $inc: { numberOfBrands: 1 } },
    );
  }

  async updatedCompanyDeliverectInfo(companyId: Types.ObjectId) {
    return await this.companyModel.updateOne(
      { _id: companyId },
      {
        $set: {
          usingDeliverectChannel: true,
        },
      },
    );
  }

  findAllCurrencies() {
    return Object.keys(Currency);
  }

  async findAll(filter: FilterQuery<CompanyDocument>) {
    return await this.companyModel.find(filter);
  }

  async updateCompanyLocalization(
    updateCompanyLocalizationDto: UpdateCompanyLocalizationDto,
  ): Promise<string> {
    const countryTimezones = moment.tz.zonesForCountry(
      CountriesNameToIsoCodeMapping[
        updateCompanyLocalizationDto.localization.country
      ],
    );
    if (
      !countryTimezones.includes(
        updateCompanyLocalizationDto.localization.timezone,
      )
    )
      throw new BadRequestException(
        'Timezone must be included into the country timezones',
        responseCode.IN_VALID_INPUT.toString(),
      );

    const country = await this.locationItemService.findOneByNameAndType(
      updateCompanyLocalizationDto.localization.country,
      LocationItemType.COUNTRY,
    );

    if (country) {
      updateCompanyLocalizationDto.localization.countryItem =
        LocationItemDocumentToEmbedded([country])[0];
    }

    const countryDetails = countries.find(
      (x) => x.name == updateCompanyLocalizationDto.localization.country,
    );

    if (countryDetails) {
      updateCompanyLocalizationDto.localization.countryCode =
        countryDetails.dialCode as CountryDialCode;
      updateCompanyLocalizationDto.localization.countryAcronym =
        countryDetails.isoCode as CountriesNameToIsoCodeMapping;
    }

    const company = await this.companyModel.findById(
      updateCompanyLocalizationDto.companyId,
    );
    if (!company)
      throw new BadRequestException(
        'Company Not Found',
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    await this.companyModel.updateOne(
      { _id: company._id },
      { $set: { localization: updateCompanyLocalizationDto.localization } },
    );
    await this.eventEmitter.emit('company.localization.updated', company);
    return 'Company with ID ' + company._id + ' updated successfully';
  }

  async incrementNumberOfOrders(
    companyId: Types.ObjectId,
  ): Promise<CompanyDocument> {
    return this.companyModel.findOneAndUpdate(
      { _id: companyId },
      { $inc: { number_of_orders: 1 } },
    );
  }

  async findByMicrosKeyId(keyId: string): Promise<CompanyDocument> {
    const company = await this.companyModel.findOne({
      integratorChannelConfig: {
        [CompanyIntegratorChannelEnum.MICROS]: { $exists: true },
      },
      'integratorChannelConfig.keyId': keyId,
    });

    return company;
  }

  private ensureValidCompanyData(
    newCompany: CreateCompanyDto | UpdateCompanyConfigDto,
  ): void {
    if (
      newCompany.usingDeliverectPos &&
      (!newCompany.deliverectAccountId ||
        !newCompany.deliverectPOSClientId ||
        !newCompany.deliverectPOSSecret)
    ) {
      throw new HttpException(
        'Please add the deliverect POS data',
        HttpStatus.NOT_FOUND,
      );
    }

    if (
      '_id' in newCompany &&
      newCompany.usingSolutionErpPos &&
      !newCompany.solutionErpApiKey
    )
      throw new BadRequestException(
        'Please add the API key of Solution ERP',
        responseCode.IN_VALID_INPUT.toString(),
      );

    if (
      newCompany.type === CompanyType.RESTAURANT &&
      newCompany.hasRetailModule === true
    ) {
      throw new BadRequestException(
        'Cannot have a company with type restaurant and retail module enabled',
        responseCode.IN_VALID_INPUT.toString(),
      );
    }
  }

  private updateCalendarSystem(
    company: CompanyDocument,
    { loyaltyProgramConfig }: { loyaltyProgramConfig?: LoyaltyProgramConfig },
  ): void {
    const hasCalendarSystemChanged =
      company?.loyaltyProgramConfig?.calendarSystem &&
      loyaltyProgramConfig?.calendarSystem &&
      company.loyaltyProgramConfig.calendarSystem !==
        loyaltyProgramConfig.calendarSystem;
    if (!hasCalendarSystemChanged) return;

    company.loyaltyProgramConfig.calendarSystem =
      loyaltyProgramConfig.calendarSystem;
    company.markModified('loyaltyProgramConfig');
    this.eventEmitter.emit('company.calendarSystem.updated', company);
  }

  public async updateLoyaltyProgramConfig(
    companyId: Types.ObjectId,
    updateLoyaltyProgramConfigDto: UpdateLoyaltyProgramConfigDto,
  ) {
    const company = await this.findById(companyId);

    this.validateLoyaltyProgramConfigUpdate(
      company,
      updateLoyaltyProgramConfigDto,
    );

    const oldLoyaltyProgramConfig = company.loyaltyProgramConfig;
    const newLoyaltyProgramConfig = await this.applyLoyaltyProgramConfigUpdates(
      company,
      updateLoyaltyProgramConfigDto,
    );

    company.termsAndConditionsUrl =
      updateLoyaltyProgramConfigDto.termsAndConditionsUrl ??
      company.termsAndConditionsUrl;
    company.loyaltyProgramConfig = newLoyaltyProgramConfig;
    await company.save();

    const isCalendarSystemUpdated =
      oldLoyaltyProgramConfig?.calendarSystem !==
      newLoyaltyProgramConfig.calendarSystem;
    if (isCalendarSystemUpdated)
      this.eventEmitter.emit('company.calendarSystem.updated', company);

    this.pusherService.fireEvent(
      this.pusherService.getCompanyChannelName(company),
      'companyLoyaltyProgramConfigUpdated',
      { loyaltyProgramConfig: newLoyaltyProgramConfig },
    );

    return company;
  }

  public async findManualAcknowledgementCompanyIds(): Promise<
    Types.ObjectId[]
  > {
    return this.companyModel.distinct('_id', {
      acknowledgementScreenType: AcknowledgementType.MANUAL,
    });
  }

  private validateLoyaltyProgramConfigUpdate(
    { hasLoyaltyProgram, loyaltyProgramConfig }: CompanyDocument,
    updateLoyaltyProgramConfigDto: UpdateLoyaltyProgramConfigDto,
  ) {
    if (!hasLoyaltyProgram)
      throw new BadRequestException('Please toggle company.hasLoyaltyProgram');

    const hasPoints =
      updateLoyaltyProgramConfigDto.hasLoyaltyPoints ||
      (!('hasLoyaltyPoints' in updateLoyaltyProgramConfigDto) &&
        loyaltyProgramConfig?.hasLoyaltyPoints);
    if (
      !hasPoints &&
      loyaltyProgramConfig?.tierLevellingUpMethod !==
        TierLevellingUpMethod.POINTS_RATE &&
      updateLoyaltyProgramConfigDto.tierLevellingUpMethod ===
        TierLevellingUpMethod.POINTS_RATE
    )
      throw new BadRequestException(
        'Please enable points toggle to use points rate levelling up method.',
      );

    const isUsingPointsRate =
      loyaltyProgramConfig?.tierLevellingUpMethod ===
        TierLevellingUpMethod.POINTS_RATE &&
      loyaltyProgramConfig?.hasLoyaltyPoints;

    const isDisablingPointsToggle =
      'hasLoyaltyPoints' in updateLoyaltyProgramConfigDto &&
      !updateLoyaltyProgramConfigDto.hasLoyaltyPoints;

    const isChangingTierLevellingUpMethod =
      updateLoyaltyProgramConfigDto.tierLevellingUpMethod &&
      updateLoyaltyProgramConfigDto.tierLevellingUpMethod !==
        TierLevellingUpMethod.POINTS_RATE;

    if (
      isUsingPointsRate &&
      isDisablingPointsToggle &&
      !isChangingTierLevellingUpMethod
    )
      throw new BadRequestException(
        `Cannot disable points since points rate levelling up method is being used.`,
      );

    if (
      updateLoyaltyProgramConfigDto.hasFlexiblePointsRedemption &&
      !updateLoyaltyProgramConfigDto.redemptionExchangeRate &&
      !loyaltyProgramConfig.redemptionExchangeRate
    )
      throw new BadRequestException(
        'Cannot enable flexible points redemption without configuring redemptionExchangeRate',
      );
  }

  private async applyLoyaltyProgramConfigUpdates(
    company: CompanyDocument,
    { tiers, ...loyaltyProgramConfig }: UpdateLoyaltyProgramConfigDto,
  ): Promise<LoyaltyProgramConfig> {
    const isTierLevellingUpMethodChanged =
      company.loyaltyProgramConfig?.tierLevellingUpMethod &&
      loyaltyProgramConfig.tierLevellingUpMethod &&
      company.loyaltyProgramConfig.tierLevellingUpMethod !==
        loyaltyProgramConfig.tierLevellingUpMethod;

    const newLoyaltyProgramConfig = {
      ...(company.loyaltyProgramConfig || new CreateLoyaltyProgramConfig()),
      ...loyaltyProgramConfig,
    };

    if (
      newLoyaltyProgramConfig.hasLoyaltyPunchCards &&
      newLoyaltyProgramConfig.hasLoyaltyTiers &&
      [TierLevellingUpMethod.ORDER_RATE, TierLevellingUpMethod.BOTH].includes(
        newLoyaltyProgramConfig.tierLevellingUpMethod,
      )
    )
      throw new BadRequestException(
        "Tier and Punch Card toggles can only be enabled together if tierLevellingUpMethod is not 'order rate' and 'order rate and amount spent' . [ECRM-684][ECRM-838]",
      );

    if (
      isTierLevellingUpMethodChanged &&
      newLoyaltyProgramConfig.tierLevellingUpMethod ===
        TierLevellingUpMethod.POINTS_RATE &&
      !newLoyaltyProgramConfig.hasLoyaltyPoints
    ) {
      throw new BadRequestException(
        'Cannot use points levelling up method without points toggle.',
      );
    }

    if (isTierLevellingUpMethodChanged) {
      await this.validateTierLevellingUpMethod(
        company._id,
        company.loyaltyProgramConfig.tierLevellingUpMethod,
        loyaltyProgramConfig.tierLevellingUpMethod,
        tiers || [],
      );
      if (tiers?.length)
        this.eventEmitter.emit(
          'company.loyaltyProgramConfig.tierLevellingUpMethod.updated',
          company,
          tiers,
        );
    }

    return newLoyaltyProgramConfig;
  }

  private async validateTierLevellingUpMethod(
    companyId: Types.ObjectId,
    oldMethod: TierLevellingUpMethod,
    newMethod: TierLevellingUpMethod,
    tiers: TierWithRequirements[],
  ) {
    const oldThresholds = ThresholdMap[oldMethod];
    const newThresholds = ThresholdMap[newMethod];

    const addedThresholds = newThresholds.filter(
      (threshold) => !oldThresholds.includes(threshold),
    );
    const needsValidation = addedThresholds?.length;
    if (!needsValidation && !tiers?.length) return;

    if (needsValidation)
      this.validateNoMissingThreshold(addedThresholds, tiers);

    const existingTiers =
      await this.loyaltyTierReadService.findNonVipTiersByCompanyId(companyId);

    if (needsValidation) this.validateNoMissingTier(existingTiers, tiers);
  }

  private validateNoMissingThreshold(
    addedThresholds: (keyof LoyaltyTier)[],
    tiers: TierWithRequirements[],
  ) {
    const missingThreshold = addedThresholds.find((threshold) =>
      tiers.some((tier) => !tier[threshold]),
    );

    if (missingThreshold)
      throw new BadRequestException(
        `Provide new threshold values for ${missingThreshold} for all existing tiers.`,
      );
  }

  private validateNoMissingTier(
    existingTiers: LoyaltyTierDocument[],
    tiers: TierWithRequirements[],
  ) {
    const missingTier = existingTiers.find((tier) =>
      tiers.every((t) => !tier._id.equals(t._id)),
    );

    if (missingTier)
      throw new BadRequestException(
        `Provide new threshold values for tier ${missingTier.nameEn} with ID ${missingTier._id}.`,
      );
  }

  private matchById(
    match: unknown,
    companyId: Types.ObjectId,
    companyIds: Types.ObjectId[],
  ) {
    if (companyId) match['_id'] = companyId;
    if (!companyId && companyIds && companyIds.length != 0) {
      match['_id'] = { $in: companyIds };
    }
  }

  public async evaluateTierLevellingUpMethod<T>(
    company: CompanyDocument,
    actionMap: TierLevellingUpMethodActionMap<T>,
  ): Promise<T> {
    const tierLevellingUpMethod =
      company.loyaltyProgramConfig?.tierLevellingUpMethod;

    switch (tierLevellingUpMethod) {
      case TierLevellingUpMethod.ORDER_RATE:
        return await actionMap.orderRate();
      case TierLevellingUpMethod.AMOUNT_SPENT:
        return await actionMap.amountSpent();
      case TierLevellingUpMethod.POINTS_RATE:
        return await actionMap.pointsRate();
      case TierLevellingUpMethod.BOTH:
        return await this.evaluateTierLevellingUpMethodBoth(actionMap);
      default:
        return await this.evaluateTierLevellingUpMethodNone(
          tierLevellingUpMethod,
          actionMap.fallback,
        );
    }
  }

  private async evaluateTierLevellingUpMethodBoth(
    actionMap: TierLevellingUpMethodActionMap,
  ) {
    if (actionMap.both) return await actionMap.both();
    const orderRateResult = await actionMap.orderRate();
    if (typeof orderRateResult === 'number') {
      return Math.max(orderRateResult, await actionMap.amountSpent());
    } else {
      return orderRateResult || (await actionMap.amountSpent());
    }
  }

  private async evaluateTierLevellingUpMethodNone(
    tierLevellingUpMethod: never,
    fallback: any,
  ) {
    if (fallback !== undefined) {
      this.logger.warn(
        `Missing or misconfigured tierLevellingUpMethod: ${tierLevellingUpMethod}`,
      );
      return fallback;
    } else throw new UnreachableError(tierLevellingUpMethod);
  }

  public getCurrencySymbol(company: CompanyDocument): CurrencySymbol {
    const currency = company.localization?.currency;
    if (!currency) return CurrencySymbol.QAR;
    if (currency in CurrencySymbol) return CurrencySymbol[currency];

    this.logger.warn(
      `Company ${company._id} currency ${currency} does not have currency symbol defined`,
    );
    return currency as any as CurrencySymbol;
  }

  async findByBranchId(branchId: Types.ObjectId) {
    return await this.companyModel.findOne({ branches: branchId });
  }

  async deleteBranchFromCompany(branchId: Types.ObjectId) {
    await this.companyModel.updateOne(
      { branches: branchId },
      { $pull: { branches: branchId } },
    );
  }

  async getMetabaseIframeUrl(companyId: Types.ObjectId): Promise<string> {
    const { metabaseDashboardId } = await this.get_details(
      companyId.toHexString(),
    );
    const payload = {
      resource: { dashboard: metabaseDashboardId },
      params: {},
      exp: Math.round(Date.now() / 1000) + 24 * 60 * 60, // 24 hours expiration
    };

    const token = jwt.sign(
      payload,
      this.configService.get('METABASE_SECRET_KEY'),
    );
    const iframeUrl = `${this.configService.get('METABASE_SITE_URL')}/embed/dashboard/${token}#theme=transparent&bordered=true&titled=false`;

    return iframeUrl;
  }

  async findByDefaultBranchId(_id: Types.ObjectId): Promise<CompanyDocument> {
    return this.companyModel.findOne({
      'branchAssignmentConfig.defaultBranchId': _id,
    });
  }

  public hasCarryOver(company: CompanyDocument): boolean {
    const isRelativeCalendarSystem =
      this.calendarSystemService.isRelativeCalendarSystem(
        company.loyaltyProgramConfig.calendarSystem,
      );
    if (isRelativeCalendarSystem) return false;

    return company.loyaltyProgramConfig.canTierProgressCarryOver;
  }
}
