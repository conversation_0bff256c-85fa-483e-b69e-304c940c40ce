import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { DeliveryThirdPartyName, GenericRepository } from '@app/shared-stuff';
import {
  DeliveryThirdParty,
  DeliveryThirdPartyDocument,
} from '../../models/delivery-third-party.model';
import { DeliveryThirdPartyRepositoryInterface } from './delivery-third-party.repositories.interface';

@Injectable()
export class DeliveryThirdPartyRepository
  extends GenericRepository<DeliveryThirdPartyDocument, DeliveryThirdParty>
  implements DeliveryThirdPartyRepositoryInterface
{
  constructor(
    @InjectModel(DeliveryThirdParty.name)
    private deliveryThirdPartyModel: Model<
      DeliveryThirdPartyDocument,
      DeliveryThirdParty
    >,
  ) {
    super(deliveryThirdPartyModel);
  }

  async findByNameIn(
    deliveryThirdPartyNames: DeliveryThirdPartyName[],
  ): Promise<DeliveryThirdPartyDocument[]> {
    return await this.deliveryThirdPartyModel.find({
      name: { $in: deliveryThirdPartyNames },
    });
  }

  async findByName(
    deliveryThirdPartyName: DeliveryThirdPartyName,
  ): Promise<DeliveryThirdPartyDocument[]> {
    return await this.deliveryThirdPartyModel.find({
      name: deliveryThirdPartyName,
    });
  }

  async findByCompanyId(
    companyId: Types.ObjectId,
  ): Promise<DeliveryThirdPartyDocument[]> {
    return await this.deliveryThirdPartyModel.find({
      companyId,
      deletedAt: null,
    });
  }
}
