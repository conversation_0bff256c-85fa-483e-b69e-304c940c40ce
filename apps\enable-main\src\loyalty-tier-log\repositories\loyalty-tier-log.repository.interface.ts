import {
  CalendarCycle,
  CustomerDocument,
  IGenericRepository,
  LoyaltyTierLog,
  LoyaltyTierLogDocument,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface LoyaltyTierLogRepositoryInterface
  extends IGenericRepository<LoyaltyTierLogDocument, LoyaltyTierLog> {
  findHighestAssignedTierId(
    customer: CustomerDocument,
    calendarCycle: CalendarCycle,
  ): Promise<Types.ObjectId>;
}

export const LoyaltyTierLogRepositoryInterface = Symbol(
  'LoyaltyTierLogRepositoryInterface',
);
