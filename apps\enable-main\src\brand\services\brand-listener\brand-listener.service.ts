import { BranchDocument, EmbeddedBranchDto } from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { BrandRepositoryInterface } from '../../repositories/brand.repository.interface';
import { BrandServiceInterface } from '../brand/brand.service.interface';

@Injectable()
export class BrandListenerService {
  constructor(
    @Inject('BrandRepositoryInterface')
    private readonly brandRepository: BrandRepositoryInterface,
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandServiceInterface,
  ) {}

  @OnEvent('branch.created')
  async onBranchCreated(branch: BranchDocument) {
    const existingBrandIds = branch.brands?.map((brand) => brand._id) || [];
    await this.brandRepository.addBranchToBrands(
      this.getEmbeddedBranchDto(branch),
      existingBrandIds,
    );
  }

  @OnEvent('branch.updated')
  async onBranchUpdated(branch: BranchDocument) {
    const existingBrandIds = branch.brands?.map((brand) => brand._id) || [];
    await this.brandRepository.dropBranchFromBrands(
      branch._id,
      existingBrandIds,
    );

    const embeddedBranchDto = this.getEmbeddedBranchDto(branch);
    await this.brandRepository.addBranchToBrands(
      embeddedBranchDto,
      existingBrandIds,
    );
    await this.brandRepository.updateBranchOnBrands(embeddedBranchDto);
  }

  private getEmbeddedBranchDto(branch: BranchDocument): EmbeddedBranchDto {
    return {
      _id: branch._id,
      name: branch.name,
    };
  }
}
