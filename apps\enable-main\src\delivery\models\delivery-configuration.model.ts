import {
  CollectionName,
  DeliveryMethod,
  EnumField,
  Field,
  ThirdPartyConfiguration,
} from '@app/shared-stuff';
import { Schema, SchemaFactory } from '@nestjs/mongoose';
import { IsBoolean, IsEnum, ValidateNested } from 'class-validator';
import { Document, Types } from 'mongoose';

export type DeliveryConfigurationWithId = DeliveryConfiguration & {
  _id: Types.ObjectId;
};
export type DeliveryConfigurationDocument = DeliveryConfiguration & Document;

@Schema({ timestamps: true })
export class DeliveryConfiguration {
  @Field({
    type: Boolean,
    required: false,
    default: false,
    additionalDecorators: [IsBoolean()],
  })
  usingBranchDrivers? = false;

  @Field({
    type: Boolean,
    required: false,
    default: false,
    additionalDecorators: [IsBoolean()],
  })
  usingCompanyDrivers? = false;

  @Field({
    type: Boolean,
    required: false,
    default: false,
    additionalDecorators: [IsBoolean()],
  })
  usingThirdParty? = false;

  @Field({
    type: Boolean,
    required: false,
    default: false,
    additionalDecorators: [IsBoolean()],
  })
  usingEbDelivery? = false;

  @EnumField({
    type: String,
    required: false,
    enum: DeliveryMethod,
    additionalDecorators: [IsEnum(DeliveryMethod)],
  })
  defaultDeliveryMethod?: DeliveryMethod;

  @Field({
    type: () => ThirdPartyConfiguration,
    required: (deliveryConfiguration: DeliveryConfiguration) =>
      deliveryConfiguration.usingThirdParty === true,
    modelOptions: {
      description: 'it will be required if usingThirdParty equal to true',
    },
  })
  thirdPartyConfiguration?: ThirdPartyConfiguration;

  @Field({
    type: Types.ObjectId,
    modelOptions: {
      ref: CollectionName.COMPANY,
    },
    required: false,
  })
  companyId?: Types.ObjectId;

  @Field({
    type: Types.ObjectId,
    modelOptions: {
      ref: CollectionName.BRAND,
    },
    required: false,
  })
  brandId?: Types.ObjectId;

  @Field({
    type: Types.ObjectId,
    modelOptions: {
      ref: CollectionName.BRANCH,
    },
    required: false,
  })
  branchId?: Types.ObjectId;
}

export const DeliveryConfigurationSchema = SchemaFactory.createForClass(
  DeliveryConfiguration,
);

DeliveryConfigurationSchema.index(
  { companyId: 1, brandId: 1, branchId: 1 },
  { unique: true },
);
