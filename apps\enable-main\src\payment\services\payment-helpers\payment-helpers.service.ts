import {
  BranchWithId,
  CompanyDocument,
  DeliveryMethod,
  OrderDeliveryAction,
  OrderDocument,
  OrderLogActionEnum,
  OrderPaymentStatus,
  Payment,
  PaymentDocument,
  PaymentGatewayConfig,
  PaymentGatewayType,
  PaymentStatusEnum,
  PusherService,
} from '@app/shared-stuff';
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as moment from 'moment-timezone';
import { CustomerReadServiceInterface } from '../../../customer/modules/customer-read/customer-read.service.interface';
import { OrderLogServiceInterface } from '../../../order/services/interfaces/order-log.service.interface';
import { PaymentRepositoryInterface } from '../../repositories/interfaces/payment.repository.interface';
import { PaymentConfigurationServiceInterface } from '../payment-configuration/payment-configuration.service.interface';
import { DriverService } from './../../../delivery/services/driver/driver.service';
import { OrderDeliveryService } from './../../../order/services/order-delivery/order-delivery.service';
import { PaymentNotificationService } from './../payment-notification/payment-notification.service';
import { GetPaymentConfig } from '@app/shared-stuff/types/payment/get-payment-config,type';

@Injectable()
export class PaymentHelpersService {
  constructor(
    private configService: ConfigService,
    private paymentNotification: PaymentNotificationService,
    @Inject(CustomerReadServiceInterface)
    private customerReadService: CustomerReadServiceInterface,
    private driverService: DriverService,
    @Inject(forwardRef(() => OrderDeliveryService))
    private orderDeliveryService: OrderDeliveryService,
    @Inject('OrderLogServiceInterface')
    private orderLogService: OrderLogServiceInterface,
    private eventEmitter: EventEmitter2,
    private pusherService: PusherService,
    @Inject('PaymentRepositoryInterface')
    private paymentRepository: PaymentRepositoryInterface,
    @Inject(PaymentConfigurationServiceInterface)
    private readonly paymentConfigurationService: PaymentConfigurationServiceInterface,
  ) {}

  generateCallBackUrl(
    payment: PaymentDocument,
    company: CompanyDocument,
    responseCode: string,
    order: OrderDocument,
  ) {
    // Payment status NEEDS to be defined before calling this function
    let callbackUrlParameters = `?lang=${
      payment.language == 'arabic' ? 'ar' : 'en'
    }&customer_name=${payment.customer_name}&amount=${
      payment.amount
    }&transaction_id=${payment.transaction_id}&company_name=${
      company.name
    }&payment_code=${payment.code}&transaction_date=${payment.transaction_date.toISOString()}&responsecode=${responseCode}`;

    if (order) {
      callbackUrlParameters += `&udf5=${order.invoice_number}`;
      callbackUrlParameters += `&udf4=${order.invoice_number}`;
    }

    let callbackUrl;

    if (
      payment.status == PaymentStatusEnum.TRANSACTION_COMPLETED ||
      responseCode === '000'
    ) {
      callbackUrl =
        this.configService.get('SUCCESSFULL_RECEIPT_PAGE') +
        callbackUrlParameters;
    } else {
      callbackUrl =
        this.configService.get('UNSUCCESSFULL_RECEIPT_PAGE') +
        callbackUrlParameters;
    }

    if (payment.callback_url && payment.callback_url != 'default') {
      callbackUrl = payment.callback_url + callbackUrlParameters;
    } else if (company.call_back_url != 'default') {
      if (company.call_back_url.indexOf(':id') != -1) {
        callbackUrl = company.call_back_url.replace(':id', payment._id);
      } else {
        callbackUrl = company.call_back_url + callbackUrlParameters;
      }
    }

    return this.reformatCallbackUrl(callbackUrl);
  }

  async saveTransaction(
    payment: PaymentDocument,
    transactionId: string,
    responseData: any,
    processingSource: PaymentGatewayType,
  ): Promise<unknown> {
    if (payment.status == PaymentStatusEnum.TRANSACTION_COMPLETED)
      responseData['ENABLE_NOTE'] =
        'The Payment is Successful and it trying to update to this one';

    payment.transaction_date = moment().toDate();
    payment.transaction_id = transactionId;
    payment.gatewayType = processingSource;
    payment.paymentTries = [...(payment.paymentTries || []), responseData];
    await payment.save();
    return responseData;
  }

  async handlePaymentStatusLogic(
    payment: PaymentDocument,
    status: PaymentStatusEnum,
    order: OrderDocument,
    company: CompanyDocument,
  ) {
    await this.savingNewPaymentStatus(payment.status, status, payment);

    payment.status = status;
    if (payment.status === PaymentStatusEnum.PENDING)
      return await payment.save();

    if (payment.status == PaymentStatusEnum.TRANSACTION_COMPLETED) {
      await this.handlePaymentSuccessful(payment, order);
    } else {
      await this.handlePaymentUnSuccessful(payment, order);
    }
    await payment.save();
    await this.removeLatestPaymentFromTheCustomer(payment);

    // In case there is an order using company drivers
    await this.handleTheTookanTaskUpdate(order, company);
    return payment;
  }

  firePusherEvent(
    callBackURL: string,
    payment: PaymentDocument,
    newPaymentStatus: PaymentStatusEnum,
  ) {
    this.pusherService.fireEvent(payment.code, 'paymentWebhookUpdates', {
      callBackURL: callBackURL,
      code: payment.code,
      status: newPaymentStatus,
    });
  }

  async getPaymentAndConfiguration(
    paymentCode: string,
  ): Promise<GetPaymentConfig> {
    const payment =
      await this.paymentRepository.findByCodeWithBranch(paymentCode);
    if (!payment) return;

    const paymentConfig =
      await this.paymentConfigurationService.findPaymentConfig(
        payment.branch?._id,
        payment.brand?._id,
        payment.company['_id'],
      );

    const gatewayConfig: PaymentGatewayConfig =
      paymentConfig.configuration[payment.payment_method];
    if (!gatewayConfig) {
      throw new BadRequestException(
        'No payment configuration found for this payment method',
      );
    }

    return {
      payment,
      paymentConfig,
      gatewayConfig,
      gatewayType: gatewayConfig.paymentGatewayType,
    };
  }

  private async handlePaymentSuccessful(
    payment: PaymentDocument,
    order: OrderDocument,
  ) {
    if (order) {
      order.payment_status = OrderPaymentStatus.COMPLETED;
      await order.save();
      this.eventEmitter.emit('payment.completed', order);
      await this.orderLogService.saveOrderLog(
        order,
        { requestedObject: payment },
        { responseObject: order },
        OrderLogActionEnum.ON_PAYMENT_COMPLETED,
        order.createdBy,
      );
    }

    await this.paymentNotification.onPaymentSuccess(payment);
  }

  private async handlePaymentUnSuccessful(
    payment: PaymentDocument,
    order: OrderDocument,
  ) {
    if (order) {
      order.payment_status = OrderPaymentStatus.UNSUCCESSFUL;
      await order.save();
    }
    await this.paymentNotification.onPaymentUnSuccessful(payment);
  }

  private async savingNewPaymentStatus(
    oldStatus: string,
    newStatus: string,
    payment: PaymentDocument,
  ) {
    if (!payment.statusChanges) {
      payment.statusChanges = [];
    }
    payment.statusChanges.push({
      oldStatus,
      newStatus,
      processingType: payment.gatewayType,
    });
    await payment.save();
  }

  private async removeLatestPaymentFromTheCustomer(payment: Payment) {
    const customer = await this.customerReadService.findOne(
      payment.customer._id.toHexString(),
      payment.company,
    );
    if (customer) {
      customer.latestPaymentDate = undefined;
      customer.latestPayment = undefined;
      await customer.save();
    }
  }

  private async handleTheTookanTaskUpdate(
    order: OrderDocument,
    company: CompanyDocument,
  ) {
    if (
      order &&
      order.deliveryMethod != DeliveryMethod.THIRD_PARTY &&
      order.delivery_action != OrderDeliveryAction.IN_STORE_PICKUP
    ) {
      const customer = await this.customerReadService.findOne(
        order.customer['_id']
          ? order.customer['_id'].toHexString()
          : order.customer.toHexString(),
        company._id,
      );
      const branch =
        order.branch && order.branch['_id']
          ? (order.branch as BranchWithId)
          : undefined;
      const driver = await this.driverService.get_details(
        order.driver ? order.driver.toHexString() : undefined,
      );

      await this.orderDeliveryService.tookanTaskProcessing({
        order,
        customer,
        branch,
        company,
        driver,
      });
    }
  }

  private reformatCallbackUrl(callback_url) {
    let occurrence = 0;
    for (let i = 0; i < callback_url.length; i++) {
      if (callback_url[i] == '?') {
        occurrence++;
      }
      if (occurrence == 2) {
        occurrence = 0;
        callback_url = this.replaceAt(callback_url, i, '&');
      }
    }
    return callback_url;
  }

  private replaceAt(str, index, replacement) {
    return (
      str.substr(0, index) +
      replacement +
      str.substr(index + replacement.length)
    );
  }
}
