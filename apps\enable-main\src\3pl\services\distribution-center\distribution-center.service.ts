import { Inject, Injectable } from '@nestjs/common';
import { DistributionCenterServiceInterface } from './distribution-center-service.interface';
import {
  CreateDistributionCenterDto,
  DistributionCenter,
  GetAllDistributionCenterDto,
  HelperSharedServiceInterface,
  IHelperSharedService,
  IndexResultDto,
  UpdateDistributionCenterDto,
} from '@app/shared-stuff';
import { FilterQuery, PipelineStage, Types } from 'mongoose';
import { DistributionCenterRepositoryInterface } from '../../repositories/distribution-center/distribution-center-repository.interface';
import { plainToInstance } from 'class-transformer';
import { Shared3plService } from '../shared-3pl.service';

@Injectable()
export class DistributionCenterService
  implements DistributionCenterServiceInterface
{
  constructor(
    @Inject(DistributionCenterRepositoryInterface)
    private repository: DistributionCenterRepositoryInterface,
    private sharedService: Shared3plService,
    @Inject(IHelperSharedService)
    private helperSharedService: HelperSharedServiceInterface,
  ) {}

  async findAll(getAllDistributionCenterDto: GetAllDistributionCenterDto) {
    const pipeline: PipelineStage[] = [];
    this.addMatchStage(pipeline, getAllDistributionCenterDto);
    this.addPaginationStage(pipeline, getAllDistributionCenterDto);
    return (await this.repository.aggregate(pipeline)) as [
      IndexResultDto<DistributionCenter>,
    ];
  }

  async findByCompanyId(companyId: Types.ObjectId) {
    return await this.repository.findAll({ companyId });
  }

  async create({ name, location, companyId }: CreateDistributionCenterDto) {
    return this.repository.create(
      plainToInstance(DistributionCenter, {
        name,
        location:
          location && (await this.sharedService.createAddress(location)),
        companyId,
      }),
    );
  }

  async findOne(id: Types.ObjectId) {
    const distributionCenter = await this.repository.findById(id);
    if (!distributionCenter) {
      throw new Error('Distribution Center not found');
    }
    return distributionCenter;
  }

  async update({ _id, name, location }: UpdateDistributionCenterDto) {
    // Fetch the existing distribution center
    const distributionCenter = await this.findOne(_id);
    if (name) {
      distributionCenter.name = name;
    }

    if (location) {
      distributionCenter.location = await this.sharedService.updateAddress(
        distributionCenter.location,
        location,
      );
    }

    return this.repository.findOneAndUpdate(
      { _id },
      { $set: { ...distributionCenter } },
    );
  }

  private addMatchStage(
    pipeline: PipelineStage[],
    getAllDistributionCenterDto: GetAllDistributionCenterDto,
  ) {
    const match: FilterQuery<DistributionCenter> = {};
    if (getAllDistributionCenterDto.companyId) {
      match.companyId = getAllDistributionCenterDto.companyId;
    }
    pipeline.push({ $match: match });
  }

  private addPaginationStage(
    pipeline: PipelineStage[],
    getAllDistributionCenterDto: GetAllDistributionCenterDto,
  ) {
    pipeline.push(
      this.helperSharedService.createPaginationStage(
        getAllDistributionCenterDto.offset,
        getAllDistributionCenterDto.limit,
      ),
    );
  }
}
