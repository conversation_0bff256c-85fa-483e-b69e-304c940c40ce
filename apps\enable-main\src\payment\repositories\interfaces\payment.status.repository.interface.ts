import { IGenericRepository, PaymentStatusEnum } from '@app/shared-stuff';
import { Types } from 'mongoose';
import {
  PaymentStatus,
  PaymentStatusDocument,
} from '../../models/payment.status.model';

export interface PaymentStatusRepositoryInterface
  extends IGenericRepository<PaymentStatusDocument, PaymentStatus> {
  save(
    new_status: PaymentStatusEnum,
    old_status: PaymentStatusEnum,
    paymentId: Types.ObjectId,
  ): Promise<void>;
}
