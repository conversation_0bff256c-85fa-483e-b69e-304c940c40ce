import {
  BrandDocument,
  CountryDialCode,
  CustomerDocument,
  LoyaltyCustomerReplacementsDto,
  LRPSource,
  TriggerAction,
} from '@app/shared-stuff';

import { Types } from 'mongoose';

export interface CustomerNotificationServiceInterface {
  fireOnSendOrdableLinkTrigger(
    customer: CustomerDocument,
    brandId: Types.ObjectId,
  ): Promise<void>;

  fireOnLoyaltyProgramEnrollmentTrigger(
    customer: CustomerDocument,
  ): Promise<void>;

  fireOnCouponRedeemedTrigger(customer: CustomerDocument): Promise<void>;

  fireOnTierUpgradeTrigger(
    customer: CustomerDocument,
    replacements: LoyaltyCustomerReplacementsDto,
    brandId?: Types.ObjectId,
  ): Promise<void>;

  fireOnTierDowngradeTrigger(
    customer: CustomerDocument,
    replacements: LoyaltyCustomerReplacementsDto,
  ): Promise<void>;

  fireOnTierRemoveTrigger(
    customer: CustomerDocument,
    replacements: LoyaltyCustomerReplacementsDto,
  ): Promise<void>;

  fireOnTierCalendarSystemUpdateTrigger(
    customer: CustomerDocument,
  ): Promise<void>;

  fireOnTierManualAssignmentTrigger(
    customer: CustomerDocument,
    replacements?: LoyaltyCustomerReplacementsDto,
  ): Promise<void>;

  fireOnBaseTierAssignmentTrigger(
    customer: CustomerDocument,
    replacements: LoyaltyCustomerReplacementsDto,
    brandId: Types.ObjectId,
  ): Promise<void>;

  fireOnLoyaltyProgramRegistrationTrigger(
    customer: CustomerDocument,
    brandId: Types.ObjectId,
  ): Promise<void>;

  fireOnFirstGracePeriodReminderTrigger(
    customer: CustomerDocument,
  ): Promise<void>;

  fireLoyaltyCardStatusOrRegistrationPromotion(
    customer: CustomerDocument,
    brandId: Types.ObjectId,
    triggerAction:
      | TriggerAction.ON_SEND_LOYALTY_CARD
      | TriggerAction.ON_SEND_LOYALTY_REGISTRATION,
    branchId?: Types.ObjectId,
  ): Promise<void>;

  fireLoyaltyCardStatusOrRegistrationPromotionByPhone(
    brand: BrandDocument,
    phone: string,
    countryCode: CountryDialCode,
    triggerAction:
      | TriggerAction.ON_SEND_LOYALTY_CARD
      | TriggerAction.ON_SEND_LOYALTY_REGISTRATION,
    loyaltyRegistrationPageLink?: string,
  ): Promise<void>;

  fireOnManualLoyaltyRegistration(
    customer: CustomerDocument,
    brandId: Types.ObjectId,
  ): Promise<void>;

  fireOnTierDowngradeWarningTrigger(
    customer: CustomerDocument,
    isThresholdMet: boolean,
  ): Promise<void>;

  fireOnWalletPassAdded(customer: CustomerDocument): Promise<void>;

  fireOnComputationCycleDay(customer: CustomerDocument): Promise<void>;
}

export const CustomerNotificationServiceInterface = Symbol(
  'CustomerNotificationServiceInterface',
);
