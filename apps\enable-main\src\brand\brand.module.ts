import { CollectionName, SharedStuffModule } from '@app/shared-stuff';
import { BrandSchema } from '@app/shared-stuff/models/brand.model';
import { forwardRef, Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { MongooseModule } from '@nestjs/mongoose';
import { CompanyModule } from '../company/company.module';
import { LoyaltyTierReadModule } from '../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.module';
import { SharedModule } from '../shared/shared.module';
import { BrandController } from './controllers/brand.controller';
import { BrandRepository } from './repositories/brand.repository';
import { BrandListenerService } from './services/brand-listener/brand-listener.service';
import { BrandService } from './services/brand/brand.service';

@Module({
  controllers: [BrandController],
  providers: [
    {
      provide: 'BrandServiceInterface',
      useClass: BrandService,
    },
    {
      provide: 'BrandRepositoryInterface',
      useClass: BrandRepository,
    },
    BrandListenerService,
  ],

  imports: [
    MongooseModule.forFeature([
      { name: CollectionName.BRAND, schema: BrandSchema },
    ]),
    SharedModule,
    SharedStuffModule,
    EventEmitterModule,
    forwardRef(() => CompanyModule),
    LoyaltyTierReadModule,
  ],
  exports: [MongooseModule, 'BrandServiceInterface'],
})
export class BrandModule {}
