import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';

import { SharedStuffModule } from '@app/shared-stuff';
import { BranchModule } from '../../../branch/branch.module';
import { CompanyModule } from '../../../company/company.module';
import { OrderModule } from '../../../order/order.module';
import { RestaurantModule } from '../../../restaurant/restaurant.module';
import { SharedModule } from '../../../shared/shared.module';
import { IntegrationLogModule } from '../../integration-log/integration-log.module';
import { DeliverectPosController } from './controllers/deliverect.controller';
import { DeliverectPosListener } from './listeners/deliverect-pos.listener';
import { DeliverectPosService } from './services/deliverect-pos.service';

@Module({
  imports: [
    SharedModule,
    OrderModule,
    BranchModule,
    CompanyModule,
    RestaurantModule,
    HttpModule,
    IntegrationLogModule,
    SharedStuffModule,
  ],
  controllers: [DeliverectPosController],
  providers: [DeliverectPosService, DeliverectPosListener],
})
export class DeliverectPosModule {}
