import {
  BulkActionResponseDto,
  LoggerService,
  LoyaltyTierDocument,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { Types } from 'mongoose';
import * as xlsx from 'xlsx';
import { CustomerRepositoryInterface } from '../customer/modules/customer-repository/customer.repository.interface';
import { CustomerTierServiceInterface } from '../customer/modules/customer-tier/customer-tier.service.interface';
import { LoyaltyTierReadServiceInterface } from '../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.service.interface';

interface ExcelCustomerRow {
  'Phone Number'?: string;
  'Loyalty Tier Name'?: string;
  [key: string]: any;
}

interface CustomerTierGroup {
  tierName: string;
  tierId: Types.ObjectId;
  customerIds: Types.ObjectId[];
}

@Injectable()
export class CustomerTierBulkAssignmentService {
  private readonly logger = new LoggerService(
    CustomerTierBulkAssignmentService.name,
  );

  constructor(
    @Inject(CustomerRepositoryInterface)
    private readonly customerRepository: CustomerRepositoryInterface,
    @Inject(CustomerTierServiceInterface)
    private readonly customerTierService: CustomerTierServiceInterface,
    @Inject(LoyaltyTierReadServiceInterface)
    private readonly loyaltyTierReadService: LoyaltyTierReadServiceInterface,
  ) {}

  async processCustomerTierBulkAssignment(
    companyId: Types.ObjectId,
    excelFilePath: string,
  ): Promise<BulkActionResponseDto> {
    this.logger.log(
      `Starting customer tier bulk assignment for company: ${companyId}`,
    );

    try {
      // Step 1: Read Excel file
      const excelData = this.readExcelFile(excelFilePath);
      this.logger.log(`Read ${excelData.length} rows from Excel file`);

      // Step 2: Get all tiers for the company
      const companyTiers =
        await this.loyaltyTierReadService.findByCompanyId(companyId);
      this.logger.log(`Found ${companyTiers.length} tiers for company`);

      // Step 3: Process customers and group by tier
      const customerTierGroups = await this.processCustomersAndGroupByTier(
        excelData,
        companyId,
        companyTiers,
      );

      // Step 4: Execute bulk tier assignments
      const result = await this.executeBulkTierAssignments(customerTierGroups);

      this.logger.log(
        `Bulk assignment completed. Success: ${result.success}, Failed: ${result.failed}, Unchanged: ${result.unchanged}`,
      );

      return result;
    } catch (error) {
      this.logger.error('Error in customer tier bulk assignment:', error);
      throw error;
    }
  }

  private readExcelFile(filePath: string): ExcelCustomerRow[] {
    try {
      const workbook = xlsx.readFile(filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data: ExcelCustomerRow[] = xlsx.utils.sheet_to_json(worksheet);

      return data.filter(
        (row) => row['Phone Number'] && row['Loyalty Tier Name'],
      );
    } catch (error) {
      this.logger.error('Error reading Excel file:', error);
      throw new Error(`Failed to read Excel file: ${error.message}`);
    }
  }

  private async processCustomersAndGroupByTier(
    excelData: ExcelCustomerRow[],
    companyId: Types.ObjectId,
    companyTiers: LoyaltyTierDocument[],
  ): Promise<CustomerTierGroup[]> {
    const tierGroups = new Map<string, CustomerTierGroup>();

    for (const row of excelData) {
      const phoneNumber = row['Phone Number'];
      const tierName = row['Loyalty Tier Name'];

      if (!phoneNumber || !tierName) {
        this.logger.warn(
          `Skipping row with missing phone or tier: ${JSON.stringify(row)}`,
        );
        continue;
      }

      try {
        // Find customer by phone and company
        const customer = await this.customerRepository.findByPhoneAndCompanyId(
          phoneNumber,
          companyId,
        );

        if (!customer) {
          this.logger.warn(`Customer not found for phone: ${phoneNumber}`);
          continue;
        }

        // Find tier by name
        const tier = companyTiers.find(
          (t) => t.nameEn === tierName || t.nameAr === tierName,
        );

        if (!tier) {
          this.logger.warn(`Tier not found for name: ${tierName}`);
          continue;
        }

        // Group customers by tier
        if (!tierGroups.has(tierName)) {
          tierGroups.set(tierName, {
            tierName,
            tierId: tier._id,
            customerIds: [],
          });
        }

        tierGroups.get(tierName)!.customerIds.push(customer._id);
      } catch (error) {
        this.logger.error(`Error processing customer ${phoneNumber}:`, error);
      }
    }

    return Array.from(tierGroups.values());
  }

  private async executeBulkTierAssignments(
    customerTierGroups: CustomerTierGroup[],
  ): Promise<BulkActionResponseDto> {
    const result: BulkActionResponseDto = {
      success: 0,
      failed: 0,
      unchanged: 0,
    };

    for (const group of customerTierGroups) {
      this.logger.log(
        `Processing tier assignment for ${group.customerIds.length} customers to tier: ${group.tierName}`,
      );

      try {
        const bulkResult = await this.customerTierService.bulkTierAssignment({
          tierId: group.tierId,
          customerIds: group.customerIds,
          notifyCustomers: false, // Set to false to avoid sending notifications
        });

        result.success += bulkResult.success;
        result.failed += bulkResult.failed;
        result.unchanged += bulkResult.unchanged;
      } catch (error) {
        this.logger.error(
          `Error in bulk tier assignment for tier ${group.tierName}:`,
          error,
        );
        result.failed += group.customerIds.length;
      }
    }

    return result;
  }
}
