import { SharedStuffModule } from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CompanyModule } from '../../../company/company.module';
import { ShortenUrlModule } from '../../../integration/shorten-url/shorten-url.module';
import { LoyaltyPointLogModule } from '../../../loyalty-point-log/loyalty-point-log.module';
import { PunchCardAchievementModule } from '../../../punch-card/modules/punch-card-achievement/punch-card-achievement.module';
import { SharedModule } from '../../../shared/shared.module';
import { CustomerIndexModule } from '../customer-index/customer-index.module';
import { CustomerPunchCardModule } from '../customer-punch-card/customer-punch-card.module';
import { CustomerReadModule } from '../customer-read/customer-read.module';
import { CustomerTierInfoModule } from '../customer-tier-info/customer-tier-info.module';
import { CustomerTokenModule } from '../customer-token/customer-token.module';
import { CustomerLoyaltyService } from './customer-loyalty.service';
import { CustomerLoyaltyServiceInterface } from './customer-loyalty.service.interface';
import { LoyaltyTierReadModule } from '../../../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.module';

@Module({
  providers: [
    {
      provide: CustomerLoyaltyServiceInterface,
      useClass: CustomerLoyaltyService,
    },
  ],
  imports: [
    ConfigModule,
    SharedModule,
    SharedStuffModule,
    CompanyModule,
    LoyaltyPointLogModule,
    LoyaltyTierReadModule,
    PunchCardAchievementModule,
    CustomerReadModule,
    CustomerTierInfoModule,
    CustomerTokenModule,
    CustomerPunchCardModule,
    CustomerIndexModule,
    ShortenUrlModule,
  ],
  exports: [CustomerLoyaltyServiceInterface],
})
export class CustomerLoyaltyModule {}
