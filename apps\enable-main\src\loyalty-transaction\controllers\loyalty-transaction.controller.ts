import {
  CreateManualLoyaltyTransactionDto,
  GenericExceptionFilter,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Inject,
  Post,
  SetMetadata,
  UseFilters,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiBasic<PERSON>uth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { LoyaltyTransactionServiceInterface } from '../services/loyalty-transaction-service.interface';

@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@UsePipes(
  new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidUnknownValues: true,
  }),
)
@ApiTags('LoyaltyTransaction')
@SetMetadata('module', 'loyalty-transaction')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@Controller('loyalty-transaction')
export class LoyaltyTransactionController {
  constructor(
    @Inject(LoyaltyTransactionServiceInterface)
    private service: LoyaltyTransactionServiceInterface,
  ) {}

  @Post()
  @SetMetadata('action', 'create')
  async createManual(
    @Body()
    createManualLoyaltyTransactionDto: CreateManualLoyaltyTransactionDto,
  ) {
    return this.service.createManual(createManualLoyaltyTransactionDto);
  }
}
