import {
  CbPayResponse,
  CollectionName,
  OrderDocument,
  OrderPaymentStatus,
  OrderStatusEnum,
  Payment,
  PaymentDocument,
  PaymentGatewayType,
  PaymentMethodUsed,
  PaymentStatusEnum,
  PusherService,
} from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Agent } from 'https';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import { CbPayTokenToGenerate } from '../../../dto/cbpay/cbpay-token-to-generate.dto';
import { CbPayTokenToRequest } from '../../../dto/cbpay/cbpay-token-to-request.dto';
import { CbPayTransactionToInit } from '../../../dto/cbpay/cbpay-transaction-to-init.dto';
import { CbPayErrors } from '../../../enums/cbpay-errors.enum';
import { PaymentRepositoryInterface } from '../../../repositories/interfaces/payment.repository.interface';
import { PaymentStatusRepositoryInterface } from '../../../repositories/interfaces/payment.status.repository.interface';
import { PaymentHelpersService } from '../../payment-helpers/payment-helpers.service';
import { CompanyService } from './../../../../company/services/company/company.service';
import { CustomerReadServiceInterface } from './../../../../customer/modules/customer-read/customer-read.service.interface';
import { PaymentNotificationService } from './../../payment-notification/payment-notification.service';

@Injectable()
export class PaymentCbpayService {
  BASE_URL: string;
  MERCHANT_ID: string;
  CLIENT_ID: string;
  SECRET_ID: string;

  constructor(
    private configService: ConfigService,
    private http: HttpService,
    private paymentNotification: PaymentNotificationService,
    @Inject('PaymentStatusRepositoryInterface')
    private paymentStatusRepository: PaymentStatusRepositoryInterface,
    private companyService: CompanyService,
    @InjectModel(CollectionName.ORDER)
    private paymentOrderModel: Model<OrderDocument>,
    @Inject(CustomerReadServiceInterface)
    private customerReadService: CustomerReadServiceInterface,
    @Inject('PaymentRepositoryInterface')
    private paymentRepository: PaymentRepositoryInterface,
    private paymentHelperService: PaymentHelpersService,
    private pusherService: PusherService,
  ) {
    this.BASE_URL = this.configService.get<string>('CBPAY_BASE_URL');
    this.MERCHANT_ID = this.configService.get<string>('CBPAY_MERCHANT_ID');
    this.CLIENT_ID = this.configService.get<string>('CBPAY_CLIENT_ID');
    this.SECRET_ID = this.configService.get<string>('CBPAY_SECRET_KEY');
  }

  async afterPaymentDone(data: any) {
    const selectedPayment = await this.paymentRepository.findByCBPayCode(
      data['TransactionCode'],
    );

    if (selectedPayment.status == PaymentStatusEnum.TRANSACTION_COMPLETED) {
      selectedPayment.paymentTries = selectedPayment.paymentTries
        ? [...selectedPayment.paymentTries, data]
        : [data];
      await selectedPayment.save();
      return;
    }

    const company = await this.companyService.get_details(
      selectedPayment.company,
    );
    let callback_url = '';
    selectedPayment.transaction_date = moment().toDate();
    selectedPayment.raw_response_data = data;
    selectedPayment.paymentTries
      ? selectedPayment.paymentTries.push(data)
      : [data];
    data['responsecode'] = data['statusid'] == '10' ? '000' : '152';
    data['date'] = moment().utc().toDate();
    let callback_paramter = `?lang=${
      selectedPayment.language == 'arabic' ? 'ar' : 'en'
    }&customer_name=${selectedPayment.customer_name}&amount=${
      selectedPayment.amount
    }&transaction_id=${selectedPayment.transaction_id}&company_name=${
      company.name
    }&payment_code=${selectedPayment.code}&transaction_date=${
      selectedPayment.transaction_date
    }&responsecode=${data['responsecode']}`;

    let selectedOrder: OrderDocument;
    if (selectedPayment.order_code) {
      selectedOrder = await this.paymentOrderModel.findOne({
        code: selectedPayment.order_code,
      });
    }
    if (data['responsecode'] == '000') {
      await this.paymentStatusRepository.save(
        PaymentStatusEnum.TRANSACTION_COMPLETED,
        selectedPayment.status,
        selectedPayment._id,
      );
      selectedPayment.status = PaymentStatusEnum.TRANSACTION_COMPLETED;
      await selectedPayment.save();
      if (selectedOrder) {
        selectedOrder.status = OrderStatusEnum.SCHEDULED;
        await this.paymentHelperService.handlePaymentStatusLogic(
          selectedPayment,
          selectedPayment.status,
          selectedOrder,
          company,
        );
        callback_paramter += `&udf4=${selectedOrder.invoice_number}`;
        await selectedOrder.save();
      }
      await this.paymentNotification.onPaymentSuccess(selectedPayment);
      callback_url =
        this.configService.get('SUCCESSFULL_RECEIPT_PAGE') + callback_paramter;
    } else {
      await this.paymentStatusRepository.save(
        PaymentStatusEnum.UNSUCCESSFUL,
        selectedPayment.status,
        selectedPayment._id,
      );
      selectedPayment.status = PaymentStatusEnum.UNSUCCESSFUL;
      await selectedPayment.save();
      if (selectedOrder) {
        selectedOrder.payment_status = OrderPaymentStatus.UNSUCCESSFUL;
        callback_paramter += `$udf5=${selectedOrder.invoice_number}`;
        callback_paramter += `&udf4=${selectedOrder.invoice_number}`;
        await selectedOrder.save();
      }
      await this.paymentNotification.onPaymentUnSuccessful(selectedPayment);
      callback_url =
        this.configService.get('UNSUCCESSFULL_RECEIPT_PAGE') +
        callback_paramter;
    }
    if (
      selectedPayment.callback_url &&
      selectedPayment.callback_url != 'default'
    ) {
      callback_url = selectedPayment.callback_url + callback_paramter;
    } else if (company.call_back_url != 'default') {
      callback_url = company.call_back_url + callback_paramter;
    }
    const callbackUrl = this.reformat_callback_url(callback_url);
    await this.pusherService.fireEvent(
      selectedPayment.code,
      'paymentCompleted',
      {
        callbackUrl,
      },
    );

    // perform the request of callbackUrl without waiting
    this.http
      .get(callbackUrl, {
        httpsAgent: new Agent({ rejectUnauthorized: false }),
      })
      .subscribe();
    this.removeLatestPaymentFromCustomer(selectedPayment);

    return callbackUrl;
  }

  async removeLatestPaymentFromCustomer(payment: Payment) {
    const customer = await this.customerReadService.findOne(
      payment.customer._id.toHexString(),
      payment.company,
    );
    if (customer) {
      customer.latestPaymentDate = undefined;
      customer.latestPayment = undefined;
      await customer.save();
    }
  }

  reformat_callback_url(callback_url) {
    let occu = 0;
    for (let i = 0; i < callback_url.length; i++) {
      if (callback_url[i] == '?') {
        occu++;
      }
      if (occu == 2) {
        occu = 0;
        callback_url = this.replace_at(callback_url, i, '&');
      }
    }
    return callback_url;
  }

  replace_at(str, index, replacement) {
    return (
      str.substr(0, index) +
      replacement +
      str.substr(index + replacement.length)
    );
  }

  async processPayment(payment: PaymentDocument) {
    if (!payment.cbPayResponse) {
      payment.cbPayResponse = new CbPayResponse();
    }
    let currentToken = payment.cbPayResponse.tokenResponse['TokenID'];
    const paymentExpireDate = moment(
      payment.cbPayResponse.tokenResponse['TokenExpiry'],
      'MM/DD/YYYY hh:mm:ss a',
    );
    if (paymentExpireDate.isBefore(moment())) {
      const response = await this.requestToken({ paymentCode: payment.code });
      currentToken = response['TokenID'];
    }
    let customerPhoneNumber = payment.country_code + payment.customer_phone;
    customerPhoneNumber = customerPhoneNumber.replace('+', '');
    const transactionResponse = await this.InitiateRemoteTransaction({
      Amount: payment.amount,
      Token: currentToken,
      MerchantId: this.MERCHANT_ID,
      MerchantRedirectionURL: 'https://enable.tech',
      RecipientMobileNo: customerPhoneNumber,
      Comments: payment.comments,
    });
    if (!payment.cbPayResponse) {
      payment.cbPayResponse = new CbPayResponse();
    }
    payment.cbPayResponse.transactionResponse = transactionResponse;
    payment.transaction_id = transactionResponse['TransactionCode'];
    payment.status = PaymentStatusEnum.PROCESSING;
    payment.gatewayType = PaymentGatewayType.CBPAY;
    payment.dateGateway = moment().utc().toDate();
    await this.paymentStatusRepository.save(
      PaymentStatusEnum.PROCESSING,
      payment.status,
      payment._id,
    );
    payment.payment_method_used = PaymentMethodUsed.COMMERCIAL_BANK;
    if (!payment.cbPayTransactionIds) {
      payment.cbPayTransactionIds = [];
    }
    payment.cbPayTransactionIds.push(transactionResponse['TransactionCode']);
    await payment.save();
    return payment;
  }

  async requestToken(cbPayTokenToRequest: CbPayTokenToRequest) {
    const paymentFilter = {
      $or: [{ code: cbPayTokenToRequest.paymentCode }],
    } as any;
    if (Types.ObjectId.isValid(cbPayTokenToRequest.paymentCode)) {
      paymentFilter.$or.push({ _id: cbPayTokenToRequest.paymentCode });
    }

    const payment = await this.paymentRepository.findOne(paymentFilter);
    let customerPhoneNumber = payment.country_code + payment.customer_phone;
    customerPhoneNumber = customerPhoneNumber.replace('+', '');

    ///Check if the token is exist and not expired
    if (payment.cbPayResponse && payment.cbPayResponse.tokenResponse) {
      const expiryDate = moment(
        payment.cbPayResponse.tokenResponse['TokenExpiry'],
        'MM/DD/YYYY hh:mm:ss a',
      );
      if (expiryDate.isAfter(moment())) {
        return payment.cbPayResponse.tokenResponse;
      }
    }

    const tokenResponse = await this.generateAccessToken({
      MerchantId: this.MERCHANT_ID,
      ClientID: this.CLIENT_ID,
      SecretKey: this.SECRET_ID,
      RecipientMobileNo: customerPhoneNumber,
    });
    if (!payment.cbPayResponse) {
      payment.cbPayResponse = new CbPayResponse();
    }
    payment.cbPayResponse.tokenResponse = tokenResponse;

    await payment.save();
    return tokenResponse;
  }

  private async generateAccessToken(tokenToGenerate: CbPayTokenToGenerate) {
    return new Promise((resolve, reject) => {
      this.http
        .post(this.BASE_URL + '/GenerateAccessToken', tokenToGenerate)
        .subscribe(
          (data) => {
            try {
              if (data['data']['errorCode'] == CbPayErrors.SUCCESS) {
                resolve(data['data']['data']);
              } else {
                reject(data['data']);
              }
            } catch (err) {
              reject(err);
            }
          },
          (err) => {
            reject(err);
          },
        );
    });
  }

  private async InitiateRemoteTransaction(
    transactionToInit: CbPayTransactionToInit,
  ) {
    return new Promise((resolve, reject) => {
      this.http
        .post(this.BASE_URL + '/InitiateRemoteTransaction', transactionToInit)
        .subscribe(
          (data) => {
            try {
              if (data['data']['errorCode'] == CbPayErrors.SUCCESS) {
                resolve(data['data']['data']);
              } else {
                reject(data['data']);
              }
            } catch (err) {
              reject(err);
            }
          },
          (err) => {
            reject(err);
          },
        );
    });
  }
}
