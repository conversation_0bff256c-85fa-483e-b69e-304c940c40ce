import {
  ImportMenuItemsDto,
  MenuItemBulkWriteDto,
  MenuItemImportPreference,
  MenuItemToCreate,
  MenuItemToIndex,
  MenuItemToPromote,
  MenuItemToUpdate,
  responseCode,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  SetMetadata,
  UploadedFile,
  UseInterceptors,
  Version,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiExcludeEndpoint,
  ApiTags,
} from '@nestjs/swagger';

import { Request, Response } from 'express';
import { memoryStorage } from 'multer';
import { CurrentUserService } from '../../../shared/services/current-user/current-user.service';
import { HelperService } from './../../../shared/services/helper/helper.service';
import { MenuItemService } from './../../services/menu-item/menu-item.service';

@Controller('menu-item')
@ApiTags('Restaurant Menu Items')
@SetMetadata('module', 'menu-item')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class MenuItemController {
  constructor(
    private menuItemService: MenuItemService,
    private helperService: HelperService,
    private currentUserService: CurrentUserService,
  ) {}

  @Get()
  @SetMetadata('action', 'get_all')
  async index(@Query() menuItemToIndex: MenuItemToIndex, @Res() res: Response) {
    try {
      const items = await this.menuItemService.index(menuItemToIndex);

      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to get all Menus items',
        {
          menuItems: items[0]['paginatedResult'],
          totalMenuItems: items[0]['totalCount'][0]
            ? items[0]['totalCount'][0]['createdAt']
            : 0,
        },
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  async getDetails(@Param('id') id: string, @Res() res: Response) {
    try {
      const item = await this.menuItemService.getDetailsForFrontEnd(id);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'success to get all Menus item details',
        item,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get('public/promotional/:id')
  @SetMetadata('public', 'true')
  async getDetailsForPromotional(
    @Param('id') id: string,
    @Res() res: Response,
  ) {
    try {
      const item =
        await this.menuItemService.getMenuItemWithBranchAndBrandDetails(id);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'success to get all Menus item details',
        item,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get('public/:id')
  @SetMetadata('public', 'true')
  async getPublicDetails(@Param('id') id: string, @Res() res: Response) {
    try {
      const item = await this.menuItemService.getDetails(id);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'success to get all Menus item details',
        item,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get('tags/index')
  @SetMetadata('action', 'getMenuTags')
  async getTags(@Res() res: Response) {
    try {
      const tags = await this.menuItemService.getTags();
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to get all tags',
        tags,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() menuItemToCreate: MenuItemToCreate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      menuItemToCreate['company'] = req['company_id'];
      menuItemToCreate.createdBy = req['current'];
      const item = await this.menuItemService.create(menuItemToCreate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'success to Create Menus item',
        item,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('bulk-write')
  @SetMetadata('action', 'bulk-write')
  async bulkWrite(@Body() bulkWriteDto: MenuItemBulkWriteDto) {
    return await this.menuItemService.bulkWrite(
      this.currentUserService.getCurrentCompanyId(),
      bulkWriteDto,
    );
  }

  @Post('promote')
  @SetMetadata('action', 'promote')
  async promote(
    @Body() menuItemToPromote: MenuItemToPromote,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      menuItemToPromote['company'] = req['company_id'];
      menuItemToPromote.currentUser = req['current'];
      const firedTrigger =
        await this.menuItemService.promoteMenuItem(menuItemToPromote);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'success to Promote Menu item',
        firedTrigger,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Put(':id')
  @SetMetadata('action', 'update')
  async update(
    @Body() menuItemToUpdate: MenuItemToUpdate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      menuItemToUpdate['company'] = req['company_id'];
      menuItemToUpdate.updatedBy = req['current'];
      const item = await this.menuItemService.update(menuItemToUpdate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'success to Update Menus item ',
        item,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Delete(':id')
  @SetMetadata('action', 'delete')
  async delete(
    @Param('id') id: string,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const item = await this.menuItemService.remove({
        _id: id,
        deletedBy: req['current'],
      });
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_REMOVE,
        'success to REMOVE Menus item',
        item,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('import')
  @SetMetadata('action', 'import_excel')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      required: ['menuId', 'companyId', 'file'],
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        menuId: {
          type: 'string',
        },
        companyId: {
          type: 'string',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file', { storage: memoryStorage() }))
  async importMenuItems(
    @UploadedFile('file') file: Express.Multer.File,
    @Body() importMenuItemsDto: ImportMenuItemsDto,
  ) {
    await this.menuItemService.importMenuItems(importMenuItemsDto, file);
    return 200;
  }

  @Post('import')
  @SetMetadata('action', 'import_excel_v2')
  @ApiConsumes('multipart/form-data')
  @Version('2')
  @ApiBody({
    schema: {
      type: 'object',
      required: ['menuId', 'companyId', 'file'],
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        menuId: {
          type: 'string',
        },
        companyId: {
          type: 'string',
        },
        preference: {
          type: 'string',
          enum: Object.values(MenuItemImportPreference),
        },
        keyMapping: {
          type: 'string',
          description: 'JSON string of property-to-column mappings',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file', { storage: memoryStorage() }))
  async importMenuItemsV2(
    @UploadedFile('file') file: Express.Multer.File,
    @Body() importDto: ImportMenuItemsDto,
    @Res() res: Response,
  ) {
    try {
      // Parse keyMapping if it's provided as a string
      if (typeof importDto.keyMapping === 'string' && !importDto.preference) {
        importDto.keyMapping = JSON.parse(importDto.keyMapping);
      }

      const result = await this.menuItemService.importMenuItems(
        importDto,
        file,
      );

      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'Successfully imported menu items',
        result,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
}
