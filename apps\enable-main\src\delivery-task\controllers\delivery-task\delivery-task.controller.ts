import {
  DeliveryTaskToCreate,
  DeliveryTaskToIndex,
} from './../../dto/delivery-task.dto';
import { DeliveryTaskService } from './../../services/delivery-task/delivery-task.service';
import { HelperService } from './../../../shared/services/helper/helper.service';
import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Req,
  Res,
  SetMetadata,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { responseCode } from '@app/shared-stuff';
import { Request, Response } from 'express';

@Controller('delivery-task')
@ApiTags('Delivery Task')
@SetMetadata('module', 'delivery-task')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class DeliveryTaskController {
  constructor(
    private helperService: HelperService,
    private deliveryTaskService: DeliveryTaskService,
  ) {}

  @Get()
  @SetMetadata('action', 'getAll')
  async index(@Query() dataToIndex: DeliveryTaskToIndex, @Res() res: Response) {
    try {
      const deliveryTasks = await this.deliveryTaskService.index(dataToIndex);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to get ',
        { deliveryTasks: deliveryTasks },
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() deliveryToCreate: DeliveryTaskToCreate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const deliveryParty =
        await this.deliveryTaskService.create(deliveryToCreate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success to create a new Delivery TASK',
        deliveryParty,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
}
