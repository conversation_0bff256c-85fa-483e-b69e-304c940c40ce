import { Types } from 'mongoose';
import {
  Campaign,
  CampaignDocument,
  IGenericRepository,
} from '@app/shared-stuff';

export interface CampaignRepositoryInterface
  extends IGenericRepository<CampaignDocument, Campaign> {
  findByCompany(companyId: Types.ObjectId): Promise<CampaignDocument[]>;
  findByBrand(brandId: Types.ObjectId): Promise<CampaignDocument[]>;
}

export const CampaignRepositoryInterface = Symbol(
  'CampaignRepositoryInterface',
);
