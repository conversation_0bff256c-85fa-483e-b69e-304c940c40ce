// Migrate loyalty tier pass fields to pass config
// loyaltyCardForegroundColor, loyaltyCardBackgroundColor, stripBackgroundImage

db.loyaltytiers
  .find({
    $or: [
      { loyaltyCardForegroundColor: { $exists: true } },
      { loyaltyCardBackgroundColor: { $exists: true } },
      { stripBackgroundImage: { $exists: true } },
    ],
  })
  .forEach((tier) => {
    db.pass_configs.insertOne({
      owner: {
        _id: tier._id,
        name: tier.nameEn,
        type: 'tier',
      },
      ...(tier.stripBackgroundImage && {
        stripImageConfig: { backgroundImage: tier.stripBackgroundImage },
      }),
      ...((tier.loyaltyCardForegroundColor ||
        tier.loyaltyCardBackgroundColor) && {
        passTemplate: {
          ...(tier.loyaltyCardBackgroundColor && {
            backgroundColor: tier.loyaltyCardBackgroundColor,
          }),
          ...(tier.loyaltyCardForegroundColor && {
            labelTextColor: tier.loyaltyCardForegroundColor,
            valueTextColor: tier.loyaltyCardForegroundColor,
          }),
        },
      }),
    });

    db.loyaltytiers.updateOne({ _id: tier._id }, [
      {
        $unset: [
          'loyaltyCardForegroundColor',
          'loyaltyCardBackgroundColor',
          'stripBackgroundImage',
        ],
      },
    ]);
  });
