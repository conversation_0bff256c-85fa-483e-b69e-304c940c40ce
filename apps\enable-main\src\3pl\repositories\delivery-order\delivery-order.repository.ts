import { Injectable } from '@nestjs/common';
import {
  CollectionName,
  DeliveryOrder,
  DeliveryOrderDocument,
  GenericRepository,
} from '@app/shared-stuff';
import { DeliveryOrderRepositoryInterface } from './delivery-order-repository.interface';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@Injectable()
export class DeliveryOrderRepository
  extends GenericRepository<DeliveryOrderDocument, DeliveryOrder>
  implements DeliveryOrderRepositoryInterface
{
  constructor(
    @InjectModel(CollectionName.DELIVERY_ORDER)
    private deliveryOrderModel: Model<DeliveryOrderDocument, DeliveryOrder>,
  ) {
    super(deliveryOrderModel);
  }

  async findOneByCode(code: string): Promise<DeliveryOrderDocument> {
    return this.deliveryOrderModel.findOne({ code }).exec();
  }
}
