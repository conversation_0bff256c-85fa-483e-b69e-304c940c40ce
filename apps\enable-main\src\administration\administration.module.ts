import {
  BranchSchema,
  CollectionName,
  CustomerSchema,
  SavedLocationSchema,
  createOrderSchemaWithHooks,
} from '@app/shared-stuff';
import { BrandSchema } from '@app/shared-stuff/models/brand.model';
import { CompanySchema } from '@app/shared-stuff/models/company.model';
import { CompanyConfigSchema } from '@app/shared-stuff/models/companyConfig.model';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { MongooseModule } from '@nestjs/mongoose';
import { CompanyModule } from '../company/company.module';
import { MobileModule } from '../mobile/mobile.module';
import { VersionModule } from '../mobile/modules/version/version.module';
import { OrderLog, OrderLogSchema } from '../order/models/order.log.model';
import { OrderItem, OrderItemSchema } from '../order/models/orderItems.model';
import { RoleSchema } from '../rbac/models/role.model';
import { SharedModule } from '../shared/shared.module';
import { User, UserSchema } from '../user/models/user.model';
import { UserModule } from '../user/user.module';
import { AdminController } from './admin/admin.controller';
import { EnableLog, EnableLogSchema } from './models/log.model';
import { AdminService } from './services/admin/admin.service';

@Module({
  controllers: [AdminController],
  providers: [
    AdminService,
    // LogsService
  ],
  imports: [
    SharedModule,
    UserModule,
    MobileModule,
    VersionModule,
    ConfigModule,
    CompanyModule,
    MongooseModule.forFeatureAsync([
      { name: EnableLog.name, useFactory: () => EnableLogSchema },
      { name: User.name, useFactory: () => UserSchema },
      { name: CollectionName.COMPANY, useFactory: () => CompanySchema },
      { name: CollectionName.BRAND, useFactory: () => BrandSchema },
      { name: CollectionName.BRANCH, useFactory: () => BranchSchema },
      { name: CollectionName.CUSTOMER, useFactory: () => CustomerSchema },
      { name: OrderItem.name, useFactory: () => OrderItemSchema },
      {
        name: CollectionName.COMPANY_CONFIG,
        useFactory: () => CompanyConfigSchema,
      },
      { name: CollectionName.ROLES, useFactory: () => RoleSchema },
      { name: OrderLog.name, useFactory: () => OrderLogSchema },
      {
        name: CollectionName.SAVED_LOCATION,
        useFactory: () => SavedLocationSchema,
      },
      {
        name: CollectionName.ORDER,
        imports: [],
        useFactory: (eventEmitter: EventEmitter2) => {
          return createOrderSchemaWithHooks(eventEmitter);
        },
        inject: [EventEmitter2],
      },
    ]),
  ],
  exports: [
    // LogsService
  ],
})
export class AdministrationModule {}
