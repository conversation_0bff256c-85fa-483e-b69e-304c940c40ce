import { MicroserviceCommunicationModule } from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { BrandModule } from '../brand/brand.module';
import { CompanyModule } from '../company/company.module';
import { CustomerIndexModule } from '../customer/modules/customer-index/customer-index.module';
import { CustomerReplacementsModule } from '../customer/modules/customer-replacements/customer-replacements.module';
import { SharedModule } from '../shared/shared.module';
import { CampaignController } from './controllers/campaign.controller';
import { CampaignService } from './services/campaign.service';
import { CampaignServiceInterface } from './services/campaign.service.interface';

@Module({
  controllers: [CampaignController],
  providers: [{ provide: CampaignServiceInterface, useClass: CampaignService }],
  imports: [
    MicroserviceCommunicationModule.forChild('enable-main-campaign-producer'),
    SharedModule,
    CompanyModule,
    BrandModule,
    CustomerIndexModule,
    CustomerReplacementsModule,
  ],
})
export class CampaignModule {}
