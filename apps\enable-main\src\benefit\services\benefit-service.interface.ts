import {
  Benefit,
  BenefitDocument,
  BenefitUsage,
  BenefitWithSourceAndId,
  CreateBenefitDto,
  CustomerBenefit,
  CustomerDocument,
  CustomerEarnedBenefit,
  GetAllBenefitDto,
  IndexResultDto,
  LoyaltyTierBenefit,
  OrderDocument,
  UpdateBenefitDto,
} from '@app/shared-stuff';
import { CustomerBenefitSource } from '@app/shared-stuff/enums/customer/customer-benefit-source.enum';
import { Types } from 'mongoose';

export interface BenefitServiceInterface {
  index(getAllBenefitDto: GetAllBenefitDto): Promise<IndexResultDto<Benefit>[]>;
  redeem(
    benefit: CustomerEarnedBenefit,
    customer: CustomerDocument,
    order: OrderDocument,
  ): Promise<OrderDocument>;

  create(createBenefitDto: CreateBenefitDto): Promise<BenefitDocument>;

  update(updateBenefitDto: UpdateBenefitDto): Promise<BenefitDocument>;

  findOneById(id: Types.ObjectId): Promise<Benefit>;

  findByIdIn(ids: Types.ObjectId[]): Promise<BenefitDocument[]>;

  earn(benefits: CustomerBenefit[], customer: CustomerDocument): Promise<void>;

  removeEarnedBenefits(
    benefits: CustomerEarnedBenefit[],
    customer: CustomerDocument,
  ): Promise<void>;

  updateEarnedBenefits(
    benefits: BenefitWithSourceAndId[],
    customer: CustomerDocument,
  ): Promise<void>;

  replaceCustomerEarnedBenefits(
    benefits: CustomerEarnedBenefit[],
    customer: CustomerDocument,
  ): Promise<void>;

  onProgramBenefitUpdated(
    oldBenefits: LoyaltyTierBenefit[],
    newBenefits: LoyaltyTierBenefit[],
    source: CustomerBenefitSource,
    customer: CustomerDocument,
  ): Promise<void>;

  createBenefitUsageMatcher(
    benefit: LoyaltyTierBenefit | CustomerEarnedBenefit,
    source: CustomerBenefitSource,
  ): (benefitUsage: BenefitUsage | CustomerEarnedBenefit) => boolean;
}

export const BenefitServiceInterface = Symbol('BenefitServiceInterface');
