import { CollectionName, SharedStuffModule } from '@app/shared-stuff';
import { Lo<PERSON>, Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { MongooseModule } from '@nestjs/mongoose';
import { SharedModule } from '../shared/shared.module';
import { StorageModule } from '../storage/storage.module';
import { PermissionController } from './controllers/permission/permission.controller';
import { PrivilegesController } from './controllers/privileges/privileges.controller';
import { RoleController } from './controllers/role/role.controller';
import { PermissionSchema } from './models/permission.model';
import { PrivilegeSchema } from './models/privilege.model';
import { RoleSchema } from './models/role.model';
import { PermissionService } from './services/permission/permission.service';
import { PrivilegesService } from './services/privileges/privileges.service';
import { RoleService } from './services/role/role.service';

@Module({
  controllers: [RoleController, PermissionController, PrivilegesController],
  providers: [RoleService, PermissionService, PrivilegesService, Logger],
  imports: [
    SharedModule,
    ConfigModule,
    EventEmitterModule,
    SharedStuffModule,
    StorageModule,
    MongooseModule.forFeature([
      { name: CollectionName.ROLES, schema: RoleSchema },
      { name: CollectionName.PERMISSIONS, schema: PermissionSchema },
      { name: 'Privilege', schema: PrivilegeSchema },
    ]),
  ],
  exports: [PermissionService, PrivilegesService, RoleService],
})
export class RbacModule {}
