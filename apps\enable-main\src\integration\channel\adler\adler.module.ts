import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';

import { SharedStuffModule } from '@app/shared-stuff';

import { BranchModule } from '../../../branch/branch.module';
import { CompanyModule } from '../../../company/company.module';
import { OrderModule } from '../../../order/order.module';
import { RestaurantModule } from '../../../restaurant/restaurant.module';
import { SharedModule } from '../../../shared/shared.module';
import { UserModule } from '../../../user/user.module';
import { IntegrationLogModule } from '../../integration-log/integration-log.module';
import { AdlerController } from './controllers/adler.controller';
import { AdlerListener } from './listeners/adler.listener';
import { AdlerService } from './services/adler.service';

@Module({
  imports: [
    SharedModule,
    SharedStuffModule,
    CompanyModule,
    UserModule,
    BranchModule,
    RestaurantModule,
    HttpModule,
    OrderModule,
    IntegrationLogModule,
  ],
  providers: [AdlerService, AdlerListener],
  controllers: [AdlerController],
  exports: [],
})
export class AdlerModule {}
