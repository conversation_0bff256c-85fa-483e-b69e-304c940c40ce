import {
  CalendarSystem,
  CompanyDocument,
  CustomerDocument,
  forEachAsync,
  LogError,
  LoggerService,
  mapAsync,
  TierStatus,
  TriggerAction,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { BrandServiceInterface } from '../../brand/services/brand/brand.service.interface';
import { CompanyService } from '../../company/services/company/company.service';
import { TemplateService } from '../../notification/services/template/template.service';
import { CustomerNotificationServiceInterface } from '../modules/customer-notification/customer-notification.service.interface';
import { CustomerRepositoryInterface } from '../modules/customer-repository/customer.repository.interface';
import { CustomerTierInfoServiceInterface } from '../modules/customer-tier-info/customer-tier-info.service.interface';
import { CustomerTierServiceInterface } from '../modules/customer-tier/customer-tier.service.interface';
import { OnEvent } from '@nestjs/event-emitter';

@Injectable()
export class CustomerCronJobService {
  private readonly loggerService = new LoggerService(
    CustomerCronJobService.name,
  );

  constructor(
    private readonly companyService: CompanyService,
    private readonly templateService: TemplateService,
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandServiceInterface,
    @Inject(CustomerRepositoryInterface)
    private readonly customerRepository: CustomerRepositoryInterface,
    @Inject(CustomerTierInfoServiceInterface)
    private readonly customerLoyaltyTierInfoService: CustomerTierInfoServiceInterface,
    @Inject(CustomerTierServiceInterface)
    private readonly customerTierService: CustomerTierServiceInterface,
    @Inject(CustomerNotificationServiceInterface)
    private readonly customerNotificationService: CustomerNotificationServiceInterface,
  ) {}

  // Every first of the month at 11:30 AM
  @OnEvent('cron.every1stDayAt1130')
  @LogError()
  async fireComputationCycleDay() {
    this.loggerService.log(
      '[Cron Job Schedular] fire computation cycle day , every1stDayAt1130',
    );
    const isQuarterlyComputation = [0, 3, 6, 9].includes(moment.utc().month());

    const companies = await this.companyService.findAll({
      hasLoyaltyProgram: true,
      'loyaltyProgramConfig.hasLoyaltyTiers': true,
      'loyaltyProgramConfig.calendarSystem': {
        $in: isQuarterlyComputation
          ? [CalendarSystem.QUARTERLY, CalendarSystem.MONTHLY]
          : [CalendarSystem.MONTHLY],
      },
    });

    this.loggerService.log(
      `[ComputationCycleDayCron] Firing trigger for ${companies.length} companies.`,
    );
    for (let i = 0; i < companies.length; i++) {
      const company = companies[i];
      const brands = await this.brandService.findByCompanyId(company._id);

      const doesTemplateExist = await this.templateService.checkTemplateExists({
        ownerIds: [company._id, ...brands.map((brand) => brand._id)],
        triggerActions: [TriggerAction.ON_COMPUTATION_CYCLE_DAY],
      });

      this.loggerService.log(
        `[ComputationCycleDayCron] "${company.name}" does${doesTemplateExist ? '' : ' not'} have a computation cycle day template.`,
      );
      if (!doesTemplateExist) continue;

      await this.fireComputationCycleDayForCompany(company._id, company.name);
    }
  }

  private async fireComputationCycleDayForCompany(
    companyId: Types.ObjectId,
    companyName: string,
  ) {
    const customers =
      await this.customerRepository.findRecentlyComputed(companyId);

    this.loggerService.log(
      `[ComputationCycleDayCron] Found ${customers.length} customers for "${companyName}"`,
    );
    for (let i = 0; i < customers.length; i++) {
      const customer = customers[i];
      await this.customerNotificationService.fireOnComputationCycleDay(
        customer,
      );
    }
  }

  @OnEvent('cron.everyDayAt1145PM')
  @LogError()
  async processRelativeCalendarCycles() {
    this.loggerService.log(
      '[Cron Job Schedular] process relative calendar cycles,everyDayAt1145PM',
    );

    const companies = await this.companyService.findAll({
      hasLoyaltyProgram: true,
      'loyaltyProgramConfig.hasLoyaltyTiers': true,
      'loyaltyProgramConfig.calendarSystem': CalendarSystem.LAST_12_MONTHS,
    });

    for (let i = 0; i < companies.length; i++) {
      const company = companies[i];
      await this.resetCustomerCalendarCycles(
        company,
        moment.utc().subtract(12, 'months').toDate(),
      );
    }
  }

  @OnEvent('cron.everyDayAt1145AM')
  @LogError()
  async fireTierDowngradeWarningForRelativeCalendarSystem() {
    this.loggerService.log(
      '[Cron Job Schedular] fire Tier Downgrade Warning For Relative Calendar System , everyDayAt1145AM',
    );

    const targetDate = moment.utc().add(45, 'days').subtract(12, 'months');

    await this.fireTierDowngradeWarningForCalendarSystem(
      CalendarSystem.LAST_12_MONTHS,
      {
        start: targetDate.startOf('day').toDate(),
        end: targetDate.endOf('day').toDate(),
      },
    );
  }

  @OnEvent('cron.everyDayAt1130AM')
  @LogError()
  async fireGracePeriodReminderTriggers() {
    this.loggerService.log(
      '[Cron Job Schedular] fire Grace Period Reminder Triggers , everyDayAt1130AM',
    );
    const companies = await this.companyService.findAll({
      hasLoyaltyProgram: true,
      'loyaltyProgramConfig.hasLoyaltyTiers': true,
      'loyaltyProgramConfig.calendarSystem': {
        $in: [CalendarSystem.MONTHLY, CalendarSystem.QUARTERLY],
      },
    });

    const monthlyCustomers = await this.getCustomersForCalendarSystem(
      companies,
      CalendarSystem.MONTHLY,
    );

    const quarterlyCustomers = await this.getCustomersForCalendarSystem(
      companies,
      CalendarSystem.QUARTERLY,
    );

    const eligibleCustomers = await this.filterCustomersForGracePeriodReminder([
      ...monthlyCustomers,
      ...quarterlyCustomers,
    ]);

    const firedTriggers = eligibleCustomers.map(
      async (customer) =>
        await this.customerNotificationService.fireOnFirstGracePeriodReminderTrigger(
          customer,
        ),
    );

    await Promise.all(firedTriggers);
  }

  @OnEvent('cron.every1stDayOfMonthAtMidnight')
  @LogError()
  async processMonthlyCalendarCycles() {
    this.loggerService.log(
      '[Cron Job Schedular] fire Grace Period Reminder Triggers,every1stDayOfMonthAtMidnight',
    );
    const companies = await this.companyService.findAll({
      hasLoyaltyProgram: true,
      'loyaltyProgramConfig.hasLoyaltyTiers': true,
      'loyaltyProgramConfig.calendarSystem': CalendarSystem.MONTHLY,
    });

    await forEachAsync(companies, (company) =>
      this.resetCustomerCalendarCycles(company),
    );
  }

  @OnEvent('cron.every1stDayOfQuarterAt30MinAfterMidnight')
  @LogError()
  async processQuarterlyCalendarCycles() {
    this.loggerService.log(
      '[Cron Job Schedular] process Quarterly Calendar Cycles,every1stDayOfQuarterAt30MinAfterMidnights',
    );
    const companies = await this.companyService.findAll({
      hasLoyaltyProgram: true,
      'loyaltyProgramConfig.hasLoyaltyTiers': true,
      'loyaltyProgramConfig.calendarSystem': CalendarSystem.QUARTERLY,
    });

    await forEachAsync(companies, (company) =>
      this.resetCustomerCalendarCycles(company),
    );
  }

  @OnEvent('cron.everyFebruary18At1130')
  @LogError()
  async onTenDaysBeforeFebruary() {
    this.loggerService.log(
      '[Cron Job Schedular] on Ten Days Before February,everyFebruary18At1130',
    );
    return await this.fireTierDowngradeWarningForCalendarSystem(
      CalendarSystem.MONTHLY,
    );
  }

  @OnEvent('cron.every30DayMonth20thAt1130')
  @LogError()
  async onTenDaysBeforeThirtyDayMonth() {
    this.loggerService.log(
      '[Cron Job Schedular] on Ten Days Before Thirty Day Month,every30DayMonth20thAt1130',
    );
    return await this.fireTierDowngradeWarningForCalendarSystem(
      CalendarSystem.MONTHLY,
    );
  }

  @OnEvent('cron.every31DayMonth21stAt1130')
  @LogError()
  async onTenDaysBeforeThirtyOneDayMonth() {
    this.loggerService.log(
      '[Cron Job Schedular] on Ten Days Before Thirty one Day Month,every31DayMonth21stAt1130',
    );
    return await this.fireTierDowngradeWarningForCalendarSystem(
      CalendarSystem.MONTHLY,
    );
  }

  @OnEvent('cron.everyQuarter30Day5thAt1130')
  @LogError()
  async onTwentyFiveDaysBeforeThirtyDayQuarter() {
    this.loggerService.log(
      '[Cron Job Schedular] on Twenty Five Days Before Thirty Day Quarter,everyQuarter30Day5thAt1130',
    );
    return await this.fireTierDowngradeWarningForCalendarSystem(
      CalendarSystem.QUARTERLY,
    );
  }

  @OnEvent('cron.everyQuarter31Day6thAt1130')
  @LogError()
  async onTwentyFiveDaysBeforeThirtyOneDayQuarter() {
    this.loggerService.log(
      '[Cron Job Schedular] on Twenty Five Days Before Thirty One Day Quarter,everyQuarter31Day6thAt1130',
    );
    return await this.fireTierDowngradeWarningForCalendarSystem(
      CalendarSystem.QUARTERLY,
    );
  }

  private async getCustomersForCalendarSystem(
    companies: CompanyDocument[],
    calendarSystem: CalendarSystem,
  ): Promise<CustomerDocument[]> {
    const companyIds = companies
      .filter(
        (company: CompanyDocument): boolean =>
          company.loyaltyProgramConfig.calendarSystem === calendarSystem,
      )
      .map((company: CompanyDocument): Types.ObjectId => company._id);

    return await this.customerRepository.findEligibleForGracePeriodReminder(
      companyIds,
      this.getRegistrationDateForCalendarSystem(calendarSystem),
    );
  }

  private getRegistrationDateForCalendarSystem(
    calendarSystem: CalendarSystem,
  ): Date {
    const throwawayDate = new Date(2000, 1, 1);

    if (calendarSystem === CalendarSystem.MONTHLY) {
      const oneMonthAgo = moment.utc().subtract(30, 'days');
      return oneMonthAgo.date() > 3 ? oneMonthAgo.toDate() : throwawayDate;
    }

    if (calendarSystem === CalendarSystem.QUARTERLY) {
      const oneQuarterAgo = moment.utc().subtract(90, 'days');
      const startOfQuarter = moment.utc(oneQuarterAgo).startOf('quarter');

      const isStartOfQuarter = oneQuarterAgo.isSame(startOfQuarter, 'month');
      const isEligibleDay = !isStartOfQuarter || oneQuarterAgo.date() > 14;
      return isEligibleDay ? oneQuarterAgo.toDate() : throwawayDate;
    }

    return throwawayDate;
  }

  private async filterCustomersForGracePeriodReminder(
    customers: CustomerDocument[],
  ) {
    const eligibleCustomerOrUndefined = await mapAsync(
      customers,
      async (customer) => {
        const hasCustomerMaintainedTier =
          await this.customerLoyaltyTierInfoService.hasCustomerMaintainedTier(
            customer,
          );
        return hasCustomerMaintainedTier ? undefined : customer;
      },
    );

    const eligibleCustomers = eligibleCustomerOrUndefined.filter(
      (customer) => customer !== undefined,
    );

    return eligibleCustomers;
  }

  private async resetCustomerCalendarCycles(
    company: CompanyDocument,
    tierUpdatedBefore?: Date,
  ) {
    const customers =
      await this.customerRepository.findEligibleForTierComputation(
        [company._id],
        tierUpdatedBefore,
      );

    this.loggerService.log(
      `Tier Update for ${customers.length} customers for company ${company.name}`,
    );

    for (let i = 0; i < customers.length; i++) {
      const customer = customers[i];

      const result = await this.customerTierService
        .resetCustomerCalendarCycle(customer, company)
        .catch((error) => {
          const message = `Error processing customer tier update for customer ${customer._id}`;
          this.loggerService.error(message, error);
          return {
            message,
            error: error,
            stack: error?.stack,
          };
        });

      this.loggerService.log(
        `Customer Tier Update Result for ${customer._id}`,
        result,
      );
    }
  }

  private async fireTierDowngradeWarningForCalendarSystem(
    calendarSystem: CalendarSystem,
    targetDateRange?: { start: Date; end: Date },
  ) {
    const companies = await this.companyService.findAll({
      hasLoyaltyProgram: true,
      'loyaltyProgramConfig.hasLoyaltyTiers': true,
      'loyaltyProgramConfig.calendarSystem': calendarSystem,
    });

    this.loggerService.log(
      `[TierDowngrade] Firing for ${companies.length} companies`,
    );
    for (let i = 0; i < companies.length; i++) {
      const company = companies[i];
      const brands = await this.brandService.findByCompanyId(company._id);

      const doesTemplateExist = await this.templateService.checkTemplateExists({
        ownerIds: [company._id, ...brands.map((brand) => brand._id)],
        triggerActions: [
          TriggerAction.ON_TIER_DOWNGRADE_WARNING_BELOW_THRESHOLD,
          TriggerAction.ON_TIER_DOWNGRADE_WARNING_EXCEEDED_THRESHOLD,
        ],
      });

      this.loggerService.log(
        `[TierDowngrade] "${company.name}" does${doesTemplateExist ? '' : ' not'} have a downgrade template.`,
      );
      if (!doesTemplateExist) continue;

      const customers =
        await this.customerRepository.findEligibleForTierComputation(
          [company._id],
          targetDateRange?.start,
          targetDateRange?.end,
          TierStatus.NOT_MAINTAINED,
        );

      this.loggerService.log(
        `[TierDowngrade] "Found ${customers.length} customers for "${company.name}"`,
      );
      await this.fireTierDowngradeWarning(company, customers);
    }
  }

  private async fireTierDowngradeWarning(
    company: CompanyDocument,
    customers: CustomerDocument[],
  ): Promise<void> {
    for (let i = 0; i < customers.length; i++) {
      const customer = customers[i];
      await this.customerNotificationService.fireOnTierDowngradeWarningTrigger(
        customer,
        await this.isThresholdMet(company, customer),
      );
    }
  }

  private async isThresholdMet(
    company: CompanyDocument,
    customer: CustomerDocument,
  ): Promise<boolean> {
    const companyThreshold =
      company?.loyaltyProgramConfig?.downgradeWarningThreshold;
    const defaultThreshold = 70;
    const threshold = companyThreshold ?? defaultThreshold;
    const thresholdPercent = threshold / 100;

    return await this.companyService.evaluateTierLevellingUpMethod(company, {
      orderRate: () => this.isOrderRateThresholdMet(customer, thresholdPercent),
      amountSpent: () =>
        this.isAmountSpentThresholdMet(customer, thresholdPercent),
      pointsRate: () =>
        this.isPointsRateThresholdMet(customer, thresholdPercent),
      fallback: false,
    });
  }

  private async isOrderRateThresholdMet(
    customer: CustomerDocument,
    thresholdPercent: number,
  ) {
    const remainingOrdersCurrentTier =
      await this.customerLoyaltyTierInfoService.getRemainingOrdersCurrentTier(
        customer,
      );
    const remainingOrdersPercent =
      remainingOrdersCurrentTier / customer.loyaltyTier.orderRateThreshold;
    return 1 - remainingOrdersPercent >= thresholdPercent;
  }

  private async isAmountSpentThresholdMet(
    customer: CustomerDocument,
    thresholdPercent: number,
  ) {
    const remainingAmountCurrentTier =
      await this.customerLoyaltyTierInfoService.getRemainingAmountSpentCurrentTier(
        customer,
      );
    const remainingAmountPercent =
      remainingAmountCurrentTier / customer.loyaltyTier?.amountSpentThreshold;
    return 1 - remainingAmountPercent >= thresholdPercent;
  }

  private async isPointsRateThresholdMet(
    customer: CustomerDocument,
    thresholdPercent: number,
  ) {
    const remainingPointsCurrentTier =
      this.customerLoyaltyTierInfoService.computeRemainingPoints(
        customer.loyaltyTier,
        await this.customerLoyaltyTierInfoService.getTotalLoyaltyPointsRate(
          customer,
        ),
      );
    const remainingAmountPercent =
      remainingPointsCurrentTier / customer.loyaltyTier?.pointsRateThreshold;
    return 1 - remainingAmountPercent >= thresholdPercent;
  }
}
