import { PaymentMethod, ObjectIdTransform } from '@app/shared-stuff';
import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { Types } from 'mongoose';
import { CardDetails } from './card-details.dto';

export class PaymentToProcess {
  @ApiProperty({
    type: String,
    required: true,
  })
  @ObjectIdTransform()
  paymentId: Types.ObjectId;

  @ApiProperty({
    type: String,
    enum: PaymentMethod,
    required: true,
  })
  @IsNotEmpty()
  @IsEnum(PaymentMethod)
  paymentMethod: PaymentMethod;

  @ApiProperty({
    type: CardDetails,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  cardDetails?: CardDetails;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  appleToken?: string;
}
