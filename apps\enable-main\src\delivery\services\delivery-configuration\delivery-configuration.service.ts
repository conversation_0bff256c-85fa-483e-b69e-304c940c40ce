import {
  CollectionName,
  CreateDeliveryConfigurationDto,
  DeliveryConfigurationSearchType,
  DeliveryMethod,
  GetDeliveryConfigurationDto,
  IndexDeliveryConfigurationDto,
  OrderDocument,
  responseCode,
  UpdateDeliveryConfigurationDto,
} from '@app/shared-stuff';
import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { plainToInstance } from 'class-transformer';
import { Model, Types } from 'mongoose';
import { HelperService } from '../../../shared/services/helper/helper.service';
import {
  DeliveryConfiguration,
  DeliveryConfigurationDocument,
  DeliveryConfigurationWithId,
} from '../../models/delivery-configuration.model';
import { DeliveryConfigurationRepositoryInterface } from '../../repositories/delivery-configuration.repository.interface';
import { DeliveryThirdPartyVehicleConfig } from '../../types/third-party-vehicle-response.type';
import { ThirdPartyService } from '../third-parties/third-party.service';
import { DeliveryConfigurationServiceInterface } from './delivery-configuration.service.interface';

@Injectable()
export class DeliveryConfigurationService
  implements DeliveryConfigurationServiceInterface
{
  constructor(
    @Inject(DeliveryConfigurationRepositoryInterface)
    private readonly deliveryConfigurationRepository: DeliveryConfigurationRepositoryInterface,
    //TODO refactor this part in order refactor task
    @InjectModel(CollectionName.ORDER) private orderModel: Model<OrderDocument>,
    private helperService: HelperService,
    private thirdPartyService: ThirdPartyService,
  ) {}

  async create(
    createDeliveryConfigurationDto: CreateDeliveryConfigurationDto,
  ): Promise<DeliveryConfigurationDocument> {
    this.helperService.checkDeliveryOptionExist(
      createDeliveryConfigurationDto.usingBranchDrivers,
      createDeliveryConfigurationDto.usingCompanyDrivers,
      createDeliveryConfigurationDto.usingEbDelivery,
      createDeliveryConfigurationDto.usingThirdParty,
    );
    const deliveryConfiguration = plainToInstance(
      DeliveryConfiguration,
      createDeliveryConfigurationDto,
      {
        excludeExtraneousValues: true,
      },
    );
    deliveryConfiguration.usingBranchDrivers =
      createDeliveryConfigurationDto.usingBranchDrivers;
    deliveryConfiguration.usingCompanyDrivers =
      createDeliveryConfigurationDto.usingCompanyDrivers;
    deliveryConfiguration.usingEbDelivery =
      createDeliveryConfigurationDto.usingEbDelivery;
    deliveryConfiguration.usingThirdParty =
      createDeliveryConfigurationDto.usingThirdParty;
    deliveryConfiguration.defaultDeliveryMethod =
      createDeliveryConfigurationDto.defaultDeliveryMethod;
    deliveryConfiguration.thirdPartyConfiguration =
      createDeliveryConfigurationDto.thirdPartyConfiguration;
    deliveryConfiguration.companyId = createDeliveryConfigurationDto.companyId;
    deliveryConfiguration.brandId = createDeliveryConfigurationDto.brandId;
    deliveryConfiguration.branchId = createDeliveryConfigurationDto.branchId;
    return await this.deliveryConfigurationRepository.create(
      deliveryConfiguration,
    );
  }

  async update(
    updateDeliveryConfigurationDto: UpdateDeliveryConfigurationDto,
  ): Promise<DeliveryConfigurationDocument> {
    this.helperService.checkDeliveryOptionExist(
      updateDeliveryConfigurationDto.usingBranchDrivers,
      updateDeliveryConfigurationDto.usingCompanyDrivers,
      updateDeliveryConfigurationDto.usingEbDelivery,
      updateDeliveryConfigurationDto.usingThirdParty,
    );
    const deliveryConfiguration = plainToInstance(
      DeliveryConfiguration,
      updateDeliveryConfigurationDto,
      {
        excludeExtraneousValues: true,
      },
    );

    deliveryConfiguration.usingBranchDrivers =
      updateDeliveryConfigurationDto.usingBranchDrivers;
    deliveryConfiguration.usingCompanyDrivers =
      updateDeliveryConfigurationDto.usingCompanyDrivers;
    deliveryConfiguration.usingEbDelivery =
      updateDeliveryConfigurationDto.usingEbDelivery;
    deliveryConfiguration.usingThirdParty =
      updateDeliveryConfigurationDto.usingThirdParty;
    deliveryConfiguration.defaultDeliveryMethod =
      updateDeliveryConfigurationDto.defaultDeliveryMethod;
    deliveryConfiguration.companyId = updateDeliveryConfigurationDto.companyId;
    deliveryConfiguration.brandId = updateDeliveryConfigurationDto.brandId;
    deliveryConfiguration.branchId = updateDeliveryConfigurationDto.branchId;

    if (!updateDeliveryConfigurationDto.defaultDeliveryMethod)
      await this.deliveryConfigurationRepository.deleteDefaultDeliveryMethod(
        updateDeliveryConfigurationDto._id,
      );
    if (updateDeliveryConfigurationDto.usingThirdParty === false)
      await this.deliveryConfigurationRepository.deleteThirdPartyConfiguration(
        updateDeliveryConfigurationDto._id,
      );
    else
      deliveryConfiguration.thirdPartyConfiguration =
        updateDeliveryConfigurationDto.thirdPartyConfiguration;
    const updatedDeliveryConfiguration =
      await this.deliveryConfigurationRepository.findOneAndUpdate(
        { _id: updateDeliveryConfigurationDto._id },
        deliveryConfiguration,
      );

    if (!updatedDeliveryConfiguration)
      throw new NotFoundException(
        `Delivery Configuration with ID ${updateDeliveryConfigurationDto._id} not found.`,
      );

    return updatedDeliveryConfiguration;
  }

  async index(
    indexDeliveryConfigurationDto: IndexDeliveryConfigurationDto,
  ): Promise<any> {
    const pipeline = [],
      match = { deletedAt: null };
    this.addMatchStage(pipeline, match, indexDeliveryConfigurationDto);
    this.addPaginationStage(pipeline, indexDeliveryConfigurationDto);
    const selectedDeliveryConfigurations =
      await this.deliveryConfigurationRepository.aggregate(pipeline);
    return {
      DeliveryConfigurations:
        selectedDeliveryConfigurations[0]['paginatedResult'],
      totalCount: selectedDeliveryConfigurations[0]['totalCount'][0]
        ? selectedDeliveryConfigurations[0]['totalCount'][0]['createdAt']
        : 0,
    };
  }

  async findById(id: Types.ObjectId): Promise<DeliveryConfigurationDocument> {
    return await this.deliveryConfigurationRepository.findById(id);
  }

  async delete(id: Types.ObjectId): Promise<string> {
    const deliveryConfiguration = await this.findById(id);
    if (!deliveryConfiguration)
      throw new BadRequestException(
        "Delivery Configuration Doesn't Exist",
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    await this.deliveryConfigurationRepository.remove(id);
    return (
      'Delivery Configuration with ID ' +
      id.toString() +
      ' Deleted Successfully'
    );
  }

  async getDeliveryConfiguration(
    getDeliveryConfigurationDto: GetDeliveryConfigurationDto,
  ): Promise<DeliveryConfigurationWithId> {
    let deliveryConfiguration: DeliveryConfigurationDocument;
    if (getDeliveryConfigurationDto.branchId) {
      deliveryConfiguration =
        await this.deliveryConfigurationRepository.getByBranchId(
          getDeliveryConfigurationDto.branchId,
        );
    }
    if (getDeliveryConfigurationDto.brandId) {
      deliveryConfiguration =
        await this.deliveryConfigurationRepository.getByBrandId(
          getDeliveryConfigurationDto.brandId,
        );
    }
    if (getDeliveryConfigurationDto.companyId) {
      deliveryConfiguration = await this.findByCompanyId(
        new Types.ObjectId(getDeliveryConfigurationDto.companyId),
      );
    }
    if (getDeliveryConfigurationDto.orderId) {
      deliveryConfiguration = await this.getDeliveryConfigurationByOrderId(
        getDeliveryConfigurationDto.orderId,
      );
    }
    if (deliveryConfiguration) {
      const deliveryConfigurationAsJson =
        deliveryConfiguration.toObject<DeliveryConfigurationWithId>();
      deliveryConfigurationAsJson['enabledDeliveryMethodsCount'] =
        this.countEnabledDeliveryMethods(deliveryConfiguration);
      deliveryConfigurationAsJson['thirdPartyConfiguration'] &&
        (deliveryConfigurationAsJson['thirdPartyConfiguration'][
          'thirdPartyVehicleTypes'
        ] = await this.fetchThirdPartyVehicles(deliveryConfiguration));
      return deliveryConfigurationAsJson;
    }
    throw new BadRequestException({
      message: 'Please provide at least one parameter',
    });
  }

  async findByCompanyId(
    companyId: Types.ObjectId,
  ): Promise<DeliveryConfigurationDocument> {
    return await this.deliveryConfigurationRepository.getByCompanyId(companyId);
  }

  async findByBranchId(
    branchId: Types.ObjectId,
  ): Promise<DeliveryConfigurationDocument> {
    return await this.deliveryConfigurationRepository.findByBranchId(branchId);
  }

  async deleteByBranchId(branchId: Types.ObjectId): Promise<string> {
    await this.deliveryConfigurationRepository.remove(branchId);
    return (
      'Delivery Configuration with branch ID ' +
      branchId.toHexString() +
      ' Deleted Successfully'
    );
  }

  private addMatchStage(
    pipeline: any[],
    match: any,
    indexDeliveryConfigurationDto: IndexDeliveryConfigurationDto,
  ) {
    if (indexDeliveryConfigurationDto.search_key) {
      match[
        DeliveryConfigurationSearchType[
          indexDeliveryConfigurationDto.searchType
        ]
      ] = {
        $regex: indexDeliveryConfigurationDto.search_key,
        $options: 'i',
      };
      pipeline.push({ $match: match });
    }
  }

  private addPaginationStage(
    pipeline: any[],
    indexDeliveryConfigurationDto: IndexDeliveryConfigurationDto,
  ) {
    pipeline.push({
      $facet: {
        paginatedResult: [
          ...(Number(indexDeliveryConfigurationDto.offset)
            ? [
                {
                  $skip:
                    Number(indexDeliveryConfigurationDto.offset) *
                    Number(indexDeliveryConfigurationDto.limit),
                },
              ]
            : [
                {
                  $skip: 0,
                },
              ]),
          ...(Number(indexDeliveryConfigurationDto.limit)
            ? [
                {
                  $limit: Number(indexDeliveryConfigurationDto.limit),
                },
              ]
            : []),
        ],
        totalCount: [
          {
            $count: 'createdAt',
          },
        ],
      },
    });
  }

  private async fetchThirdPartyVehicles(
    deliveryConfiguration: DeliveryConfigurationDocument,
  ) {
    if (
      !deliveryConfiguration.usingThirdParty ||
      !deliveryConfiguration.thirdPartyConfiguration
    )
      return {};

    const thirdParties =
      deliveryConfiguration.thirdPartyConfiguration.thirdParties;
    return thirdParties.reduce<DeliveryThirdPartyVehicleConfig>(
      (acc, thirdParty) => {
        const vehicles = this.thirdPartyService.getVehicles(thirdParty);
        if (!vehicles || !vehicles.vehicleTypes.length) return acc;
        acc[thirdParty] = vehicles;
        return acc;
      },
      {},
    );
  }

  private countEnabledDeliveryMethods(
    deliveryConfiguration: DeliveryConfigurationDocument,
  ): number {
    let enabledDeliveryMethodsCount = 0;
    if (deliveryConfiguration.usingBranchDrivers) enabledDeliveryMethodsCount++;
    if (deliveryConfiguration.usingCompanyDrivers)
      enabledDeliveryMethodsCount++;
    if (deliveryConfiguration.usingEbDelivery) enabledDeliveryMethodsCount++;
    if (deliveryConfiguration.usingThirdParty) enabledDeliveryMethodsCount++;
    return enabledDeliveryMethodsCount;
  }

  private async getDeliveryConfigurationByOrderId(
    orderId: Types.ObjectId,
  ): Promise<DeliveryConfigurationDocument> {
    const order = await this.orderModel.findById(orderId);
    const branchId =
      typeof order.branch !== 'string'
        ? order.branch?._id
        : new Types.ObjectId(order.branch);
    const brandId = order.brand?._id;
    const companyId = order.company
      ? new Types.ObjectId(order.company)
      : undefined;
    const brandBranchDeliveryConfiguration =
      await this.deliveryConfigurationRepository.getByBrandIdAndBranchId(
        branchId,
        brandId,
      );
    let branchDeliveryConfiguration =
      await this.deliveryConfigurationRepository.getByBranchId(branchId);
    const companyDeliveryConfiguration =
      await this.deliveryConfigurationRepository.getByCompanyId(companyId);
    if (
      companyDeliveryConfiguration &&
      typeof order.branch !== 'string' &&
      order.branch.isFollowingCompanyDeliveryConfiguration === true
    )
      return companyDeliveryConfiguration;
    const brandDeliveryConfiguration =
      await this.deliveryConfigurationRepository.getByBrandId(brandId);
    if (
      companyDeliveryConfiguration &&
      branchDeliveryConfiguration &&
      order.branch &&
      typeof order.branch !== 'string' &&
      order.branch.isFollowingCompanyDeliveryConfiguration === false
    ) {
      branchDeliveryConfiguration =
        this.syncBranchConfigurationsWithCompanyConfigurations(
          companyDeliveryConfiguration,
          branchDeliveryConfiguration,
        );
    }

    return (
      brandBranchDeliveryConfiguration ||
      branchDeliveryConfiguration ||
      brandDeliveryConfiguration ||
      companyDeliveryConfiguration ||
      null
    );
  }

  private syncBranchConfigurationsWithCompanyConfigurations(
    companyDeliveryConfiguration: DeliveryConfigurationDocument,
    branchDeliveryConfiguration: DeliveryConfigurationDocument,
  ): DeliveryConfigurationDocument {
    if (companyDeliveryConfiguration.usingBranchDrivers == false)
      this.syncBranchDriversDeliveryConfiguration(
        companyDeliveryConfiguration,
        branchDeliveryConfiguration,
      );

    if (companyDeliveryConfiguration.usingCompanyDrivers == false)
      this.syncCompanyDriversDeliveryConfiguration(
        companyDeliveryConfiguration,
        branchDeliveryConfiguration,
      );

    if (companyDeliveryConfiguration.usingThirdParty == false)
      this.syncThirdPartyDeliveryConfiguration(
        companyDeliveryConfiguration,
        branchDeliveryConfiguration,
      );

    if (companyDeliveryConfiguration.usingEbDelivery == false)
      branchDeliveryConfiguration.usingEbDelivery = false;

    if (
      companyDeliveryConfiguration.thirdPartyConfiguration &&
      branchDeliveryConfiguration.thirdPartyConfiguration
    ) {
      this.syncBranchDeliveryConfigurationThirdPartiesArray(
        companyDeliveryConfiguration,
        branchDeliveryConfiguration,
      );
      if (
        companyDeliveryConfiguration.thirdPartyConfiguration
          .defaultThirdParty &&
        branchDeliveryConfiguration.thirdPartyConfiguration.defaultThirdParty
      )
        this.syncBranchDeliveryConfigurationDefaultThirdParty(
          companyDeliveryConfiguration,
          branchDeliveryConfiguration,
        );
    }

    return branchDeliveryConfiguration;
  }

  private syncBranchDeliveryConfigurationDefaultThirdParty(
    companyDeliveryConfiguration: DeliveryConfigurationDocument,
    branchDeliveryConfiguration: DeliveryConfigurationDocument,
  ) {
    if (
      branchDeliveryConfiguration?.thirdPartyConfiguration?.thirdParties.includes(
        branchDeliveryConfiguration?.thirdPartyConfiguration?.defaultThirdParty,
      ) == false
    ) {
      branchDeliveryConfiguration.thirdPartyConfiguration.defaultThirdParty =
        companyDeliveryConfiguration.thirdPartyConfiguration.defaultThirdParty;
    }
  }

  private syncBranchDeliveryConfigurationThirdPartiesArray(
    companyDeliveryConfiguration: DeliveryConfigurationDocument,
    branchDeliveryConfiguration: DeliveryConfigurationDocument,
  ) {
    const branchThirdPartiesLength =
      branchDeliveryConfiguration?.thirdPartyConfiguration?.thirdParties
        ?.length;
    for (let i = 0; i < branchThirdPartiesLength; i++) {
      if (
        companyDeliveryConfiguration?.thirdPartyConfiguration?.thirdParties?.includes(
          branchDeliveryConfiguration.thirdPartyConfiguration.thirdParties[i],
        ) == false
      )
        branchDeliveryConfiguration.thirdPartyConfiguration.thirdParties.splice(
          i,
          1,
        );
    }
  }

  private syncThirdPartyDeliveryConfiguration(
    companyDeliveryConfiguration: DeliveryConfigurationDocument,
    branchDeliveryConfiguration: DeliveryConfigurationDocument,
  ) {
    branchDeliveryConfiguration.usingThirdParty = false;
    if (
      branchDeliveryConfiguration.defaultDeliveryMethod ==
      DeliveryMethod.THIRD_PARTY
    )
      branchDeliveryConfiguration.defaultDeliveryMethod =
        companyDeliveryConfiguration.defaultDeliveryMethod;
  }

  private syncCompanyDriversDeliveryConfiguration(
    companyDeliveryConfiguration: DeliveryConfigurationDocument,
    branchDeliveryConfiguration: DeliveryConfigurationDocument,
  ) {
    branchDeliveryConfiguration.usingCompanyDrivers = false;
    if (
      branchDeliveryConfiguration.defaultDeliveryMethod ==
      DeliveryMethod.COMPANY_DRIVERS
    )
      branchDeliveryConfiguration.defaultDeliveryMethod =
        companyDeliveryConfiguration.defaultDeliveryMethod;
  }

  private syncBranchDriversDeliveryConfiguration(
    companyDeliveryConfiguration: DeliveryConfigurationDocument,
    branchDeliveryConfiguration: DeliveryConfigurationDocument,
  ) {
    branchDeliveryConfiguration.usingBranchDrivers = false;
    if (
      branchDeliveryConfiguration.defaultDeliveryMethod ==
      DeliveryMethod.BRANCH_DRIVERS
    )
      branchDeliveryConfiguration.defaultDeliveryMethod =
        companyDeliveryConfiguration.defaultDeliveryMethod;
  }
}
