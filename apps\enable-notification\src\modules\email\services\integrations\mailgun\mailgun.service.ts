import { EmailConfigDto, MailGunConfigDto } from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import * as formData from 'form-data';
import * as Mailgun from 'mailgun.js';
import { IMailgunClient } from 'mailgun.js/Interfaces';
import { EmailSendDto } from '../../../types/dtos/email-send.dto';
import { EmailIntegrationsServiceInterface } from './../email-intergrations.service.interface';

@Injectable()
export class MailgunService implements EmailIntegrationsServiceInterface {
  private mailgunClient: IMailgunClient;

  async send(emailSendDto: EmailSendDto, emailConfiguration: EmailConfigDto) {
    const mailgun = this.getCurrentMailGun(emailConfiguration);

    return await mailgun.messages.create(
      emailConfiguration.mailGunConfig.domain,
      {
        ...emailSendDto,
        text: emailSendDto.content,
      },
    );
  }

  private getCurrentMailGun(emailConfiguration: EmailConfigDto) {
    if (!this.mailgunClient) {
      this.mailgunClient = this.initMailGun(emailConfiguration.mailGunConfig);
    }
    return this.mailgunClient;
  }

  private initMailGun(mailgunConfigDto: MailGunConfigDto) {
    const mailgun = new Mailgun.default(formData);

    return mailgun.client({
      username: mailgunConfigDto.username,
      key: mailgunConfigDto.apiKey,
    });
  }
}
