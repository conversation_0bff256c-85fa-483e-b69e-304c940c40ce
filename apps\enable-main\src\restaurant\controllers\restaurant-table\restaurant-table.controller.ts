import { Request, Response } from 'express';
import { RestaurantTableService } from './../../services/restaurant-table/restaurant-table.service';
import { HelperService } from './../../../shared/services/helper/helper.service';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  SetMetadata,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import {
  responseCode,
  TableToCreate,
  TableToIndex,
  TableToUpdate,
} from '@app/shared-stuff';

@Controller('restaurant-table')
@ApiTags('Restaurant Table')
@SetMetadata('module', 'restaurant-table')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class RestaurantTableController {
  constructor(
    private helperService: HelperService,
    private tableService: RestaurantTableService,
  ) {}

  @Get()
  @SetMetadata('action', 'get_all')
  async index(@Query() taleToIndex: TableToIndex, @Res() res: Response) {
    try {
      const tables = await this.tableService.index(taleToIndex);
      const totalNumberOfTables =
        await this.tableService.getTotalNumberOfTables(taleToIndex);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to get all restaurant tables',
        { tables: tables, totalTables: totalNumberOfTables },
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  async getDetails(@Param('id') id: string, @Res() res: Response) {
    try {
      const item = await this.tableService.getDetails(id);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'success to get all restaurant table details',
        item,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() tableToCreate: TableToCreate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      tableToCreate['company'] = req['company_id'];
      tableToCreate.currentUser = req['current'];
      const item = await this.tableService.create(tableToCreate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success to Create Restaurant table',
        item,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Put(':id')
  @SetMetadata('action', 'update')
  async update(
    @Body() tableToUpdate: TableToUpdate,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      tableToUpdate['company'] = req['company_id'];
      tableToUpdate.currentUser = req['current'];
      const item = await this.tableService.update(tableToUpdate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_UPDATE,
        'success to Update restaurant table ',
        item,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Delete(':id')
  @SetMetadata('action', 'delete')
  async delete(
    @Param('id') id: string,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const item = await this.tableService.remove({
        _id: id,
        deletedBy: req['current'],
      });
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_REMOVE,
        'success to REMOVE Menus item',
        item,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
}
