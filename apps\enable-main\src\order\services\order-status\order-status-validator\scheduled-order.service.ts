import {
  CurrentUser,
  OrderDeliveryType,
  OrderDocument,
  OrderLogActionEnum,
  OrderStatusEnum,
  OrderTransitionTrigger,
  responseCode,
} from '@app/shared-stuff';
import { BadRequestException, Inject } from '@nestjs/common';
import { Types } from 'mongoose';
import { UserService } from '../../../../user/services/user/user.service';
import { OrderLogServiceInterface } from '../../interfaces/order-log.service.interface';
import { OrderStatusServiceInterface } from '../order-status.interface';

export class ScheduledOrderService implements OrderStatusServiceInterface {
  transitionalStatuses = [OrderStatusEnum.PENDING];
  transitionalTrigger = [
    OrderTransitionTrigger.ACKNOWLEDGED,
    OrderTransitionTrigger.AUTOMATIC_ACKNOWLEDGEMENT,
    OrderTransitionTrigger.DELIVERECT_STATUS_UPDATE,
  ];
  constructor(
    @Inject('OrderLogServiceInterface')
    private readonly orderLogService: OrderLogServiceInterface,
    private userService: UserService,
  ) {}

  validateStatus(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
  ): boolean {
    return (
      this.ValidateTransition(order, orderTransitionTrigger) &&
      this.validatePreCondition(order)
    );
  }

  private ValidateTransition(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
  ): boolean {
    if (
      this.transitionalTrigger.includes(orderTransitionTrigger) &&
      this.transitionalStatuses.includes(order.status)
    )
      return true;

    throw new BadRequestException(
      "Can't Change From " + order.status + ' To Scheduled',
      responseCode.STATUS_NOT_VALID.toString(),
    );
  }

  private validatePreCondition(order: OrderDocument): boolean {
    if (
      order.delivery_type != OrderDeliveryType.scheduled &&
      order.delivery_type != OrderDeliveryType.schedule_later
    ) {
      throw new BadRequestException(
        "Can't change From " +
          order.status +
          ' to  Scheduled' +
          'The  delivery type of The order with ID ' +
          order._id +
          ' is  Not Scheduled Or Scheduled Later ',
        responseCode.STATUS_NOT_VALID.toString(),
      );
    }
    if (!order.branch)
      throw new BadRequestException(
        "Can't change From " +
          order.status +
          ' to  Scheduled' +
          'The  the branch manager must accepts the order with ID ' +
          order._id,
        responseCode.STATUS_NOT_VALID.toString(),
      );
    return true;
  }

  async applyPostFunction(
    order: OrderDocument,
    orderTransitionTrigger: OrderTransitionTrigger,
    oldStatus: OrderStatusEnum,
    user: CurrentUser,
  ) {
    await this.userService.notifyUsersAboutOrderAcknowledged(
      new Types.ObjectId(order._id),
    );
    await this.userService.removeAcknowledgeOrdersFromUsers(
      order,
      'acknowledged',
    );

    order.delayTag = undefined;
    await order.save();

    await this.orderLogService.saveOrderLog(
      order,
      { oldStatus: oldStatus },
      { newStatus: OrderStatusEnum.SCHEDULED, trigger: orderTransitionTrigger },
      OrderLogActionEnum.ON_ORDER_NOTIFICATION_ACKNOWLEDGE,
      user,
    );
  }
}
