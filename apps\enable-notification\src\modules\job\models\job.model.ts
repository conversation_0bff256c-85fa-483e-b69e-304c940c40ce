import { Field } from '@app/shared-stuff';
import { Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, SchemaTypes } from 'mongoose';

export type JobDocument = Job & Document;

@Schema({ timestamps: true })
export class Job {
  @Field({ type: Date, required: true })
  scheduledAt: Date;

  @Field({ type: String, required: true })
  eventName: string;

  @Field({ type: SchemaTypes.Mixed, required: false, default: {} })
  payload: any;
}

export const JobSchema = SchemaFactory.createForClass(Job);
