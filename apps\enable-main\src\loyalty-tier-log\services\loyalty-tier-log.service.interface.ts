import {
  CalendarCycle,
  CurrentUser,
  CustomerDocument,
  LoyaltyTierLog,
  LoyaltyTierLogDocument,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export type LoyaltyTierLogger = (
  customer: CustomerDocument,
  previousTierId?: Types.ObjectId | null,
) => Promise<LoyaltyTierLogDocument>;

export interface LoyaltyTierLogServiceInterface {
  log(log: LoyaltyTierLog): Promise<LoyaltyTierLogDocument>;
  findHighestAssignedTierId(
    customer: CustomerDocument,
    calendarCycle: CalendarCycle,
  ): Promise<Types.ObjectId>;
  onBulkTierAssignment(
    customer: CustomerDocument,
    previousTierId: Types.ObjectId,
    currentUser: CurrentUser,
  ): Promise<LoyaltyTierLogDocument>;
  onManualTierDowngrade: LoyaltyTierLogger;
  onManualTierUpgrade: LoyaltyTierLogger;
  onOrderCompletionUpgrade: LoyaltyTierLogger;
  onTierComputationMaintain: LoyaltyTierLogger;
  onTierComputationDowngrade: LoyaltyTierLogger;
  onTierDiscountRedeem: LoyaltyTierLogger;
  onTierRequirementsLowered: LoyaltyTierLogger;
  onCalendarSystemUpdated: LoyaltyTierLogger;
  onBaseTierAssignment: LoyaltyTierLogger;
  onFreeDeliveryRedeem: LoyaltyTierLogger;
  onCustomerDeletion: LoyaltyTierLogger;
  onTierDeletion: LoyaltyTierLogger;
  onOrderDeletionDowngrade: LoyaltyTierLogger;
  onForceTierRecomputation: LoyaltyTierLogger;
}

export const LoyaltyTierLogServiceInterface = Symbol(
  'LoyaltyTierLogServiceInterface',
);
