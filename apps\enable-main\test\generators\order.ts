import * as moment from 'moment-timezone';
import { faker } from '@faker-js/faker';
import {
  Customer,
  DeliveryMethod,
  OrderCreationSource,
  OrderDeliveryAction,
  OrderDeliveryType,
  OrderPaymentMethod,
  OrderPosToCreate,
  OrderSource,
  TransportType,
} from '@app/shared-stuff';

export const generateRandomOrder = (
  customer: Customer,
  override: Partial<OrderPosToCreate> = {},
): Partial<OrderPosToCreate> => ({
  first_name: customer.first_name,
  last_name: customer.last_name,
  phone: customer.phone,
  email: customer.email,
  country_code: customer.country_code,
  isCustomerUpdatable: false,
  company: customer.company.toString(),
  creationSource: OrderCreationSource.ENABLE_MAIN,

  delivery_action: OrderDeliveryAction.DELIVERY_LOCATION,
  delivery_date: moment.utc().format('YYYY-MM-DD'),
  delivery_time: moment.utc().add(1, 'hour').format('HH:mm'),

  payment_method: OrderPaymentMethod.prepaid,
  orderIsAlreadyPaid: true,

  source: faker.helpers.objectValue(OrderSource),
  transport_type: faker.helpers.objectKey(TransportType),
  deliveryMethod: DeliveryMethod.BRANCH_DRIVERS,
  delivery_type: OrderDeliveryType.urgent,

  items: [
    {
      name: 'Mini Pancake\t',
      description: 'Original flavor.',
      price: 62.52,
      index: 0,
    } as any,
  ],
  invoiced_amount: 62.52,
  delivery_amount: 0,
  total_amount: 62.52,

  is_test: true,
  ...override,
});
