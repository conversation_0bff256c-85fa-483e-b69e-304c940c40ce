// [EBL-6012] Wallet Pass Builder v1.0
// Migrate `passConfig.fieldConfig.headerFields` and `passConfig.fieldConfig.secondaryFields`

db.pass_configs.bulkWrite(
  db.pass_configs
    .find(
      {
        $or: [
          { 'fieldConfig.headerFields.0': { $type: 'string' } },
          { 'fieldConfig.secondaryFields.0': { $type: 'string' } },
        ],
      },
      { fieldConfig: 1 },
    )
    .toArray()
    .map(({ _id, fieldConfig }) => ({
      updateOne: {
        filter: { _id },
        update: {
          $set: {
            ...(fieldConfig.headerFields?.length && {
              'fieldConfig.headerFields': fieldConfig.headerFields.map(
                (fieldName) => ({ fieldName }),
              ),
            }),
            ...(fieldConfig.secondaryFields?.length && {
              'fieldConfig.secondaryFields': fieldConfig.secondaryFields.map(
                (fieldName) => ({ fieldName }),
              ),
            }),
          },
        },
      },
    })),
  { ordered: false },
);
