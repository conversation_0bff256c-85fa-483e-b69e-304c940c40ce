import {
  GenericExceptionFilter,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Controller,
  Get,
  Param,
  Res,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { ImageService } from '../services/image/image.service';

@Controller('images')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags('Image')
@SetMetadata('module', 'image')
export class ImageController {
  constructor(private readonly imageService: ImageService) {}

  @Get(':text/barcode.png')
  @SetMetadata('public', 'true')
  @SetMetadata('exclude-from-transformation', true)
  async generateBarcode(@Param('text') text: string) {
    return this.imageService.generateBarcodeImage(text);
  }

  @Get(':folder/:name')
  @SetMetadata('public', 'true')
  async redirectToImage(
    @Param('folder') folder: string,
    @Param('name') name: string,
    @Res() res: Response,
  ) {
    const url = await this.imageService.getImageUrl(folder, name);
    return res.redirect(url);
  }
}
