import { FilterQuery, Types } from 'mongoose';

import {
  IGenericRepository,
  LoyaltyPointLog,
  LoyaltyPointLogDocument,
  PopulatedLoyaltyPointLog,
} from '@app/shared-stuff';

export interface LoyaltyPointLogRepositoryInterface
  extends IGenericRepository<LoyaltyPointLogDocument, LoyaltyPointLog> {
  create(
    log: Omit<LoyaltyPointLog, 'createdAt' | 'updatedAt'>,
  ): Promise<LoyaltyPointLogDocument>;
  findByCustomerId(
    customerId: Types.ObjectId,
  ): Promise<PopulatedLoyaltyPointLog[]>;
  exists(filter: FilterQuery<LoyaltyPointLog>): Promise<boolean>;
}

export const LoyaltyPointLogRepositoryInterface = Symbol(
  'LoyaltyPointLogRepositoryInterface',
);
