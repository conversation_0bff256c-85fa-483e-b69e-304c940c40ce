// EBL-4951 Multi Item Counter | Punch Card
// Initialize menuItemSources for existing menu item punch cards

db.punchcards.updateMany(
  {
    'counter.type': 'menu_item',
    'counter.menuItemSources': { $exists: false },
  },
  [
    {
      $set: {
        'counter.menuItemSources': [
          {
            brandId: '$counter.menuItem.brandId',
            menuId: '$counter.menuItem.menuId',
            masterMenuItemId: '$counter.menuItem.masterMenuItemId',
            masterMenuItemName: '$counter.menuItem.nameEn',
          },
        ],
      },
    },
  ],
);

db.punchcards.updateMany({ 'counter.type': 'menu_item' }, [
  { $unset: ['counter.menuItem.brandId', 'counter.menuItem.menuId'] },
]);
