import {
  CreateBrandDto,
  EmbeddedBrandDto,
  IndexBrandDto,
  PatchBrandConfigDto,
  UpdateBrandDto,
} from '@app/shared-stuff';
import { BrandDocument } from '@app/shared-stuff/models/brand.model';
import { Types } from 'mongoose';
import { BrandDetails } from '../../types/brand-details.type';

export interface BrandServiceInterface {
  index(
    indexBrandDto: IndexBrandDto,
    requestedBranches: Types.ObjectId[],
  ): Promise<any>;

  update(updateBrandDto: UpdateBrandDto): Promise<BrandDocument>;

  create(createBrandDto: CreateBrandDto): Promise<BrandDocument>;

  patchConfig(patchBrandConfigDto: PatchBrandConfigDto): Promise<void>;

  findById(id: Types.ObjectId): Promise<BrandDocument>;

  findByIdForPublicPages(id: Types.ObjectId): Promise<BrandDetails>;

  findByIdIn(ids: Types.ObjectId[]): Promise<BrandDocument[]>;

  findByBranchId(branchId: Types.ObjectId): Promise<BrandDocument[]>;

  findLoyaltyBrand(companyId: Types.ObjectId): Promise<BrandDocument | null>;
  toEmbeddedBrandDto(brand: BrandDocument): EmbeddedBrandDto;

  findOneByNameAndCompanyId(
    name: string,
    companyId: Types.ObjectId,
  ): Promise<BrandDocument | null>;

  findByCompanyId(companyId: Types.ObjectId): Promise<BrandDocument[]>;

  updatePwaBaseUrl(brandId: Types.ObjectId, pwaBaseUrl?: string): Promise<void>;
}
