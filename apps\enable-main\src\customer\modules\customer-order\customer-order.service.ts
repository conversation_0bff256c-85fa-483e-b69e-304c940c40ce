import {
  areObjectIdsEqual,
  BenefitType,
  BranchWithId,
  Company,
  CompanyDocument,
  ContactChannel,
  Coupon,
  CouponDocument,
  CustomerDocument,
  Discount,
  DiscountApplyTo,
  DiscountSource,
  DiscountType,
  EarnedStamp,
  EmbeddedBrandDto,
  embeddedCouponFields,
  EmbeddedOrderDto,
  forEachAsync,
  loyaltyOrderSources,
  LoyaltyPointLogAction,
  LoyaltyProgress,
  LoyaltyStatus,
  OrderDocument,
  orderEmbeddedTierFields,
  OrderSource,
  OrderStatusEnum,
  OrderToComplete,
  pick,
  PointsLoyaltyProgress,
  PunchCardLoyaltyProgress,
  PunchCardProgress,
  TierLoyaltyProgress,
  TrackingEvent,
  TrackingService,
  UnreachableError,
  ValidDiscount,
} from '@app/shared-stuff';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { BenefitTypeFactoryService } from '../../../benefit/services/types/benefit-type-factory.service';
import { CompanyService } from '../../../company/services/company/company.service';
import { CouponServiceInterface } from '../../../coupon/services/coupon.service.interface';
import { LoyaltyPointLogServiceInterface } from '../../../loyalty-point-log/services/loyalty-point-log.service.interface';
import { LoyaltyTierLogServiceInterface } from '../../../loyalty-tier-log/services/loyalty-tier-log.service.interface';
import { OrderRedemptionService } from '../../../order/modules/order-redemption/order-redemption.service';
import { CustomerLoyaltyMemberServiceInterface } from '../customer-loyalty-member/customer-loyalty-member.service.interface';
import { CustomerPointsService } from '../customer-points/customer-points.service';
import { CustomerPunchCardServiceInterface } from '../customer-punch-card/customer-punch-card.service.interface';
import { CustomerReadServiceInterface } from '../customer-read/customer-read.service.interface';
import { CustomerRepositoryInterface } from '../customer-repository/customer.repository.interface';
import { CustomerTierInfoServiceInterface } from '../customer-tier-info/customer-tier-info.service.interface';
import { CustomerTierServiceInterface } from '../customer-tier/customer-tier.service.interface';
import { CustomerWebstoreServiceInterface } from '../customer-webstore/customer-webstore.service.interface';
import { CustomerOrderServiceInterface } from './customer-order.service.interface';

@Injectable()
export class CustomerOrderService implements CustomerOrderServiceInterface {
  private readonly loggerService = new Logger(CustomerOrderService.name);

  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly trackingService: TrackingService,
    private readonly companyService: CompanyService,
    private readonly customerPointsService: CustomerPointsService,
    @Inject(CustomerRepositoryInterface)
    private readonly customerRepository: CustomerRepositoryInterface,
    @Inject(CustomerReadServiceInterface)
    private readonly customerReadService: CustomerReadServiceInterface,
    @Inject(CustomerTierServiceInterface)
    private readonly customerTierService: CustomerTierServiceInterface,
    @Inject(CustomerTierInfoServiceInterface)
    private readonly customerTierInfoService: CustomerTierInfoServiceInterface,
    @Inject(CustomerWebstoreServiceInterface)
    private readonly customerWebstoreService: CustomerWebstoreServiceInterface,
    @Inject(CustomerLoyaltyMemberServiceInterface)
    private readonly customerLoyaltyMemberService: CustomerLoyaltyMemberServiceInterface,
    @Inject(CustomerPunchCardServiceInterface)
    private readonly customerPunchCardService: CustomerPunchCardServiceInterface,
    @Inject(CouponServiceInterface)
    private readonly couponService: CouponServiceInterface,
    @Inject(LoyaltyPointLogServiceInterface)
    private readonly loyaltyPointLogService: LoyaltyPointLogServiceInterface,
    @Inject(LoyaltyTierLogServiceInterface)
    private readonly loyaltyTierLogService: LoyaltyTierLogServiceInterface,
    private readonly orderRedemptionService: OrderRedemptionService,
    private readonly benefitTypeFactoryService: BenefitTypeFactoryService,
  ) {}

  async computeLoyaltyProgress(
    company: CompanyDocument,
    order: OrderDocument,
    customer: CustomerDocument,
  ): Promise<LoyaltyProgress> {
    return {
      ...(await this.getPointsLoyaltyProgress(company, order, customer)),
      ...(await this.getTierLoyaltyProgress(company, order, customer)),
      ...(await this.getPunchCardLoyaltyProgress(company, order, customer)),
    };
  }

  async getPointsLoyaltyProgress(
    company: CompanyDocument,
    order: OrderDocument,
    customer: CustomerDocument,
  ): Promise<Partial<PointsLoyaltyProgress>> {
    if (!company.loyaltyProgramConfig?.hasLoyaltyPoints) return {};

    const coupons = await this.couponService.findByCompanyId(company._id);

    const pointsRedeemedOnCoupons = this.calculatePointsRedeemedOnCoupons(
      order,
      coupons,
    );

    const pointsRedeemedOnFlexibleRedemptions =
      this.orderRedemptionService.calculatePointsRedeemedOnFlexibleRedemptions(
        order,
        company,
      );

    const pointsRedeemed =
      pointsRedeemedOnCoupons + pointsRedeemedOnFlexibleRedemptions;

    const pointsEarned =
      this.customerPointsService.calculatePointsEarnedOnOrder(
        order,
        customer,
        company,
      );

    const newBalance = (customer.loyaltyPoints || 0) + pointsEarned;
    const unlockedCoupons = coupons.filter(
      (coupon) =>
        coupon.loyaltyPointCost > (customer.loyaltyPoints || 0) &&
        coupon.loyaltyPointCost <= newBalance,
    );
    const couponsUnlocked = unlockedCoupons.length;
    const highestCouponUnlocked = pick(
      unlockedCoupons.reduce(
        (highestCoupon, currentCoupon) =>
          highestCoupon &&
          highestCoupon.loyaltyPointCost >= currentCoupon.loyaltyPointCost
            ? highestCoupon
            : currentCoupon,
        null,
      ),
      embeddedCouponFields,
    );

    return {
      pointsRedeemed,
      pointsEarned,
      couponsUnlocked,
      highestCouponUnlocked,
    };
  }

  private calculatePointsRedeemedOnCoupons(
    order: OrderDocument,
    coupons: CouponDocument[],
  ): number {
    if (!order.couponId) return 0;

    const appliedCoupon = coupons.find((coupon) =>
      areObjectIdsEqual(coupon._id, order.couponId),
    );

    return appliedCoupon?.loyaltyPointCost ?? 0;
  }

  async getTierLoyaltyProgress(
    company: CompanyDocument,
    order: OrderDocument,
    customer: CustomerDocument,
  ): Promise<Partial<TierLoyaltyProgress>> {
    if (!company.loyaltyProgramConfig?.hasLoyaltyTiers) return {};

    const tier = customer.loyaltyTier
      ? pick(customer.loyaltyTier, orderEmbeddedTierFields)
      : null;
    const embeddedOrderDto = {
      ...this.getEmbeddedOrderDto(company, order, customer),
      status: OrderStatusEnum.COMPLETED,
    };
    const isLoyaltyOrder = this.customerTierInfoService.createIsLoyaltyOrder(
      customer,
      company,
    );
    if (!isLoyaltyOrder(embeddedOrderDto)) {
      this.loggerService.log(
        `${embeddedOrderDto._id.toString()} is not a loyalty order, no tier progress will be earned.`,
      );
      return { tier, tierMaintained: false, tierUpgraded: false };
    }

    return {
      tier,
      tierMaintained: await this.customerTierInfoService.willTierBeMaintained(
        company,
        embeddedOrderDto,
        customer,
      ),
      tierUpgraded: await this.customerTierInfoService.willTierBeUpgraded(
        company,
        embeddedOrderDto,
        customer,
      ),
    };
  }

  async getPunchCardLoyaltyProgress(
    company: CompanyDocument,
    order: OrderDocument,
    customer: CustomerDocument,
  ): Promise<Partial<PunchCardLoyaltyProgress>> {
    if (!company.loyaltyProgramConfig?.hasLoyaltyPunchCards) return {};

    const punchCardProgresses = await this.getPunchCardProgresses(customer);

    const earnedStamps: EarnedStamp[] = [];
    let stampsEarned = 0;
    let rewardsUnlocked = 0;
    let rewardsUnlockedFirstTrack = 0;
    let rewardsUnlockedSecondTrack = 0;

    await forEachAsync(punchCardProgresses, async (progress, track) => {
      const increment = this.customerPunchCardService.getIncrementAmount(
        progress,
        {
          ...(order.toJSON() as OrderToComplete),
          isLoyaltyOrder: this.isLoyaltyOrder(order, company),
        },
      );
      stampsEarned += increment;
      earnedStamps.push({
        punchCardId: progress.punchCard._id,
        punchCardName: progress.punchCard.nameEn,
        count: increment,
      });

      const rewardsUnlockedOnIncrement =
        await this.customerPunchCardService.getRewardsUnlockedOnIncrement(
          customer,
          progress,
          increment,
        );
      rewardsUnlocked += rewardsUnlockedOnIncrement;
      if (track === 0) rewardsUnlockedFirstTrack += rewardsUnlockedOnIncrement;
      if (track === 1) rewardsUnlockedSecondTrack += rewardsUnlockedOnIncrement;
    });

    return {
      stampsEarned,
      rewardsUnlocked,
      earnedStamps,
      rewardsUnlockedFirstTrack,
      rewardsUnlockedSecondTrack,
    };
  }

  private async getPunchCardProgresses(
    customer: CustomerDocument,
  ): Promise<PunchCardProgress[]> {
    const punchCardsToInit =
      await this.customerPunchCardService.findPunchCardsToInit(customer);
    const newPunchCardProgresses = punchCardsToInit.map(
      (punchCard) => new PunchCardProgress(punchCard),
    );

    const activePunchCardProgresses = (customer.punchCardProgress || []).filter(
      (progress) => !progress.completedAt,
    );

    return newPunchCardProgresses.concat(activePunchCardProgresses);
  }

  private getEmbeddedOrderDto(
    company: CompanyDocument,
    order: OrderDocument,
    customer: CustomerDocument,
  ): EmbeddedOrderDto {
    return {
      _id: order._id,
      source: order.source,
      status: order.status,
      pickup_date: order.pickup_date,
      delivery_date: order.delivery_date,
      isCartValueThresholdMet: this.getIsCartValueThresholdMet(order, company),
      isOrderCounted: this.getIsOrderCounted(customer, company),
      total_amount: order.total_amount,
      loyaltyProgress: pick(order.loyaltyProgress || {}, ['pointsEarned']),
    };
  }

  async assignOrderToCustomer(
    order: OrderDocument,
    customer: CustomerDocument,
  ): Promise<CustomerDocument> {
    const company = await this.companyService.findById(customer.company);
    const embeddedOrderDto: EmbeddedOrderDto = this.getEmbeddedOrderDto(
      company,
      order,
      customer,
    );
    const orders = customer.orders
      ? [...customer.orders, embeddedOrderDto]
      : [embeddedOrderDto];
    const newNumberOfOrders = (customer.number_of_orders || 0) + 1;
    const newTotalOrdersAmount =
      customer.total_orders_amount + (order.total_amount || 0);
    const isContactChannelUpdatable =
      !customer.contact_channel ||
      customer.contact_channel === ContactChannel.UNKNOWN;

    const orderHasTierPercentDiscount = order?.discounts?.some(
      (discount) =>
        discount.source === DiscountSource.LOYALTY_TIER &&
        discount.applyTo === DiscountApplyTo.CART,
    );
    const orderHasTierFreeDelivery = order?.discounts?.some(
      (discount) =>
        discount.source === DiscountSource.LOYALTY_TIER &&
        discount.applyTo === DiscountApplyTo.DELIVERY,
    );
    const isWebstoreOrder = order.source === OrderSource.WEBSTORE;

    const orderHasDiscount = order.discounts?.length > 0;
    if (orderHasDiscount && isWebstoreOrder) {
      await this.parseWebstoreDiscounts(order, customer, company);
    }

    await this.orderRedemptionService.updateCustomerLoyaltyUsage(
      customer,
      orderHasTierPercentDiscount,
      orderHasTierFreeDelivery,
    );

    customer = await this.customerRepository.findOneAndUpdate(
      { _id: customer._id },
      {
        orders,
        last_order_date: moment.utc().toDate(),
        last_order_id: order._id,
        number_of_orders: newNumberOfOrders,
        total_orders_amount: newTotalOrdersAmount,
        average_order_value: newTotalOrdersAmount / newNumberOfOrders,
        ...(isContactChannelUpdatable ? { contact_channel: order.source } : {}),
        latestContactChannel: order.source,
      },
    );

    await this.orderRedemptionService.applyCouponRedemptionPostFunctions(
      order,
      customer,
    );

    const orderHasFlexiblePointsRedemption = order.discounts?.some(
      (discount) => discount.source === DiscountSource.POINTS,
    );
    if (orderHasFlexiblePointsRedemption) {
      await this.orderRedemptionService.applyFlexiblePointsRedemptionPostFunctions(
        order,
        customer,
        company,
      );
    }

    const orderHasRewardDiscount = order.discounts?.some(
      (discount) => discount.source === DiscountSource.REWARD,
    );
    if (orderHasRewardDiscount) await this.redeemRewards(order, customer);

    const orderHasBenefitDiscount = order.benefits?.length;
    if (orderHasBenefitDiscount) await this.redeemBenefits(order, customer);

    if (isWebstoreOrder)
      this.trackingService.track(TrackingEvent.WEBSTORE_ORDER, {
        _id: customer._id.toString(),
        company: customer.company.toString(),
        phone: customer.phone,
        countryCode: customer.country_code,
        isLoyaltyMember: customer.loyaltyStatus === LoyaltyStatus.MEMBER,
        customerOrdableLink: this.customerWebstoreService.getOrdableLink(
          customer,
          order.brand._id,
        ),
        brandId: order.brand._id.toString(),
        brandName: order.brand.name,
        discounts: order.discounts?.length || 0,
      });

    return customer;
  }

  private redeemBenefits(
    order: OrderDocument,
    customer: CustomerDocument,
  ): Promise<void> {
    return forEachAsync(order.benefits, (benefit) =>
      this.benefitTypeFactoryService.postRedeem(benefit, customer),
    );
  }

  private getIsOrderCounted(
    customer: CustomerDocument,
    company: CompanyDocument,
  ) {
    return (
      customer.loyaltyStatus === LoyaltyStatus.MEMBER ||
      Boolean(company.loyaltyProgramConfig?.canNonMembersProgress)
    );
  }

  private getIsCartValueThresholdMet(
    order: OrderDocument,
    company: CompanyDocument,
  ) {
    const cartValueThreshold =
      company?.loyaltyProgramConfig?.orderValueThreshold ?? 0;

    const cartValue = order.total_amount_after_discount;

    return !cartValueThreshold || cartValue >= cartValueThreshold;
  }

  private async parseWebstoreDiscounts(
    order: OrderDocument,
    customer: CustomerDocument,
    company: Company,
  ) {
    // When a tier or coupon discount is redeemed on Ordable, no couponId is
    // attached to the order. So we have to figure out the discount type ourselves.
    if (order.source !== OrderSource.WEBSTORE) return;
    if (!order.discounts || order.discounts.length !== 1) return;
    if (order.couponId) return;

    const parsedDiscounts = await this.parseDiscount(order, customer);
    if (!parsedDiscounts || parsedDiscounts.length === 0) return;

    order.discounts = parsedDiscounts as any;

    const validatedDiscounts =
      await this.orderRedemptionService.validateLoyaltyRedemptions(
        company,
        order,
        customer,
      );
    order.discounts = validatedDiscounts;

    const couponDiscount = order.discounts?.find(
      (discount) => discount.source === DiscountSource.COUPON,
    );
    if (couponDiscount) {
      await this.updateLoyaltyProgressOnParsedCouponRedemption(
        order,
        couponDiscount,
      );
    } else {
      await order.updateOne({ discounts: validatedDiscounts });
    }
  }

  private async updateLoyaltyProgressOnParsedCouponRedemption(
    order: OrderDocument,
    couponDiscount: ValidDiscount,
  ) {
    order.loyaltyProgress = {
      ...order.loyaltyProgress,
      pointsRedeemed:
        (order.loyaltyProgress.pointsRedeemed ?? 0) +
        couponDiscount.loyaltyPointCost,
    };
    await order.updateOne({
      loyaltyProgress: order.loyaltyProgress,
      discounts: order.discounts,
    });
  }

  private async parseDiscount(
    order: OrderDocument,
    customer: CustomerDocument,
  ): Promise<Discount[]> {
    const discount = order.discounts[0];
    const discountAmount =
      discount.type === DiscountType.FLAT
        ? discount.amount
        : order.invoiced_amount * (discount.amount / 100);

    const canUseFreeDelivery =
      order.delivery_amount > 0 && customer.loyaltyTier?.freeDelivery;

    if (canUseFreeDelivery && discountAmount === order.delivery_amount) {
      return [Discount.fromTierFreeDelivery(customer.loyaltyTier)];
    }

    const tierDiscountAmount = customer.loyaltyTier?.percentDiscount
      ? order.invoiced_amount * (customer.loyaltyTier.percentDiscount / 100)
      : 0;

    if (tierDiscountAmount > 0 && tierDiscountAmount === discountAmount) {
      return [Discount.fromTierPercentDiscount(customer.loyaltyTier)];
    }

    const discountAmountWithFreeDelivery =
      discountAmount - order.delivery_amount;

    if (
      canUseFreeDelivery &&
      tierDiscountAmount > 0 &&
      tierDiscountAmount === discountAmountWithFreeDelivery
    ) {
      return [
        Discount.fromTierPercentDiscount(customer.loyaltyTier),
        Discount.fromTierFreeDelivery(customer.loyaltyTier),
      ];
    }

    const coupons =
      await this.couponService.findCustomerRedeemableCoupons(customer);
    const getCouponValue = (coupon: Coupon): number => {
      switch (coupon.benefits[0].type) {
        case BenefitType.FIXED_DISCOUNT:
          return coupon.benefits[0].value;
        case BenefitType.PERCENTAGE_DISCOUNT:
          return order.invoiced_amount * (coupon.benefits[0].value / 100);
        default:
          throw new UnreachableError(coupon.benefits[0].type);
      }
    };

    const couponUsedWithTierDiscount = coupons.find(
      (coupon) =>
        getCouponValue(coupon) === discountAmount - tierDiscountAmount,
    );

    if (tierDiscountAmount > 0 && couponUsedWithTierDiscount) {
      return [
        Discount.fromTierPercentDiscount(customer.loyaltyTier),
        Discount.fromCoupon(couponUsedWithTierDiscount),
      ];
    }

    const couponUsedWithFreeDelivery = coupons.find(
      (coupon) => getCouponValue(coupon) === discountAmountWithFreeDelivery,
    );

    if (canUseFreeDelivery && couponUsedWithFreeDelivery) {
      return [
        Discount.fromTierFreeDelivery(customer.loyaltyTier),
        Discount.fromCoupon(couponUsedWithFreeDelivery),
      ];
    }

    const couponUsedWithTierDiscountAndFreeDelivery = coupons.find(
      (coupon) =>
        getCouponValue(coupon) ===
        discountAmountWithFreeDelivery - tierDiscountAmount,
    );

    if (
      tierDiscountAmount > 0 &&
      canUseFreeDelivery &&
      couponUsedWithTierDiscountAndFreeDelivery
    ) {
      return [
        Discount.fromTierPercentDiscount(customer.loyaltyTier),
        Discount.fromTierFreeDelivery(customer.loyaltyTier),
        Discount.fromCoupon(couponUsedWithTierDiscountAndFreeDelivery),
      ];
    }

    const couponUsed = coupons.find(
      (coupon) => getCouponValue(coupon) === discountAmount,
    );

    if (couponUsed) return [Discount.fromCoupon(couponUsed)];

    this.loggerService.error('Could not figure out Ordable discount type', '', {
      orderId: order._id,
      discounts: order.discounts,
      customerId: customer._id,
      loyaltyTier: customer.loyaltyTier,
      discountAmount,
      tierDiscountAmount,
      coupons,
    });
  }

  private assignFirstBrandOrderedToCustomer(
    customer: CustomerDocument,
    brand: EmbeddedBrandDto,
  ) {
    customer.firstBrandOrderd = brand;
  }

  private assignFirstBranchOrderedToCustomer(
    customer: CustomerDocument,
    branch: BranchWithId,
  ) {
    customer.firstBranchOrderd = {
      _id: branch._id,
      name: branch.name,
    };
  }

  private isLoyaltyOrder(
    order: OrderDocument,
    company: CompanyDocument,
  ): boolean {
    return (
      loyaltyOrderSources.includes(order.source) &&
      this.getIsCartValueThresholdMet(order, company)
    );
  }

  async completeCustomerOrder(order: OrderDocument): Promise<void> {
    const customer = await this.customerReadService.findOne(
      order.customer.toHexString(),
      order.company,
    );

    customer.activeBrand = order.brand;

    if (!customer.firstBrandOrderd)
      this.assignFirstBrandOrderedToCustomer(customer, order.brand);

    if (!customer.firstBranchOrderd && typeof order.branch !== 'string')
      this.assignFirstBranchOrderedToCustomer(customer, order.branch);

    await customer.save();
    const company = await this.companyService.get_details(order.company);
    const isOrderCounted = this.getIsOrderCounted(customer, company);
    await customer.save();
    if (company.hasLoyaltyProgram && isOrderCounted) {
      await this.updateLoyaltyPointsOnOrderCompletion(order, customer, company);
      await this.customerTierService.checkForPromotion(
        customer,
        this.loyaltyTierLogService.onOrderCompletionUpgrade,
        order.brand?._id,
      );
      await this.customerTierService.checkForTierMaintained(customer);

      const isLoyaltyOrder = this.isLoyaltyOrder(order, company);
      await this.customerPunchCardService.incrementPunchCardCounters(customer, {
        ...(order.toJSON() as OrderToComplete),
        isLoyaltyOrder,
      });
      if (isLoyaltyOrder) {
        await this.customerLoyaltyMemberService.enrollLoyaltyCustomer(customer);
        this.eventEmitter.emit('customer.orders.updated', customer);
      }
    }
  }

  private async updateLoyaltyPointsOnOrderCompletion(
    order: OrderDocument,
    customer: CustomerDocument,
    company: CompanyDocument,
  ): Promise<void> {
    if (!company.loyaltyProgramConfig?.hasLoyaltyPoints) return;
    if (!loyaltyOrderSources.includes(order.source)) return;
    if (!this.getIsOrderCounted(customer, company)) return;

    if (!company.loyaltyProgramConfig?.earningExchangeRates)
      return this.loggerService.warn(
        "Company has loyalty program but doesn't have exchange rates",
        company,
      );

    if (!customer)
      return this.loggerService.warn(
        'Customer earned loyalty points, but could not be found',
        customer,
      );

    await this.customerLoyaltyMemberService.enrollLoyaltyCustomer(customer);

    const loyaltyPointsEarned =
      this.customerPointsService.calculatePointsEarnedOnOrder(
        order,
        customer,
        company,
      );
    const oldBalance = customer.loyaltyPoints;
    customer.loyaltyPoints = (oldBalance ?? 0) + loyaltyPointsEarned;

    await customer.save();
    await this.loyaltyPointLogService.create({
      action: LoyaltyPointLogAction.ON_ORDER_COMPLETED,
      oldBalance,
      cartValue: order.total_amount_after_discount,
      loyaltyPointsEarned: loyaltyPointsEarned,
      newBalance: customer.loyaltyPoints,
      currency: company.localization?.currency,
      customerId: customer._id,
      orderId: order._id,
      orderCode: order.code,
      orderSource: order.source,
      branchId:
        typeof order.branch !== 'string'
          ? order.branch?._id
          : new Types.ObjectId(order.branch),
      brandId: order.brand._id,
    });
    this.eventEmitter.emit('customer.points.updated', customer);
  }

  async redeemRewards(order: OrderDocument, customer: CustomerDocument) {
    const rewardDiscounts = order.discounts.filter(
      (discount) => discount.source === DiscountSource.REWARD,
    );

    const rewardIds = rewardDiscounts.map((discount) =>
      discount.rewardId.toString(),
    );
    const rewards = customer.rewards.filter((reward) =>
      rewardIds.includes(reward._id.toString()),
    );

    await this.customerRepository.redeemRewards(customer, rewards);
    this.eventEmitter.emit('customer.rewards.updated', customer);
  }
}
