import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { GenericRepository } from '@app/shared-stuff';
import { OTP, OTPDocument } from '../models/otp.model';
import { OTPRepositoryInterface } from './otp.repository.interface';

@Injectable()
export class OTPRepository
  extends GenericRepository<OTPDocument, OTP>
  implements OTPRepositoryInterface
{
  constructor(
    @InjectModel(OTP.name)
    private otpModel: Model<OTPDocument, OTP>,
  ) {
    super(otpModel);
  }

  async findOne(filter: Partial<OTP>): Promise<OTPDocument> {
    return this.otpModel.findOne({
      deletedAt: null,
      ...filter,
    });
  }
}
