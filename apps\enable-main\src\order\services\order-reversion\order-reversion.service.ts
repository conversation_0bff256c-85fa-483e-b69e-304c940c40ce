import {
  Achievement,
  BenefitMaximumUsageType,
  BenefitUsage,
  CompanyDocument,
  CustomerBenefitSource,
  CustomerDocument,
  CustomerEarnedBenefit,
  DiscountApplyTo,
  DiscountSource,
  EarnedReward,
  LoyaltyPointLogAction,
  LoyaltyProgress,
  NumberOfUsesType,
  OrderDocument,
  PunchCardProgress,
} from '@app/shared-stuff';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BenefitServiceInterface } from 'apps/enable-main/src/benefit/services/benefit-service.interface';
import { CalendarSystemService } from 'apps/enable-main/src/company/services/calendar-system/calendar-system.service';
import { CompanyService } from 'apps/enable-main/src/company/services/company/company.service';
import { CouponServiceInterface } from 'apps/enable-main/src/coupon/services/coupon.service.interface';
import { CustomerReadServiceInterface } from 'apps/enable-main/src/customer/modules/customer-read/customer-read.service.interface';
import { CustomerTierInfoServiceInterface } from 'apps/enable-main/src/customer/modules/customer-tier-info/customer-tier-info.service.interface';
import { CustomerTierServiceInterface } from 'apps/enable-main/src/customer/modules/customer-tier/customer-tier.service.interface';
import { LoyaltyTierLogServiceInterface } from 'apps/enable-main/src/loyalty-tier-log/services/loyalty-tier-log.service.interface';
import { LoyaltyTierReadServiceInterface } from 'apps/enable-main/src/loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.service.interface';
import { LoyaltyTransactionServiceInterface } from 'apps/enable-main/src/loyalty-transaction/services/loyalty-transaction-service.interface';
import { CompletedPunchCardService } from 'apps/enable-main/src/punch-card/modules/completed-punch-card/completed-punch-card.service';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { LoyaltyPointLogServiceInterface } from '../../../loyalty-point-log/services/loyalty-point-log.service.interface';
import { OrderNotificationService } from '../order-notification/order-notification.service';

@Injectable()
export class OrderReversionService {
  private readonly loggerService = new Logger(OrderReversionService.name);

  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly companyService: CompanyService,
    @Inject(CustomerReadServiceInterface)
    private readonly customerReadService: CustomerReadServiceInterface,
    @Inject(CustomerTierServiceInterface)
    private readonly customerTierService: CustomerTierServiceInterface,
    @Inject(LoyaltyPointLogServiceInterface)
    private readonly loyaltyPointLogService: LoyaltyPointLogServiceInterface,
    @Inject(LoyaltyTransactionServiceInterface)
    private readonly loyaltyTransactionService: LoyaltyTransactionServiceInterface,
    @Inject(LoyaltyTierReadServiceInterface)
    private readonly loyaltyTierReadService: LoyaltyTierReadServiceInterface,
    @Inject(CustomerTierInfoServiceInterface)
    private readonly customerLoyaltyTierInfoService: CustomerTierInfoServiceInterface,
    @Inject(LoyaltyTierLogServiceInterface)
    private readonly loyaltyTierLogService: LoyaltyTierLogServiceInterface,
    private readonly completedPunchCardService: CompletedPunchCardService,
    @Inject(BenefitServiceInterface)
    private readonly benefitService: BenefitServiceInterface,
    private readonly calendarSystemService: CalendarSystemService,
    private readonly orderNotificationService: OrderNotificationService,
    @Inject(CouponServiceInterface)
    private readonly couponService: CouponServiceInterface,
  ) {}

  async cancelCustomerOrder(order: OrderDocument): Promise<CustomerDocument> {
    const customer = await this.customerReadService.findById(order.customer);
    customer.number_of_orders -= 1;
    customer.total_orders_amount -= order.total_amount ?? 0;
    customer.average_order_value =
      customer.number_of_orders === 0
        ? 0
        : customer.total_orders_amount / customer.number_of_orders;

    const company = await this.companyService.findById(customer.company);

    if (company.hasLoyaltyProgram) {
      const orderWasCompleted =
        await this.loyaltyPointLogService.getWasOrderCompleted(order._id);
      await this.refundOrderRedemptions(order, customer);

      if (orderWasCompleted) await this.revertLoyaltyProgress(order, customer);
      await this.orderNotificationService.fireOnLoyaltyTransactionReversionTrigger(
        order,
      );
    }
    return await customer.save();
  }

  async deleteCustomerOrder(order: OrderDocument): Promise<void> {
    const customer = await this.customerReadService.findById(
      order.customer._id,
    );

    const company = await this.companyService.findById(customer.company);

    if (company.hasLoyaltyProgram) {
      const orderWasCompleted =
        await this.loyaltyPointLogService.getWasOrderCompleted(order._id);
      await this.refundOrderRedemptions(order, customer);
      if (orderWasCompleted) await this.revertLoyaltyProgress(order, customer);

      await this.orderNotificationService.fireOnLoyaltyTransactionReversionTrigger(
        order,
      );
      this.eventEmitter.emit('customer.points.updated', customer);
    }
  }

  async refundOrderRedemptions(
    order: OrderDocument,
    customer: CustomerDocument,
  ): Promise<void> {
    const pointsRefundResult = await this.refundPointsRedemption(
      order,
      customer,
    );

    const tierRefundResult =
      await this.refundTierNumberOfUsesAndBenefitsRedemption(order, customer);

    const rewardRefundResult = await this.refundRewardRedemption(
      order,
      customer,
    );

    order.loyaltyProgress = {
      ...order.loyaltyProgress,
      ...pointsRefundResult,
      ...tierRefundResult,
      ...rewardRefundResult,
    };
    order.markModified('loyaltyProgress');
    await order.save();
  }

  async refundRewardRedemption(
    order: OrderDocument,
    customer: CustomerDocument,
  ): Promise<LoyaltyProgress> {
    const rewardDiscount = (order.discounts || []).find(
      (discount) => discount.source === DiscountSource.REWARD,
    );

    if (!rewardDiscount) return { rewardsRefunded: 0 };

    if (customer.usedRewards && customer.usedRewards.length > 0) {
      const rewardIndex = customer.usedRewards.findIndex(
        (reward) =>
          reward._id.toString() === rewardDiscount.rewardId.toString(),
      );

      if (rewardIndex !== -1) {
        const [refundedReward] = customer.usedRewards.splice(rewardIndex, 1);
        customer.rewards.push(refundedReward);
        await customer.save();
        return { rewardsRefunded: 1 };
      }
    }

    return { rewardsRefunded: 0 };
  }

  async refundTierNumberOfUsesAndBenefitsRedemption(
    order: OrderDocument,
    customer: CustomerDocument,
  ): Promise<LoyaltyProgress> {
    if (
      !customer.loyaltyTier &&
      (!order.benefits || order.benefits.length === 0)
    ) {
      return {};
    }
    this.loggerService.log(
      'on refundTierNumberOfUsesAndBenefitsRedemption',
      customer.loyaltyTier,
    );

    const tierUsageRefunded = this.refundLoyaltyTierUses(order, customer);
    const { benefitsRefunded, benefitUsageRefunded } =
      await this.refundBenefitUsages(order, customer);

    return {
      benefitsRefunded,
      usageRefunded: tierUsageRefunded + benefitUsageRefunded,
    };
  }

  private refundLoyaltyTierUses(
    order: OrderDocument,
    customer: CustomerDocument,
  ): number {
    let totalUsagesRefunded = 0;

    const tierDiscount = (order.discounts || []).find(
      (discount) =>
        discount.source === DiscountSource.LOYALTY_TIER &&
        discount.applyTo === DiscountApplyTo.CART,
    );

    if (customer.loyaltyTier && tierDiscount) {
      totalUsagesRefunded += this.refundPercentDiscountUsage(customer);
      totalUsagesRefunded += this.refundFreeDeliveryUsage(customer);
    }

    customer.markModified('loyaltyTier');
    return totalUsagesRefunded;
  }

  private async refundBenefitUsages(
    order: OrderDocument,
    customer: CustomerDocument,
  ): Promise<{ benefitUsageRefunded: number; benefitsRefunded: number }> {
    let benefitUsageRefunded = 0;
    let benefitsRefunded = 0;
    if (order.benefits) {
      this.loggerService.log('order.benefits: ', order.benefits);

      for (const orderBenefit of order.benefits || []) {
        const benefitMatcher = this.benefitService.createBenefitUsageMatcher(
          orderBenefit,
          CustomerBenefitSource.TIER_PROGRAM,
        );
        const earnedBenefitIndex = customer.earnedBenefits.findIndex(
          (earnedBenefit) => benefitMatcher(earnedBenefit),
        );

        this.loggerService.log('orderBenefit: ', orderBenefit);

        if (earnedBenefitIndex !== -1) {
          benefitUsageRefunded += this.refundEarnedBenefitUsage(
            customer,
            earnedBenefitIndex,
          );
        } else {
          benefitsRefunded += await this.refundUsedBenefitUsage(
            customer,
            benefitMatcher,
          );
        }
      }
    }

    customer.markModified('earnedBenefits');
    customer.markModified('usedBenefits');
    await customer.save();

    return { benefitUsageRefunded, benefitsRefunded };
  }

  private refundPercentDiscountUsage(customer: CustomerDocument): number {
    if (
      customer.loyaltyTier.percentDiscountMaximumNumberOfUsesType ===
      NumberOfUsesType.LIMITED_USAGE
    ) {
      const maxUses =
        customer.loyaltyTier.percentDiscountMaximumNumberOfUses || 0;
      const remainingUses =
        customer.loyaltyTier.percentDiscountRemainingNumberOfUses || 0;

      const usagesToRefund = Math.min(1, maxUses - remainingUses);
      customer.loyaltyTier.percentDiscountRemainingNumberOfUses +=
        usagesToRefund;

      return usagesToRefund;
    }

    return 0;
  }

  private refundFreeDeliveryUsage(customer: CustomerDocument): number {
    if (
      customer.loyaltyTier.freeDeliveryMaximumNumberOfUsesType ===
      NumberOfUsesType.LIMITED_USAGE
    ) {
      const maxUses = customer.loyaltyTier.freeDeliveryMaximumNumberOfUses || 0;
      const remainingUses =
        customer.loyaltyTier.freeDeliveryRemainingNumberOfUses || 0;

      const usagesToRefund = Math.min(1, maxUses - remainingUses);
      customer.loyaltyTier.freeDeliveryRemainingNumberOfUses += usagesToRefund;

      return usagesToRefund;
    }

    return 0;
  }

  private refundEarnedBenefitUsage(
    customer: CustomerDocument,
    earnedBenefitIndex: number,
  ): number {
    const earnedBenefit = customer.earnedBenefits[earnedBenefitIndex];

    if (
      earnedBenefit.config.maximumUsageType !==
      BenefitMaximumUsageType.UNLIMITED
    ) {
      const currentUsage = earnedBenefit.numberOfUsages;

      if (currentUsage > 0) {
        earnedBenefit.numberOfUsages -= 1;
        return 1;
      } else {
        this.loggerService.log(
          `Skipping update: Maximum usage reached 0:`,
          earnedBenefit,
        );
      }
    }

    return 0;
  }

  private async refundUsedBenefitUsage(
    customer: CustomerDocument,
    benefitMatcher: (
      benefitUsage: BenefitUsage | CustomerEarnedBenefit,
    ) => boolean,
  ): Promise<number> {
    const usedBenefitIndex = customer.usedBenefits.findIndex((usedBenefit) =>
      benefitMatcher(usedBenefit),
    );

    if (usedBenefitIndex !== -1) {
      const usedBenefit = customer.usedBenefits[usedBenefitIndex];
      if (
        (usedBenefit.numberOfUsages > 0,
        usedBenefit?.config?.maximumUsageType !==
          BenefitMaximumUsageType.UNLIMITED)
      ) {
        usedBenefit.numberOfUsages -= 1;
        customer.usedBenefits.splice(usedBenefitIndex, 1);
        customer.earnedBenefits.push(usedBenefit);

        await this.loyaltyTransactionService.recordRefundedBenefits(customer, [
          usedBenefit,
        ]);

        return 1;
      }
    }

    return 0;
  }

  async revertLoyaltyProgress(
    order: OrderDocument,
    customer: CustomerDocument,
  ): Promise<void> {
    this.loggerService.log(
      'Order loyalty progress',
      JSON.stringify(order.loyaltyProgress),
    );

    const wasTierMaintained =
      await this.customerLoyaltyTierInfoService.hasCustomerMaintainedTier(
        customer,
      );

    await this.deductOrderPointsFromCustomer(order, customer);
    this.updateEmbeddedOrder(order, customer);
    const company = await this.companyService.findById(customer.company);
    await this.updateCustomerTier(order, customer, company);
    await this.removeStampsAndRewards(order, customer);

    const isTierMaintainedNow =
      await this.customerLoyaltyTierInfoService.hasCustomerMaintainedTier(
        customer,
      );

    if (wasTierMaintained && !isTierMaintainedNow) {
      order.loyaltyProgress.tierUnmaintained = true;
    }

    await customer.save();
    order.markModified('loyaltyProgress');
    await order.save();
  }

  private async deductOrderPointsFromCustomer(
    order: OrderDocument,
    customer: CustomerDocument,
  ): Promise<void> {
    const pointsDeducted = Math.min(
      order.loyaltyProgress.pointsEarned || 0,
      customer.loyaltyPoints || 0,
    );

    const oldBalance = customer.loyaltyPoints;
    customer.loyaltyPoints -= pointsDeducted;
    const newBalance = customer.loyaltyPoints;

    order.loyaltyProgress.pointsUnearned = pointsDeducted;

    const lostCoupons = await this.couponService.findByCost(
      customer.company,
      newBalance,
      oldBalance,
    );
    order.loyaltyProgress.couponsLost = lostCoupons.length;

    await this.logPointsDeduction(order, customer);
  }

  private async logPointsDeduction(
    order: OrderDocument,
    customer: CustomerDocument,
  ): Promise<void> {
    const oldBalance =
      customer.loyaltyPoints + order.loyaltyProgress.pointsUnearned;
    await this.loyaltyPointLogService.create({
      brandId: order?.brand?._id,
      customerId: customer?._id,
      orderId: order._id,
      action: LoyaltyPointLogAction.ON_ORDER_DELETED,
      oldBalance,
      loyaltyPointsEarned: -order.loyaltyProgress.pointsUnearned,
      newBalance: customer.loyaltyPoints,
      currency: order?.localization?.currency,
      orderSource: order.source,
      branchId:
        typeof order.branch !== 'string'
          ? order?.branch?._id
          : new Types.ObjectId(order?.branch),
    });
  }

  private updateEmbeddedOrder(
    order: OrderDocument,
    customer: CustomerDocument,
  ): void {
    if (customer.orders?.length > 0) {
      const embeddedOrderIndex = customer.orders.findIndex(
        (embeddedOrder) =>
          embeddedOrder._id.toString() === order._id.toString(),
      );

      if (embeddedOrderIndex !== -1) {
        customer.orders[embeddedOrderIndex] = {
          ...customer.orders[embeddedOrderIndex],
          status: order.status,
          deletedAt: moment().utc().toDate(),
          total_amount: order.total_amount,
          loyaltyProgress: {
            ...order.loyaltyProgress,
          },
        };
        customer.markModified(`orders.${embeddedOrderIndex}`);
      }
    }
  }

  private async updateCustomerTier(
    order: OrderDocument,
    customer: CustomerDocument,
    company: CompanyDocument,
  ): Promise<void> {
    if (customer.loyaltyTier?.isVipTier) return;

    const { orderRate, amountSpent, pointsRate } =
      await this.customerLoyaltyTierInfoService.getLoyaltyTierProgramProgress(
        customer,
        company,
      );

    const highestEligibleTier =
      await this.loyaltyTierReadService.findHighestEligibleTierAfter(
        company._id,
        await this.loyaltyTierLogService.findHighestAssignedTierId(customer, {
          startDate: this.calendarSystemService.getStartDate(company),
          endDate: this.calendarSystemService.getEndDate(customer, company),
        }),
        orderRate,
        amountSpent,
        pointsRate,
      );

    if (
      customer.loyaltyTier?._id.toString() !==
      highestEligibleTier?._id.toString()
    ) {
      const oldTierIndex = customer?.loyaltyTier?.tierIndex;

      await this.customerTierService.updateCustomerTier(
        customer,
        highestEligibleTier?._id,
        this.loyaltyTierLogService.onOrderDeletionDowngrade,
      );

      if (
        !highestEligibleTier ||
        highestEligibleTier.tierIndex < oldTierIndex
      ) {
        order.loyaltyProgress.tierDemoted = true;
      }
    } else if (
      customer.loyaltyTier &&
      customer.loyaltyTier?._id.toString() ===
        highestEligibleTier?._id.toString()
    ) {
      await this.customerTierService.checkForTierUnmaintained(
        customer,
        company,
      );
    }
  }

  private async removeStampsAndRewards(
    order: OrderDocument,
    customer: CustomerDocument,
  ): Promise<void> {
    if (!order?.loyaltyProgress?.earnedStamps || !customer?.punchCardProgress) {
      return;
    }

    let totalRemovedStamps = 0;
    let stampsRemovedFirstTrack = 0;
    let stampsRemovedSecondTrack = 0;
    const removedRewards: EarnedReward[] = [];

    for (const { punchCardId, count } of order.loyaltyProgress.earnedStamps) {
      const matchingTrack = customer.punchCardProgress.find(
        (progress) =>
          progress.punchCard._id.toString() === punchCardId.toString(),
      );

      if (!matchingTrack) continue;

      const removed = await this.processPunchCardStamps(
        count,
        matchingTrack,
        punchCardId,
        customer,
        removedRewards,
      );

      totalRemovedStamps += removed;

      if (matchingTrack.punchCard.track === 0) {
        stampsRemovedFirstTrack += removed;
      } else if (matchingTrack.punchCard.track === 1) {
        stampsRemovedSecondTrack += removed;
      }
    }

    Object.assign(order.loyaltyProgress, {
      stampsRemoved: totalRemovedStamps,
      rewardsRemoved: removedRewards.length,
      stampsRemovedFirstTrack,
      stampsRemovedSecondTrack,
    });

    customer.markModified('punchCardProgress');
  }

  private async processPunchCardStamps(
    count: number,
    matchingTrack: PunchCardProgress,
    punchCardId: Types.ObjectId,
    customer: CustomerDocument,
    removedRewards: EarnedReward[],
  ): Promise<number> {
    let totalRemovedStamps = 0;
    const oldCount = matchingTrack.count;
    const removedProgresses: PunchCardProgress[] = [];
    let activeProgress: PunchCardProgress;

    if (count < matchingTrack.count) {
      matchingTrack.count -= count;
      totalRemovedStamps += count;
      activeProgress = matchingTrack;
    } else {
      totalRemovedStamps += await this.deductStampsFromProgress(
        count,
        matchingTrack,
        punchCardId,
        customer,
        removedProgresses,
      );

      activeProgress = customer.punchCardProgress.find(
        (progress) =>
          progress.punchCard._id.toString() === punchCardId.toString(),
      );
      this.loggerService.log('activeProgress', JSON.stringify(activeProgress));
    }

    const unearnedAchievements = this.collectUnearnedAchievements(
      oldCount,
      matchingTrack,
      removedProgresses,
      activeProgress,
    );

    this.loggerService.log(
      'unearnedAchievements',
      JSON.stringify(unearnedAchievements),
    );
    this.removeRewards(
      customer,
      unearnedAchievements,
      removedRewards,
      matchingTrack,
      removedProgresses,
    );

    return totalRemovedStamps;
  }

  private async deductStampsFromProgress(
    count: number,
    matchingTrack: PunchCardProgress,
    punchCardId: Types.ObjectId,
    customer: CustomerDocument,
    removedProgresses: PunchCardProgress[],
  ): Promise<number> {
    let totalRemovedStamps = matchingTrack.count;
    matchingTrack.count = 0;
    customer.punchCardProgress = customer.punchCardProgress.filter(
      (progress) =>
        progress.punchCard._id.toString() !== punchCardId.toString(),
    );

    let remainingStampsToDeduct = count - totalRemovedStamps;

    while (remainingStampsToDeduct > 0) {
      const latestProgress =
        await this.completedPunchCardService.findAndRemoveLatestProgress(
          customer._id,
          punchCardId,
        );

      if (!latestProgress) {
        this.loggerService.error(
          `No latest progress found for punchCardId: ${punchCardId.toHexString()} and customerId: ${customer._id}`,
        );
        break;
      }

      const stampsToDeduct = Math.min(
        remainingStampsToDeduct,
        latestProgress.count,
      );
      latestProgress.count -= stampsToDeduct;
      remainingStampsToDeduct -= stampsToDeduct;

      removedProgresses.push({ ...latestProgress.toObject() });
      if (latestProgress.count > 0) {
        customer.punchCardProgress.push({
          ...latestProgress.toObject(),
          completedAt: undefined,
        });
      }

      totalRemovedStamps += stampsToDeduct;
    }

    return totalRemovedStamps;
  }

  private collectUnearnedAchievements(
    oldCount: number,
    matchingTrack: PunchCardProgress,
    removedProgresses: PunchCardProgress[],
    activeProgress: PunchCardProgress,
  ): Achievement[] {
    const unearnedAchievementsFromPreviouslyActiveProgress =
      matchingTrack.punchCard.achievements.filter(
        (achievement) => achievement.requirement.targetValue <= oldCount,
      );

    const unearnedAchievementsFromRemovedProgresses = removedProgresses.flatMap(
      (progress) => progress.punchCard.achievements,
    );

    const unearnedAchievementsFromActiveProgress =
      activeProgress?.punchCard.achievements.filter(
        (achievement) =>
          achievement.requirement.targetValue > activeProgress.count,
      ) || [];

    return [
      ...unearnedAchievementsFromPreviouslyActiveProgress,
      ...unearnedAchievementsFromRemovedProgresses,
      ...unearnedAchievementsFromActiveProgress,
    ];
  }

  private removeRewards(
    customer: CustomerDocument,
    unearnedAchievements: Achievement[],
    removedRewards: EarnedReward[],
    punchCardProgress?: PunchCardProgress,
    removedProgresses?: PunchCardProgress[],
  ): void {
    for (const achievement of unearnedAchievements) {
      if (punchCardProgress && customer.rewards) {
        const rewardToRemove = customer.rewards.find(
          (reward) =>
            reward.achievementId?.toString() === achievement._id.toString() &&
            reward.progressId.toString() === punchCardProgress._id.toString(),
        );

        if (rewardToRemove) {
          customer.rewards = customer.rewards.filter(
            (reward) => reward._id.toString() !== rewardToRemove._id.toString(),
          );
          removedRewards.push(rewardToRemove);
        }
      }

      if (removedProgresses?.length) {
        for (const progress of removedProgresses) {
          const rewardToRemove = customer.rewards.find(
            (reward) =>
              reward.achievementId?.toString() === achievement._id.toString() &&
              reward.progressId.toString() === progress._id.toString(),
          );

          if (rewardToRemove) {
            customer.rewards = customer.rewards.filter(
              (reward) =>
                reward._id.toString() !== rewardToRemove._id.toString(),
            );
            removedRewards.push(rewardToRemove);
          }
        }
      }
    }

    this.loggerService.log('removedRewards', JSON.stringify(removedRewards));
  }

  async refundPointsRedemption(
    order: OrderDocument,
    customer: CustomerDocument,
  ): Promise<Partial<LoyaltyProgress>> {
    if (!order.loyaltyProgress.pointsRedeemed) return {};

    const oldBalance = customer.loyaltyPoints ?? 0;
    const pointsRefunded = order.loyaltyProgress.pointsRedeemed;
    customer.loyaltyPoints = oldBalance + pointsRefunded;

    await this.loyaltyPointLogService.create({
      action: LoyaltyPointLogAction.ON_ORDER_CANCELLED,
      oldBalance,
      loyaltyPointsEarned: pointsRefunded,
      newBalance: customer.loyaltyPoints,
      currency: order.localization?.currency,
      customerId: customer._id,
      orderId: order._id,
      orderCode: order.code,
      orderSource: order.source,
      brandId: order.brand._id,
      branchId:
        typeof order.branch !== 'string'
          ? order.branch?._id
          : new Types.ObjectId(order.branch),
    });

    return { pointsRefunded };
  }
}
