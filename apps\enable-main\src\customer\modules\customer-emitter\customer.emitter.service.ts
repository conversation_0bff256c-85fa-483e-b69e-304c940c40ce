import {
  CustomerDocument,
  MicroserviceCommunicationService,
} from '@app/shared-stuff';
import {
  Inject,
  Injectable,
  OnModuleDestroy,
  OnModuleInit,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';

@Injectable()
export class CustomerEmitterService implements OnModuleInit, OnModuleDestroy {
  constructor(
    @Inject('enable-main-notification-producer')
    private client: ClientProxy,
    private microserviceCommunicationService: MicroserviceCommunicationService,
  ) {}

  onModuleInit() {
    this.microserviceCommunicationService.connect(this.client);
  }

  onModuleDestroy() {
    this.microserviceCommunicationService.disconnect(this.client);
  }

  public handleCustomerDeleted(customer: CustomerDocument) {
    this.microserviceCommunicationService.produceAndForget(
      this.client,
      'recipient.delete.request',
      { recipientId: customer._id.toString() },
    );
  }
}
