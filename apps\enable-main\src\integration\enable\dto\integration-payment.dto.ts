import { DeprecatedPaymentMethod } from '@app/shared-stuff';
import { CurrentUser } from '@app/shared-stuff/types/general/current-user';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IntegrationPaymentSource } from '../enums/integration-payment-source.enum';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class IntegrationPayment {
  @ApiProperty({
    type: String,
    required: true,
  })
  customer_name: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  customer_phone: string;

  @ApiProperty({
    required: true,
    type: String,
    description: 'Phone Number Country Code',
  })
  country_code: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  amount: number;

  @ApiProperty({
    type: String,
    // TODO: use Language ENUM after PaymentRefactor is merged
    enum: ['english', 'arabic'],
    default: 'english',
  })
  language: string;

  @ApiProperty({
    type: String,
    enum: DeprecatedPaymentMethod,
    required: true,
  })
  payment_method: DeprecatedPaymentMethod;

  @ApiProperty({
    type: Boolean,
    required: false,
  })
  @Type(() => Boolean)
  is_test: boolean;

  @ApiProperty({
    type: String,
    required: false,
  })
  callback_url: string;

  @ApiProperty({
    type: String,
    required: false,
    format: 'HH:mm',
    description: 'Order Delivery Time Range (To) => Example 15:36',
  })
  @IsOptional()
  timezone: string;

  @ApiProperty({
    type: String,
    enum: IntegrationPaymentSource,
    required: false,
  })
  source: IntegrationPaymentSource;

  @ApiProperty({
    type: Boolean,
    default: false,
    required: false,
  })
  @Type(() => Boolean)
  send_sms = false;

  @ApiProperty({
    type: String,
    required: false,
  })
  brandId = '';

  company: string;
  createdBy: CurrentUser;
}
