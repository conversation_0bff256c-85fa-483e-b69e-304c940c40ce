import {
  Inject,
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { Types } from 'mongoose';
import { PwaConfigRepository } from '../repositories/pwa-config.repository';
import { GoogleCloudStorageService } from '../../storage/google-cloud-storage.service';
import {
  BrandDocument,
  CreatedPwaConfigResponse,
  CreatePwaConfigDto,
  GeneratePresignedUrlDto,
  PublicPwaConfig,
  PwaConfigDocument,
  responseCode,
  SignedPwaAssetsResponse,
  UpdatePwaConfigDto,
} from '@app/shared-stuff';
import { CloudflarePWAService } from './cloudflare/cloudflare-pwa.service';
import { BrandServiceInterface } from '../../brand/services/brand/brand.service.interface';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class PwaConfigService {
  constructor(
    private readonly pwaConfigRepository: PwaConfigRepository,
    private readonly cloudflareService: CloudflarePWAService,
    private readonly googleCloudStorageService: GoogleCloudStorageService,
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandServiceInterface,
    private readonly configService: ConfigService,
  ) {}

  async findPublicByHost(host: string): Promise<PublicPwaConfig> {
    const config = await this.pwaConfigRepository.findOne({ host });
    if (!config)
      throw new NotFoundException(
        'PWA config not found',
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    return config;
  }

  async findAll(companyId?: Types.ObjectId): Promise<PwaConfigDocument[]> {
    if (!companyId) return this.pwaConfigRepository.findAll();

    const brands = await this.brandService.findByCompanyId(companyId);
    const brandIds = brands.map((brand: BrandDocument) => brand._id);

    if (!brandIds.length) return [];

    return this.pwaConfigRepository.findByBrandIdsIn(brandIds);
  }

  async findById(id: Types.ObjectId): Promise<PwaConfigDocument> {
    const config = await this.pwaConfigRepository.findById(id);
    if (!config)
      throw new NotFoundException(
        'PWA config not found',
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    return config;
  }

  async create(
    createPwaConfigDto: CreatePwaConfigDto,
  ): Promise<CreatedPwaConfigResponse> {
    const { host, brandId } = createPwaConfigDto;
    const configBaseUrl = this.configService.get<string>('CF_PWA_CONFIG_BASE');

    if (!host.endsWith(configBaseUrl)) {
      throw new UnprocessableEntityException(
        `Invalid host: must end with "${configBaseUrl}"`,
      );
    }

    const pwaConfig = await this.pwaConfigRepository.create(createPwaConfigDto);
    await this.cloudflareService.createPWA(host);

    const pwaBaseUrl = `https://${host}/pass`;
    await this.brandService.updatePwaBaseUrl(brandId, pwaBaseUrl);

    return { id: pwaConfig._id };
  }

  async update(
    id: Types.ObjectId,
    updatePwaConfigDto: UpdatePwaConfigDto,
  ): Promise<PwaConfigDocument> {
    const updated = await this.pwaConfigRepository.findOneAndUpdate(
      { _id: id },
      updatePwaConfigDto,
      {
        new: true,
      },
    );
    if (!updated)
      throw new NotFoundException(
        'PWA config not found',
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    return updated;
  }

  async delete(id: Types.ObjectId): Promise<void> {
    const pwaConfig = await this.findById(id);
    await this.pwaConfigRepository.remove(id);
    await this.cloudflareService.deletePWA(pwaConfig.host);
    await this.brandService.updatePwaBaseUrl(pwaConfig.brandId);
  }

  async generateSignedUploadUrl({
    brandId,
    host,
    images,
  }: GeneratePresignedUrlDto): Promise<SignedPwaAssetsResponse> {
    const presignedUrls = await Promise.all(
      images.map(async ({ name, contentType }) => {
        const filePath = `pwa/${brandId.toHexString()}/${host}/${name}`;
        const url = await this.googleCloudStorageService.getSignedUploadUrl(
          filePath,
          contentType,
        );
        return { name, url };
      }),
    );

    return { presignedUrls };
  }
}
