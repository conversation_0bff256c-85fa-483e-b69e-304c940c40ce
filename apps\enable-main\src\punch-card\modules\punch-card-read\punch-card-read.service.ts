import {
  EmbeddedPunchCardDto,
  IndexPunchCardsDto,
  PunchCardDocument,
} from '@app/shared-stuff';
import { Injectable, NotFoundException } from '@nestjs/common';
import { Types } from 'mongoose';
import { PunchCardRepository } from '../punch-card-repository/punch-card.repository';

@Injectable()
export class PunchCardReadService {
  constructor(private readonly punchCardRepository: PunchCardRepository) {}

  public async findById(
    punchCardId: Types.ObjectId,
  ): Promise<PunchCardDocument> {
    const punchCard = await this.punchCardRepository.findById(punchCardId);
    if (!punchCard)
      throw new NotFoundException(
        `Punch card with ID ${punchCardId} not found.`,
      );
    return punchCard;
  }

  public async findManyById(
    punchCardIds: Types.ObjectId[],
  ): Promise<PunchCardDocument[]> {
    return await this.punchCardRepository.findManyById(punchCardIds);
  }

  public index({
    companyId,
    counterType,
  }: IndexPunchCardsDto): Promise<PunchCardDocument[]> {
    return this.punchCardRepository.findAll({
      companyId,
      ...(counterType && { 'counter.type': counterType }),
    });
  }

  public async getDetails(
    punchCardId: Types.ObjectId,
  ): Promise<PunchCardDocument> {
    return await this.punchCardRepository.findById(punchCardId);
  }

  public getCompletionThreshold({
    achievements,
    hasStampOnRedeem,
  }: EmbeddedPunchCardDto): number {
    if (!achievements || !achievements.length) return 1;
    const highestAchievementTarget = Math.max(
      ...achievements.map(
        (achievement) => achievement?.requirement?.targetValue,
      ),
    );
    return highestAchievementTarget + (hasStampOnRedeem ? 1 : 0);
  }
}
