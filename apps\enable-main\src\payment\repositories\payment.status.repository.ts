import { Injectable } from '@nestjs/common';
import { GenericRepository, PaymentStatusEnum } from '@app/shared-stuff';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  PaymentStatus,
  PaymentStatusDocument,
} from '../models/payment.status.model';
import { PaymentStatusRepositoryInterface } from './interfaces/payment.status.repository.interface';

@Injectable()
export class PaymentStatusRepository
  extends GenericRepository<PaymentStatusDocument, PaymentStatus>
  implements PaymentStatusRepositoryInterface
{
  constructor(
    @InjectModel(PaymentStatus.name)
    private paymentStatusModel: Model<PaymentStatusDocument, PaymentStatus>,
  ) {
    super(paymentStatusModel);
  }

  async save(
    new_status: PaymentStatusEnum,
    old_status: PaymentStatusEnum,
    paymentId: Types.ObjectId,
  ): Promise<void> {
    const newPaymentStatus = new this.paymentStatusModel({
      new_status,
      old_status,
      payment: paymentId,
    });

    await newPaymentStatus.save();
  }
}
