import { DataIndex, ObjectIdTransform } from '@app/shared-stuff';
import { ApiProperty } from '@nestjs/swagger';
import { Types } from 'mongoose';

export class CityToCreate {
  @ApiProperty({
    type: String,
    required: true,
  })
  name: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  country: string;
}

export class CityToUpdate {
  @ApiProperty({
    type: String,
    required: true,
  })
  _id: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  name: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  country: string;
}

export class CityToIndex extends DataIndex {
  @ApiProperty({
    type: String,
    required: false,
  })
  @ObjectIdTransform({ optional: true })
  country?: Types.ObjectId;
}
