import { Store } from '../../../../../store/models/store.model';
import { CreateOrdableOptionsDto } from '../../dtos/options/create-ordable-options.dto';
import { UpdateOrdableOptionsDto } from '../../dtos/options/update-ordable-options.dto';

export interface OrdableOptionsServiceInterface {
  create(
    createOrdableOptionsDto: CreateOrdableOptionsDto,
    store: Store,
  ): Promise<any>;

  update(
    updateOrdableOptionsDto: UpdateOrdableOptionsDto,
    store: Store,
  ): Promise<any>;

  findAll(store: Store): Promise<any>;
}
