import {
  CollectionName,
  CompletedPunchCard,
  CompletedPunchCardDocument,
  CustomerDocument,
  GenericRepository,
  PunchCardProgress,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';

@Injectable()
export class CompletedPunchCardRepository extends GenericRepository<
  CompletedPunchCardDocument,
  CompletedPunchCard
> {
  constructor(
    @InjectModel(CollectionName.COMPLETED_PUNCH_CARD)
    private completedPunchCardModel: Model<
      CompletedPunchCardDocument,
      CompletedPunchCard
    >,
  ) {
    super(completedPunchCardModel);
  }

  public async save(
    customer: CustomerDocument,
    progress: PunchCardProgress,
  ): Promise<CompletedPunchCardDocument> {
    const completedProgress = this.completeProgress(customer, progress);
    return await this.completedPunchCardModel.create(completedProgress);
  }

  public async bulkSave(
    customer: CustomerDocument,
    progresses: PunchCardProgress[],
  ): Promise<void> {
    await this.completedPunchCardModel.create(
      progresses.map((progress) => this.completeProgress(customer, progress)),
    );
  }

  private completeProgress(
    customer: CustomerDocument,
    progress: PunchCardProgress,
  ): CompletedPunchCard {
    return {
      ...progress,
      companyId: customer.company._id,
      customerId: customer._id,
      completedAt: moment.utc().toDate(),
    };
  }

  public async findLatestProgress(
    customerId: Types.ObjectId,
    punchCardId: Types.ObjectId,
  ): Promise<CompletedPunchCardDocument> {
    return await this.completedPunchCardModel
      .findOne({
        'punchCard._id': punchCardId,
        customerId,
      })
      .sort({ completedAt: -1 });
  }

  public async findAndRemoveLatestProgress(
    customerId: Types.ObjectId,
    punchCardId: Types.ObjectId,
  ): Promise<CompletedPunchCardDocument> {
    return await this.completedPunchCardModel
      .findOneAndDelete(
        {
          'punchCard._id': punchCardId,
          customerId,
        },
        { lean: false, includeResultMetadata: false },
      )
      .sort({ completedAt: -1 });
  }
}
