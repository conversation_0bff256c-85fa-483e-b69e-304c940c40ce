import {
  CreateOrUpdateTemplateCondition,
  CreateTemplateDto,
  EbChatTrigger,
  GenericExceptionFilter,
  IndexTemplateDto,
  ListConditionsDto,
  ListRolesDto,
  MicroserviceCommunicationService,
  SyncTemplateWhatsappDto,
  TransformInterceptor,
  UpdateTemplateDto,
} from '@app/shared-stuff';
import { SingleIdDto } from '@app/shared-stuff/types/general/single-id.dto';
import {
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  OnModuleDestroy,
  OnModuleInit,
  Param,
  Post,
  Put,
  Query,
  Req,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { Types } from 'mongoose';

import { TemplateService } from '../../services/template/template.service';

@Controller('template')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags('Template')
@SetMetadata('module', 'template')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class TemplateController implements OnModuleInit, OnModuleDestroy {
  constructor(
    private templateService: TemplateService,
    @Inject('enable-main-notification-producer') private client: ClientProxy,
    private microserviceCommunicationService: MicroserviceCommunicationService,
  ) {}

  async onModuleDestroy() {
    await this.microserviceCommunicationService.disconnect(this.client);
  }

  async onModuleInit() {
    await this.microserviceCommunicationService.connect(this.client);
  }

  @Post()
  @SetMetadata('action', 'create-template')
  public async create(
    @Body() createTemplateDto: CreateTemplateDto,
    @Req() req: Request,
  ) {
    createTemplateDto.owner._id = createTemplateDto.owner._id
      ? createTemplateDto.owner._id
      : req['company_id'];
    return this.microserviceCommunicationService.produceAndWaitForResponse<
      CreateTemplateDto,
      any
    >(this.client, 'create.template.request', createTemplateDto);
  }

  @Put()
  @SetMetadata('action', 'update-template')
  public async update(
    @Body() updateTemplateDto: UpdateTemplateDto,
    @Req() req: Request,
  ) {
    updateTemplateDto.owner._id = updateTemplateDto.owner._id
      ? updateTemplateDto.owner._id
      : req['company_id'];
    return this.microserviceCommunicationService.produceAndWaitForResponse<
      UpdateTemplateDto,
      any
    >(this.client, 'update.template.request', updateTemplateDto);
  }

  @Get()
  @SetMetadata('action', 'index-template')
  public async index(
    @Query() indexTemplateDto: IndexTemplateDto,
    @Req() req: Request,
  ) {
    indexTemplateDto.ownerId = indexTemplateDto.ownerId
      ? indexTemplateDto.ownerId
      : req['company_id'];
    return this.microserviceCommunicationService.produceAndWaitForResponse<
      IndexTemplateDto,
      any
    >(this.client, 'index.template.request', indexTemplateDto);
  }

  @Get('ebchat/trigger')
  @SetMetadata('action', 'get-all-ebchat-triggers')
  public getEbChatTriggers() {
    return Object.values(EbChatTrigger);
  }

  @Get(':id')
  @SetMetadata('action', 'findById-template')
  public async findById(@Param('id') id: string) {
    return this.microserviceCommunicationService.produceAndWaitForResponse<
      SingleIdDto,
      any
    >(this.client, 'findById.template.request', { id: new Types.ObjectId(id) });
  }

  @Get('list/roles')
  @SetMetadata('action', 'list-all-roles')
  public async listRoles(@Query() listRolesDto: ListRolesDto) {
    return await this.templateService.listRoles(listRolesDto);
  }

  @Delete(':id')
  @SetMetadata('action', 'delete')
  public async delete(@Param('id') id: string, @Req() req: Request) {
    return this.microserviceCommunicationService.produceAndWaitForResponse<
      SingleIdDto,
      any
    >(this.client, 'delete.template.request', {
      id: new Types.ObjectId(id),
      currentUser: req['current'],
    });
  }

  @Post('sync/whatsapp')
  @SetMetadata('action', 'syncWhatsApp')
  public async syncWhatsApp(
    @Body() syncWhatsAppTemplate: SyncTemplateWhatsappDto,
  ) {
    await this.templateService.assignBrandOwner(syncWhatsAppTemplate);
    return this.microserviceCommunicationService.produceAndWaitForResponse<
      SyncTemplateWhatsappDto,
      any
    >(this.client, 'sync.template.whatsapp', syncWhatsAppTemplate);
  }

  @Get('list/condition')
  @SetMetadata('action', 'list-condition')
  public listConditions(@Query() listConditionsDto: ListConditionsDto) {
    return this.templateService.listConditions(listConditionsDto);
  }

  @Post('condition')
  @SetMetadata('action', 'create-or-update-condition')
  public async createOrUpdateCondition(
    @Body() createOrUpdateCondition: CreateOrUpdateTemplateCondition,
  ) {
    return this.microserviceCommunicationService.produceAndWaitForResponse<
      CreateOrUpdateTemplateCondition,
      string
    >(
      this.client,
      'createOrUpdate.template.condition',
      createOrUpdateCondition,
    );
  }
}
