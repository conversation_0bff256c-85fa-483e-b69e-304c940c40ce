import { LogError, TierWithRequirements } from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Types } from 'mongoose';
import { LoyaltyTierWriteServiceInterface } from '../modules/loyalty-tier-write/loyalty-tier-write.service.interface';

@Injectable()
export class LoyaltyTierListener {
  constructor(
    @Inject(LoyaltyTierWriteServiceInterface)
    private loyaltyTierWriteService: LoyaltyTierWriteServiceInterface,
  ) {}

  @LogError()
  @OnEvent('company.loyaltyProgramConfig.tierLevellingUpMethod.updated')
  async handleTierLevellingUpMethodUpdated(
    companyId: string,
    tiers: TierWithRequirements[],
  ) {
    await this.loyaltyTierWriteService.updateTierLevellingUpMethod(
      new Types.ObjectId(companyId),
      tiers,
    );
  }
}
