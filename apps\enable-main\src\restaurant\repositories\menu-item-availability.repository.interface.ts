import {
  IGenericRepository,
  MenuItemAvailability,
  MenuItemAvailabilityDocument,
  MenuItemAvailabilityType,
} from '@app/shared-stuff';

export interface MenuItemAvailabilityRepositoryInterface
  extends IGenericRepository<
    MenuItemAvailabilityDocument,
    MenuItemAvailability
  > {
  findOneWithTiming(
    dayOfWeek: string,
    startTime: string,
    endTime: string,
    isWeekly: boolean,
    type: MenuItemAvailabilityType,
  ): Promise<MenuItemAvailabilityDocument>;
}
