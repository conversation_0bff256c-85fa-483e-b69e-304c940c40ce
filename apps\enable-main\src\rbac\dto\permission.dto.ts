import { DataIndex } from '@app/shared-stuff';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class PermissionToCreate {
  @IsNotEmpty()
  @ApiProperty()
  name: string;

  @IsNotEmpty()
  @ApiProperty()
  module: string;

  @IsNotEmpty()
  @ApiProperty()
  action: string;

  @IsNotEmpty()
  @ApiProperty()
  route: string;
}

export class PermissionToUpdate {
  @IsNotEmpty()
  @ApiProperty()
  _id: string;

  @IsNotEmpty()
  @ApiProperty()
  name: string;

  @IsNotEmpty()
  @ApiProperty()
  module: string;

  @IsNotEmpty()
  @ApiProperty()
  action: string;

  @IsNotEmpty()
  @ApiProperty()
  route: string;
}

export class PermissionToIndx extends DataIndex {
  @ApiProperty({
    type: Boolean,
    required: false,
  })
  groupped: boolean;

  user_data: any;
}
