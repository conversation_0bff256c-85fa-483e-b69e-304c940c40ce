import {
  CollectionName,
  CompanyDocument,
  CountriesNameToIsoCodeMapping,
  LanguageToLanguageCode,
  LoggerService,
  MyFatoorahBalanceDataModel,
  MyFatoorahConfiguration,
  MyFatoorahData,
  MyFatoorahEventType,
  MyFatoorahGatewayConfiguration,
  MyFatoorahGetPaymentStatusResponseDto,
  MyFatoorahRecurringDataModel,
  MyFatoorahRefundDataModel,
  MyFatoorahResponseDto,
  MyFatoorahSendInvoiceResponse,
  MyFatoorahSupplierDataModel,
  MyFatoorahTransactionDataModel,
  MyFatoorahTransactionStatus,
  MyFatoorahWebhookDto,
  OrderDocument,
  PaymentDocument,
  PaymentGatewayType,
  PaymentMethod,
  PaymentStatusEnum,
} from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import {
  BadRequestException,
  Inject,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import * as crypto from 'crypto';
import { Model } from 'mongoose';
import { PaymentIntegrationType } from '../../../../../../../libs/shared-stuff/src/enums/payment/payment-integration-type.enum';
import { CompanyService } from '../../../../company/services/company/company.service';
import { ENABLE_TECH_URL } from '../../../constants/payment-callback.const';
import { PaymentLogAction } from '../../../enums/payment-log-action.enum';
import { PaymentLogRepositoryInterface } from '../../../repositories/interfaces/payment.log.repository.interface';
import { PaymentRepositoryInterface } from '../../../repositories/interfaces/payment.repository.interface';
import { PaymentConfigurationServiceInterface } from '../../payment-configuration/payment-configuration.service.interface';
import { PaymentHelpersService } from '../../payment-helpers/payment-helpers.service';

@Injectable()
export class PaymentMyFatoorahService {
  private readonly logger = new LoggerService(PaymentMyFatoorahService.name);

  MY_FATOORAH_API_KEY: string = undefined;
  MY_FATOORAH_BASE_URL: string = undefined;
  COUNTRY_ISO_CODE_MAPPING = {
    KWT: 'KWD',
    ARE: 'AED',
    EGY: 'EGP',
    QAT: 'QAR',
  };

  private readonly webhookHandlers = new Map<
    MyFatoorahEventType,
    (data: any) => Promise<void>
  >([
    [
      MyFatoorahEventType.TRANSACTION_STATUS_CHANGED,
      this.processTransactionStatusChanged.bind(this),
    ],
    [
      MyFatoorahEventType.REFUND_STATUS_CHANGED,
      this.processRefundStatusChanged.bind(this),
    ],
    [
      MyFatoorahEventType.BALANCE_TRANSFERRED,
      this.processBalanceTransferred.bind(this),
    ],
    [
      MyFatoorahEventType.SUPPLIER_STATUS_CHANGED,
      this.processSupplierStatusChanged.bind(this),
    ],
    [
      MyFatoorahEventType.RECURRING_STATUS_CHANGED,
      this.processRecurringStatusChanged.bind(this),
    ],
  ]);

  constructor(
    private readonly configService: ConfigService,
    private readonly http: HttpService,
    private paymentHelperService: PaymentHelpersService,
    private companyService: CompanyService,
    @InjectModel(CollectionName.ORDER)
    private paymentOrderModel: Model<OrderDocument>,
    @Inject('PaymentRepositoryInterface')
    private paymentRepository: PaymentRepositoryInterface,
    @Inject('PaymentLogRepositoryInterface')
    private paymentLogRepository: PaymentLogRepositoryInterface,
    @Inject(PaymentConfigurationServiceInterface)
    private readonly paymentConfigurationService: PaymentConfigurationServiceInterface,
  ) {}

  public async initiateSession(
    payment: PaymentDocument,
    paymentConfig: MyFatoorahGatewayConfiguration,
  ) {
    const response = await this.performMyFatoorahRequest<MyFatoorahData>(
      {
        CustomerIdentifier: payment._id.toHexString
          ? payment._id.toHexString()
          : payment._id,
      },
      'v2/InitiateSession',
      paymentConfig.configuration,
      payment.localization.countryAcronym,
    );

    await this.paymentLogRepository.create({
      sentObject: { payment },
      receivedObject: response,
      logAction: PaymentLogAction.MyFatoorahPaymentCreated,
      paymentCode: payment.code,
      paymentId: payment._id,
    });

    return response;
  }

  public async sendPaymentLink(
    payment: PaymentDocument,
    paymentConfig: MyFatoorahGatewayConfiguration,
  ): Promise<string> {
    if (payment.gateway_link) return payment.gateway_link;

    if (
      paymentConfig.configuration.integrationType !=
      PaymentIntegrationType.REDIRECT
    )
      throw new BadRequestException('Invalid MyFatoorah Integration Type');

    const sendPaymentDto = this.constructSendPaymentLinkDto(payment);
    const response =
      await this.performMyFatoorahRequest<MyFatoorahSendInvoiceResponse>(
        sendPaymentDto,
        'v2/SendPayment',
        paymentConfig.configuration,
        payment.localization.countryAcronym,
      );

    await this.paymentLogRepository.create({
      sentObject: { sendPaymentDto },
      receivedObject: response,
      logAction: PaymentLogAction.MyFatoorahSendInvoiceLink,
      paymentCode: payment.code,
      paymentId: payment._id,
    });

    await this.paymentRepository.findOneAndUpdate(
      { _id: payment._id },
      { gateway_link: response?.InvoiceURL },
    );

    return response?.InvoiceURL;
  }

  public async redirectCustomer(paymentCode: string, others: any) {
    const SECURE_PAYMENT_ROUTE = this.configService.get<string>(
      'SECURE_PAYMENT_ROUTE',
    );
    const { gatewayType, payment } =
      await this.paymentHelperService.getPaymentAndConfiguration(paymentCode);

    if (!payment || gatewayType !== PaymentGatewayType.MY_FATOORAH) {
      return ENABLE_TECH_URL;
    }

    const callbackURL =
      SECURE_PAYMENT_ROUTE +
      `?code=${payment.code}&&lang=${LanguageToLanguageCode[payment.language]}`;

    await this.paymentLogRepository.create({
      sentObject: { callbackURL },
      receivedObject: { paymentCode, ...others },
      logAction: PaymentLogAction.MY_FATOORAH_REDIRECTED,
      paymentCode: payment.code,
      paymentId: payment._id,
    });

    return callbackURL;
  }

  public async executePayment(sessionId: string) {
    const payment = await this.paymentRepository.findOne({
      'myFatoorahData.SessionId': sessionId,
    });
    if (!payment) {
      throw new BadRequestException('Please Include Correct Payment Code');
    }

    const paymentConfig =
      await this.paymentConfigurationService.findPaymentConfig(
        payment.branch?._id,
        payment.brand?._id,
        payment.company as any,
      );

    const executePaymentDto = this.constructExecutePaymentDTO(payment);

    const response = await this.performMyFatoorahRequest<MyFatoorahData>(
      executePaymentDto,
      'v2/ExecutePayment',
      paymentConfig.configuration[PaymentMethod.CREDIT_CARDS]
        .configuration as MyFatoorahConfiguration,
      payment.localization.countryAcronym,
    );

    await this.paymentLogRepository.create({
      sentObject: { executePaymentDto },
      receivedObject: response,
      logAction: PaymentLogAction.MyFatoorahPaymentExecute,
      paymentCode: payment.code,
      paymentId: payment._id,
    });

    return response['PaymentURL'];
  }

  private constructSendPaymentLinkDto(payment: PaymentDocument) {
    return {
      NotificationOption: 'LNK',
      ...this.constructSharedDtoProperties(payment),
    };
  }

  private constructExecutePaymentDTO(payment: PaymentDocument) {
    return {
      SessionId: payment.myFatoorahData.SessionId,
      DisplayCurrencyIso:
        this.COUNTRY_ISO_CODE_MAPPING[payment.myFatoorahData.CountryCode],
      ...this.constructSharedDtoProperties(payment),
    };
  }

  private constructSharedDtoProperties(payment: PaymentDocument) {
    return {
      InvoiceValue: payment.amount,
      CustomerName: payment.customer_name,
      MobileCountryCode: payment.country_code ?? '+974',
      CustomerMobile: payment.customer_phone,
      CustomerEmail: `${payment.customer_phone}@enable.tech`,
      CallBackUrl: `${this.configService.get(
        'HOST_URL',
      )}/payment/fatoorah/callback/${payment.code}`,
      ErrorUrl: `${this.configService.get(
        'HOST_URL',
      )}/payment/fatoorah/callback/${payment.code}`,
      Language: payment.language == 'arabic' ? 'ar' : 'en',
      CustomerReference: payment.code,
    };
  }

  private async getMyFatoorahPaymentStatus(
    payment: PaymentDocument,
    config: MyFatoorahConfiguration,
  ) {
    return this.performMyFatoorahRequest<MyFatoorahGetPaymentStatusResponseDto>(
      {
        key: payment.code,
        keyType: 'CustomerReference',
      },
      'v2/GetPaymentStatus',
      config,
      payment.localization.countryAcronym,
    );
  }

  private async performMyFatoorahRequest<ResponseType>(
    dataToSend: any,
    subUrlDirection: string,
    myFatoorahConfig: MyFatoorahConfiguration,
    countryAcronym: CountriesNameToIsoCodeMapping,
  ): Promise<ResponseType> {
    const apiKey =
      myFatoorahConfig && myFatoorahConfig.apiKey
        ? myFatoorahConfig.apiKey
        : this.getApiKey();
    const baseURL = this.getBaseUrl(countryAcronym);
    return new Promise((resolve, reject) => {
      this.http
        .post<MyFatoorahResponseDto<ResponseType>>(
          baseURL + subUrlDirection,
          dataToSend,
          {
            headers: { Authorization: `Bearer ${apiKey}` },
          },
        )
        .subscribe({
          next: (response) => {
            if (response.data.IsSuccess) resolve(response.data['Data']);
            else {
              this.logger.error(
                `[MyFatoorah] ${response.data.Message}`,
                '',
                response.data,
              );
              reject(response.data);
            }
          },
          error: (err) => {
            this.logger.error(`[MyFatoorah] ${err.message}`, err.stack, err);
            reject(
              err.response && err.response.data ? err.response.data : err.code,
            );
          },
        });
    });
  }

  private getApiKey() {
    if (!this.MY_FATOORAH_API_KEY)
      this.MY_FATOORAH_API_KEY = this.configService.get('MY_FATOORAH_API_KEY');

    return this.MY_FATOORAH_API_KEY;
  }

  private getBaseUrl(countryAcronym: CountriesNameToIsoCodeMapping) {
    if (countryAcronym === CountriesNameToIsoCodeMapping.Qatar) {
      return this.configService.get('MY_FATOORAH_QATAR_BASE_URL');
    }

    return this.configService.get('MY_FATOORAH_BASE_URL');
  }

  async afterPaymentDone(
    myFatoorahWebhookDto: MyFatoorahWebhookDto,
    signature: string,
  ) {
    this.logger.log(
      '[My Fatoorah] received myfatoorah webhook',
      myFatoorahWebhookDto,
    );

    const myFatoorahWebhookDtoString = JSON.stringify(myFatoorahWebhookDto);

    const { gatewayConfig } =
      await this.paymentHelperService.getPaymentAndConfiguration(
        (myFatoorahWebhookDto.Data as MyFatoorahTransactionDataModel)
          ?.CustomerReference,
      );

    const webhookSecretKey = (
      gatewayConfig.configuration as MyFatoorahConfiguration
    ).webhookSecretKey;

    if (
      !this.validateSignature(
        myFatoorahWebhookDtoString,
        signature,
        webhookSecretKey,
      )
    ) {
      this.logger.warn(
        '[My Fatoorah] Invalid signature received for MyFatoorah webhook',
      );
      throw new UnauthorizedException(
        'Invalid signature received for MyFatoorah webhook',
      );
    }

    const handler = this.webhookHandlers.get(myFatoorahWebhookDto.EventType);
    if (!handler) {
      this.logger.warn(
        '[My Fatoorah] Unknown EventType received:',
        myFatoorahWebhookDto.EventType,
      );
      return;
    }

    try {
      await handler(myFatoorahWebhookDto.Data);
    } catch (error) {
      this.logger.error(
        `[My Fatoorah] Error handling MyFatoorah webhook: ${error.message}`,
      );
    }
  }

  private async processTransactionStatusChanged(
    myFatoorahTransactionDataModel: MyFatoorahTransactionDataModel,
  ): Promise<void> {
    this.logger.log(
      ' [My Fatoorah] MyFatoorah Transaction Data Model: ',
      myFatoorahTransactionDataModel,
    );

    const { gatewayType, payment } =
      await this.paymentHelperService.getPaymentAndConfiguration(
        myFatoorahTransactionDataModel.CustomerReference,
      );

    if (gatewayType !== PaymentGatewayType.MY_FATOORAH) return;

    try {
      const company = await this.companyService.get_details(
        payment.company['_id'],
      );

      const order = payment.order_code
        ? await this.paymentOrderModel.findOne({ code: payment.order_code })
        : undefined;

      const { newPaymentStatus, responseCode } = this.determinePaymentStatus(
        myFatoorahTransactionDataModel.TransactionStatus,
      );

      this.logger.log('[My Fatoorah] New Payment Status', newPaymentStatus);

      await this.onTransactionStatusChangedSuccessfully(
        payment,
        newPaymentStatus,
        myFatoorahTransactionDataModel,
        order,
        company,
        responseCode,
      );
    } catch (exception) {
      await this.onTransactionStatusChangedFailure(exception, payment);
    }
  }

  private async onTransactionStatusChangedSuccessfully(
    payment: PaymentDocument,
    newPaymentStatus: PaymentStatusEnum,
    transactionData: MyFatoorahTransactionDataModel,
    order: OrderDocument,
    company: CompanyDocument,
    responseCode: string,
  ) {
    await this.paymentHelperService.saveTransaction(
      payment,
      transactionData.PaymentId,
      transactionData,
      PaymentGatewayType.MY_FATOORAH,
    );

    await this.paymentHelperService.handlePaymentStatusLogic(
      payment,
      newPaymentStatus,
      order,
      company,
    );

    this.logger.log(
      '[My Fatoorah] before generating the callback url',
      newPaymentStatus,
    );
    const callBackURL = this.paymentHelperService.generateCallBackUrl(
      payment,
      company,
      responseCode,
      order,
    );

    this.logger.log(
      '[My Fatoorah] after generating the callback url',
      callBackURL,
    );

    await this.paymentLogRepository.create({
      sentObject: { callBackURL },
      receivedObject: transactionData,
      logAction: PaymentLogAction.MY_FATOORAH_WEBHOOK_FIRED,
      paymentCode: payment.code,
      paymentId: payment._id,
    });

    this.paymentHelperService.firePusherEvent(
      callBackURL,
      payment,
      newPaymentStatus,
    );

    const updatedIntegrationInfo = {
      ...(payment.integrationInfo ?? {}),
      callbackUrl: callBackURL,
      myFatoorahData: transactionData,
    };

    await this.paymentRepository.findOneAndUpdate(
      { _id: payment._id },
      { $set: { integrationInfo: updatedIntegrationInfo } },
    );
  }

  private async onTransactionStatusChangedFailure(
    exception: any,
    payment: PaymentDocument,
  ) {
    this.logger.error('[My Fatoorah] MyFatoorah payment exception', exception);

    await this.paymentRepository.findOneAndUpdate(
      { _id: payment._id },
      { $set: { status: PaymentStatusEnum.INTERNAL_FAILED } },
    );

    await this.paymentLogRepository.create({
      sentObject: { exception },
      receivedObject: {},
      logAction: PaymentLogAction.MY_FATOORAH_WEBHOOK_FAILED,
      paymentCode: payment.code,
      paymentId: payment._id,
    });
  }

  private determinePaymentStatus(
    transactionStatus: MyFatoorahTransactionStatus,
  ) {
    const statusMap = new Map<
      string,
      { status: PaymentStatusEnum; code?: string }
    >([
      [
        MyFatoorahTransactionStatus.FAILED,
        { status: PaymentStatusEnum.UNSUCCESSFUL, code: '152' },
      ],
      [
        MyFatoorahTransactionStatus.SUCCESS,
        { status: PaymentStatusEnum.TRANSACTION_COMPLETED, code: '000' },
      ],
    ]);

    const { status: newPaymentStatus, code: responseCode } =
      statusMap.get(transactionStatus);

    return { newPaymentStatus, responseCode };
  }

  private validateSignature(
    bodyString: string,
    signature: string,
    webhookSecretKey: string,
  ): boolean {
    const json = JSON.parse(bodyString);

    if (json['Event'] === 'RefundStatusChanged') {
      delete json['Data']['GatewayReference'];
    }

    const unOrderedArray = json['Data'];
    const orderedArray = Object.keys(unOrderedArray).sort((a, b) =>
      a.localeCompare(b),
    );

    const orderedString = orderedArray
      .map((key) => `${key}=${unOrderedArray[key] || ''}`)
      .join(',');

    const hash = crypto
      .createHmac('sha256', webhookSecretKey)
      .update(orderedString)
      .digest('base64');

    return hash === signature;
  }

  private async processRefundStatusChanged(
    myFatoorahRefundDataModel: MyFatoorahRefundDataModel,
  ): Promise<void> {
    // TODO : whenever requested from the business/product team
    // Implement handling for Refund Status Changed
    this.logger.error(
      'unhandled method for my fatoorah payment refund status changes',
    );
  }

  private async processBalanceTransferred(
    myFatoorahBalanceDataModel: MyFatoorahBalanceDataModel,
  ): Promise<void> {
    // TODO : whenever requested from the business/product team
    // Implement handling for Balance Transferred
    this.logger.error(
      'unhandled method for my fatoorah payment balance transfer',
    );
  }

  private async processSupplierStatusChanged(
    myFatoorahSupplierDataModel: MyFatoorahSupplierDataModel,
  ): Promise<void> {
    // TODO : whenever requested from the business/product team
    // Implement handling for Supplier Status Changed
    this.logger.error(
      'unhandled method for my fatoorah payment supplier status changes',
    );
  }

  private async processRecurringStatusChanged(
    myFatoorahRecurringDataModel: MyFatoorahRecurringDataModel,
  ): Promise<void> {
    // TODO : whenever requested from the business/product team
    // Implement handling for Recurring Status Changed
    this.logger.error(
      'unhandled method for my fatoorah payment recurring status changes',
    );
  }
}
