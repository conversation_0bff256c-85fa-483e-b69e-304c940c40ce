import {
  GenericRepository,
  MenuItemAvailability,
  MenuItemAvailabilityDocument,
  MenuItemAvailabilityType,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { MenuItemAvailabilityRepositoryInterface } from './menu-item-availability.repository.interface';

@Injectable()
export class MenuItemAvailabilityRepository
  extends GenericRepository<MenuItemAvailabilityDocument, MenuItemAvailability>
  implements MenuItemAvailabilityRepositoryInterface
{
  constructor(
    @InjectModel(MenuItemAvailability.name)
    private menuItemAvailabilityModel: Model<
      MenuItemAvailabilityDocument,
      MenuItemAvailability
    >,
  ) {
    super(menuItemAvailabilityModel);
  }

  async findOneWithTiming(
    dayOfWeek: string,
    startTime: string,
    endTime: string,
    isWeekly: boolean,
    type: MenuItemAvailabilityType,
  ): Promise<MenuItemAvailabilityDocument> {
    return await this.menuItemAvailabilityModel.findOne({
      dayOfWeek: dayOfWeek,
      startTime: startTime,
      endTime: endTime,
      isWeekly: isWeekly,
      type: type,
    });
  }
}
