import {
  CollectionName,
  DataIndex,
  LogError,
  pick,
  PusherService,
} from '@app/shared-stuff';
import { CompanyDocument } from '@app/shared-stuff/models/company.model';
import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model, Types } from 'mongoose';
import * as xlsx from 'xlsx';
import { RoleToCreate, RoleToUpdate } from '../../../rbac/dto/role.dto';
import { RoleDocument } from '../../../rbac/models/role.model';
import { PermissionDocument } from '../../models/permission.model';
import { RoleExcelToImport, RoleToIndex } from './../../dto/role.dto';

@Injectable()
export class RoleService {
  constructor(
    @InjectModel(CollectionName.ROLES) private roleModel: Model<RoleDocument>,
    private pusherService: PusherService,
  ) {}

  async index(dataIndex: DataIndex, company: string) {
    const filter: FilterQuery<RoleDocument> = { cannotBeViewed: { $ne: true } };

    if (company) filter.company = new Types.ObjectId(company);

    let query = this.roleModel.find(filter);

    if (dataIndex.limit)
      query = query
        .skip((dataIndex.offset ?? 0) * dataIndex.limit)
        .limit(dataIndex.limit);

    return query.exec();
  }

  async admin_index(roleToIndex: RoleToIndex) {
    let roles = this.roleModel.find();
    if (roleToIndex.limit && roleToIndex.limit) {
      roles = roles
        .skip(roleToIndex.offset * roleToIndex.limit)
        .limit(roleToIndex.limit);
    }
    if (roleToIndex.month) {
      roles = roles.where('month', roleToIndex.month);
    }
    if (roleToIndex.company) {
      roles = roles.where('company', new Types.ObjectId(roleToIndex.company));
    }
    if (roleToIndex.search_key) {
      if (roleToIndex.search_type == 'name') {
        roles = roles.where('name', {
          $regex: `.*${roleToIndex.search_key.toLowerCase()}.*`,
          $options: 'i',
        });
      } else if (roleToIndex.search_type == 'date_created') {
        roles = roles.where('createdAt', {
          $regex: `.*${roleToIndex.search_key.toLowerCase()}.*`,
        });
      } else {
        roles = roles.find({
          $or: [
            { name: { $regex: `.*${roleToIndex.search_key.toLowerCase()}.*` } },
            {
              createdAt: {
                $regex: `.*${roleToIndex.search_key.toLowerCase()}.*`,
              },
            },
          ],
        });
      }
    }
    if (roleToIndex.name) {
      const names = roleToIndex.name.split(',');
      roles = roles.find({ name: { $in: names } });
    }

    if (roleToIndex.isDefault) {
      roles = roles.where('company', { $exists: false });
    }

    if (roleToIndex.sort_type) {
      const sortMapping = {
        name_a_z: { name: 1 },
        name_z_a: { name: -1 },
        date_created: { createdAt: -1 },
        date_created_a_z: { createdAt: 1 },
        date_created_z_a: { createdAt: -1 },
      };
      if (sortMapping[roleToIndex.sort_type]) {
        roles = roles.sort(sortMapping[roleToIndex.sort_type]);
      }
    }

    return roles.exec();
  }

  async get_total_roles(roleToIndex: RoleToIndex) {
    delete roleToIndex.offset;
    delete roleToIndex.limit;
    return await (
      await this.admin_index(roleToIndex)
    ).length;
  }

  async create(roleToCreate: RoleToCreate) {
    const createdRole = new this.roleModel(roleToCreate);

    createdRole.createdBy = roleToCreate.currentUser;
    await createdRole.save();
    return createdRole;
  }

  async update(roleToUpdate: RoleToUpdate) {
    const selectedRole = await this.roleModel.findByIdAndUpdate(
      roleToUpdate._id,
      roleToUpdate,
      { new: true },
    );

    if (selectedRole && selectedRole.name !== 'Super Admin') {
      await this.pusherService.fireEvent(
        selectedRole._id.toString(),
        'RoleUpdated',
        {
          _id: selectedRole._id.toString(),
          name: selectedRole.name,
          company: selectedRole.company,
        },
      );
    }

    return selectedRole;
  }

  async get_details(id) {
    const selectedRole = await this.roleModel
      .findById(id)
      .populate('permissions');
    return selectedRole;
  }

  async remove(id: string) {
    const deletedRole = await this.roleModel.findByIdAndRemove(id);
    return deletedRole;
  }

  async importExcel(roleExcelToImport: RoleExcelToImport) {
    const workbook = xlsx.readFile(roleExcelToImport.file_path);
    const sheet_name_list = workbook.SheetNames;
    const rolesData = xlsx.utils.sheet_to_json(
      workbook.Sheets[sheet_name_list[0]],
    );
    for (let i = 0; i < rolesData.length; i++) {
      const currentRole = rolesData[i] as any;
      const permissions = [];
      const permissions_arr = currentRole.permissions.split(',');
      for (let j = 0; j < permissions_arr.length; j++) {
        permissions.push(new Types.ObjectId(permissions_arr[j]));
      }
      const roleToCreate: RoleToCreate = {
        name: currentRole.name,
        permissions: permissions as any,
        description: currentRole.description,
        company: roleExcelToImport.company,
        company_name: ' ',
        privileges: [],
        currentUser: {},
      };
      await this.create(roleToCreate);
    }
    return rolesData;
  }

  async addToSuperAdmin(permission: PermissionDocument) {
    await this.roleModel.updateMany(
      { name: 'Super Admin' },
      { $push: { permissions: permission._id } },
    );
  }

  async getDefaultCustomerRole() {
    return await this.roleModel
      .findOne({
        name: 'Customer',
        company: null,
      })
      .populate('permissions');
  }

  @OnEvent('company.created')
  @LogError()
  async duplicateRolesForCompany(company: CompanyDocument) {
    const roles = await this.roleModel.find({
      name: {
        $in: [
          'Super Admin',
          'Branch Manager',
          'Dispatcher',
          'Orders Manager',
          'Company Admin',
          'Supervisor',
          'Dispatcher',
          'CallCenter Agent',
        ],
      },
      company: null,
    });
    roles.map(async (role) => {
      const roleToCreate: RoleToCreate = {
        name: role.name,
        permissions: role.permissions,
        privileges: role.privileges,
        company: company._id,
        description: role.description,
        company_name: company.name,
        currentUser: company.createdBy,
      };
      await this.create(roleToCreate);
    });
  }
}
