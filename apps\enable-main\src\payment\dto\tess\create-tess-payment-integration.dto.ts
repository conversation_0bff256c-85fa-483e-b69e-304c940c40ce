import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUrl,
  <PERSON><PERSON>ength,
  <PERSON><PERSON><PERSON><PERSON>,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { TESSPaymentMethod } from '../../enums/tess/tess-payment-methods.enum';
import { UrlTargetTESSPaymentEnum } from '../../enums/tess/url-target-tess-payment.enum';
import { BillingAddressTESSPaymentIntegration } from '../../types/tess/billing-address-tess-payment-integration.type';
import { CustomerTESSPaymentIntegration } from '../../types/tess/customer-tess-payment-integration.type';
import { OrderTESSPaymentIntegration } from '../../types/tess/order-tess-payment-integration.type';

export class CreateTESSPaymentIntegrationDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Key for Merchant identification',
  })
  @IsNotEmpty()
  @IsString()
  merchant_key: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Defines a payment transaction',
  })
  @IsNotEmpty()
  @IsString()
  operation: string;

  @ApiProperty({
    type: [String],
    required: false,
  })
  methods?: TESSPaymentMethod[];

  @ApiProperty({
    type: String,
    required: true,
    description:
      'URL to redirect the Customer in case of the successful payment',
    maxLength: 1024,
  })
  @MaxLength(1024)
  @IsNotEmpty()
  @IsUrl()
  success_url: string;

  @ApiProperty({
    type: String,
    required: false,
    maxLength: 1024,
    minLength: 0,
    description: 'URL to return Customer in case of a payment cancellation',
  })
  @MinLength(0)
  @MaxLength(1024)
  @IsUrl()
  cancel_url?: string;

  @ApiProperty({
    type: String,
    required: true,
    enum: UrlTargetTESSPaymentEnum,
    description:
      'Name of, or keyword for a browsing context where Customer should be returned according to HTML specification.',
  })
  @IsEnum(UrlTargetTESSPaymentEnum)
  url_target?: UrlTargetTESSPaymentEnum;

  @ApiProperty({
    type: Boolean,
    required: false,
    default: false,
    description:
      'Special attribute pointing for further tokenization If the card_token is specified, req_token will be ignored',
  })
  @IsBoolean()
  req_token? = false;

  @ApiProperty({
    type: [String],
    required: false,
    description: 'Credit card token value',
  })
  card_token?: string[];

  @ApiProperty({
    type: Boolean,
    required: false,
    default: false,
    description:
      'Special attribute pointing for further tokenization If the card_token is specified, req_token will be ignored',
  })
  @IsBoolean()
  recurring_init? = false;

  @ApiProperty({
    type: String,
    required: false,
    description:
      'Schedule ID for recurring payments , It s available when recurring_init = true ',
  })
  @ValidateIf(
    (createTESSPaymentIntegrationDto: CreateTESSPaymentIntegrationDto) =>
      createTESSPaymentIntegrationDto.recurring_init === true,
  )
  @IsString()
  schedule_id?: string;

  @ApiProperty({
    type: String,
    required: true,
    description:
      '	Special signature to validate your request to Payment Platform Addition in Signature section.',
  })
  @IsNotEmpty()
  @IsString()
  hash: string;

  @ApiProperty({
    type: () => OrderTESSPaymentIntegration,
    required: true,
    description: 'Information about an order',
  })
  @IsNotEmpty()
  @ValidateNested()
  order: OrderTESSPaymentIntegration;

  @ApiProperty({
    type: () => CustomerTESSPaymentIntegration,
    required: true,
    description: "Customer's information",
  })
  @IsNotEmpty()
  @ValidateNested()
  customer: CustomerTESSPaymentIntegration;

  @ApiProperty({
    type: () => BillingAddressTESSPaymentIntegration,
    required: false,
    description: 'Billing address information.',
  })
  @IsOptional()
  @ValidateNested()
  billing_address?: BillingAddressTESSPaymentIntegration;

  parameters?: { [key: string]: any };
}
