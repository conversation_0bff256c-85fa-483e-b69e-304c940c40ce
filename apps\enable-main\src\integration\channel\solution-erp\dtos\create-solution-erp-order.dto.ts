import { ApiProperty } from '@nestjs/swagger';
import { SolutionERPOrderType } from '../enums/solution-erp-order-type.enum';
import { SolutionERPCustomer } from '../types/solution-erp-customer.type';
import { SolutionERPItem } from '../types/solution-erp-item.type';
import { IsOptional } from 'class-validator';

export class CreateSolutionErpOrderDto {
  @ApiProperty({
    type: SolutionERPCustomer,
    required: false,
  })
  customer: SolutionERPCustomer;

  @ApiProperty({
    type: Date,
    required: false,
  })
  orderDate: Date;

  @ApiProperty({
    type: String,
    required: false,
  })
  deliveryName: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  deliveryAddress: string;

  @ApiProperty({
    type: Date,
    required: true,
  })
  deliveryDate: Date;

  @ApiProperty({
    type: String,
    required: true,
  })
  deliveryTime: string;

  @ApiProperty({
    type: Number,
    required: false,
  })
  priority: number;

  @ApiProperty({
    type: String,
    required: false,
  })
  comments: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  discount: string;

  @ApiProperty({
    type: [SolutionERPItem],
    required: false,
  })
  items?: SolutionERPItem[];

  @ApiProperty({
    type: Number,
    required: false,
  })
  shippingCharge: number;

  @ApiProperty({
    type: Number,
    required: false,
  })
  orderTotal: number;

  @ApiProperty({
    type: SolutionERPOrderType,
    required: false,
    enumName: 'SolutionERPOrderType',
  })
  orderType: SolutionERPOrderType;

  @ApiProperty({
    type: String,
    required: false,
  })
  paymentMethod: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  contact?: string;
}
