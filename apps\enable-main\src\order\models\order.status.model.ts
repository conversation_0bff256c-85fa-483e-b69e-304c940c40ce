import { CollectionName } from '@app/shared-stuff';
import { OrderStatusEnum } from '@app/shared-stuff/enums/order/order-new-status.enum';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

export type OrderStatusDocument = HydratedDocument<OrderStatus>;
@Schema({ timestamps: true })
export class OrderStatus {
  @Prop({
    enum: Object.keys(OrderStatusEnum),
    type: String,
    required: true,
  })
  old_status: OrderStatusEnum;

  @Prop({
    enum: Object.keys(OrderStatusEnum),
    type: String,
    required: true,
  })
  new_status: OrderStatusEnum;

  @Prop({
    type: String,
    required: false,
  })
  comment: string;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: CollectionName.BRANCH,
  })
  branch: string;

  @Prop({
    type: Types.ObjectId,
    ref: CollectionName.ORDER,
    required: true,
  })
  order: string;

  @Prop({
    type: {},
    required: false,
  })
  updatedBy: {};

  @Prop({
    type: {},
    required: false,
  })
  createdBy: {};
  @Prop({
    type: {},
    required: false,
  })
  assignedTo: {};
  @Prop({
    type: {},
    required: false,
  })
  deletedBy: {};

  @Prop({
    type: Date,
    required: false,
  })
  deletedAt: Date;
}

export const OrderStatusSchema = SchemaFactory.createForClass(OrderStatus);
