import {
  CollectionName,
  GenericRepository,
  LoggerService,
  PassTypeIdentifierConfig,
  PassTypeIdentifierConfigDocument,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@Injectable()
export class PassTypeIdentifierConfigRepository extends GenericRepository<
  PassTypeIdentifierConfigDocument,
  PassTypeIdentifierConfig
> {
  private readonly logger = new LoggerService(
    PassTypeIdentifierConfigRepository.name,
  );
  constructor(
    @InjectModel(CollectionName.PASS_TYPE_IDENTIFIER_CONFIG)
    private readonly passTypeIdentifierConfigModel: Model<
      PassTypeIdentifierConfigDocument,
      PassTypeIdentifierConfig
    >,
    private readonly configService: ConfigService,
  ) {
    super(passTypeIdentifierConfigModel);
  }

  public async findByPassTypeIdentifier(
    passTypeIdentifier?: string,
  ): Promise<PassTypeIdentifierConfigDocument> {
    if (!passTypeIdentifier) {
      passTypeIdentifier = this.configService.get(
        'DEFAULT_PASS_TYPE_IDENTIFIER',
      );
    }
    const passConfig = await this.passTypeIdentifierConfigModel.findOne({
      passTypeIdentifier,
    });
    if (!passConfig)
      this.logger.error(`No config found for ${passTypeIdentifier}`);
    return passConfig;
  }
}
