import { CollectionName } from '@app/shared-stuff';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { HydratedDocument, Types } from 'mongoose';

export type TableDocument = HydratedDocument<Table>;
@Schema({ timestamps: true })
export class Table {
  @Prop({
    type: String,
    required: true,
  })
  name: string;

  @Prop({
    type: String,
    required: false,
  })
  internalId: string;

  @Prop({
    type: Number,
    required: false,
  })
  seats: number;

  @Prop({
    type: String,
    required: false,
  })
  deliverectId: string;

  @Prop({
    type: String,
    required: false,
  })
  companyName: string;

  @Prop({
    type: Boolean,
    required: false,
  })
  hasActiveOrder: boolean;

  @Prop({
    type: String,
    required: false,
  })
  branchName: string;

  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: CollectionName.BRANCH,
  })
  branch: Types.ObjectId;

  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: CollectionName.COMPANY,
  })
  company: Types.ObjectId;

  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: 'Floor',
  })
  floor: Types.ObjectId;

  @Prop({
    type: {},
    required: false,
  })
  updatedBy: {};

  @Prop({
    type: {},
    required: false,
  })
  createdBy: {};

  @Prop({
    type: {},
    required: false,
  })
  assignedTo: {};

  @Prop({
    type: {},
    required: false,
  })
  deletedBy: {};

  @Prop({
    type: Date,
    required: false,
  })
  deletedAt: Date;

  @Prop({
    type: String,
    default: moment().startOf('month').format('MMMM'),
  })
  month: string;

  @Prop({
    type: Number,
    default: moment().week(),
  })
  week: number;

  @Prop({
    type: Number,
    default: moment().date(),
  })
  day: number;

  @Prop({
    type: Number,
    default: moment().year(),
  })
  year: number;
}

const TableSchema = SchemaFactory.createForClass(Table);

export { TableSchema };
