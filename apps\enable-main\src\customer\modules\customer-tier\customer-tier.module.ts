import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { BenefitModule } from '../../../benefit/benefit.module';
import { CompanyModule } from '../../../company/company.module';
import { LoyaltyTierLogModule } from '../../../loyalty-tier-log/loyalty-tier-log.module';
import { LoyaltyTierReadModule } from '../../../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.module';
import { CustomerIndexModule } from '../customer-index/customer-index.module';
import { CustomerNotificationModule } from '../customer-notification/customer-notification.module';
import { CustomerReadModule } from '../customer-read/customer-read.module';
import { CustomerReplacementsModule } from '../customer-replacements/customer-replacements.module';
import { CustomerRepositoryModule } from '../customer-repository/customer-repository.module';
import { CustomerTierInfoModule } from '../customer-tier-info/customer-tier-info.module';
import { CustomerTierService } from './customer-tier.service';
import { CustomerTierServiceInterface } from './customer-tier.service.interface';
import { LoyaltyTransactionModule } from 'apps/enable-main/src/loyalty-transaction/loyalty-transaction.module';

@Module({
  providers: [
    { provide: CustomerTierServiceInterface, useClass: CustomerTierService },
  ],
  imports: [
    EventEmitterModule,
    LoyaltyTierReadModule,
    LoyaltyTierLogModule,
    LoyaltyTransactionModule,
    CompanyModule,
    CustomerRepositoryModule,
    CustomerTierInfoModule,
    CustomerNotificationModule,
    CustomerReplacementsModule,
    CustomerIndexModule,
    CustomerReadModule,
    BenefitModule,
  ],
  exports: [CustomerTierServiceInterface],
})
export class CustomerTierModule {}
