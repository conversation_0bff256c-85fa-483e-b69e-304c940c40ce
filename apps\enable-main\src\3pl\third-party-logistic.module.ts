import {
  CollectionName,
  DeliveryOrderSchema,
  DistributionCenterSchema,
  SharedStuffModule,
  ShipmentSchema,
  TempCustomerSchema,
} from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BrandModule } from '../brand/brand.module';
import { CompanyModule } from '../company/company.module';
import { CustomerReadModule } from '../customer/modules/customer-read/customer-read.module';
import { CustomerReplacementsModule } from '../customer/modules/customer-replacements/customer-replacements.module';
import { CustomerWriteModule } from '../customer/modules/customer-write/customer-write.module';
import { DeliveryModule } from '../delivery/delivery.module';
import { LocationModule } from '../location/location.module';
import { NotificationModule } from '../notification/notification.module';
import { SharedModule } from '../shared/shared.module';
import { DeliveryOrderController } from './controllers/delivery-order.controller';
import { DistributionCenterController } from './controllers/distribution-center.controllers';
import { ShipmentController } from './controllers/shipment.controller';
import { TempCustomerController } from './controllers/temp-customer.controller';
import { DeliveryOrderRepositoryInterface } from './repositories/delivery-order/delivery-order-repository.interface';
import { DeliveryOrderRepository } from './repositories/delivery-order/delivery-order.repository';
import { DistributionCenterRepositoryInterface } from './repositories/distribution-center/distribution-center-repository.interface';
import { DistributionCenterRepository } from './repositories/distribution-center/distribution-center.repository';
import { ShipmentRepositoryInterface } from './repositories/shipment/shipment-repository.interface';
import { ShipmentRepository } from './repositories/shipment/shipment.repository';
import { TempCustomerRepositoryInterface } from './repositories/temp-customer/temp-customer-repository.interface';
import { TempCustomerRepository } from './repositories/temp-customer/temp-customer.repository';
import { DeliveryOrderServiceInterface } from './services/delivery-order/delivery-order-service.interface';
import { DeliveryOrderTookanService } from './services/delivery-order/delivery-order-tookan.service';
import { DeliveryOrderListener } from './services/delivery-order/delivery-order.listener';
import { DeliveryOrderService } from './services/delivery-order/delivery-order.service';
import { DistributionCenterServiceInterface } from './services/distribution-center/distribution-center-service.interface';
import { DistributionCenterService } from './services/distribution-center/distribution-center.service';
import { Shared3plService } from './services/shared-3pl.service';
import { ShipmentServiceInterface } from './services/shipment/shipment-service.interface';
import { ShipmentService } from './services/shipment/shipment.service';
import { WayBillService } from './services/shipment/way-bill.service';
import { TempCustomerServiceInterface } from './services/temp-customer/temp-customer-service.interface';
import { TempCustomerService } from './services/temp-customer/temp-customer.service';
import { ShipmentNotificationService } from './services/shipment/shipment-notification.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: CollectionName.DISTRIBUTION_CENTER,
        schema: DistributionCenterSchema,
      },
      { name: CollectionName.SHIPMENT, schema: ShipmentSchema },
      { name: CollectionName.TEMP_CUSTOMER, schema: TempCustomerSchema },
      { name: CollectionName.DELIVERY_ORDER, schema: DeliveryOrderSchema },
    ]),
    SharedModule,
    SharedStuffModule,
    LocationModule,
    CompanyModule,
    BrandModule,
    DeliveryModule,
    NotificationModule,
    CustomerWriteModule,
    CustomerReadModule,
    CustomerReplacementsModule,
  ],
  controllers: [
    DistributionCenterController,
    TempCustomerController,
    ShipmentController,
    DeliveryOrderController,
  ],
  providers: [
    {
      provide: DistributionCenterRepositoryInterface,
      useClass: DistributionCenterRepository,
    },
    {
      provide: DistributionCenterServiceInterface,
      useClass: DistributionCenterService,
    },
    {
      provide: ShipmentRepositoryInterface,
      useClass: ShipmentRepository,
    },
    {
      provide: ShipmentServiceInterface,
      useClass: ShipmentService,
    },
    {
      provide: TempCustomerRepositoryInterface,
      useClass: TempCustomerRepository,
    },
    {
      provide: TempCustomerServiceInterface,
      useClass: TempCustomerService,
    },
    {
      provide: DeliveryOrderServiceInterface,
      useClass: DeliveryOrderService,
    },
    {
      provide: DeliveryOrderRepositoryInterface,
      useClass: DeliveryOrderRepository,
    },
    Shared3plService,
    DeliveryOrderTookanService,
    DeliveryOrderListener,
    ShipmentNotificationService,
    WayBillService,
  ],

  exports: [ShipmentServiceInterface, TempCustomerServiceInterface],
})
export class ThirdPartyLogisticModule {}
