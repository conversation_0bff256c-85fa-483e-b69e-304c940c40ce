import {
  Action,
  CreateActionVariantDto,
  IndexActionVariantDto,
  TemplateDocument,
  UpdateActionVariantDto,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface ActionVariantServiceInterface {
  create(
    createMessageVariantDto: CreateActionVariantDto,
  ): Promise<TemplateDocument>;
  update(
    updateMessageVariantDto: UpdateActionVariantDto,
  ): Promise<TemplateDocument>;
  delete(actionVariantId: Types.ObjectId): Promise<number>;

  index(indexVariantDto: IndexActionVariantDto): Promise<Action>;
}

export const ActionVariantServiceInterface = Symbol(
  'ActionVariantServiceInterface',
);
