import {
  BrandDocument,
  CompanyDocument,
  DeferredReplacementMap,
  FireTriggerDto,
  GenericTriggerModel,
  LanguageCode,
  LoggerService,
  mapAsync,
  MicroserviceCommunicationService,
  NotificationRecommendedIntegration,
  PrefireResponseDto,
  PrefireTriggerDto,
  ReplacementMap,
  SingleIdDto,
  TemplateTo,
  TriggerAction,
  TriggerClient,
  TriggerUserDto,
} from '@app/shared-stuff';
import {
  Inject,
  Injectable,
  OnModuleDestroy,
  OnModuleInit,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { isObjectIdOrHexString, Types } from 'mongoose';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { CompanyService } from '../../../company/services/company/company.service';
import { CustomerUserServiceInterface } from '../../../customer/modules/customer-user/customer-user.service.interface';
import { UserService } from '../../../user/services/user/user.service';
import { GenericReplacementService } from '../generic-replacement/generic-replacement.service';

@Injectable()
export class TriggerService implements OnModuleInit, OnModuleDestroy {
  private readonly loggerService = new LoggerService(TriggerService.name);
  constructor(
    private userService: UserService,
    @Inject(CustomerUserServiceInterface)
    private customerUserService: CustomerUserServiceInterface,
    @Inject('enable-main-notification-producer')
    private client: ClientProxy,
    private companyService: CompanyService,
    private microserviceCommunicationService: MicroserviceCommunicationService,
    @Inject('BrandServiceInterface')
    private brandService: BrandServiceInterface,
    private genericReplacementService: GenericReplacementService,
  ) {}

  async onModuleDestroy() {
    await this.microserviceCommunicationService.disconnect(this.client);
  }

  async onModuleInit() {
    await this.microserviceCommunicationService.connect(this.client);
  }

  async fireTrigger(
    genericTriggerModel: GenericTriggerModel,
    triggerAction: TriggerAction,
    customUsers?: TriggerUserDto[],
    recommendedIntegration?: NotificationRecommendedIntegration,
  ): Promise<FireTriggerDto> {
    const company = await this.companyService.get_details(
      genericTriggerModel.companyId,
    );

    const brandId = genericTriggerModel.brandId;
    const brand: BrandDocument = isObjectIdOrHexString(brandId)
      ? await this.brandService.findById(new Types.ObjectId(brandId))
      : null;

    const prefireTriggerDto = this.fillPrefireTriggerDto(
      company,
      genericTriggerModel,
      triggerAction,
      brand,
    );

    const response =
      await this.microserviceCommunicationService.produceAndWaitForResponse<
        PrefireTriggerDto,
        PrefireResponseDto
      >(this.client, 'prefire.notification.trigger', prefireTriggerDto);

    const loadedReplacements = await this.loadReplacements(
      genericTriggerModel.replacements,
      response.replacements,
    );

    const users = [
      ...(customUsers || []),
      ...(await this.getAllUsers(genericTriggerModel)),
    ];

    const trigger = this.fillFireTriggerDto(
      users,
      company,
      genericTriggerModel,
      triggerAction,
      brand,
      loadedReplacements,
      recommendedIntegration,
    );

    this.microserviceCommunicationService.produceAndForget(
      this.client,
      'fire.notification.trigger',
      trigger,
    );

    return trigger;
  }

  private async loadReplacements(
    replacements: DeferredReplacementMap,
    replacementsList: string[],
  ): Promise<ReplacementMap> {
    return Object.fromEntries(
      await mapAsync(replacementsList, async (replacement) => [
        replacement,
        typeof replacements[replacement] === 'function'
          ? await replacements[replacement]()
          : replacements[replacement],
      ]),
    );
  }

  async findById(triggerId: Types.ObjectId) {
    return this.microserviceCommunicationService.produceAndWaitForResponse<
      SingleIdDto,
      any
    >(this.client, 'findById.notification.trigger', { id: triggerId });
  }

  private async getAllUsers(
    genericTriggerModel: GenericTriggerModel,
  ): Promise<TriggerUserDto[]> {
    let users: TriggerUserDto[] = [];

    if (genericTriggerModel.branchId) {
      users = await this.userService.getBranchManagersUsers(
        genericTriggerModel,
        TemplateTo.BRANCH_MANAGER,
        users,
      );
    }
    users = await this.userService.getSuperAdminUsers(
      genericTriggerModel,
      TemplateTo.SUPER_ADMIN,
      users,
    );
    users = await this.userService.getDispatcherUsers(
      genericTriggerModel,
      TemplateTo.DISPATCHER,
      users,
    );
    users = await this.customerUserService.getCustomerUser(
      genericTriggerModel,
      users,
    );
    if (genericTriggerModel.isGift == true) {
      users = this.userService.getGiftRecipientUser(genericTriggerModel, users);
    }
    users = this.userService.getItemCreatorUser(genericTriggerModel, users);
    return users;
  }

  private fillPrefireTriggerDto(
    company: CompanyDocument,
    genericTriggerModel: GenericTriggerModel,
    triggerAction: TriggerAction,
    brand: BrandDocument,
  ): PrefireTriggerDto {
    return {
      client: TriggerClient.ENABLE_MAIN,
      module: genericTriggerModel.triggerModule,
      action: triggerAction,
      owner: {
        _id: company?._id,
        name: company?.name,
        smsSenderId: genericTriggerModel.senderId,
        emailSenderId: genericTriggerModel.senderId,
      },
      secondOwner: brand
        ? {
            _id: brand._id,
            name: brand.name,
            smsSenderId: brand.senderId,
            emailSenderId: brand.emailSenderId,
          }
        : undefined,
    };
  }

  private fillFireTriggerDto(
    users: TriggerUserDto[],
    company: CompanyDocument,
    genericTriggerModel: GenericTriggerModel,
    triggerAction: TriggerAction,
    brand: BrandDocument,
    replacements: ReplacementMap,
    recommendedIntegration?: NotificationRecommendedIntegration,
  ): FireTriggerDto {
    const genericReplacement =
      this.genericReplacementService.getGenericReplacement(
        genericTriggerModel.language == LanguageCode.ar,
      );

    const mergedReplacements = {
      ...replacements,
      ...genericReplacement,
    };

    return {
      client: TriggerClient.ENABLE_MAIN,
      module: genericTriggerModel.triggerModule,
      action: triggerAction,
      owner: {
        _id: company?._id,
        name: company?.name,
        smsSenderId: genericTriggerModel.senderId,
        emailSenderId: genericTriggerModel.senderId,
      },
      secondOwner: brand
        ? {
            _id: brand._id,
            name: brand.name,
            smsSenderId: brand.senderId,
            emailSenderId: brand.emailSenderId,
          }
        : undefined,
      users: users,
      replacements: mergedReplacements,
      recommendedIntegration: recommendedIntegration,
      context: genericTriggerModel.context,
    };
  }
}
