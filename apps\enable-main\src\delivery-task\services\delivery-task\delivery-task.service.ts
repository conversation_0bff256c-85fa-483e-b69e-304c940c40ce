import {
  CollectionName,
  CompanyDocument,
  ContactChannel,
  DeliveryMethod,
  DriverDocument,
  LogError,
  OrderDocument,
  responseCode,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';

import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import { CustomerReadServiceInterface } from '../../../customer/modules/customer-read/customer-read.service.interface';
import { CustomerWriteServiceInterface } from '../../../customer/modules/customer-write/customer-write.service.interface';
import { DeliveryPartyThirdParty } from '../../../delivery-task/dto/delivery-party.dto';
import { BeeService } from '../../../delivery/services/third-parties/bee/bee.service';
import { BranchService } from './../../../branch/services/branch/branch.service';
import { CompanyService } from './../../../company/services/company/company.service';
import { TookanMultipleTasksToAssign } from './../../../delivery/dto/tookan.dto';
import { DriverService } from './../../../delivery/services/driver/driver.service';
import { TookanService } from './../../../delivery/services/tookan/tookan.service';
import {
  DeliveryTaskDetailsToGet,
  DeliveryTaskDriverToAssign,
  DeliveryTaskPointToDelete,
  DeliveryTaskStatus,
  DeliveryTaskStatusToUpdate,
  DeliveryTaskToCreate,
  DeliveryTaskToIndex,
  DeliveryTaskToRemove,
  DeliveryTaskType,
} from './../../dto/delivery-task.dto';
import { DeliveryPartyDocument } from './../../models/delivery-party.model';
import { DeliveryTaskLogDocument } from './../../models/delivery-task-log.model';
import { DeliveryTaskDocument } from './../../models/delivery-task.model';
import { DeliveryPartyService } from './../delivery-party/delivery-party.service';

@Injectable()
export class DeliveryTaskService {
  constructor(
    @InjectModel(CollectionName.ORDER) private orderModel: Model<OrderDocument>,
    @InjectModel('DeliveryTask')
    private deliveryTaskModel: Model<DeliveryTaskDocument>,
    @InjectModel('DeliveryTaskLog')
    private deliveryTaskLog: Model<DeliveryTaskLogDocument>,
    @InjectModel('DeliveryParty')
    private deliveryPartyModel: Model<DeliveryPartyDocument>,
    private beeService: BeeService,
    private branchService: BranchService,
    private eventEmitter: EventEmitter2,
    private tookanService: TookanService,
    private driverService: DriverService,
    @Inject(CustomerReadServiceInterface)
    private customerReadService: CustomerReadServiceInterface,
    @Inject(CustomerWriteServiceInterface)
    private customerWriteService: CustomerWriteServiceInterface,
    private deliveryPartyService: DeliveryPartyService,
    private companyService: CompanyService,
  ) {}

  async index(deliveryTaskToIndex: DeliveryTaskToIndex) {
    const match: unknown = { deletedAt: { $exists: false } };
    const sort: unknown = {};

    if (deliveryTaskToIndex.searchKey && deliveryTaskToIndex.searchValue) {
      const availableFields = [
        'code',
        'status',
        'type',
        'orderCode',
        'barCode',
        'deliveryPartyName',
        'driverName',
        'customerName',
        'customerPhone',
      ].filter((field) => {
        return !Object.keys(match).includes(field);
      });

      if (
        availableFields.includes(deliveryTaskToIndex.searchKey) &&
        deliveryTaskToIndex.searchValue
      ) {
        match[deliveryTaskToIndex.searchKey] = {
          $regex: deliveryTaskToIndex.searchValue,
          $options: 'g',
        };
      } else if (deliveryTaskToIndex.searchKey === 'all') {
        match['$or'] = availableFields.map((field) => {
          return {
            [field]: {
              $regex: deliveryTaskToIndex.searchValue,
              $options: 'g',
            },
          };
        });
      }
    }

    if (deliveryTaskToIndex.sortBy) {
      const availableSortingFields = [
        'code',
        'status',
        'type',
        'orderCode',
        'barCode',
        'deliveryPartyName',
        'driverName',
        'customerName',
        'customerPhone',
      ];
      const parts = deliveryTaskToIndex.sortBy.split(':');
      if (availableSortingFields.includes(parts[0])) {
        sort[parts[0]] = parts[1] === 'desc' ? -1 : 1;
      } else {
        sort['createdAt'] = -1;
      }
    } else {
      // default sorting
      // if user is not providing a sorting type
      sort['createdAt'] = -1;
    }

    if (deliveryTaskToIndex.company) {
      match['company'] = deliveryTaskToIndex.company;
    }

    if (deliveryTaskToIndex.customer) {
      match['customer'] = deliveryTaskToIndex.customer;
    }

    if (deliveryTaskToIndex.driver) {
      match['driver'] = deliveryTaskToIndex.driver;
    }

    const pipeline = [
      { $match: match },
      { $sort: { _id: 1 } },
      { $sort: sort },
      ...(deliveryTaskToIndex.limit !== null &&
      deliveryTaskToIndex.offset !== null
        ? [{ $limit: deliveryTaskToIndex.limit + deliveryTaskToIndex.offset }]
        : []),
      ...(deliveryTaskToIndex.offset !== null
        ? [{ $skip: deliveryTaskToIndex.offset }]
        : []),
    ];

    const deliveryTasks = await this.deliveryTaskModel.aggregate(
      pipeline as any,
    );
    return deliveryTasks;
  }

  async getTotal(deliveryTaskToIndex: DeliveryTaskToIndex) {
    deliveryTaskToIndex.limit = null;
    deliveryTaskToIndex.offset = null;
    const totalDeliveryTasks = await this.index(deliveryTaskToIndex);
    return totalDeliveryTasks.length;
  }

  async getDetails(deliveryPartyDetailsToGet: DeliveryTaskDetailsToGet) {
    const match = {
      _id: deliveryPartyDetailsToGet.deliveryTaskId,
      deletedAt: { $exists: false },
    };

    if (deliveryPartyDetailsToGet.companyId) {
      match['company'] = deliveryPartyDetailsToGet.companyId;
    }
    if (deliveryPartyDetailsToGet.deliveryPartyId) {
      match['deliveryParty'] = deliveryPartyDetailsToGet.deliveryPartyId;
    }

    if (
      !deliveryPartyDetailsToGet.deliveryPartyId &&
      !deliveryPartyDetailsToGet.companyId
    ) {
      throw {
        code: responseCode.BAD_REQUEST,
        statusCode: 400,
        message: "You don't have access to this resources",
      };
    }

    const deliveryTask = await this.deliveryTaskModel.findOne(match);

    if (!deliveryTask) {
      throw {
        code: responseCode.BAD_REQUEST,
        statusCode: 400,
        message: 'You need to provide a valid and existing Delivery Task id',
      };
    }

    return deliveryTask;
  }

  // Task Creation Start
  async create(deliveryTaskToCreate: DeliveryTaskToCreate) {
    const company = await this.companyService.get_details(
      deliveryTaskToCreate.company as any,
    );

    // Generate the TaskCode
    const count = await this.deliveryTaskModel.count({
      company: deliveryTaskToCreate.company,
    });
    deliveryTaskToCreate['code'] =
      company.acronym + '-D-' + count.toString().padStart(5, '0');

    // Update the Task ORder Info
    if (deliveryTaskToCreate.order) {
      const order = await this.orderModel.findOne({
        _id: new Types.ObjectId(deliveryTaskToCreate.order),
      });
      deliveryTaskToCreate['orderCode'] = order.code;
    }

    // Update the location name and location and barcode  id property for pickup
    await this.updatePickupPoints(deliveryTaskToCreate);

    // Update the location name and location and barcode  id property for delivery
    await this.updateDeliveryPoints(deliveryTaskToCreate);

    // Check if the User provider Customer Not customerId
    await this.gatherCustomerInfo(deliveryTaskToCreate, company);

    // Check if there is a driver and if true gather the driver info
    await this.gatherDriverInfo(deliveryTaskToCreate);

    const deliveryTask = new this.deliveryTaskModel(deliveryTaskToCreate);

    // fire the task to Tookan or Bee
    await this.fireTaskToThirdParty(deliveryTask, company);

    await deliveryTask.save();
    return deliveryTask;
  }

  private async updatePickupPoints(deliveryTaskToCreate: DeliveryTaskToCreate) {
    if (
      deliveryTaskToCreate.type == DeliveryTaskType.Pickup ||
      deliveryTaskToCreate.type == DeliveryTaskType['Pickup & Delivery']
    ) {
      for (let i = 0; i < deliveryTaskToCreate.pickupPoints.length; i++) {
        const currentPoint = deliveryTaskToCreate.pickupPoints[i];

        if (!currentPoint.branch) {
          throw {
            code: responseCode.BAD_REQUEST,
            statusCode: 400,
            message: 'Please Provide the branch for the point',
          };
        }

        const branch = await this.branchService.get_details(
          currentPoint.branch,
        );
        currentPoint.location = new Types.ObjectId(branch['address']['_id']);
        currentPoint.locationName = branch['address']['nickname'];
        currentPoint.pinLocation = branch['address']['pin_link'];
        currentPoint.barCode = deliveryTaskToCreate.barCode;
      }
    } else {
      deliveryTaskToCreate.pickupPoints = [];
    }
  }

  private async updateDeliveryPoints(
    deliveryTaskToCreate: DeliveryTaskToCreate,
  ) {
    if (
      deliveryTaskToCreate.type == DeliveryTaskType.Delivery ||
      deliveryTaskToCreate.type == DeliveryTaskType['Pickup & Delivery']
    ) {
      for (let i = 0; i < deliveryTaskToCreate.deliveryPoints.length; i++) {
        const currentPoint = deliveryTaskToCreate.deliveryPoints[i];

        if (!currentPoint.location) {
          throw {
            code: responseCode.BAD_REQUEST,
            statusCode: 400,
            message: 'Please Provide the branch for the point',
          };
        }

        // const address = await this.savedAddressService.get_details(
        //   currentPoint.location,
        // );
        // currentPoint.location = new Types.ObjectId(address._id);
        // currentPoint.locationName = address['nickname'];
        // currentPoint.pinLocation = address['pin_link'];
        currentPoint.barCode = deliveryTaskToCreate.barCode;
      }
    } else {
      deliveryTaskToCreate.deliveryPoints = [];
    }
  }

  private async gatherCustomerInfo(
    deliveryTaskToCreate: DeliveryTaskToCreate,
    company: CompanyDocument,
  ) {
    if (deliveryTaskToCreate.customer && !deliveryTaskToCreate.customerId) {
      const customer = await this.customerWriteService.findOrCreate({
        full_name:
          deliveryTaskToCreate.customer.first_name +
          deliveryTaskToCreate.customer.last_name,
        first_name: deliveryTaskToCreate.customer.first_name,
        last_name: deliveryTaskToCreate.customer.last_name,
        phone: deliveryTaskToCreate.customer.phone,
        country_code: deliveryTaskToCreate.customer.country_code,
        company: company._id,
        email: deliveryTaskToCreate.customer.email,
        contact_channel: ContactChannel.UNKNOWN,
      });
      deliveryTaskToCreate.customerId = new Types.ObjectId(customer._id);
    }
    deliveryTaskToCreate.customer = new Types.ObjectId(
      deliveryTaskToCreate.customerId,
    ) as any;

    const customer = await this.customerReadService.findOne(
      deliveryTaskToCreate.customerId as any,
      company._id,
    );
    deliveryTaskToCreate['customerName'] = customer.full_name;
    deliveryTaskToCreate['customerPhone'] = customer.phone;
    deliveryTaskToCreate['customerCountryCode'] = customer.country_code;
  }

  private async gatherDriverInfo(deliveryTaskToCreate: DeliveryTaskToCreate) {
    if (deliveryTaskToCreate.driver) {
      const driver = await this.driverService.get_details(
        deliveryTaskToCreate.driver.toHexString(),
      );
      deliveryTaskToCreate['driverName'] = driver.first_name + driver.last_name;
      deliveryTaskToCreate['assignDriverLater'] = false;
    } else {
      deliveryTaskToCreate['assignDriverLater'] = true;
    }
  }

  private async fireTaskToThirdParty(
    deliveryTask: DeliveryTaskDocument,
    company: CompanyDocument,
  ) {
    const deliveryParty = await this.deliveryPartyService.getDetails(
      deliveryTask.deliveryParty as any,
    );

    if (deliveryParty.isOwnDrivers) {
      deliveryParty.thirdPartyConfig.TEAM_ID = company.tookan_team_id;
    }

    deliveryTask.deliveryPartyName = deliveryParty.name;

    if (
      deliveryTask.driver &&
      deliveryParty.thirdParty == DeliveryPartyThirdParty.TOOKAN
    ) {
      await this.createTookanTask(deliveryTask, deliveryParty, company);
    } else if (deliveryParty.thirdParty == DeliveryPartyThirdParty.BEE) {
      await this.createBeeTask(deliveryTask, deliveryParty, company);
    }
  }
  // Task Create End

  async createDeliveryTaskFromOrder(order: OrderDocument) {
    let deliveryParty;
    const customer = await this.customerReadService.findOne(
      order.customer.toHexString(),
      order.company,
    );

    if (
      order.deliveryMethod == DeliveryMethod.THIRD_PARTY &&
      order.deliveryParty == 'bee'
    ) {
      deliveryParty = await this.deliveryPartyModel.findOne({
        name: 'Bee Delivery',
      });
    } else if (order.deliveryMethod == DeliveryMethod.BRANCH_DRIVERS) {
      deliveryParty = await this.deliveryPartyModel.findOne({
        name: 'Own Drivers',
      });
    } else if (order.deliveryMethod == DeliveryMethod.E_BUTLER) {
      deliveryParty = await this.deliveryPartyModel.findOne({
        name: 'EB Delivery',
      });
    }

    const deliveryTaskToCreate: DeliveryTaskToCreate = {
      customerId: customer._id,
      autoAssign: order.autoAssign,
      barCode: order.barCode,
      deliveryFees: order.delivery_amount,
      type: DeliveryTaskType['Pickup & Delivery'],
      deliveryPoints: [],
      pickupPoints: [],
      order: order._id,
      deliveryParty: deliveryParty._id,
      company: order.company as any,
      driver: order.driver,
    };

    if (order.branch_object) {
      deliveryTaskToCreate.pickupPoints.push({
        date: moment(order.pickup_date).toDate(),
        branch: order.branch_object as any,
        barCode: order.barCode,
      } as any);
    }

    if (order.deliveryLocation) {
      deliveryTaskToCreate.deliveryPoints.push({
        date: moment(order.delivery_date).toDate(),
        location: order.deliveryLocation,
      } as any);
    }

    const task = await this.create(deliveryTaskToCreate);
    return task;
  }

  async remove(deliveryTaskToRemove: DeliveryTaskToRemove) {
    const deliveryTask = await this.getDetails({ ...deliveryTaskToRemove });
    deliveryTask.deletedAt = moment.utc().toDate();
    deliveryTask.deletedBy = deliveryTaskToRemove.currentUser;
    await deliveryTask.save();
    return deliveryTask;
  }

  async updateStatus(deliveryTaskStatusToUpdate: DeliveryTaskStatusToUpdate) {
    const deliveryTask = await this.getDetails({
      deliveryTaskId: deliveryTaskStatusToUpdate.deliveryTaskId,
      companyId: deliveryTaskStatusToUpdate.companyId,
      deliveryPartyId: deliveryTaskStatusToUpdate.deliveryPartyId,
    });

    deliveryTask.status = deliveryTaskStatusToUpdate.status;
    await deliveryTask.save();

    return deliveryTask;
  }

  async deletePickupPoint(
    deliveryTaskPointToDelete: DeliveryTaskPointToDelete,
  ) {
    const deliveryTask = await this.getDetails({
      deliveryPartyId: deliveryTaskPointToDelete.deliveryPartyId,
      companyId: deliveryTaskPointToDelete.companyId,
      deliveryTaskId: deliveryTaskPointToDelete.deliveryTaskId,
    });

    const pickupPointIndex = deliveryTask.pickupPoints.findIndex(
      (x) => x.barCode == deliveryTaskPointToDelete.barCode,
    );
    if (pickupPointIndex != -1) {
      deliveryTask.pickupPoints = deliveryTask.pickupPoints.splice(
        pickupPointIndex,
        1,
      );
    }
    await deliveryTask.save();
  }

  async deleteDeliveryPoint(
    deliveryTaskPointToDelete: DeliveryTaskPointToDelete,
  ) {
    const deliveryTask = await this.getDetails({
      deliveryPartyId: deliveryTaskPointToDelete.deliveryPartyId,
      companyId: deliveryTaskPointToDelete.companyId,
      deliveryTaskId: deliveryTaskPointToDelete.deliveryTaskId,
    });

    const deliveryPointIndex = deliveryTask.deliveryPoints.findIndex(
      (x) => x.barCode == deliveryTaskPointToDelete.barCode,
    );
    if (deliveryPointIndex != -1) {
      deliveryTask.deliveryPoints = deliveryTask.deliveryPoints.splice(
        deliveryPointIndex,
        1,
      );
    }
    await deliveryTask.save();
  }

  async assignDriverToTask(
    deliveryTaskDriverToAssign: DeliveryTaskDriverToAssign,
  ) {
    const deliveryTask = await this.deliveryTaskModel.findOne({
      _id: deliveryTaskDriverToAssign.deliveryTaskId,
    });
    const driver = await this.driverService.get_details(
      deliveryTaskDriverToAssign.driver.toHexString(),
    );
    const company = await this.companyService.get_details(deliveryTask._id);

    deliveryTask.driver = deliveryTaskDriverToAssign.driver;
    deliveryTask.driverName = driver.first_name + ' ' + driver.last_name;

    if (
      deliveryTask.assignDriverLater &&
      deliveryTask.status != DeliveryTaskStatus['Fired To Party']
    ) {
      await this.fireTaskToThirdParty(deliveryTask, company);
    }

    await deliveryTask.save();
    return deliveryTask;
  }

  async createBeeTask(
    deliveryTask: DeliveryTaskDocument,
    deliveryParty: DeliveryPartyDocument,
    company: CompanyDocument,
  ) {
    // const currentPickupPoint = deliveryTask.pickupPoints[0];
    // const currentDeliveryPoint = deliveryTask.deliveryPoints[0];
    // const deliveryAddress = await this.savedAddressService.get_details(
    //   currentDeliveryPoint.location,
    // );
    // const pickupAddress = await this.savedAddressService.get_details(
    //   currentPickupPoint.location,
    // );
    // const branch = await this.branchService.get_details(
    //   currentPickupPoint.branch as any,
    //   '',
    // );
    // const deliveryTime = moment(currentDeliveryPoint.date).diff(
    //   moment(currentPickupPoint.date),
    //   'minute',
    // );
    // const beeDeliveryTaskToCreate: BeeTaskToCreate = {
    //   API_KEY: deliveryParty.thirdPartyConfig.API_KEY,
    //   name: deliveryTask.customerName,
    //   mobile:
    //     (deliveryTask.customerCountryCode ?? '') + deliveryTask.customerPhone,
    //   branch_name: branch.name,
    //   ref_number: deliveryTask.code,
    //   payment_type: '1',
    //   total_amount: deliveryTask.deliveryFees,
    //   customer_full_address:
    //     deliveryAddress['nickname'] +
    //     ', Building ' +
    //     deliveryAddress['building_name'] +
    //     ', Zone ' +
    //     deliveryAddress['zone_number'] +
    //     ', Street ' +
    //     deliveryAddress['street_number'] +
    //     ', Building Number ' +
    //     deliveryAddress['building_number'],
    //   branch_id: branch['_id'].toHexString(),
    //   branch_address:
    //     pickupAddress['nickname'] +
    //     ', Building ' +
    //     pickupAddress['building_name'] +
    //     ', Zone ' +
    //     pickupAddress['zone_number'] +
    //     ', Street ' +
    //     pickupAddress['street_number'] +
    //     ', Building Number ' +
    //     pickupAddress['building_number'],
    //   client_name: company['name'],
    //   branch_latitude: pickupAddress.pin_lat,
    //   branch_longitude: pickupAddress.pin_lng,
    //   customer_address_latitude: deliveryAddress.pin_lat,
    //   customer_address_longitude: deliveryAddress.pin_lng,
    //   client_id: new Types.ObjectId().toHexString(),
    //   estimate_cooking_time: deliveryTime.toString(),
    // } as any;
    // beeDeliveryTaskToCreate.mobile = beeDeliveryTaskToCreate.mobile.replace(
    //   '+',
    //   '',
    // );
    // const response = await this.beeService.createTask(beeDeliveryTaskToCreate);
    // await this.deliveryTaskLog.create({
    //   deliveryTask: deliveryTask._id,
    //   rawRequest: beeDeliveryTaskToCreate,
    //   rawResponse: response,
    // });
    // return response;
  }

  // Bee Delivery Integration
  @OnEvent('bee.updated')
  @LogError()
  async onBeeTaskUpdated(beeData: any) {
    const deliveryTask = await this.deliveryTaskModel.findOne({
      code: beeData['order_id'],
    });
    if (!deliveryTask) {
      return;
    }

    if (beeData['delivery_status'] == 'delivery_picked_up') {
      deliveryTask.status = DeliveryTaskStatus['In Route'];
      deliveryTask.pickupPoints[0].completeDate = moment().utc().toDate();
    } else if (beeData['delivery_status'] == 'delivery_assigned') {
      deliveryTask.status = DeliveryTaskStatus['Picked Up'];
      deliveryTask.deliveryPoints[0].startDate = moment().utc().toDate();
    } else if (beeData['delivery_status'] == 'delivery_completed') {
      deliveryTask.status = DeliveryTaskStatus.Delivered;
      deliveryTask.deliveryPoints[0].completeDate = moment().utc().toDate();
    }
    await deliveryTask.save();
    this.eventEmitter.emit('deliveryTask.updated', deliveryTask);
    return deliveryTask;
  }

  async createTookanTask(
    deliveryTask: DeliveryTaskDocument,
    deliveryParty: DeliveryPartyDocument,
    company: CompanyDocument,
  ) {
    // let driver;
    // if (deliveryTask.driver) {
    //   driver = await this.driverService.get_details(deliveryTask.driver);
    // }
    // const customer = await this.customerService.get_details(
    //   deliveryTask.customer.toHexString(),
    //   '',
    //   '',
    // );
    // const tookanTasksToCreate: TookanMultipleTasksToCreate = {
    //   pickups: [],
    //   deliveries: [],
    //   api_key: deliveryParty.thirdPartyConfig.API_KEY,
    //   tags: deliveryTask.autoAssign ? `${deliveryTask.company.toString()}` : '',
    //   timezone: 0,
    //   auto_assignment: deliveryTask.autoAssign ? 1 : 0,
    //   team_id: deliveryParty.thirdPartyConfig.TEAM_ID,
    //   fleet_id: driver ? parseInt(driver.tookan_driver_id) : 0,
    //   geofence: 0,
    //   layout_type: 0,
    //   has_delivery: 1,
    //   has_pickup: 1,
    // };
    // // Handle The Pickup Points;
    // for (let i = 0; i < deliveryTask.pickupPoints.length; i++) {
    //   const currentPoint = deliveryTask.pickupPoints[i];
    //   const address = await this.savedAddressService.get_details(
    //     currentPoint.location,
    //   );
    //   const branch = await this.branchService.get_details(
    //     currentPoint.branch.toHexString(),
    //     '',
    //   );
    //   tookanTasksToCreate.pickups.push({
    //     address:
    //       address.nickname +
    //       ', Building ' +
    //       address.building_name +
    //       ', Zone ' +
    //       address.zone_number +
    //       ', Street ' +
    //       address.street_number +
    //       ', Building Number ' +
    //       address.building_number,
    //     barcode: deliveryTask.barCode,
    //     time: moment(currentPoint.date).format('HH:mm'),
    //     email: branch.email,
    //     name: branch.name,
    //     job_description: currentPoint.locationName,
    //     order_id: deliveryTask.code,
    //     latitude: address.pin_lat,
    //     longitude: address.pin_lng,
    //     ref_images: [],
    //     template_data: [],
    //     template_name: '',
    //   });
    // }
    // // Handle the Delivery Points;
    // for (let i = 0; i < deliveryTask.deliveryPoints.length; i++) {
    //   const currentPoint = deliveryTask.deliveryPoints[i];
    //   const address = await this.savedAddressService.get_details(
    //     currentPoint.location,
    //   );
    //   tookanTasksToCreate.deliveries.push({
    //     address:
    //       address.nickname +
    //       ', Building ' +
    //       address.building_name +
    //       ', Zone ' +
    //       address.zone_number +
    //       ', Street ' +
    //       address.street_number +
    //       ', Building Number ' +
    //       address.building_number,
    //     barcode: deliveryTask.barCode,
    //     time: moment(currentPoint.date).format('HH:mm'),
    //     email: customer.email,
    //     name: customer.first_name + ' ' + customer.last_name,
    //     job_description: currentPoint.locationName,
    //     order_id: deliveryTask.code,
    //     latitude: address.pin_lat,
    //     longitude: address.pin_lng,
    //     ref_images: [],
    //     template_data: [],
    //     template_name: '',
    //   });
    // }
    // const response = await this.tookanService.createMultipleTask(
    //   tookanTasksToCreate,
    // );
    // await this.deliveryTaskLog.create({
    //   deliveryTask: deliveryTask._id,
    //   rawRequest: tookanTasksToCreate,
    //   rawResponse: response,
    // });
    // //Update the pickups ids and deliverers ids
    // for (let i = 0; i < response['pickups'].length; i++) {
    //   deliveryTask.pickupPoints[i].taskId = response['pickups'][i]['job_id'];
    // }
    // for (let i = 0; i < response['deliveries'].length; i++) {
    //   deliveryTask.deliveryPoints[i].taskId =
    //     response['deliveries'][i]['job_id'];
    // }
    // deliveryTask.status = DeliveryTaskStatus['Fired To Party'];
    // await deliveryTask.save();
    // return deliveryTask;
  }

  // Tookan Delivery Integration
  async onTookanTaskUpdated(tookanData: any) {
    const deliveryTask = await this.deliveryTaskModel.findOne({
      code: tookanData['order_id'],
    });

    if (!deliveryTask) {
      return;
    }
    // order.assigned_driver_name = tookanData['fleet_name'];

    // Logger.log(tookanData);
    if (tookanData['job_status'] == 2 && tookanData['job_type'] == 0) {
      deliveryTask.status = DeliveryTaskStatus['Picked Up'];
    } else if (tookanData['job_status'] == 1 && tookanData['job_type'] == 1) {
      deliveryTask.status = DeliveryTaskStatus['In Route'];
    } else if (tookanData['job_status'] == 2 && tookanData['job_type'] == 1) {
      deliveryTask.status = DeliveryTaskStatus['Delivered'];
    }
    await deliveryTask.save();
    return deliveryTask;
  }

  async assignTookanDriverToMultipleTasks(
    deliveryTask: DeliveryTaskDocument,
    deliveryParty: DeliveryPartyDocument,
    driver: DriverDocument,
  ) {
    const jobIds = [];

    // handel pickup JobIds
    for (let i = 0; i < deliveryTask.pickupPoints.length; i++) {
      jobIds.push(deliveryTask.pickupPoints[i].taskId);
    }
    // handel Delivery JobIds
    for (let i = 0; i < deliveryTask.deliveryPoints.length; i++) {
      jobIds.push(deliveryTask.deliveryPoints[i].taskId);
    }

    const tookanMultipleTasksToAAssign: TookanMultipleTasksToAssign = {
      api_key: deliveryParty.thirdPartyConfig.API_KEY,
      fleet_id: parseInt(driver.tookan_driver_id),
      team_id: parseInt(deliveryParty.thirdPartyConfig.TEAM_ID),
      job_ids: jobIds,
    };
    const result = await this.tookanService.assignDriverToMultipleTask(
      tookanMultipleTasksToAAssign,
    );
    return result;
  }
}
