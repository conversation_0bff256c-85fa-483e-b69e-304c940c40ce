import { OrdableHttpResponseDto } from '../dtos/ordable-http-response.dto';

export interface OrdableHttpRequestsServiceInterface {
  createOrdablePostRequest(
    URL: string,
    API_KEY: string,
    createOrdableDto: any, //  any just for now
  ): Promise<OrdableHttpResponseDto>;
  createOrdablePatchRequest(
    URL: string,
    API_KEY: string,
    updateOrdableDto: any,
  ): Promise<OrdableHttpResponseDto>;
  createOrdableGetRequest(
    URL: string,
    API_KEY: string,
  ): Promise<OrdableHttpResponseDto>;
  createOrdableDeleteRequest(
    URL: string,
    API_KEY: string,
  ): Promise<OrdableHttpResponseDto>;
  logOrdableResponse(
    action: string,
    requestBody: any,
    responseBody: any,
  ): Promise<void>;
}
