import { IGenericRepository } from '@app/shared-stuff';
import { Types } from 'mongoose';
import {
  ErrorBody,
  IntegrationLog,
  IntegrationLogDocument,
} from '../../models/integration.log.model';

export interface IntegrationLogRepositoryInterface
  extends IGenericRepository<IntegrationLogDocument, IntegrationLog> {
  logSuccess(
    action: string,
    requestBody: Record<string, any>,
    responseBody: Record<string, any>,
    itemId?: string | Types.ObjectId,
    requestHeaders?: Record<string, any>,
  ): Promise<void>;

  logError(
    action: string,
    requestBody: Record<string, any>,
    responseBody: Record<string, any>,
    errorBody?: ErrorBody,
    requestHeaders?: Record<string, any>,
  ): Promise<void>;
}
