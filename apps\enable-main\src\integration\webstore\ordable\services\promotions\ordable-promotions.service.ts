import {
  BenefitMaximumUsageType,
  BenefitType,
  CouponDocument,
  CustomerEarnedBenefit,
  CustomerOrdableStores,
  EarnedReward,
  LoggerService,
  LoyaltyTierDocument,
  PromotionOrdableInfo,
  PunchCardBenefit,
} from '@app/shared-stuff';
import {
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';

import * as moment from 'moment-timezone';
import { CustomerReadServiceInterface } from '../../../../../customer/modules/customer-read/customer-read.service.interface';
import { capitalizeFirstLetter } from '../../../../../shared/services/helper/helper.service';
import { StoreDocument } from '../../../../../store/models/store.model';
import { OrdablePromotionResponseDto } from '../../dtos/promotions/ordable-promotion-response.dto';
import { OrdablePromotionDto } from '../../dtos/promotions/ordable-promotion.dto';
import { OrdableDiscountType } from '../../enums/ordable-discount-type.enum';
import { OrdableHttpRequestsServiceInterface } from '../ordable-http-requests.service.interface';
import { OrdableStoresServiceInterface } from '../stores/ordable-stores.service.interface';
import { OrdablePromotionsServiceInterface } from './ordable-promotions.service.interface';
import { CompanyService } from 'apps/enable-main/src/company/services/company/company.service';

@Injectable()
export class OrdablePromotionsService
  implements OrdablePromotionsServiceInterface
{
  private readonly logger = new LoggerService(OrdablePromotionsService.name);
  private readonly PROMOTIONS_URI = '/api/promotions/';
  constructor(
    @Inject('OrdableHttpRequestsServiceInterface')
    private readonly ordableHttpRequestsService: OrdableHttpRequestsServiceInterface,
    @Inject(OrdableStoresServiceInterface)
    private readonly ordableStoresService: OrdableStoresServiceInterface,
    @Inject(CustomerReadServiceInterface)
    private readonly customerReadService: CustomerReadServiceInterface,
    private readonly companyService: CompanyService,
  ) {}

  async createCouponDiscount(coupon: CouponDocument): Promise<CouponDocument> {
    const customers =
      await this.customerReadService.findEligibleForCoupon(coupon);
    if (!customers || customers.length === 0) return;

    return await this.ordableStoresService.forEachStore(
      coupon,
      async (store) =>
        await this.createCouponDiscountForStore(
          store,
          coupon,
          customers.map((customer) => customer.ordableStores),
        ),
    );
  }

  private async createCouponDiscountForStore(
    store: StoreDocument,
    coupon: CouponDocument,
    customerOrdableStores: CustomerOrdableStores[],
  ): Promise<PromotionOrdableInfo | void> {
    const storeId: string = store._id.toHexString();
    const targeted_user = customerOrdableStores
      .filter((customerOrdableStore) => storeId in customerOrdableStore)
      .map((customer) => customer[storeId].ordableId);

    const ordablePromotionDto = await this.getOrdablePromotionDto(
      coupon,
      store,
      {
        targeted_user,
      },
    );

    const response: OrdablePromotionResponseDto =
      await this.ordableHttpRequestsService.createOrdablePostRequest(
        `${store.apiBaseUrl}${this.PROMOTIONS_URI}`,
        store.apiKey,
        ordablePromotionDto,
      );

    await this.ordableHttpRequestsService.logOrdableResponse(
      '[Ordable] Create Promotion for new Coupon',
      ordablePromotionDto,
      response,
    );

    if (!response.success) return;

    const couponOrdableInfo: PromotionOrdableInfo = {
      ordableId: response.data.id,
      ordableCustomerIds: targeted_user,
    };

    return couponOrdableInfo;
  }

  async deleteCouponDiscount(coupon: CouponDocument): Promise<CouponDocument> {
    if (!coupon || !coupon.ordableStores) return;

    return await this.ordableStoresService.forEachStore(
      coupon,
      async (store) =>
        await this.deleteOrdablePromotion(
          store,
          coupon.ordableStores[store._id.toHexString()]?.ordableId,
        ),
    );
  }

  async syncPromotionUpdate(
    enablePromotion: LoyaltyTierDocument | CouponDocument,
  ): Promise<void> {
    if (!enablePromotion || !enablePromotion.ordableStores) return;

    await this.ordableStoresService.forEachStore(
      enablePromotion,
      async (store) =>
        await this.syncPromotionUpdateForStore(store, enablePromotion),
    );
  }

  private async syncPromotionUpdateForStore(
    store: StoreDocument,
    enablePromotion: LoyaltyTierDocument | CouponDocument,
  ): Promise<void> {
    const ordableId =
      enablePromotion?.ordableStores?.[store._id.toHexString()]?.ordableId;
    if (!ordableId) return;

    const updateOrdablePromotionDto = await this.getOrdablePromotionDto(
      enablePromotion,
      store,
      { id: ordableId },
    );

    const response: OrdablePromotionResponseDto =
      await this.ordableHttpRequestsService.createOrdablePatchRequest(
        `${store.apiBaseUrl}${this.PROMOTIONS_URI}${ordableId}/`,
        store.apiKey,
        updateOrdablePromotionDto,
      );

    await this.ordableHttpRequestsService.logOrdableResponse(
      '[Ordable] Update Promotion',
      updateOrdablePromotionDto,
      response,
    );
  }

  private isLoyaltyTier(
    enablePromotion:
      | LoyaltyTierDocument
      | CouponDocument
      | EarnedReward
      | CustomerEarnedBenefit,
  ): enablePromotion is LoyaltyTierDocument {
    return 'tierIndex' in enablePromotion;
  }

  private isEarnedReward(
    enablePromotion:
      | LoyaltyTierDocument
      | CouponDocument
      | EarnedReward
      | CustomerEarnedBenefit,
  ): enablePromotion is EarnedReward {
    return 'earnedAt' in enablePromotion && !('appliedTo' in enablePromotion);
  }

  private async getOrdablePromotionDto(
    enablePromotion:
      | LoyaltyTierDocument
      | CouponDocument
      | EarnedReward
      | CustomerEarnedBenefit,
    store: StoreDocument,
    overrides?: Partial<OrdablePromotionDto>,
  ): Promise<OrdablePromotionDto> {
    if (this.isLoyaltyTier(enablePromotion)) {
      return this.getOrdableTierPromotionDto(enablePromotion, overrides);
    } else if (this.isEarnedReward(enablePromotion)) {
      return this.getOrdableRewardPromotionDto(
        enablePromotion,
        store,
        overrides,
      );
    } else if (this.isCustomerEarnedBenefit(enablePromotion)) {
      return this.getOrdableCustomerEarnedBenefitPromotionDto(
        enablePromotion,
        store,
        overrides,
      );
    }

    return await this.getOrdableCouponPromotionDto(enablePromotion, overrides);
  }

  private isCustomerEarnedBenefit(
    enablePromotion:
      | LoyaltyTierDocument
      | CouponDocument
      | EarnedReward
      | CustomerEarnedBenefit,
  ): enablePromotion is CustomerEarnedBenefit {
    return 'appliedTo' in enablePromotion;
  }

  private getOrdableCustomerEarnedBenefitPromotionDto(
    enablePromotion: CustomerEarnedBenefit,
    store: StoreDocument,
    overrides?: Partial<OrdablePromotionDto>,
  ): OrdablePromotionDto {
    return {
      name: enablePromotion.titleEn,
      ar_name: enablePromotion.titleAr,
      quantity:
        enablePromotion.config.maximumUsageType ===
        BenefitMaximumUsageType.UNLIMITED
          ? 999999
          : enablePromotion.config.maximumUsage,
      start: moment.utc().format('YYYY-MM-DD'),
      expiry: enablePromotion.config.expiryDate
        ? moment.utc(enablePromotion.config.expiryDate).format('YYYY-MM-DD')
        : '2099-12-31', // TODO: increment this before the year 2100
      discount_type:
        enablePromotion.type === BenefitType.FIXED_DISCOUNT
          ? OrdableDiscountType.FIXED
          : OrdableDiscountType.PERCENT,
      discount_value: enablePromotion.value,
      ...overrides,
    };
  }

  private getOrdableTierPromotionDto(
    {
      nameEn,
      nameAr,
      percentDiscount,
      minimumCartValueForDiscount,
      _id,
    }: LoyaltyTierDocument,
    overrides?: Partial<OrdablePromotionDto>,
  ): OrdablePromotionDto {
    const hasMinOrder =
      minimumCartValueForDiscount && minimumCartValueForDiscount > 0;
    const minOrderText = hasMinOrder
      ? ` on orders above ${minimumCartValueForDiscount}QR`
      : '';
    const minOrderTextAr = hasMinOrder
      ? ` على الطلبات التي تتجاوز ${minimumCartValueForDiscount} ريال قطرى`
      : '';
    const promotionName = `${percentDiscount}% ${capitalizeFirstLetter(
      nameEn,
    )} Tier Discount${minOrderText}`;
    const promotionNameAr = `خصم بنسبة ${percentDiscount}% لعملاء الفئة ${nameAr}${minOrderTextAr}`;
    return {
      name: promotionName,
      ar_name: promotionNameAr,
      quantity: 999999,
      start: moment.utc().format('YYYY-MM-DD'),
      expiry: '2099-12-31', // TODO: increment this before the year 2100
      discount_type: OrdableDiscountType.PERCENT,
      discount_value: percentDiscount,
      min_order: minimumCartValueForDiscount,
      external_id: _id.toHexString(),
      ...overrides,
    };
  }

  private async getOrdableCouponPromotionDto(
    {
      loyaltyPointCost,
      benefits,
      titleEn,
      titleAr,
      _id,
      companyId,
    }: CouponDocument,
    overrides?: Partial<OrdablePromotionDto>,
  ): Promise<OrdablePromotionDto> {
    const company = await this.companyService.get_details(companyId);
    const promotionName = `${titleEn} Discount for ${loyaltyPointCost} ${company.loyaltyProgramConfig.pointsBalanceTitleEn}`;
    const promotionNameAr = `${titleAr} خصم مقابل ${loyaltyPointCost} ${company.loyaltyProgramConfig.pointsBalanceTitleAr}`;
    return {
      name: promotionName,
      ar_name: promotionNameAr,
      quantity: 999999,
      start: moment.utc().format('YYYY-MM-DD'),
      expiry: '2099-12-31', // TODO: increment this before the year 2100
      discount_type: OrdableDiscountType.FIXED,
      discount_value: benefits[0].value, // we are not supporting multiple benefits with the integrators
      loyality_points: loyaltyPointCost,
      external_id: _id.toHexString(),
      ...overrides,
    };
  }

  private getOrdableRewardPromotionDto(
    reward: EarnedReward,
    store: StoreDocument,
    overrides?: Partial<OrdablePromotionDto>,
  ): OrdablePromotionDto {
    if (reward.benefit === PunchCardBenefit.MENU_ITEM)
      return this.getOrdableMenuItemRewardPromotionDto(
        reward,
        store,
        overrides,
      );

    const promotionName = `${reward.amount}% Reward `;
    const promotionNameAr = `${reward.amount}% مكافأة`;
    return {
      name: promotionName,
      ar_name: promotionNameAr,
      quantity: 999999,
      start: moment.utc().format('YYYY-MM-DD'),
      expiry: '2099-12-31', // TODO: increment this before the year 2100
      discount_type: OrdableDiscountType.PERCENT,
      discount_value: reward.amount,
      external_id: reward._id.toHexString(),
      ...overrides,
    };
  }

  private getOrdableMenuItemRewardPromotionDto(
    reward: EarnedReward,
    store: StoreDocument,
    overrides: Partial<OrdablePromotionDto>,
  ): OrdablePromotionDto {
    const ordableId =
      reward?.menuItem?.integrationInfo?.ordableInfo?.[store._id.toString()];

    if (!ordableId) {
      this.logger.error(
        `Cannot sync menu item reward for "${reward?.menuItem?.nameEn}" as it does not have an ordableId for store ${store._id.toHexString()}. Ensure it is a master menu item, and try ordable menu syncing the master menu to this store.`,
      );
      return;
    }

    const promotionName = `Free ${reward.menuItem.nameEn}`;
    const promotionNameAr = `مجاني ${reward.menuItem.nameAr}`;
    return {
      name: promotionName,
      ar_name: promotionNameAr,
      quantity: 1,
      start: moment.utc().format('YYYY-MM-DD'),
      expiry: '2099-12-31', // TODO: increment this before the year 2100
      discount_type: OrdableDiscountType.PRODUCTS_PERCENT,
      discount_value: 100,
      products: [ordableId],
      external_id: reward._id.toHexString(),
      ...overrides,
    };
  }

  async removeCustomerFromPromotion(
    store: StoreDocument,
    customerOrdableId: number,
    enablePromotion: LoyaltyTierDocument | CouponDocument,
  ): Promise<PromotionOrdableInfo> {
    const ordableInfo =
      enablePromotion?.ordableStores?.[store._id.toHexString()];
    if (!ordableInfo || !ordableInfo.ordableId) return ordableInfo;

    const shouldDeleteTier =
      ordableInfo.ordableCustomerIds.length === 1 &&
      ordableInfo.ordableCustomerIds[0] === customerOrdableId;

    if (shouldDeleteTier) {
      return await this.deleteOrdablePromotion(store, ordableInfo?.ordableId);
    }

    if (
      !customerOrdableId ||
      !ordableInfo.ordableCustomerIds.includes(customerOrdableId)
    )
      return ordableInfo;

    const updateOrdablePromotionDto = {
      id: ordableInfo.ordableId,
      targeted_user: ordableInfo.ordableCustomerIds.filter(
        (id) => id !== customerOrdableId,
      ),
    };

    const response: OrdablePromotionResponseDto =
      await this.ordableHttpRequestsService.createOrdablePatchRequest(
        `${store.apiBaseUrl}${this.PROMOTIONS_URI}${ordableInfo.ordableId}`,
        store.apiKey,
        updateOrdablePromotionDto,
      );

    await this.ordableHttpRequestsService.logOrdableResponse(
      `[Ordable] Remove Customer ${customerOrdableId} From Promotion`,
      updateOrdablePromotionDto,
      response,
    );

    if (!response.success) return ordableInfo;
    return {
      ...ordableInfo,
      ordableCustomerIds: updateOrdablePromotionDto.targeted_user,
    };
  }

  async deleteOrdablePromotion(
    store: StoreDocument,
    promotionOrdableId: number,
  ): Promise<PromotionOrdableInfo> {
    if (!promotionOrdableId)
      return { ordableId: undefined, ordableCustomerIds: [] };

    const URL = `${store.apiBaseUrl}${this.PROMOTIONS_URI}${promotionOrdableId}/`;
    const response: { success: boolean } =
      await this.ordableHttpRequestsService.createOrdableDeleteRequest(
        URL,
        store.apiKey,
      );

    await this.ordableHttpRequestsService.logOrdableResponse(
      `[Ordable] Delete Promotion ${promotionOrdableId}`,
      { URL },
      response,
    );

    return { ordableId: undefined, ordableCustomerIds: [] };
  }

  async addCustomerToPromotion(
    store: StoreDocument,
    customerOrdableId: number,
    enablePromotion: LoyaltyTierDocument | CouponDocument,
  ): Promise<PromotionOrdableInfo> {
    const ordableInfo = await this.findOrCreatePromotionOrdableInfo(
      store,
      customerOrdableId,
      enablePromotion,
    );
    if (!ordableInfo)
      throw new InternalServerErrorException(
        `Ordable Promotion not found for tier/coupon ${enablePromotion._id.toHexString()}`,
      );

    if (
      !customerOrdableId ||
      ordableInfo.ordableCustomerIds.includes(customerOrdableId)
    )
      return ordableInfo;

    const updateOrdablePromotionDto = {
      id: ordableInfo.ordableId,
      targeted_user: [...ordableInfo.ordableCustomerIds, customerOrdableId],
    };

    const response: OrdablePromotionResponseDto =
      await this.ordableHttpRequestsService.createOrdablePatchRequest(
        `${store.apiBaseUrl}${this.PROMOTIONS_URI}${ordableInfo.ordableId}/`,
        store.apiKey,
        updateOrdablePromotionDto,
      );

    await this.ordableHttpRequestsService.logOrdableResponse(
      `[Ordable] Add Customer ${customerOrdableId} To Promotion`,
      updateOrdablePromotionDto,
      response,
    );

    if (!response.success) return ordableInfo;

    return {
      ...ordableInfo,
      ordableCustomerIds: updateOrdablePromotionDto.targeted_user,
    };
  }

  private async findOrCreatePromotionOrdableInfo(
    store: StoreDocument,
    customerOrdableId: number,
    enablePromotion: LoyaltyTierDocument | CouponDocument,
  ): Promise<PromotionOrdableInfo> {
    if (enablePromotion?.ordableStores?.[store._id.toHexString()]?.ordableId) {
      return enablePromotion.ordableStores[store._id.toHexString()];
    }
    return await this.createOrdablePromotion(
      store,
      [customerOrdableId],
      enablePromotion,
    );
  }

  async createOrdablePromotion(
    store: StoreDocument,
    ordableCustomerIds: number[],
    enablePromotion:
      | LoyaltyTierDocument
      | CouponDocument
      | EarnedReward
      | CustomerEarnedBenefit,
  ): Promise<PromotionOrdableInfo> {
    if (!ordableCustomerIds || ordableCustomerIds.length === 0) return;

    const ordablePromotionDto = await this.getOrdablePromotionDto(
      enablePromotion,
      store,
      { targeted_user: ordableCustomerIds },
    );
    if (!ordablePromotionDto) return;

    const response: OrdablePromotionResponseDto =
      await this.ordableHttpRequestsService.createOrdablePostRequest(
        `${store.apiBaseUrl}${this.PROMOTIONS_URI}`,
        store.apiKey,
        ordablePromotionDto,
      );

    await this.ordableHttpRequestsService.logOrdableResponse(
      '[Ordable] Create Promotion',
      ordablePromotionDto,
      response,
    );

    if (!response.success) return;

    const promotionOrdableInfo: PromotionOrdableInfo = {
      ordableId: response.data.id,
      ordableCustomerIds,
    };
    return promotionOrdableInfo;
  }
}
