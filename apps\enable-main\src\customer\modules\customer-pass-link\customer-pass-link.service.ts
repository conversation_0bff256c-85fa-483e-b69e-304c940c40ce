import { BrandDocument, CustomerDocument, WalletApp } from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ShortenUrlServiceInterface } from '../../../integration/shorten-url/services/shorten-url.service.interface';
import { GooglePassesServiceInterface } from '../../../passes/services/google-passes/google-passes.service.interface';
import { CustomerPassLinkServiceInterface } from './customer-pass-link-service.interface';

@Injectable()
export class CustomerPassLinkService
  implements CustomerPassLinkServiceInterface
{
  constructor(
    private readonly configService: ConfigService,
    @Inject(GooglePassesServiceInterface)
    private readonly googlePassesService: GooglePassesServiceInterface,
    @Inject(ShortenUrlServiceInterface)
    private readonly shortenUrlService: ShortenUrlServiceInterface,
  ) {}

  public async getWalletPassLink(
    customer: CustomerDocument,
    brand: BrandDocument,
    walletApp: WalletApp,
    shortened: boolean = false,
  ): Promise<string> {
    const brandId = brand._id.toHexString();
    const customerId = customer._id.toHexString();

    let url = '';
    if (walletApp == WalletApp.GOOGLE_WALLET) {
      url = await this.googlePassesService.generatePassWithoutAuth({
        brandId,
        customerId: customer._id,
        forceUpdate: false,
      });
    } else if (walletApp === WalletApp.ENABLE_WALLET) {
      const defaultUrl = this.configService.get<string>(
        'DEFAULT_ENABLE_WALLET_LINK',
      );
      const baseUrl = brand.loyaltyProgramConfig?.pwaBaseUrl ?? defaultUrl;
      url = `${baseUrl}?brandId=${brandId}&customerId=${customerId}`;
    } else {
      const baseUrl = this.configService.get<string>('HOST_URL');
      url = `${
        walletApp == WalletApp.WALLET_PASSES ? 'https://walletpass.io?u=' : ''
      }${baseUrl}/v1/passes/${brandId}/${customerId}/pass.pkpass`;
    }
    if (shortened)
      return this.shortenUrlService.shortenUrl({
        url,
        canExpire: false,
        partialCode: 'loyalty-card',
      });
    return url;
  }
}
