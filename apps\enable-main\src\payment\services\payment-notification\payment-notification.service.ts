import {
  CollectionName,
  GenericTriggerModel,
  GiftRecipientUser,
  Language,
  LanguageCode,
  OrderDeliveryType,
  OrderDocument,
  OrderSource,
  PaymentDocument,
  TriggerAction,
  TriggerModule,
} from '@app/shared-stuff';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { BranchService } from '../../../branch/services/branch/branch.service';
import { CustomerReadServiceInterface } from '../../../customer/modules/customer-read/customer-read.service.interface';
import { TriggerService } from '../../../notification/services/trigger/trigger.service';
import { OrderNotificationService } from '../../../order/services/order-notification/order-notification.service';
import { capitalizeFirstLetter } from '../../../shared/services/helper/helper.service';

@Injectable()
export class PaymentNotificationService {
  constructor(
    private configService: ConfigService,
    @Inject(forwardRef(() => OrderNotificationService))
    private orderNotificationService: OrderNotificationService,
    @InjectModel(CollectionName.ORDER) private orderModel: Model<OrderDocument>,
    private triggerService: TriggerService,
    @Inject(CustomerReadServiceInterface)
    private readonly customerService: CustomerReadServiceInterface,
    private branchService: BranchService,
  ) {}

  async onPaymentSuccess(payment: PaymentDocument) {
    if (
      ![
        OrderSource.WEBSTORE,
        OrderSource.MOBILE_APP,
        OrderSource.DINE_IN,
      ].includes(payment.source)
    ) {
      const genericTriggerModel =
        await this.populateGenericTriggerModel(payment);
      if (payment.order) {
        const order = await this.orderModel.findOne({ _id: payment.order });
        if (order.delivery_type === OrderDeliveryType.urgent)
          await this.orderNotificationService.fireGiftRecipientLocationTrigger(
            order,
          );
      }
      await this.triggerService.fireTrigger(
        genericTriggerModel,
        TriggerAction.ON_PAYMENT_SUCCESSFUL,
      );
    }
  }

  async onPaymentUnSuccessful(payment: PaymentDocument) {
    if (
      ![
        OrderSource.WEBSTORE,
        OrderSource.MOBILE_APP,
        OrderSource.DINE_IN,
      ].includes(payment.source)
    ) {
      const genericTriggerModel =
        await this.populateGenericTriggerModel(payment);
      await this.triggerService.fireTrigger(
        genericTriggerModel,
        TriggerAction.ON_PAYMENT_UN_SUCCESSFULLY,
      );
    }
  }

  async onPaymentUnpaid(payment: PaymentDocument) {}

  async onSendPaymentLink(payment: PaymentDocument) {
    if (
      ![
        OrderSource.WEBSTORE,
        OrderSource.MOBILE_APP,
        OrderSource.DINE_IN,
      ].includes(payment.source)
    ) {
      const genericTriggerModel =
        await this.populateGenericTriggerModel(payment);
      await this.triggerService.fireTrigger(
        genericTriggerModel,
        TriggerAction.ON_SEND_PAYMENT_LINK,
      );
    }
  }

  async populateGenericTriggerModel(
    payment: PaymentDocument,
  ): Promise<GenericTriggerModel> {
    const replacements = await this.getAllPaymentReplacements(payment);
    let giftRecipientUser: GiftRecipientUser;
    let isGiftOrder = undefined;
    const customer = await this.customerService.findOne(
      payment.customer._id.toHexString(),
    );

    let order: OrderDocument;
    if (payment.order) {
      order = await this.orderModel.findOne({ _id: payment.order });
      isGiftOrder = order.is_gift;
      giftRecipientUser = {
        recipientName: order?.recipient_name,
        recipientCountryCode: order?.recipient_country_code,
        recipientPhone: order?.recipient_phone,
      };
    }

    return {
      companyId: payment.company?._id
        ? payment.company._id.toString()
        : payment.company.toString(),
      branchId: payment.branch?._id?.toHexString(),
      brandId: payment?.brand?._id.toHexString(),
      customerId: new Types.ObjectId(payment?.customer),
      countryCode: payment?.country_code,
      createdBy: payment?.createdBy,
      giftRecipientUser: giftRecipientUser,
      isGift: isGiftOrder,
      senderId: payment.brand ? payment.brand.senderId : undefined,
      emailSenderId: payment.brand ? payment.brand.emailSenderId : undefined,
      triggerModule: TriggerModule.PAYMENT,
      replacements: replacements,
      language:
        payment.language === Language.english
          ? LanguageCode.en
          : LanguageCode.ar,
      context: {
        customer: {
          ...customer.toJSON(),
          _id: customer._id,
        },
      },
    };
  }

  private async getAllPaymentReplacements(
    payment: PaymentDocument,
    order: OrderDocument = undefined,
  ) {
    return {
      paymentAmount: payment?.amount.toString() ?? '',
      paymentLinkId: payment?.code ?? '',
      paymentSource: payment?.source ?? '',
      paymentLink: payment?.code
        ? this.configService.get('PAY_PAGE') + '/' + payment?.code
        : '',
      brandName: payment?.brand?.name ?? '',
      customerName: capitalizeFirstLetter(payment?.customer_name) ?? '',
      phoneNumber: payment?.customer_phone ?? '',
      companyName: capitalizeFirstLetter(payment.company_name) ?? '',
      userName: payment?.createdBy?.name ?? '',
      paymentMethod: payment?.payment_method ?? '',
      paymentShortenURL: payment?.shortenUrl ?? '',
      orderLocationShortenURL:
        order && order.shortenUrl ? order.shortenUrl : '',
      orderLocationLink: order
        ? this.configService.get('LOCATION_PAGE') + '/' + order.code
        : '',
    };
  }
}
