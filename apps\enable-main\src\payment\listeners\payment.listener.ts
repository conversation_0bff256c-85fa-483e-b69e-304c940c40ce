import {
  LogError,
  OrderDocument,
  OrderPaymentMethod,
  OrderPaymentStatus,
  PaymentStatusEnum,
} from '@app/shared-stuff';
import { OnEvent } from '@nestjs/event-emitter';
import { Types } from 'mongoose';
import { Brand } from '@app/shared-stuff/models/brand.model';
import { PaymentService } from '../services/payment/payment.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class PaymentListener {
  constructor(private paymentService: PaymentService) {}

  @OnEvent('on-Brand-Update')
  @LogError()
  async onBrandUpdate(updatedBrand: Brand, brandId: Types.ObjectId) {
    await this.paymentService.syncPaymentBrands(updatedBrand, brandId);
  }

  @OnEvent('order.cancelled')
  @LogError()
  async onOrderCancelled(order: OrderDocument) {
    if (order.payment_method == OrderPaymentMethod.online) {
      const payment = await this.paymentService.get_details(order.payment_code);
      if (
        payment &&
        payment.status != PaymentStatusEnum.TRANSACTION_COMPLETED
      ) {
        if (order.payment_status == OrderPaymentStatus.COMPLETED)
          payment.status = PaymentStatusEnum.TRANSACTION_COMPLETED;

        if (order.payment_status == OrderPaymentStatus.PENDING)
          payment.status = PaymentStatusEnum.EXPIRED;

        await payment.save();
      }
    }
  }
}
