import { Inject, Injectable } from '@nestjs/common';

import {
  GenericTriggerModel,
  TemplateTo,
  TriggerUserDto,
} from '@app/shared-stuff';
import { HelperService } from '../../../shared/services/helper/helper.service';
import { CustomerReadServiceInterface } from '../customer-read/customer-read.service.interface';
import { CustomerUserServiceInterface } from './customer-user.service.interface';
import { Types } from 'mongoose';

@Injectable()
export class CustomerUserService implements CustomerUserServiceInterface {
  constructor(
    private helperService: HelperService,
    @Inject(CustomerReadServiceInterface)
    private customerReadService: CustomerReadServiceInterface,
  ) {}

  public async getCustomerUser(
    genericTriggerModel: GenericTriggerModel,
    users: TriggerUserDto[],
  ): Promise<TriggerUserDto[]> {
    if (!genericTriggerModel['customerId']) return users;
    const customer = await this.customerReadService.findOne(
      genericTriggerModel['customerId']['_id']
        ? genericTriggerModel['customerId']['_id'].toHexString()
        : genericTriggerModel['customerId'].toHexString(),
      new Types.ObjectId(genericTriggerModel.companyId),
    );
    return users.concat(
      this.helperService.mapToTriggerUser(
        customer,
        genericTriggerModel,
        TemplateTo.CUSTOMER,
      ),
    );
  }
}
