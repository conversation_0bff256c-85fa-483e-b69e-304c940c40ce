// EBL-3715 [Loyalty Program] ON_LOYALTY_ORDER Notification Trigger
// Add ON_LOYALTY_ORDER_LOYALTY_MEMBERS and ON_LOYALTY_ORDER_NON_LOYALTY_MEMBERS
db.triggers.insertMany([
  {
    name: '[ORDER] ON_LOYALTY_ORDER_LOYALTY_MEMBERS',
    client: 'ENABLE_MAIN',
    action: 'ON_LOYALTY_ORDER_LOYALTY_MEMBERS',
    module: 'ORDER',
    replacement: [
      'firstName',
      'fullName',
      'loyaltyPointBalance',
      'loyaltyTier',
      'remainingOrdersCurrentTier',
      'remainingOrdersUpperTier',
      'upperTierDiscountValue',
      'tierDiscountValue',
      'tierDiscountOrderValueThreshold',
      'loyaltyRegistrationPageLink',
      'walletPassAccessPageLink',
      'walletPassLink',
      'ordableLink',
      'totalAmount',
      'orderCode',
      'orderSource',
      'paymentLinkId',
      'paymentLink',
      'brandName',
      'customerName',
      'phoneNumber',
      'email',
      'companyName',
      'userName',
      'amountWithoutDelivery',
      'paymentMethod',
      'recipientName',
      'deliveryAction',
      'pickupDate',
      'deliveryDate',
      'locationShortenURL',
      'locationLink',
      'orderTrackingShortenURL',
      'orderTrackingLink',
      'paymentShortenURL',
      'orderEarnedLoyaltyPoints',
    ],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    name: '[ORDER] ON_LOYALTY_ORDER_NON_LOYALTY_MEMBERS',
    client: 'ENABLE_MAIN',
    action: 'ON_LOYALTY_ORDER_NON_LOYALTY_MEMBERS',
    module: 'ORDER',
    replacement: [
      'firstName',
      'fullName',
      'loyaltyPointBalance',
      'loyaltyTier',
      'remainingOrdersCurrentTier',
      'remainingOrdersUpperTier',
      'upperTierDiscountValue',
      'tierDiscountValue',
      'tierDiscountOrderValueThreshold',
      'loyaltyRegistrationPageLink',
      'walletPassAccessPageLink',
      'walletPassLink',
      'ordableLink',
      'totalAmount',
      'orderCode',
      'orderSource',
      'paymentLinkId',
      'paymentLink',
      'brandName',
      'customerName',
      'phoneNumber',
      'email',
      'companyName',
      'userName',
      'amountWithoutDelivery',
      'paymentMethod',
      'recipientName',
      'deliveryAction',
      'pickupDate',
      'deliveryDate',
      'locationShortenURL',
      'locationLink',
      'orderTrackingShortenURL',
      'orderTrackingLink',
      'paymentShortenURL',
      'orderEarnedLoyaltyPoints',
    ],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
]);
