import {
  Adler<PERSON>ser,
  CompanyDocument,
  MenuGroupToCreate,
  OrderDeliveryAction,
  OrderDocument,
  OrderItemGroup,
  OrderLogActionEnum,
  OrderPaymentMethod,
  OrderPaymentStatus,
  OrderSource,
  SavedLocationAddressType,
  SavedLocationType,
} from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { BranchService } from '../../../../branch/services/branch/branch.service';
import { CompanyService } from '../../../../company/services/company/company.service';
import { OrderLogServiceInterface } from '../../../../order/services/interfaces/order-log.service.interface';
import { MenuCategoryService } from '../../../../restaurant/services/menu-category/menu-category.service';
import { MenuItemService } from '../../../../restaurant/services/menu-item/menu-item.service';
import { MenuService } from '../../../../restaurant/services/menu/menu.service';
import { HelperService } from '../../../../shared/services/helper/helper.service';
import { UserService } from '../../../../user/services/user/user.service';
import {
  AdlerBranchToSync,
  AdlerMenuToSync,
  AdlerOrderItem,
  AdlerOrderToCreate,
} from '../dto/adler.dto';

@Injectable()
export class AdlerService {
  constructor(
    private configService: ConfigService,
    private httpService: HttpService,
    private companyService: CompanyService,
    private userService: UserService,
    private branchService: BranchService,
    private menuService: MenuService,
    private menuItemService: MenuItemService,
    private menuCategoryService: MenuCategoryService,
    @Inject('OrderLogServiceInterface')
    private orderLogService: OrderLogServiceInterface,
    private helperService: HelperService,
  ) {}

  async syncBranchesWithAdlerOutlet(adlerBranchToSync: AdlerBranchToSync) {
    const company = await this.companyService.get_details(
      adlerBranchToSync.companyId,
    );
    if (!company || !company.adlerSecretAccessKey || !company.adlerCompanyId) {
      return;
    }
    const companySuperAdmins = await this.userService.admin_index({
      company: company._id,
      role_name: 'Super Admin',
    } as any);
    const companyUsersIds = companySuperAdmins.map(
      (x) => new Types.ObjectId(x._id),
    );

    let numberOfCreatedBranches = 0;
    const outlets = (await this.getOutletList(company)) as any;
    for (let i = 0; i < outlets.length; i++) {
      const branchExist = await this.branchService.get_details(
        outlets[i]['outletid'].toString(),
      );

      if (!branchExist) {
        await this.branchService.create({
          name: outlets[i]['outletname'],
          phone: outlets[i]['phonenumber'],
          email: outlets[i]['emailid'],
          users: companyUsersIds as any,
          company: company._id,
          brands: [],
          acronym: company.acronym + outlets[i]['outletname'],
          internalName: outlets[i]['outletname'],
          company_name: company.name,
          deliveryAreas: [],
          adlerOutletId: outlets[i]['outletid'],
          image: undefined,
          senderId: '',
          callCenterConfig: {
            currentlyDeliveringIn: 0,
            messages: [],
            deliveryFee: 0,
            deliveryTiming: '0',
            freeDeliveryAbove: 200,
            minimumForDelivery: 0,
            openTiming: '',
            allowCallerToReceivePromotion: false,
            webStoreLink: '',
          },
          radius: 0,
          location: {
            nickname: outlets[i]['outletname'],
            area: 'Doha',
            city: 'Doha',
            buildingName: 'Test',
            buildingNumber: 0,
            zoneNumber: 0,
            streetNumber: 0,
            latitude: 0,
            pinLink: 'https://maps.google.com?q=0,0',
            longitude: 0,
            customerId: undefined,
            type: SavedLocationType.PIN_LOCATION,
            additionalInfo: '',
            addressType: SavedLocationAddressType.APARTMENT,
            unitNumber: 0,
            country: 'Qatar',
            floorNumber: 0,
            nearestLandmark: '',
            streetName: '',
          },
          externalLinks: {
            feedbackLink: '',
            webStoreLink: '',
          },
          mrDeliveryVendorId: '',
          isFollowingCompanyDeliveryConfiguration: true,
          deliveryConfiguration: null,
        });
        numberOfCreatedBranches++;
      }
    }

    return { numberOfCreatedBranches };
  }

  async syncMenuItemsWithAdler(adlerMenuToSync: AdlerMenuToSync) {
    const company = await this.companyService.get_details(
      adlerMenuToSync.companyId,
    );

    if (!company || !company.adlerSecretAccessKey || !company.adlerCompanyId) {
      return;
    }
    const adlerMenuItems = (await this.getMenuList(company)) as any;
    const adlerSubMenuItems = (await this.getSubMenuList(company)) as any;
    const defaultMenuGroup: MenuGroupToCreate = {
      nameAr: 'Adler Addons',
      nameEn: 'Adler Addons',
      createdBy: AdlerUser,
      deliverectId: '',
      descriptionAr: 'Adler Addons',
      descriptionEn: 'Adler Addons',
      index: '0',
      max: 99999,
      min: 1,
      multiply: 9999,
      multiSelectMax: 9999,
      plu: 'Adler-Addons',
      reference: '',
      totalPrice: 0,
      items: adlerSubMenuItems.map((x, index) => ({
        name: x.description,
        price: 0,
        index: index,
        plu: x.uniqueid,
        deliverectRawId: '',
      })),
      integrationInfo: {
        ordableInfo: {},
      },
    };

    const enableMenuCheck = await this.menuService.index({
      company: company._id,
      search_key: 'Adler Menu',
      search_type: 'name',
    } as any);

    if (enableMenuCheck && enableMenuCheck[0]) {
      const menu = enableMenuCheck[0];
      await this.menuItemService.deleteItemInsideMenu(menu);
      await this.menuCategoryService.deleteAllCategoryInsideMenu(menu);
      await this.menuService.deleteMenu(menu);
    }

    const branches = (
      await this.branchService.index({ company: company._id } as any)
    ).map((x) => x._id);
    const enableMenu = await this.menuService.create({
      name: 'Adler Menu',
      internalName: 'Adler Menu',
      availabilities: [],
      branches: branches,
      branchesName: '',
      company: company._id,
      companyName: company.name,
      brand: undefined,
      currentUser: AdlerUser,
      deliverectId: '',
      deliverectRaw: {},
      description: 'Adler Menu',
      reference: 'AdlerMenu',
      pdfURL: '',
      type: 'pickupAndDelivery',
    });

    for (let i = 0; i < adlerMenuItems.length; i++) {
      const currentMenuItem = adlerMenuItems[i];
      await this.menuItemService.create({
        nameAr: '',
        nameEn: currentMenuItem['description'],
        descriptionAr: '',
        descriptionEn: currentMenuItem['description'],
        price: currentMenuItem['salesprice']
          ? parseFloat(currentMenuItem['salesprice'])
          : 0,
        sortOrder: 1,
        deliveryTax: 0,
        takeawayTax: 0,
        max: 999999,
        min: 1,
        available: true,
        calories: 0,
        showInPos: true,
        showOnWeb: true,
        multiSelectMax: 1,
        multiply: 1,
        code: Math.random().toString(),
        adlerId: currentMenuItem['menuid'],
        type: 'regular',
        createdBy: { name: 'Adler POS' },
        menu: enableMenu._id,
        menuCategory: null,
        tags: ['Adler Item'],
        images: [],
        subItems: [],
        menuGroups: [defaultMenuGroup],
        reference: currentMenuItem['menuid'],
        plu: currentMenuItem['menuid'],
        deliverectRaw: undefined,
        deliverectId: undefined,
        company: company._id,
        externalImage: undefined,
        forGroupingOnly: false,
        integrationInfo: {
          ordableInfo: {},
        },
        availabilities: [],
        isScheduledAvailabilityActive: false,
        externalBrandId: '',
      });
    }
  }

  // Adler Calls
  private async getOutletList(company: CompanyDocument) {
    return new Promise((resolve) => {
      const URL =
        this.configService.get('ADLER_BASE_URL') + 'POSAPI/Restaurant/Outlet';
      this.httpService
        .post(
          URL,
          { outletname: '' },
          {
            headers: {
              SECRET_ACCESS_KEY: company.adlerSecretAccessKey,
              'Content-Type': 'application/json',
            },
          },
        )
        .subscribe(
          (data) => {
            if (data.data['status'] == 'true' || data.data['status'] == '1') {
              resolve(data.data['result']);
            } else {
              resolve([]);
            }
          },
          () => {
            resolve([]);
          },
        );
    });
  }

  private async getMenuList(company: CompanyDocument) {
    return new Promise((resolve) => {
      const URL =
        this.configService.get('ADLER_BASE_URL') + 'POSAPI/Restaurant/Menu';
      this.httpService
        .post(
          URL,
          { partnumber: '', description: '' },
          {
            headers: {
              SECRET_ACCESS_KEY: company.adlerSecretAccessKey,
              'Content-Type': 'application/json',
            },
          },
        )
        .subscribe(
          (data) => {
            if (data.data['status'] == 'true' || data.data['status'] == '1') {
              resolve(data.data['result']);
            } else {
              resolve([]);
            }
          },
          () => {
            resolve([]);
          },
        );
    });
  }

  private async getSubMenuList(company: CompanyDocument) {
    return new Promise((resolve) => {
      const URL =
        this.configService.get('ADLER_BASE_URL') + 'POSAPI/Restaurant/Submenu';
      this.httpService
        .post(
          URL,
          { description: '' },
          {
            headers: {
              SECRET_ACCESS_KEY: company.adlerSecretAccessKey,
              'Content-Type': 'application/json',
            },
          },
        )
        .subscribe(
          (data) => {
            if (data.data['status'] == 'true' || data.data['status'] == '1') {
              resolve(data.data['result']);
            } else {
              resolve([]);
            }
          },
          () => {
            resolve([]);
          },
        );
    });
  }

  async createAdlerOrder(order: OrderDocument) {
    const enableCompanyId = order['company']['_id']
      ? order['company']['_id']
      : order['company'];
    const company = await this.companyService.get_details(enableCompanyId);

    if (!company || !company['adlerCompanyId'] || !company['isAdlerCompany']) {
      //The company does not exist on adler system
      return;
    }
    // let notes = '';
    // if (order.order_remarks)
    //   notes = await this.handleAdlerOrderNotes(order.order_remarks);

    const adlerOrderToCreate: AdlerOrderToCreate = {
      companyid: company.adlerCompanyId,
      customerid: '254483',
      customermobilenumber: order.country_code + order.customer_phone,
      customername:
        order.payment_method == OrderPaymentMethod.online
          ? order.payment_status != OrderPaymentStatus.COMPLETED
            ? 'Enable Pending'
            : 'Enable Online'
          : order.customer_name,
      employeeid: '736',
      userid: '165',
      notes: order.order_remarks,
      numberofpersons: '0',
      orderdateandtime: moment().format('DD-MMM-YYYY HH:mm'),
      ordertype:
        order.delivery_action == OrderDeliveryAction.DINE_IN
          ? '1'
          : order.delivery_action == OrderDeliveryAction.IN_STORE_PICKUP
            ? '2'
            : order.delivery_action == OrderDeliveryAction.DELIVERY_LOCATION ||
                order.delivery_action == OrderDeliveryAction.SEND_SMS
              ? '4'
              : '1',
      outletid: order.branch['adlerOutletId'],
      referencenumber: order.code,
      tableno: order.tableCode ? '1' : '0',
      restaurantorderdtlsave: [],
    };

    if (order.source == OrderSource.DELIVERECT) {
      if (order.deliverectPosCourier == 'gorafeeq') {
        adlerOrderToCreate.ordertype = '2';
      }
      if (order.deliverectChannelName == 'TALABAT') {
        adlerOrderToCreate['customermobilenumber'] = '00000001';
        adlerOrderToCreate['customername'] = 'Talabat4170';
        adlerOrderToCreate['referencenumber'] = '892253334';
      } else if (order.deliverectChannelName == 'RAFEEQ') {
        adlerOrderToCreate['customermobilenumber'] = '00000003';
        adlerOrderToCreate['customername'] =
          'Rafeeq' + order.deliverectPosOrderDisplayId?.slice(-3);
        adlerOrderToCreate['referencenumber'] = '39492400';
      }
      adlerOrderToCreate['talabatreferencenumber'] =
        order.deliverectPosOrderDisplayId;
    }

    if (order.items) {
      for (let i = 0; i < order.items.length; i++) {
        const currentItem = order.items[i];
        const adlerItem: AdlerOrderItem = {
          menuid: currentItem.adlerId ? currentItem.adlerId : currentItem.plu,
          orderquantity: currentItem.quantity.toString(),
          description: currentItem.name + ' ' + currentItem.description,
          notes: currentItem.special_instructions ?? '',
          unitrate: currentItem.basePrice
            ? currentItem.basePrice.toString()
            : currentItem.price.toString(),
          toppingsrate: '0',
          salesprice: currentItem.totalAmount.toString(),
        };
        // Calculate topping rate
        const [toppingRate, notes] = this.calculateToppingRateAndNotes(
          currentItem.modifierGroups,
        );

        adlerItem.notes += ', ' + notes;
        adlerItem.toppingsrate = toppingRate.toString();

        if (adlerItem.unitrate == '0') {
          adlerItem.unitrate = adlerItem.toppingsrate;
          // adlerItem.toppingsrate = '0';
        }

        if (adlerItem.salesprice == '0') {
          adlerItem.salesprice = (
            parseFloat(adlerItem.unitrate) * parseInt(adlerItem.orderquantity) +
            parseFloat(adlerItem.toppingsrate)
          ).toString();
        }

        //TODO: Check For Duplication
        adlerOrderToCreate.restaurantorderdtlsave.push(adlerItem);
      }
    }

    const adlerResponse = await this.postAdlerOrder(
      adlerOrderToCreate,
      company,
    );

    if (adlerResponse && adlerResponse['status'] != 'false') {
      order.adlerPosOrderId = (adlerResponse as string).trim();
      await order.save();
    }
    await this.orderLogService.saveOrderLog(
      order,
      { requestedObject: adlerOrderToCreate },
      { responseObject: adlerResponse },
      OrderLogActionEnum.ON_ORDER_SENT_TO_ADLER,
      order.createdBy,
    );
  }

  private async postAdlerOrder(
    adlerOrderToCreate: AdlerOrderToCreate,
    company: CompanyDocument,
  ) {
    return new Promise((resolve) => {
      const URL =
        this.configService.get('ADLER_BASE_URL') +
        'POSAPI/Restaurant/OrderSave';
      this.httpService
        .post(URL, adlerOrderToCreate, {
          headers: {
            SECRET_ACCESS_KEY: company.adlerSecretAccessKey,
            'Content-Type': 'application/json',
          },
        })
        .subscribe(
          (data) => {
            if (data.data['status'] == '1' || data.data['status'] == 'true') {
              resolve(data.data['result']);
            } else {
              resolve(data.data);
            }
          },
          (err) => {
            resolve(err);
          },
        );
    });
  }

  private calculateToppingRateAndNotes(modifierGroups: OrderItemGroup[]) {
    let toppingRate = 0,
      notes = '';
    if (modifierGroups && modifierGroups.length) {
      for (let i = 0; i < modifierGroups.length; i++) {
        for (let j = 0; j < modifierGroups[i].modifiers.length; j++) {
          toppingRate += modifierGroups[i].modifiers[j].price;
          notes += modifierGroups[i].modifiers[j].name + ', ';
        }
      }
    }
    return [toppingRate, notes];
  }

  async cancelAdlerOrder(order: OrderDocument) {
    const companyId = order.company['_id']
      ? order.company['_id']
      : order.company;
    const company = await this.companyService.get_details(companyId);

    const response = await this.postCancelAdlerOrder(
      order.adlerPosOrderId,
      company,
    );

    await this.orderLogService.saveOrderLog(
      order,
      { requestedObject: { adlerOrderId: order.adlerPosOrderId } },
      { responseObject: response },
      OrderLogActionEnum.ON_ADLER_ORDER_CANCELED,
      order.createdBy,
    );
  }

  private async postCancelAdlerOrder(
    orderReferenceNumber: string,
    company: CompanyDocument,
  ) {
    return new Promise((resolve) => {
      const URL =
        this.configService.get('ADLER_BASE_URL') +
        'POSAPI/Restaurant/OrderCancel';
      this.httpService
        .post(
          URL,
          { refernumber: orderReferenceNumber, orderstatus: 0 },
          {
            headers: {
              SECRET_ACCESS_KEY: company.adlerSecretAccessKey,
              'Content-Type': 'application/json',
            },
          },
        )
        .subscribe(
          (data) => {
            if (data.data['status'] == 'true' || data.data['status'] == '1') {
              resolve(data.data['result']);
            } else {
              resolve([]);
            }
          },
          () => {
            resolve([]);
          },
        );
    });
  }
}
