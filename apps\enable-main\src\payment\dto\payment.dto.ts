import {
  DataIndex,
  Month,
  PaymentMethod,
  PaymentMethodUsed,
  PaymentStatusEnum,
  YesOrNo,
} from '@app/shared-stuff';
import { ApiProperty } from '@nestjs/swagger';
import { PaymentIndexGatewayType } from '../enums/payment-index-gateway-type.enum';
import { PaymentIndexSearchType } from '../enums/payment-index-search-type.enum';
import { PaymentIndexSortType } from '../enums/payment-index-sort-type.enum';
import { PaymentIndexType } from '../enums/payment-index-type.enum';

export class PaymentIndex extends DataIndex {
  @ApiProperty({
    type: String,
    required: false,
  })
  company: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  companyIds: string;

  @ApiProperty({
    required: false,
    type: String,
    enum: PaymentIndexSearchType,
  })
  search_type: PaymentIndexSearchType;

  @ApiProperty({
    required: false,
    type: String,
    enum: PaymentIndexSortType,
  })
  sort_type: PaymentIndexSortType;

  @ApiProperty({
    required: false,
    type: String,
    enum: Month,
  })
  month: Month;

  @ApiProperty({
    type: String,
    required: false,
    description: 'separated by comma ,',
  })
  status: string;

  @ApiProperty({
    type: [String],
    required: false,
  })
  branches: string[];

  @ApiProperty({
    type: String,
    required: false,
  })
  branch: string;

  @ApiProperty({
    type: String,
    enum: PaymentIndexType,
    required: false,
  })
  type: PaymentIndexType;

  @ApiProperty({
    type: String,
    enum: PaymentMethod,
    required: false,
  })
  payment_method: PaymentMethod;

  @ApiProperty({
    type: String,
    enum: PaymentMethodUsed,
    required: false,
  })
  payment_method_used: PaymentMethodUsed;

  @ApiProperty({
    type: String,
    enum: PaymentStatusEnum,
    required: false,
  })
  payment_status: PaymentStatusEnum;

  @ApiProperty({
    type: String,
    required: false,
  })
  payment_id: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  brandId: string;

  @ApiProperty({
    type: String,
    format: 'YYYY-MM-DD',
    required: false,
  })
  createdDateFrom: string;

  @ApiProperty({
    type: String,
    format: 'YYYY-MM-DD',
    required: false,
  })
  createdDateTo: string;

  @ApiProperty({
    type: String,
    format: 'YYYY-MM-DD',
    required: false,
  })
  transactionDateFrom: string;

  @ApiProperty({
    type: String,
    format: 'YYYY-MM-DD',
    required: false,
  })
  transactionDateTo: string;

  @ApiProperty({
    type: String,
    enum: YesOrNo,
    required: false,
  })
  testing: YesOrNo;

  @ApiProperty({
    type: String,
    enum: PaymentIndexGatewayType,
    required: false,
  })
  gatewayType: PaymentIndexGatewayType;

  @ApiProperty({
    type: String,
    enum: YesOrNo,
    required: false,
  })
  isRefunded: YesOrNo;
}
