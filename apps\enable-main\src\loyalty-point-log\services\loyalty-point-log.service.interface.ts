import {
  LoyaltyPointLog,
  LoyaltyPointLogDocument,
  TransformedLoyaltyPointLog,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface LoyaltyPointLogServiceInterface {
  create(
    log: Omit<LoyaltyPointLog, 'createdAt' | 'updatedAt'>,
  ): Promise<LoyaltyPointLogDocument>;
  getLogsForCustomer(
    customerId: Types.ObjectId,
  ): Promise<TransformedLoyaltyPointLog[]>;

  getWasOrderCompleted(orderId: Types.ObjectId): Promise<boolean>;
}

export const LoyaltyPointLogServiceInterface = Symbol(
  'LoyaltyPointLogServiceInterface',
);
