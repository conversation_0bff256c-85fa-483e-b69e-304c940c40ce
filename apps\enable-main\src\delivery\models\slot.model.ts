import { CollectionName } from '@app/shared-stuff';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

export type SlotDocument = HydratedDocument<Slot>;
@Schema({ timestamps: true })
export class Slot {
  @Prop({
    required: true,
    type: String,
  })
  from: string;

  @Prop({
    required: true,
    type: String,
  })
  to: string;

  @Prop({
    type: Number,
    default: 1,
  })
  maximum_number_of_orders: number;

  @Prop({
    type: Number,
    default: 1,
  })
  maximum_number_of_urgent_orders: number;

  @Prop({
    type: String,
    enum: ['ebutler', 'company'],
    default: 'ebutler',
  })
  owner: string;

  @Prop({
    type: [String],
    required: false,
  })
  days: string[];

  @Prop({
    type: Types.ObjectId,
    ref: CollectionName.COMPANY,
  })
  company: Types.ObjectId;

  @Prop({
    type: {},
    default: {},
  })
  orders_per_day: any;

  @Prop({
    type: {},
    default: {},
  })
  urgent_orders_per_day: any;

  @Prop({
    type: {},
    required: false,
  })
  updatedBy: {};

  @Prop({
    type: {},
    required: false,
  })
  createdBy: {};

  @Prop({
    type: {},
    required: false,
  })
  assignedTo: {};

  @Prop({
    type: {},
    required: false,
  })
  deletedBy: {};

  @Prop({
    type: Date,
    required: false,
  })
  deletedAt: Date;
}
const SlotSchema = SchemaFactory.createForClass(Slot);

export { SlotSchema };
