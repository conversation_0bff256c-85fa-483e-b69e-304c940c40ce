import { OrderDocument } from '@app/shared-stuff';
import { ThirdPartyTaskCreationDto } from '../../dto/third-party-task-creation.dto';

export interface ThirdPartiesServiceInterface {
  vehicleTypes: readonly string[];
  defaultVehicleType: this['vehicleTypes'][number];

  createTask(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
  ): Promise<any>;
  applyPostFunction(taskCreationResponse: any, order: OrderDocument);
  //TODO make a generic dto contain parameters with optional taskCreationResponse
}
