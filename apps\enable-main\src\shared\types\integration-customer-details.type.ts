import { IntegrationCoupon } from './integration-coupon.type';
import { IntegrationLoyaltyTier } from './integration-loyalty-tier.type';
import { LoyaltyStatus } from '@app/shared-stuff';
import { IntegrationReward } from './integration-reward.type';

export class IntegrationCustomerDetails {
  customerName: string;
  customerId: string;
  title: string;
  gender: string;
  phoneNumber: string;
  countryCode: string;
  email: string;
  discounts: number[];
  loyaltyBenefits: string[];
  loyaltyTier?: IntegrationLoyaltyTier;
  coupons: IntegrationCoupon[];
  loyaltyPoints: number;
  loyaltyStatus: LoyaltyStatus;
  rewards?: IntegrationReward[];
}
