import {
  GroupItem,
  MenuGroupToCreate,
  MenuTalabatServiceUser,
  MenuWithId,
  TalabatMenuToFetch,
} from '@app/shared-stuff';
import { CompanyDocument } from '@app/shared-stuff/models/company.model';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { Types } from 'mongoose';
import { CompanyService } from '../../../company/services/company/company.service';
import { menuTypeMapping } from '../../../integration/pos/deliverect-channel/constants';
import { MenuService } from '../menu/menu.service';
import { MenuCategoryService } from './../menu-category/menu-category.service';
import { MenuItemService } from './../menu-item/menu-item.service';

@Injectable()
export class MenuTalabatService {
  constructor(
    private menuService: MenuService,
    private menuItemService: MenuItemService,
    private httpService: HttpService,
    private companyService: CompanyService,
    private menuCategoryService: MenuCategoryService,
  ) {}

  async onFetchingTalabatMenu(talabatMenuToFetch: TalabatMenuToFetch) {
    // Steps
    // 1- Extract The Menu Id From the Menu URL
    const talabatMenuId = talabatMenuToFetch.menuUrl.match(/\d+/)[0];
    // 2- Fetching The Talabat Menu Information From the Talabat API
    const talabatMenu = await this.menuFetching(talabatMenuId);
    // 3- Fetching The Company Information
    const company = await this.companyService.get_details(
      talabatMenuToFetch['companyId'],
    );
    // 4- Create the Menu
    const menu = await this.menuService.create({
      companyName: company.name,
      branches: talabatMenuToFetch.branches?.map(
        (branch) => new Types.ObjectId(branch),
      ),
      availabilities: [],
      branchesName: '',
      company: company._id,
      currentUser: MenuTalabatServiceUser,
      brand: {
        name: talabatMenuToFetch.brand.name,
        _id: talabatMenuToFetch.brand._id,
        senderId: undefined,
        emailSenderId: undefined,
        phoneNumber: talabatMenuToFetch.brand.phoneNumber,
        image: talabatMenuToFetch.brand.image,
        preferredLanguage: talabatMenuToFetch.brand.preferredLanguage,
      },
      deliverectId: '',
      name: talabatMenuToFetch.menuName,
      description: talabatMenuToFetch.menuName,
      deliverectRaw: {},
      reference: talabatMenuId,
      internalName: talabatMenuToFetch.menuName,
      type: menuTypeMapping[0],
      pdfURL: '',
    });

    await this.menuItemsCreationLogic(talabatMenu, menu, company);

    return 'DONE';
  }

  private async menuFetching(talabatMenuId: string) {
    return new Promise((resolve, reject) => {
      const menuURL = this.getTalabatMenuURL(talabatMenuId);
      this.httpService.get(menuURL).subscribe(
        (data) => {
          resolve(data.data['result']['menu']['menuSection']);
        },
        (err) => {
          reject(err);
        },
      );
    });
  }

  private async menuItemsCreationLogic(
    talabatMenu: any,
    menu: MenuWithId,
    company: CompanyDocument,
  ) {
    // 5- Loop Over the Menu items
    for (let i = 0; i < talabatMenu.length; i++) {
      const currentCategory = talabatMenu[i];
      // 6- Create the Category
      const category = await this.menuCategoryService.create({
        createdBy: MenuTalabatServiceUser,
        deliverectId: '',
        deliverectRaw: {},
        description: currentCategory['nm'],
        name: currentCategory['nm'],
        sortOrder: 0,
        reference: currentCategory['id'],
        externalImage: '',
        image: undefined,
        menu: menu._id,
        menuName: menu.name,
        plu: currentCategory['nm'],
        integrationInfo: {
          ordableInfo: {},
        },
      });
      // 7- Create menuItem inside the Category
      for (let j = 0; j < currentCategory['itm'].length; j++) {
        const currentMenuItem = currentCategory['itm'][j];

        if (currentMenuItem) {
          // 8- for each menuItem get Choices from the talabat API
          const menuModifierGroups =
            await this.fetchingTalabatMenuItemModifiersGroup(
              menu.reference,
              currentMenuItem['id'],
            );

          // 9- insert the Choices as Modifiers Group

          await this.menuItemService.create({
            nameEn: currentMenuItem['nm'],
            nameAr: currentMenuItem['nm'],
            descriptionAr: currentMenuItem['dsc'],
            descriptionEn: currentMenuItem['dsc'],
            available: true,
            calories: 0,
            price: currentMenuItem['pr'],
            code: currentMenuItem['id'],
            company: company._id,
            createdBy: {},
            deliverectId: '',
            deliverectRaw: {},
            deliveryTax: 0,
            takeawayTax: 0,
            externalImage: currentMenuItem['imgurl'],
            forGroupingOnly: false,
            menuCategory: category._id,
            menu: menu._id,
            showInPos: true,
            showOnWeb: true,
            images: [],
            max: 1,
            min: 1,
            multiSelectMax: 1,
            multiply: 1,
            plu: currentMenuItem['id'],
            reference: currentMenuItem['id'],
            sortOrder: 1,
            subItems: [],
            type: 'regular',
            tags: [],
            menuGroups: menuModifierGroups as any,
            adlerId: '',
            integrationInfo: {
              ordableInfo: {},
            },
            availabilities: [],
            isScheduledAvailabilityActive: false,
            externalBrandId: '',
          });
        }
      }
    }
  }

  private async fetchingTalabatMenuItemModifiersGroup(
    talabatMenuId: string,
    talabatMenuItemId: string,
  ) {
    return new Promise((resolve, reject) => {
      const URL = this.getTalabatChoicesMenuItemURL(
        talabatMenuId,
        talabatMenuItemId,
      );
      this.httpService.get(URL).subscribe(
        (data) => {
          const menuModifierGroups: MenuGroupToCreate[] = [];
          const items = data.data['result']['choiceForItem'][0]
            ? data.data['result']['choiceForItem'][0]['choiceSections']
            : [];
          for (let i = 0; i < items.length; i++) {
            const currentItem = items[i];
            const modifiers: GroupItem[] = [];
            let totalPrice = 0;
            for (let j = 0; j < currentItem['ich'].length; j++) {
              const currentModifier = currentItem['ich'][j];
              modifiers.push({
                name: currentModifier['nm'],
                price: currentModifier['pr'],
                deliverectRawId: '',
                index: j.toString(),
                plu: currentModifier['id'],
                integrationInfo: {
                  ordableInfo: {},
                },
              });
              totalPrice += currentModifier['pr'];
            }
            menuModifierGroups.push({
              nameAr: currentItem['nm'],
              nameEn: currentItem['nm'],
              descriptionEn: currentItem['st'],
              descriptionAr: currentItem['st'],
              totalPrice: totalPrice,
              reference: currentItem['id'],
              createdBy: MenuTalabatServiceUser,
              deliverectId: '',
              index: i.toString(),
              max: currentItem['mxq'],
              min: currentItem['mnq'],
              items: modifiers,
              multiSelectMax: 1,
              multiply: 1,
              plu: currentItem['id'],
              integrationInfo: {
                ordableInfo: {},
              },
            });
          }
          resolve(menuModifierGroups);
        },
        (err) => {
          reject(err);
        },
      );
    });
  }

  private getTalabatMenuURL(menuId: string) {
    return `https://www.talabat.com/nextMenuApi/v2/branches/${menuId}/menu`;
  }
  private getTalabatChoicesMenuItemURL(menuId: string, menuItemId: string) {
    return `https://www.talabat.com/nextMenuApi/v2/branches/${menuId}/menu/${menuItemId}/choices`;
  }
}
