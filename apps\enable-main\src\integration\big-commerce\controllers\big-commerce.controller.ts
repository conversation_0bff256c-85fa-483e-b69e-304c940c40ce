import { Controller, HttpCode, HttpStatus, SetMetadata } from '@nestjs/common';
import {
  Body,
  Inject,
  Post,
  Req,
  Res,
  UseInterceptors,
} from '@nestjs/common/decorators';
import { Request, Response } from 'express';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { BigCommerceServiceInterface } from '../services/big-commerce.service.interface';
import { TransformInterceptor } from '@app/shared-stuff';
import { IntegrationLogRepositoryInterface } from '../../integration-log/repositories/interfaces/integration-log.repository.interface';

@Controller('bigcommerce')
@UseInterceptors(TransformInterceptor)
@ApiTags('BigCommerce')
@SetMetadata('module', 'BigCommerce')
export class BigCommerceController {
  constructor(
    @Inject(BigCommerceServiceInterface)
    private readonly bigCommerceService: BigCommerceServiceInterface,
    @Inject('IntegrationLogRepositoryInterface')
    private readonly integrationLogRepository: IntegrationLogRepositoryInterface,
  ) {}

  @Post('webhook/createOrder')
  @ApiOkResponse()
  @HttpCode(HttpStatus.OK)
  @SetMetadata('action', 'create-bigCommerce-order')
  async createOrderWebhook(
    @Req() req: Request,
    @Body() rawData: any,
    @Res() res: Response,
  ) {
    try {
      const response = await this.bigCommerceService.createOrder(
        rawData,
        req['company_id'],
        req['brand'] ? req['brand']['_id'] : undefined,
      );
      await this.integrationLogRepository.logSuccess(
        'create-bigCommerce-order',
        rawData,
        response,
        rawData?.data?.id,
      );
      return response;
    } catch (error) {
      await this.integrationLogRepository.logError(
        'create-bigCommerce-order',
        rawData,
        JSON.stringify(error) as any,
      );
      res.status(HttpStatus.OK).send();
    }
  }
}
