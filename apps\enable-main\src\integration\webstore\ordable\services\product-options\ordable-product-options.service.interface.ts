import { Store } from '../../../../../store/models/store.model';
import { CreateOrdableProductOptionsDto } from '../../dtos/product-options/create-ordable-product-options.dto';
import { UpdateOrdableProductOptionsDto } from '../../dtos/product-options/update-ordable-product-options.dto';

export interface OrdableProductOptionsServiceInterface {
  create(
    createOrdableProductOptionsDto: CreateOrdableProductOptionsDto,
    store: Store,
  ): Promise<any>;

  update(
    updateOrdableProductOptionsDto: UpdateOrdableProductOptionsDto,
    store: Store,
  ): Promise<any>;
}
