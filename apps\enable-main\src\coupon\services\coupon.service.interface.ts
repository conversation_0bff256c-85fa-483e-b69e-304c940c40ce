import {
  CouponDocument,
  CouponIndexDto,
  CreateCouponDto,
  CurrentUser,
  CustomerDocument,
  UpdateCouponDto,
} from '@app/shared-stuff';
import { Types } from 'mongoose';

export interface CouponServiceInterface {
  index(couponIndexDto: CouponIndexDto): Promise<CouponDocument[]>;
  findById(couponId: Types.ObjectId): Promise<CouponDocument>;
  findCustomerRedeemableCoupons(
    customer: CustomerDocument,
  ): Promise<CouponDocument[]>;
  findByCompanyId(
    companyId: Types.ObjectId,
    withException?: boolean,
  ): Promise<CouponDocument[]>;
  findCustomerHighestRedeemableCoupon(
    customer: CustomerDocument,
  ): Promise<CouponDocument>;
  findCustomerNextCoupon(
    customer: CustomerDocument,
  ): Promise<CouponDocument | null>;
  findByOrdableId(ordableId: number): Promise<CouponDocument | null>;
  findByShopifyDiscountCodeForCustomer(
    code: string,
    customer: CustomerDocument,
  ): Promise<CouponDocument | null>;
  findByCost(
    companyId: Types.ObjectId,
    lowerBound: number,
    upperBound: number,
  ): Promise<CouponDocument[]>;
  create(createCouponDto: CreateCouponDto): Promise<CouponDocument>;
  update(updateCouponDto: UpdateCouponDto): Promise<CouponDocument | null>;
  delete(
    couponId: Types.ObjectId,
    companyId: Types.ObjectId,
    currentUser: CurrentUser,
  ): Promise<string>;
}

export const CouponServiceInterface = Symbol('CouponServiceInterface');
