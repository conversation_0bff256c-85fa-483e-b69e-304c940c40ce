import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import {
  ChatConfigDto,
  EmailConfigDto,
  Field,
  PushNotificationConfigDto,
  SmsConfigDto,
  TemplateOwner,
  WalletConfigDto,
} from '@app/shared-stuff';

export type ConfigurationDocument = Configuration & Document;

@Schema({
  timestamps: true,
  collection: 'notification_configuration',
})
export class Configuration {
  @Prop({
    type: () => EmailConfigDto,
    required: true,
  })
  emailConfig: EmailConfigDto;

  @Prop({
    type: () => SmsConfigDto,
    required: true,
  })
  smsConfig: SmsConfigDto;

  @Prop({
    type: () => SmsConfigDto,
    required: false,
  })
  otpConfig: SmsConfigDto;

  @Prop({
    type: () => ChatConfigDto,
    required: false,
  })
  chatConfig: ChatConfigDto;

  @Prop({
    type: () => PushNotificationConfigDto,
    required: true,
  })
  pushNotificationConfig: PushNotificationConfigDto;

  @Field({ type: WalletConfigDto, required: false })
  walletConfig?: WalletConfigDto;

  @Prop({
    type: () => TemplateOwner,
    required: false,
  })
  owner: TemplateOwner;
}

export const ConfigurationSchema = SchemaFactory.createForClass(Configuration);
