import {
  CompanyDocument,
  CreateManualLoyaltyTransactionDto,
  CustomerDocument,
  CustomerEarnedBenefit,
  EarnedReward,
  LoyaltyTransaction,
  LoyaltyTransactionDocument,
  LoyaltyTransactionEffect,
  LoyaltyTransactionSource,
  LoyaltyTransactionType,
  RewardSource,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import {
  BadRequestException,
  NotFoundException,
} from '@nestjs/common/exceptions';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { plainToInstance } from 'class-transformer';
import * as moment from 'moment-timezone';
import { Types } from 'mongoose';
import { CustomerReadServiceInterface } from '../../customer/modules/customer-read/customer-read.service.interface';
import { CustomerWriteServiceInterface } from '../../customer/modules/customer-write/customer-write.service.interface';
import { PunchCardAchievementService } from '../../punch-card/modules/punch-card-achievement/punch-card-achievement.service';
import { LoyaltyTransactionRepositoryInterface } from '../repositories/loyalty-transaction-repository.interface';
import { LoyaltyTransactionServiceInterface } from './loyalty-transaction-service.interface';
import { CalendarSystemService } from '../../company/services/calendar-system/calendar-system.service';

@Injectable()
export class LoyaltyTransactionService
  implements LoyaltyTransactionServiceInterface
{
  transactionHandler: Map<
    LoyaltyTransactionType,
    (
      customer: CustomerDocument,
      createLoyaltyTransactionDto: CreateManualLoyaltyTransactionDto,
    ) => Promise<LoyaltyTransaction>
  > = new Map();

  constructor(
    @Inject(LoyaltyTransactionRepositoryInterface)
    private repository: LoyaltyTransactionRepositoryInterface,
    private punchCardAchievementService: PunchCardAchievementService,
    @Inject(CustomerWriteServiceInterface)
    private customerWriteService: CustomerWriteServiceInterface,
    @Inject(CustomerReadServiceInterface)
    private customerReadService: CustomerReadServiceInterface,
    private calendarSystemService: CalendarSystemService,
    private eventEmitterService: EventEmitter2,
  ) {
    this.initTransactionHandler();
  }

  private initTransactionHandler() {
    this.transactionHandler.set(
      LoyaltyTransactionType.REWARD,
      this.handleReward.bind(this),
    );
  }

  async recordEarnedBenefits(
    customer: CustomerDocument,
    earnedBenefits: CustomerEarnedBenefit[],
  ) {
    return this.repository.createMany(
      earnedBenefits.map((benefit) => ({
        companyId: customer.company,
        customerId: customer._id,
        effect: LoyaltyTransactionEffect.ADD,
        source: LoyaltyTransactionSource.BENEFIT_EARNED,
        type: LoyaltyTransactionType.BENEFIT,
        benefit,
      })),
    );
  }

  async recordRedeemedBenefit(
    customer: CustomerDocument,
    benefit: CustomerEarnedBenefit,
  ): Promise<LoyaltyTransactionDocument> {
    return this.repository.create({
      companyId: customer.company,
      customerId: customer._id,
      effect: LoyaltyTransactionEffect.REDEEM,
      source: LoyaltyTransactionSource.BENEFIT_REDEEMED,
      type: LoyaltyTransactionType.BENEFIT,
      benefit,
    });
  }

  async recordRefundedBenefits(
    customer: CustomerDocument,
    benefits: CustomerEarnedBenefit[],
  ): Promise<LoyaltyTransactionDocument[]> {
    return this.repository.createMany(
      benefits.map((benefit) => ({
        companyId: customer.company,
        customerId: customer._id,
        effect: LoyaltyTransactionEffect.REMOVE,
        source: LoyaltyTransactionSource.BENEFIT_REFUNDED,
        type: LoyaltyTransactionType.BENEFIT,
        benefit,
      })),
    );
  }

  async createManual(
    createLoyaltyTransactionDto: CreateManualLoyaltyTransactionDto,
  ) {
    const customer = await this.customerReadService.findOne(
      createLoyaltyTransactionDto.customerId.toHexString(),
      createLoyaltyTransactionDto.companyId,
    );

    const loyaltyTransaction = await this.transactionHandler.get(
      createLoyaltyTransactionDto.type,
    )(customer, createLoyaltyTransactionDto);

    return await this.repository.create(loyaltyTransaction);
  }

  async handleReward(
    customer: CustomerDocument,
    createLoyaltyTransactionDto: CreateManualLoyaltyTransactionDto,
  ): Promise<LoyaltyTransaction> {
    const achievement = (
      await this.punchCardAchievementService.findAllAchievements(
        createLoyaltyTransactionDto.companyId,
      )
    ).find((achievement) =>
      achievement._id.equals(createLoyaltyTransactionDto.achievementId),
    );

    if (!achievement) {
      throw new NotFoundException('Achievement not found');
    }

    if (createLoyaltyTransactionDto.effect == LoyaltyTransactionEffect.ADD) {
      await this.customerWriteService.updateRewards(customer, [
        {
          ...achievement.reward,
          achievementId: achievement._id,
          source: RewardSource.MANUAL_TRANSACTION,
          earnedAt: moment.utc().toDate(),
          _id: new Types.ObjectId(),
        },
      ] as EarnedReward[]);
    } else {
      throw new BadRequestException(
        `Effect ${createLoyaltyTransactionDto.effect} is not supported for reward transaction`,
      );
    }

    this.eventEmitterService.emit('customer.rewards.updated', customer);

    return plainToInstance(LoyaltyTransaction, {
      ...createLoyaltyTransactionDto,
      reward: achievement.reward,
      source: LoyaltyTransactionSource.MANUAL,
    });
  }

  async getRedeemedBenefitCount(
    customer: CustomerDocument,
    company: CompanyDocument,
    benefitId: Types.ObjectId,
  ): Promise<number> {
    const startDate = this.calendarSystemService.getStartDate(company);
    const endDate = this.calendarSystemService.getEndDate(customer, company);

    return this.repository.countDocuments({
      customerId: customer._id,
      companyId: company._id,
      source: LoyaltyTransactionSource.BENEFIT_REDEEMED,
      type: LoyaltyTransactionType.BENEFIT,
      'benefit._id': benefitId,
      createdAt: { $gte: startDate, $lte: endDate },
    });
  }
}
