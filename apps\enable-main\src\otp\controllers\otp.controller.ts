import {
  GenericExceptionFilter,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Inject,
  Post,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { GenerateOTPDto } from '../dto/generate-otp.dto';
import { VerifyOTPResponseDTO } from '../dto/verify-otp-response.dto';
import { VerifyOTPDto } from '../dto/verify-otp.dto';
import { OTPServiceInterface } from '../services/otp.service.interface';

@Controller('otp')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@ApiTags('OTP')
@SetMetadata('module', 'otp')
export class OTPController {
  constructor(
    @Inject(OTPServiceInterface)
    private readonly otpService: OTPServiceInterface,
  ) {}

  @Post('generate')
  @SetMetadata('public', 'true')
  async generate(@Body() generateOTPDto: GenerateOTPDto) {
    return await this.otpService.generateOTP(generateOTPDto);
  }

  @Post('verify')
  @SetMetadata('public', 'true')
  async verify(
    @Body() verifyOTPDto: VerifyOTPDto,
  ): Promise<VerifyOTPResponseDTO> {
    return await this.otpService.verifyOTP(verifyOTPDto);
  }
}
