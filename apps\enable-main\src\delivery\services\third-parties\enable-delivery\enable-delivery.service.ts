import { OrderDocument } from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ThirdPartyTaskCreationDto } from '../../../dto/third-party-task-creation.dto';
import { ThirdPartiesServiceInterface } from '../third-parties.service.interface';
import { ThirdPartySharedService } from '../third-party-shared.service';

@Injectable()
export class EnableDeliveryService implements ThirdPartiesServiceInterface {
  vehicleTypes: string[] = [
    'car',
    'motor cycle',
    'bicycle',
    'scooter',
    'foot',
    'truck',
  ] as const;
  defaultVehicleType: string = 'bicycle';
  constructor(
    private configService: ConfigService,
    private thirdPartySharedService: ThirdPartySharedService,
  ) {}

  async applyPostFunction(taskCreationResponse: any, order: OrderDocument) {
    await this.updateOrder(order);
  }

  async createTask(
    thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto,
  ): Promise<any> {
    const teamId = this.configService.get('ENABLE_DELIVERY_TEAM_ID');
    return await this.thirdPartySharedService.createTookanTask(
      thirdPartyTaskCreationDto.order,
      thirdPartyTaskCreationDto.customer,
      thirdPartyTaskCreationDto.branch,
      thirdPartyTaskCreationDto.company,
      undefined,
      true,
      teamId,
    );
  }

  private async updateOrder(order: OrderDocument) {
    order.assigned_driver_name = 'Enable Delivery';
    order.driver = undefined;
    order.deliveryTaskCreated = true;
    await order.save();
  }
}
