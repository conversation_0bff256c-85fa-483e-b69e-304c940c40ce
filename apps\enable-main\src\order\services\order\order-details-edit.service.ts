import {
  BranchWithId,
  CollectionName,
  DeliveryMethod,
  OrderDeliveryAction,
  OrderDocument,
  OrderLogActionEnum,
  OrderPaymentMethod,
  OrderPaymentStatus,
  responseCode,
} from '@app/shared-stuff';
import { CompanyDocument } from '@app/shared-stuff/models/company.model';
import { ForbiddenException, Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { BranchService } from '../../../branch/services/branch/branch.service';
import { CompanyService } from '../../../company/services/company/company.service';
import { CustomerOrderServiceInterface } from '../../../customer/modules/customer-order/customer-order.service.interface';
import { CustomerReadServiceInterface } from '../../../customer/modules/customer-read/customer-read.service.interface';
import { OrderDetailsToEdit } from '../../dto/order.dto';
import { OrderInvoiceService } from '../../modules/order-invoice/order-invoice.service';
import { OrderInvoice } from '../../types/order-invoice.type';
import { OrderLogServiceInterface } from '../interfaces/order-log.service.interface';
import { OrderDeliveryService } from '../order-delivery/order-delivery.service';
import { OrderNotificationService } from '../order-notification/order-notification.service';
import { OrderPaymentService } from '../order-payment/order-payment.service';

@Injectable()
export class OrderDetailsEditService {
  constructor(
    @InjectModel(CollectionName.ORDER) private orderModel: Model<OrderDocument>,
    private branchService: BranchService,
    private companyService: CompanyService,
    @Inject(CustomerOrderServiceInterface)
    private customerOrderService: CustomerOrderServiceInterface,
    @Inject(CustomerReadServiceInterface)
    private customerReadService: CustomerReadServiceInterface,
    private orderInvoiceService: OrderInvoiceService,
    private orderNotificationService: OrderNotificationService,
    private orderPaymentService: OrderPaymentService,
    @Inject('OrderLogServiceInterface')
    private orderLogService: OrderLogServiceInterface,
    private orderDeliveryService: OrderDeliveryService,
  ) {}

  async editOrderDetails(
    orderDetailsToEdit: OrderDetailsToEdit,
    company_id: string,
  ) {
    const order = await this.orderModel.findOne({
      _id: orderDetailsToEdit.order,
    });
    const oldTotalAmount = order.total_amount;

    if (company_id && order.company.toString() != company_id) {
      throw new ForbiddenException({
        code: responseCode.MISSING_DATA,
        statusCode: 422,
        message: "You don't have access to this company orders",
      });
    }

    const company = await this.companyService.get_details(order.company);
    let branch: BranchWithId | undefined;
    if (order.branch_object) {
      branch = await this.branchService.get_details(order.branch_object);
    }

    order.source = orderDetailsToEdit.source;
    order.order_remarks = orderDetailsToEdit.order_remarks;
    order.invoice_number = orderDetailsToEdit.invoice_number;

    const orderInvoice: OrderInvoice =
      this.orderInvoiceService.computeFromInvoicedAmount(
        orderDetailsToEdit.invoiced_amount,
        orderDetailsToEdit.delivery_amount,
        order.discounts,
      );

    await this.handlePaymentLogic(
      order,
      orderDetailsToEdit,
      orderInvoice,
      branch,
      company,
    );

    order.set(orderInvoice);

    const isTotalAmountChanged = oldTotalAmount !== order.total_amount;
    if (isTotalAmountChanged) {
      const customer = await this.customerReadService.findOne(
        order.customer.toHexString(),
        company._id,
      );
      order.loyaltyProgress =
        await this.customerOrderService.computeLoyaltyProgress(
          company,
          order,
          customer,
        );
    }

    await this.handleGiftLogic(order, orderDetailsToEdit);

    order.updatedBy = orderDetailsToEdit.current_user;
    await order.save();

    await this.processDeliveryTask(order, company, branch);
    await this.orderLogService.saveOrderLog(
      order,
      { requestedObject: orderDetailsToEdit },
      { responseObject: {} },
      OrderLogActionEnum.ON_ORDER_DETAILS_EDITED,
      orderDetailsToEdit.current_user,
    );
    return order;
  }

  private async handlePaymentLogic(
    order: OrderDocument,
    orderDetailsToEdit: OrderDetailsToEdit,
    orderInvoice: OrderInvoice,
    branch: BranchWithId | undefined,
    company: CompanyDocument,
  ) {
    if (
      order.payment_method == OrderPaymentMethod.online &&
      orderDetailsToEdit.payment_method == OrderPaymentMethod.online &&
      orderDetailsToEdit.total_amount != order.total_amount
    ) {
      await this.orderPaymentService.updateOrderPaymentAmount(
        order.payment_code,
        orderInvoice.total_amount,
      );
    }
    if (
      (order.payment_method == OrderPaymentMethod.cash ||
        order.payment_method == OrderPaymentMethod.card_machine) &&
      orderDetailsToEdit.payment_method == OrderPaymentMethod.online
    ) {
      order.payment_method = OrderPaymentMethod.online;
      await this.orderPaymentService.createOrderPayment(
        order,
        branch,
        company,
        company.call_back_url,
        order.delivery_action,
        orderDetailsToEdit.current_user,
      );
    }
    if (
      order.payment_method == OrderPaymentMethod.online &&
      (orderDetailsToEdit.payment_method == OrderPaymentMethod.cash ||
        orderDetailsToEdit.payment_method == OrderPaymentMethod.card_machine) &&
      order.payment_status != OrderPaymentStatus.COMPLETED
    ) {
      await this.orderPaymentService.removeOrderFromPayment(
        order,
        orderDetailsToEdit.payment_method,
        orderDetailsToEdit.current_user,
      );
    }
    if (order.payment_method == OrderPaymentMethod.prepaid) {
      order.payment_status = OrderPaymentStatus.COMPLETED;
    }
    if (
      orderDetailsToEdit.payment_method == OrderPaymentMethod.card_machine &&
      order.payment_method == OrderPaymentMethod.cash
    ) {
      order.payment_method = OrderPaymentMethod.card_machine;
    }
    if (
      orderDetailsToEdit.payment_method == OrderPaymentMethod.cash &&
      order.payment_method == OrderPaymentMethod.card_machine
    ) {
      order.payment_method = OrderPaymentMethod.cash;
    }
  }

  private async handleGiftLogic(
    order: OrderDocument,
    orderDetailsToEdit: OrderDetailsToEdit,
  ) {
    if (!orderDetailsToEdit.is_gift) return;

    const wasGiftOrder = order.is_gift;
    const recipientChanged =
      order.recipient_name !== orderDetailsToEdit.recipient_name ||
      order.recipient_phone !== orderDetailsToEdit.recipient_phone;
    const shouldSendDeliveryNotification =
      (!wasGiftOrder || recipientChanged) &&
      order.delivery_action === OrderDeliveryAction.SEND_SMS;

    order.is_gift = true;
    order.is_secret = orderDetailsToEdit.is_secret;
    order.recipient_name = orderDetailsToEdit.recipient_name;
    order.recipient_phone = orderDetailsToEdit.recipient_phone;
    order.recipient_country_code = orderDetailsToEdit.recipient_country_code;
    order.cardMessage = orderDetailsToEdit.cardMessage;

    if (shouldSendDeliveryNotification) {
      await order.save();
      await this.orderNotificationService.handleDeliveryAndPaymentNotification(
        order,
      );
    }
  }

  private async processDeliveryTask(
    order: OrderDocument,
    company: CompanyDocument,
    branch: BranchWithId,
  ) {
    if (
      order.delivery_action !== OrderDeliveryAction.IN_STORE_PICKUP &&
      order.tookan_job_id &&
      (order.deliveryMethod == DeliveryMethod.BRANCH_DRIVERS ||
        order.deliveryMethod == DeliveryMethod.COMPANY_DRIVERS)
    ) {
      const customer = await this.customerReadService.findOne(
        order.customer.toHexString(),
        company._id,
      );

      await this.orderDeliveryService.tookanTaskProcessing({
        order,
        customer,
        branch,
        company,
        driver: null,
        reassignDriver: false,
      });
    }
  }
}
