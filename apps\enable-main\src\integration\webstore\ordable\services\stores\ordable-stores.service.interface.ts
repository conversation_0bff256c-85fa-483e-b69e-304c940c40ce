import { OrdableEntityDocument, OrdableInfo } from '@app/shared-stuff';

import { Types } from 'mongoose';
import { StoreDocument } from '../../../../../store/models/store.model';

export interface OrdableStoresServiceInterface {
  handleSyncCompleted(store: StoreDocument): Promise<void>;
  getStoresForCompany(companyId: Types.ObjectId): Promise<StoreDocument[]>;
  forEachStore<ENTITY extends OrdableEntityDocument>(
    entity: ENTITY,
    action: (store: StoreDocument) => Promise<OrdableInfo | void>,
  ): Promise<ENTITY>;
}

export const OrdableStoresServiceInterface = Symbol(
  'OrdableStoresServiceInterface',
);
