import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class DeliverectTable {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  floorId: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  seats: number;
}
