import { FilterDTO, responseCode, WebStoreToPromote } from '@app/shared-stuff';
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Req,
  Res,
  SetMetadata,
} from '@nestjs/common';
import { Api<PERSON><PERSON>cAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { Types } from 'mongoose';
import { MainService } from '../../../main/services/main/main.service';
import { OrderIndex } from '../../../order/dto/order.dto';
import { OrderService } from '../../../order/services/order/order.service';
import { HelperService } from '../../../shared/services/helper/helper.service';
import { CutomerOnCallDto } from '../../dto/customer-on-call.dto';
import { PhonePopulateService } from '../../services/phone-populate/phone-populate.service';

@Controller()
@ApiTags('Main Controller')
@SetMetadata('module', 'main')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class MainController {
  constructor(
    private orderService: OrderService,
    private helperService: HelperService,
    private mainService: MainService,
    private phonePopulate: PhonePopulateService,
  ) {}

  @Get('dashboard')
  @SetMetadata('action', 'dashboard')
  async get_dashboard(
    @Query() orderIndex: OrderIndex,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      orderIndex.company = req['company_id'] ? req['company_id'] : '';
      orderIndex.branch = req['branch_id'] ? req['branch_id'] : '';
      orderIndex.filter_branches = req['branches'] ? req['branches'] : '';
      const orders = await this.orderService.index(orderIndex);

      // let totalOrders = await this.orderService.getTotalNumberOfOrders(orderIndex);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to get all',
        {
          orders: orders[0]['paginatedResults'],
          totalOrders: orders[0]['totalCount'][0]
            ? orders[0]['totalCount'][0]['createdAt']
            : 0,
          counts: [],
        },
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get('search')
  @SetMetadata('action', 'search')
  async search(
    @Query('keyword') keyword: string,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const company_id = req['company_id'] ? req['company_id'] : '';
      const searchResult = await this.mainService.search(keyword, company_id);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'Success To Perform Search',
        searchResult,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get('populate')
  @SetMetadata('action', 'findAll')
  async findAllByPhoneNumber(
    @Query() filterDto: FilterDTO,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      const requestedCompanyId = req['company_id'] ? req['company_id'] : '';
      const requestedBranches = req['branches'] ? req['branches'] : '';
      const requestedCompanies = req['companies'] ? req['companies'] : '';

      // let populateResult = await this.mainService.findAllByPhoneNumber(
      //   filterDto,
      //   requestedCompanyId,
      //   requestedBranches,
      //   requestedCompanies
      // );
      const response = await this.phonePopulate.populate({
        phoneNumber: filterDto.phoneNumber,
        companyId: requestedCompanyId
          ? requestedCompanyId
          : filterDto.companyId,
        companies: requestedCompanies
          ? requestedCompanies
          : filterDto.companies,
        branches: requestedBranches ? requestedBranches : undefined,
        branchId: filterDto.branchId as any,
        brandId: filterDto.brandId as any,
      });
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'Success To Populate',
        response,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('webstore/promote')
  @SetMetadata('action', 'promoteWebStore')
  async promoteWebStore(
    @Body() webStoreToPromote: WebStoreToPromote,
    @Req() req: Request,
  ) {
    webStoreToPromote.companyId = new Types.ObjectId(req['company_id']);
    return await this.mainService.webStorePromote(webStoreToPromote);
  }

  @Post('/customer/callEvent')
  @SetMetadata('action', 'populateCustomerPhone')
  async pushCustomerCallEvent(@Body() customerOnCallDto: CutomerOnCallDto) {
    this.mainService.pushCustomerCallEvent(customerOnCallDto);
    return 'DONE';
  }

  @Get('documents/:name')
  @SetMetadata('public', 'true')
  async redirectToDocument(@Param('name') name: string, @Res() res: Response) {
    const url = await this.mainService.getDocumentUrl(name);
    return res.redirect(url);
  }

  @Get('health')
  @SetMetadata('public', 'true')
  async healthCheck(@Res() res: Response) {
    return res.status(200).send('OK');
  }
}
