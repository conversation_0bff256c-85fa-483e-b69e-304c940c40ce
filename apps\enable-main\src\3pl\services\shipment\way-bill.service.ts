import {
  CompanyDocument,
  LoggerService,
  ShipmentDocument,
  TempCustomerDocument,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as Handlebars from 'handlebars';
import * as pdf from 'html-pdf-node';
import * as moment from 'moment-timezone';
import * as path from 'path';

@Injectable()
export class WayBillService {
  private readonly loggerService = new LoggerService(WayBillService.name);

  constructor(private configService: ConfigService) {}

  async generateWayBill(
    shipment: ShipmentDocument,
    company: CompanyDocument,
    tempCustomer: TempCustomerDocument,
  ): Promise<Buffer> {
    try {
      const html = await this.compileTemplate(shipment, company, tempCustomer);
      const pdfBuffer = await this.createPdfFromHtml(html);
      return pdfBuffer;
    } catch (error) {
      this.loggerService.error(
        `Error generating waybill: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to generate waybill PDF: ${error.message}`);
    }
  }

  private async compileTemplate(
    shipment: ShipmentDocument,
    company: CompanyDocument,
    tempCustomer: TempCustomerDocument,
  ): Promise<string> {
    const pathToTemplate = path.join('files', 'sample-way-bill.hbs');
    const templateFile = fs.readFileSync(pathToTemplate, {
      encoding: 'utf-8',
    });
    const template = Handlebars.compile(templateFile);
    return template({
      shipment: {
        ...shipment.toJSON(),
        createdAt: moment.utc(shipment['createdAt']).format('DD/MM/YYYY'),
        deliveryDate: moment.utc(shipment['deliveryDate']).format('DD/MM/YYYY'),
      },
      company: company.toJSON(),
      tempCustomer: tempCustomer.toJSON(),
      barCodeImage: `${this.configService.get(
        'HOST_URL',
      )}/images/${shipment.trackingNumber}/barcode.png`,
    });
  }

  private async createPdfFromHtml(html: string): Promise<Buffer> {
    try {
      const file = { content: html };
      const pdfBuffer = await pdf.generatePdf(file, { format: 'A4' });
      return pdfBuffer;
    } catch (error) {
      this.loggerService.error(
        `Error creating PDF: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to create PDF: ${error.message}`);
    }
  }
}
