import { Discount, OrderDocument } from '@app/shared-stuff';

export type InvoiceFields =
  | 'invoiced_amount' // cart value
  | 'total_discount' // total discount value applicable to cart
  | 'total_amount' // total amount (includes cart value, discount, and delivery amount)
  | 'total_amount_after_discount' // cart value after discount, does not include delivery amount
  | 'delivery_amount'; // delivery amount excluding delivery discount

export type OrderInvoice = Pick<OrderDocument, InvoiceFields> & {
  discounts?: Discount[];
};
