import {
  AcknowledgementType,
  areObjectIdsEqual,
  BranchAssignmentScheme,
  BranchDocument,
  CollectionName,
  CompanyType,
  CountryDialCode,
  CurrentUser,
  CustomerDocument,
  CustomerEarnedBenefit,
  DeliveryMethod,
  Discount,
  DiscountSource,
  EnableLocalization,
  LogError,
  Order,
  OrderBranchRejection,
  OrderDeliveryAction,
  OrderDeliveryType,
  OrderDocument,
  OrderEventEnum,
  OrderItem,
  OrderLogActionEnum,
  OrderPaymentMethod,
  OrderPaymentStatus,
  OrderSource,
  OrderStatusEnum,
  OrderTransitionTrigger,
  pick,
  PusherService,
  responseCode,
  SavedLocationDocument,
  TrackingPageSelectedLogo,
  TriggerAction,
} from '@app/shared-stuff';
import { Brand } from '@app/shared-stuff/models/brand.model';
import {
  BadRequestException,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { CouponServiceInterface } from 'apps/enable-main/src/coupon/services/coupon.service.interface';
import { LoyaltyTransactionServiceInterface } from 'apps/enable-main/src/loyalty-transaction/services/loyalty-transaction-service.interface';
import * as xl from 'excel4node';
import * as moment from 'moment-timezone';
import {
  FilterQuery,
  isObjectIdOrHexString,
  isValidObjectId,
  Model,
  Types,
} from 'mongoose';
import { BranchService } from '../../../branch/services/branch/branch.service';
import { CompanyService } from '../../../company/services/company/company.service';
import { CustomerReadServiceInterface } from '../../../customer/modules/customer-read/customer-read.service.interface';
import { ThirdPartyTaskCreationDto } from '../../../delivery/dto/third-party-task-creation.dto';
import { DeliveryConfigurationServiceInterface } from '../../../delivery/services/delivery-configuration/delivery-configuration.service.interface';
import { DriverService } from '../../../delivery/services/driver/driver.service';
import { CallbackFalconFlexDto } from '../../../delivery/services/third-parties/falcon-flex/dtos/callback-falcon-flex.dto';
import { FalconFlexCallBackType } from '../../../delivery/services/third-parties/falcon-flex/enums/falcon-flex-call-back-type.enum';
import { FalconFlexTaskStatus } from '../../../delivery/services/third-parties/falcon-flex/enums/falcon-flex-task-status.enum';
import { UpdateWishboxDeliveryStatusDto } from '../../../delivery/services/third-parties/wishbox/dtos/update-wishbox-delivery-status.dto';
import { WishboxOrderDeliveryStatusCode } from '../../../delivery/services/third-parties/wishbox/enums/wishbox-order-delivery-status-code.enum';
import { SavedLocationService } from '../../../location/services/saved-location/saved-location.service';
import { GoogleCloudStorageService } from '../../../storage/google-cloud-storage.service';
import { UserDocument } from '../../../user/models/user.model';
import { UserService } from '../../../user/services/user/user.service';
import { ChangeOrderPaymentStatusDto } from '../../dto/change-order-payment-status.dto';
import { DeliveryOptionsDto } from '../../dto/delivery-options.dto';
import {
  AcknowledgeOrderDto,
  DriverAssignToOrder,
  FirstPartyTaskState,
  OrderDeliveryAddressToUpdate,
  OrderDeliveryTypeMapping,
  OrderDispatchToEdit,
  OrderDriverToChange,
  OrderEventDto,
  OrderIndex,
  OrderOwnDriversAssignmentType,
  OrderPaymentMethodToChange,
  OrderSearchMapping,
  OrderSortMapping,
  OrderToAssign,
  OrderToCancel,
  OrderToReject,
  TrackingOrderDto,
} from '../../dto/order.dto';
import { ReadyOrderDto } from '../../dto/ready-order.dto';
import { RefireOrderAcknowledgementDto } from '../../dto/refire-order-acknowledgement.dto';
import { TrackingOrderForMapDto } from '../../dto/tracking-order-for-map.dto';
import { OrderStatusDocument } from '../../models/order.status.model';
import { OrderLogServiceInterface } from '../interfaces/order-log.service.interface';
import { OrderDeliveryService } from '../order-delivery/order-delivery.service';
import { OrderNotificationService } from '../order-notification/order-notification.service';
import { OrderPaymentService } from '../order-payment/order-payment.service';
import { OrderReversionService } from '../order-reversion/order-reversion.service';
import { OrderStatusService } from '../order-status/order-status.service';
import { SlotService } from './../../../delivery/services/slot/slot.service';

@Injectable()
export class OrderService {
  private readonly logger = new Logger(OrderService.name);

  constructor(
    @InjectModel(CollectionName.ORDER) private orderModel: Model<OrderDocument>,
    @Inject(CustomerReadServiceInterface)
    private customerReadService: CustomerReadServiceInterface,
    private companyService: CompanyService,
    @InjectModel('OrderStatus')
    private orderStatusModel: Model<OrderStatusDocument>,
    @Inject('OrderLogServiceInterface')
    private readonly orderLogService: OrderLogServiceInterface,
    private configService: ConfigService,
    private orderNotificationService: OrderNotificationService,
    private branchService: BranchService,
    private orderPaymentService: OrderPaymentService,
    private slotService: SlotService,
    private orderDeliveryService: OrderDeliveryService,
    private driverService: DriverService,
    private savedLocationService: SavedLocationService,
    private userService: UserService,
    private orderStatusService: OrderStatusService,
    @Inject(DeliveryConfigurationServiceInterface)
    private deliveryConfigurationService: DeliveryConfigurationServiceInterface,
    private eventEmitter: EventEmitter2,
    private pusherService: PusherService,
    private googleCloudStorageService: GoogleCloudStorageService,
    private readonly orderReversionService: OrderReversionService,
    @Inject(CouponServiceInterface)
    private readonly couponService: CouponServiceInterface,
    @Inject(LoyaltyTransactionServiceInterface)
    private readonly loyaltyTransactionService: LoyaltyTransactionServiceInterface,
  ) {}

  async index(orderToIndex: OrderIndex) {
    const match: FilterQuery<OrderDocument> = {};
    const aggregation = [] as any;

    if (orderToIndex.company) {
      match['company'] = new Types.ObjectId(orderToIndex.company);
    }

    if (orderToIndex.month) {
      match['month'] = orderToIndex.month;
    }

    if (orderToIndex.year) {
      match['year'] = orderToIndex.year;
    }

    if (orderToIndex.customer) {
      match['customer'] = new Types.ObjectId(orderToIndex.customer);
    }

    if (orderToIndex.order_status?.length) {
      match['status'] = { $in: orderToIndex.order_status };
    }

    if (orderToIndex.payment_status?.length) {
      match['payment_status'] = { $in: orderToIndex.payment_status };
    }

    if (orderToIndex.payment_method?.length) {
      match['payment_method'] = { $in: orderToIndex.payment_method };
    }

    if (orderToIndex.brandId) {
      match['brand._id'] = new Types.ObjectId(orderToIndex.brandId);
    }

    if (!orderToIndex.includeCapturedOrders) {
      match.isManuallyCaptured = { $ne: true };
    }

    if (!orderToIndex.includeAggregatorOrders) {
      match.isAggregator = { $ne: true };
    }

    if (orderToIndex.prepaidBy !== undefined) {
      match.prepaidBy = orderToIndex.prepaidBy;
    }
    if (orderToIndex.acknowledgedBy !== undefined) {
      match['acknowledgedBy.id'] = new Types.ObjectId(
        orderToIndex.acknowledgedBy,
      );
    }

    if (orderToIndex.createdBy !== undefined) {
      match['createdBy.id'] = new Types.ObjectId(orderToIndex.createdBy);
    }

    if (
      orderToIndex.filter_branches &&
      orderToIndex.delivery_type != 'unassigned' &&
      orderToIndex.delivery_type != 'all'
    ) {
      match['branch_object'] = { $in: orderToIndex.filter_branches };
    }

    if (orderToIndex.branch) {
      match['branch_object'] = orderToIndex.branch.toString();
    }

    if (orderToIndex.source) {
      const sources = orderToIndex.source.split(',');
      sources.forEach((source) => {
        if (source == 'Webstore') sources.push('webstore');
      });
      match['source'] = { $in: sources };
    }

    if (
      orderToIndex.filter_branches &&
      orderToIndex.delivery_type != 'unassigned' &&
      orderToIndex.delivery_type != 'all'
    ) {
      match['branch_object'] = { $in: orderToIndex.filter_branches };
    }

    if (orderToIndex.branches?.length) {
      const branches = orderToIndex.branches
        .filter(Types.ObjectId.isValid)
        .map((branch) => new Types.ObjectId(branch));
      if (branches.length > 0) {
        match['branch_object'] = { $in: branches };
      }
    }

    if (orderToIndex.delivery_type && orderToIndex.delivery_type != 'all') {
      aggregation.push({
        $match: OrderDeliveryTypeMapping(orderToIndex.delivery_type),
      });
    }

    if (orderToIndex.deliverectPosOrderSnooze == 'yes') {
      match['source'] = {
        ...(match['source'] ?? {}),
        $ne: OrderSource.DELIVERECT,
      };
    }

    match['deletedAt'] = null;

    if (orderToIndex.search_key) {
      if (orderToIndex.search_type == 'all') {
        aggregation.push(
          {
            $addFields: {
              matched: {
                $or: [
                  {
                    $regexMatch: {
                      input: '$customer_name',
                      options: 'i',
                      regex: `.*${orderToIndex.search_key}.*`,
                    },
                  },
                  {
                    $regexMatch: {
                      input: '$invoice_number',
                      options: 'i',
                      regex: `.*${orderToIndex.search_key}.*`,
                    },
                  },
                  {
                    $regexMatch: {
                      input: '$customer_phone',
                      options: 'i',
                      regex: `.*${orderToIndex.search_key}.*`,
                    },
                  },
                  {
                    $regexMatch: {
                      input: '$code',
                      options: 'i',
                      regex: `.*${orderToIndex.search_key}.*`,
                    },
                  },
                  {
                    $regexMatch: {
                      input: '$branchName',
                      options: 'i',
                      regex: `.*${orderToIndex.search_key}.*`,
                    },
                  },
                  {
                    $eq: [
                      '$total_amount',
                      !Number.isNaN(Number(orderToIndex.search_key))
                        ? parseFloat(orderToIndex.search_key)
                        : -1,
                    ],
                  },
                ],
              },
            },
          },
          { $match: { matched: true } },
        );
      } else {
        if (orderToIndex.search_type == 'amount') {
          match['total_amount'] = parseFloat(orderToIndex.search_key);
        } else {
          aggregation.push(
            {
              $addFields: {
                matched: {
                  $regexMatch: OrderSearchMapping(
                    orderToIndex.search_type || 'all',
                    orderToIndex.search_key,
                  ),
                },
              },
            },
            { $match: { matched: true } },
          );
        }
      }
    }

    if (orderToIndex.createdDateFrom && orderToIndex.createdDateTo) {
      const from = moment.utc(orderToIndex.createdDateFrom).toDate();

      const isDateOnly = moment
        .utc(orderToIndex.createdDateTo, 'YYYY-MM-DD', true)
        .isValid();
      const to = isDateOnly
        ? moment.utc(orderToIndex.createdDateTo).endOf('day').toDate()
        : moment.utc(orderToIndex.createdDateTo).toDate();

      match['createdAt'] = { $gte: from, $lte: to };
    }

    if (orderToIndex.deliveryDateFrom && orderToIndex.deliveryDateTo) {
      const from = moment.utc(orderToIndex.deliveryDateFrom).toDate();

      const isDateOnly = moment
        .utc(orderToIndex.deliveryDateTo, 'YYYY-MM-DD', true)
        .isValid();

      const to = isDateOnly
        ? moment.utc(orderToIndex.deliveryDateTo).endOf('day').toDate()
        : moment.utc(orderToIndex.deliveryDateTo).toDate();

      match['$or'] = [
        {
          delivery_date: { $gte: from, $lte: to },
          delivery_action: { $ne: OrderDeliveryAction.IN_STORE_PICKUP },
        },
        {
          pickup_date: { $gte: from, $lte: to },
          delivery_action: { $eq: OrderDeliveryAction.IN_STORE_PICKUP },
        },
      ];
    }

    if (orderToIndex.companyIds && !orderToIndex.company) {
      const companyIdsArray = orderToIndex.companyIds.split(',');
      const companyIds = companyIdsArray.map((x) => new Types.ObjectId(x));
      match['company'] = { $in: companyIds, $exists: true };
    }

    aggregation.push({
      $match: match,
    });

    if (orderToIndex.sort_type) {
      aggregation.push({
        $sort: OrderSortMapping[orderToIndex.sort_type]
          ? OrderSortMapping[orderToIndex.sort_type]
          : { createdAt: -1 },
      });
    } else {
      aggregation.push({
        $sort: { createdAt: -1 },
      });
    }
    aggregation.push({
      $facet: {
        paginatedResults: [
          ...(Number(orderToIndex.offset) || Number(orderToIndex.offset) == 0
            ? [
                {
                  $skip:
                    Number(orderToIndex.offset) * Number(orderToIndex.limit),
                },
              ]
            : [
                {
                  $skip: 0,
                },
              ]),
          ...(Number(orderToIndex.limit)
            ? [
                {
                  $limit: Number(orderToIndex.limit),
                },
              ]
            : [{ $limit: 10 }]),
          {
            $set: {
              delivery_date: {
                $cond: {
                  if: {
                    $eq: ['$delivery_type', OrderDeliveryType.schedule_later],
                  },
                  then: undefined,
                  else: '$delivery_date',
                },
              },
            },
          },
          // TODO we should update this logic bu adding items:1 instead after mongo version update
          orderToIndex.includedItems
            ? {
                $project: {
                  tookan_job_id: 0,
                  deliveryTaskResponse: 0,
                  deliveryTaskSent: 0,
                  order_items: 0,
                  tookanPickupJobIds: 0,
                  tracerIds: 0,
                  acknowledgedBy: 0,
                  assignedBy: 0,
                  deliveryJobId: 0,
                  last_contact: 0,
                  pickupJobId: 0,
                },
              }
            : {
                $project: {
                  tookan_job_id: 0,
                  deliveryTaskResponse: 0,
                  deliveryTaskSent: 0,
                  items: 0,
                  order_items: 0,
                  tookanPickupJobIds: 0,
                  tracerIds: 0,
                  acknowledgedBy: 0,
                  assignedBy: 0,
                  deliveryJobId: 0,
                  last_contact: 0,
                  pickupJobId: 0,
                },
              },
        ],
        totalCount: [
          {
            $count: 'createdAt',
          },
        ],
      },
    });

    aggregation.push({
      $set: {
        delivery_date: {
          $cond: {
            if: { $eq: ['$delivery_type', OrderDeliveryType.schedule_later] },
            then: undefined,
            else: '$delivery_date',
          },
        },
      },
    });
    const orders = await this.orderModel
      .aggregate(aggregation)
      .allowDiskUse(true);
    orders[0]['paginatedResults'] = await this.updateTimezones(
      orders[0]['paginatedResults'],
    );
    return orders;
  }

  async remove(orderId: Types.ObjectId, currentUser: CurrentUser) {
    const order = await this.orderModel.findOne({ _id: orderId });
    order.deletedAt = moment().utc().toDate();
    order.deletedBy = currentUser;

    await this.orderReversionService.deleteCustomerOrder(order);
    await this.orderNotificationService.onOrderDeleted(order);

    await order.save();
    return order;
  }

  async get_details(id: string) {
    const filterData = {
      $or: [
        { code: id },
        { external_order_id: id },
        { tableCode: id },
        { deliverectPosOrderId: id },
        { deliverectPosOrderDisplayId: id },
      ],
    } as any;

    if (isObjectIdOrHexString(id)) {
      filterData.$or.push({ _id: new Types.ObjectId(id) });
    }

    if (!isNaN(Number(id))) {
      filterData.$or.push({ bigcommerceOrderId: Number(id) });
    }

    return this.orderModel
      .findOne(filterData)
      .populate('order_items')
      .populate('customer')
      .populate('company');
  }

  async getDetailsForFront(uniqueIdentifier: string, company_id: string) {
    let selectedOrder = await this.orderModel
      .findOne(this.buildFindOrderFilter(uniqueIdentifier))
      .populate({
        path: 'pickupLocation',
        model: CollectionName.SAVED_LOCATION,
      })
      .populate({ path: 'order_items', model: OrderItem.name })
      .populate('customer', 'full_name _id email phone')
      .populate({ path: 'payment_link_id', model: CollectionName.PAYMENT })
      .populate('couponId', 'titleEn titleAr')
      .populate(
        'company',
        'name _id image interfaceConfig website followBranchMechanism',
      )
      .populate('delivery_slot');

    if (company_id && selectedOrder.company['_id'].toString() != company_id) {
      throw {
        code: responseCode.MISSING_DATA,
        statusCode: 422,
        message: "You don't have access to this company orders",
      };
    }

    selectedOrder = selectedOrder.toJSON({ virtuals: true }) as any;
    selectedOrder = (await this.updateTimezones([selectedOrder]))[0] as any;
    const orderStatues = await this.orderStatusModel
      .find({
        order: selectedOrder._id,
        new_status: {
          $in: [OrderStatusEnum.CANCELED],
        },
      })
      .populate('branch', 'name _id');

    selectedOrder['order_statuses'] = orderStatues;
    return selectedOrder;
  }

  async changeStatus(id: string, status: OrderStatusEnum, currentUser) {
    const selectedOrder = await this.orderModel.findOne({ _id: id });
    selectedOrder.last_status_update = moment().toDate();
    await this.orderStatusService.changeOrderStatus(
      selectedOrder,
      status,
      OrderTransitionTrigger.MANUAL,
      currentUser,
    );
    return selectedOrder;
  }

  async export_excel(orderToIndex: OrderIndex) {
    const wb = new xl.Workbook();
    orderToIndex.limit = 1000;
    orderToIndex.offset = 0;
    const ws = wb.addWorksheet('Orders');
    orderToIndex.includedItems = true;
    const ordersResponse = await this.index(orderToIndex);
    let orders: OrderDocument[] = ordersResponse[0]['paginatedResults'];

    const totalCount = ordersResponse[0]['totalCount'][0]
      ? ordersResponse[0]['totalCount'][0]['createdAt']
      : 0;
    orderToIndex.offset++;
    while (orders.length < totalCount) {
      const fetchedOrders = (await this.index(orderToIndex))[0][
        'paginatedResults'
      ];
      orders = orders.concat(fetchedOrders);
      orderToIndex.offset++;
    }

    const company = await this.companyService.get_details(orderToIndex.company);

    const headers = [
      'Order Id',
      'Customer Name',
      'Phone Number',
      'Email',
      'Created At',
      'Branch Name',
      'Assigned Branch',
      'Order Remarks',
      'Invoice Number',
      'Invoiced Amount',
      'Delivery Charge',
      'Discounted Amount',
      'Total Amount',
      'Pickup Date',
      'Delivery Date',
      'Payment Status',
      'Payment Method',
      'Order Status',
      'Order Source',
      'Driver Name',
      'Month',
      'Order Items (With Modifiers)',
      'Order Items',
      'Order Items (With SKUs)',
      'Order Items (With Prices)',
      'City',
      'Area',
      'Gift Order Recipient Name',
      'Gift Order Recipient Phone Number',
      'Gift Order Message',
      'Created By',
    ];
    const style = wb.createStyle({
      font: {
        color: '#FF0800',
        size: 12,
      },
      numberFormat: '$#,##0.00; ($#,##0.00); -',
    });
    for (let i = 1; i <= headers.length; i++) {
      ws.cell(1, i)
        .string(headers[i - 1])
        .style(style);
    }
    for (let i = 0; i < orders.length; i++) {
      ws.cell(i + 2, 1).string(orders[i]['code']?.toString());

      ws.cell(i + 2, 2).string(
        orders[i]['customer_name'] ? orders[i]['customer_name'] : '-',
      );
      ws.cell(i + 2, 3).string(
        orders[i]['customer_phone'] ? orders[i]['customer_phone'] : '-',
      );
      ws.cell(i + 2, 4).string(
        orders[i]['customer_email'] ? orders[i]['customer_email'] : '-',
      );

      ws.cell(i + 2, 5).string(
        moment(orders[i]['createdAt'])
          .tz(company.localization.timezone ?? 'Asia/Qatar')
          .format('YYYY-MM-DD hh:mm a'),
      );

      if (orders[i]['branch']) {
        ws.cell(i + 2, 6).string(orders[i]['branch']['name']?.toString());
        ws.cell(i + 2, 7).string(orders[i]['branch']['acronym']?.toString());
      }
      if (orders[i]['order_remarks']) {
        ws.cell(i + 2, 8).string(orders[i]['order_remarks']?.toString());
      }
      ws.cell(i + 2, 9).string(
        orders[i]['invoice_number']
          ? orders[i]['invoice_number'].toString()
          : '-',
      );
      ws.cell(i + 2, 10).number(
        orders[i]['invoiced_amount'] ? orders[i]['invoiced_amount'] : 0,
      );
      ws.cell(i + 2, 11).number(
        orders[i]['delivery_amount'] ? orders[i]['delivery_amount'] : 0,
      );
      ws.cell(i + 2, 12).number(
        orders[i]['total_discount'] ? orders[i]['total_discount'] : 0,
      );
      ws.cell(i + 2, 13).number(
        orders[i]['total_amount'] ? orders[i]['total_amount'] : 0,
      );
      ws.cell(i + 2, 14).string(
        moment.utc(orders[i]['pickup_date']).format('YYYY-MM-DD hh:mm a'),
      );
      ws.cell(i + 2, 15).string(
        moment.utc(orders[i]['delivery_date']).format('YYYY-MM-DD hh:mm a'),
      );
      ws.cell(i + 2, 16).string(orders[i]['payment_status']?.toString());
      ws.cell(i + 2, 17).string(orders[i]['payment_method']?.toString());
      ws.cell(i + 2, 18).string(orders[i]['status']?.toString());
      ws.cell(i + 2, 19).string(orders[i]['source']?.toString());
      ws.cell(i + 2, 20).string(
        orders[i]['assigned_driver_name']
          ? orders[i]['assigned_driver_name']
          : '-',
      );
      ws.cell(i + 2, 21).string(orders[i]['month']?.toString());
      if (orders[i]['items'] && orders[i]['items'].length >= 1) {
        let itemsStr = '';
        orders[i].items.forEach((item) => {
          itemsStr += `${item.quantity} X (${item.name}: `;
          item.modifierGroups?.forEach((modifierGroup) => {
            modifierGroup.modifiers?.forEach((modifier) => {
              itemsStr += `${modifier.name}, `;
            });
          });
          itemsStr += ' ) + ';
        });
        ws.cell(i + 2, 22).string(itemsStr?.toString());

        const itemsWithoutModifiersStr = orders[i].items
          .map((item) => `${item.quantity} X (${item.name})`)
          .join(' + ');
        ws.cell(i + 2, 23).string(itemsWithoutModifiersStr);

        const itemsWithSkusStr = orders[i].items
          .map((item) => `${item.quantity} X ([${item.code}]${item.name})`)
          .join(' + ');
        ws.cell(i + 2, 24).string(itemsWithSkusStr);

        const itemsWithPricesStr = orders[i].items
          .map(
            (item) =>
              `${item.quantity} X ([${item.totalAmountAfterDiscount}]${item.name})`,
          )
          .join(' + ');
        ws.cell(i + 2, 25).string(itemsWithPricesStr);
      }
      if (orders[i].deliveryLocation) {
        ws.cell(i + 2, 26).string(orders[i].deliveryLocation.city?.toString());
        ws.cell(i + 2, 27).string(orders[i].deliveryLocation.area?.toString());
      }

      if (orders[i]['is_gift']) {
        ws.cell(i + 2, 28).string(orders[i]['recipient_name']);
        ws.cell(i + 2, 29).string(
          (orders[i]['recipient_country_code'] ?? '') +
            orders[i]['recipient_phone'],
        );
        ws.cell(i + 2, 30).string(orders[i]['cardMessage']);
      }

      if (orders[i]['createdBy']) {
        ws.cell(i + 2, 31).string(orders[i]['createdBy']['name']);
      }
    }

    const fileName =
      company.name +
      '-Orders-' +
      moment().format('DD-MM-YYYY HH-mm-ss').toString() +
      '.xlsx';

    await this.googleCloudStorageService.uploadDocument(
      await wb.writeToBuffer(),
      fileName,
    );

    return fileName;
  }

  async assignOrderToBranch(
    orderToAssign: OrderToAssign,
    currentUser: CurrentUser,
  ) {
    const order = await this.checkOrderExisting(orderToAssign.order_id);

    if (order.status == OrderStatusEnum.CANCELED) {
      throw new BadRequestException(
        "You Can't re-assign this order to Branch because it's already Canceled",
        responseCode.MISSING_DATA.toString(),
      );
    }
    const branch = await this.checkBranchExisting(
      orderToAssign.branch,
      order.company,
    );
    order.branch_object = new Types.ObjectId(orderToAssign.branch) as any;
    order.branch = branch as any;
    order.assignedAt = moment().utc().toDate();
    order.last_status_update = moment().toDate();
    branch.total_orders = branch.total_orders + 1;
    branch.total_revenue = branch.total_revenue + (order.total_amount || 0);
    if (order.payment_code) {
      await this.orderPaymentService.assignPaymentToBranch(
        branch._id.toString(),
        order.payment_code,
        undefined,
      );
    }
    order.pickupLocationId = branch.locationId;
    order.assignedBy = currentUser;
    order.branchName = branch.name;

    const company = await this.companyService.get_details(order.company);

    order.isAcknowledged =
      company.acknowledgementScreenType !== AcknowledgementType.MANUAL;

    await Promise.all([branch.save(), order.save()]);
    order.company = company as any;

    await this.orderStatusService.changeOrderStatus(
      order,
      OrderStatusEnum.PENDING,
      OrderTransitionTrigger.ASSIGNED_TO_BRANCH,
      currentUser as any,
    );

    await this.orderLogService.saveOrderLog(
      order,
      { requestedObject: orderToAssign },
      { responseObject: { branch, order } },
      OrderLogActionEnum.ON_ORDER_ASSIGNED_TO_BRANCH,
      currentUser,
    );

    return order;
  }

  async rejectOrderFromBranch(orderToReject: OrderToReject) {
    const order = await this.checkOrderExisting(orderToReject.order_id);

    order.last_status_update = moment().toDate();
    order.branch = undefined;
    order.branch_object = undefined;
    order.branchName = undefined;
    order.isAcknowledged = false;

    const branch = await this.checkBranchExisting(
      orderToReject.branch,
      order.company,
    );
    branch.total_orders = branch.total_orders - 1;
    branch.total_revenue = branch.total_revenue - (order.total_amount || 0);
    const branchRejection: OrderBranchRejection = {
      currentUser: orderToReject.currentUser,
      branch: { _id: branch._id, name: branch.name },
      createdAt: moment.utc().toDate(),
      rejectionReason: orderToReject.comment,
    };
    order.branchRejections = order.branchRejections
      ? order.branchRejections.concat([branchRejection])
      : [branchRejection];

    const promisedOrderBranch = await Promise.all([
      branch.save(),
      order.save(),
    ]);

    await this.orderStatusService.changeOrderStatus(
      order,
      OrderStatusEnum.UNASSIGNED,
      OrderTransitionTrigger.REJECTED,
      orderToReject.currentUser,
    );

    await this.orderLogService.saveOrderLog(
      order,
      { requestedObject: orderToReject },
      { responseObject: promisedOrderBranch },
      OrderLogActionEnum.ON_ORDER_REJECTED,
      orderToReject.currentUser,
    );

    return order;
  }

  async cancelOrder({
    order_id,
    orderRef,
    currentUser,
    comment,
  }: OrderToCancel) {
    const filterObj: FilterQuery<Order> = {
      $or: [{ _id: order_id }, { code: orderRef }],
    } as any;

    if (isObjectIdOrHexString(orderRef))
      filterObj.$or.push({ _id: new Types.ObjectId(orderRef) });

    const order = await this.orderModel.findOne(filterObj);

    if (!order)
      throw new NotFoundException(
        'Order Not Found',
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    if (order.status == OrderStatusEnum.CANCELED) {
      throw new BadRequestException(
        'The Order is already Canceled',
        responseCode.STATUS_NOT_VALID.toString(),
      );
    }

    order.cancellation = {
      cancelledAt: moment.utc().toDate(),
      cancelledBy: currentUser,
      previousStatus: order.status,
      reason: comment,
    };
    order.deletedBy = currentUser;
    await this.orderStatusService.changeOrderStatus(
      order,
      OrderStatusEnum.CANCELED,
      OrderTransitionTrigger.CANCEL,
      currentUser as any,
    );
    order.last_status_update = moment().toDate();
    await order.save();
    const selectedCompany = await this.companyService.get_details(
      order.company,
    );
    selectedCompany.total_revenue =
      selectedCompany.total_revenue - (order.total_amount || 0);

    await selectedCompany.save();

    return order;
  }

  async checkOrderExisting(orderId: Types.ObjectId): Promise<OrderDocument> {
    const order = await this.orderModel.findOne({
      _id: orderId,
    });
    if (!order)
      throw new NotFoundException(
        'Order Not Found',
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    return order;
  }

  async update_delivery_location(
    orderDeliveryAddressToUpdate: OrderDeliveryAddressToUpdate,
  ) {
    const filterData = {
      $or: [{ code: orderDeliveryAddressToUpdate.order_id }],
      deletedAt: null,
    } as any;
    if (isValidObjectId(orderDeliveryAddressToUpdate.order_id)) {
      filterData.$or.push({
        _id: new Types.ObjectId(orderDeliveryAddressToUpdate.order_id),
      });
    }
    const order = await this.orderModel.findOne(filterData);
    const company = await this.companyService.get_details(order.company);
    const customer = await this.customerReadService.findOne(
      order.customer.toHexString(),
      company._id,
    );
    if (
      orderDeliveryAddressToUpdate.location &&
      !orderDeliveryAddressToUpdate.locationId &&
      !orderDeliveryAddressToUpdate.location['_id']
    ) {
      orderDeliveryAddressToUpdate.location.customerId = customer._id;
      const location = await this.savedLocationService.create(
        orderDeliveryAddressToUpdate.location,
      );

      order.deliveryLocationId = location._id;
      order.deliveryLocation = location;

      await this.orderNotificationService.onDeliveryLocationUpdateSuccess(
        order,
      );

      // check if there is a new slot_delivery_id
      if (orderDeliveryAddressToUpdate.delivery_slot_id) {
        const selectedSlot = await this.slotService.get_details(
          orderDeliveryAddressToUpdate.delivery_slot_id,
        );
        order.delivery_slot_from = selectedSlot.from;
        order.delivery_slot_to = selectedSlot.to;
        order.delivery_slot = new Types.ObjectId(
          orderDeliveryAddressToUpdate.delivery_slot_id,
        ) as any;
        //Checking if the user change the delivery date
        if (
          orderDeliveryAddressToUpdate.delivery_date !=
          moment.utc(order.delivery_date).format('YYYY-MM-DD')
        ) {
          const deliveryDate =
            orderDeliveryAddressToUpdate.delivery_date +
            ' ' +
            order.delivery_slot_to;
          order.delivery_date = moment.utc(deliveryDate).toDate();

          order.pickup_date = moment
            .utc(order.delivery_date)
            .subtract(3, 'hours')
            .toDate();
        }
      }
      await location.save();
    } else if (
      orderDeliveryAddressToUpdate.location &&
      orderDeliveryAddressToUpdate.location['_id'] &&
      !orderDeliveryAddressToUpdate.locationId
    ) {
      orderDeliveryAddressToUpdate.location['_id'] = new Types.ObjectId(
        orderDeliveryAddressToUpdate.location['_id'],
      );
      const location = await this.savedLocationService.update(
        orderDeliveryAddressToUpdate.location as any,
      );
      order.deliveryLocationId = location._id;
    } else {
      const location = await this.savedLocationService.getDetails(
        orderDeliveryAddressToUpdate.locationId as any,
      );
      order.deliveryLocationId = new Types.ObjectId(
        orderDeliveryAddressToUpdate.locationId,
      );
      order.deliveryLocation = location;
    }
    order.delivery_action = OrderDeliveryAction.DELIVERY_LOCATION;

    if (
      !order.branch &&
      company.branchAssignmentConfig.branchAssignmentScheme ===
        BranchAssignmentScheme.NEAREST
    ) {
      const location = await this.savedLocationService.getDetails(
        order.deliveryLocationId.toHexString(),
      );
      if (location.latitude && location.longitude) {
        const branch = await this.branchService.getNearestBranch(
          location.latitude,
          location.longitude,
          company._id,
          [],
        );
        order.branch = branch;
        if (order.status === OrderStatusEnum.UNASSIGNED) {
          this.orderStatusService.changeOrderStatus(
            order,
            OrderStatusEnum.PENDING,
            OrderTransitionTrigger.ASSIGNED_TO_BRANCH,
            orderDeliveryAddressToUpdate?.current_user,
          );
        }
      }
    }

    const saveOrder = this.orderModel.updateOne(
      { _id: order._id },
      { $set: pick(order, order.modifiedPaths() as any) },
    );
    await Promise.all([saveOrder, customer.save()]);
    const branch = order.branch
      ? await this.branchService.get_details(order.branch['_id'])
      : null;

    /*let thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto = {
              order: order,
              customer: customer,
              branch: branch,
              company: company,
              pickupLocation: order.pickupLocation,
              currentUser: order.createdBy,
            };*/
    // Update Or Create the Tookan Task
    if (
      order.deliveryMethod &&
      order.deliveryMethod != DeliveryMethod.THIRD_PARTY &&
      order.branch
    ) {
      const driver = await this.driverService.get_details(
        order.driver ? order.driver.toHexString() : undefined,
      );
      await this.orderDeliveryService.tookanTaskProcessing({
        order,
        customer,
        branch,
        company,
        driver,
      });
    }
    /*else if (
              order.drivers_type == 'thirdParty' ||
              (company.defaultDeliveryMethod &&
                company.deliveryParty.length == 1 &&
                thirdPartyTaskCreationDto.company.usingEbDelivery == false &&
                thirdPartyTaskCreationDto.company.usingCompanyDrivers == false &&
                thirdPartyTaskCreationDto.company.hasOwnDrivers == false &&
                thirdPartyTaskCreationDto.company.using3rdPartyDelivery == true)
            ) {
              await this.orderDeliveryService.handleThirdPartyTasks(
                order.drivers_type == 'thirdParty'
                  ? order.deliveryParty
                  : thirdPartyTaskCreationDto.company.deliveryParty[0],
                thirdPartyTaskCreationDto,
              );
            }*/

    await this.orderLogService.saveOrderLog(
      order,
      { requestedObject: {} },
      { responseObject: orderDeliveryAddressToUpdate },
      OrderLogActionEnum.ORDER_DELIVERY_LOCATION_UPDATED,
      orderDeliveryAddressToUpdate.current_user,
    );
    return order;
  }

  async onOrderReady(
    { order_id, deliveryOptions }: ReadyOrderDto,
    currentUser: CurrentUser,
  ) {
    this.logger.log('[onOrderReady]deliveryOptions', deliveryOptions);
    const order = await this.orderModel.findOne({ _id: order_id });
    const companyId = order['company']['_id']
      ? order['company']['_id']
      : order['company'];
    const company = await this.companyService.get_details(companyId);
    if (!order) {
      throw new NotFoundException(
        `Order with id ${order_id} not found. Please provide a valid order id.`,
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    }
    this.logger.log('[onOrderReady] order', order);

    if (company.type !== CompanyType.RESTAURANT && deliveryOptions)
      this.updateOrderDates(order, deliveryOptions);

    this.checkMarkAsReadyApplicability(order, deliveryOptions);

    if (this.doesOrderNeedDelivery(order)) {
      const canUseDeliveryOptions = this.getCanUseDeliveryOptions(
        order,
        deliveryOptions,
      );
      this.logger.log(
        '[onOrderReady] canUseDeliveryOptions',
        canUseDeliveryOptions,
      );

      if (canUseDeliveryOptions) {
        this.logger.log('[onOrderReady] before can Delivery Task Created');
        this.setOrderDeliveryMethods(order, deliveryOptions);
        if (this.canDeliveryTaskCreated(order)) {
          if (deliveryOptions.assignmentType) {
            this.handleOrderAssignmentType(
              order,
              deliveryOptions as Required<DeliveryOptionsDto>,
            );
          }

          await this.orderDeliveryService.handleOrderDeliveryMethodCreation({
            order,
            currentUser,
            thirdParty: deliveryOptions.thirdParty,
            deliveryMethod: deliveryOptions.deliveryMethod,
            driverId: deliveryOptions.driverId,
          });
        }
      }
    }
    if (order.status === OrderStatusEnum.SCHEDULED && !order.isOperable) {
      order.isOperable = true;
      await this.orderDeliveryService.handleOrderDeliveryMethodCreation({
        order: order,
        currentUser: order.createdBy,
        thirdParty: order.deliveryParty,
        deliveryMethod: order.deliveryMethod,
        driverId: order.driver,
      });
    }
    order.is_ready = true;
    await order.save();
    await this.orderStatusService.changeOrderStatus(
      order,
      OrderStatusEnum.PENDING_PICKUP,
      OrderTransitionTrigger.READY,
      {} as any,
    );
    await this.orderNotificationService.fireOrderNotificationTrigger(
      order,
      TriggerAction.ON_ORDER_READY_FOR_PICKUP,
    );

    return order;
  }

  async assignDriverToOrder(
    driverAssignToOrder: DriverAssignToOrder,
    currentUser: CurrentUser,
  ) {
    let selectedDriver = await this.driverService.get_details(
      driverAssignToOrder.driver,
    );

    const order = await this.orderModel.findOne({
      _id: driverAssignToOrder.order,
    });

    if (order.status == OrderStatusEnum.CANCELED) {
      throw new BadRequestException(
        "You Can't re-assign this order to Driver because it's already Canceled",
        responseCode.MISSING_DATA.toString(),
      );
    }

    if (!selectedDriver) {
      selectedDriver = await this.driverService.get_details(
        order.driver ? order.driver.toHexString() : undefined,
      );
    }

    if (selectedDriver) {
      order.driverManuallyAssigned = true;
      order.driver = selectedDriver._id;
      order.assigned_driver_name =
        selectedDriver.first_name + ' ' + selectedDriver.last_name;
    }
    await order.save();
    await this.orderLogService.saveOrderLog(
      order,
      { requestedObject: {} },
      { responseObject: driverAssignToOrder },
      OrderLogActionEnum.ON_ORDER_ASSIGNED_TO_DRIVER,
      currentUser,
    );
    return selectedDriver;
  }

  async changeDeliveryMethod(
    orderDriverToChange: OrderDriverToChange,
    currentUser: CurrentUser,
  ) {
    const order = await this.orderModel.findOne({
      _id: orderDriverToChange.orderId,
    });
    if (!order)
      throw new NotFoundException(
        'Order Not Found',
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    let deliveryPartyChanges = false;
    if (orderDriverToChange.deliveryType) {
      order['delivery_type'] = orderDriverToChange.deliveryType;
    }

    if (orderDriverToChange.deliveryMethod) {
      if (order.deliveryMethod != orderDriverToChange.deliveryMethod) {
        deliveryPartyChanges = true;
      }
      order.deliveryMethod = orderDriverToChange.deliveryMethod;
      if (orderDriverToChange.deliveryMethod == DeliveryMethod.THIRD_PARTY) {
        if (order.deliveryParty != orderDriverToChange.thirdParty) {
          deliveryPartyChanges = true;
        }
        order.deliveryParty = orderDriverToChange.thirdParty;
      }
    }

    if (orderDriverToChange.assignmentType) {
      // if (
      //   orderDriverToChange.assignmentType ==
      //   OrderOwnDriversAssignmentType.autoAssign
      // ) {
      //   order.autoAssign = true;
      // } else if (
      //   orderDriverToChange.assignmentType ==
      //   OrderOwnDriversAssignmentType.assignNow
      // ) {
      //   const driver = await this.driverService.get_details(
      //     orderDriverToChange.driverId.toHexString
      //       ? orderDriverToChange.driverId.toHexString()
      //       : (orderDriverToChange.driverId as any),
      //   );
      //
      //   if (!driver) {
      //     return;
      //   }
      //   order.driver = driver._id;
      //   order.assigned_driver_name =
      //     driver.first_name + ' ' + driver.last_name ?? '';
      //   order.driverManuallyAssigned = true;
      // } else {
      //   order.driver = undefined;
      // }
      await this.handleOrderAssignmentType(order, {
        assignmentType: orderDriverToChange.assignmentType,
        driverId: orderDriverToChange.driverId,
      });
    }

    const company = await this.companyService.get_details(order.company);
    const deliveryConfiguration =
      await this.deliveryConfigurationService.findByCompanyId(company._id);
    const customer = await this.customerReadService.findOne(
      order.customer.toHexString(),
      company._id,
    );
    const branch = await this.branchService.get_details(order.branch['_id']);

    const reassignDriver = true;
    const thirdPartyTaskCreationDto: ThirdPartyTaskCreationDto = {
      order: order,
      customer: customer,
      branch: branch as any,
      company: company,
      pickupLocation: order.pickupLocation,
      currentUser: order.createdBy,
    };
    let driver = await this.assignDriverToOrder(
      {
        order: order._id,
        driver:
          orderDriverToChange.deliveryMethod == DeliveryMethod.E_BUTLER
            ? '5fd09b4c35497a3edbe3d6fb'
            : orderDriverToChange.driverId
              ? (orderDriverToChange.driverId as any)
              : '',
        traceId: orderDriverToChange.traceId,
      },
      currentUser,
    );

    if (deliveryPartyChanges) {
      order.deliveryTaskCreated = false;
    }

    if (
      order.deliveryMethod == DeliveryMethod.THIRD_PARTY ||
      (deliveryConfiguration.defaultDeliveryMethod &&
        deliveryConfiguration?.thirdPartyConfiguration?.thirdParties.length ===
          1 &&
        deliveryConfiguration.usingEbDelivery == false &&
        deliveryConfiguration.usingCompanyDrivers == false &&
        deliveryConfiguration.usingBranchDrivers == false &&
        deliveryConfiguration.usingThirdParty == true)
    ) {
      await this.orderDeliveryService.handleThirdPartyTasks(
        orderDriverToChange.thirdParty,
        thirdPartyTaskCreationDto,
      );
    } else if (
      (order.deliveryMethod == DeliveryMethod.BRANCH_DRIVERS ||
        order.deliveryMethod == DeliveryMethod.COMPANY_DRIVERS) &&
      branch
    ) {
      if (order.deliveryMethod == DeliveryMethod.COMPANY_DRIVERS) {
        order.autoAssign = true;
        order.assigned_driver_name = undefined;
        order.driver = undefined;
        driver = undefined;
      }

      await this.orderDeliveryService.tookanTaskProcessing({
        order,
        customer,
        branch,
        company,
        driver,
        reassignDriver,
      });
    }

    if (deliveryPartyChanges) {
      order.deliveryTaskCreated = false;
    }
    order.vehicleType = orderDriverToChange.vehicleType ?? order.vehicleType;
    await order.save();
    await this.orderLogService.saveOrderLog(
      order,
      { requestedObject: order },
      { responseObject: orderDriverToChange },
      OrderLogActionEnum.ON_ORDER_DELIVERY_METHOD_CHANGES,
      currentUser,
    );
    return 'DONE';
  }

  async editOrderDispatch(
    orderDispatchToEdit: OrderDispatchToEdit,
    company_id: Types.ObjectId,
  ) {
    const order = await this.orderModel.findOne({
      _id: orderDispatchToEdit.order,
    });

    if (company_id && order.company.toString() !== company_id.toString()) {
      throw {
        code: responseCode.MISSING_DATA,
        statusCode: 422,
        message: "You don't have access to this company orders",
      };
    }

    const companyId = order['company']['_id']
      ? order['company']['_id']
      : order['company'];
    const company = await this.companyService.get_details(companyId);
    order.deliveryMethod = orderDispatchToEdit.deliveryMethod;

    const newDeliveryDate = this.getNewDate(
      order.delivery_date,
      orderDispatchToEdit.delivery_date,
      orderDispatchToEdit.delivery_time,
    );
    const newPickupDate = this.getNewDate(
      order.pickup_date,
      orderDispatchToEdit.pickup_date,
      orderDispatchToEdit.pickup_time,
    );

    const canUpdateDates =
      company.type !== CompanyType.RESTAURANT ||
      (company.type === CompanyType.RESTAURANT &&
        order.delivery_type === OrderDeliveryType.scheduled);
    if (canUpdateDates) {
      order.pickup_date = newPickupDate;
      const oldDeliveryDate = order.delivery_date;
      order.delivery_date = newDeliveryDate;
      order.delivery_time = moment(newDeliveryDate).format('HH:mm');

      if (
        !oldDeliveryDate ||
        oldDeliveryDate?.valueOf() !== newDeliveryDate?.valueOf()
      )
        await this.orderNotificationService.onDeliveryDateHasBeenUpdated(order);
    }

    if (
      orderDispatchToEdit.delivery_slot_id &&
      orderDispatchToEdit.delivery_slot_id.toString() !==
        order.delivery_slot?.toString()
    ) {
      await this.slotService.reserveSlotToOrder(
        order,
        order.delivery_date,
        orderDispatchToEdit.delivery_slot_id,
      );
    }

    order.delivery_type = orderDispatchToEdit.delivery_type;

    const driverId = new Types.ObjectId(orderDispatchToEdit.driver_id);
    if (orderDispatchToEdit.driver_id && driverId != order.driver) {
      const driver = await this.driverService.get_details(
        orderDispatchToEdit.driver_id,
      );
      order.driver = driverId;
      order.driverManuallyAssigned = true;
      order.assigned_driver_name = driver.first_name + ' ' + driver.last_name;
    }

    // Order delivery Changes to send_sms [Update Order Delivery Action]
    if (
      order.delivery_action != OrderDeliveryAction.SEND_SMS &&
      orderDispatchToEdit.delivery_action == OrderDeliveryAction.SEND_SMS
    ) {
      //delivery location sms logic here
      await this.orderNotificationService.handleDeliveryAndPaymentNotification(
        order,
      );
    }

    if (
      orderDispatchToEdit.delivery_action == OrderDeliveryAction.IN_STORE_PICKUP
    ) {
      order.deliveryLocationId = undefined;
      order.deliveryLocation = undefined;
    }

    if (orderDispatchToEdit.traceId) {
      if (!order.tracerIds) {
        order.tracerIds = [orderDispatchToEdit.traceId];
      } else if (order.tracerIds.indexOf(orderDispatchToEdit.traceId) == -1) {
        order.tracerIds.push(orderDispatchToEdit.traceId);
      }
    }

    order.delivery_action = orderDispatchToEdit.delivery_action;
    order.updatedBy = orderDispatchToEdit.current_user;
    await order.save();
    await this.orderLogService.saveOrderLog(
      order,
      { requestedObject: {} },
      { responseObject: orderDispatchToEdit },
      OrderLogActionEnum.ON_ORDER_DISPATCH_DETAILS_EDITED,
      orderDispatchToEdit.current_user,
    );
    return order;
  }

  async changePaymentMethod(
    orderPaymentMethodToChange: OrderPaymentMethodToChange,
  ) {
    const order = await this.orderModel
      .findOne({
        _id: orderPaymentMethodToChange.orderId,
      })
      .exec();

    if (!order) {
      throw {
        code: responseCode.BAD_REQUEST,
        statusCode: 400,
        message: 'Please Provide a Correct Id',
      };
    }

    // Fetching the order Related Items
    const branch = order.branch ? order.branch : undefined;
    const company = await this.companyService.get_details(order.company);

    // if the payment is cash and new payment is online
    if (
      order.payment_method != OrderPaymentMethod.online &&
      orderPaymentMethodToChange.paymentMethod == OrderPaymentMethod.online
    ) {
      order.payment_method = OrderPaymentMethod.online;
      await this.orderPaymentService.createOrderPayment(
        order,
        typeof branch != 'string' ? branch : undefined,
        company,
        company.call_back_url,
        order.delivery_action,
        orderPaymentMethodToChange.currentUser,
      );
    }

    // if the payment is online and new payment is cash
    if (
      order.payment_method == OrderPaymentMethod.online &&
      (orderPaymentMethodToChange.paymentMethod == OrderPaymentMethod.cash ||
        orderPaymentMethodToChange.paymentMethod ==
          OrderPaymentMethod.card_machine) &&
      order.payment_status != OrderPaymentStatus.COMPLETED
    ) {
      await this.orderPaymentService.removeOrderFromPayment(
        order,
        orderPaymentMethodToChange.paymentMethod,
        orderPaymentMethodToChange.currentUser,
      );
    }
    // the payment is prepaid
    if (
      order.payment_method != OrderPaymentMethod.card_machine &&
      orderPaymentMethodToChange.paymentMethod == 'card_machine'
    ) {
      order.payment_method = OrderPaymentMethod.card_machine;
    }

    if (
      order.payment_method != OrderPaymentMethod.cash &&
      orderPaymentMethodToChange.paymentMethod == OrderPaymentMethod.cash
    ) {
      order.payment_method = OrderPaymentMethod.cash;
    }

    if (
      orderPaymentMethodToChange.paymentMethod == OrderPaymentMethod.prepaid
    ) {
      order.prepaidBy = orderPaymentMethodToChange.prepaidBy;
      order.payment_method = OrderPaymentMethod.prepaid;
      order.payment_status = OrderPaymentStatus.COMPLETED;
    }

    await order.save();
    await this.orderLogService.saveOrderLog(
      order,
      { requestedObject: {} },
      { responseObject: orderPaymentMethodToChange },
      OrderLogActionEnum.ON_ORDER_PAYMENT_METHOD_CHANGED,
      orderPaymentMethodToChange.currentUser,
    );

    return order;
  }

  async acknowledgeOrder(acknowledgeOrderDto: AcknowledgeOrderDto) {
    const order = await this.orderModel.findOne({
      _id: acknowledgeOrderDto.orderId,
    });
    if (!order) return;
    if (order.isAcknowledged) {
      await this.userService.removeAcknowledgeOrdersFromUsers(
        order,
        'acknowledged',
      );
      throw new BadRequestException(
        'Order Is Already Acknowledged',
        responseCode.ORDER_IS_ACKNOWLEDGED.toString(),
      );
    }
    this.constructOrderDate(order, acknowledgeOrderDto);
    this.setOrderDeliveryMethods(order, acknowledgeOrderDto);
    if (acknowledgeOrderDto.assignmentType) {
      await this.handleOrderAssignmentType(order, {
        assignmentType: acknowledgeOrderDto.assignmentType,
        driverId: acknowledgeOrderDto.driverId,
      });
    }
    if (
      order.delivery_type == OrderDeliveryType.urgent &&
      acknowledgeOrderDto.deliveryMethod
    )
      await this.orderDeliveryService.handleOrderDeliveryMethodCreation({
        order: order,
        currentUser: acknowledgeOrderDto.currentUser,
        thirdParty: acknowledgeOrderDto.thirdParty,
        deliveryMethod: acknowledgeOrderDto.deliveryMethod,
        driverId: acknowledgeOrderDto.driverId,
      });
    order.isAcknowledged = true;
    order.acknowledgedBy = acknowledgeOrderDto.currentUser;
    order.vehicleType = acknowledgeOrderDto.vehicleType ?? order.vehicleType;
    await order.save();
    await this.applyAcknowledgePostFunctions(order, acknowledgeOrderDto);
    return order;
  }

  async refireOrderAcknowledgement(
    refireOrderAcknowledgementDto: RefireOrderAcknowledgementDto,
  ) {
    const { userId, orderId } = refireOrderAcknowledgementDto;

    const user = await this.userService.get_details(userId.toHexString());
    if (!user) return;

    let ordersToSend: string[] = [];

    if (orderId) {
      ordersToSend = await this.processSingleOrder(user, orderId);
    } else {
      ordersToSend = await this.processMultipleOrders(user);
    }

    await this.pusherService.fireEvent(
      user._id.toHexString(),
      'orderAcknowledgeReceived',
      { orders: ordersToSend },
    );

    await this.userService.updateOrdersToBeAcknowledged(
      userId,
      user.ordersToBeAcknowledged,
    );
  }

  @OnEvent('savedLocation.updated')
  @LogError()
  async onSavedLocationUpdated(savedLocation: SavedLocationDocument) {
    const order = await this.orderModel.findOne({
      deliveryLocationId: savedLocation._id,
    });

    if (order) {
      order.deliveryLocation = savedLocation;
      await this.orderModel.updateOne({ _id: order._id }, order);
    }
  }

  @OnEvent('on-Brand-Update')
  @LogError()
  async onBrandUpdate(updatedBrand: Brand, brandId: Types.ObjectId) {
    const startDate = moment().subtract(1, 'month').startOf('day');
    const endDate = moment().add(1, 'day').endOf('day');

    const orders = (
      await this.index({
        brandId: brandId as any,
        createdDateFrom: startDate.format('YYYY-MM-DD'),
        createdDateTo: endDate.format('YYYY-MM-DD'),
      } as any)
    )[0]['paginatedResults'];

    for (let i = 0; i < orders.length; i++) {
      await this.orderModel.updateOne(
        { _id: orders[i]._id },
        {
          $set: {
            brand: {
              name: updatedBrand.name,
              phoneNumber: updatedBrand.phoneNumber,
              image: updatedBrand.image,
              _id: new Types.ObjectId(brandId),
            },
          },
        },
      );
    }
  }

  async findTrackingOrder(uniqueIdentifier: string): Promise<TrackingOrderDto> {
    let order: OrderDocument;
    if (uniqueIdentifier) {
      order = await this.get_details(uniqueIdentifier);
    }
    const statuses = this.getStatusesList(order);
    const paymentLink = this.getPaymentLink(order);
    const eventName = this.getMappedEventName(order);
    const event = this.getMappedEventObject(eventName);
    const status = this.getMappedStatus(order, statuses);
    const trackingLink = this.getTrackingLink(order, status);
    const locationLink = this.getLocationLink(order);
    return {
      orderId: order._id,
      orderCode: order.code,
      invoiceNumber: order.invoice_number,
      trackingLink: trackingLink,
      paymentLink: paymentLink,
      customerName: order.customer_name,
      brand: order.brand,
      orderStatus: status,
      orderEvent: event,
      statuses: statuses,
      deliveryDate: trackingLink ? order.delivery_date : null,
      locationLink,
    };
  }

  async findTrackingOrderForMap(
    orderCode: string,
  ): Promise<TrackingOrderForMapDto> {
    const order = await this.findByIdOrCode(orderCode);
    if (!order) {
      throw new NotFoundException(
        `Order with code ${orderCode} not found`,
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    }

    const company = await this.companyService.findById(
      new Types.ObjectId(order.company),
    );

    const deliveryLocationPromise = order.deliveryLocationId
      ? this.savedLocationService
          .getDetails(order.deliveryLocationId.toString())
          .catch((error) => {
            this.logger.error(
              `Error retrieving delivery location details: ${error.message}`,
              { error },
            );
            return null;
          })
      : Promise.resolve(null);

    const driverPromise = order.driver
      ? this.driverService
          .get_details(order.driver.toString())
          .catch((error) => {
            this.logger.error(
              `Error retrieving driver details: ${error.message}`,
              { error },
            );
            return null;
          })
      : Promise.resolve(null);

    const [deliveryLocation, driver] = await Promise.all([
      deliveryLocationPromise,
      driverPromise,
    ]);

    return {
      orderCode: order.code,
      orderStatus: order.status,
      enableOverlay:
        company?.interfaceConfig?.orderConfig?.trackingPageConfig
          ?.enableOverlay,
      brandingColor:
        company?.interfaceConfig?.orderConfig?.trackingPageConfig
          ?.brandingColor ?? '#214497',
      tookanTrackingUrl:
        order.tookan_delivery_track_url || order.tookan_pickup_track_url,
      selectedLogo:
        company?.interfaceConfig?.orderConfig?.trackingPageConfig
          ?.selectedLogo ?? TrackingPageSelectedLogo.ENABLE,
      countryCode: order.country_code ?? CountryDialCode.QATAR,
      brandLogo: order.brand?.image?.url,
      deliveryLatitude:
        order.deliveryLocation?.latitude ?? deliveryLocation?.latitude,
      deliveryLongitude:
        order.deliveryLocation?.longitude ?? deliveryLocation?.longitude,
      driverName: driver
        ? `${driver.first_name} ${driver.last_name || ''}`.trim()
        : undefined,
      driverPhoneNumber: driver?.phone,
      tookanDriverId: driver?.tookan_driver_id,
    };
  }

  getOrderStatusAsList() {
    // arr of key and value and isManual
    const orderStatues: {
      key: string;
      value: string;
      canTriggeredManually: boolean;
    }[] = [];

    Object.keys(OrderStatusEnum)
      .filter(
        (key) =>
          key !==
          Object.keys(OrderStatusEnum).at(
            Object.values(OrderStatusEnum).findIndex(
              (val) => val === OrderStatusEnum.FAILED,
            ),
          ),
      )
      .map((statusKey) => {
        orderStatues.push({
          key: statusKey,
          value: OrderStatusEnum[statusKey],
          canTriggeredManually: [OrderStatusEnum.COMPLETED].includes(
            OrderStatusEnum[statusKey],
          ),
        });
      });

    return orderStatues;
  }

  async getOrdersCanBeDispatchedToDeliverect() {
    return await this.orderModel
      .find({
        $and: [
          { canSentToDeliverectAsChannel: true },
          { dispatchedToDeliverectChannel: false },
          { deliverectPosOrderId: { $exists: false } },
          { branch: { $exists: true } },
          { 'branch.deliverectChannelId': { $exists: true } },
          { 'branch.deliverectChannelName': { $exists: true } },
          { 'branch.deliverectLocationId': { $exists: true } },
          {
            $or: [
              {
                payment_method: { $ne: OrderPaymentMethod.online },
              },
              {
                payment_method: OrderPaymentMethod.online,
                payment_status: OrderPaymentStatus.COMPLETED,
              },
            ],
          },
          {
            $or: [
              {
                deliveryLocation: { $exists: true },
                delivery_action: OrderDeliveryAction.DELIVERY_LOCATION,
              },
              {
                delivery_action: OrderDeliveryAction.IN_STORE_PICKUP,
              },
            ],
          },
        ],
      })
      .exec();
  }

  async changeOrderPaymentStatus(
    changeOrderPaymentStatusDto: ChangeOrderPaymentStatusDto,
  ): Promise<void> {
    const order = await this.findByIdOrCode(
      changeOrderPaymentStatusDto.uniqueIdentifier,
    );
    if (order) {
      if (
        changeOrderPaymentStatusDto.orderPaymentStatus ==
        OrderPaymentStatus.REFUNDED
      )
        await this.changeOrderPaymentStatusToRefunded(
          order,
          changeOrderPaymentStatusDto.orderPaymentStatus,
        );
      else if (
        order.payment_status == OrderPaymentStatus.COMPLETED &&
        changeOrderPaymentStatusDto.orderPaymentStatus ==
          OrderPaymentStatus.NEEDS_REFUND
      )
        await this.changeOrderPaymentStatusToNeedsRefund(
          order,
          changeOrderPaymentStatusDto.orderPaymentStatus,
        );
      else
        throw new BadRequestException(
          'Please Enter Valid Inputs',
          responseCode.IN_VALID_INPUT.toString(),
        );
    } else
      throw new BadRequestException(
        'Order is not found ! ',
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
  }

  async findByIdOrCode(uniqueIdentifier: string): Promise<OrderDocument> {
    return this.orderModel.findOne(this.buildFindOrderFilter(uniqueIdentifier));
  }

  async findOneByBigCommerceOrderId(
    bigcommerceOrderId: number,
  ): Promise<OrderDocument> {
    return this.orderModel.findOne({
      bigcommerceOrderId,
      deletedAt: null,
    });
  }

  async findByWishboxOrderId(wishboxOrderId: number): Promise<OrderDocument> {
    return await this.orderModel.findOne({ wishboxOrderId: wishboxOrderId });
  }

  @OnEvent('wishbox.updated')
  async updateWishboxDelivery(
    updateWishboxDeliveryStatusDto: UpdateWishboxDeliveryStatusDto,
  ) {
    const order = await this.findByWishboxOrderId(
      updateWishboxDeliveryStatusDto.orderId,
    );

    if (!order)
      throw new NotFoundException(
        `Order with wishbox Id ${updateWishboxDeliveryStatusDto.orderId} Not Found`,
      );

    const wishboxOrderDeliveryStatusMap = {
      [WishboxOrderDeliveryStatusCode.PREPARING]: {
        firstPartyState: FirstPartyTaskState.PENDING,
      },
      [WishboxOrderDeliveryStatusCode.READY_TO_DISPATCH]: {
        firstPartyState: FirstPartyTaskState.PICKUP_ENDED,
      },
      [WishboxOrderDeliveryStatusCode.ON_THE_WAY]: {
        firstPartyState: FirstPartyTaskState.DELIVERY_STARTED,
        orderStatus: OrderStatusEnum.IN_ROUTE,
        orderTransitionTrigger: OrderTransitionTrigger.DELIVERY_TASK_STARTED,
      },
      [WishboxOrderDeliveryStatusCode.DELIVERED]: {
        firstPartyState: FirstPartyTaskState.DELIVERY_ENDED,
        orderStatus: OrderStatusEnum.COMPLETED,
        orderTransitionTrigger: OrderTransitionTrigger.THIRD_PARTY_DELIVERED,
      },
      [WishboxOrderDeliveryStatusCode.CANCELLED]: {
        assigned_driver_name: 'Wishbox Failed',
      },
    };

    const {
      firstPartyState,
      orderStatus,
      assigned_driver_name,
      orderTransitionTrigger,
    } =
      wishboxOrderDeliveryStatusMap[
        updateWishboxDeliveryStatusDto.status.code
      ] || {};

    if (firstPartyState) order.firstPartyState = firstPartyState;

    if (orderStatus && orderTransitionTrigger)
      await this.orderStatusService.changeOrderStatus(
        order,
        orderStatus,
        orderTransitionTrigger,
        order.createdBy,
      );

    if (assigned_driver_name) order.assigned_driver_name = assigned_driver_name;

    await order.save();
  }

  @OnEvent('falcon.flex.updated')
  async updateFalconFlexDeliveryTaskStatus(
    callbackFalconFlexDto: CallbackFalconFlexDto,
  ) {
    const falconFlexId =
      callbackFalconFlexDto.Data?.Id ?? callbackFalconFlexDto.Data?.Task?.Id;
    const order = await this.orderModel.findOne({
      falconFlexTaskId: falconFlexId,
    });

    if (!order)
      throw new NotFoundException(
        `Order with Falcon Flex Id ${falconFlexId} Not Found`,
      );

    const falconFlexOrderDeliveryStatusMap = {
      [FalconFlexTaskStatus.UN_PLANNED]: {
        firstPartyState: FirstPartyTaskState.PENDING,
        assigned_driver_name: '[Snoonu] Pending',
      },
      [FalconFlexTaskStatus.IN_PROGRESS]: {
        assigned_driver_name: '[Snoonu] Driver Assigned',
      },
      [FalconFlexTaskStatus.DELIVERING]: {
        firstPartyState: FirstPartyTaskState.DELIVERY_STARTED,
        orderStatus: OrderStatusEnum.IN_ROUTE,
        orderTransitionTrigger: OrderTransitionTrigger.DELIVERY_TASK_STARTED,
        assigned_driver_name: '[Snoonu] Delivering',
      },
      [FalconFlexTaskStatus.DELIVERED]: {
        firstPartyState: FirstPartyTaskState.DELIVERY_ENDED,
        orderStatus: OrderStatusEnum.COMPLETED,
        orderTransitionTrigger: OrderTransitionTrigger.THIRD_PARTY_DELIVERED,
        assigned_driver_name: '[Snoonu] Delivered',
      },
      [FalconFlexTaskStatus.CANCELLED]: {
        assigned_driver_name: '[Snoonu] Cancelled',
      },
    };

    let {
      firstPartyState,
      // eslint-disable-next-line prefer-const
      orderStatus,
      assigned_driver_name,
      // eslint-disable-next-line prefer-const
      orderTransitionTrigger,
    } =
      falconFlexOrderDeliveryStatusMap[
        callbackFalconFlexDto.Data?.TaskStatus?.toString()
      ] || {};

    if (
      callbackFalconFlexDto.CallBackType ===
      FalconFlexCallBackType.AGENT_ARRIVED_AT_STEP
    ) {
      if (callbackFalconFlexDto.Data.TripStepTypeId === 1) {
        firstPartyState = FirstPartyTaskState.PICKUP_STARTED;
        assigned_driver_name = '[Snoonu] Picking Up';
      } else if (callbackFalconFlexDto.Data.TripStepTypeId === 2) {
        assigned_driver_name = '[Snoonu] Arrived';
      }
    }

    if (firstPartyState) order.firstPartyState = firstPartyState;

    if (orderStatus && orderTransitionTrigger)
      await this.orderStatusService.changeOrderStatus(
        order,
        orderStatus,
        orderTransitionTrigger,
        order.createdBy,
      );

    if (assigned_driver_name) order.assigned_driver_name = assigned_driver_name;

    await order.save();
  }

  @OnEvent('customer.check.active.orders')
  async checkActiveOrdersByCustomer(customer: CustomerDocument): Promise<void> {
    const orderIndex: OrderIndex = {
      company: customer.company.toHexString(),
      customer: customer._id.toHexString(),
      includeCapturedOrders: true,
      includeAggregatorOrders: true,
      order_status: [
        OrderStatusEnum.UNASSIGNED,
        OrderStatusEnum.PENDING,
        OrderStatusEnum.SCHEDULED,
        OrderStatusEnum.PREPARING,
        OrderStatusEnum.PENDING_PICKUP,
        OrderStatusEnum.IN_ROUTE,
      ],
    };

    const orders = await this.index(orderIndex);
    const activeOrders = orders[0]['paginatedResults'];
    if (activeOrders && activeOrders.length > 0)
      throw new BadRequestException(
        'Can not Delete a customer with active orders',
        responseCode.FAIL_TO_DELETE.toString(),
      );
  }

  async findOneByMicrosOrderId(microsOrderId: string): Promise<OrderDocument> {
    return await this.orderModel.findOne({ microsOrderId });
  }

  private canDeliveryTaskCreated(order: OrderDocument): boolean {
    return (
      !order.deliveryTaskCreated ||
      (order.delivery_type != OrderDeliveryType.urgent && !order.isOperable) ||
      (order.deliveryTaskCreated && !order.driver)
    );
  }

  private async processSingleOrder(
    user: UserDocument,
    orderId: Types.ObjectId,
  ): Promise<string[]> {
    const order = await this.get_details(orderId.toHexString());
    if (!order) {
      throw new NotFoundException(
        'Order not found',
        responseCode.ENTITY_NOT_FOUND.toString(),
      );
    }
    if (order.isAcknowledged) {
      throw new BadRequestException(
        'Order already acknowledged',
        responseCode.ORDER_IS_ACKNOWLEDGED.toString(),
      );
    }

    if (!this.isOrderInBuffer(user, orderId)) {
      throw new BadRequestException(
        'Order is not in the buffer',
        responseCode.IN_VALID_INPUT.toString(),
      );
    }

    return [order._id.toString()];
  }

  private isOrderInBuffer(
    user: UserDocument,
    orderId: Types.ObjectId,
  ): boolean {
    if (!user.ordersToBeAcknowledged) return false;
    return user.ordersToBeAcknowledged.some(
      (bufferOrder) => bufferOrder.orderId.toString() === orderId.toString(),
    );
  }

  private async processMultipleOrders(user: any): Promise<string[]> {
    this.validateOrdersToBeAcknowledged(user);

    const orderIds = user.ordersToBeAcknowledged.map((order) => order.orderId);
    const validOrdersToRefire = await this.fetchValidOrdersForRefire(orderIds);

    if (!validOrdersToRefire.length) {
      throw new BadRequestException(
        'No valid orders to Refire Acknowledgment',
        responseCode.BAD_REQUEST.toString(),
      );
    }

    return this.filterValidOrders(user, validOrdersToRefire);
  }

  private validateOrdersToBeAcknowledged(user: any): void {
    if (!user.ordersToBeAcknowledged || !user.ordersToBeAcknowledged.length) {
      throw new BadRequestException(
        'No orders to acknowledge',
        responseCode.BAD_REQUEST.toString(),
      );
    }
  }

  private async fetchValidOrdersForRefire(
    orderIds: Types.ObjectId[],
  ): Promise<OrderDocument[]> {
    return this.orderModel.find({
      _id: { $in: orderIds },
      status: { $ne: OrderStatusEnum.CANCELED },
      deletedAt: { $exists: false },
      isAcknowledged: { $ne: true },
    });
  }

  private filterValidOrders(
    user: UserDocument,
    validOrdersToRefire: OrderDocument[],
  ): string[] {
    const validOrderIds = new Set(
      validOrdersToRefire.map((validOrderToRefire) =>
        validOrderToRefire._id.toString(),
      ),
    );
    user.ordersToBeAcknowledged = user.ordersToBeAcknowledged.filter(
      (orderToBeAcknowledged) =>
        validOrderIds.has(orderToBeAcknowledged.orderId.toString()),
    );

    return Array.from(validOrderIds);
  }

  private buildFindOrderFilter(
    uniqueIdentifier: string | Types.ObjectId,
  ): FilterQuery<Order> {
    const filter: FilterQuery<Order> = { deletedAt: null };

    if (isObjectIdOrHexString(uniqueIdentifier)) {
      filter._id = new Types.ObjectId(uniqueIdentifier);
    } else {
      filter.code = uniqueIdentifier;
    }

    return filter;
  }

  private async checkBranchExisting(
    branchId: Types.ObjectId,
    companyId: string | Types.ObjectId,
  ): Promise<BranchDocument> {
    const branch = await this.branchService.findById(branchId);
    if (!branch)
      throw new NotFoundException(
        'Branch Not Found',
        responseCode.ENTITY_NOT_FOUND.toString(),
      );

    if (!areObjectIdsEqual(branch.company, companyId))
      throw new BadRequestException('Branch is not under the same company');

    return branch;
  }

  private checkMarkAsReadyApplicability(
    order: OrderDocument,
    deliveryOptions?: DeliveryOptionsDto,
  ) {
    if (
      (order.delivery_action === OrderDeliveryAction.DELIVERY_LOCATION ||
        order.delivery_action === OrderDeliveryAction.SEND_SMS) &&
      !(order.deliveryMethod || deliveryOptions?.deliveryMethod)
    ) {
      throw new BadRequestException(
        'Can not mark the order with ID ' + order._id + ' as ready',
        responseCode.IN_VALID_INPUT.toString(),
      );
    }
    if (order.status === OrderStatusEnum.SCHEDULED && !order.isOperable) return;
    else if (
      order.isAcknowledged === false ||
      order.status !== OrderStatusEnum.PREPARING
    )
      throw new BadRequestException(
        'Can not mark the order with ID ' + order._id + ' as ready',
        responseCode.IN_VALID_INPUT.toString(),
      );
  }

  private updateOrderDates(
    order: OrderDocument,
    deliveryOptions: DeliveryOptionsDto,
  ): void {
    this.validateDatesInDeliveryOptionsForOrder(order, deliveryOptions);
    if (
      deliveryOptions.delivery_date &&
      order.delivery_action !== OrderDeliveryAction.IN_STORE_PICKUP
    )
      order.delivery_date = moment
        .utc(deliveryOptions.delivery_date, 'YYYY-MM-DD HH:mm')
        .toDate() as any;

    if (
      deliveryOptions.pickup_date &&
      order.delivery_action === OrderDeliveryAction.IN_STORE_PICKUP
    )
      order.pickup_date = moment
        .utc(deliveryOptions.pickup_date, 'YYYY-MM-DD HH:mm')
        .toDate() as any;
  }

  private validateDatesInDeliveryOptionsForOrder(
    order: OrderDocument,
    deliveryOptions: DeliveryOptionsDto,
  ): void {
    const orderHasPickupDate = !!order.pickup_date;
    const orderNeedsPickupDate =
      order.delivery_action === OrderDeliveryAction.IN_STORE_PICKUP &&
      !orderHasPickupDate;

    if (orderNeedsPickupDate && !deliveryOptions?.pickup_date) {
      throw new BadRequestException(
        "Since pickup order does not have pickup_date set, 'deliveryOptions.pickup_date' is required.",
        responseCode.BAD_REQUEST.toString(),
      );
    }

    const orderHasDeliveryDate = !!order.delivery_date;
    const orderNeedsDeliveryDate =
      order.delivery_action !== OrderDeliveryAction.IN_STORE_PICKUP &&
      !orderHasDeliveryDate;

    if (orderNeedsDeliveryDate && !deliveryOptions?.delivery_date) {
      throw new BadRequestException(
        "Since order does not have delivery_date set, 'deliveryOptions.delivery_date' is required.",
        responseCode.BAD_REQUEST.toString(),
      );
    }
  }

  private getNewDate(
    currentDate: Date,
    newDate: string, // YYYY-MM-DD
    newTime: string, // HH:mm
  ): Date {
    if (newDate && newTime)
      return moment.utc(newDate + ' ' + newTime, 'YYYY-MM-DD HH:mm').toDate();

    if (newTime) {
      const oldDate = moment.utc(currentDate).format('YYYY-MM-DD');
      return moment.utc(oldDate + ' ' + newTime, 'YYYY-MM-DD HH:mm').toDate();
    }

    if (newDate) {
      const oldTime = moment.utc(currentDate).format('HH:mm');
      return moment.utc(newDate + ' ' + oldTime, 'YYYY-MM-DD HH:mm').toDate();
    }

    return currentDate;
  }

  // as UTC. These old orders need to be updated to UTC time.
  private async updateTimezones(
    orders: OrderDocument[],
  ): Promise<OrderDocument[]> {
    if (!orders || orders.length == 0) return orders;

    const company = await this.companyService.get_details(
      (orders[0].company as any)?._id ?? orders[0].company,
    );
    if (!company || !company.localization?.timezone) return orders;

    const isCreatedBeforeLocalizationUpdate = (order: OrderDocument): boolean =>
      !order.localization?.timezone;

    await this.updateDatabaseTimezones(
      orders.filter(isCreatedBeforeLocalizationUpdate),
      company.localization,
    );

    const toUtcTime = (order: OrderDocument) => {
      if (!isCreatedBeforeLocalizationUpdate(order)) return order;

      if (order.pickup_date)
        order.pickup_date = moment(order.pickup_date)
          .subtract(3, 'hours')
          .toDate();

      if (order.delivery_date)
        order.delivery_date = moment(order.delivery_date)
          .subtract(3, 'hours')
          .toDate();

      order.localization = company.localization;
      return order;
    };

    return orders.map(toUtcTime);
  }

  private async updateDatabaseTimezones(
    orders: OrderDocument[],
    localization: EnableLocalization,
  ) {
    const ThreeHoursInMilliseconds = 1000 * 60 * 60 * 3;
    await this.orderModel.updateMany(
      {
        _id: {
          $in: orders.map((order) => order._id),
        },
      },
      [
        {
          $set: {
            localization,
            pickup_date: {
              $subtract: ['$pickup_date', ThreeHoursInMilliseconds],
            },
            delivery_date: {
              $subtract: ['$delivery_date', ThreeHoursInMilliseconds],
            },
          },
        },
      ],
    );
  }

  private constructOrderDate(
    order: OrderDocument,
    acknowledgeOrderDto: AcknowledgeOrderDto,
  ) {
    if (order.delivery_type == OrderDeliveryType.urgent)
      this.constructUrgentOrderDates(
        order,
        acknowledgeOrderDto.preparationTime,
      );
  }

  //TODO: move this function to helper service
  private constructUrgentOrderDates(
    order: OrderDocument,
    preparationTime: number,
  ) {
    const pickupDate = moment()
      .utc()
      .add(preparationTime, 'minutes')
      .format('YYYY-MM-DD HH:mm');
    order.pickup_date = moment.utc(pickupDate, 'YYYY-MM-DD HH:mm').toDate();
    if (order.branch) {
      const deliveryDate = moment(pickupDate);
      if (order.delivery_action != OrderDeliveryAction.IN_STORE_PICKUP) {
        deliveryDate.add(
          order['branch']['callCenterConfig']
            ? order['branch']['callCenterConfig']['currentlyDeliveringIn']
            : 0,
          'minutes',
        );
        order.delivery_date = moment
          .utc(deliveryDate.format('YYYY-MM-DD HH:mm'), 'YYYY-MM-DD HH:mm')
          .toDate();
      } else order.delivery_date = undefined;
    }
  }

  private constructScheduledAndDeliveryOrderPickupDate(order: OrderDocument) {
    const deliveryDate = moment.utc(order.delivery_date);
    const pickupDate = deliveryDate.subtract(30, 'minutes');
    order.pickup_date = moment
      .utc(pickupDate.format('YYYY-MM-DD HH:mm'), 'YYYY-MM-DD HH:mm')
      .toDate();
  }

  private async applyAcknowledgePostFunctions(
    order: OrderDocument,
    acknowledgeOrderDto: AcknowledgeOrderDto,
  ) {
    if (order.delivery_type == 'urgent')
      await this.orderStatusService.changeOrderStatus(
        order,
        OrderStatusEnum.PREPARING,
        OrderTransitionTrigger.ACKNOWLEDGED,
        acknowledgeOrderDto.currentUser,
      );
    else
      await this.orderStatusService.changeOrderStatus(
        order,
        OrderStatusEnum.SCHEDULED,
        OrderTransitionTrigger.ACKNOWLEDGED,
        acknowledgeOrderDto.currentUser,
      );

    await this.orderNotificationService.onOrderAcknowledged(
      order,
      acknowledgeOrderDto,
    );

    await this.orderLogService.saveOrderLog(
      order,
      { requestedObject: acknowledgeOrderDto },
      {},
      OrderLogActionEnum.ON_ORDER_ACKNOWLEDGE,
      acknowledgeOrderDto.currentUser,
    );
  }

  private setOrderDeliveryMethods(
    order: OrderDocument,
    deliveryOptions: Partial<DeliveryOptionsDto>,
  ) {
    if (deliveryOptions.deliveryType)
      order.delivery_type = OrderDeliveryType[deliveryOptions.deliveryType];

    if (deliveryOptions.deliveryMethod)
      this.setOrderDeliveryParty(
        order,
        deliveryOptions as Required<DeliveryOptionsDto>,
      );
  }

  private async handleOrderAssignmentType(
    order: OrderDocument,
    {
      assignmentType,
      driverId,
    }: Pick<DeliveryOptionsDto, 'assignmentType' | 'driverId'>,
  ) {
    order.assignmentType = assignmentType;
    if (assignmentType == OrderOwnDriversAssignmentType.autoAssign)
      order.autoAssign = true;
    else if (order.assignmentType == OrderOwnDriversAssignmentType.assignNow) {
      const driver = await this.driverService.get_details(
        driverId.toHexString ? driverId.toHexString() : (driverId as any),
      );
      if (!driver)
        throw new NotFoundException(
          `Driver with id ${driverId.toHexString()} not found. Please provide a valid driver id.`,
          responseCode.ENTITY_NOT_FOUND.toString(),
        );
      order.driver = driver._id;
      order.assigned_driver_name = driver.last_name
        ? driver.first_name + ' ' + driver.last_name
        : driver.first_name;
      order.driverManuallyAssigned = true;
    } else order.driver = undefined;
  }

  private setOrderDeliveryParty(
    order: OrderDocument,
    {
      deliveryMethod,
      thirdParty,
    }: Pick<DeliveryOptionsDto, 'deliveryMethod' | 'thirdParty'>,
  ) {
    order.deliveryMethod = deliveryMethod;
    if (deliveryMethod == DeliveryMethod.THIRD_PARTY)
      order.deliveryParty = thirdParty;
    else order.deliveryParty = undefined;
  }

  private getStatusesList(order: OrderDocument): string[] {
    if (order.delivery_action == OrderDeliveryAction.IN_STORE_PICKUP)
      return ['Received', 'Ready For Pickup', 'PickedUp'];
    else return ['Received', 'Out for Delivery', 'Delivered'];
  }

  private getPaymentLink(order: OrderDocument): string {
    const paymentLink =
      this.configService.get<string>('PAY_PAGE') + '/' + order?.payment_code;

    return order.payment_method == OrderPaymentMethod.online &&
      order.payment_status != OrderPaymentStatus.COMPLETED &&
      !order.deletedAt &&
      order.status != OrderStatusEnum.CANCELED
      ? paymentLink
      : undefined;
  }

  private getMappedEventName(order: OrderDocument): OrderEventEnum {
    const orderEventMap: Map<OrderStatusEnum, OrderEventEnum> = new Map([
      [OrderStatusEnum.UNASSIGNED, OrderEventEnum.PENDING],
      [OrderStatusEnum.PENDING, OrderEventEnum.PENDING],
      [OrderStatusEnum.SCHEDULED, OrderEventEnum.PENDING],
      [OrderStatusEnum.PREPARING, OrderEventEnum.PREPARING],
      [OrderStatusEnum.PENDING_PICKUP, OrderEventEnum.AWAITING_PICKUP],
      [OrderStatusEnum.IN_ROUTE, OrderEventEnum.AWAITING_DELIVERY],
      [OrderStatusEnum.COMPLETED, OrderEventEnum.DELIVERED_SUCCESSFULLY],
      [OrderStatusEnum.CANCELED, OrderEventEnum.CANCELED_ORDER],
    ]);
    if (order?.deletedAt) return OrderEventEnum.DELETED_ORDER;
    else if (
      order.status == OrderStatusEnum.COMPLETED &&
      order.delivery_action == OrderDeliveryAction.IN_STORE_PICKUP
    )
      return OrderEventEnum.COLLECTED_SUCCESSFULLY;
    else if (
      order.status == OrderStatusEnum.PENDING_PICKUP &&
      order.delivery_action == OrderDeliveryAction.DELIVERY_LOCATION
    )
      return OrderEventEnum.PREPARING;
    else return orderEventMap.get(order?.status);
  }

  private getMappedEventObject(eventName: OrderEventEnum): OrderEventDto {
    const orderEventNameToEventMap: Map<OrderEventEnum, OrderEventDto> =
      new Map([
        [
          OrderEventEnum.PENDING,
          {
            name: OrderEventEnum.PENDING,
            description: 'Your order will be accepted soon',
            iconName: 'pending.svg',
            isViewableStepper: true,
          },
        ],
        [
          OrderEventEnum.PREPARING,
          {
            name: OrderEventEnum.PREPARING,
            description: 'Your order is being prepared',
            iconName: 'preparing.svg',
            isViewableStepper: true,
          },
        ],
        [
          OrderEventEnum.AWAITING_PICKUP,
          {
            name: OrderEventEnum.AWAITING_PICKUP,
            description: 'Your order is ready for pickup',
            iconName: 'awaitingPickup.svg',
            isViewableStepper: true,
          },
        ],
        [
          OrderEventEnum.AWAITING_DELIVERY,
          {
            name: OrderEventEnum.AWAITING_DELIVERY,
            description: 'Your order is on the way',
            iconName: 'awaitingDelivery.svg',
            isViewableStepper: true,
          },
        ],
        [
          OrderEventEnum.DELIVERED_SUCCESSFULLY,
          {
            name: OrderEventEnum.DELIVERED_SUCCESSFULLY,
            description: '',
            iconName: 'successIcon.svg',
            isViewableStepper: true,
          },
        ],
        [
          OrderEventEnum.COLLECTED_SUCCESSFULLY,
          {
            name: OrderEventEnum.COLLECTED_SUCCESSFULLY,
            description: '',
            iconName: 'successIcon.svg',
            isViewableStepper: true,
          },
        ],
        [
          OrderEventEnum.CANCELED_ORDER,
          {
            name: OrderEventEnum.CANCELED_ORDER,
            description: 'Unfortunately, Your order has been canceled',
            iconName: 'cancel.svg',
            isViewableStepper: false,
          },
        ],
        [
          OrderEventEnum.DELETED_ORDER,
          {
            name: OrderEventEnum.DELETED_ORDER,
            description: 'Unfortunately, Your order has been deleted',
            iconName: 'cancel.svg',
            isViewableStepper: false,
          },
        ],
      ]);
    return orderEventNameToEventMap.get(eventName);
  }

  private getMappedStatus(order: OrderDocument, statuses: string[]): string {
    if (
      order?.status == OrderStatusEnum.PREPARING ||
      (order?.status == OrderStatusEnum.PENDING_PICKUP &&
        order.delivery_action != OrderDeliveryAction.IN_STORE_PICKUP)
    )
      return statuses[0];
    else if (
      (order?.status == OrderStatusEnum.IN_ROUTE &&
        order?.firstPartyState == FirstPartyTaskState.DELIVERY_STARTED) ||
      (order?.status == OrderStatusEnum.PENDING_PICKUP &&
        order.is_ready == true)
    )
      return statuses[1];
    else if (order?.status == OrderStatusEnum.COMPLETED) return statuses[2];
    else return null;
  }

  private getTrackingLink(order: OrderDocument, status: string): string {
    return (order.deliveryMethod == DeliveryMethod.COMPANY_DRIVERS ||
      order.deliveryMethod == DeliveryMethod.BRANCH_DRIVERS) &&
      status == 'Out for Delivery' &&
      !order.deletedAt &&
      order.status != OrderStatusEnum.CANCELED
      ? order.tookan_delivery_track_url
      : null;
  }

  private getLocationLink(order: OrderDocument) {
    const locationLink = order.shortenUrl;
    return order.delivery_action != OrderDeliveryAction.IN_STORE_PICKUP &&
      order.delivery_action != OrderDeliveryAction.DINE_IN &&
      !order.deliveryLocation
      ? locationLink
      : undefined;
  }

  private async changeOrderPaymentStatusToNeedsRefund(
    order: OrderDocument,
    orderPaymentStatus: OrderPaymentStatus,
  ): Promise<void> {
    order.payment_status = orderPaymentStatus;
    await order.save();
  }

  private async changeOrderPaymentStatusToRefunded(
    order: OrderDocument,
    orderPaymentStatus: OrderPaymentStatus,
  ): Promise<void> {
    const endedOrderPaymentStatuses = [
      OrderPaymentStatus.NEEDS_REFUND,
      OrderPaymentStatus.COMPLETED,
    ];
    if (endedOrderPaymentStatuses.includes(order?.payment_status)) {
      order.payment_status = orderPaymentStatus;
      await order.save();
      await this.orderNotificationService.fireOrderNotificationTrigger(
        order,
        TriggerAction.ON_ORDER_REFUNDED,
      );
      this.eventEmitter.emit('on.order.refunded', order);
    }
  }

  private getCanUseDeliveryOptions(
    order: OrderDocument,
    deliveryOptions: DeliveryOptionsDto,
  ): boolean {
    const orderHasDeliveryMethods =
      order.deliveryMethod &&
      !(
        order.deliveryMethod === DeliveryMethod.BRANCH_DRIVERS &&
        order.assignmentType == OrderOwnDriversAssignmentType.assignLater &&
        !order.driver
      );

    if (
      orderHasDeliveryMethods &&
      deliveryOptions &&
      deliveryOptions.deliveryMethod
    ) {
      throw new BadRequestException(
        "Order already has delivery options set. Cannot accept 'deliveryOptions'.",
        responseCode.BAD_REQUEST.toString(),
      );
    }

    if (
      !orderHasDeliveryMethods &&
      (!deliveryOptions || !deliveryOptions.deliveryMethod)
    ) {
      throw new BadRequestException(
        "Since order needs to be delivered but does not have delivery options set, 'deliveryOptions' is required.",
        responseCode.BAD_REQUEST.toString(),
      );
    }

    return !orderHasDeliveryMethods;
  }

  private doesOrderNeedDelivery(order: OrderDocument): boolean {
    return (
      order.delivery_action &&
      [
        OrderDeliveryAction.SEND_SMS,
        OrderDeliveryAction.ADD_DELIVERY_LOCATION_LATER,
        OrderDeliveryAction.DELIVERY_LOCATION,
      ].includes(order.delivery_action)
    );
  }

  async applyCouponBenefitToOrder(
    couponId: Types.ObjectId,
    customer: CustomerDocument,
  ): Promise<{
    benefits: CustomerEarnedBenefit[];
    discounts: Discount[];
  }> {
    if (!couponId) return { benefits: [], discounts: [] };

    const coupon = await this.couponService.findById(couponId);

    const benefits = coupon.benefits.map((couponBenefit) =>
      CustomerEarnedBenefit.fromCoupon(couponBenefit),
    );

    if (customer.loyaltyPoints < coupon.loyaltyPointCost)
      throw new BadRequestException(
        "Customer doesn't have enough loyalty points to redeem coupon",
      );

    const discounts = coupon.benefits.map((benefit) =>
      Discount.fromBenefit(benefit),
    );

    await this.loyaltyTransactionService.recordEarnedBenefits(
      customer,
      benefits,
    );

    return { benefits, discounts };
  }

  public extractCouponInfoForOrdable(
    discounts: Discount[],
    existingCouponId?: Types.ObjectId,
  ): { couponId?: Types.ObjectId; filteredDiscounts: Discount[] } {
    const couponDiscount = Array.isArray(discounts)
      ? discounts.find((d) => d.source === DiscountSource.COUPON)
      : undefined;

    const couponId = existingCouponId ?? couponDiscount?.couponId;

    const filteredDiscounts = Array.isArray(discounts)
      ? discounts.filter((d) => d.source !== DiscountSource.COUPON)
      : [];

    return { couponId, filteredDiscounts };
  }
}
