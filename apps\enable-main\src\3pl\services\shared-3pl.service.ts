import { Inject, Injectable } from '@nestjs/common';

import {
  AddressDto,
  BrandDocument,
  CreateAddressDto,
  EmbeddedBrandDto,
  SavedLocation,
  SavedLocationDocument,
  SavedLocationToCreate,
  SavedLocationToUpdate,
  UpdateAddressDto,
} from '@app/shared-stuff';
import { Types } from 'mongoose';
import { BrandServiceInterface } from '../../brand/services/brand/brand.service.interface';
import { SavedLocationService } from '../../location/services/saved-location/saved-location.service';
import { LocationType } from '../../shared/enums/location-type.enum';
import { HelperService } from '../../shared/services/helper/helper.service';

@Injectable()
export class Shared3plService {
  constructor(
    private savedLocationService: SavedLocationService,
    private helperService: HelperService,
    @Inject('BrandServiceInterface')
    private brandService: BrandServiceInterface,
  ) {}

  async createAddress(address: CreateAddressDto): Promise<AddressDto> {
    let { location } = address;

    if (location) {
      location = await this.savedLocationService.create(location);
    }

    const locationDocument = location as SavedLocationDocument;
    const plainLocation = locationDocument
      ? locationDocument.toObject<SavedLocation>()
      : null;

    if (!address.address && plainLocation) {
      address.address = this.helperService.convertLocationToString(
        plainLocation,
        LocationType.DELIVERY,
      );
    }

    return { ...address, location: locationDocument };
  }

  async updateAddress(
    currentAddress: AddressDto,
    updatedAddress: UpdateAddressDto,
  ): Promise<AddressDto> {
    let savedLocation:
      | SavedLocation
      | SavedLocationToUpdate
      | SavedLocationToCreate = updatedAddress.location;

    if (savedLocation) {
      savedLocation =
        '_id' in savedLocation
          ? await this.savedLocationService.update(savedLocation)
          : await this.savedLocationService.create(savedLocation);
    }

    if (!updatedAddress.address && savedLocation) {
      updatedAddress.address = this.helperService.convertLocationToString(
        savedLocation as SavedLocationDocument,
        LocationType.DELIVERY,
      );
    }

    return {
      ...currentAddress,
      ...updatedAddress,
      location: savedLocation as SavedLocationDocument,
    };
  }

  async fetchEmbeddedBrand(brandId: Types.ObjectId): Promise<EmbeddedBrandDto> {
    const brand = await this.brandService.findById(brandId);
    return this.brandDocumentToEmbeddedBrand(brand);
  }

  async fetchBrandByName(
    name: string,
    companyId: Types.ObjectId,
  ): Promise<EmbeddedBrandDto | null> {
    const brand = await this.brandService.findOneByNameAndCompanyId(
      name,
      companyId,
    );
    if (!brand) return null;

    return this.brandDocumentToEmbeddedBrand(brand);
  }

  private brandDocumentToEmbeddedBrand(brand: BrandDocument): EmbeddedBrandDto {
    return {
      _id: brand._id,
      name: brand.name,
      phoneNumber: brand.phoneNumber,
      countryCode: brand.countryCode,
      image: brand.image,
      emailSenderId: brand.emailSenderId,
      senderId: brand.senderId,
    };
  }
}
