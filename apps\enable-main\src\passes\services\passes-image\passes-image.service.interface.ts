import {
  BrandDocument,
  CompanyDocument,
  CustomerDocument,
  GenerateStampImageDto,
  LoyaltyTierProgramProgress,
  PassConfig,
} from '@app/shared-stuff';

export interface PassesImageServiceInterface {
  generateImage(
    customer: CustomerDocument,
    loyaltyTierProgramProgress: LoyaltyTierProgramProgress,
    passConfig: PassConfig,
    company: CompanyDocument,
  ): Promise<Buffer>;
  generateImageUrl(customer: CustomerDocument, brand: BrandDocument): string;
  generateStreamableImage(
    generateStampImageDto: GenerateStampImageDto,
  ): Promise<Buffer>;
}

export const PassesImageServiceInterface = Symbol(
  'PassesImageServiceInterface',
);
