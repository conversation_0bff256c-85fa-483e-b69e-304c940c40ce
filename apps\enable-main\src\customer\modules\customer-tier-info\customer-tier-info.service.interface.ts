import {
  CalendarCycle,
  CompanyDocument,
  CustomerDocument,
  EmbeddedOrderDto,
  EmbeddedTierDto,
  IsLoyaltyOrder,
  LoyaltyTierDocument,
  LoyaltyTierMilestone,
  LoyaltyTierProgramProgress,
} from '@app/shared-stuff';

export interface CustomerTierInfoServiceInterface {
  createIsLoyaltyOrder(
    customer: CustomerDocument,
    company: CompanyDocument,
    calendarCycle?: CalendarCycle,
  ): IsLoyaltyOrder;

  getLoyaltyOrderRate(
    customer: CustomerDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<number>;

  getTotalLoyaltyOrderRate(
    customer: CustomerDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<number>;

  getLoyaltyAmountSpent(
    customer: CustomerDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<number>;

  getTotalLoyaltyAmountSpent(
    customer: CustomerDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<number>;

  getLoyaltyPointsRate(
    customer: CustomerDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<number>;

  getTotalLoyaltyPointsRate(
    customer: CustomerDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<number>;

  getRemainingOrdersCurrentTier(
    customer: CustomerDocument,
    loyaltyOrderRate?: number,
  ): Promise<number>;

  getRemainingOrdersUpperTier(
    customer: CustomerDocument,
    upperTier: LoyaltyTierDocument,
    loyaltyOrderRate?: number,
  ): Promise<number>;

  getRemainingAmountSpentCurrentTier(
    customer: CustomerDocument,
    loyaltyAmountSpent?: number,
  ): Promise<number>;

  getRemainingAmountSpentUpperTier(
    customer: CustomerDocument,
    upperTier: LoyaltyTierDocument,
    loyaltyAmountSpent?: number,
  ): Promise<number>;

  computeRemainingOrders(
    loyaltyTier: LoyaltyTierDocument | EmbeddedTierDto,
    loyaltyOrderRate: number,
  ): number;

  computeRemainingAmount(
    loyaltyTier: LoyaltyTierDocument | EmbeddedTierDto,
    loyaltyAmountSpent: number,
  ): number;

  computeRemainingPoints(
    loyaltyTier: LoyaltyTierDocument | EmbeddedTierDto,
    loyaltyPointsRate: number,
  ): number;

  getTierValidTill(
    customer: CustomerDocument,
    company: CompanyDocument,
    useReminderDateAsValidTill?: boolean,
  ): Promise<Date | null>;

  getGracePeriodReminderDate(
    customer: CustomerDocument,
    company: CompanyDocument,
  ): Promise<Date | null>;

  getThresholdPercentage(
    customer: CustomerDocument,
    company: CompanyDocument,
    tier?: LoyaltyTierDocument | EmbeddedTierDto,
  ): Promise<number>;

  hasCustomerMaintainedTier(
    customer: CustomerDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<boolean>;

  hasCustomerMetTierRequirements(
    customer: CustomerDocument,
    company: CompanyDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<boolean>;

  willTierBeMaintained(
    company: CompanyDocument,
    order: EmbeddedOrderDto,
    customer: CustomerDocument,
  ): Promise<boolean>;

  willTierBeUpgraded(
    company: CompanyDocument,
    order: EmbeddedOrderDto,
    customer: CustomerDocument,
  ): Promise<boolean>;

  getNextMilestone(
    customer: CustomerDocument,
    company: CompanyDocument,
  ): Promise<LoyaltyTierMilestone | null>;

  getLastMilestone(
    customer: CustomerDocument,
    company: CompanyDocument,
  ): Promise<LoyaltyTierMilestone | null>;

  calculateRemainingRequirements(
    customer: CustomerDocument,
    upperTier: LoyaltyTierDocument,
  ): Promise<string>;

  getLoyaltyTierProgramProgress(
    customer: CustomerDocument,
    company: CompanyDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<LoyaltyTierProgramProgress>;

  isInGracePeriod(
    customer: CustomerDocument,
    company: CompanyDocument,
    calendarCycle?: CalendarCycle,
  ): Promise<boolean>;
}

export const CustomerTierInfoServiceInterface = Symbol(
  'CustomerTierInfoServiceInterface',
);
