// https://e-butler.atlassian.net/browse/EBL-5028 Compensation | Loyalty Manual Transactions
// Initialize reward source inside customer rewards and usedRewards

db.customers.updateMany({}, [
  {
    $set: {
      rewards: {
        $map: {
          input: '$rewards',
          as: 'reward',
          in: {
            $mergeObjects: [
              '$$reward',
              {
                source: { $ifNull: ['$$reward.source', 'punch_card_progress'] },
              },
            ],
          },
        },
      },
      usedRewards: {
        $map: {
          input: '$usedRewards',
          as: 'usedReward',
          in: {
            $mergeObjects: [
              '$$usedReward',
              {
                source: {
                  $ifNull: ['$$usedReward.source', 'punch_card_progress'],
                },
              },
            ],
          },
        },
      },
    },
  },
]);
