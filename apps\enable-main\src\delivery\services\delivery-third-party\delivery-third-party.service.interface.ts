import { DeliveryThirdPartyDocument } from '../../models/delivery-third-party.model';
import { UpdateDeliveryThirdPartyDto } from '../../dto/delivery-third-party/update-delivery-third-party.dto';
import { IndexDeliveryThirdPartyDto } from '../../dto/delivery-third-party/index-delivery-third-party.dto';

export interface DeliveryThirdPartyServiceInterface {
  index(
    couponIndexDto: IndexDeliveryThirdPartyDto,
  ): Promise<DeliveryThirdPartyDocument[]>;
  update(
    updateDeliveryThirdPartyDto: UpdateDeliveryThirdPartyDto,
  ): Promise<DeliveryThirdPartyDocument>;
}

export const DeliveryThirdPartyServiceInterface = Symbol(
  'DeliveryThirdPartyServiceInterface',
);
