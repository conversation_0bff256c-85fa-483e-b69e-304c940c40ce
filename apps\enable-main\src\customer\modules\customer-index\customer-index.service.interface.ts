import {
  CustomerDocument,
  FetchEligibleCustomerForBulkActionDto,
  IndexCustomerDto,
} from '@app/shared-stuff';

export interface CustomerIndexServiceInterface {
  index(indexCustomerDto: IndexCustomerDto): Promise<any[]>;
  indexAsDocuments(
    indexCustomerDto: IndexCustomerDto,
  ): Promise<CustomerDocument[]>;
  fetchEligibleCustomersForBulkAction(
    fetchEligibleCustomerForBulkActionDto: FetchEligibleCustomerForBulkActionDto,
  ): Promise<CustomerDocument[]>;
  count(indexCustomerDto: IndexCustomerDto): Promise<number>;
}

export const CustomerIndexServiceInterface = Symbol(
  'CustomerIndexServiceInterface',
);
