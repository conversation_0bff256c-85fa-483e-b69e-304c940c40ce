// Manual Campaigns - R&P
// Create the permissions and privileges for the manual campaigns module
// WARNING: WILL REMOVE THE PERMISSIONS/PRIVILEGES FROM ROLES IF THEY ALREADY EXIST

const permissionsExist = db.permissions.find({ module: 'campaign' }).toArray();
if (permissionsExist.length) {
  const permissionIds = permissionsExist.map((p) => p._id);
  const rolesWithPerm = db.roles
    .find({ permissions: { $in: permissionIds } })
    .map((r) => r._id)
    .toArray();

  console.log('Removing permission from roles', JSON.stringify(rolesWithPerm));

  db.roles.updateMany(
    { _id: { $in: rolesWithPerm } },
    { $pull: { permissions: { $in: permissionIds } } },
  );
} else {
  db.permissions.insertMany([
    {
      name: ' index-campaign campaign',
      module: 'campaign',
      action: 'index-campaign',
      route: 'campaign/index-campaign',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      name: ' create-campaign campaign',
      module: 'campaign',
      action: 'create-campaign',
      route: 'campaign/create-campaign',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ]);
}

const privilege = db.privileges.findOne({ module: 'campaign' });

if (privilege) {
  const roleswithPriv = db.roles
    .find({ privileges: privilege._id })
    .map((r) => r._id)
    .toArray();

  console.log('Removing privilege from roles', JSON.stringify(roleswithPriv));

  db.roles.updateMany(
    { _id: { $in: roleswithPriv } },
    { $pull: { privileges: privilege._id } },
  );
} else {
  db.privileges.insertOne({
    module: 'campaign',
    label: 'Campaign',
    value: 'campaign module',
    createdAt: new Date(),
    updatedAt: new Date(),
  });
}
