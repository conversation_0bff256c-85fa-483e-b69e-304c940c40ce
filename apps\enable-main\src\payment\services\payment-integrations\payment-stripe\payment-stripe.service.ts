import {
  CollectionName,
  LoggerService,
  OrderDocument,
  PaymentDocument,
  PaymentGatewayType,
  PaymentMethodUsed,
  PaymentStatusEnum,
  PusherService,
  StripeConfiguration,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import Stripe from 'stripe';
import { CompanyService } from '../../../../company/services/company/company.service';
import { PaymentLogAction } from '../../../enums/payment-log-action.enum';
import { PaymentLogRepositoryInterface } from '../../../repositories/interfaces/payment.log.repository.interface';
import { PaymentRepositoryInterface } from '../../../repositories/interfaces/payment.repository.interface';
import { PaymentHelpersService } from '../../payment-helpers/payment-helpers.service';

@Injectable()
export class PaymentStripeService {
  private readonly loggerService = new LoggerService(PaymentStripeService.name);

  private stripeClient: Stripe;

  constructor(
    private configService: ConfigService,
    private companyService: CompanyService,
    @InjectModel(CollectionName.ORDER)
    private paymentOrderModel: Model<OrderDocument>,
    private paymentHelperService: PaymentHelpersService,
    @Inject('PaymentRepositoryInterface')
    private paymentRepository: PaymentRepositoryInterface,
    @Inject('PaymentLogRepositoryInterface')
    private paymentLogRepository: PaymentLogRepositoryInterface,
    private pusherService: PusherService,
  ) {}

  async createStripePayment(
    payment: PaymentDocument,
    stripeConfiguration: StripeConfiguration,
  ) {
    this.stripeClient = new Stripe(stripeConfiguration.keySecret, {
      apiVersion: '2024-06-20',
    });
    // Check if the customer Exist on Stripe Section if not create new one

    let stripeCustomer = (
      await this.stripeClient.customers.list({
        email: `${payment.customer_phone}@enable.tech`,
      })
    ).data[0];

    if (!stripeCustomer) {
      stripeCustomer = await this.stripeClient.customers.create({
        email: `${payment.customer_phone}@enable.tech`,
        name: payment.customer_name,
        description: payment.customer_name,
      });
    }

    // Create the Payment with The Currency
    const paymentIntent = await this.stripeClient.paymentIntents.create({
      amount: Math.round(payment.amount * 100),
      currency: payment.localization?.currency || 'QAR',
      payment_method_types: ['card'],
      customer: stripeCustomer.id,
      description: 'Payment Code: ' + payment.code,
    });

    // Update Payment Method Used
    payment.payment_method_used = PaymentMethodUsed.STRIPE;
    await payment.save();

    // Sending the Payment back to the
    return { clientSecret: paymentIntent.client_secret };
  }

  async onStripeUpdated(event: Stripe.Event) {
    // Handle the event
    switch (event.type) {
      case 'payment_intent.payment_failed':
        await this.onPaymentUpdated(event, 'payment_failed');
        break;
      case 'payment_intent.succeeded':
        await this.onPaymentUpdated(event, 'succeeded');
        break;
      // ... handle other event types
      default:
        this.loggerService.log(`Unhandled event type ${event.type}`, event);
    }

    return 'ANY';
  }

  private async onPaymentUpdated(stripEvent: Stripe.Event, status: string) {
    const payment = await this.paymentRepository.findOne({
      stripeClientSecret: stripEvent.data.object['client_secret'],
    });

    if (!payment) {
      return;
    }
    const stripData = {};

    const company = await this.companyService.get_details(payment.company);

    // Getting The Order Details
    let order: OrderDocument;
    if (payment.order_code) {
      order = await this.paymentOrderModel.findOne({
        code: payment.order_code,
      });
    }

    if (payment.status == PaymentStatusEnum.TRANSACTION_COMPLETED) {
      stripData['ENABLE_NOTE'] =
        'The Payment is Successful and it trying to update to this one';

      payment.paymentTries
        ? payment.paymentTries.push(stripData as any)
        : (payment.paymentTries = [stripData] as any);
      await payment.save();

      return 'https://enable.tech';
    }

    // Handle THe Payment Default Data like the transction date and payment tries
    await this.paymentHelperService.saveTransaction(
      payment,
      stripEvent.id,
      stripData,
      PaymentGatewayType.STRIPE,
    );

    const paymentStatus =
      status == 'succeeded'
        ? PaymentStatusEnum.TRANSACTION_COMPLETED
        : PaymentStatusEnum.UNSUCCESSFUL;
    await this.paymentHelperService.handlePaymentStatusLogic(
      payment,
      paymentStatus,
      order,
      company,
    );

    const responseCode = status == 'succeeded' ? '000' : '123';
    // Generate the CallbackURL
    const callbackURL = this.paymentHelperService.generateCallBackUrl(
      payment,
      company,
      responseCode,
      order,
    );

    // Payment Log Updating
    await this.paymentLogRepository.create({
      sentObject: { callbackURL },
      receivedObject: stripEvent.data,
      logAction: PaymentLogAction.StripWebhookFired,
      paymentCode: payment.code,
      paymentId: payment._id,
    });

    await this.pusherService.fireEvent(payment.code, 'stripePaymentCompleted', {
      callbackURL,
    });
  }
}
