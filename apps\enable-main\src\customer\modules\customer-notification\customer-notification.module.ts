import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { BrandModule } from '../../../brand/brand.module';
import { CompanyModule } from '../../../company/company.module';
import { NotificationModule } from '../../../notification/notification.module';
import { CustomerReplacementsModule } from '../customer-replacements/customer-replacements.module';
import { CustomerNotificationService } from './customer-notification.service';
import { CustomerNotificationServiceInterface } from './customer-notification.service.interface';

@Module({
  providers: [
    {
      provide: CustomerNotificationServiceInterface,
      useClass: CustomerNotificationService,
    },
  ],
  imports: [
    NotificationModule,
    CompanyModule,
    EventEmitterModule,
    BrandModule,
    CustomerReplacementsModule,
  ],
  exports: [CustomerNotificationServiceInterface],
})
export class CustomerNotificationModule {}
