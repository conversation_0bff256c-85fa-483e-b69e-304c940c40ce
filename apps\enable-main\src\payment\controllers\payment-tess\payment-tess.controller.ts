import {
  GenericExceptionFilter,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Get,
  Inject,
  Param,
  Post,
  Res,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { Api<PERSON><PERSON><PERSON>Auth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { CallbackTESSPaymentIntegrationDto } from '../../dto/tess/callback-tess-payment-integration.dto';
import { PaymentCallbackResult } from '../../enums/tess/payment-callback-result.enum';
import { PaymentIntegrationServiceInterface } from '../../services/payment-integrations/payment-integration.service.interface';
import { PaymentService } from '../../services/payment/payment.service';

@Controller('payment-tess')
@ApiTags('Payment TESS')
@SetMetadata('module', 'payment-tess')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
export class PaymentTESSController {
  constructor(
    @Inject('PaymentTESSService')
    private readonly paymentTESSService: PaymentIntegrationServiceInterface,
    private paymentService: PaymentService,
  ) {}

  @Get('/public/callback/:paymentCallbackResult/:paymentCode')
  @SetMetadata('public', 'true')
  async callback(
    @Param('paymentCallbackResult')
    paymentCallbackResult: PaymentCallbackResult,
    @Param('paymentCode') paymentCode: string,
    @Res() res: Response,
  ) {
    const callbackUrl = await this.paymentTESSService.callback(
      paymentCallbackResult,
      paymentCode,
    );
    return res.redirect(callbackUrl);
  }

  @Post('/public/callback')
  @SetMetadata('public', 'true')
  async webhook(
    @Body()
    callbackTESSPaymentIntegrationDto: CallbackTESSPaymentIntegrationDto,
  ) {
    const payment = await this.paymentService.get_details(
      callbackTESSPaymentIntegrationDto.order_number,
    );
    payment.transaction_id = callbackTESSPaymentIntegrationDto.id;
    await payment.save();
    return callbackTESSPaymentIntegrationDto;
  }
}
