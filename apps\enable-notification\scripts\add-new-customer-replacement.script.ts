// Generic script to add a new customer module replacement to all existing triggers and templates

const newReplacements = [
  'loyaltyPointsBalanceInCash',
  'enableWalletPass',
  'remainingUntilNextPunchCardAchievement',
  'remainingUntilNextPunchCardAchievementFirstTrack',
  'remainingUntilNextPunchCardAchievementSecondTrack',
  'tierPointsExchangeRate',
  'upperTierPointsExchangeRate',
  'remainingPointsCurrentTier',
  'remainingPointsUpperTier',
  'highestUnlockedCouponBenefit',
  'nextPunchCardAchievementRequirements',
  'nextPunchCardAchievementBenefits',
  'nextPunchCardAchievementRequirementsFirstTrack',
  'nextPunchCardAchievementRequirementsSecondTrack',
  'punchCardFirstTrackName',
  'punchCardSecondTrackName',
  'remainingAmountSpentUpperTier',
  'remainingAmountSpentCurrentTier',
  'upperTierDiscountValueNumberOfUses',
  'tierDiscountValueNumberOfUses',
  'upperTierFreeDeliveryNumberOfUses',
  'tierFreeDeliveryNumberOfUses',
  'tierFreeDeliveryRemainingNumberOfUses',
  'tierDiscountValueRemainingNumberOfUses',
  'nextPunchCardAchievementBenefitsFirstTrack',
  'nextPunchCardAchievementBenefitsSecondTrack',
  'googleWalletPassLink',
  'tierProgramPointsProgress',
  'punchCardProgressFirstTrack',
  'punchCardProgressSecondTrack',
  'numberOfRewards',
  'tierProgramPointsTitleEn',
  'tierProgramPointsTitleAr',
  'currentMonth',
  'customerTitle',
  'customerShortCode',
  'lastName',
];

const orderTriggers = [
  'ON_ORDER_CREATED',
  'THREE_HOURS_AFTER_ORDER_COMPLETION',
  'ON_WEBSTORE_ORDER_CREATED',
  'ON_ORDER_COMPLETED',
  'ON_ORDER_COMPLETED_DELIVERECT',
  'ON_DINEIN_ORDER',
  'ON_WALK_IN_ORDER',
  'ON_AGGREGATOR_ORDER',
  'ON_AGGREGATOR_ORDER_NON_LOYALTY_MEMBERS',
  'THREE_HOURS_AFTER_AGGREGATOR_ORDER',
  'THREE_HOURS_AFTER_AGGREGATOR_ORDER_NON_LOYALTY_MEMBERS',
  'ON_LOYALTY_ORDER_LOYALTY_MEMBERS',
  'ON_LOYALTY_ORDER_NON_LOYALTY_MEMBERS',
  'ON_TAKEAWAY_ORDER',
  'ON_WEBSTORE_ORDER_CREATED_NON_LOYALTY_MEMBERS',
  'ON_WHATSAPP_ORDER_NON_LOYALTY_MEMBERS',
  'ON_ORDER_CREATED_NON_LOYALTY_MEMBERS',
];

db.triggers.updateMany(
  {
    $or: [
      { module: 'CUSTOMER', action: { $ne: 'ON_SEND_LOYALTY_REGISTRATION' } },
      {
        module: 'ORDER',
        action: {
          $in: orderTriggers,
        },
      },
    ],
  },
  { $addToSet: { replacement: { $each: newReplacements } } },
);

db.templates.updateMany(
  {
    $or: [
      {
        'trigger.module': 'CUSTOMER',
        'trigger.action': { $ne: 'ON_SEND_LOYALTY_REGISTRATION' },
      },
      {
        'trigger.module': 'ORDER',
        'trigger.action': {
          $in: orderTriggers,
        },
      },
    ],
  },
  { $addToSet: { 'trigger.replacement': { $each: newReplacements } } },
);
