import {
  CacheKeys,
  CacheServiceInterface,
  fullCacheKeys,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { StoreWithId } from 'apps/enable-main/src/store/dto/store-with-id.dto';
import { StoreEvents } from '../enumerations/store-events.enum';
import { StoreDocument } from '../models/store.model';

@Injectable()
export class StoreListener {
  constructor(
    @Inject(CacheServiceInterface)
    private readonly cacheService: CacheServiceInterface,
  ) {}

  @OnEvent(StoreEvents.STORE_UPDATED)
  async handleStoreUpdated(store: StoreDocument) {
    const key = fullCacheKeys[CacheKeys.STORE](store._id);
    const keyExists = await this.cacheService.getCache(key, StoreWithId);

    if (keyExists) {
      await this.cacheService.setCache(key, store.toJSON());
    }
  }
}
