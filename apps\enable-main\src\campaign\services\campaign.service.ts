import {
  CampaignDocument,
  CreateCampaignDtoWithAuth,
  CustomerDocument,
  IndexCampaignDto,
  mapAsync,
  MicroserviceCommunicationService,
  TemplateOwner,
} from '@app/shared-stuff';
import {
  BadRequestException,
  Inject,
  Injectable,
  Logger,
  OnModuleDestroy,
  OnModuleInit,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { Types } from 'mongoose';
import { BrandServiceInterface } from '../../brand/services/brand/brand.service.interface';
import { CompanyService } from '../../company/services/company/company.service';
import { CustomerIndexServiceInterface } from '../../customer/modules/customer-index/customer-index.service.interface';
import { CustomerReplacementsServiceInterface } from '../../customer/modules/customer-replacements/customer-replacements.service.interface';
import { CampaignServiceInterface } from './campaign.service.interface';

@Injectable()
export class CampaignService
  implements CampaignServiceInterface, OnModuleInit, OnModuleDestroy
{
  private readonly logger = new Logger(CampaignService.name);

  constructor(
    @Inject('enable-main-campaign-producer') private client: ClientProxy,
    private microserviceCommunicationService: MicroserviceCommunicationService,
    @Inject('BrandServiceInterface')
    private brandService: BrandServiceInterface,
    @Inject(CustomerIndexServiceInterface)
    private customerIndexService: CustomerIndexServiceInterface,
    @Inject(CustomerReplacementsServiceInterface)
    private customerReplacementsService: CustomerReplacementsServiceInterface,
    private companyService: CompanyService,
  ) {}

  async onModuleDestroy() {
    await this.microserviceCommunicationService.disconnect(this.client);
  }

  async onModuleInit() {
    await this.microserviceCommunicationService.connect(this.client);
  }

  public async index(
    indexCampaignDto: IndexCampaignDto,
  ): Promise<CampaignDocument[]> {
    return await this.microserviceCommunicationService.produceAndWaitForResponse(
      this.client,
      'index.campaign.request',
      indexCampaignDto,
    );
  }

  public async create(
    createCampaignDto: CreateCampaignDtoWithAuth,
  ): Promise<CampaignDocument> {
    const company = await this.companyService.findById(
      createCampaignDto.companyId,
    );
    await this.checkIfCompanyHasCampaigns(company.hasManualCampaigns);

    const campaign: CampaignDocument =
      await this.microserviceCommunicationService.produceAndWaitForResponse(
        this.client,
        'create.campaign.request',
        createCampaignDto,
      );
    this.launch(campaign);
    return campaign;
  }

  private async launch(campaign: CampaignDocument) {
    this.logger.log(`Launching campaign ${campaign._id}`);
    const company = await this.companyService.findById(campaign.companyId);
    await this.checkIfCompanyHasCampaigns(company.hasManualCampaigns);

    const owner: TemplateOwner = await this.getOwner(
      campaign,
      company._id,
      company.name,
    );

    const customers = await this.customerIndexService.indexAsDocuments({
      ...campaign.audience,
      company: campaign.companyId,
    });
    this.logger.log(`Found ${customers.length} customers`);

    const replacementsMap = await this.getReplacementsMap(
      customers,
      campaign.brandId,
    );

    const launchCampaignDto = {
      campaign,
      customers,
      replacementsMap,
      owner,
    };

    this.microserviceCommunicationService.produceAndForget(
      this.client,
      'launch.campaign.request',
      launchCampaignDto,
    );
  }

  private async getOwner(
    campaign: CampaignDocument,
    companyId: Types.ObjectId,
    companyName: string,
  ): Promise<TemplateOwner> {
    if (campaign.brandId) {
      const brand = await this.brandService.findById(campaign.brandId);
      return {
        _id: brand._id,
        name: brand.name,
        emailSenderId: brand.emailSenderId,
        smsSenderId: brand.senderId,
      };
    }
    return {
      _id: companyId,
      name: companyName,
      emailSenderId: companyName,
      smsSenderId: companyName,
    };
  }

  private async getReplacementsMap(
    customers: CustomerDocument[],
    brandId?: Types.ObjectId,
  ) {
    const toReplacementMapEntry = async (customer: CustomerDocument) => [
      customer._id.toString(),
      await this.customerReplacementsService.getCustomerReplacements(customer, {
        brandId,
      }),
    ];

    const mapEntries = await mapAsync(customers, toReplacementMapEntry);
    return Object.fromEntries(mapEntries);
  }

  private async checkIfCompanyHasCampaigns(hasManualCampaigns: boolean) {
    if (!hasManualCampaigns) {
      throw new BadRequestException(
        'Company Does not have manual campaigns enabled',
      );
    }
  }
}
