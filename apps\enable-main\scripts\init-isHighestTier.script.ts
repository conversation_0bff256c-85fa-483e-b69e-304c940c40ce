// EBL-4227 Introduce new automation journeys customer filters / conditions
// Init isHighestTier for all loyalty tiers

db.loyaltytiers
  .aggregate([
    { $match: { isHighestTier: { $exists: false } } },
    { $group: { _id: '$companyId', tiers: { $push: '$$ROOT' } } },
  ])
  .forEach(({ tiers }) => {
    const writes = tiers
      .sort((a, b) => a.tierIndex - b.tierIndex)
      .map(({ _id }, index) => ({
        updateOne: {
          filter: { _id },
          update: { $set: { isHighestTier: index === tiers.length - 1 } },
        },
      }));
    db.loyaltytiers.bulkWrite(writes);
  });
