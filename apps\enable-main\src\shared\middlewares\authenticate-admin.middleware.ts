import { ConfigService } from '@nestjs/config';
import { Injectable, NestMiddleware } from '@nestjs/common';
import { responseCode } from '@app/shared-stuff';
import { AuthService } from '../../user/services/auth/auth.service';
import { HelperService } from '../services/helper/helper.service';

@Injectable()
export class AuthenticateAdminMiddleware implements NestMiddleware {
  constructor(
    private configService: ConfigService,
    private authService: AuthService,
    private helperService: HelperService,
  ) {}
  async use(req: any, res: any, next: () => void) {
    try {
      const BarrerToken = req.headers.authorization;
      if (!BarrerToken) {
        return res.status(400).send({
          code: responseCode.TOKEN_NOT_FOUND,
          message: 'Please Provide token or api key to procceed',
        });
      }
      let currentUser = {};

      const token = BarrerToken.split(' ')[1];
      const tokenExpiredCheck =
        await this.authService.checkIfTokenInExpired(token);
      this.authService.verifyToken(token);

      if (tokenExpiredCheck) {
        return res.status(400).send({
          message: 'Your token is expired',
          code: responseCode.TOKEN_EXPIRED,
        });
      }
      const selectedUser = await this.authService.get_me(token);
      req['user'] = selectedUser;
      if (selectedUser.companies) req['companies'] = selectedUser.companies;
      if (selectedUser.company) {
        return res.status(400).send({
          message: 'You cannot access admin panel',
          code: responseCode.USER_NOT_ACTIVE,
        });
      }
      currentUser = {
        name: selectedUser.name,
        id: selectedUser._id,
        type: 'user',
      };
      if (selectedUser.status != 'active') {
        return res.status(400).send({
          message:
            'Your are no acitve right now, please contact administration',
          code: responseCode.USER_NOT_ACTIVE,
        });
      }
      req['current'] = currentUser;
      next();
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }
}
