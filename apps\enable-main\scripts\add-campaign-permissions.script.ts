// Manual Campaigns - R&P
// Add permissions and privileges for the manual campaigns module to a role

declare const ObjectId: (string) => string;

const ROLE_ID = ObjectId('63d26fcf6796b19cf34c7d12'); // OR write a db.roles.findOne query and get the ._id

const campaignPermissions = db.permissions
  .find({ module: 'campaign' })
  .toArray()
  .map((p) => p._id);
const campaignPrivilege = db.privileges.findOne({ module: 'campaign' });

const role = db.roles.findOne({ _id: ROLE_ID });
if (!role) console.log(`Role not found with filter ${{ _id: ROLE_ID }}`);
else if (!role.company && !role.companies?.length) {
  console.log(
    "Role is not company specific, try reassigning the user's role to the company specific role",
  );
} else {
  db.roles.updateOne(
    { _id: ROLE_ID },
    {
      $addToSet: {
        permissions: { $each: campaignPermissions },
        privileges: campaignPrivilege,
      },
    },
  );
}
