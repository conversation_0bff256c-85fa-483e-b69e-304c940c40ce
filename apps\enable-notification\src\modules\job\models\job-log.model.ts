import { Field } from '@app/shared-stuff';
import { Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { Job } from './job.model';

export type JobLogDocument = HydratedDocument<JobLog>;

@Schema({ timestamps: true, collection: 'job_logs' })
export class JobLog {
  @Field({ type: Job })
  job: Job;

  @Field({ type: Date })
  runAt: Date;
}

export const JobLogSchema = SchemaFactory.createForClass(JobLog);
