import {
  CollectionName,
  CustomerDocument,
  DibsyConfiguration,
  DibsyGatewayConfiguration,
  LoggerService,
  OrderDocument,
  PaymentDocument,
  PaymentGatewayType,
  PaymentMethod,
  PaymentStatusEnum,
  PusherService,
  responseCode,
} from '@app/shared-stuff';
import { HttpService } from '@nestjs/axios';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { PaymentIntegrationType } from '../../../../../../../libs/shared-stuff/src/enums/payment/payment-integration-type.enum';
import { CustomerReadServiceInterface } from '../../../../customer/modules/customer-read/customer-read.service.interface';
import { ENABLE_TECH_URL } from '../../../constants/payment-callback.const';
import { DibsyCreatePaymentLinkDto } from '../../../dto/dibsy/dibsy-create-payment-link.dto';
import { DibsyCustomerToCreate } from '../../../dto/dibsy/dibsy-customer-to-create.dto';
import { DibsyMerchantValidatedToRequest } from '../../../dto/dibsy/dibsy-merchant-validation-request.dto';
import { DibsyPaymentToCreate } from '../../../dto/dibsy/dibsy-payment-to-create.dto';
import { PaymentToProcess } from '../../../dto/payment-to-process.dto';
import { PaymentLogAction } from '../../../enums/payment-log-action.enum';
import { PaymentLogRepositoryInterface } from '../../../repositories/interfaces/payment.log.repository.interface';
import { PaymentRepositoryInterface } from '../../../repositories/interfaces/payment.repository.interface';
import { DibsyCreateCustomerResponse } from '../../../types/dibsy/dibsy-create-customer-response.type';
import { DibsyCreatePaymentLinkResponse } from '../../../types/dibsy/dibsy-create-payment-link-response.type';
import { PaymentConfigurationServiceInterface } from '../../payment-configuration/payment-configuration.service.interface';
import { CompanyService } from './../../../../company/services/company/company.service';
import { PaymentHelpersService } from './../../payment-helpers/payment-helpers.service';

@Injectable()
export class PaymentDibsyService {
  private readonly loggerService = new LoggerService(PaymentDibsyService.name);

  private BASE_URL: string;

  constructor(
    private configService: ConfigService,
    private httpService: HttpService,
    @Inject(CustomerReadServiceInterface)
    private customerReadService: CustomerReadServiceInterface,
    private companyService: CompanyService,
    private paymentHelperService: PaymentHelpersService,
    @InjectModel(CollectionName.ORDER)
    private paymentOrderModel: Model<OrderDocument>,
    @Inject('PaymentRepositoryInterface')
    private paymentRepository: PaymentRepositoryInterface,
    @Inject('PaymentLogRepositoryInterface')
    private paymentLogRepository: PaymentLogRepositoryInterface,
    private pusherService: PusherService,
    @Inject(PaymentConfigurationServiceInterface)
    private paymentConfigurationService: PaymentConfigurationServiceInterface,
  ) {
    this.BASE_URL = this.configService.get('DIBSY_BASE_URL');
  }

  async processPayment(
    payment: PaymentDocument,
    config: DibsyConfiguration,
    paymentToProcess: PaymentToProcess,
  ): Promise<string | void> {
    const { dibsySecretApiKey } = config;
    const customerId = payment.customer._id.toHexString();
    const appleToken = paymentToProcess.appleToken;

    if (!dibsySecretApiKey || !appleToken) return;

    const customer = await this.ensureDibsyCustomerId(
      customerId,
      dibsySecretApiKey,
    );
    if (!customer) return;

    // Create Payment
    const dibsyPayment = this.constructDibsyPayment(
      payment,
      customer.dibsyId,
      appleToken,
    );
    const response = await this.performCreatePaymentRequest(
      dibsyPayment,
      dibsySecretApiKey,
    );

    await this.paymentLogRepository.create({
      sentObject: dibsyPayment,
      receivedObject: response,
      logAction: PaymentLogAction.PaymentProcessedFromDibsy,
      paymentCode: payment.code,
      paymentId: payment._id,
    });

    return 'DONE';
  }

  private async ensureDibsyCustomerId(
    customerId: string,
    dibsySecretApiKey: string,
  ): Promise<CustomerDocument | null> {
    const customer = await this.customerReadService.findOne(customerId);
    if (!customer) return null;

    if (!customer.dibsyId) {
      const dibsyCustomerId = await this.createDibsyCustomerIfNeeded(
        customer,
        dibsySecretApiKey,
      );
      if (!dibsyCustomerId) return null;

      customer.dibsyId = dibsyCustomerId;
      await customer.save();
    }

    return customer;
  }

  private async createDibsyCustomerIfNeeded(
    customer: CustomerDocument,
    apiKey: string,
  ): Promise<string> {
    const customerData = {
      name: customer.full_name,
      phone: customer.phone,
      email: customer.email ?? `${customer.phone}@e-butler.com`,
    };
    const response = await this.performCreateCustomerRequest(
      customerData,
      apiKey,
    );
    return response?.id ?? null;
  }

  private constructDibsyPayment(
    payment: PaymentDocument,
    dibsyId: string,
    appleToken: string,
  ): DibsyPaymentToCreate {
    return {
      amount: {
        value: payment.amount.toFixed(2),
        currency: payment.localization.currency,
      },
      description: payment.code,
      redirectUrl: `${this.configService.get('HOST_URL')}/payment/action/dibsy/after_done/${payment._id}`,
      webhookUrl: `${this.configService.get('HOST_URL')}/payment/action/dibsy/after_done/${payment._id}`,
      customerId: dibsyId,
      method: 'applepay',
      applePayToken: appleToken,
    };
  }

  async afterPaymentDone(paymentId: string, data: any) {
    this.loggerService.log('dibsy webhook payload', data);

    const payment = await this.paymentRepository.findByIdWithBranch(
      new Types.ObjectId(paymentId),
    );

    this.loggerService.log(`payment with code : ${payment?.code}`);

    if (!payment) return;

    const company = await this.companyService.get_details(payment.company);

    const paymentConfiguration =
      await this.paymentConfigurationService.findPaymentConfigByIdentifier(
        company._id,
      );

    let dibsyPaymentInfo;

    const gatewayConfig =
      paymentConfiguration.configuration[PaymentMethod.APPLE_PAY];
    if (
      gatewayConfig.paymentGatewayType === PaymentGatewayType.DIBSY &&
      gatewayConfig.configuration.integrationType ===
        PaymentIntegrationType.REDIRECT
    ) {
      dibsyPaymentInfo = await this.fetchPaymentInfo(
        data['id'],
        gatewayConfig.configuration.dibsySecretApiKey,
      );
    }

    if (!dibsyPaymentInfo) return;

    dibsyPaymentInfo['details'] = '*********';
    data['paymentInfo'] = dibsyPaymentInfo;

    let order: OrderDocument;
    if (payment.order_code) {
      order = await this.paymentOrderModel.findOne({
        code: payment.order_code,
      });
    }

    if (payment.status == PaymentStatusEnum.TRANSACTION_COMPLETED) {
      data['ENABLE_NOTE'] =
        'The Payment is Successful and it trying to update to this one';
      payment.paymentTries
        ? payment.paymentTries.push(data)
        : (payment.paymentTries = [data]);
      await payment.save();

      return 'https://enable.tech';
    }

    await this.paymentHelperService.saveTransaction(
      payment,
      data['paymentInfo']['id'],
      data,
      PaymentGatewayType.DIBSY,
    );

    const paymentStatus =
      data['paymentInfo']['status'] == 'succeeded'
        ? PaymentStatusEnum.TRANSACTION_COMPLETED
        : PaymentStatusEnum.UNSUCCESSFUL;

    await this.paymentHelperService.handlePaymentStatusLogic(
      payment,
      paymentStatus,
      order,
      company,
    );

    const callbackURL = this.paymentHelperService.generateCallBackUrl(
      payment,
      company,
      paymentStatus == PaymentStatusEnum.TRANSACTION_COMPLETED ? '000' : '152',
      order,
    );

    await this.paymentLogRepository.create({
      sentObject: {},
      receivedObject: data,
      logAction: PaymentLogAction.DibsyWebhookFired,
      paymentCode: payment.code,
      paymentId: payment._id,
    });

    await this.pusherService.fireEvent(payment.code, 'dibsyPaymentCompleted', {
      callbackURL,
    });

    return callbackURL;
  }

  async merchantValidationRequest(
    dibsyMerchantValidationToRequest: DibsyMerchantValidatedToRequest,
  ) {
    const secretKey = this.configService.get('DIBSY_SECRET_KEY');
    const response = await this.performMerchantValidationRequest(
      dibsyMerchantValidationToRequest.domain,
      dibsyMerchantValidationToRequest.validationUrl,
      secretKey,
    );

    return response;
  }

  private async performMerchantValidationRequest(
    domain: string,
    validationUrl: string,
    secretKey: string,
  ) {
    return new Promise((resolve, reject) => {
      this.httpService
        .post(
          this.BASE_URL + '/wallets/applepay/session',
          { domain, validationUrl },
          {
            headers: {
              Authorization: 'Bearer ' + secretKey,
            },
          },
        )
        .subscribe(
          (data) => {
            resolve(data.data);
          },
          (err) => {
            reject(err);
          },
        );
    });
  }

  private async performCreatePaymentRequest(
    dibsyApplePayToCreate: DibsyPaymentToCreate,
    secretKey: string,
  ) {
    return new Promise((resolve, reject) => {
      this.httpService
        .post(this.BASE_URL + 'payments', dibsyApplePayToCreate, {
          headers: {
            Authorization: 'Bearer ' + secretKey,
          },
        })
        .subscribe(
          (data) => {
            resolve(data.data);
          },
          (err) => {
            reject(err);
          },
        );
    });
  }

  private async performCreateCustomerRequest(
    dibsyCustomerToCreate: DibsyCustomerToCreate,
    secretKey: string,
  ): Promise<DibsyCreateCustomerResponse> {
    return new Promise((resolve, reject) => {
      this.httpService
        .post(this.BASE_URL + 'customers', dibsyCustomerToCreate, {
          headers: {
            Authorization: 'Bearer ' + secretKey,
          },
        })
        .subscribe(
          (data) => {
            resolve(data.data);
          },
          (err) => {
            reject(err);
          },
        );
    });
  }

  private async fetchPaymentInfo(paymentId: string, secretKey: string) {
    const GET_PAYMENT_URL = `${this.BASE_URL}/payments/${paymentId}`;
    this.loggerService.log('GET_PAYMENT_URL', GET_PAYMENT_URL);
    return new Promise((resolve, reject) => {
      this.httpService
        .get(GET_PAYMENT_URL, {
          headers: {
            Authorization: 'Bearer ' + secretKey,
          },
        })
        .subscribe(
          (data) => {
            resolve(data.data);
          },
          (err) => {
            reject(err);
          },
        );
    });
  }

  async createPaymentLink(
    payment: PaymentDocument,
    dibsyGatewayConfiguration: DibsyGatewayConfiguration,
  ): Promise<string> {
    if (payment.gateway_link) return payment.gateway_link;

    if (
      dibsyGatewayConfiguration.configuration.integrationType !==
      PaymentIntegrationType.REDIRECT
    )
      throw new BadRequestException(
        'Invalid Dibsy Integration Type',
        responseCode.IN_VALID_INPUT.toString(),
      );

    const createPaymentLinkDto =
      this.constructDibsyCreatePaymentLinkDto(payment);

    try {
      const response = await this.performCreatePaymentLinkRequest(
        createPaymentLinkDto,
        dibsyGatewayConfiguration?.configuration?.dibsySecretApiKey,
      );

      await this.paymentLogRepository.create({
        sentObject: { createPaymentLinkDto },
        receivedObject: response,
        logAction: PaymentLogAction.DIBSY_CREATE_PAYMENT_LINK,
        paymentCode: payment.code,
        paymentId: payment._id,
      });

      const updatedFields = {
        transaction_id: response.id,
        gateway_link: response?._links?.checkout?.href,
      };
      await this.paymentRepository.findOneAndUpdate(
        { _id: payment._id },
        { $set: updatedFields },
      );

      return response?._links?.checkout?.href;
    } catch (exception) {
      this.loggerService.error(
        `Exception while perform create payment link request with message ${exception.message}`,
        exception.stacktrace,
        createPaymentLinkDto,
      );
    }
  }

  private constructDibsyCreatePaymentLinkDto(
    payment: PaymentDocument,
  ): DibsyCreatePaymentLinkDto {
    return {
      name: payment.customer_name,
      amount: {
        value: payment.amount.toFixed(2),
        currency: payment.localization.currency,
      },
      description: `Payment code : ${payment.code}`,
      redirectUrl: `${this.configService.get('HOST_URL')}/payment/action/dibsy/after_done/${payment._id}`,
      webhookUrl: `${this.configService.get('HOST_URL')}/payment/action/dibsy/after_done/${payment._id}`,
      reusable: false,
    };
  }

  async performCreatePaymentLinkRequest(
    createPaymentLinkDto: DibsyCreatePaymentLinkDto,
    secretKey: string,
  ): Promise<DibsyCreatePaymentLinkResponse> {
    const DIBSY_CREATE_PAYMENT_URL = this.BASE_URL + 'payment-links';
    const HEADERS = this.constructRequestHeaders(secretKey);
    return new Promise(async (resolve, reject) => {
      this.httpService
        .post(DIBSY_CREATE_PAYMENT_URL, createPaymentLinkDto, {
          headers: HEADERS,
        })
        .subscribe({
          next: (data) => {
            resolve(data.data ?? {});
          },
          error: (error) => {
            this.loggerService.log(
              'Error while create dibsy payment link' + error.message,
              error.stacktrace,
              createPaymentLinkDto,
            );
            reject(error);
          },
        });
    });
  }

  private constructRequestHeaders(secretKey: string) {
    const CONTENT_TYPE = 'application/json';
    return {
      Authorization: `Bearer ${secretKey}`,
      'Content-Type': CONTENT_TYPE,
    };
  }

  async afterPaymentDoneRedirection(enablePaymentId: string) {
    this.loggerService.log('redirect enable paymentId :- ', enablePaymentId);

    const payment = await this.paymentRepository.findById(
      new Types.ObjectId(enablePaymentId),
    );
    if (!payment) return ENABLE_TECH_URL;

    const paymentConfig =
      await this.paymentConfigurationService.findPaymentConfig(
        payment.branch?._id,
        payment.brand?._id,
        payment.company['_id'],
      );

    const gatewayConfig = paymentConfig.configuration[payment.payment_method];

    if (gatewayConfig?.paymentGatewayType !== PaymentGatewayType.DIBSY)
      return ENABLE_TECH_URL;

    const company = await this.companyService.get_details(payment.company);

    let order: OrderDocument;
    if (payment.order_code) {
      order = await this.paymentOrderModel.findOne({
        code: payment.order_code,
      });
    }

    return await this.paymentHelperService.generateCallBackUrl(
      payment,
      company,
      '000',
      order,
    );
  }
}
