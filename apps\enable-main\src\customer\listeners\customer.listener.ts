import {
  Achievement,
  BenefitDocument,
  BrandDocument,
  CompanyDocument,
  CustomerDocument,
  embeddedTierFields,
  LogError,
  LoyaltyTierDocument,
  mapAsync,
  NumberOfUsesType,
  OrderDocument,
  pick,
  PunchCardDocument,
  UpdateCustomerDto,
} from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';

import { BrandServiceInterface } from '../../brand/services/brand/brand.service.interface';
import { PunchCardReadService } from '../../punch-card/modules/punch-card-read/punch-card-read.service';
import { StoreDocument } from '../../store/models/store.model';
import { CustomerEmitterService } from '../modules/customer-emitter/customer.emitter.service';
import { CustomerLoyaltyMemberServiceInterface } from '../modules/customer-loyalty-member/customer-loyalty-member.service.interface';
import { CustomerNotificationServiceInterface } from '../modules/customer-notification/customer-notification.service.interface';
import { CustomerRepositoryInterface } from '../modules/customer-repository/customer.repository.interface';
import { CustomerTierServiceInterface } from '../modules/customer-tier/customer-tier.service.interface';
import { LoyaltyTierLogServiceInterface } from '../../loyalty-tier-log/services/loyalty-tier-log.service.interface';

@Injectable()
export class CustomerListener {
  constructor(
    @Inject(CustomerRepositoryInterface)
    private readonly customerRepository: CustomerRepositoryInterface,
    @Inject(CustomerNotificationServiceInterface)
    private readonly customerNotificationService: CustomerNotificationServiceInterface,
    @Inject(CustomerTierServiceInterface)
    private readonly customerTierService: CustomerTierServiceInterface,
    @Inject(CustomerLoyaltyMemberServiceInterface)
    private readonly customerLoyaltyMemberService: CustomerLoyaltyMemberServiceInterface,
    @Inject(LoyaltyTierLogServiceInterface)
    private readonly loyaltyTierLogService: LoyaltyTierLogServiceInterface,
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandServiceInterface,
    private readonly eventEmitter: EventEmitter2,
    private readonly customerEmitterService: CustomerEmitterService,
    private readonly punchCardService: PunchCardReadService,
  ) {}

  @OnEvent('company.calendarSystem.updated')
  @LogError()
  async onCalendarSystemUpdated(company: CompanyDocument) {
    await this.customerTierService.resetCarryOver(company);

    const customers = await this.customerRepository.findByCompanyId(
      company._id,
    );

    await Promise.all(
      customers.map(async (customer) => {
        await this.customerTierService.checkForPromotion(
          customer,
          this.loyaltyTierLogService.onCalendarSystemUpdated,
        );
        await this.customerNotificationService.fireOnTierCalendarSystemUpdateTrigger(
          customer,
        );
      }),
    );
  }

  @LogError()
  @OnEvent('order.updated')
  async updateCustomerEmbeddedOrderDto(order: OrderDocument) {
    await this.customerRepository.updateOne(
      {
        _id: order.customer._id,
        'orders._id': order._id,
      },
      {
        $set: {
          'orders.$.source': order.source,
          'orders.$.status': order.status,
          'orders.$.pickup_date': order.pickup_date,
          'orders.$.delivery_date': order.delivery_date,
          'orders.$.total_amount': order.total_amount,
        },
      },
    );
  }

  @LogError()
  @OnEvent('customer.updated.pre')
  async onPreCustomerUpdated(
    customer: CustomerDocument,
    updateCustomerDto: UpdateCustomerDto,
  ) {
    return await this.customerTierService.handleManualUpdatedLoyaltyTier(
      customer,
      updateCustomerDto,
    );
  }

  @LogError()
  @OnEvent('customer.deleted')
  async onCustomerDeleted(customer: CustomerDocument) {
    this.customerEmitterService.handleCustomerDeleted(customer);
    return await this.customerTierService.handleCustomerDeletion(customer);
  }

  @LogError()
  @OnEvent('customer.tier.updated')
  async onCustomerTierUpdated(customer: CustomerDocument) {
    if (!customer.loyaltyTier) return;
    return await this.customerLoyaltyMemberService.enrollLoyaltyCustomer(
      customer,
    );
  }

  @LogError()
  @OnEvent('loyaltytier.requirements.lowered')
  async onLoyaltyTierRequirementsLowered(
    updatedTier: LoyaltyTierDocument,
    tiersEligibleForUpgrades: LoyaltyTierDocument[],
  ) {
    const loyaltyTierIds = tiersEligibleForUpgrades.map((tier) => tier._id);

    const customerEligibleForUpgrade =
      await this.customerRepository.findByLoyaltyTier(
        [...loyaltyTierIds, null],
        updatedTier.companyId,
      );

    await mapAsync(customerEligibleForUpgrade, async (customer) => {
      await this.customerTierService.checkForPromotion(
        customer,
        this.loyaltyTierLogService.onTierRequirementsLowered,
      );
      await this.customerTierService.checkForTierMaintained(customer);
    });
  }

  @LogError()
  @OnEvent('on-Brand-Update')
  async onBrandUpdate(brand: BrandDocument) {
    await this.customerRepository.syncBrandChanges(
      this.brandService.toEmbeddedBrandDto(brand),
    );
  }

  @LogError()
  @OnEvent('loyaltytier.updated')
  async onLoyaltyTierUpdate(
    newTier: LoyaltyTierDocument,
    oldTier?: LoyaltyTierDocument,
  ) {
    const pickedFields = pick(newTier, embeddedTierFields);
    await this.customerRepository.updateEmbeddedTier(pickedFields);

    await this.updateBenefitsOnLoyaltyTierUpdate(newTier, oldTier);

    const customers = await this.customerRepository.findByLoyaltyTier(
      [newTier._id],
      newTier.companyId,
    );

    for (const customer of customers) {
      this.eventEmitter.emit('loyaltyTier.numberOfUses.updated', customer);
    }

    this.eventEmitter.emit('customer.embeddedTier.updated', newTier);
  }

  private async updateBenefitsOnLoyaltyTierUpdate(
    newTier: LoyaltyTierDocument,
    oldTier: LoyaltyTierDocument,
  ) {
    const benefitsChanged =
      (oldTier && !oldTier.benefits && newTier.benefits) ||
      (oldTier?.benefits &&
        newTier.benefits &&
        JSON.stringify(oldTier.benefits) !== JSON.stringify(newTier.benefits));

    if (benefitsChanged) {
      await this.customerTierService.customerTierBenefitsUpdated(
        oldTier,
        newTier,
      );
    }

    const {
      percentDiscountMaximumNumberOfUsesType: oldDiscountType,
      percentDiscountMaximumNumberOfUses: oldDiscountMaximum,
      freeDeliveryMaximumNumberOfUsesType: oldDeliveryType,
      freeDeliveryMaximumNumberOfUses: oldDeliveryMaximum,
    } = oldTier;

    const {
      percentDiscountMaximumNumberOfUsesType: newDiscountType,
      percentDiscountMaximumNumberOfUses: newDiscountMaximum,
      freeDeliveryMaximumNumberOfUsesType: newDeliveryType,
      freeDeliveryMaximumNumberOfUses: newDeliveryMaximum,
    } = newTier;

    if (
      oldDiscountType === NumberOfUsesType.UNTIL_THEY_LOSE_THEIR_TIER &&
      newDiscountType === NumberOfUsesType.LIMITED_USAGE
    ) {
      await this.customerRepository.updateCustomerUsageByTier(
        newTier._id,
        'percentDiscountRemainingNumberOfUses',
        newTier.percentDiscountMaximumNumberOfUses,
      );
    }

    if (
      oldDiscountType === NumberOfUsesType.LIMITED_USAGE &&
      newDiscountType === NumberOfUsesType.LIMITED_USAGE &&
      oldDiscountMaximum !== newDiscountMaximum
    ) {
      await this.customerRepository.addRemainingUsesByTier(
        newTier._id,
        'percentDiscountRemainingNumberOfUses',
        newTier.percentDiscountMaximumNumberOfUses -
          oldTier.percentDiscountMaximumNumberOfUses,
        newTier.percentDiscountMaximumNumberOfUses,
      );
    }

    if (
      oldDeliveryType === NumberOfUsesType.UNTIL_THEY_LOSE_THEIR_TIER &&
      newDeliveryType === NumberOfUsesType.LIMITED_USAGE
    ) {
      await this.customerRepository.updateCustomerUsageByTier(
        newTier._id,
        'freeDeliveryRemainingNumberOfUses',
        newTier.freeDeliveryMaximumNumberOfUses,
      );
    }

    if (
      oldDeliveryType === NumberOfUsesType.LIMITED_USAGE &&
      newDeliveryType === NumberOfUsesType.LIMITED_USAGE &&
      oldDeliveryMaximum !== newDeliveryMaximum
    ) {
      await this.customerRepository.addRemainingUsesByTier(
        newTier._id,
        'freeDeliveryRemainingNumberOfUses',
        newTier.freeDeliveryMaximumNumberOfUses -
          oldTier.freeDeliveryMaximumNumberOfUses,
        newTier.freeDeliveryMaximumNumberOfUses,
      );
    }
  }

  @LogError()
  @OnEvent('customer.passes.added')
  async onLoyaltyCardStatusChanged(customer: CustomerDocument) {
    await this.customerNotificationService.fireOnWalletPassAdded(customer);
  }

  @LogError()
  @OnEvent('store.updated')
  async onStoreUpdate(store: StoreDocument) {
    await this.customerRepository.updateEmbeddedStore(store);
  }

  @LogError()
  @OnEvent('punchcard.achievement.updated')
  async handlePunchCardAchievementUpdated(
    punchCard: PunchCardDocument,
    achievement: Achievement,
    oldAchievement: Achievement,
  ) {
    const isAchievementEasier =
      achievement?.requirement?.targetValue <
      oldAchievement?.requirement?.targetValue;
    if (!isAchievementEasier) return;

    await this.customerRepository.handlePunchCardCompletionLowered(
      punchCard,
      this.punchCardService.getCompletionThreshold(punchCard),
    );
  }

  @LogError()
  @OnEvent('punchcard.achievement.deleted')
  async handlePunchCardAchievementDeleted(
    punchCard: PunchCardDocument,
    deletedAchievement: Achievement,
  ) {
    const wasHighestAchievement = punchCard.achievements.some(
      (achievement) =>
        achievement?.requirement?.targetValue >
        deletedAchievement?.requirement?.targetValue,
    );
    if (!wasHighestAchievement) return;

    await this.customerRepository.handlePunchCardCompletionLowered(
      punchCard,
      this.punchCardService.getCompletionThreshold(punchCard),
    );
  }

  @LogError()
  @OnEvent('benefit.updated')
  async handleBenefitUpdated(benefit: BenefitDocument) {
    await this.customerRepository.updateCustomerEarnedBenefits(benefit);
  }
}
