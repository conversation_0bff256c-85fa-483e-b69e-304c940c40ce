export enum PaymentLogAction {
  'PaymentProcessedFromSkipCash' = 'PaymentProcessedFromSkipCash',
  SKIP_CASH_WEBHOOK_FIRED = 'SkipCashWebhookFired',
  SKIP_CASH_WEBHOOK_FAILED = 'SkipCashWebhookFailed',
  SKIP_CASH_WEBHOOK_PENDING = 'SkipCashWebhookPending',

  'VistaMoneyWebhookFired' = 'VistaMoneyWebhookFired',
  'PaymentProcessedFromDibsy' = 'PaymentProcessedFromDibsy',
  'DibsyWebhookFired' = 'DibsyWebhookFired',
  'StripWebhookFired' = 'StripWebhookFired',
  'TessCreditWebhookFired' = 'TessCreditWebhookFired',
  'MyFatoorahPaymentExecute' = 'MyFatoorahPaymentExecute',
  'MyFatoorahPaymentCreated' = 'MyFatoorahPaymentCreated',
  'TessPaymentProcessed' = 'tessPaymentProcessed',
  'TessPaymentWebhookFired' = 'TessPaymentWebhookFired',
  MyFatoorahSendInvoiceLink = 'MyFatoorahSendInvoiceLink',
  MY_FATOORAH_REDIRECTED = 'MyFatoorahRedirected',
  MY_FATOORAH_WEBHOOK_FIRED = 'MyFatoorahWebhookFired',
  MY_FATOORAH_WEBHOOK_FAILED = 'MyFatoorahWebhookFailed',
  DIBSY_CREATE_PAYMENT_LINK = 'DibsyCreatePaymentLink',
  TAP_CREATE_PAYMENT_LINK = 'TapCreatePaymentLink',
  TAP_CREATE_PAYMENT_LINK_ERROR = 'TapCreatePaymentLinkError',
  TAP_AFTER_DONE = 'TapAfterDone',
  TAP_UPDATE_STATE = 'TapUpdateStatus',
}
