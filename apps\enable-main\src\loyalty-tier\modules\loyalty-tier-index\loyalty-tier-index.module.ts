import { Module } from '@nestjs/common';
import { CompanyModule } from '../../../company/company.module';
import { LoyaltyTierRepositoryModule } from '../loyalty-tier-repository/loyalty-tier-repository.module';
import { LoyaltyTierIndexService } from './loyalty-tier-index.service';

@Module({
  providers: [LoyaltyTierIndexService],
  imports: [CompanyModule, LoyaltyTierRepositoryModule],
  exports: [LoyaltyTierIndexService],
})
export class LoyaltyTierIndexModule {}
