import {
  Benefit,
  CreateBenefitDto,
  GenericExceptionFilter,
  GetAllBenefitDto,
  IndexResultDto,
  LoyaltyTierBenefitConfigDto,
  TransformInterceptor,
  UpdateBenefitDto,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Get,
  Inject,
  Param,
  Patch,
  Post,
  Query,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBasicAuth, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { CurrentUserService } from '../../shared/services/current-user/current-user.service';
import { BenefitServiceInterface } from '../services/benefit-service.interface';

@Controller('benefit')
@ApiTags('Benefit')
@UseFilters(GenericExceptionFilter)
@UseInterceptors(TransformInterceptor)
@SetMetadata('module', 'benefit')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class BenefitController {
  constructor(
    @Inject(BenefitServiceInterface)
    private benefitService: BenefitServiceInterface,
    private currentUserService: CurrentUserService,
  ) {}

  @Get()
  @SetMetadata('action', 'index')
  async index(
    @Query() getAllBenefitDto: GetAllBenefitDto,
  ): Promise<IndexResultDto<Benefit>[]> {
    getAllBenefitDto.companyId =
      this.currentUserService.getCurrentCompanyId() ??
      getAllBenefitDto.companyId;
    return await this.benefitService.index(getAllBenefitDto);
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(@Body() createBenefitDto: CreateBenefitDto): Promise<Benefit> {
    createBenefitDto.companyId =
      this.currentUserService.getCurrentCompanyId() ??
      createBenefitDto.companyId;
    return await this.benefitService.create(createBenefitDto);
  }

  @Patch()
  @SetMetadata('action', 'update')
  async update(
    @Param('id') id: string,
    @Body() updateBenefitDto: UpdateBenefitDto,
  ): Promise<Benefit> {
    updateBenefitDto.companyId =
      this.currentUserService.getCurrentCompanyId() ??
      updateBenefitDto.companyId;
    updateBenefitDto._id = new Types.ObjectId(id);

    return await this.benefitService.update(updateBenefitDto);
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  async getDetails(@Param('id') id: string): Promise<Benefit> {
    return await this.benefitService.findOneById(new Types.ObjectId(id));
  }

  @Get('program-config')
  @SetMetadata('action', 'get_program_config')
  async getProgramConfig() {
    const programConfigs = [LoyaltyTierBenefitConfigDto];

    const result = programConfigs.reduce((acc, ConfigClass) => {
      const config = new ConfigClass();
      acc[config.programName] = config.getPropertiesWithoutDefaultValue();
      return acc;
    }, {});

    return result;
  }
}
