import {
  AssignMasterToSlaveDto,
  GenericException<PERSON>ilter,
  MenuPosToPush,
  MenuToCreate,
  MenuToDuplicate,
  MenuToIndex,
  MenuToUpdate,
  responseCode,
  SyncOrdableMenuDto,
  TalabatMenuToFetch,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  SetMetadata,
  UploadedFile,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiExcludeEndpoint,
  ApiTags,
} from '@nestjs/swagger';
import { Request, Response } from 'express';
import { memoryStorage } from 'multer';
import { HelperService } from '../../../shared/services/helper/helper.service';
import { MenuTalabatService } from './../../services/menu-talabat/menu-talabat.service';
import { MenuService } from './../../services/menu/menu.service';

@Controller('menu')
@ApiTags('Restaurant Menu')
@SetMetadata('module', 'menu')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class MenuController {
  constructor(
    private menuService: MenuService,
    private helperService: HelperService,
    private menuTalabatService: MenuTalabatService,
  ) {}

  @Get()
  @SetMetadata('action', 'get_all')
  async index(
    @Query() menuToIndex: MenuToIndex,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      menuToIndex.company = req['company_id']
        ? req['company_id']
        : menuToIndex.company;
      menuToIndex.branches = req['branches'] ? req['branches'].toString() : '';
      menuToIndex.currentUser = req['current'];

      const menus = await this.menuService.index(menuToIndex);
      const counts = await this.menuService.getTotalNumberOfMenus(menuToIndex);

      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETALL,
        'success to get all Menus',
        { menus, totalMenus: counts },
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Get(':id')
  @SetMetadata('action', 'get_details')
  async get_details(@Param('id') id: string, @Res() res: Response) {
    try {
      const menu = await this.menuService.getDetails(id);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'success to get all Menus',
        menu,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('talabat')
  @SetMetadata('action', 'get_details')
  async fetchMenuTalabatService(
    @Body() talabatMenuToFetch: TalabatMenuToFetch,
    @Res() res: Response,
  ) {
    try {
      const menu =
        await this.menuTalabatService.onFetchingTalabatMenu(talabatMenuToFetch);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_GETDETAILS,
        'success To POST the menu',
        menu,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('push')
  @SetMetadata('action', 'pushMenu')
  async pushMenu(
    @Body() menuToPush: MenuPosToPush,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      menuToPush.currentUser = req['current'];
      menuToPush.company = req['company_id'];
      const warehouse = await this.menuService.pushMenu(menuToPush);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success to push the Menu',
        warehouse,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post()
  @SetMetadata('action', 'create')
  async create(
    @Body() menuToCreate: MenuToCreate,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      menuToCreate.currentUser = req['current'];
      menuToCreate.company = req['company_id'];
      const warehouse = await this.menuService.create(menuToCreate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success to create new Menu',
        warehouse,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('duplicate')
  @SetMetadata('action', 'duplicate')
  async duplicate(
    @Body() menuToDuplicate: MenuToDuplicate,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      const duplicate = await this.menuService.duplicateMenu(
        menuToDuplicate,
        req['branches'],
      );
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success to create new Menu',
        duplicate,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('sync/ordable')
  @SetMetadata('action', 'sync-ordable')
  async syncOrdableMenu(
    @Body() syncOrdableMenuDto: SyncOrdableMenuDto,
    @Res() res: Response,
  ) {
    try {
      const response =
        await this.menuService.syncOrdableMenu(syncOrdableMenuDto);
      return this.helperService.handelSuccessResponse(
        responseCode.GENERAL_SUCCESS,
        'Success to sync Ordable menu',
        response,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Put(':id')
  @SetMetadata('action', 'update')
  async update(
    @Body() menuToUpdate: MenuToUpdate,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      menuToUpdate.currentUser = req['current'];
      const menu = await this.menuService.update(menuToUpdate);
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success to update new Menu',
        menu,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Delete(':id')
  @SetMetadata('action', 'remove')
  async remove(
    @Param('id') id: string,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      const menu = await this.menuService.remove({
        _id: id,
        currentUser: req['current'],
      });
      return this.helperService.handelSuccessResponse(
        responseCode.SUCCESS_TO_CREATE,
        'success to remove new Menu',
        menu,
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @Post('upload/pdf')
  @SetMetadata('action', 'uploadMenuPdf')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: 'multipart/form-data',
    required: true,
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file', { storage: memoryStorage() }))
  async uploadFile(
    @UploadedFile('file') file: Express.Multer.File,
    @Res() res: Response,
  ) {
    try {
      if (file) {
        const fileName = await this.menuService.uploadPdfMenuFile(file);
        return this.helperService.handelSuccessResponse(
          responseCode.SUCCESS_TO_UPDATE,
          'Success To upload the file',
          { filePath: fileName },
          res,
        );
      }
      return this.helperService.handelError(
        { error: 'Error while uploading' },
        res,
      );
    } catch (err) {
      return this.helperService.handelError(err, res);
    }
  }

  @UseInterceptors(TransformInterceptor)
  @UseFilters(GenericExceptionFilter)
  @Post('assignMasterToSlave')
  @SetMetadata('action', 'assignMasterToSlave')
  async assignMasterToSlave(
    @Body() assignMasterToSlaveDto: AssignMasterToSlaveDto,
  ) {
    return await this.menuService.assignMasterToSlave(assignMasterToSlaveDto);
  }
}
