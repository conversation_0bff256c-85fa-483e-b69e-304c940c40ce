import { DibsyAmount } from '../../dto/dibsy/dibsy-amount.dto';

export class DibsyLink {
  href: string;
  type: string;
}

export class DibsyLinks {
  self: DibsyLink;
  dashboard: DibsyLink;
  checkout: DibsyLink;
}

export enum DibsyMode {
  TEST = 'test',
  LIVE = 'live',
}

export enum DibsyResource {
  PAYMENT_LINK = 'payment-link',
  SUBSCRIPTION = 'subscription',
  INVOICE = 'invoice',
}

export class DibsyCreatePaymentLinkResponse {
  id: string;
  resource: DibsyResource;
  mode: DibsyMode;
  name: string;
  active: boolean;
  amount: DibsyAmount;
  description: string;
  redirectUrl: string;
  reusable: boolean;
  notifyWithSms: boolean;
  expiresAt: string;
  totalPayments: number;
  createdAt: string;
  organizationId: string;
  _links: DibsyLinks;
}
