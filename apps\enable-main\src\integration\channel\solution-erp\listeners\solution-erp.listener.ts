import { LogError, LoggerService, OrderDocument } from '@app/shared-stuff';
import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';

import { CompanyService } from '../../../../company/services/company/company.service';
import { IntegrationLogRepositoryInterface } from '../../../integration-log/repositories/interfaces/integration-log.repository.interface';
import { CreateSolutionErpOrderDto } from '../dtos/create-solution-erp-order.dto';
import { SolutionERPServiceInterface } from '../services/solution-erp.service.interface';

@Injectable()
export class SolutionERPListener {
  private readonly loggerService = new LoggerService(SolutionERPListener.name);
  constructor(
    @Inject('SolutionERPServiceInterface')
    private solutionERPService: SolutionERPServiceInterface,
    @Inject('IntegrationLogRepositoryInterface')
    private integrationLogRepository: IntegrationLogRepositoryInterface,
    private companyService: CompanyService,
  ) {}

  @OnEvent('order.acknowledged')
  @LogError()
  async createSolutionErpOrder(order: OrderDocument) {
    this.loggerService.log(
      'first line in createSolutionErpOrder with order details :',
      order,
    );
    const enableCompanyId = order['company']['_id']
      ? order['company']['_id']
      : order['company'];
    const company = await this.companyService.get_details(enableCompanyId);
    let solutionErpResponse: unknown;
    let createSolutionErpOrderDto: CreateSolutionErpOrderDto;
    if (company && company.usingSolutionErpPos) {
      this.loggerService.log(
        'createSolutionErpOrder inside usingSolutionErpPos condition  with order details :',
        order,
      );
      createSolutionErpOrderDto =
        await this.solutionERPService.constructCreateSolutionErpOrderDto(order);
      solutionErpResponse = await this.solutionERPService.create(
        createSolutionErpOrderDto,
        company.solutionErpApiKey,
      );
      this.loggerService.log(
        'after creating erp order , solutionErpResponse :',
        createSolutionErpOrderDto,
        solutionErpResponse,
      );
      await this.integrationLogRepository.create({
        itemId: order._id.toString(),
        action: 'Solution ERP Integration',
        requestBody: createSolutionErpOrderDto,
        responseBody: solutionErpResponse,
        requestStatus: solutionErpResponse?.['status'],
      });
    }
  }
}
