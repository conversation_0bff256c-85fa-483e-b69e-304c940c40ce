import {
  ObjectIdTransform,
  TriggerAction,
  TriggerModule,
} from '@app/shared-stuff';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { Types } from 'mongoose';

export class FireTriggerBackdoorDto {
  @IsNotEmpty()
  @IsEnum(TriggerModule)
  triggerModule: TriggerModule;

  @IsNotEmpty()
  @IsString()
  triggerAction: TriggerAction;

  @ObjectIdTransform()
  @IsNotEmpty()
  customerId: Types.ObjectId;

  @ObjectIdTransform()
  @IsNotEmpty()
  brandId: Types.ObjectId;
}
