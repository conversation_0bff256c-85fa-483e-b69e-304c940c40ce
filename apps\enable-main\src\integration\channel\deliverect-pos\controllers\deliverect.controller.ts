import {
  GenericExceptionFilter,
  responseCode,
  TransformInterceptor,
} from '@app/shared-stuff';
import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Req,
  SetMetadata,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { IntegrationLogInterceptor } from '../../../integration-log/interceptors/integration-log.interceptor';
import { RegisterNewPosDto } from '../dto/register-new-pos.dto';
import { DeliverectPosService } from '../services/deliverect-pos.service';

@Controller('deliverect/pos')
@UseInterceptors(IntegrationLogInterceptor)
@UseFilters(GenericExceptionFilter)
@ApiTags('Deliverect Integration')
@SetMetadata('module', 'deliverect')
@ApiBearerAuth('authenticate-token')
@ApiBasicAuth('authenticate-apikey')
export class DeliverectPosController {
  constructor(private deliverectPosService: DeliverectPosService) {}

  @Get('product/:companyId/:locationId')
  @SetMetadata('public', 'true')
  @HttpCode(HttpStatus.OK)
  async getPostProducts(
    @Param('companyId') companyId: string,
    @Param('locationId') locationId: string,
  ) {
    return await this.deliverectPosService.onGetProducts(companyId, locationId);
  }

  @Get('tables/:companyId/:locationId')
  @SetMetadata('public', 'true')
  @HttpCode(HttpStatus.OK)
  async getPosTables(@Param('locationId') locationId: string) {
    return await this.deliverectPosService.onGetTables(locationId);
  }

  @Get('floors/:companyId/:locationId')
  @SetMetadata('public', 'true')
  @HttpCode(HttpStatus.OK)
  async getPosFloors(@Param('locationId') locationId: string) {
    return await this.deliverectPosService.onGetFloors(locationId);
  }

  @Post('order/:companyId')
  @SetMetadata('public', 'true')
  @HttpCode(HttpStatus.OK)
  async postNewOrder(@Body() data: any) {
    return await this.deliverectPosService.onNewOrder(data);
  }

  @Post('register')
  @SetMetadata('public', 'true')
  @HttpCode(HttpStatus.OK)
  async registerNewPos(@Body() deliverectData: RegisterNewPosDto) {
    return await this.deliverectPosService.onNewLocationRegistered(
      deliverectData,
    );
  }

  @Get('accounts')
  @SetMetadata('action', 'getDeliverectLinkedAccounts')
  @UseInterceptors(TransformInterceptor)
  @HttpCode(responseCode.DELIVERECT_POS_SUCCESS)
  async getLinkedAccounts(@Req() req: Request) {
    return await this.deliverectPosService.getLinkedAccounts(req['company_id']);
  }

  @Get('locations')
  @SetMetadata('action', 'getDeliverectLocations')
  @UseInterceptors(TransformInterceptor)
  @HttpCode(responseCode.DELIVERECT_POS_SUCCESS)
  async getLocations(@Req() req: Request) {
    return await this.deliverectPosService.getLocations(req['company_id']);
  }
}
