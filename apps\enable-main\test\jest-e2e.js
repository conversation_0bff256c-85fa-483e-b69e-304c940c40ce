module.exports = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '../../../.',
  modulePaths: ['<rootDir>'],
  testEnvironment: 'node',
  testRegex: '-order\\.e2e-spec.ts$',

  moduleNameMapper: {
    '^@app/shared-stuff(|/.*)$': '<rootDir>/libs/shared-stuff/src/$1',
    '^axios$': require.resolve('axios'),
    'puppeteer-core': 'puppeteer',
  },

  transform: {
    '^.+\\.jsx?$': 'babel-jest',
    '^.+\\.tsx?$': 'ts-jest',
  },
  transformIgnorePatterns: [
    '/node_modules/(?!axios/).+\\.js$',
    '/node_modules/(?!puppeteer/).+\\.js$',
  ],
  testRunner: 'jest-circus/runner',
};
