import {
  <PERSON>Name,
  PassConfigSchema,
  PassTypeIdentifierConfigSchema,
  SharedStuffModule,
} from '@app/shared-stuff';
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BranchModule } from '../branch/branch.module';
import { BrandModule } from '../brand/brand.module';
import { CompanyModule } from '../company/company.module';
import { CouponModule } from '../coupon/coupon.module';
import { CustomerCodeModule } from '../customer/modules/customer-code/customer-code.module';
import { CustomerPassModule } from '../customer/modules/customer-pass/customer-pass.module';
import { CustomerPunchCardModule } from '../customer/modules/customer-punch-card/customer-punch-card.module';
import { CustomerReadModule } from '../customer/modules/customer-read/customer-read.module';
import { CustomerTierInfoModule } from '../customer/modules/customer-tier-info/customer-tier-info.module';
import { IntegrationLogModule } from '../integration/integration-log/integration-log.module';
import { LocationModule } from '../location/location.module';
import { LoyaltyTierIndexModule } from '../loyalty-tier/modules/loyalty-tier-index/loyalty-tier-index.module';
import { LoyaltyTierReadModule } from '../loyalty-tier/modules/loyalty-tier-read/loyalty-tier-read.module';
import { CompletedPunchCardModule } from '../punch-card/modules/completed-punch-card/completed-punch-card.module';
import { PunchCardReadModule } from '../punch-card/modules/punch-card-read/punch-card-read.module';
import { SharedModule } from '../shared/shared.module';
import { StorageModule } from '../storage/storage.module';
import { PassConfigController } from './controllers/pass-config.controller';
import { PassesController } from './controllers/passes.controller';
import { PassLogInterceptor } from './interceptors/pass-log.interceptor';
import { PassLog, PassLogSchema } from './models/pass-log.model';
import { PassConfigRepository } from './repositories/pass-config.repository';
import { PassTypeIdentifierConfigRepository } from './repositories/pass-type-identifier-config.repository';
import { EnablePassesService } from './services/enable-passes/enable-passes.service';
import { GooglePassesHttpService } from './services/google-passes-http/google-passes-http.service';
import { GooglePassesService } from './services/google-passes/google-passes.service';
import { GooglePassesServiceInterface } from './services/google-passes/google-passes.service.interface';
import { PassConfigService } from './services/pass-config/pass-config.service';
import { PassesCronService } from './services/passes-cron/passes-cron.service';
import { PassesFieldService } from './services/passes-field/passes-field.service';
import { PassesFieldServiceInterface } from './services/passes-field/passes-field.service.interface';
import { PassesImageService } from './services/passes-image/passes-image.service';
import { PassesImageServiceInterface } from './services/passes-image/passes-image.service.interface';
import { PassesListenerService } from './services/passes-listener/passes-listener.service';
import { PassesRefreshService } from './services/passes-refresh/passes-refresh.service';
import { PassesService } from './services/passes.service';
import { PKPassPassesService } from './services/pkpass-passes/pkpass-passes.service';

@Module({
  controllers: [PassesController, PassConfigController],
  providers: [
    { provide: PassesImageServiceInterface, useClass: PassesImageService },
    { provide: PassesFieldServiceInterface, useClass: PassesFieldService },
    { provide: GooglePassesServiceInterface, useClass: GooglePassesService },
    PassesService,
    GooglePassesHttpService,
    PassesListenerService,
    PassLogInterceptor,
    PassTypeIdentifierConfigRepository,
    PassConfigRepository,
    PassConfigService,
    PKPassPassesService,
    EnablePassesService,
    PassesCronService,
    PassesRefreshService,
  ],
  imports: [
    SharedModule,
    SharedStuffModule,
    CustomerReadModule,
    CustomerPassModule,
    CustomerTierInfoModule,
    CustomerPunchCardModule,
    CustomerCodeModule,
    LoyaltyTierReadModule,
    LoyaltyTierIndexModule,
    PunchCardReadModule,
    CouponModule,
    BrandModule,
    CompanyModule,
    HttpModule,
    BranchModule,
    IntegrationLogModule,
    CompletedPunchCardModule,
    LocationModule,
    StorageModule,
    MongooseModule.forFeature([
      { name: PassLog.name, schema: PassLogSchema },
      {
        name: CollectionName.PASS_TYPE_IDENTIFIER_CONFIG,
        schema: PassTypeIdentifierConfigSchema,
      },
      { name: CollectionName.PASS_CONFIG, schema: PassConfigSchema },
    ]),
  ],
  exports: [PassesService, GooglePassesServiceInterface, PassConfigService],
})
export class PassesModule {}
