import { CollectionName, LoyaltyTierSchema } from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { LoyaltyTierRepository } from './loyalty-tier.repository';
import { LoyaltyTierRepositoryInterface } from './loyalty-tier.repository.interface';

@Module({
  providers: [
    {
      provide: LoyaltyTierRepositoryInterface,
      useClass: LoyaltyTierRepository,
    },
  ],
  imports: [
    MongooseModule.forFeature([
      { name: CollectionName.LOYALTY_TIER, schema: LoyaltyTierSchema },
    ]),
  ],
  exports: [LoyaltyTierRepositoryInterface],
})
export class LoyaltyTierRepositoryModule {}
