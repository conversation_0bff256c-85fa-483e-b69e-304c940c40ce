import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { BranchModule } from '../../../branch/branch.module';
import { BrandModule } from '../../../brand/brand.module';
import { LocationModule } from '../../../location/location.module';
import { CustomerRepositoryModule } from '../customer-repository/customer-repository.module';
import { CustomerWriteService } from './customer-write.service';
import { CustomerWriteServiceInterface } from './customer-write.service.interface';

@Module({
  providers: [
    {
      provide: CustomerWriteServiceInterface,
      useClass: CustomerWriteService,
    },
  ],
  imports: [
    EventEmitterModule,
    LocationModule,
    BranchModule,
    BrandModule,
    CustomerRepositoryModule,
  ],
  exports: [CustomerWriteServiceInterface],
})
export class CustomerWriteModule {}
