// regarding https://e-butler.atlassian.net/browse/EBL-6204
db.coupons.updateMany(
  {
    $or: [
      { benefit: 'flat_discount', flatDiscountAmount: { $exists: true } },
      { benefit: 'percent_discount', percentDiscount: { $exists: true } },
    ],
  },
  [
    {
      $set: {
        titleEn: {
          $cond: [
            { $eq: ['$benefit', 'flat_discount'] },
            {
              $concat: [
                { $toString: '$flatDiscountAmount' },
                ' ',
                '$currency',
                ' Coupon',
                ' for ',
                { $toString: '$loyaltyPointCost' },
              ],
            },
            {
              $concat: [
                { $toString: '$percentDiscount' },
                '% Coupon',
                ' for ',
                { $toString: '$loyaltyPointCost' },
              ],
            },
          ],
        },
        titleAr: {
          $cond: [
            { $eq: ['$benefit', 'flat_discount'] },
            {
              $concat: [
                { $toString: '$flatDiscountAmount' },
                ' ',
                '$currency',
                ' كوبون',
                ' for ',
                { $toString: '$loyaltyPointCost' },
              ],
            },
            {
              $concat: [
                { $toString: '$percentDiscount' },
                '% كوبون',
                ' for ',
                { $toString: '$loyaltyPointCost' },
              ],
            },
          ],
        },
        benefits: [
          {
            companyId: '$companyId',
            titleEn: {
              $cond: [
                { $eq: ['$benefit', 'flat_discount'] },
                {
                  $concat: [
                    { $toString: '$flatDiscountAmount' },
                    ' ',
                    '$currency',
                    ' Coupon',
                    ' for ',
                    { $toString: '$loyaltyPointCost' },
                  ],
                },
                {
                  $concat: [
                    { $toString: '$percentDiscount' },
                    '% Coupon',
                    ' for ',
                    { $toString: '$loyaltyPointCost' },
                  ],
                },
              ],
            },
            titleAr: {
              $cond: [
                { $eq: ['$benefit', 'flat_discount'] },
                {
                  $concat: [
                    { $toString: '$flatDiscountAmount' },
                    ' ',
                    '$currency',
                    ' كوبون',
                    ' for ',
                    { $toString: '$loyaltyPointCost' },
                  ],
                },
                {
                  $concat: [
                    { $toString: '$percentDiscount' },
                    '% كوبون',
                    ' for ',
                    { $toString: '$loyaltyPointCost' },
                  ],
                },
              ],
            },
            value: {
              $cond: [
                { $eq: ['$benefit', 'flat_discount'] },
                '$flatDiscountAmount',
                '$percentDiscount',
              ],
            },
            type: {
              $cond: [
                { $eq: ['$benefit', 'flat_discount'] },
                'fixed_discount',
                'percentage_discount',
              ],
            },
            appliedTo: 'order_total_amount',
            config: {
              programName: 'COUPON_PROGRAM',
              maximumUsage: 1,
              maximumUsageType: 'LIMITED',
              preserveUsage: false,
              minimumAmountToRedeem: 0,
            },
          },
        ],
      },
    },
    {
      $unset: ['flatDiscountAmount', 'percentDiscount', 'benefit', 'currency'],
    },
  ],
);

print(
  '✅ flat‐ and percent‐discount coupons migrated with correct titles (including loyalty cost).',
);

db.coupons.find({ 'benefits._id': { $exists: false } }).forEach((coupon) => {
  const { _id: couponId, companyId, benefits: embeddedBens } = coupon;

  const updatedBenefits = embeddedBens.map((b) => {
    // remove any “ Coupon” (or “كوبون”) suffix
    const cleanTitleEn = b.titleEn.replace(/ ?Coupon/gi, '');
    const cleanTitleAr = b.titleAr.replace(/ ?كوبون/gi, '');

    // prepare the standalone benefit document
    const benefitDoc = {
      companyId,
      titleEn: cleanTitleEn,
      titleAr: cleanTitleAr,
      value: b.value,
      type: b.type,
      appliedTo: b.appliedTo,
      config: b.config,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    const { insertedId } = db.benefits.insertOne(benefitDoc);

    // return original fields + cleaned titles + new _id
    return {
      ...b,
      titleEn: cleanTitleEn,
      titleAr: cleanTitleAr,
      _id: insertedId,
    };
  });

  // 3. Write the enriched benefits array back into the coupon
  db.coupons.updateOne(
    { _id: couponId },
    { $set: { benefits: updatedBenefits } },
  );
});

print(
  '✅ All coupons’ embedded benefits now include real _id’s and their titles no longer mention “Coupon”/“كوبون”.',
);
