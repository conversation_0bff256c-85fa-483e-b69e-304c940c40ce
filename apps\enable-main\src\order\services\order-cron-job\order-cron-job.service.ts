import {
  aggregatorOrderSources,
  CollectionName,
  LogError,
  LoggerService,
  LoyaltyStatus,
  OrderDelayTag,
  OrderDeliveryAction,
  OrderDeliveryType,
  OrderDocument,
  OrderPaymentStatus,
  OrderStatusEnum,
  OrderTransitionTrigger,
  TriggerAction,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment-timezone';
import { Model, Types } from 'mongoose';
import { CompanyService } from '../../../company/services/company/company.service';
import { OrderDeliveryService } from '../order-delivery/order-delivery.service';
import { OrderNotificationService } from '../order-notification/order-notification.service';
import { OrderStatusService } from '../order-status/order-status.service';

@Injectable()
export class OrderCronJobService {
  private readonly logger = new LoggerService(OrderCronJobService.name);
  constructor(
    @InjectModel(CollectionName.ORDER) private orderModel: Model<OrderDocument>,
    private orderDeliveryService: OrderDeliveryService,
    private orderNotificationService: OrderNotificationService,
    private companyService: CompanyService,
    private orderStatusService: OrderStatusService,
  ) {}

  @OnEvent('cron.every5mins')
  @LogError()
  async handleDelayedAcknowledgeOrders() {
    const manualAcknowledgementCompanyIds =
      await this.companyService.findManualAcknowledgementCompanyIds();
    await this.handleDelayedFirstAcknowledgeOrders(
      manualAcknowledgementCompanyIds,
    );
    await this.handleDelayedSecondAcknowledgeOrders(
      manualAcknowledgementCompanyIds,
    );
  }

  async handleDelayedFirstAcknowledgeOrders(
    manualAckowledgementCompanyIds: Types.ObjectId[],
  ) {
    const firstAcknowledgeOrders = await this.orderModel.find({
      delivery_type: OrderDeliveryType.scheduled,
      status: { $eq: OrderStatusEnum.UNASSIGNED },
      company: { $in: manualAckowledgementCompanyIds },
      deletedAt: null,
    });

    this.logger.log(
      `Checking delayed first acknowledge for ${firstAcknowledgeOrders.length} orders`,
    );

    if (firstAcknowledgeOrders.length > 0)
      await this.fireTriggerOnFirstAcknowledgeDelayedOrders(
        firstAcknowledgeOrders,
      );
  }

  @OnEvent('cron.every5mins')
  @LogError()
  async handleDelayedCompletedOrders() {
    const startDay = moment.utc().startOf('day').toDate();
    const endDay = moment.utc().endOf('day').toDate();
    const completedOrders = await this.orderModel.find({
      delivery_date: { $gte: startDay, $lte: endDay },
      status: { $eq: OrderStatusEnum.COMPLETED },
      firedTriggers: {
        $ne: TriggerAction.THREE_HOURS_AFTER_ORDER_COMPLETION,
      },
    });
    if (completedOrders.length > 0)
      await this.fireTriggerOnDelayedCompletedOrders(completedOrders);
  }

  @OnEvent('cron.every5mins')
  @LogError()
  async handleScheduledOrders() {
    const currentDate = moment().utc();
    const scheduledOrders = await this.orderModel.find({
      deletedAt: { $eq: null },
      isOperable: { $eq: false },
      status: { $eq: OrderStatusEnum.SCHEDULED },
      delivery_type: {
        $in: [OrderDeliveryType.scheduled, OrderDeliveryType.schedule_later],
      },
    });
    for (let i = 0; i < scheduledOrders.length; i++) {
      const orderDeliveryDate = moment.utc(scheduledOrders[i]?.delivery_date);
      const orderPickupDate = moment.utc(scheduledOrders[i]?.pickup_date);

      if (scheduledOrders[i].is_gift)
        await this.handleScheduledGiftOrders(
          scheduledOrders[i],
          currentDate,
          orderDeliveryDate,
        );

      if (
        (scheduledOrders[i].delivery_action !=
          OrderDeliveryAction.IN_STORE_PICKUP &&
          orderDeliveryDate.diff(currentDate, 'minutes') <= 240) ||
        (scheduledOrders[i].delivery_action ==
          OrderDeliveryAction.IN_STORE_PICKUP &&
          orderPickupDate.diff(currentDate, 'minutes') <= 30)
      ) {
        scheduledOrders[i].isOperable = true;
        await this.orderStatusService.changeOrderStatus(
          scheduledOrders[i],
          OrderStatusEnum.PREPARING,
          OrderTransitionTrigger.ACKNOWLEDGED,
          scheduledOrders[i].updatedBy,
        );
        await this.orderDeliveryService.handleOrderDeliveryMethodCreation({
          order: scheduledOrders[i],
          currentUser: scheduledOrders[i].createdBy,
          thirdParty: scheduledOrders[i].deliveryParty,
          deliveryMethod: scheduledOrders[i].deliveryMethod,
          driverId: scheduledOrders[i].driver,
        });
      }
    }
  }

  // TODO : need Refactor
  @OnEvent('cron.every1min')
  @LogError()
  async handleDelayedOrders() {
    const currentDate = moment().utc();
    // Handel Delayed Orders
    //  Ready Delay
    const notReadyOrders = await this.orderModel.find({
      is_ready: false,
      delayTag: { $ne: OrderDelayTag.ReadyDelay },
      status: { $eq: OrderStatusEnum.PREPARING },
    });
    for (let i = 0; i < notReadyOrders.length; i++) {
      const pickUpDate = moment.utc(notReadyOrders[i]['pickup_date']);

      if (!pickUpDate.isValid()) {
        continue;
      }
      const diff = pickUpDate.diff(currentDate, 'minutes');
      if (diff <= 5 && pickUpDate.isAfter(currentDate)) {
        notReadyOrders[i].delayTag = OrderDelayTag.ReadyDelay;
        const order = await notReadyOrders[i].save();
        await this.orderNotificationService.fireOrderNotificationTrigger(
          order,
          TriggerAction.ON_READY_DELAY,
        );
      }
    }

    // Pickup Delay
    const notPickedUpOrders = await this.orderModel.find({
      delayTag: { $ne: OrderDelayTag.PickupDelay },
      is_ready: true,
      status: {
        $nin: [
          OrderStatusEnum.IN_ROUTE,
          OrderStatusEnum.COMPLETED,
          OrderStatusEnum.CANCELED,
        ],
      },
    });

    for (let i = 0; i < notPickedUpOrders.length; i++) {
      const pickUpDate = moment.utc(notPickedUpOrders[i]['pickup_date']);
      if (!pickUpDate.isValid()) {
        continue;
      }
      if (pickUpDate.isBefore(currentDate)) {
        notPickedUpOrders[i].delayTag = OrderDelayTag.PickupDelay;
        await this.orderNotificationService.onOrderDelayed(
          notPickedUpOrders[i],
        );
        const order = await notPickedUpOrders[i].save();
        await this.orderNotificationService.fireOrderNotificationTrigger(
          order,
          TriggerAction.ON_PICKUP_DELAY,
        );
      }
    }

    // Delivery Delay
    const notDeliveredOrders = await this.orderModel.find({
      delayTag: { $ne: OrderDelayTag.DeliveryDelay },
      status: { $eq: OrderStatusEnum.IN_ROUTE },
    });
    for (let i = 0; i < notDeliveredOrders.length; i++) {
      const deliveryDate = moment.utc(notDeliveredOrders[i]['delivery_date']);
      if (!deliveryDate.isValid()) {
        continue;
      }
      if (deliveryDate.isBefore(currentDate)) {
        notDeliveredOrders[i].delayTag = OrderDelayTag.DeliveryDelay;
        const order = await notDeliveredOrders[i].save();
        await this.orderNotificationService.fireOrderNotificationTrigger(
          order,
          TriggerAction.ON_DELIVERY_DELAY,
        );
      }
    }

    // const dayStart = moment.utc().startOf('day').toDate();
    // const dayEnd = moment.utc().endOf('day').toDate();
    // // Handle The Creation of Third Party Order Tasks Creation
    // const thirdPartyTaskCreationOrders = await this.orderModel.find({
    //   deliveryMethod: DeliveryMethod.THIRD_PARTY,
    //   pickup_date: { $gte: dayStart, $lte: dayEnd },
    //   status: { $nin: [OrderStatusEnum.CANCELED] },
    // });
    // for (let i = 0; i < thirdPartyTaskCreationOrders.length; i++) {
    //   const pickupDate = moment.utc(
    //     thirdPartyTaskCreationOrders[i].pickup_date,
    //   );
    //   const diff = pickupDate.diff(
    //     moment.utc(moment().format('YYYY-MM-DD HH:mm'), 'YYYY-MM-DD HH:mm'),
    //     'minutes',
    //   );
    //
    //   if (
    //     thirdPartyTaskCreationOrders[i].tookan_job_id &&
    //     !thirdPartyTaskCreationOrders[i].tookanTaskCancelled &&
    //     ![
    //       DeliveryThirdParty.enableDelivery,
    //       DeliveryThirdParty.deliveryHub,
    //     ].includes(thirdPartyTaskCreationOrders[i].deliveryParty)
    //   ) {
    //     await this.orderDeliveryService.cancelTookanTask(
    //       thirdPartyTaskCreationOrders[i],
    //     );
    //   }
    // }
  }

  private async handleScheduledGiftOrders(
    scheduledOrder: OrderDocument,
    currentDate: moment.Moment,
    orderDeliveryDate: moment.Moment,
  ): Promise<void> {
    if (
      scheduledOrder.payment_status === OrderPaymentStatus.COMPLETED &&
      !scheduledOrder.firedTriggers.includes(
        TriggerAction.ON_SEND_DELIVERY_LOCATION_SMS_GIFT,
      ) &&
      orderDeliveryDate.diff(currentDate, 'hours') <= 24
    )
      await this.orderNotificationService.fireGiftRecipientLocationTrigger(
        scheduledOrder,
      );
  }

  private async fireTriggerOnFirstAcknowledgeDelayedOrders(
    firstAcknowledgeOrders: OrderDocument[],
  ) {
    for (let i = 0; i < firstAcknowledgeOrders.length; i++) {
      const orderCreationDate = moment.utc(
        firstAcknowledgeOrders[i]['createdAt'],
      );
      if (orderCreationDate.diff(moment.utc(), 'minutes') >= 3) {
        await this.orderNotificationService.fireOrderNotificationTrigger(
          firstAcknowledgeOrders[i],
          TriggerAction.ON_FIRST_ACKNOWLEDGE_ORDER_DELAYED,
        );
      }
    }
  }

  private async handleDelayedSecondAcknowledgeOrders(
    manualAckowledgementCompanyIds: Types.ObjectId[],
  ) {
    const secondAcknowledgeOrders = await this.orderModel.find({
      pickup_date: { $exists: true },
      delivery_type: OrderDeliveryType.scheduled,
      status: { $eq: OrderStatusEnum.SCHEDULED },
      company: { $in: manualAckowledgementCompanyIds },
      deletedAt: null,
    });

    this.logger.log(
      `Checking delayed second acknowledge for ${secondAcknowledgeOrders.length} orders`,
    );

    if (secondAcknowledgeOrders.length > 0) {
      await this.fireTriggerOnSecondAcknowledgeDelayedOrders(
        secondAcknowledgeOrders,
      );
    }
  }

  private async fireTriggerOnSecondAcknowledgeDelayedOrders(
    secondAcknowledgeOrders: OrderDocument[],
  ) {
    for (let i = 0; i < secondAcknowledgeOrders.length; i++) {
      const orderPickupDate = moment.utc(
        secondAcknowledgeOrders[i]?.pickup_date,
      );
      if (orderPickupDate.diff(moment.utc(), 'minutes') >= 3) {
        await this.orderNotificationService.fireOrderNotificationTrigger(
          secondAcknowledgeOrders[i],
          TriggerAction.ON_SECOND_ACKNOWLEDGE_ORDER_DELAYED,
        );
      }
    }
  }

  private async fireTriggerOnDelayedCompletedOrders(
    completedOrders: OrderDocument[],
  ) {
    const currentDate = moment().utc();
    for (let i = 0; i < completedOrders.length; i++) {
      const orderDeliveryDate = moment.utc(completedOrders[i]?.delivery_date);
      if (currentDate.diff(orderDeliveryDate, 'hours') >= 3) {
        await this.orderNotificationService.fireOrderNotificationTrigger(
          completedOrders[i],
          TriggerAction.THREE_HOURS_AFTER_ORDER_COMPLETION,
        );
        completedOrders[i].firedTriggers.push(
          TriggerAction.THREE_HOURS_AFTER_ORDER_COMPLETION,
        );
        await completedOrders[i].save();
        await this.fireAggregatorTriggersOnDelayedCompletedOrders(
          completedOrders[i],
        );
      }
    }
  }

  private async fireAggregatorTriggersOnDelayedCompletedOrders(
    order: OrderDocument,
  ) {
    if (!aggregatorOrderSources.includes(order.source)) return;

    await this.orderNotificationService.fireOrderNotificationTrigger(
      order,
      TriggerAction.THREE_HOURS_AFTER_AGGREGATOR_ORDER,
    );

    await order.populate('customer');
    if (!order.customer) return;
    if (!order.customer['loyaltyStatus']) return;
    if (order.customer['loyaltyStatus'] === LoyaltyStatus.MEMBER) return;

    await this.orderNotificationService.fireOrderNotificationTrigger(
      order,
      TriggerAction.THREE_HOURS_AFTER_AGGREGATOR_ORDER_NON_LOYALTY_MEMBERS,
    );
  }
}
