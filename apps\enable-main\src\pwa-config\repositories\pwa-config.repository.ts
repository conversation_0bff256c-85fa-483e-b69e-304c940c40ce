import {
  CollectionName,
  GenericRepository,
  PwaConfig,
  PwaConfigDocument,
} from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';

@Injectable()
export class PwaConfigRepository extends GenericRepository<
  PwaConfigDocument,
  PwaConfig
> {
  constructor(
    @InjectModel(CollectionName.PWA_CONFIG)
    private readonly pwaConfigModel: Model<PwaConfigDocument, PwaConfig>,
  ) {
    super(pwaConfigModel);
  }

  async findByBrandIdsIn(
    brandIds: Types.ObjectId[],
  ): Promise<PwaConfigDocument[]> {
    return this.pwaConfigModel.find({ brandId: { $in: brandIds } });
  }
}
