import {
  CollectionName,
  LoggerService,
  OrderDocument,
  OrderLogActionEnum,
  TookanWebhookUser,
} from '@app/shared-stuff';
import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { BranchService } from '../../../branch/services/branch/branch.service';
import { CompanyService } from '../../../company/services/company/company.service';
import { CustomerReadServiceInterface } from '../../../customer/modules/customer-read/customer-read.service.interface';
import { TookanJobType } from '../../../delivery/enums/tookan-job-type.enum';
import { TookanTaskStatus } from '../../../delivery/enums/tookan-task-status.enum';
import { DriverService } from '../../../delivery/services/driver/driver.service';
import { TookanWebhookPayload } from '../../../delivery/types/tookan-webhook-payload.type';
import { OrderLogServiceInterface } from '../interfaces/order-log.service.interface';
import { TookanTaskWebhookService } from './tookan-task-webhook-service';

@Injectable()
export class OrderTookanService {
  private readonly logger = new LoggerService(OrderTookanService.name);
  constructor(
    @InjectModel(CollectionName.ORDER) private orderModel: Model<OrderDocument>,
    private eventEmitter: EventEmitter2,
    private companyService: CompanyService,
    @Inject(CustomerReadServiceInterface)
    private customerReadService: CustomerReadServiceInterface,
    private branchService: BranchService,
    private driverService: DriverService,
    @Inject('OrderLogServiceInterface')
    private readonly orderLogService: OrderLogServiceInterface,
    private tookanTaskWebhookService: TookanTaskWebhookService,
  ) {}

  async onDeliveryTaskSuccess(tookanTask: TookanWebhookPayload) {
    try {
      this.logger.log(
        `[Order][Tookan][Delivery][Success]: Received for order ${tookanTask.order_id}`,
        { tookanTask },
      );
      const order = await this.fetchOrder(tookanTask.order_id);
      if (!order) return;

      const isDeliveryTask = tookanTask.job_type === TookanJobType.DELIVERY;
      if (!isDeliveryTask) return;

      const isJobSuccessful =
        tookanTask.job_status === TookanTaskStatus.SUCCESSFUL;

      if (!isJobSuccessful || !isDeliveryTask) return;

      const company = await this.companyService.get_details(order.company);
      const customer = await this.customerReadService.findOne(
        order.customer.toHexString(),
        order.company,
      );
      const branch = await this.branchService.get_details(
        typeof order.branch !== 'string'
          ? order.branch._id
          : new Types.ObjectId(order.branch),
      );
      const driver = order.driver
        ? await this.driverService.get_details(order.driver.toHexString())
        : undefined;

      await this.tookanTaskWebhookService.handleSuccessfulDeliveryTask(
        order,
        customer,
        branch,
        company,
        driver,
      );
      await this.orderLogService.saveOrderLog(
        order,
        { requestedObject: tookanTask },
        { responseObject: order.toJSON() },
        OrderLogActionEnum.ORDER_TOOKAN_DELIVERY_SUCCESS,
        TookanWebhookUser,
      );
    } catch (error) {
      const payload = {
        message: `[Order][Tookan][Delivery][Success]: ${error.message} For order ${tookanTask.order_id}`,
        orderId: tookanTask.order_id,
        error,
      };
      throw new InternalServerErrorException(payload);
    }
  }

  private async fetchOrder(orderId: string | undefined) {
    if (!orderId) {
      throw new BadRequestException('Order ID is required');
    }
    if (orderId.includes('-D-')) {
      this.eventEmitter.emit('tookan-updated-for-non-order', orderId);
      return;
    }
    const order = await this.orderModel.findOne({ code: orderId });
    if (!order) {
      throw new NotFoundException('Order not found');
    }
    return order;
  }
}
