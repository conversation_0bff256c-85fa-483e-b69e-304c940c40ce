import { Module } from '@nestjs/common';

import { BrandModule } from '../../../brand/brand.module';
import { ShortenUrlModule } from '../../../integration/shorten-url/shorten-url.module';
import { PassesModule } from '../../../passes/passes.module';
import { CustomerPassLinkServiceInterface } from './customer-pass-link-service.interface';
import { CustomerPassLinkService } from './customer-pass-link.service';

@Module({
  imports: [BrandModule, PassesModule, ShortenUrlModule],
  controllers: [],
  providers: [
    {
      provide: CustomerPassLinkServiceInterface,
      useClass: CustomerPassLinkService,
    },
  ],
  exports: [CustomerPassLinkServiceInterface],
})
export class CustomerPassLinkModule {}
