// [EBL-5893] Points Redemption Method
// populate `order.loyaltyProgress.pointsRedeemed` so orders can be reverted

const companiesWithReversion = db.companies.distinct('_id', {
  hasLoyaltyReversion: true,
});

db.orders.bulkWrite(
  db.orders
    .find(
      {
        company: { $in: companiesWithReversion },
        'discounts.couponId': { $exists: true },
        'discounts.source': 'Coupon Discount',
        'loyaltyProgress.pointsRedeemed': { $exists: false },
        'loyaltyProgress.pointsRefunded': { $exists: false },
      },
      { discounts: 1 },
    )
    .toArray()
    .map(({ _id, discounts }) => {
      const pointsRedeemed = discounts
        .filter((discount) => discount.couponId)
        .map((discount) => db.coupons.findOne(discount.couponId))
        .map((coupon) => coupon.loyaltyPointCost)
        .reduce((a, b) => a + b, 0);

      return {
        updateOne: {
          filter: { _id },
          update: {
            $set: { 'loyaltyProgress.pointsRedeemed': pointsRedeemed },
          },
        },
      };
    }),
  { ordered: false },
);
