import {
  ActionGroup,
  CreateActionGroupDto,
  MicroserviceCommunicationService,
  UpdateActionGroupDto,
} from '@app/shared-stuff';
import {
  Inject,
  Injectable,
  OnModuleDestroy,
  OnModuleInit,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { Types } from 'mongoose';
import { ActionGroupServiceInterface } from './action-group.service.interface';

@Injectable()
export class ActionGroupService
  implements ActionGroupServiceInterface, OnModuleInit, OnModuleDestroy
{
  constructor(
    @Inject('enable-main-notification-producer')
    private client: ClientProxy,
    private microserviceCommunicationService: MicroserviceCommunicationService,
  ) {}

  onModuleInit() {
    this.microserviceCommunicationService.connect(this.client);
  }

  onModuleDestroy() {
    this.microserviceCommunicationService.disconnect(this.client);
  }

  async create(
    createActionGroupDto: CreateActionGroupDto,
  ): Promise<ActionGroup> {
    return await this.microserviceCommunicationService.produceAndWaitForResponse(
      this.client,
      'action-group.create.request',
      createActionGroupDto,
    );
  }

  async update(updateActionDto: UpdateActionGroupDto): Promise<ActionGroup> {
    return await this.microserviceCommunicationService.produceAndWaitForResponse(
      this.client,
      'action-group.update.request',
      updateActionDto,
    );
  }

  async delete(groupId: Types.ObjectId): Promise<number> {
    return await this.microserviceCommunicationService.produceAndWaitForResponse(
      this.client,
      'action-group.delete.request',
      { groupId },
    );
  }
}
