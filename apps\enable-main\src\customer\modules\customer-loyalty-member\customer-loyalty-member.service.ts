import {
  BulkActionResponseDto,
  BulkLoyaltyRegistrationDto,
  ContactChannel,
  CustomerDocument,
  CustomerWithToken,
  LoyaltyStatus,
  LRPSource,
  LRPUser,
  RegisterLoyaltyCustomerDto,
  RegistrationContextDto,
} from '@app/shared-stuff';
import {
  BadRequestException,
  Inject,
  Injectable,
  Logger,
} from '@nestjs/common';
import * as moment from 'moment-timezone';
import { BrandServiceInterface } from '../../../brand/services/brand/brand.service.interface';
import { CompanyService } from '../../../company/services/company/company.service';
import { OrdableCustomersServiceInterface } from '../../../integration/webstore/ordable/services/customers/ordable-customers.service.interface';
import { CustomerCodeServiceInterface } from '../customer-code/customer-code.service.interface';
import { CustomerIndexServiceInterface } from '../customer-index/customer-index.service.interface';
import { CustomerNotificationServiceInterface } from '../customer-notification/customer-notification.service.interface';
import { CustomerPassLinkServiceInterface } from '../customer-pass-link/customer-pass-link-service.interface';
import { CustomerTierServiceInterface } from '../customer-tier/customer-tier.service.interface';
import { CustomerTokenServiceInterface } from '../customer-token/customer-token.service.interface';
import { CustomerWriteServiceInterface } from '../customer-write/customer-write.service.interface';
import { CustomerLoyaltyMemberServiceInterface } from './customer-loyalty-member.service.interface';

@Injectable()
export class CustomerLoyaltyMemberService
  implements CustomerLoyaltyMemberServiceInterface
{
  private readonly logger = new Logger(CustomerLoyaltyMemberService.name);

  constructor(
    private readonly companyService: CompanyService,
    @Inject('BrandServiceInterface')
    private readonly brandService: BrandServiceInterface,
    @Inject(CustomerIndexServiceInterface)
    private readonly customerIndexService: CustomerIndexServiceInterface,
    @Inject(CustomerTierServiceInterface)
    private readonly customerTierService: CustomerTierServiceInterface,
    @Inject(CustomerNotificationServiceInterface)
    private readonly customerNotificationService: CustomerNotificationServiceInterface,
    @Inject(CustomerWriteServiceInterface)
    private readonly customerWriteService: CustomerWriteServiceInterface,
    @Inject(CustomerTokenServiceInterface)
    private readonly customerTokenService: CustomerTokenServiceInterface,
    @Inject(CustomerCodeServiceInterface)
    private readonly customerCodeService: CustomerCodeServiceInterface,
    @Inject(OrdableCustomersServiceInterface)
    private readonly ordableCustomersService: OrdableCustomersServiceInterface,
    @Inject(CustomerPassLinkServiceInterface)
    private readonly customerPassLinkService: CustomerPassLinkServiceInterface,
  ) {}

  async enrollLoyaltyCustomer(customer: CustomerDocument) {
    const isNewLoyaltyCustomer =
      customer.loyaltyStatus === LoyaltyStatus.UNENROLLED;

    if (!isNewLoyaltyCustomer) return;

    await customer.updateOne({ loyaltyStatus: LoyaltyStatus.ENROLLED });
    return await this.customerNotificationService.fireOnLoyaltyProgramEnrollmentTrigger(
      customer,
    );
  }

  async registerCustomerInLoyaltyProgram({
    brandId,
    source,
    enrollmentCode,
    loyaltyRegistrationBranchId,
    walletApp,
    ...customerToCreate
  }: RegisterLoyaltyCustomerDto): Promise<CustomerWithToken> {
    const brand = await this.brandService.findById(brandId);
    const company = await this.companyService.get_details(
      brand.companyId.toHexString(),
    );

    const customer = await this.customerWriteService.updateOrCreate(
      {
        ...customerToCreate,
        contact_channel: ContactChannel.LOYALTY_REGISTRATION_PAGE,
        company: company._id,
        registrationContext: {
          brandId,
          source: Object.values(LRPSource).includes(source)
            ? source
            : LRPSource.UNKNOWN,
          rawSource: source,
          enrollmentCode,
          loyaltyRegistrationBranchId,
        },
      },
      LRPUser,
    );

    const registeredCustomer = await this.registerLoyaltyCustomer(
      customer,
      customer.registrationContext,
    );

    const token = this.customerTokenService.generateCustomerToken({
      customerId: registeredCustomer._id,
      companyId: registeredCustomer.company,
      brandId,
    });

    let passLink = '';
    if (walletApp)
      passLink = await this.customerPassLinkService.getWalletPassLink(
        registeredCustomer,
        brand,
        walletApp,
      );
    return { ...registeredCustomer.toObject(), token, passLink };
  }

  async registerLoyaltyCustomer(
    customer: CustomerDocument,
    registrationContext: RegistrationContextDto,
    fireNotification = true,
    skipVerification = false,
  ): Promise<CustomerDocument> {
    const company = await this.companyService.get_details(
      customer.company.toHexString(),
    );
    const isPhoneNumberVerified = customer?.verificationContext?.isVerified;

    const needsVerification =
      company.loyaltyProgramConfig?.otpChannels &&
      company.loyaltyProgramConfig?.otpChannels.length > 0;

    if (!skipVerification && !isPhoneNumberVerified && needsVerification)
      throw new BadRequestException(
        `Company requires phone number verification, but customer is not verified`,
      );

    const brand = await this.brandService.findById(
      registrationContext.brandId || customer.registrationContext?.brandId,
    );

    const wasNonMember = customer.loyaltyStatus !== LoyaltyStatus.MEMBER;
    if (wasNonMember) {
      if (registrationContext.loyaltyRegistrationBranchId)
        customer.loyaltyRegistrationBranchId =
          registrationContext.loyaltyRegistrationBranchId;

      customer.registrationContext = {
        ...(customer.registrationContext || {}),
        ...registrationContext,
      };
      customer.activeBrand = this.brandService.toEmbeddedBrandDto(brand);
      customer.loyaltyStatus = LoyaltyStatus.MEMBER;
      customer.loyaltyRegistrationAt = moment.utc().toDate();
      await customer.save();

      customer.shortCode =
        await this.customerCodeService.getOrGenerateCustomerShortCode(customer);

      customer =
        await this.ordableCustomersService.regenerateOrdableLink(customer);
    }

    if (!customer.firstTierEarnedAt) {
      let enrollmentCode = registrationContext.enrollmentCode;

      if (
        !enrollmentCode &&
        company.loyaltyProgramConfig?.baseTierEnrollmentCode
      ) {
        enrollmentCode = company.loyaltyProgramConfig.baseTierEnrollmentCode;
      }

      if (enrollmentCode) {
        await this.customerTierService.enrollCustomerInTier(
          customer,
          enrollmentCode,
        );
      }
    }

    if (wasNonMember && fireNotification) {
      await this.customerNotificationService.fireOnLoyaltyProgramRegistrationTrigger(
        customer,
        brand._id,
      );
    }

    return customer;
  }

  async bulkLoyaltyRegistration({
    brandId,
    ...bulkActionDto
  }: BulkLoyaltyRegistrationDto) {
    const response: BulkActionResponseDto = {
      success: 0,
      unchanged: 0,
      failed: 0,
    };
    const customers =
      await this.customerIndexService.fetchEligibleCustomersForBulkAction(
        bulkActionDto,
      );
    const eligibleCustomers = customers.filter(
      (customer) => customer.loyaltyStatus != LoyaltyStatus.MEMBER,
    );
    response.unchanged = customers.length - eligibleCustomers.length;

    const fireTrigger = async (customer: CustomerDocument) => {
      await this.customerNotificationService.fireOnManualLoyaltyRegistration(
        customer,
        brandId,
      );
    };

    for (let i = 0; i < eligibleCustomers.length; i++) {
      try {
        const customer = eligibleCustomers[i];
        await this.registerLoyaltyCustomer(
          customer,
          {
            ...customer.registrationContext,
            source: LRPSource.ENABLE_MAIN_MANUAL_REGISTRATION,
            rawSource: LRPSource.ENABLE_MAIN_MANUAL_REGISTRATION,
            brandId,
          },
          true,
        );
        await fireTrigger(customer);
        response.success++;
      } catch (exception) {
        this.logger.error(exception);
        response.failed++;
      }
    }
    return response;
  }
}
