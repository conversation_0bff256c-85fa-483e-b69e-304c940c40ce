export enum PaymentIndexSortType {
  NAME_A_Z = 'name_a_z',
  NAME_Z_A = 'name_z_a',
  PAYMENT_STATUS_A_Z = 'payment_status_a_z',
  PAYMENT_STATUS_Z_A = 'payment_status_z_a',
  LINK_ID_ASC = 'link_id_asc',
  LINK_ID_DESC = 'link_id_desc',
  AMOUNT_ASC = 'amount_asc',
  AMOUNT_DESC = 'amount_desc',
  ORDER_CODE_ASC = 'order_code_asc',
  ORDER_CODE_DESC = 'order_code_desc',
  RECORD_UPDATED = 'record_updated',
  DATE_CREATED_ASC = 'date_created_asc',
  DATE_CREATED_DESC = 'date_created_desc',
  TRANSACTION_ID_ASC = 'transaction_id_asc',
  TRANSACTION_ID_DESC = 'transaction_id_desc',
  CODE_ASC = 'code_asc',
  CODE_DESC = 'code_desc',
  COMPANY_ASC = 'company_asc',
  COMPANY_DESC = 'company_desc',
  TRANSACTION_DATE_ASC = 'transaction_date_asc',
  TRANSACTION_DATE_DESC = 'transaction_date_desc',
  SOURCE_ASC = 'source_asc',
  SOURCE_DESC = 'source_desc',
  CREATED_BY_ASC = 'created_by_asc',
  CREATED_BY_DESC = 'created_by_desc',
}
