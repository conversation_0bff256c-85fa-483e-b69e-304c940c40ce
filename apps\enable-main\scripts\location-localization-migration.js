db.getCollection('countries')
  .find({})
  .toArray()
  .forEach((country) => {
    print(`Currenty Processing Couyntry name: ${country.name}`);
    const countryLocationItemResult = db
      .getCollection('LocationItems')
      .insertOne({
        type: 'country',
        createdAt: new Date(),
        updateAt: new Date(),
        name: country.name,
        code: country.code,
      });
    const cities = db
      .getCollection('cities')
      .find({ country: country._id })
      .toArray();
    for (let i = 0; i < cities.length; i++) {
      const city = cities[i];
      print(`Currenty Processing City Name: ${city.name}`);
      const cityLocationItemResult = db
        .getCollection('LocationItems')
        .insertOne({
          name: city.name,
          type: 'city',
          createdAt: new Date(),
          updateAt: new Date(),
          ancestors: [countryLocationItemResult.insertedId],
          parent: {
            _id: countryLocationItemResult.insertedId,
            name: country.name,
            h3Indexes: [],
            h3HighestResolution: 0,
            h3LowestResolution: 0,
          },
        });
      const areas = db
        .getCollection('areas')
        .find({ city: city._id })
        .toArray();
      for (let j = 0; j < areas.length; j++) {
        const area = areas[j];
        print(`Currenty Processing Area Name: ${area.name}`);
        const areaLocationItemResult = db
          .getCollection('LocationItems')
          .insertOne({
            name: area.name,
            type: 'area',
            createdAt: new Date(),
            updateAt: new Date(),
            ancestors: [
              countryLocationItemResult.insertedId,
              cityLocationItemResult.insertedId,
            ],
            parent: {
              _id: cityLocationItemResult.insertedId,
              name: city.name,
              h3Indexes: [],
              h3HighestResolution: 0,
              h3LowestResolution: 0,
            },
          });
      }
    }
  });
