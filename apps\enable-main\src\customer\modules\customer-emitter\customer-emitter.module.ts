import { MicroserviceCommunicationModule } from '@app/shared-stuff';
import { Module } from '@nestjs/common';
import { CustomerEmitterService } from './customer.emitter.service';

@Module({
  providers: [CustomerEmitterService],
  exports: [CustomerEmitterService],
  imports: [
    MicroserviceCommunicationModule.forChild(
      'enable-main-notification-producer',
    ),
  ],
})
export class CustomerEmitterModule {}
